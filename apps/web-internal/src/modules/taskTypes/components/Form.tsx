import { useTheme } from '@emotion/react'

import { Form, Formik, useField } from 'formik'

import {
  Button,
  ButtonLink,
  Input,
  InputNumber,
  P3,
  remSpacing,
  Save,
} from '@apella/component-library'
import {
  BasicFormPageTemplate,
  ButtonToolbar,
  FormSection,
} from 'src/components/BasicFormPageTemplate'
import { AnnotatorSelect } from 'src/components/molecules/AnnotatorSelect'
import { EventTypesInput } from 'src/components/molecules/Filters/EventTypesFilter'
import { InputCheckbox } from 'src/components/molecules/InputCheckbox'
import { IntervalInput } from 'src/components/molecules/IntervalInput'
import { RoomsInput } from 'src/components/molecules/RoomsInput'
import { LocationPath } from 'src/router/LocationPath'

const InputNumberField = ({
  onChange: onChangeProp,
  ...props
}: React.ComponentProps<typeof InputNumber>) => {
  const [field, meta, { setValue }] = useField<number | undefined>({
    name: props.name,
    type: 'time',
  })

  const errorMessage = meta.error && meta.touched ? meta.error : ''

  return (
    <InputNumber
      {...field}
      onChange={(num) => {
        setValue(num)

        if (onChangeProp) onChangeProp(num)
      }}
      error={errorMessage}
      {...props}
    />
  )
}

interface FormInputs {
  allowSkippingReview: boolean
  annotatorIds: string[]
  contextEventTypes: string[]
  description: string
  detectIdle: boolean
  eventTypes: string[]
  interval?: number
  name: string
  optimizeTasks: boolean
  priority?: number
  provisionalAnnotatorIds: string[]
  reviewerIds: string[]
  roomIds: string[]
  startTime: string
}

export function TaskTypesForm({
  title,
  initialValues,
  onSubmit,
  additionalButtons,
}: {
  title: string
  initialValues: FormInputs
  onSubmit: (data: Required<FormInputs>) => Promise<void>
  additionalButtons?: React.ReactNode
}) {
  const theme = useTheme()

  return (
    <BasicFormPageTemplate title={title}>
      <Formik
        initialValues={initialValues}
        onSubmit={async ({
          name,
          description,
          priority,
          startTime,
          detectIdle,
          allowSkippingReview,
          interval,
          optimizeTasks,
          ...fields
        }) => {
          if (!(interval && startTime && name.length > 3 && priority)) {
            alert(
              'Please fill out all fields. Name must be at least 3 characters.'
            )
            return
          }

          // When using a <MultiSelect />, if no field is selected then the value is
          // undefined, causing these fields to not be updated.
          // TODO: handle this in the components
          const eventTypes = fields.eventTypes ?? []
          const contextEventTypes = (fields.contextEventTypes ?? []).filter(
            (id) => !eventTypes.includes(id)
          )
          const roomIds = fields.roomIds ?? []
          const annotatorIds = fields.annotatorIds ?? []
          const provisionalAnnotatorIds = fields.provisionalAnnotatorIds ?? []
          const reviewerIds = fields.reviewerIds ?? []
          await onSubmit({
            name,
            description,
            eventTypes,
            contextEventTypes,
            startTime,
            priority,
            detectIdle,
            allowSkippingReview,
            optimizeTasks,
            interval,
            roomIds,
            annotatorIds,
            provisionalAnnotatorIds,
            reviewerIds,
          })
        }}
      >
        {({ values }) => {
          return (
            <Form>
              <FormSection>
                <div css={{ marginBottom: remSpacing.medium }}>
                  <Input.Text label="Name" name="name" required />
                </div>

                <Input.Text multiline label="Description" name="description" />

                <InputNumberField
                  label="Priority"
                  name="priority"
                  required
                  hint="1 = Highest, 10 = Lowest"
                  min={1}
                  max={10}
                />

                <InputCheckbox
                  name="detectIdle"
                  label="Cancel idle tasks"
                  hint='When checked, tasks without predicted activity will automatically be marked with a status of "Cancelled" and a cancelled reason of "Idle"'
                />

                <AnnotatorSelect
                  name="annotatorIds"
                  label="Assigned annotators"
                  placeholder="No annotators selected"
                  hint="This affects which users can be assigned to annotate tasks of this type"
                  // search /* TODO: fix `InputSelect` and add this prop */
                  css={{ margin: `${remSpacing.xsmall} 0`, width: '100%' }}
                />

                <AnnotatorSelect
                  name="reviewerIds"
                  label="Assigned reviewers"
                  placeholder="No reviewers selected"
                  hint="This affects which users can be assigned to review tasks of this type"
                  // search /* TODO: fix `InputSelect` and add this prop */
                  css={{ margin: `${remSpacing.xsmall} 0`, width: '100%' }}
                />
              </FormSection>

              <FormSection title="Selective Annotations">
                <div>
                  <InputCheckbox
                    name="allowSkippingReview"
                    label="Allow skipping review"
                  />
                  <P3
                    as="div"
                    color={theme.palette.text.tertiary}
                    css={{ marginTop: remSpacing.xsmall }}
                  >
                    {'When checked, tasks will automatically be changed to a status of "Done" instead of "Ready for Review", unless the "Needs Review" label is added to an event.' +
                      ' Note that a small portion of tasks without the "Needs Review" will still be randomly selected for review.'}
                  </P3>
                </div>

                {values.allowSkippingReview && (
                  <AnnotatorSelect
                    name="provisionalAnnotatorIds"
                    label="Provisional annotators"
                    placeholder="No provisional annotators selected"
                    hint="Provisional annotators will always have their tasks reviewed by another annotator."
                    // search /* TODO: fix `InputSelect` and add this prop */
                    css={{
                      margin: `${remSpacing.xsmall} 0`,
                      width: '100%',
                    }}
                  />
                )}

                <div>
                  <InputCheckbox name="optimizeTasks" label="Optimize tasks" />
                  <P3
                    as="div"
                    color={theme.palette.text.tertiary}
                    css={{ marginTop: remSpacing.xsmall }}
                  >
                    {'When checked, task generation will be based on the new annotation task optimizer.' +
                      ' Note that not all predicted events will be annotated.'}
                  </P3>
                </div>
              </FormSection>

              <FormSection title="Events">
                <EventTypesInput
                  label="Annotated Event Types"
                  placeholder="No event types selected"
                  name="eventTypes"
                  css={{ margin: `${remSpacing.xsmall} 0`, width: '100%' }}
                />

                <EventTypesInput
                  label="Contextual Event Types"
                  placeholder="No event types selected"
                  filter={(id) =>
                    !!(id && !(values.eventTypes ?? []).includes(id))
                  }
                  name="contextEventTypes"
                  hint='These event types won&apos;t be annotated by tasks of this type but will be visible to annotators. Note that the "Annotated Event Types" for this task will not be present in this list.'
                  css={{ margin: `${remSpacing.xsmall} 0`, width: '100%' }}
                />
              </FormSection>

              <FormSection title="Schedule">
                <RoomsInput
                  search
                  label="Rooms"
                  name="roomIds"
                  css={{
                    margin: `${remSpacing.xsmall} 0`,
                    width: '100%',
                  }}
                />

                <Input.Time required name="startTime" label="Start Time" />

                <IntervalInput
                  required
                  name="interval"
                  label="Interval"
                  css={{
                    margin: `${remSpacing.xsmall} 0`,
                    width: '100%',
                  }}
                />
              </FormSection>

              <ButtonToolbar
                left={
                  <ButtonLink
                    to={LocationPath.TaskTypes}
                    type="button"
                    color="alternate"
                  >
                    Back
                  </ButtonLink>
                }
                right={
                  <>
                    <Button type="submit">
                      <Save size="sm" /> Save
                    </Button>
                    {additionalButtons}
                  </>
                }
              />
            </Form>
          )
        }}
      </Formik>
    </BasicFormPageTemplate>
  )
}
