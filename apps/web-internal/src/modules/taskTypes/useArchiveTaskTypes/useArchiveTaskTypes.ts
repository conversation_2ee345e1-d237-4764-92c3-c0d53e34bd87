import { useCallback } from 'react'

import { useMutation } from '@apollo/client'

import { ArchiveTaskType, ArchiveTaskTypeVariables } from './__generated__'
import { ARCHIVE_TASK_TYPE } from './queries'

export function useArchiveTaskType(taskTypeId: string) {
  const [archiveTaskType] = useMutation<
    ArchiveTaskType,
    ArchiveTaskTypeVariables
  >(ARCHIVE_TASK_TYPE)

  const archive = useCallback(() => {
    if (confirm('Are you sure you want to archive this task type?')) {
      archiveTaskType({ variables: { taskTypeId, archived: true } })
    }
  }, [archiveTaskType, taskTypeId])

  const unarchive = useCallback(() => {
    if (confirm('Are you sure you want to restore this task type?')) {
      archiveTaskType({ variables: { taskTypeId, archived: false } })
    }
  }, [archiveTaskType, taskTypeId])

  return { archive, unarchive }
}
