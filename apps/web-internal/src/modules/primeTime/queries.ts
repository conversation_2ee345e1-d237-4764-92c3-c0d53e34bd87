import { gql } from '@apollo/client'

export const SITE_PRIME_TIME_CONFIG_FRAGMENT = gql`
  fragment SitePrimeTimeConfigFragment on SitePrimeTimeConfig {
    sunday {
      startTime
      endTime
    }
    monday {
      startTime
      endTime
    }
    tuesday {
      startTime
      endTime
    }
    wednesday {
      startTime
      endTime
    }
    thursday {
      startTime
      endTime
    }
    friday {
      startTime
      endTime
    }
    saturday {
      startTime
      endTime
    }
  }
`

export const ROOM_PRIME_TIME_CONFIG_FRAGMENT = gql`
  fragment RoomPrimeTimeConfigFragment on RoomPrimeTimeConfig {
    source
    sunday {
      startTime
      endTime
    }
    monday {
      startTime
      endTime
    }
    tuesday {
      startTime
      endTime
    }
    wednesday {
      startTime
      endTime
    }
    thursday {
      startTime
      endTime
    }
    friday {
      startTime
      endTime
    }
    saturday {
      startTime
      endTime
    }
  }
`
