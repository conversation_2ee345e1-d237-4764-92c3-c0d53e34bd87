export type WeekRecord<T> = {
  sunday: T
  monday: T
  tuesday: T
  wednesday: T
  thursday: T
  friday: T
  saturday: T
}

export const DAYS_OF_WEEK = [
  'sunday',
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
] as const
export type DayOfWeek = (typeof DAYS_OF_WEEK)[number]

export type TimeRange = {
  startTime: string
  endTime: string
}

export type WeekConfig = WeekRecord<TimeRange | undefined>
