import { useTheme } from '@emotion/react'

import {
  P3,
  pxSpacing,
  Caps2,
  Tooltip,
  remSpacing,
} from '@apella/component-library'

import { TimeRange } from '../types'
import { formatTimeRangeToStr } from '../utils'

export const OverrideIndicator = ({
  message,
  className,
  timeRange,
}: {
  message: string
  className?: string
  timeRange?: TimeRange
}) => {
  const theme = useTheme()

  return (
    <Tooltip
      css={{ width: '256px' }}
      placement="right"
      body={
        <div>
          <P3 css={{ paddingBottom: pxSpacing.medium }}>{message}</P3>
          <Caps2 color={theme.palette.gray[60]}>
            SITE: {formatTimeRangeToStr(timeRange)}
          </Caps2>
        </div>
      }
    >
      <div className={className}>
        <Circle size={`${pxSpacing.xsmall}px`} color={theme.palette.blue[50]} />
      </div>
    </Tooltip>
  )
}

const Circle = ({
  size = remSpacing.xsmall,
  color,
  className,
}: {
  size?: string
  className?: string
  color: string
}) => (
  <div
    className={className}
    css={{
      height: size,
      width: size,
      background: color,
      borderRadius: '50%',
    }}
  />
)
