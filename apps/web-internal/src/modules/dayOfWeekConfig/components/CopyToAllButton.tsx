import React, { useState } from 'react'

import { useTheme } from '@emotion/react'

import {
  Button,
  ContentCopy,
  Dialog,
  P2,
  pxSpacing,
} from '@apella/component-library'

export const CopyToAllButton = ({
  onCopy,
  showConfirmation = true,
  confirmationMessage,
  ...props
}: {
  confirmationMessage: string
  showConfirmation: boolean
  onCopy: () => void
} & Omit<React.ComponentProps<typeof Button>, 'onClick'>) => {
  const theme = useTheme()
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false)

  return (
    <>
      <Dialog
        isOpen={isConfirmationModalOpen}
        title="Copy to all?"
        onClose={() => {
          setIsConfirmationModalOpen(false)
        }}
      >
        <P2
          css={{ marginBottom: pxSpacing.gutter }}
          color={theme.palette.gray[70]}
        >
          {confirmationMessage}
        </P2>
        <div css={{ display: 'flex', gap: pxSpacing.small }}>
          <Button
            onClick={() => {
              onCopy()
              setIsConfirmationModalOpen(false)
            }}
          >
            Copy to all
          </Button>
          <Button
            color="alternate"
            onClick={() => {
              setIsConfirmationModalOpen(false)
            }}
          >
            Cancel
          </Button>
        </div>
      </Dialog>
      <Button
        type="button"
        css={{
          marginBottom: pxSpacing.xsmall,
        }}
        size="sm"
        appearance="link"
        onClick={() => {
          if (showConfirmation) {
            setIsConfirmationModalOpen(true)
          } else {
            onCopy()
          }
        }}
        {...props}
      >
        <ContentCopy size="xs" />
        Copy to all
      </Button>
    </>
  )
}
