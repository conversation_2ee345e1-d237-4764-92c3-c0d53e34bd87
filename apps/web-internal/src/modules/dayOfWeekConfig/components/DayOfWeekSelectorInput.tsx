import { useCallback } from 'react'

import { useTheme } from '@emotion/react'

import { useField } from 'formik'

import { P2 } from '@apella/component-library'
import { DayOfWeek } from 'src/modules/dayOfWeekConfig/types'

const getShorthand = (day: DayOfWeek) => {
  switch (day) {
    case 'sunday':
      return 'S'
    case 'monday':
      return 'M'
    case 'tuesday':
      return 'T'
    case 'wednesday':
      return 'W'
    case 'thursday':
      return 'Th'
    case 'friday':
      return 'F'
    case 'saturday':
      return 'S'
    default:
      throw new Error(`Invalid day ${day}`)
  }
}

export const DayOfWeekSelectorInput = ({
  name,
  onChange: onChangeProp = () => {},
  ...props
}: {
  day: DayOfWeek
  name: string
  onChange?: (checked: boolean) => void
}) => {
  const [
    {
      value,
      // Omit onChange - we manually implement changing the value
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      onChange: _fieldOnChange,
      ...field
    },
    ,
    { setValue },
  ] = useField<boolean>(name)

  const onChange = useCallback(
    (checked: boolean) => {
      setValue(checked)
      onChangeProp(checked)
    },
    [onChangeProp, setValue]
  )

  return (
    <DayOfWeekSelector
      checked={value}
      {...field}
      {...props}
      onChange={onChange}
    />
  )
}

export const DayOfWeekSelector = ({
  name,
  day,
  checked,
  onChange: onChangeProp = () => {},
  onClick: onClickProp = () => {},
  ...props
}: Omit<React.JSX.IntrinsicElements['button'], 'value' | 'onChange'> & {
  name?: string
  day: DayOfWeek
  checked: boolean
  onChange?: (checked: boolean) => void
}) => {
  const theme = useTheme()

  return (
    <button
      name={name}
      type="button"
      role="checkbox"
      aria-checked={checked}
      aria-label={day}
      css={{
        cursor: 'pointer',
        display: 'flex',
        width: '36px',
        height: '36px',
        padding: '6px 0px',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: '8px',
        ...(checked
          ? { background: theme.palette.blue.background, border: 'none' }
          : {
              border: `1px solid ${theme.palette.gray[30]}`,
              background: theme.palette.background.primary,
            }),
      }}
      {...props}
      onClick={(e) => {
        onClickProp(e)
        onChangeProp(!checked)
      }}
    >
      <P2 color={checked ? theme.palette.blue[50] : theme.palette.gray[50]}>
        {getShorthand(day)}
      </P2>
    </button>
  )
}
