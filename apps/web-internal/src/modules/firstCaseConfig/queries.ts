import { gql } from '@apollo/client'

export const SITE_FIRST_CASE_CONFIG_FRAGMENT = gql`
  fragment SiteFirstCaseConfigFragment on SiteFirstCaseConfig {
    sunday {
      startTime
      endTime
    }
    monday {
      startTime
      endTime
    }
    tuesday {
      startTime
      endTime
    }
    wednesday {
      startTime
      endTime
    }
    thursday {
      startTime
      endTime
    }
    friday {
      startTime
      endTime
    }
    saturday {
      startTime
      endTime
    }
  }
`

export const ROOM_FIRST_CASE_CONFIG_FRAGMENT = gql`
  fragment RoomFirstCaseConfigFragment on RoomFirstCaseConfig {
    source
    sunday {
      startTime
      endTime
    }
    monday {
      startTime
      endTime
    }
    tuesday {
      startTime
      endTime
    }
    wednesday {
      startTime
      endTime
    }
    thursday {
      startTime
      endTime
    }
    friday {
      startTime
      endTime
    }
    saturday {
      startTime
      endTime
    }
  }
`
