import {
  Button,
  ButtonLink,
  Form,
  Input,
  remSpacing,
  Save,
} from '@apella/component-library'
import {
  BasicFormPageTemplate,
  ButtonToolbar,
  FormSection,
} from 'src/components/BasicFormPageTemplate'
import { OrgInput } from 'src/components/molecules/OrgInput'
import { LocationPath } from 'src/router/LocationPath'

interface FormInputs {
  color?: string
  name: string
  orgId?: string
}

export function RoomTagsForm({
  title,
  initialValues,
  onSubmit,
}: {
  title: string
  initialValues: FormInputs
  onSubmit: (data: Required<FormInputs>) => Promise<void>
}) {
  return (
    <BasicFormPageTemplate title={title}>
      <Form
        initialValues={initialValues}
        onSubmit={async ({ name: nameInput, orgId, color }) => {
          const name = nameInput.trim()

          if (!(name.length >= 3) || !orgId || !color) {
            alert(
              'Please fill out all fields. Name must be at least 3 characters.'
            )
            return
          }

          await onSubmit({ name, orgId, color })
        }}
      >
        <FormSection>
          <Input.Text name="name" label="Name" required />

          <Input.Color name="color" label="Color" required />

          <OrgInput
            name="orgId"
            disabled={initialValues.orgId !== undefined}
            required
            css={{ margin: `${remSpacing.xsmall} 0`, width: '100%' }}
          />
        </FormSection>

        <ButtonToolbar
          left={
            <ButtonLink
              to={LocationPath.RoomTags}
              type="button"
              color="alternate"
            >
              Back
            </ButtonLink>
          }
          right={
            <Button type="submit">
              <Save size="sm" /> Save
            </Button>
          }
        />
      </Form>
    </BasicFormPageTemplate>
  )
}
