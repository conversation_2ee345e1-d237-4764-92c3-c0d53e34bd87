import {
  object as YupO<PERSON>,
  string as <PERSON>p<PERSON><PERSON>,
  boolean as YupBoolean,
} from 'yup'

import {
  Button,
  FlexContainer,
  Form,
  Input,
  Save,
} from '@apella/component-library'
import {
  BasicFormPageTemplate,
  ButtonToolbar,
  FormSection,
} from 'src/components/BasicFormPageTemplate'

import { OrgInputMultiple } from '../../../components/molecules/OrgInput'

export interface FormInputs {
  color: string
  description: string
  id: string
  name: string
  orgIdFilter: string[] | undefined
  shownInDashboard: boolean
  shownInInternal: boolean
  type: string
}

const formSchema = YupObject({
  id: YupString()
    .required('Id is required (e.g. patient_wheels_in)')
    .min(5, 'Id must be at least 5 characters')
    .matches(
      /^[a-zA-Z0-9_-]+$/,
      'Id can only contain alphanumeric characters, dashes, and underscores'
    ),
  name: YupString()
    .required('Name is required (e.g. Patient Wheels In)')
    .min(5, 'Name must be at least 5 characters'),
  type: YupString()
    .required('Category is required! Use "uncategorized" if unsure.')
    .min(5, 'Category must be at least 5 characters'),
  description: YupString(),
  color: YupString()
    .matches(/^#[0-9a-f]{6}$/i)
    .required('Color (hex) is required!'),
  shownInInternal: YupBoolean().default(true),
})

export const EventTypesForm = ({
  title,
  initialValues,
  shouldDisableIdField = false,
  onSubmit,
  onBackButtonClick,
}: {
  title: string
  initialValues: FormInputs
  shouldDisableIdField?: boolean
  onSubmit: (data: Required<FormInputs>) => Awaited<void>
  onBackButtonClick?: () => void
}) => {
  return (
    <BasicFormPageTemplate title={title}>
      <Form
        initialValues={initialValues}
        validationSchema={formSchema}
        onSubmit={onSubmit}
      >
        {({ values }) => (
          <>
            <FormSection>
              <Input.Text
                label="Id"
                name="id"
                required
                disabled={shouldDisableIdField}
              />

              <Input.Text label="Name" name="name" required />

              <Input.Text
                label="Category"
                name="type"
                required
                autoComplete
                placeholder="e.g. back_table_status"
              />

              <Input.Text multiline label="Description" name="description" />

              <Input.Color required label="Color" name="color" />
            </FormSection>
            <FormSection title={'Visibility'}>
              <Input.Checkbox
                name={'shownInInternal'}
                ariaLabel={'Shown in Internal'}
              >
                Shown in Internal
              </Input.Checkbox>
              <Input.Checkbox
                name={'shownInDashboard'}
                ariaLabel={'Shown in Dashboard'}
              >
                Shown in Dashboard
              </Input.Checkbox>
              <OrgInputMultiple
                label={'Org Filter'}
                name={'orgIdFilter'}
                placeholder={'All orgs'}
                disabled={!values.shownInDashboard}
              />
            </FormSection>

            <ButtonToolbar
              left={
                onBackButtonClick && (
                  <FlexContainer direction="row" justifyContent="flex-start">
                    <Button
                      type="button"
                      color="alternate"
                      onClick={onBackButtonClick}
                    >
                      Back
                    </Button>
                  </FlexContainer>
                )
              }
              right={
                <Button type="submit">
                  <Save size="sm" /> Save
                </Button>
              }
            />
          </>
        )}
      </Form>
    </BasicFormPageTemplate>
  )
}
