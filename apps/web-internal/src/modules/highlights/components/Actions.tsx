import { useContext } from 'react'
import { generatePath, Link } from 'react-router'

import { DateTime } from 'luxon'

import { Button, Highlights } from '@apella/component-library'
import { LocaleContext } from 'src/contexts/locale'
import { GetHighlightSearchData } from 'src/modules/highlights/queries/__generated__'
import { LocationPath } from 'src/router/LocationPath'

type GetHighlightSearchData_highlightSearch_edges_node =
  GetHighlightSearchData['highlightSearch']['edges'][number]['node']

export const Actions = ({
  rowData,
}: {
  rowData: GetHighlightSearchData_highlightSearch_edges_node
}): React.JSX.Element => {
  const locale = useContext(LocaleContext)

  const highlightEditorLocation = generatePath(LocationPath.HighlightEditor, {
    highlightId: rowData.id,
  })

  const searchParams = !rowData.startTime
    ? ''
    : `?${new URLSearchParams({
        roomId: rowData.room.id,
        siteId: rowData.site.id,
        organizationId: rowData.organization.id,
        startDate: DateTime.fromISO(rowData.startTime).setZone(locale).toISO(),
      })}`

  return (
    <Link
      to={`${highlightEditorLocation}${searchParams}`}
      target="_blank"
      rel="noopener noreferrer"
    >
      <Button>
        <Highlights size="sm" />
        Edit Highlight
      </Button>
    </Link>
  )
}
