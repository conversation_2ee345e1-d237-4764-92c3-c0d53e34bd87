import { gql } from '@apollo/client'

import { PAGE_CURSORS_FRAGMENT } from 'src/utils/graphqlFragments'

export const ORG_USER_FRAGMENT = gql`
  fragment OrgUserNodeFragment on User {
    id
    name
    email
  }

  fragment OrgUserFragment on UserEdge {
    node {
      ...OrgUserNodeFragment
    }
  }
`

export const CREATE_HIGHLIGHT = gql`
  mutation CreateHighlight($highlight: HighlightCreateInput!) {
    highlightCreate(input: $highlight) {
      success
      createdHighlight {
        id
        description
        category
      }
    }
  }
`

export const UPDATE_HIGHLIGHT = gql`
  mutation UpdateHighlight($highlight: HighlightUpdateInput!) {
    highlightUpdate(input: $highlight) {
      success
      updatedHighlight {
        id
        description
        category
      }
    }
  }
`

export const ARCHIVE_HIGHLIGHT = gql`
  mutation ArchiveHighlight($highlight: HighlightArchiveInput!) {
    highlightArchive(input: $highlight) {
      success
      archivedHighlight {
        id
        archivedTime
      }
    }
  }
`

export const GET_HIGHLIGHT_QUERY = gql`
  ${ORG_USER_FRAGMENT}
  query GetHighlightQuery($highlightId: String!) {
    highlight(id: $highlightId) {
      id
      description
      startTime
      endTime
      category
      archivedTime
      organization {
        id
        users {
          edges {
            ...OrgUserFragment
          }
        }
      }
      site {
        id
      }
      room {
        id
      }
      users {
        edges {
          node {
            id
            name
            email
          }
        }
      }
    }
  }
`

export const GET_HIGHLIGHT_ACCESSORY_QUERY = gql`
  ${ORG_USER_FRAGMENT}
  query GetHighlightAccessoryQuery(
    $roomId: ID!
    $roomIdString: String!
    $eventMinTime: DateTime!
    $eventMaxTime: DateTime!
    $verifiedType: String!
  ) {
    room(id: $roomIdString) {
      id
      name
      organization {
        id
        users {
          edges {
            ...OrgUserFragment
          }
        }
      }
    }
    eventSearch(
      query: {
        roomId: $roomId
        minStartTime: $eventMinTime
        maxStartTime: $eventMaxTime
        # Shortcut until I make this a bit more generic
        sourceType: $verifiedType
      }
      first: 20
    ) {
      edges {
        node {
          id
          version # need this b/c we are including it as a keyField in the Event type policy
          label
          startTime
        }
      }
    }
  }
`

export const GET_HIGHLIGHT_SEARCH_DATA = gql`
  ${PAGE_CURSORS_FRAGMENT}
  ${ORG_USER_FRAGMENT}
  query GetHighlightSearchData(
    $startTime: DateTime!
    $endTime: DateTime!
    $orderBy: [OrderBy!]
    $organizationIds: [ID!]
    $siteIds: [ID!]
    $roomIds: [ID!]
    $categories: [String!]
    $assignedUserIds: [String!]
    $after: String
    $first: Int
  ) {
    highlightSearch(
      query: {
        minTime: $startTime
        maxTime: $endTime
        organizationIds: $organizationIds
        siteIds: $siteIds
        roomIds: $roomIds
        categories: $categories
        assignedUserIds: $assignedUserIds
      }
      orderBy: $orderBy
      after: $after
      first: $first
    ) {
      pageCursors {
        ...PageCursorsFragment
      }
      edges {
        node {
          id
          description
          startTime
          endTime
          category
          feedback {
            edges {
              node {
                id
                rating
                comment
                user {
                  ...OrgUserNodeFragment
                }
              }
            }
          }
          organization {
            id
            name
          }
          site {
            id
            name
            timezone
          }
          room {
            id
            name
          }
          users {
            edges {
              ...OrgUserFragment
            }
          }
        }
      }
    }
  }
`

export const GET_HIGHLIGHT_SEARCH_FILTER = gql`
  ${ORG_USER_FRAGMENT}
  query GetHighlightSearchFilters {
    organizations {
      edges {
        node {
          id
          name
          sites {
            edges {
              node {
                id
                name
                rooms {
                  edges {
                    node {
                      id
                      name
                    }
                  }
                }
              }
            }
          }
          users {
            edges {
              ...OrgUserFragment
            }
          }
        }
      }
    }
  }
`
