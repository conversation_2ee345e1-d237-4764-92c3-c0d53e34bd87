import { useCallback } from 'react'
import { toast } from 'react-toastify'

import { gql, useMutation } from '@apollo/client'

import { TASK_FRAGMENT } from 'src/pages/EventsEditorPage/queries'

import {
  NextAnnotateTask,
  NextAnnotateTaskVariables,
  NextReviewTask,
  NextReviewTaskVariables,
} from './__generated__'

export const NEXT_ANNOTATE_TASK = gql`
  ${TASK_FRAGMENT}
  mutation NextAnnotateTask($taskNextInput: AnnotationTaskNextAnnotateInput!) {
    annotationTaskNextAnnotate(input: $taskNextInput) {
      success
      currentTask {
        ...TaskFragment
      }
      nextTask {
        ...TaskFragment
      }
    }
  }
`

export const NEXT_REVIEW_TASK = gql`
  ${TASK_FRAGMENT}
  mutation NextReviewTask($taskNextInput: AnnotationTaskNextReviewInput!) {
    annotationTaskNextReview(input: $taskNextInput) {
      success
      currentTask {
        ...TaskFragment
      }
      nextTask {
        ...TaskFragment
      }
    }
  }
`

export function useNextReviewTask() {
  const [nextReviewTask, { loading }] = useMutation<
    NextReviewTask,
    NextReviewTaskVariables
  >(NEXT_REVIEW_TASK)

  const next = useCallback(
    async (taskNextInput = {}) => {
      const { data } = await nextReviewTask({
        variables: { taskNextInput },
      })

      if (!data?.annotationTaskNextReview?.success) {
        toast.error(
          'Failed to get next review task, please try again or reach out if problem persists.'
        )
        return
      }

      return data.annotationTaskNextReview.nextTask
    },

    [nextReviewTask]
  )

  return { loading, next }
}

export function useNextAnnotateTask() {
  const [nextAnnotateTask, { loading }] = useMutation<
    NextAnnotateTask,
    NextAnnotateTaskVariables
  >(NEXT_ANNOTATE_TASK)

  const next = useCallback(
    async (taskNextInput = {}) => {
      const { data } = await nextAnnotateTask({
        variables: { taskNextInput },
      })

      if (!data?.annotationTaskNextAnnotate?.success) {
        toast.error(
          'Failed to get next annotation task, please try again or reach out if problem persists.'
        )
        return
      }

      return data.annotationTaskNextAnnotate.nextTask
    },

    [nextAnnotateTask]
  )

  return { loading, next }
}
