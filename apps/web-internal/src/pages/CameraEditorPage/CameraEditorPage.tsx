import { useCallback, useMemo } from 'react'
import { useParams } from 'react-router'

import { useMutation, useQuery } from '@apollo/client'
import * as Yup from 'yup'

import {
  Button,
  Col,
  FormActionRow,
  H3,
  Input,
  Tile,
  Form,
  ButtonLink,
  ArrowBack,
  theme,
} from '@apella/component-library'
import { CameraUpdateInput } from 'src/__generated__/globalTypes'
import { LoadingOverlay } from 'src/components/molecules/LoadingOverlay'
import { LocationPath } from 'src/router/LocationPath'
import { CAMERA_FAMILIES } from 'src/utils/constants'

import { GridPageTemplate } from '../../components/GridPageTemplate'
import {
  GetCameraQuery,
  GetCameraQueryVariables,
  UpdateCamera,
  UpdateCameraVariables,
} from './__generated__'
import { GET_CAMERA_QUERY, UPDATE_CAMERA } from './queries'

type CameraEditorLocationParams = 'cameraId'

const validationSchema = Yup.object({
  id: Yup.string().min(3).required('ID is required'),
  name: Yup.string().min(3).required('Name is required'),
  rtspUrl: Yup.string().matches(
    /^(?:(?:[0-9]{1,3}\.){3}[0-9]{1,3}|(?:https?|gs|rtsp):\/\/\S+)$/,
    {
      excludeEmptyString: true,
    }
  ),
  family: Yup.string().oneOf(CAMERA_FAMILIES).required('Family is required'),
})

export const CameraEditorPage = (): React.JSX.Element => {
  const cameraId = useParams<CameraEditorLocationParams>().cameraId!

  const { data: pageData, loading: cameraLoading } = useQuery<
    GetCameraQuery,
    GetCameraQueryVariables
  >(GET_CAMERA_QUERY, {
    variables: {
      cameraId,
    },
  })

  const [
    updateCamera,
    { loading: updateLoading, error: updateError, data: updatedData },
  ] = useMutation<UpdateCamera, UpdateCameraVariables>(UPDATE_CAMERA)

  const camera: CameraUpdateInput = useMemo(
    () => ({
      id: '',
      name: '',
      ...pageData?.camera,
    }),
    [pageData?.camera]
  )

  const onSubmit = useCallback(
    async ({ id, family, name, rtspUrl }: CameraUpdateInput) => {
      updateCamera({
        variables: {
          camera: {
            id,
            family,
            name,
            rtspUrl,
          },
        },
      })
    },
    [updateCamera]
  )

  return (
    <LoadingOverlay isLoading={cameraLoading}>
      <GridPageTemplate>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ButtonLink
            to={LocationPath.Cameras}
            buttonType="icon"
            color="alternate"
            appearance="link"
          >
            <ArrowBack size="sm" color={theme.palette.gray[60]} />
          </ButtonLink>
          <H3>Camera Editor</H3>
        </div>
        <Tile gutter>
          <Form
            initialValues={camera}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
          >
            <Input.Text name="id" label="ID" disabled={true} />
            <Input.Text name="name" label="Name" />
            <Input.Text name="rtspUrl" label="IP Address" />
            <Input.SingleSelect
              name="family"
              label="Family"
              css={{ width: '100%' }}
            >
              {CAMERA_FAMILIES.map((family) => (
                <Input.Option key={family} value={family} label={family} />
              ))}
            </Input.SingleSelect>
            <Input.Text name="roomId" label="Room ID" disabled={true} />
            <Input.Text name="siteId" label="Site ID" disabled={true} />
            <Input.Text
              name="organizationId"
              label="Organization ID"
              disabled={true}
            />
            <FormActionRow
              error={updateError?.message}
              saving={updateLoading}
              success={updatedData?.cameraUpdate?.success ?? undefined}
            >
              <Col>
                <Button size="lg" type="submit">
                  Update
                </Button>
              </Col>
            </FormActionRow>
          </Form>
        </Tile>
      </GridPageTemplate>
    </LoadingOverlay>
  )
}
