import { gql } from '@apollo/client'

export const GET_CAMERA_QUERY = gql`
  query GetCameraQuery($cameraId: String!) {
    camera(id: $cameraId) {
      id
      name
      rtspUrl
      family
      roomId
      siteId
      organizationId
    }
  }
`

export const UPDATE_CAMERA = gql`
  mutation UpdateCamera($camera: CameraUpdateInput!) {
    cameraUpdate(input: $camera) {
      success
      updatedCamera {
        id
        name
        rtspUrl
        family
      }
    }
  }
`
