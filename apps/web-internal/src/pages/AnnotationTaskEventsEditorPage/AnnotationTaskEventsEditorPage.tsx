import { Fragment, useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import {
  GetAnnotationTask,
  GetAnnotationTaskVariables,
} from 'src/pages/EventsEditorPage/__generated__'
import { EventsEditorPage } from 'src/pages/EventsEditorPage/EventsEditorPage'
import { GET_ANNOTATION_TASK } from 'src/pages/EventsEditorPage/queries'

import { useAnnotationTaskId } from './useAnnotationTaskId'

export const AnnotationTaskEventsEditorPage = (): React.JSX.Element => {
  const annotationTaskId = useAnnotationTaskId()!

  const { loading, data: taskData } = useQuery<
    GetAnnotationTask,
    GetAnnotationTaskVariables
  >(GET_ANNOTATION_TASK, {
    variables: {
      id: annotationTaskId,
    },
  })

  const annotationTask = taskData?.annotationTask

  return !loading && annotationTask ? (
    <EventsEditorPageWrapper annotationTask={annotationTask} />
  ) : (
    <Fragment />
  )
}

const EventsEditorPageWrapper = ({
  annotationTask,
}: {
  annotationTask: NonNullable<GetAnnotationTask['annotationTask']>
}) => {
  const minTime = useMemo(
    () => DateTime.fromISO(annotationTask.startTime),
    [annotationTask.startTime]
  )

  const maxTime = useMemo(
    () => DateTime.fromISO(annotationTask.endTime),
    [annotationTask.endTime]
  )

  return (
    <EventsEditorPage
      organizationId={annotationTask.organization.id}
      siteId={annotationTask.site.id}
      roomId={annotationTask.room.id}
      minTime={minTime}
      maxTime={maxTime}
      annotationTask={annotationTask}
      key={annotationTask.id}
    />
  )
}
