import { useCallback, useMemo } from 'react'
import { generatePath, useNavigate, useParams } from 'react-router'

import { useMutation, useQuery } from '@apollo/client'
import { string, array, boolean, object } from 'yup'

import {
  Button,
  Col,
  FormActionRow,
  H3,
  Input,
  Tile,
  Form,
  Option,
  ButtonLink,
  ArrowBack,
  theme,
} from '@apella/component-library'
import { ClusterUpdateInput } from 'src/__generated__/globalTypes'
import { LoadingOverlay } from 'src/components/molecules/LoadingOverlay'
import { LocationPath } from 'src/router/LocationPath'

import { GridPageTemplate } from '../../components/GridPageTemplate'
import {
  CreateCluster,
  CreateClusterVariables,
  GetClusterQuery,
  GetClusterQueryVariables,
  UpdateCluster,
  UpdateClusterVariables,
} from './__generated__'
import { GET_CLUSTER_QUERY, CREATE_CLUSTER, UPDATE_CLUSTER } from './queries'

type ClusterEditorLocationParams = 'clusterId'

const validationSchema = object({
  id: string().uuid().required('ID is required'),
  name: string()
    .min(3)
    .matches(
      /^[a-z0-9-]+$/,
      'ID can only contain lowercase letters, numbers, and hyphens'
    )
    .required('Name is required'),
  sites: array(string().required())
    .min(0)
    .required('At least one Site ID is required'),
  enableAudio: boolean().required(),
})

export const ClusterEditorPage = (): React.JSX.Element => {
  // Get the cluster id from the page parameters
  const navigate = useNavigate()

  const clusterId = useParams<ClusterEditorLocationParams>().clusterId!

  const isCreating = clusterId === 'new'

  const { data: pageData, loading: clusterLoading } = useQuery<
    GetClusterQuery,
    GetClusterQueryVariables
  >(GET_CLUSTER_QUERY, {
    variables: {
      clusterId,
      skipCluster: isCreating,
    },
  })

  // Create and Update Mutations
  const [
    createCluster,
    { loading: createLoading, error: createError, data: createdData },
  ] = useMutation<CreateCluster, CreateClusterVariables>(CREATE_CLUSTER, {
    refetchQueries: ['GetClusterSearchData'],
  })

  const [
    updateCluster,
    { loading: updateLoading, error: updateError, data: updatedData },
  ] = useMutation<UpdateCluster, UpdateClusterVariables>(UPDATE_CLUSTER, {
    refetchQueries: ['GetClusterSearchData'],
  })

  // Initial form data
  const cluster: ClusterUpdateInput = useMemo(
    () =>
      pageData?.cluster
        ? {
            ...pageData.cluster,
            sites: pageData.cluster.sites.edges.map((site) => site.node.id),
          }
        : {
            id: crypto.randomUUID(),
            enableAudio: false,
            name: '',
            sites: [],
          },
    [pageData?.cluster]
  )

  const siteOptions = useMemo(
    () =>
      pageData?.sites.edges.map((site) => (
        <Option
          key={site.node.id}
          value={site.node.id}
          label={site.node.name}
        />
      )),
    [pageData?.sites.edges]
  )

  const onSubmit = useCallback(
    async ({ id, name, sites, enableAudio }: ClusterUpdateInput) => {
      const upsertCluster: ClusterUpdateInput = {
        id,
        name,
        sites,
        enableAudio,
      }
      if (isCreating) {
        const createdClusterResults = await createCluster({
          variables: {
            cluster: upsertCluster,
          },
        })

        if (createdClusterResults.data?.clusterCreate?.success === true) {
          const path = generatePath(LocationPath.ClusterEditor, {
            clusterId:
              createdClusterResults.data.clusterCreate?.cluster?.id ?? '',
          })

          navigate(path)
        }
      } else {
        updateCluster({
          variables: {
            cluster: upsertCluster,
          },
        })
      }
    },
    [createCluster, isCreating, navigate, updateCluster]
  )

  return (
    <LoadingOverlay
      isLoading={clusterLoading || createLoading || updateLoading}
    >
      <GridPageTemplate>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ButtonLink
            to={LocationPath.Clusters}
            buttonType="icon"
            color="alternate"
            appearance="link"
          >
            <ArrowBack size="sm" color={theme.palette.gray[60]} />
          </ButtonLink>
          <H3>Cluster Editor</H3>
        </div>
        <Tile gutter>
          <Form
            initialValues={cluster}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
          >
            <Input.Text name="id" label="ID" disabled={true} />
            <Input.Text name="name" label="Name" />
            <Input.MultiSelect name="sites" label="Sites">
              {siteOptions}
            </Input.MultiSelect>
            <Input.Checkbox
              ariaLabel="enableAudio"
              name="enableAudio"
              label="Enable Audio"
            />
            <FormActionRow
              error={createError?.message ?? updateError?.message}
              saving={createLoading || updateLoading}
              success={
                createdData?.clusterCreate?.success ??
                updatedData?.clusterUpdate?.success ??
                undefined
              }
            >
              <Col>
                <Button size="lg" type="submit">
                  {isCreating ? 'Create' : 'Update'}
                </Button>
              </Col>
            </FormActionRow>
          </Form>
        </Tile>
      </GridPageTemplate>
    </LoadingOverlay>
  )
}
