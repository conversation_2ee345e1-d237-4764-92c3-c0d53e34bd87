import { gql } from '@apollo/client'

const CLUSTER_FRAGMENT = gql`
  fragment ClusterFragment on Cluster {
    id
    name
    enableAudio
  }
`

export const GET_CLUSTER_QUERY = gql`
  ${CLUSTER_FRAGMENT}
  query GetClusterQuery($clusterId: ID!, $skipCluster: Boolean!) {
    cluster(id: $clusterId) @skip(if: $skipCluster) {
      ...ClusterFragment
      sites {
        edges {
          node {
            id
            name
          }
        }
      }
    }
    sites {
      edges {
        node {
          id
          name
        }
      }
    }
  }
`

export const CREATE_CLUSTER = gql`
  ${CLUSTER_FRAGMENT}
  mutation CreateCluster($cluster: ClusterCreateInput!) {
    clusterCreate(input: $cluster) {
      success
      cluster {
        ...ClusterFragment
      }
    }
  }
`

export const UPDATE_CLUSTER = gql`
  ${CLUSTER_FRAGMENT}
  mutation UpdateCluster($cluster: ClusterUpdateInput!) {
    clusterUpdate(input: $cluster) {
      success
      cluster {
        ...ClusterFragment
      }
    }
  }
`
