import React from 'react'
import { <PERSON>, generatePath } from 'react-router'

import { useQuery } from '@apollo/client'

import {
  Col,
  H4,
  PaginatedTableColumn,
  Plus,
  RemotePaginatedTable,
  Edit,
  Button,
  FlexContainer,
  Row,
} from '@apella/component-library'
import { Direction } from 'src/__generated__/globalTypes'
import { FilterBarPageTemplate } from 'src/components/FilterBarPageTemplate'
import { FilterBarPageTemplateContent } from 'src/components/FilterBarPageTemplateContent'
import { FilterBarPageTemplateFilters } from 'src/components/FilterBarPageTemplateFilters'
import { LocationPath } from 'src/router/LocationPath'
import { pageSize } from 'src/utils/constants'

import {
  GetClusterSearchData,
  GetClusterSearchDataVariables,
} from './__generated__'
import { GET_CLUSTER_SEARCH_DATA } from './queries'

const DEFAULT_PAGING_STATE = {
  after: undefined,
  first: pageSize,
}

const DEFAULT_ORDER_BY_STATE: Pick<GetClusterSearchDataVariables, 'orderBy'> = {
  orderBy: [
    {
      sort: 'id',
      direction: Direction.ASC,
    },
  ],
}

export const ClusterSearchPage = (): React.JSX.Element => {
  const [state, setState] = React.useState<GetClusterSearchDataVariables>({
    ...DEFAULT_PAGING_STATE,
    ...DEFAULT_ORDER_BY_STATE,
  })

  const { loading: clustersLoading, data: clustersData } = useQuery<
    GetClusterSearchData,
    GetClusterSearchDataVariables
  >(GET_CLUSTER_SEARCH_DATA, {
    variables: {
      ...state,
    },
  })

  const tableData = React.useMemo(() => {
    const clusters = clustersData?.clusters?.edges ?? []
    return clusters.map((cluster) => ({ ...cluster.node }))
  }, [clustersData?.clusters?.edges])

  const columns: PaginatedTableColumn<(typeof tableData)[number]>[] =
    React.useMemo(
      () => [
        {
          name: 'ID',
          selector: 'id',
          sortAttribute: 'id',
        },
        {
          name: 'Name',
          selector: 'name',
          sortAttribute: 'name',
        },
        {
          name: 'Audio Enabled',
          sortAttribute: 'enableAudio',
          contents: (rowData) => <>{`${rowData.enableAudio}`}</>,
        },
        {
          name: 'Sites',
          contents: (rowData) => {
            return (
              <>
                {rowData.sites.edges.map((site) => site.node.name).join(', ')}
              </>
            )
          },
        },
        {
          name: '',
          contents: (rowData) => (
            <Link
              to={generatePath(LocationPath.ClusterEditor, {
                clusterId: rowData.id,
              })}
            >
              <Button size="sm" color="black" appearance="secondary">
                <Edit size="sm" />
              </Button>
            </Link>
          ),
        },
      ],
      []
    )

  return (
    <FilterBarPageTemplate>
      <FilterBarPageTemplateFilters>
        <Row>
          <Col css={{ width: '100%' }}>
            <FlexContainer justifyContent="space-between">
              <H4 as="h1">Clusters</H4>
              <Link
                to={generatePath(LocationPath.ClusterEditor, {
                  clusterId: 'new',
                })}
              >
                <Button>
                  <Plus size="sm" />
                  Create
                </Button>
              </Link>
            </FlexContainer>
          </Col>
        </Row>
      </FilterBarPageTemplateFilters>
      <FilterBarPageTemplateContent>
        <RemotePaginatedTable
          columns={columns}
          data={tableData}
          isLoading={clustersLoading}
          sortOrder={state.orderBy!}
          paginationType="cursor"
          onChangePage={(cursorItem) => {
            setState((prev) => ({
              ...prev,
              after: cursorItem.cursor,
            }))
          }}
          onChangeSort={(orderBy) => {
            setState((prev) => ({
              ...prev,
              ...DEFAULT_PAGING_STATE,
              orderBy,
            }))
          }}
          {...clustersData?.clusters?.pageCursors}
        />
      </FilterBarPageTemplateContent>
    </FilterBarPageTemplate>
  )
}
