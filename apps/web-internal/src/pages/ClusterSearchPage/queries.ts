import { gql } from '@apollo/client'

import { PAGE_CURSORS_FRAGMENT } from 'src/utils/graphqlFragments'

export const GET_CLUSTER_SEARCH_DATA = gql`
  ${PAGE_CURSORS_FRAGMENT}
  query GetClusterSearchData(
    $after: String
    $first: Int
    $orderBy: [OrderBy!]
  ) {
    clusters(after: $after, first: $first, orderBy: $orderBy) {
      pageCursors {
        ...PageCursorsFragment
      }
      edges {
        node {
          id
          name
          enableAudio
          sites {
            edges {
              node {
                id
                name
              }
            }
          }
        }
      }
    }
  }
`
