import { omit } from 'lodash'
import { DateTime } from 'luxon'

import { VideoProgress } from '@apella/component-library'
import { TaskStatus } from 'src/__generated__/globalTypes'
import { EVENT_TYPES, logEvent } from 'src/utils/analyticsEvents'

import { GetEventTypes } from '../../hooks/useEventTypes/__generated__'
import { maybeUpdateEventTimeToVideoProgress } from './helpers'
import {
  UnpersistedEvent,
  EventsEditorEvent,
  EventsEditorState,
  TimelineEvent,
} from './types'

type GetEventTypes_eventTypes_edges_node = NonNullable<
  GetEventTypes['eventTypes']
>['edges'][number]['node']

export const eventsEditorInitialState: EventsEditorState = {
  eventsToSave: {},
  playbackRate: 1,
  saving: false,
  activeEventState: {
    id: undefined,
    expanded: false,
    syncPlaybackWithEventTime: false,
  },
  showRemovedEvents: false,
  eventTypes: [],
}

export const eventsEditorReducerInitializer = ({
  organizationId,
  siteId,
  roomId,
  minTime,
  maxTime,
  annotationTask,
  query,
}: {
  annotationTask?: { id: string; status: TaskStatus }
  maxTime: DateTime
  minTime: DateTime
  organizationId: string
  query: URLSearchParams
  roomId: string
  siteId: string
}) => {
  const queryTime = query.get('time')
  const queryDateTime = !!queryTime
    ? DateTime.fromSeconds(Number.parseInt(queryTime))
    : undefined

  logEvent(EVENT_TYPES.LOAD_VIDEO_PAGE, {
    organizationId,
    siteId,
    roomId,
    minTime: minTime.toISO(),
    maxTime: maxTime.toISO(),
    annotationTaskId: annotationTask?.id,
    queryTime: queryDateTime?.toISO(),
  })

  return {
    ...eventsEditorInitialState,
    organizationId,
    siteId,
    roomId,
    minTime,
    maxTime,
    annotationTask,
    nextVideoStartTime: queryDateTime,
    showRemovedEvents: (() => {
      if (!annotationTask) return false

      switch (annotationTask.status) {
        case TaskStatus.DONE:
        case TaskStatus.IN_REVIEW:
        case TaskStatus.READY_FOR_REVIEW:
          return true

        case TaskStatus.CANCELLED:
        case TaskStatus.IN_PROGRESS:
        case TaskStatus.NOT_STARTED:
        default:
          return false
      }
    })(),
  }
}

export type EventsEditorAction =
  | { type: 'CLICKED_EVENT'; event: TimelineEvent }
  | {
      type: 'SELECTED_EVENT'
      event: TimelineEvent
    }
  | {
      type: 'UNSELECT_EVENT'
    }
  | {
      type: 'TOGGLE_SYNC_PLAYBACK_WITH_EVENT_TIME'
      event: EventsEditorEvent
      minTime: DateTime
    }
  | {
      type: 'UNSYNC_PLAYBACK_FROM_EVENT_TIME'
    }
  | {
      type: 'SET_ACTIVE_VIDEO'
      time?: DateTime
    }
  | { type: 'SET_PROGRESS'; progress: VideoProgress }
  | { type: 'CREATE_EVENT'; event: UnpersistedEvent }
  | {
      type: 'SAVE_EVENT'
      event: EventsEditorEvent
    }
  | {
      type: 'UPDATE_EVENT'
      event: EventsEditorEvent
    }
  | { type: 'REMOVE_EVENT'; event: EventsEditorEvent }
  | { type: 'EVENTS_SAVING' }
  | { type: 'EVENTS_SAVED'; events: EventsEditorEvent[] }
  | { type: 'SET_ACTIVE_CAMERA'; cameraId: string }
  | {
      type: 'UPDATE_EVENT_TYPES'
      eventTypes: GetEventTypes_eventTypes_edges_node[]
    }
  | { type: 'SET_IS_ACTIVE_EVENT_EXPANDED'; isActiveEventExpanded: boolean }
  | { type: 'TOGGLE_SHOW_REMOVED_EVENTS' }

export const eventsEditorReducer = (
  state: EventsEditorState,
  action: EventsEditorAction
): EventsEditorState => {
  switch (action.type) {
    case 'CLICKED_EVENT': {
      return {
        ...state,
        activeEventState: {
          ...state.activeEventState,
          syncPlaybackWithEventTime:
            action.event.id === state.activeEventState.id &&
            state.activeEventState.syncPlaybackWithEventTime,
          id: action.event.id,
          expanded: action.event.id === state.activeEventState.id,
        },
      }
    }
    case 'SELECTED_EVENT': {
      return {
        ...state,
        activeEventState: {
          ...state.activeEventState,
          syncPlaybackWithEventTime:
            action.event.id === state.activeEventState.id &&
            state.activeEventState.syncPlaybackWithEventTime,
          id: action.event.id,
        },
      }
    }
    case 'UNSELECT_EVENT':
      return {
        ...state,
        activeEventState: {
          id: undefined,
          expanded: false,
          syncPlaybackWithEventTime: false,
        },
      }
    case 'TOGGLE_SYNC_PLAYBACK_WITH_EVENT_TIME': {
      const syncPlaybackWithEventTime =
        !state.activeEventState.syncPlaybackWithEventTime

      const activeEventState = {
        ...state.activeEventState,
        id: action.event.id,
        syncPlaybackWithEventTime,
      }

      if (syncPlaybackWithEventTime && state.progress?.playerCurrentTime) {
        const updatedEvent = maybeUpdateEventTimeToVideoProgress(
          state,
          action.event,
          state.progress.playerCurrentTime
        )

        if (!!updatedEvent) {
          return {
            ...state,
            activeEventState,
            eventsToSave: {
              ...state.eventsToSave,
              [updatedEvent.id]: {
                event: updatedEvent,
                type: state.eventsToSave[updatedEvent.id]?.type ?? 'UPDATE',
              },
            },
          }
        }
      }

      return {
        ...state,
        activeEventState,
      }
    }
    case 'UNSYNC_PLAYBACK_FROM_EVENT_TIME':
      return {
        ...state,
        activeEventState: {
          ...state.activeEventState,
          syncPlaybackWithEventTime: false,
        },
      }
    case 'SET_ACTIVE_VIDEO':
      return {
        ...state,
        nextVideoStartTime: action.time || undefined,
      }
    case 'SET_PROGRESS':
      return {
        ...state,
        progress: action.progress,
      }
    case 'CREATE_EVENT':
      return {
        ...state,
        activeEventState: {
          id: action.event.id,
          expanded: true,
          syncPlaybackWithEventTime: false,
        },
        eventsToSave: {
          ...state.eventsToSave,
          [action.event.id]: {
            event: action.event,
            type: 'CREATE',
          },
        },
      }
    case 'UPDATE_EVENT':
      return {
        ...state,
        eventsToSave: {
          ...state.eventsToSave,
          [action.event.id]: {
            event: {
              ...action.event,
              cameraId: state.activeCamera ?? action.event.cameraId ?? null,
            },
            type: state.eventsToSave[action.event.id]?.type ?? 'UPDATE',
          },
        },
      }
    case 'SAVE_EVENT': {
      return {
        ...state,
        activeEventState: {
          id: action.event.id,
          expanded: false,
          syncPlaybackWithEventTime: false,
        },
        eventsToSave: {
          ...state.eventsToSave,
          [action.event.id]: {
            event: {
              ...action.event,
              cameraId: state.activeCamera ?? action.event.cameraId ?? null,
            },
            type: state.eventsToSave[action.event.id]?.type ?? 'UPDATE',
          },
        },
      }
    }
    case 'REMOVE_EVENT':
      // If we hadn't yet saved the event (we were going to "create" it),
      // then just remove it from `eventsToSave` instead of marking it for deletion
      if (state.eventsToSave[action.event.id]?.type === 'CREATE') {
        const newEventsToSave = omit(state.eventsToSave, action.event.id)
        return {
          ...state,
          eventsToSave: newEventsToSave,
        }
      }
      return {
        ...state,
        activeEventState: {
          id: undefined,
          expanded: false,
          syncPlaybackWithEventTime: false,
        },
        eventsToSave: {
          ...state.eventsToSave,
          [action.event.id]: {
            event: action.event,
            type: 'REMOVE',
          },
        },
      }
    case 'EVENTS_SAVING':
      return {
        ...state,
        saving: true,
      }
    case 'EVENTS_SAVED':
      return {
        ...state,
        eventsToSave: omit(
          state.eventsToSave,
          action.events.map((event) => event.id)
        ),
        saving: false,
      }
    case 'SET_ACTIVE_CAMERA':
      return {
        ...state,
        activeCamera: action.cameraId,
      }
    case 'UPDATE_EVENT_TYPES':
      return {
        ...state,
        eventTypes: action.eventTypes,
      }
    case 'SET_IS_ACTIVE_EVENT_EXPANDED':
      return {
        ...state,
        activeEventState: state.activeEventState.id
          ? {
              ...state.activeEventState,
              expanded: action.isActiveEventExpanded,
            }
          : state.activeEventState,
      }
    case 'TOGGLE_SHOW_REMOVED_EVENTS':
      return {
        ...state,
        showRemovedEvents: !state.showRemovedEvents,
      }
    default:
      throw new Error()
  }
}
