import React, { useState } from 'react'
import Linkify from 'react-linkify'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import {
  Button,
  Col,
  ContentCopy,
  Grid,
  P2,
  remSpacing,
  Row,
  ZIndex,
} from '@apella/component-library'
import { Drawer, DrawerToggleButton } from 'src/components/atoms/Drawer/Drawer'
import { VideoNavDialog } from 'src/components/molecules/VideoNavDialog/VideoNavDialog'
import { TaskAnnotatorSelect } from 'src/components/TaskAnnotatorSelect'
import { TaskReviewerSelect } from 'src/components/TaskReviewerSelect'
import { EventsEditorTitle } from 'src/pages/EventsEditorPage/EventsEditorHeader/EventsEditorTitle'
import { ANNOTATOR_ROLE, REVIEWER_ROLE } from 'src/utils/constants'

import {
  GetAnnotationTask,
  GetTaskUsers,
  GetTaskUsersVariables,
} from '../__generated__'
import { useEventsEditorContext } from '../EventsEditorContext'
import { TASK_USERS_QUERY } from '../queries'
import { EventsEditorEvent } from '../types'
import { EventsEditorKeyboardShortcutHelp } from './EventsEditorKeyboardShortcutHelp'
import { TaskActionButtons } from './TaskActionButtons'
import { TaskStatusSelect } from './TaskStatusSelect'

type GetAnnotationTask_annotationTask = NonNullable<
  GetAnnotationTask['annotationTask']
>

type EventsEditorHeaderProps = {
  minTime: DateTime
  maxTime: DateTime
  roomName?: string
  annotationTask?: GetAnnotationTask_annotationTask
  events: EventsEditorEvent[]
}

const HeaderContainer = ({ children }: { children: React.ReactNode }) => {
  const theme = useTheme()

  return (
    <div
      css={{
        borderBottom: `1px solid ${theme.palette.gray[20]}`,
        padding: `${remSpacing.medium} ${remSpacing.medium} ${remSpacing.medium} ${remSpacing.gutter}`,
      }}
    >
      <Row
        css={{
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'nowrap',
          gap: remSpacing.small,
        }}
      >
        {children}
      </Row>
    </div>
  )
}

const AnnotationTaskHeader = ({
  minTime,
  maxTime,
  roomName,
  annotationTask,
  events,
}: EventsEditorHeaderProps & {
  annotationTask: GetAnnotationTask_annotationTask
  events: EventsEditorEvent[]
}): React.JSX.Element => {
  const [isAnnotatorBarVisible, setIsAnnotatorBarVisible] = useState(false)

  return (
    <>
      <HeaderContainer>
        <Col
          onClick={() => setIsAnnotatorBarVisible((isVisible) => !isVisible)}
          css={{
            cursor: 'pointer',
            flexShrink: 1,
            overflow: 'hidden',
          }}
        >
          <EventsEditorTitle
            minTime={minTime}
            maxTime={maxTime}
            roomName={roomName}
            annotationTask={annotationTask}
          />
        </Col>
        <Col
          css={{
            display: 'flex',
            alignItems: 'center',
            zIndex: ZIndex.ABOVE,
            gap: remSpacing.small,
          }}
        >
          <TaskActionButtons annotationTask={annotationTask} events={events} />
          <EventsEditorKeyboardShortcutHelp />
          <CopyTaskLinkButton />
          <DrawerToggleButton
            isOpen={isAnnotatorBarVisible}
            onClick={() => setIsAnnotatorBarVisible((isVisible) => !isVisible)}
          />
        </Col>
      </HeaderContainer>

      {isAnnotatorBarVisible && (
        <Drawer onClose={() => setIsAnnotatorBarVisible(false)}>
          <AnnotationTaskBar annotationTask={annotationTask} />
        </Drawer>
      )}
    </>
  )
}

const VideoOnlyHeader = ({
  minTime,
  maxTime,
  roomName,
}: EventsEditorHeaderProps): React.JSX.Element => {
  const { organizationId, siteId, roomId } = useEventsEditorContext()
  return (
    <HeaderContainer>
      <Col
        css={{
          flexShrink: 1,
          overflow: 'hidden',
        }}
      >
        <EventsEditorTitle
          minTime={minTime}
          maxTime={maxTime}
          roomName={roomName}
        />
      </Col>
      <Col
        css={{
          display: 'flex',
          alignItems: 'center',
          zIndex: ZIndex.ABOVE,
          gap: remSpacing.small,
        }}
      >
        <VideoNavDialog
          organizationId={organizationId}
          siteId={siteId}
          roomId={roomId}
          date={minTime}
        />
        <EventsEditorKeyboardShortcutHelp />
        <CopyTaskLinkButton />
      </Col>
    </HeaderContainer>
  )
}

export const EventsEditorHeader = ({
  annotationTask,
  ...props
}: EventsEditorHeaderProps): React.JSX.Element => {
  return annotationTask ? (
    <AnnotationTaskHeader annotationTask={annotationTask} {...props} />
  ) : (
    <VideoOnlyHeader {...props} />
  )
}

const targetBlankDecorator = (
  decoratedHref: string,
  decoratedText: string,
  key: number
) => (
  <a
    href={decoratedHref}
    key={key}
    target="_blank"
    rel="noreferrer"
    referrerPolicy="same-origin"
  >
    {decoratedText}
  </a>
)

const AnnotationTaskBar = ({
  annotationTask,
}: {
  annotationTask: GetAnnotationTask_annotationTask
}) => {
  const theme = useTheme()
  const { data: taskUsersData } = useQuery<GetTaskUsers, GetTaskUsersVariables>(
    TASK_USERS_QUERY,
    {
      variables: {
        annotatorRole: ANNOTATOR_ROLE,
        reviewerRole: REVIEWER_ROLE,
      },
    }
  )

  const annotators = React.useMemo(
    () => taskUsersData?.annotators.edges ?? [],
    [taskUsersData?.annotators.edges]
  )
  const reviewers = React.useMemo(
    () => taskUsersData?.reviewers.edges ?? [],
    [taskUsersData?.reviewers.edges]
  )

  return (
    <Grid css={{ marginBottom: remSpacing.small }}>
      <Row gutter>
        <Col>
          <div>
            <label>Annotator</label>
          </div>
          <TaskAnnotatorSelect
            taskId={annotationTask.id}
            selectedId={annotationTask.annotatorUserId || undefined}
            annotators={annotators}
            site={annotationTask.site.name}
            room={annotationTask.room.name}
          />
        </Col>
        <Col>
          <div>
            <label>Reviewer</label>
          </div>
          <TaskReviewerSelect
            taskId={annotationTask.id}
            selectedId={annotationTask.reviewerUserId || undefined}
            reviewers={reviewers}
            site={annotationTask.site.name}
            room={annotationTask.room.name}
          />
        </Col>

        <TaskStatusSelect
          taskId={annotationTask.id}
          status={annotationTask.status}
          cancelledReason={annotationTask.cancelledReason}
          site={annotationTask.site.name}
          room={annotationTask.room.name}
        />
      </Row>
      <Row gutter>
        <Col css={{ width: '100%' }}>
          <label>Task Description</label>
          <P2
            css={{
              marginTop: remSpacing.small,
              color: theme.palette.text.secondary,
              overflow: 'auto',
              overflowWrap: 'break-word',
              maxHeight: 300,
            }}
          >
            <Linkify componentDecorator={targetBlankDecorator}>
              {annotationTask.type.description}
            </Linkify>
          </P2>
        </Col>
      </Row>
    </Grid>
  )
}

const CopyTaskLinkButton = () => (
  <Button
    title="Copy link to task"
    color="alternate"
    buttonType="icon"
    onClick={async () => {
      await navigator.clipboard.writeText(window.location.href)
      toast.info('Copied!')
    }}
  >
    <ContentCopy size="sm" />
  </Button>
)
