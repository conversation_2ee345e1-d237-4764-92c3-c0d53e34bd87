import { useId } from 'react'
import { toast } from 'react-toastify'

import { useMutation } from '@apollo/client'

import { Col, Option, SingleSelect } from '@apella/component-library'
import { CancelledReason, TaskStatus } from 'src/__generated__/globalTypes'
import { UpdateTask, UpdateTaskVariables } from 'src/components/__generated__'
import { UPDATE_ANNOTATION_TASK } from 'src/components/taskMutations'
import {
  CancelledReasonReadable,
  TaskStatusReadable,
} from 'src/pages/AnnotationTasksPage/TaskStatusReadable'

import { logger } from '../../../utils/logging'

interface TaskStatusSelectProps {
  cancelledReason: CancelledReason | null
  room: string
  site: string
  status: TaskStatus
  taskId: string
}

export const TaskStatusSelect = ({
  taskId,
  status,
  cancelledReason,
}: TaskStatusSelectProps): React.JSX.Element => {
  const statusId = useId()
  const cancelledReasonId = useId()

  const [updateTask] = useMutation<UpdateTask, UpdateTaskVariables>(
    UPDATE_ANNOTATION_TASK
  )

  return (
    <>
      <Col>
        <div>
          <label htmlFor={statusId}>Status</label>
        </div>

        <SingleSelect
          id={statusId}
          name="status"
          label="Status"
          value={status}
          onChange={(value) => {
            updateTask({
              variables: {
                taskUpdateInput: {
                  id: taskId,
                  status: value as TaskStatus,
                },
              },
            }).catch((error) => {
              logger.log(error.message)
              toast.error('Failed to update task status')
            })
          }}
        >
          {Object.entries(TaskStatusReadable).map(([status, statusName]) => (
            <Option key={status} label={statusName} value={status} />
          ))}
        </SingleSelect>
      </Col>

      {status == TaskStatus.CANCELLED && (
        <Col>
          <div>
            <label htmlFor={cancelledReasonId}>Cancelled Reason</label>
          </div>
          <SingleSelect
            id={cancelledReasonId}
            name="cancelledReason"
            label="No Cancelled Reason"
            value={cancelledReason ?? undefined}
            onChange={(value) => {
              updateTask({
                variables: {
                  taskUpdateInput: {
                    id: taskId,
                    cancelledReason: value as CancelledReason,
                  },
                },
              }).catch((error) => {
                logger.log(error.message)
                toast.error('Failed to update task status')
              })
            }}
          >
            {Object.entries(CancelledReasonReadable).map(([value, label]) => (
              <Option key={value} label={label} value={value} />
            ))}
          </SingleSelect>
        </Col>
      )}
    </>
  )
}
