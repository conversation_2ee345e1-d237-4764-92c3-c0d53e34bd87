import { useState } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'
import { rem } from 'polished'

import { useSize } from '@apella/hooks'

import { BIG_TICK_HEIGHT } from './cssConsts'

interface HourMarkerProps {
  dt: DateTime
  format: string
  isFirst: boolean
  textHeight: number
}

export const HourMarker = ({
  dt,
  isFirst,
  format,
  textHeight,
}: HourMarkerProps) => {
  const theme = useTheme()
  const [hourMarkerRef, setHourMarkerRef] = useState<HTMLDivElement | null>(
    null
  )
  const hourMarkerSize = useSize(hourMarkerRef)

  const showDay = isFirst || dt.hour === 0

  return (
    <div
      css={{
        display: 'inline-block',
        position: 'relative',
        fontSize: rem(`${textHeight}px`),
        color: theme.palette.gray[40],
      }}
      style={{
        left: hourMarkerSize && -hourMarkerSize?.width / 2,
      }}
    >
      <div css={{ height: textHeight, whiteSpace: 'nowrap' }}>
        {showDay && dt.toLocaleString({ month: 'short', day: 'numeric' })}
      </div>
      <div css={{ display: 'inline-block' }} ref={setHourMarkerRef}>
        {dt.toFormat(format)}
        <div
          css={{
            background: theme.palette.gray[40],
            height: BIG_TICK_HEIGHT,
            width: 1,
            margin: 'auto',
          }}
        />
      </div>
    </div>
  )
}
