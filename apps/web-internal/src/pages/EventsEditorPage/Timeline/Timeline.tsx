import {
  Dispatch,
  forwardRef,
  memo,
  MouseEvent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>vent<PERSON><PERSON><PERSON>,
  SetStateAction,
  useCallback,
  useContext,
  useMemo,
  useRef,
  useState,
  WheelEventHandler,
} from 'react'

import { useTheme } from '@emotion/react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import {
  FlexContainer,
  FlexItem,
  MarkerShape,
  remSpacing,
  VideoProgress,
  ZIndex,
} from '@apella/component-library'
import { usePosition, useSize } from '@apella/hooks'
import { ScheduleSlider } from 'src/components/molecules/ScheduleSlider/ScheduleSlider'
import { useEventTypes } from 'src/hooks/useEventTypes'
import { useTaskContextEvents } from 'src/hooks/useTaskContextEvents'
import { UNIDENTIFIED } from 'src/types/event'

import { LocaleContext } from '../../../contexts/locale'
import {
  GetCaseData,
  GetCaseDataVariables,
  GetOBXData,
  GetOBXDataVariables,
} from '../__generated__'
import {
  ON_WHEEL_SENSITIVITY,
  ZOOM_LEVEL_MAX,
  ZOOM_LEVEL_MIN,
  ZOOM_LEVEL_STEP,
  ZOOM_LEVEL_TO_SHOW_HALF_HOUR_TICK,
} from '../constants'
import { useEventsEditorContext } from '../EventsEditorContext'
import {
  calculateMiniMapPosition,
  calculateTimelinePosition,
  calculateTimePercent,
  getColorByEvent,
  getTimeMarkers,
} from '../helpers'
import { OccupancySlider } from '../OccupancySlider'
import { GET_CASE_DATA, GET_OBX_DATA } from '../queries'
import { TimelineControlGroup } from '../TimelineControlGroup'
import {
  Case,
  EventsEditorEvent,
  OnEventSelected,
  Staff,
  TimelineEvent,
} from '../types'
import {
  CURRENT_TIME_PILL_HEIGHT,
  HOUR_TEXT_HEIGHT,
  HOUR_TEXT_HEIGHT_THEATER,
  MID_HOUR_TEXT_HEIGHT,
  MID_HOUR_TEXT_HEIGHT_THEATER,
  TIME_MARKERS_HEIGHT,
} from './cssConsts'
import { EventLine, TimelineType } from './EventLine'
import { HourMarker } from './HourMarker'
import { MinuteMarker } from './MinuteMarker'
import { ProgressLine } from './ProgressLine'
import {
  TimelineEventMarker,
  TimelineEventMarkerDot,
} from './TimelineEventMarkerDot'

const getTimelineSecondsFromZoomLevel = (
  totalTimelineSeconds: number,
  zoomLevel: number
) => totalTimelineSeconds / Math.exp(zoomLevel / 10)

const OBX_SOURCE = 'OBX'

export const Timeline = memo(function Timeline({
  activeTimeWindow,
  setActiveTimeWindow,
  events,
  progress,
  seekTo,
  onEventSelected,
  minTime,
  maxTime,
}: {
  activeTimeWindow: [DateTime, DateTime]
  setActiveTimeWindow: Dispatch<SetStateAction<[DateTime, DateTime]>>
  events: EventsEditorEvent[]
  progress?: VideoProgress
  seekTo: (time: DateTime) => void
  onEventSelected: OnEventSelected
  minTime: DateTime
  maxTime: DateTime
}): React.JSX.Element {
  const timezone = useContext(LocaleContext)
  const theme = useTheme()
  const [minViewTime, maxViewTime] = activeTimeWindow
  const [zoomLevel, setZoomLevel] = useState(ZOOM_LEVEL_MIN)

  const { organizationId, siteId, roomId } = useEventsEditorContext()

  const { data: casesData } = useQuery<GetCaseData, GetCaseDataVariables>(
    GET_CASE_DATA,
    {
      variables: {
        minTime: minTime.toISO(),
        maxTime: maxTime.toISO(),
        siteIds: [siteId],
        roomIds: [roomId],
      },
    }
  )
  const cases: Case[] = useMemo(
    () =>
      casesData?.cases?.edges
        .map((edge) => edge.node)
        .map(
          ({
            id,
            room,
            site,
            scheduledStartTime,
            scheduledEndTime,
            caseStaff,
            primaryCaseProcedures,
          }) => ({
            id: id,
            room,
            site,
            scheduledStartTime:
              DateTime.fromISO(scheduledStartTime).setZone(timezone),
            scheduledEndTime:
              DateTime.fromISO(scheduledEndTime).setZone(timezone),
            staff: caseStaff
              .map((cs) =>
                cs
                  ? {
                      firstName: cs.staff?.firstName,
                      lastName: cs.staff?.lastName,
                      displayName: cs.staff?.lastName,
                      id: cs.staff?.id,
                      role: cs.role,
                    }
                  : undefined
              )
              .filter((s): s is Staff => s !== undefined),
            procedures: primaryCaseProcedures
              .map((pcp) => pcp?.procedure.name)
              .filter((procedureName) => procedureName !== ''),
          })
        ) ?? [],
    [casesData?.cases?.edges, timezone]
  )
  const caseIds = useMemo(() => cases.map((c) => c.id), [cases])
  const { data: obxData } = useQuery<GetOBXData, GetOBXDataVariables>(
    GET_OBX_DATA,
    {
      variables: {
        organizationId,
        siteIds: [siteId],
        roomIds: [roomId],
        caseIds,
        typeIds: ['OBSERVED_ANESTHESIA_READY'],
      },
      skip: !caseIds.length,
    }
  )
  const observations: TimelineEvent[] = useMemo(
    () =>
      obxData?.observation.edges.map((e) => ({
        id: e.node.id,
        label: 'Observed Anesthesia Ready',
        name: 'anesthesia_draping',
        startTime: e.node.observationTime,
        source: OBX_SOURCE,
        sourceType: OBX_SOURCE,
        processTime: e.node.recordedTime ?? e.node.observationTime,
        organizationId,
        siteId,
        roomId,
      })) ?? [],
    [obxData?.observation?.edges, organizationId, siteId, roomId]
  )

  const timeMarkers = useMemo(
    () => getTimeMarkers(minViewTime, maxViewTime, timezone),
    [minViewTime, maxViewTime, timezone]
  )

  const timelineSizeRef = useRef<HTMLDivElement>(null)
  const timelineSize = useSize(timelineSizeRef.current)

  const calculatePosition = useCallback(
    (dt?: DateTime): number =>
      calculateTimelinePosition(timelineSize, minViewTime, maxViewTime, dt),
    [timelineSize, minViewTime, maxViewTime]
  )

  const containerRef = useRef<HTMLDivElement>(null)
  const containerPosition = usePosition(containerRef.current ?? null)

  const totalTimelineSeconds = useMemo(
    () => maxViewTime.toSeconds() - minViewTime.toSeconds(),
    [minViewTime, maxViewTime]
  )
  const getPositionFromMouseEvent = useCallback(
    (e: ReactMouseEvent) => {
      if (!containerPosition || !timelineSize) {
        return
      }

      const numPixelsOnTimeline = e.clientX - containerPosition.x
      const percentageOfTimeline = numPixelsOnTimeline / timelineSize.width
      return percentageOfTimeline
    },
    [containerPosition, timelineSize]
  )
  const getSecondsFromMouseEvent = useCallback(
    (e: ReactMouseEvent) => {
      const position = getPositionFromMouseEvent(e)
      return position && position * totalTimelineSeconds
    },
    [getPositionFromMouseEvent, totalTimelineSeconds]
  )

  const onClick: MouseEventHandler = useCallback(
    (e: ReactMouseEvent) => {
      const seconds = getSecondsFromMouseEvent(e)
      seconds && seekTo(minViewTime.setZone(timezone).plus({ seconds }))
    },
    [getSecondsFromMouseEvent, minViewTime, seekTo, timezone]
  )

  const [hoverLineLeft, setHoverLineLeft] = useState<number>()
  const [hoverLineTime, setHoverLineTime] = useState<DateTime>()
  const onMouseMove: MouseEventHandler = useCallback(
    (e) => {
      if (!containerPosition || !timelineSize) return

      const left = e.clientX - containerPosition.x

      if (left < 0 || left > timelineSize.width) {
        setHoverLineLeft(undefined)
      } else {
        setHoverLineLeft(left)
        const seconds = getSecondsFromMouseEvent(e)
        const time = minViewTime.plus({ seconds }).setZone(timezone)
        setHoverLineTime(time)
      }
    },
    [
      setHoverLineLeft,
      containerPosition,
      timelineSize,
      getSecondsFromMouseEvent,
      timezone,
      minViewTime,
    ]
  )

  const onMouseLeave = useCallback(() => {
    setHoverLineLeft(undefined)
  }, [setHoverLineLeft])

  const handleZoomLevel = useCallback(
    (zLevel: number, timestamp?: DateTime, timestampPosition = 0.5) => {
      const newZoomLevel = Math.min(
        Math.max(zLevel, ZOOM_LEVEL_MIN),
        ZOOM_LEVEL_MAX
      )
      if (newZoomLevel === zoomLevel && timestampPosition !== 0.5) {
        return
      }
      document.activeElement && (document.activeElement as HTMLElement).blur()
      setZoomLevel(newZoomLevel)

      if (newZoomLevel === ZOOM_LEVEL_MIN) {
        setActiveTimeWindow([minTime, maxTime])
        return
      }

      const totalTimelineSeconds = maxTime.toSeconds() - minTime.toSeconds()

      const oldTotalTimelineSeconds = getTimelineSecondsFromZoomLevel(
        totalTimelineSeconds,
        zoomLevel
      )
      const progressTime =
        progress?.playerCurrentTime ?? minTime.setZone(timezone)

      // If we have a timestamp, use that as the target time, otherwise use
      // the current progress time if it's within the active time window,
      // otherwise use the middle of the active time window
      const targetTime =
        timestamp ||
        (progressTime >= minViewTime && progressTime <= maxViewTime
          ? progressTime
          : minViewTime.plus({ seconds: oldTotalTimelineSeconds / 2 }))

      const newTotalTimelineSeconds = getTimelineSecondsFromZoomLevel(
        totalTimelineSeconds,
        newZoomLevel
      )

      const newMinViewTime = DateTime.max(
        minTime,
        targetTime.minus({
          seconds:
            newTotalTimelineSeconds * timestampPosition +
            Math.max(
              0,
              newTotalTimelineSeconds * (1 - timestampPosition) -
                maxTime.diff(targetTime).as('seconds')
            ),
        })
      )
      const newMaxViewTime = DateTime.min(
        maxTime,
        targetTime.plus({
          seconds:
            newTotalTimelineSeconds * (1 - timestampPosition) +
            Math.max(
              0,
              newTotalTimelineSeconds * timestampPosition -
                targetTime.diff(minTime).as('seconds')
            ),
        })
      )

      setActiveTimeWindow([newMinViewTime, newMaxViewTime])
    },
    [
      zoomLevel,
      maxTime,
      minTime,
      progress?.playerCurrentTime,
      timezone,
      minViewTime,
      maxViewTime,
      setActiveTimeWindow,
    ]
  )

  const onWheel: WheelEventHandler = useCallback(
    (e) => {
      if (e.deltaX !== 0) {
        if (+minViewTime === +minTime && +maxViewTime === +maxTime) {
          // No movement if the viewport is already at the min/max time
          return
        }

        let newViewportStart: DateTime
        let newViewportEnd: DateTime
        const viewportWidthInSeconds = maxViewTime
          .diff(minViewTime)
          .as('seconds')
        const scrollMultiplier = Math.abs(e.deltaX) / 25 // arbitrary number to make scrolling feel better

        if (e.deltaX < -ON_WHEEL_SENSITIVITY && +minViewTime > +minTime) {
          newViewportStart = DateTime.max(
            minViewTime.minus({ minutes: 1 * scrollMultiplier }),
            minTime
          )
          newViewportEnd = newViewportStart.plus({
            seconds: viewportWidthInSeconds,
          })
          setActiveTimeWindow([newViewportStart, newViewportEnd])
        } else if (e.deltaX > ON_WHEEL_SENSITIVITY && +maxViewTime < +maxTime) {
          newViewportEnd = DateTime.min(
            maxViewTime.plus({ minutes: 1 * scrollMultiplier }),
            maxTime
          )
          newViewportStart = newViewportEnd.minus({
            seconds: viewportWidthInSeconds,
          })
          setActiveTimeWindow([newViewportStart, newViewportEnd])
        }
      } else if (e.deltaY !== 0) {
        const position = getPositionFromMouseEvent(e)
        const seconds = getSecondsFromMouseEvent(e)
        const mouseDateTime = minViewTime.plus({ seconds })

        if (e.deltaY < -ON_WHEEL_SENSITIVITY) {
          handleZoomLevel(zoomLevel + ZOOM_LEVEL_STEP, mouseDateTime, position)
        } else if (e.deltaY > ON_WHEEL_SENSITIVITY) {
          handleZoomLevel(zoomLevel - ZOOM_LEVEL_STEP, mouseDateTime, position)
        }
      }
    },
    [
      minViewTime,
      maxViewTime,
      minTime,
      maxTime,
      zoomLevel,
      getPositionFromMouseEvent,
      getSecondsFromMouseEvent,
      handleZoomLevel,
      setActiveTimeWindow,
    ]
  )

  return (
    <>
      <div
        css={{ position: 'relative', cursor: 'pointer' }}
        onMouseMove={onMouseMove}
        onMouseLeave={onMouseLeave}
        onClick={onClick}
        onWheel={onWheel}
        ref={containerRef}
      >
        <ProgressLine
          time={progress?.playerCurrentTime}
          left={calculatePosition(progress?.playerCurrentTime)}
          backgroundColor={theme.palette.gray[50]}
          color={theme.palette.text.alternate}
        />
        <TimeMarkers
          timeMarkers={timeMarkers}
          calculatePosition={calculatePosition}
          zoomLevel={zoomLevel}
        />
        <ContextPanel
          activeTimeWindow={activeTimeWindow}
          minTime={minTime}
          maxTime={maxTime}
          roomId={roomId}
          cases={cases}
          events={events}
          observations={observations}
          onEventSelected={onEventSelected}
          onClickTimeline={onClick}
          ref={timelineSizeRef}
        />

        <ProgressLine
          time={!!hoverLineLeft ? hoverLineTime : undefined}
          left={hoverLineLeft}
          backgroundColor={
            !!hoverLineLeft ? theme.palette.gray[30] : 'transparent'
          }
        />
      </div>

      <FlexContainer
        css={{
          border: `1px solid ${theme.palette.gray[30]}`,
          borderRadius: remSpacing.xsmall,
          boxShadow: '0px 6px 12px rgba(0, 0, 0, 0.2)',
          margin: remSpacing.xxsmall,
          height: 40,
        }}
      >
        <div
          css={{
            width: '70%',
            display: 'flex',
            alignItems: 'center',
            borderRight: `1px solid ${theme.palette.gray[30]}`,
          }}
        >
          <TimelineMinimap
            activeTimeWindow={activeTimeWindow}
            setActiveTimeWindow={setActiveTimeWindow}
            minTime={minTime}
            maxTime={maxTime}
            roomId={roomId}
            events={events}
            observations={observations}
            onWheel={onWheel}
          />
        </div>
        <div
          css={{
            width: '30%',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <TimelineControlGroup
            activeTimeWindow={activeTimeWindow}
            setActiveTimeWindow={setActiveTimeWindow}
            minTime={minTime}
            maxTime={maxTime}
            zoomLevel={zoomLevel}
            handleZoomLevel={handleZoomLevel}
          />
        </div>
      </FlexContainer>
    </>
  )
})

interface MinimapViewportProps {
  isShadow?: boolean
  viewportLeftInPercent: string
  viewportWidthInPercent: string
}

const MinimapViewport = forwardRef<HTMLDivElement, MinimapViewportProps>(
  function MinimapViewport(
    { viewportLeftInPercent, viewportWidthInPercent, isShadow },
    ref
  ) {
    const theme = useTheme()
    return (
      <div
        ref={ref}
        css={{
          backgroundColor: isShadow
            ? `${theme.palette.blue[30]}20`
            : `${theme.palette.blue[30]}33`,
          cursor: 'pointer',
          position: 'absolute',
          left: viewportLeftInPercent,
          top: 0,
          border: `1px solid ${
            isShadow ? `${theme.palette.blue[50]}50` : theme.palette.blue[50]
          }`,
          borderRadius: 6,
          width: viewportWidthInPercent,
          height: '100%',
          zIndex: ZIndex.MAX,
        }}
      ></div>
    )
  }
)

function useConvertEventsToMarkers(
  events: TimelineEvent[],
  minTime: DateTime,
  maxTime: DateTime
): TimelineEventMarker[] {
  const theme = useTheme()
  const timezone = useContext(LocaleContext)
  const { eventTypes } = useEventTypes()

  return useMemo(
    () =>
      events.map((event: TimelineEvent) => ({
        event,
        id: event.id,
        label:
          eventTypes.find((et) => et.id === event.name)?.name ?? UNIDENTIFIED,
        color: getColorByEvent(theme, eventTypes, event.name),
        startLocationPercent: calculateTimePercent(
          minTime,
          maxTime,
          DateTime.fromISO(event.startTime)
        ),
        startTime: DateTime.fromISO(event.startTime).setZone(timezone),
        name: event.name,
        sourceType: event.sourceType,
        outline: event.deletedAt
          ? theme.palette.red[50]
          : theme.palette.blue[50],
        shape: event.deletedAt ? MarkerShape.DIAMOND : MarkerShape.CIRCLE,
      })),
    [events, theme, eventTypes, minTime, maxTime, timezone]
  )
}

export const TimelineMinimap = ({
  activeTimeWindow,
  setActiveTimeWindow,
  minTime,
  maxTime,
  roomId,
  events,
  observations,
  onWheel,
}: {
  activeTimeWindow: [DateTime, DateTime]
  setActiveTimeWindow: Dispatch<SetStateAction<[DateTime, DateTime]>>
  minTime: DateTime
  maxTime: DateTime
  roomId: string
  events: TimelineEvent[]
  observations: TimelineEvent[]
  onWheel: WheelEventHandler
}) => {
  const [minViewTime, maxViewTime] = activeTimeWindow
  const eventMarkers = useConvertEventsToMarkers(events, minTime, maxTime)

  const obxEventMarkers = useConvertEventsToMarkers(
    observations,
    minTime,
    maxTime
  ).map((marker) => {
    return { ...marker, shape: MarkerShape.BAR }
  })

  const annotationTaskId = useEventsEditorContext().annotationTask?.id
  const contextEvents = useTaskContextEvents(annotationTaskId) ?? []
  const contextEventMarkers = useConvertEventsToMarkers(
    contextEvents,
    minTime,
    maxTime
  ).map((marker) => {
    return { ...marker, shape: MarkerShape.CIRCLE }
  })

  const viewportRef = useRef<HTMLDivElement>(null)

  const miniTimelineRef = useRef<HTMLDivElement>(null)
  const currentRef = miniTimelineRef?.current ?? undefined
  const timelineSize = useSize(currentRef ?? null)
  const timelinePosition = usePosition(currentRef ?? null)

  const totalTimelineSeconds = useMemo(
    () => maxTime.toSeconds() - minTime.toSeconds(),
    [minTime, maxTime]
  )
  const [viewportLeftInPercent, viewportWidthInPercent] = useMemo(() => {
    if (!timelineSize) return ['0%', '100%']

    const left =
      (minViewTime.diff(minTime).as('seconds') / totalTimelineSeconds) * 100
    const width =
      (maxViewTime.diff(minViewTime).as('seconds') / totalTimelineSeconds) * 100

    return [`${left}%`, `${width}%`]
  }, [timelineSize, minTime, minViewTime, maxViewTime, totalTimelineSeconds])

  const [shadowVPInPercent, setShadowVPInPercent] = useState<[string, string]>()

  const getPositionFromMouseEvent = useCallback(
    (e: ReactMouseEvent) => {
      const numPixelsOnTimeline = e.clientX - timelinePosition!.x
      const percentageOfTimeline = numPixelsOnTimeline / timelineSize!.width
      return percentageOfTimeline
    },
    [timelinePosition, timelineSize]
  )
  const getSecondsFromMouseEvent = useCallback(
    (e: ReactMouseEvent) => {
      const position = getPositionFromMouseEvent(e)
      return position && position * totalTimelineSeconds
    },
    [getPositionFromMouseEvent, totalTimelineSeconds]
  )

  const onClick: MouseEventHandler = useCallback(
    (e: ReactMouseEvent) => {
      e.preventDefault()
      if (!timelinePosition || !timelineSize) {
        return
      }

      if (+minViewTime === +minTime && +maxViewTime === +maxTime) {
        // No movement if the viewport is already at the min/max time
        return
      }

      const secondsFromMinTime = getSecondsFromMouseEvent(e)
      const secondsFromMaxTime = totalTimelineSeconds - secondsFromMinTime
      const viewportWidthInSeconds = maxViewTime.diff(minViewTime).as('seconds')

      const { newViewportStart, newViewportEnd } = calculateMiniMapPosition({
        secondsFromMinTime,
        secondsFromMaxTime,
        viewportWidthInSeconds,
        minTime,
        maxTime,
      })

      setActiveTimeWindow([newViewportStart, newViewportEnd])
    },
    [
      minTime,
      maxTime,
      minViewTime,
      maxViewTime,
      timelineSize,
      timelinePosition,
      totalTimelineSeconds,
      getSecondsFromMouseEvent,
      setActiveTimeWindow,
    ]
  )

  const onMouseMove: MouseEventHandler = useCallback(
    (e) => {
      if (!timelinePosition || !timelineSize) {
        return
      }

      if (+minViewTime === +minTime && +maxViewTime === +maxTime) {
        // No movement if the viewport is already at the min/max time
        return
      }

      const secondsFromMinTime = getSecondsFromMouseEvent(e)
      const secondsFromMaxTime = totalTimelineSeconds - secondsFromMinTime
      const viewportWidthInSeconds = maxViewTime.diff(minViewTime).as('seconds')

      const { newViewportStart, newViewportEnd } = calculateMiniMapPosition({
        secondsFromMinTime,
        secondsFromMaxTime,
        viewportWidthInSeconds,
        minTime,
        maxTime,
      })

      const left =
        (newViewportStart.diff(minTime).as('seconds') / totalTimelineSeconds) *
        100
      const width =
        (newViewportEnd.diff(newViewportStart).as('seconds') /
          totalTimelineSeconds) *
        100

      return setShadowVPInPercent([`${left}%`, `${width}%`])
    },
    [
      minTime,
      maxTime,
      minViewTime,
      maxViewTime,
      timelineSize,
      timelinePosition,
      totalTimelineSeconds,
      getSecondsFromMouseEvent,
      setShadowVPInPercent,
    ]
  )

  const onMouseLeave = useCallback(() => {
    setShadowVPInPercent(undefined)
  }, [setShadowVPInPercent])

  return (
    <div
      css={{
        position: 'relative',
        cursor: 'pointer',
        height: '100%',
        width: '100%',
      }}
      onMouseMove={onMouseMove}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
      onWheel={onWheel}
    >
      <MinimapViewport
        ref={viewportRef}
        viewportLeftInPercent={viewportLeftInPercent}
        viewportWidthInPercent={viewportWidthInPercent}
      />

      {shadowVPInPercent && (
        <MinimapViewport
          isShadow
          viewportLeftInPercent={shadowVPInPercent[0]}
          viewportWidthInPercent={shadowVPInPercent[1]}
        />
      )}
      <FlexContainer>
        <FlexItem
          grow
          shrink={1}
          css={{
            zIndex: ZIndex.DEFAULT,
            margin: 0,
            overflow: 'hidden',
          }}
          ref={miniTimelineRef}
        >
          {/* Apella Events */}
          <EventLine mini>
            {eventMarkers.map((marker) => (
              <TimelineEventMarkerDot
                mini
                key={marker.id}
                marker={marker}
                boundary={currentRef}
              />
            ))}
          </EventLine>

          {/* OBX and Context Events */}
          <EventLine type={TimelineType.SECONDARY} mini>
            {[...obxEventMarkers, ...contextEventMarkers].map((marker) => (
              <TimelineEventMarkerDot
                mini
                key={marker.id}
                marker={marker}
                boundary={currentRef}
              />
            ))}
          </EventLine>

          <OccupancySlider
            showTooltip={false}
            startTime={minTime}
            endTime={maxTime}
            roomId={roomId}
            startViewTime={minTime}
            endViewTime={maxTime}
            height={14}
          />
        </FlexItem>
      </FlexContainer>
    </div>
  )
}

interface ContextPanelProps {
  activeTimeWindow: [DateTime, DateTime]
  cases: Case[]
  events: EventsEditorEvent[]
  maxTime: DateTime
  minTime: DateTime
  observations: TimelineEvent[]
  onClickTimeline?: MouseEventHandler
  onEventSelected?: OnEventSelected
  roomId: string
}

const ContextPanel = forwardRef<HTMLDivElement, ContextPanelProps>(
  function ContextPanel(
    {
      activeTimeWindow,
      minTime,
      maxTime,
      roomId,
      cases,
      events,
      observations,
      onEventSelected,
    }: ContextPanelProps,
    ref
  ) {
    const [minViewTime, maxViewTime] = activeTimeWindow
    const currentRef =
      ref && 'current' in ref && ref.current ? ref.current : undefined
    const timelineSize = useSize(currentRef ?? null)

    const eventMarkers = useConvertEventsToMarkers(
      events,
      minViewTime,
      maxViewTime
    )

    const obxEventMarkers = useConvertEventsToMarkers(
      observations,
      minViewTime,
      maxViewTime
    ).map((marker) => {
      return { ...marker, shape: MarkerShape.BAR }
    })

    const {
      annotationTask,
      isTheaterMode,
      state: {
        activeEventState: { id: activeEventId },
      },
    } = useEventsEditorContext()

    const contextEvents = useTaskContextEvents(annotationTask?.id)
    const contextEventMarkers = useConvertEventsToMarkers(
      contextEvents,
      minViewTime,
      maxViewTime
    ).map((marker) => {
      return { ...marker, shape: MarkerShape.CIRCLE }
    })

    return (
      <FlexContainer>
        <FlexItem
          grow
          shrink={1}
          css={{
            margin: 0,
            overflow: 'hidden',
          }}
          ref={ref}
        >
          <ScheduleSlider
            cases={cases}
            timelineSize={timelineSize}
            startViewTime={minViewTime}
            endViewTime={maxViewTime}
          />

          {/* Apella Events Line */}
          <EventLine>
            {eventMarkers.map((marker) => (
              <TimelineEventMarkerDot
                key={marker.id}
                marker={marker}
                onClick={onEventSelected}
                boundary={currentRef}
                highlighted={marker.event.id === activeEventId}
                outlineHighlighted
              />
            ))}
          </EventLine>

          {!isTheaterMode && (
            <>
              {/* OBX and Context Events */}
              <EventLine type={TimelineType.SECONDARY}>
                {[...obxEventMarkers, ...contextEventMarkers].map((marker) => (
                  <TimelineEventMarkerDot
                    key={marker.id}
                    marker={marker}
                    onClick={onEventSelected}
                    boundary={currentRef}
                    highlighted={marker.event.id === activeEventId}
                  />
                ))}
              </EventLine>

              <OccupancySlider
                startTime={minTime}
                endTime={maxTime}
                roomId={roomId}
                startViewTime={minViewTime}
                endViewTime={maxViewTime}
                height={32}
              />
            </>
          )}
        </FlexItem>
      </FlexContainer>
    )
  }
)

export const TimeMarkers = memo(function TimeMarkers({
  timeMarkers,
  calculatePosition,
  zoomLevel,
}: {
  timeMarkers: DateTime[]
  calculatePosition: (time: DateTime) => number
  zoomLevel: number
}) {
  const { isTheaterMode } = useEventsEditorContext()

  return (
    <div
      css={{
        width: '100%',
        margin: 'auto',
        position: 'relative',
        height: TIME_MARKERS_HEIGHT,
        marginBottom: CURRENT_TIME_PILL_HEIGHT,
        zIndex: ZIndex.ABOVE,
      }}
    >
      {timeMarkers.map((h, i) => (
        <div
          key={h.toISO()}
          css={{ position: 'absolute', left: calculatePosition(h) }}
        >
          {h.minute === 0 ? (
            <HourMarker
              dt={h}
              isFirst={i === 0}
              format="T"
              textHeight={
                isTheaterMode ? HOUR_TEXT_HEIGHT_THEATER : HOUR_TEXT_HEIGHT
              }
            />
          ) : h.minute === 30 &&
            zoomLevel >= ZOOM_LEVEL_TO_SHOW_HALF_HOUR_TICK ? (
            <HourMarker
              dt={h}
              isFirst={false}
              format="T"
              textHeight={
                isTheaterMode
                  ? MID_HOUR_TEXT_HEIGHT_THEATER
                  : MID_HOUR_TEXT_HEIGHT
              }
            />
          ) : (
            <MinuteMarker dt={h} />
          )}
        </div>
      ))}
    </div>
  )
})
