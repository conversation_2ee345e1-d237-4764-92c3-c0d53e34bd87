import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import {
  BIG_TICK_HEIGHT,
  HOUR_TEXT_HEIGHT,
  LITTLE_TICK_HEIGHT,
} from './cssConsts'

export const MinuteMarker = ({ dt }: { dt: DateTime }) => {
  const theme = useTheme()

  return (
    <div
      css={{
        display: 'inline-block',
        position: 'relative',
        left: -0.5,
        color: theme.palette.gray[40],
      }}
    >
      <div
        css={{
          background: theme.palette.gray[40],
          height: dt.minute % 15 === 0 ? BIG_TICK_HEIGHT : LITTLE_TICK_HEIGHT,
          width: 1,
          margin: 'auto',
          marginTop: HOUR_TEXT_HEIGHT * 2,
        }}
      />
    </div>
  )
}
