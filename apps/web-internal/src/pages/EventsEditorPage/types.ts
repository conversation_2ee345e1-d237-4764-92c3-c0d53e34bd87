import { DateTime } from 'luxon'

import { GetEventTypes } from '../../hooks/useEventTypes/__generated__/'
import {
  EventFragment,
  EventHistoryFragment,
  GetCaseData,
} from './__generated__'

type GetEventTypes_eventTypes_edges_node = NonNullable<
  GetEventTypes['eventTypes']
>['edges'][number]['node']

type GetCaseData_cases_edges_node_room =
  GetCaseData['cases']['edges'][number]['node']['room']
type GetCaseData_cases_edges_node_site =
  GetCaseData['cases']['edges'][number]['node']['site']

type SaveEventType = 'REMOVE' | 'CREATE' | 'UPDATE' | 'RESET'

export interface ToSaveEvent {
  event: EventsEditorEvent
  predicted?: EventFragment | undefined
  type: SaveEventType
}

export type ActiveEventState =
  | {
      id: string
      syncPlaybackWithEventTime: boolean
      expanded: boolean
    }
  | {
      id: undefined
      syncPlaybackWithEventTime: false
      expanded: false
    }

export interface EventsEditorState {
  activeCamera?: string
  activeEventState: ActiveEventState
  eventsToSave: { [id: string]: ToSaveEvent }
  eventTypes: GetEventTypes_eventTypes_edges_node[]
  nextVideoStartTime?: DateTime
  playbackRate: number
  progress?: ProgressType
  saving: boolean
  showRemovedEvents: boolean
}

export type UnpersistedEvent = Omit<EventFragment, '__typename' | 'version'>

export interface TimelineEvent
  extends Omit<
    EventFragment,
    '__typename' | 'version' | 'labels' | 'notes' | 'cameraId' | 'deletedAt'
  > {
  deletedAt?: ApellaDateTime | null
}

export interface PersistedEvent extends Omit<EventFragment, '__typename'> {
  historyEvents: EventHistoryFragment[]
  lastAnnotatedEvent: EventHistory | undefined
  lastReviewedEvent: EventHistory | undefined
  prediction: EventHistory | undefined
}

export type EventsEditorEvent = PersistedEvent | UnpersistedEvent

export interface ProgressType {
  playerCurrentTime: DateTime
  playerLoadedTime?: DateTime
}

export interface Case {
  id: string
  procedures: string[]
  room: GetCaseData_cases_edges_node_room
  scheduledEndTime: DateTime
  scheduledStartTime: DateTime
  site: GetCaseData_cases_edges_node_site
  staff: Staff[]
}

export interface Staff {
  displayName: string
  firstName: string
  id: string
  lastName: string
  role: string
}

export type OnEventSelected = (
  event: TimelineEvent,
  scrollToEvent?: boolean
) => void

export type EventHistory = Pick<
  EventHistoryFragment,
  'id' | 'startTime' | 'name'
> & {
  startDateTime: DateTime
}

export interface EventProtoTileProps {
  color: string
  event: EventsEditorEvent
  isSaved?: boolean
  predicted?: EventHistory
  syncedWithPlayback?: boolean
}

export interface ApellaCamera {
  id: string
  name: string
  organizationId: string
  roomId: string
  shortName: string
  siteId: string
}
