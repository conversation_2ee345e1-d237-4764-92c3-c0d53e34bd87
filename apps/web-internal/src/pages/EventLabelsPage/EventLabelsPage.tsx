import { ChangeEvent, useCallback, useState } from 'react'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import {
  Button,
  Delete,
  Dialog,
  Edit,
  flexboxgrid,
  FlexContainer,
  H4,
  InputText,
  Plus,
  remSpacing,
  Span2,
  Span3,
  Table,
  TBody,
  TD,
  TH,
  THead,
  Tile,
  TR,
  VisuallyHidden,
} from '@apella/component-library'
import { LoadingOverlay } from 'src/components/molecules/LoadingOverlay'
import { useEventLabelOptions } from 'src/hooks/useEventLabelOptions'
import { GetEventLabelOptions } from 'src/hooks/useEventLabelOptions/__generated__'

export const EventLabelsPage = () => {
  const { eventLabelOptions, createEventLabel } = useEventLabelOptions()

  const [isOpen, setIsOpen] = useState(false)

  const createHandler = useCallback(
    (value: string) =>
      createEventLabel({
        variables: {
          name: value,
        },
      }).then((resp) => {
        if (resp.data?.eventLabelOptionCreate?.eventLabelOption) {
          toast.success(`Event label "${value}" created`)
          setIsOpen(false)
        } else {
          toast.error(`Failed to create event label "${value}"`)
        }
      }),
    [createEventLabel]
  )

  return (
    <>
      <div
        css={{
          display: 'flex',
          justifyContent: 'center',
          margin: `${remSpacing.large}`,
        }}
      >
        <div css={{ flexGrow: 1, maxWidth: flexboxgrid.breakpoints.lg }}>
          <div
            css={{
              display: 'flex',
              justifyContent: 'space-between',
              marginBottom: remSpacing.large,
              gap: remSpacing.medium,
            }}
          >
            <H4 as="h1">Event Labels</H4>

            <div
              css={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: remSpacing.small,
              }}
            >
              <Button onClick={() => setIsOpen(true)}>
                <Plus size="sm" />
                Create
              </Button>
            </div>
          </div>

          <LoadingOverlay isLoading={false}>
            <EventLabelTable eventLabels={eventLabelOptions} />
          </LoadingOverlay>
        </div>
      </div>
      <Dialog title="Create new event label" isOpen={isOpen}>
        <EventLabelDialogContent
          onSubmit={createHandler}
          onClose={() => setIsOpen(false)}
        />
      </Dialog>
    </>
  )
}

const EventLabelDialogContent = ({
  initialEventLabel = '',
  onSubmit,
  onClose,
}: {
  initialEventLabel?: string
  onSubmit?: (value: string) => void
  onClose: () => void
}) => {
  const [value, setValue] = useState(initialEventLabel)

  return (
    <FlexContainer
      css={{
        flexDirection: 'column',
        gap: remSpacing.small,
      }}
    >
      <InputText
        name="new-event-label"
        label="Event Label"
        value={value}
        onChange={(e: ChangeEvent<HTMLInputElement>) =>
          setValue(e.target.value)
        }
      />

      <FlexContainer
        css={{
          gap: remSpacing.small,
          justifyContent: 'flex-end',
        }}
      >
        <Button onClick={onClose} color="alternate" css={{}}>
          Cancel
        </Button>
        <Button
          disabled={!value}
          color="success"
          onClick={() => {
            value.trim() && onSubmit?.(value.trim())
          }}
        >
          {!initialEventLabel ? 'Create' : 'Rename'}
        </Button>
      </FlexContainer>
    </FlexContainer>
  )
}

function EventLabelTable({
  eventLabels,
}: {
  eventLabels: GetEventLabelOptions['eventLabelOptions']
}) {
  const { isGetting, isMutating, deleteEventLabel, updateEventLabel } =
    useEventLabelOptions()
  const [editingIndex, setEditingIndex] = useState(-1)
  const theme = useTheme()

  const deleteHandler = useCallback(
    (id: string, name: string) => {
      if (confirm(`Are you sure you want to delete event label "${name}"?`)) {
        deleteEventLabel({
          variables: {
            id,
          },
        }).then((resp) => {
          if (resp.data?.eventLabelOptionDelete?.id) {
            toast.success(`Event label "${name}" deleted`)
          } else {
            toast.error(`Failed to delete event label "${name}"`)
          }
        })
      }
    },
    [deleteEventLabel]
  )

  const updateHandler = useCallback(
    (id: string, name: string) => (value: string) =>
      updateEventLabel({
        variables: {
          id,
          name: value || name,
        },
      }).then((resp) => {
        if (resp.data?.eventLabelOptionUpdate?.eventLabelOption) {
          toast.success(`Event label "${value}" renamed`)
          setEditingIndex(-1)
        } else {
          toast.error(`Failed to rename event label "${value}"`)
        }
      }),
    [updateEventLabel]
  )

  return (
    <section>
      <Tile css={{ padding: 0 }}>
        <Table css={{ overflowX: 'auto' }}>
          <THead
            css={{
              position: 'sticky',
              top: 0,
              background: theme.palette.background.primary,
            }}
          >
            <TR>
              <TH>ID</TH>
              <TH>Name</TH>
              <TH /> {/* Empty cell for actions */}
            </TR>
          </THead>

          <TBody>
            {(isGetting || isMutating) && (
              <LoadingOverlay isLoading={isMutating} />
            )}

            {eventLabels.map((label, index) => {
              return (
                <TR key={label.id}>
                  <TH css={{ verticalAlign: 'middle' }}>
                    <Span3
                      css={{
                        fontFamily: 'monospace',
                      }}
                    >
                      {`${label.id.slice(0, 16)}...`}
                    </Span3>
                  </TH>
                  <TD>
                    <Span2>{label.name}</Span2>
                  </TD>
                  <TD>
                    <FlexContainer
                      gap={remSpacing.small}
                      css={{
                        justifyContent: 'flex-end',
                      }}
                    >
                      <Button
                        title="Edit"
                        size="sm"
                        color="black"
                        appearance="secondary"
                        buttonType="icon"
                        onClick={() => setEditingIndex(index)}
                      >
                        <Edit size="xs" />
                        <VisuallyHidden>Edit</VisuallyHidden>
                      </Button>

                      <Button
                        title="Archive"
                        size="sm"
                        color="error"
                        appearance="secondary"
                        buttonType="icon"
                        onClick={() => deleteHandler(label.id, label.name)}
                      >
                        <Delete size="xs" />
                        <VisuallyHidden>Delete</VisuallyHidden>
                      </Button>
                    </FlexContainer>
                  </TD>
                </TR>
              )
            })}
          </TBody>

          <Dialog title="Rename event label" isOpen={editingIndex > -1}>
            <EventLabelDialogContent
              initialEventLabel={
                editingIndex > -1 ? eventLabels[editingIndex].name : ''
              }
              onSubmit={
                editingIndex > -1
                  ? updateHandler(
                      eventLabels[editingIndex].id,
                      eventLabels[editingIndex].name
                    )
                  : undefined
              }
              onClose={() => setEditingIndex(-1)}
            />
          </Dialog>
        </Table>
      </Tile>
    </section>
  )
}
