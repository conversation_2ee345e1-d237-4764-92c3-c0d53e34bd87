import { useNavigate, useParams } from 'react-router'
import { toast } from 'react-toastify'

import { useMutation } from '@apollo/client'
import { isEqual } from 'lodash'

import { ErrorPage, Progress } from '@apella/component-library'
import {
  useEventDashboardVisibility,
  useEventTypes,
} from 'src/hooks/useEventTypes'
import { EventTypesForm } from 'src/modules/eventTypes/components/Form'
import { LocationPath } from 'src/router/LocationPath'

import {
  GET_EVENT_DASHBOARD_VISIBILITY,
  GET_EVENT_TYPES,
} from '../../hooks/useEventTypes/queries'
import {
  DeleteEventDashboardVisibility,
  DeleteEventDashboardVisibilityVariables,
  UpsertEventDashboardVisibility,
  UpsertEventDashboardVisibilityVariables,
} from '../EventTypesNewPage/__generated__'
import {
  DELETE_EVENT_DASHBOARD_VISIBILITY,
  UPSERT_EVENT_DASHBOARD_VISIBILITY,
} from '../EventTypesNewPage/queries'
import { UpdateEventType, UpdateEventTypeVariables } from './__generated__'
import { UPDATE_EVENT_TYPE } from './queries'

type EventTypeEditorPageParams = 'eventTypeId'
export const EVENT_TYPE_EDITOR_PAGE_TITLE = 'Edit Event Type'

export const EventTypeEditorPage = () => {
  const { loading: loadingEventTypes, eventTypes } = useEventTypes()
  const { loading: loadingVisibility, eventDashboardVisibility } =
    useEventDashboardVisibility()
  const eventTypeId = useParams<EventTypeEditorPageParams>().eventTypeId!

  const [updateEventType] = useMutation<
    UpdateEventType,
    UpdateEventTypeVariables
  >(UPDATE_EVENT_TYPE)
  const [upsertEventDashboardVisibility] = useMutation<
    UpsertEventDashboardVisibility,
    UpsertEventDashboardVisibilityVariables
  >(UPSERT_EVENT_DASHBOARD_VISIBILITY)
  const [deleteEventDashboardVisibility] = useMutation<
    DeleteEventDashboardVisibility,
    DeleteEventDashboardVisibilityVariables
  >(DELETE_EVENT_DASHBOARD_VISIBILITY)

  const eventType = eventTypes.find((eventType) => eventType.id === eventTypeId)

  const navigate = useNavigate()

  if (loadingEventTypes || loadingVisibility) return <Progress />

  if (!eventType)
    return <ErrorPage header="Error loading event type. Please try again" />

  const { id, name, type, description, hidden, color } = eventType
  const visibilityObj = eventDashboardVisibility.find(
    (e) => e.eventTypeId === id
  )
  const shownInDashboard = visibilityObj !== undefined
  const orgIdFilter = visibilityObj ? visibilityObj.orgIdFilter : null

  return (
    <EventTypesForm
      title={EVENT_TYPE_EDITOR_PAGE_TITLE}
      shouldDisableIdField={true}
      initialValues={{
        id: id ?? '',
        name: name ?? '',
        type: type ?? '',
        description: description ?? '',
        color: color ?? '',
        shownInInternal: !hidden,
        shownInDashboard: shownInDashboard,
        orgIdFilter: orgIdFilter ?? undefined,
      }}
      onSubmit={async (input) => {
        try {
          await updateEventType({
            variables: {
              input: {
                id: input.id,
                name: input.name,
                type: input.type,
                description: input.description,
                color: input.color,
                hidden: !input.shownInInternal,
              },
            },
            refetchQueries: [GET_EVENT_TYPES],
          })

          if (
            input.shownInDashboard &&
            // Perform an upsert if the event type was not previously shownInDashboard, or an update to the orgIdFilter has been made
            (!shownInDashboard || !isEqual(input.orgIdFilter, orgIdFilter))
          ) {
            await upsertEventDashboardVisibility({
              variables: {
                input: {
                  eventTypeId: input.id,
                  orgIdFilter: input.orgIdFilter,
                },
              },
              refetchQueries: [GET_EVENT_DASHBOARD_VISIBILITY],
            })
          } else if (!input.shownInDashboard && shownInDashboard) {
            await deleteEventDashboardVisibility({
              variables: {
                input: {
                  eventTypeId: input.id,
                },
              },
              refetchQueries: [GET_EVENT_DASHBOARD_VISIBILITY],
            })
          }

          toast.success(`Event type "${name}" updated`)
          navigate(LocationPath.EventTypes)
        } catch (e) {
          toast.error('Error updating event type' + (e as any).message || '')
        }
      }}
      onBackButtonClick={() => navigate(LocationPath.EventTypes)}
    />
  )
}
