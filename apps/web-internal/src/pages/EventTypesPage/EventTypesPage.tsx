import { useState } from 'react'
import { generatePath } from 'react-router'

import { useTheme } from '@emotion/react'

import { sortBy } from 'lodash'

import {
  Edit,
  flexboxgrid,
  H4,
  Plus,
  remSpacing,
  Span2,
  Table,
  TH,
  THead,
  Tile,
  TR,
  <PERSON>lyHidden,
  TBody,
  TD,
  ButtonLink,
} from '@apella/component-library'
import { ColorSample } from 'src/components/atoms/ColorSample'
import { LoadingOverlay } from 'src/components/molecules/LoadingOverlay'
import {
  useEventDashboardVisibility,
  useEventTypes,
} from 'src/hooks/useEventTypes'
import { GetEventTypes } from 'src/hooks/useEventTypes/__generated__'
import { LocationPath } from 'src/router/LocationPath'

type GetEventTypes_eventTypes_edges_node = NonNullable<
  GetEventTypes['eventTypes']
>['edges'][number]['node']

type EventDashboardVisibilityType = {
  eventTypeId: string
  orgIdFilter: string[] | null
}

export const EventTypesPage = () => {
  const { loading: loadingEventTypes, eventTypes } = useEventTypes()
  const { loading: loadingVisibility, eventDashboardVisibility } =
    useEventDashboardVisibility()
  const [showHiddenTypes, setShowHiddenTypes] = useState(false)

  return (
    <div
      css={{
        display: 'flex',
        justifyContent: 'center',
        margin: `${remSpacing.large}`,
      }}
    >
      <div css={{ flexGrow: 1, maxWidth: flexboxgrid.breakpoints.lg }}>
        <div
          css={{
            display: 'flex',
            justifyContent: 'space-between',
            marginBottom: remSpacing.large,
            gap: remSpacing.medium,
          }}
        >
          <H4 as="h1">Event Types</H4>

          <div
            css={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              gap: remSpacing.small,
            }}
          >
            <div>
              <input
                style={{ marginBottom: 5 }}
                checked={showHiddenTypes}
                onChange={(e) => {
                  setShowHiddenTypes(e.target.checked)
                }}
                id="showhiddentypescheckbox"
                type="checkbox"
              />
              <label htmlFor="showhiddentypescheckbox">
                <Span2>&nbsp;Show hidden on Internal</Span2>
              </label>
            </div>
            <ButtonLink to={LocationPath.EventTypesNew}>
              <Plus size="sm" />
              Create
            </ButtonLink>
          </div>
        </div>

        <LoadingOverlay isLoading={loadingEventTypes || loadingVisibility}>
          <EventTypeTable
            eventTypes={eventTypes}
            eventDashboardVisibility={eventDashboardVisibility}
            showHidden={showHiddenTypes}
          />
        </LoadingOverlay>
      </div>
    </div>
  )
}

function EventTypeTable(props: {
  eventTypes: GetEventTypes_eventTypes_edges_node[]
  eventDashboardVisibility: EventDashboardVisibilityType[]
  showHidden: boolean
}) {
  const theme = useTheme()

  return (
    <Tile css={{ padding: 0 }}>
      <Table>
        <THead
          css={{
            position: 'sticky',
            top: 0,
            background: theme.palette.background.primary,
          }}
        >
          <TR>
            <TH>Color</TH>
            <TH>ID</TH>
            <TH>Name</TH>
            <TH>Type</TH>
            <TH>Visible on Dashboard</TH>
            <TH /> {/* Empty cell for actions */}
          </TR>
        </THead>

        <TBody>
          {sortBy(props.eventTypes, 'name')
            .filter((e) => props.showHidden || !e.hidden)
            .map((eventType, index) => (
              <EventTypeTableRow
                key={eventType.id}
                eventType={eventType}
                eventDashboardVisibility={props.eventDashboardVisibility.find(
                  (e) => e.eventTypeId === eventType.id
                )}
                index={index}
              />
            ))}
        </TBody>
      </Table>
    </Tile>
  )
}

function EventTypeTableRow({
  eventType,
  eventDashboardVisibility,
  index,
}: {
  eventType: GetEventTypes_eventTypes_edges_node
  eventDashboardVisibility?: EventDashboardVisibilityType
  index: number
}) {
  const theme = useTheme()

  return (
    <TR
      css={{
        backgroundColor:
          index % 2 === 0
            ? theme.palette.background.primary
            : theme.palette.background.secondary,
      }}
    >
      <TD>
        <ColorSample color={eventType.color} />
      </TD>

      <TD>
        <Span2>{eventType.id}</Span2>
      </TD>

      <TD>
        <Span2>{eventType.name}</Span2>
      </TD>

      <TD>
        <Span2>{eventType.type}</Span2>
      </TD>

      <TD>
        <Span2>
          {eventDashboardVisibility &&
            `${eventDashboardVisibility.orgIdFilter?.length ?? 'All'} org(s)`}
        </Span2>
      </TD>

      <TD>
        <ButtonLink
          title="Edit"
          size="sm"
          color="black"
          appearance="secondary"
          buttonType="icon"
          to={encodeURI(
            generatePath(LocationPath.EventTypesEditor, {
              eventTypeId: eventType.id,
            })
          )}
        >
          <Edit size="xs" />
          <VisuallyHidden>Edit</VisuallyHidden>
        </ButtonLink>
      </TD>
    </TR>
  )
}
