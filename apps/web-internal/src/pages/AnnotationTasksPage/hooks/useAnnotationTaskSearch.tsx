import { useCallback, useContext, useMemo, useState } from 'react'

import { useQuery } from '@apollo/client'
import isEqual from 'lodash/isEqual'
import { DateTime } from 'luxon'

import { useQueryParamAndLocalStorageState } from '@apella/hooks'
import { Direction, OrderBy, TaskStatus } from 'src/__generated__/globalTypes'
import { getLocationOptionCount } from 'src/components/molecules/Filters/helper'
import {
  orderByToUrlFriendly,
  urlFriendlyOrderStrToOrderBy,
} from 'src/components/molecules/Filters/urls'
import { LocaleContext } from 'src/contexts/locale'
import { ANNOTATOR_ROLE, pageSize, REVIEWER_ROLE } from 'src/utils/constants'
import {
  dateTimeToUrlFriendlyDate,
  urlFriendlyDateToDateTime,
} from 'src/utils/dates'

import {
  GetAnnotationSearchFilters,
  GetAnnotationSearchFiltersVariables,
  GetAnnotationTasks,
  GetAnnotationTasksVariables,
} from '../__generated__'
import { GET_ANNOTATION_SEARCH_FILTERS, GET_ANNOTATION_TASKS } from '../queries'
import { TaskStatusReadable } from '../TaskStatusReadable'

type GetAnnotationSearchFilters_organizations_edges =
  GetAnnotationSearchFilters['organizations']['edges'][number]
type GetAnnotationSearchFilters_sites_edges =
  GetAnnotationSearchFilters['sites']['edges'][number]
type GetAnnotationSearchFilters_sites_edges_node_rooms_edges =
  GetAnnotationSearchFilters['sites']['edges'][number]['node']['rooms']['edges'][number]

type GetAnnotationTasks_annotationTasks_counts_orgId = NonNullable<
  GetAnnotationTasks['annotationTasks']['counts']
>['orgId'][number]
type GetAnnotationTasks_annotationTasks_counts_roomId = NonNullable<
  GetAnnotationTasks['annotationTasks']['counts']
>['roomId'][number]
type GetAnnotationTasks_annotationTasks_counts_siteId = NonNullable<
  GetAnnotationTasks['annotationTasks']['counts']
>['roomId'][number]
type GetAnnotationTasks_annotationTasks_edges_node =
  GetAnnotationTasks['annotationTasks']['edges'][number]['node']

export const START_TIME = 'startTime'
export const END_TIME = 'endTime'
export const ROOM_ID = 'roomId'
const ORG_ID = 'organizationId'
const SITE_ID = 'siteId'
const ANNOTATOR_USER_IDS = 'annotatorUserIds'
const REVIEWER_USER_IDS = 'reviewerUserIds'
const STATUSES = 'statuses'
const TYPE_IDS = 'typeIds'
const ORDER_BY = 'orderBy'

export const ANNOTATOR_USER_ID = 'annotatorUserId'
export const REVIEWER_USER_ID = 'reviewerUserId'
export const STATUS = 'status'
export const TYPE_ID = 'typeId'

const SORTABLE_FIELDS = [
  START_TIME,
  END_TIME,
  ROOM_ID,
  ANNOTATOR_USER_ID,
  REVIEWER_USER_ID,
  STATUS,
  TYPE_ID,
] as const

const DEFAULT_PAGING_STATE = {
  after: undefined,
  first: pageSize,
}

const DEFAULT_ORDER_BY_STATE: OrderBy[] = [
  {
    sort: START_TIME,
    direction: Direction.ASC,
  },
]

const DEFAULT_STATE: Partial<State> = {
  ...DEFAULT_PAGING_STATE,
  ...DEFAULT_ORDER_BY_STATE,
  organizationId: undefined,
  siteId: undefined,
  roomId: undefined,
  annotatorUserIds: undefined,
  reviewerUserIds: undefined,
  statuses: undefined,
  typeIds: undefined,
}

type State = {
  after?: string
  first?: number
  startTime: string
  endTime: string
  organizationId?: string
  siteId?: string
  roomId?: string
  annotatorUserIds?: (string | null)[]
  reviewerUserIds?: (string | null)[]
  statuses?: TaskStatus[]
  typeIds?: string[]
  orderBy?: OrderBy[]
}

export const useAnnotationTaskSearch = ({
  DEFAULT_START_TIME,
  DEFAULT_END_TIME,
}: {
  DEFAULT_START_TIME: DateTime
  DEFAULT_END_TIME: DateTime
}) => {
  const defaultState = useMemo<State>(() => {
    return {
      ...DEFAULT_STATE,
      startTime: DEFAULT_START_TIME.toISO(),
      endTime: DEFAULT_END_TIME.toISO(),
    }
  }, [DEFAULT_END_TIME, DEFAULT_START_TIME])

  const locale = useContext(LocaleContext)

  /* Local Storage / Query Params */
  const [startTime, setStartTime] =
    useQueryParamAndLocalStorageState<ApellaDateTime>(
      START_TIME,
      dateTimeToUrlFriendlyDate(DEFAULT_START_TIME)
    )
  const [endTime, setEndTime] =
    useQueryParamAndLocalStorageState<ApellaDateTime>(
      END_TIME,
      dateTimeToUrlFriendlyDate(DEFAULT_END_TIME)
    )
  const [organizationId, setOrganizationId] = useQueryParamAndLocalStorageState<
    string | undefined
  >(ORG_ID, undefined)
  const [siteId, setSiteId] = useQueryParamAndLocalStorageState<
    string | undefined
  >(SITE_ID, undefined)
  const [roomId, setRoomId] = useQueryParamAndLocalStorageState<
    string | undefined
  >(ROOM_ID, undefined)
  const [annotatorUserIds, setAnnotatorUserIds] =
    useQueryParamAndLocalStorageState<(string | null)[] | undefined>(
      ANNOTATOR_USER_IDS,
      undefined
    )
  const [reviewerUserIds, setReviewerUserIds] =
    useQueryParamAndLocalStorageState<(string | null)[] | undefined>(
      REVIEWER_USER_IDS,
      undefined
    )
  const [statuses, setStatuses] = useQueryParamAndLocalStorageState<
    TaskStatus[] | undefined
  >(STATUSES, undefined)
  const [orderBy, setOrderBy] = useQueryParamAndLocalStorageState<
    string | undefined
  >(ORDER_BY, orderByToUrlFriendly(defaultState.orderBy))
  const [typeIds, setTypeIds] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(TYPE_IDS, undefined)

  /* State */
  const [state, setState] = useState<State>({
    ...DEFAULT_PAGING_STATE,
    ...DEFAULT_ORDER_BY_STATE,
    startTime:
      urlFriendlyDateToDateTime(startTime, locale).startOf('day').toISO() ||
      DEFAULT_START_TIME.toISO(),
    endTime:
      urlFriendlyDateToDateTime(endTime, locale).endOf('day').toISO() ||
      DEFAULT_END_TIME.toISO(),
    organizationId,
    siteId,
    roomId,
    annotatorUserIds,
    reviewerUserIds,
    statuses,
    typeIds,
    orderBy:
      urlFriendlyOrderStrToOrderBy(orderBy, SORTABLE_FIELDS) ||
      DEFAULT_ORDER_BY_STATE,
  })

  // This goes through all the state changes:
  // - Update LocalStorage and QueryParams
  // - Update the state
  const updateTableFilterSortState = useCallback(
    (stateDiff: Partial<State>) => {
      setState((prevState: State) => {
        const newState = {
          ...prevState,
          ...stateDiff,
        }

        setStartTime(
          dateTimeToUrlFriendlyDate(
            DateTime.fromISO(newState.startTime).setZone(locale)
          )
        )
        setEndTime(
          dateTimeToUrlFriendlyDate(
            DateTime.fromISO(newState.endTime).setZone(locale)
          )
        )
        setOrganizationId(newState.organizationId)
        setSiteId(newState.siteId)
        setRoomId(newState.roomId)
        setAnnotatorUserIds(newState.annotatorUserIds)
        setReviewerUserIds(newState.reviewerUserIds)
        setStatuses(newState.statuses)
        setOrderBy(orderByToUrlFriendly(newState.orderBy))
        setTypeIds(newState.typeIds)

        return newState
      })
    },
    [
      setStartTime,
      locale,
      setEndTime,
      setOrganizationId,
      setSiteId,
      setRoomId,
      setAnnotatorUserIds,
      setReviewerUserIds,
      setStatuses,
      setOrderBy,
      setTypeIds,
    ]
  )

  // Queries
  const {
    loading: tasksLoading,
    data: tasksDataRaw,
    refetch: refreshTasks,
  } = useQuery<GetAnnotationTasks, GetAnnotationTasksVariables>(
    GET_ANNOTATION_TASKS,
    {
      variables: {
        ...state,
        orderBy: [
          ...(state.orderBy ?? []),
          {
            sort: 'id',
            direction: Direction.ASC,
          },
        ],
      },
    }
  )

  const { loading: filtersLoading, data: filtersData } = useQuery<
    GetAnnotationSearchFilters,
    GetAnnotationSearchFiltersVariables
  >(GET_ANNOTATION_SEARCH_FILTERS, {
    variables: {
      annotatorRole: ANNOTATOR_ROLE,
      reviewerRole: REVIEWER_ROLE,
    },
  })

  /* Handlers */
  const onDateChange = useCallback(
    (dates: Date[]) => {
      if (!filtersLoading) {
        const newStartTime = DateTime.fromJSDate(dates[0])
          .setZone(locale)
          .startOf('day')
        const newEndTime = DateTime.fromJSDate(dates[1])
          .setZone(locale)
          .endOf('day')

        updateTableFilterSortState({
          startTime: newStartTime.toISO(),
          endTime: newEndTime.toISO(),
        })
      }
    },
    [filtersLoading, locale, updateTableFilterSortState]
  )
  const onOrganizationIdChange = useCallback(
    (orgs?: string | string[]) => {
      if (!filtersLoading) {
        const organizationId = Array.isArray(orgs) ? orgs[0] : orgs
        updateTableFilterSortState({
          organizationId,
          siteId: undefined,
          roomId: undefined,
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )
  const onSiteIdChange = useCallback(
    (siteId?: string) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          siteId,
          roomId: undefined,
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )
  const onRoomIdChange = useCallback(
    (roomId?: string) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          roomId,
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )
  const onAnnotatorsChange = useCallback(
    (annotatorUserIds?: (string | null)[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          annotatorUserIds,
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )
  const onReviewersChange = useCallback(
    (reviewerUserIds?: (string | null)[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          reviewerUserIds,
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )
  const onStatusesChange = useCallback(
    (statuses?: string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          statuses: statuses as TaskStatus[],
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )
  const onTaskIdsChange = useCallback(
    (typeIds?: string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          typeIds,
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )
  const onSortChange = useCallback(
    (orderBy?: OrderBy[]) => {
      updateTableFilterSortState({
        orderBy,
      })
    },
    [updateTableFilterSortState]
  )
  const selectedDateTime: [Date, Date] = useMemo(
    () => [new Date(state.startTime), new Date(state.endTime)],
    [state.startTime, state.endTime]
  )

  const resetFilters = useCallback(() => {
    updateTableFilterSortState({ ...defaultState })
  }, [updateTableFilterSortState, defaultState])

  const rowKeySelector = useCallback(
    (rowData: GetAnnotationTasks_annotationTasks_edges_node) => rowData.id,
    []
  )

  const annotators = useMemo(
    () => filtersData?.annotators.edges ?? [],
    [filtersData?.annotators.edges]
  )
  const reviewers = useMemo(
    () => filtersData?.reviewers.edges ?? [],
    [filtersData?.reviewers.edges]
  )

  const taskTypesWithNode = useMemo(
    () =>
      (filtersData?.types ?? [])
        .filter((taskType) => !taskType.archivedTime)
        .map((t) => ({ node: t })),
    [filtersData?.types]
  )

  const tasksData = useMemo(
    () =>
      (tasksDataRaw?.annotationTasks.edges ?? [])
        .map((e) => e.node)
        .filter((task) => !task.type.archivedTime),
    [tasksDataRaw]
  )

  const tasksCountsData = useMemo(
    () => tasksDataRaw?.annotationTasks.counts,
    [tasksDataRaw]
  )

  const pageCursors = tasksDataRaw?.annotationTasks.pageCursors

  const organizations = useMemo(() => {
    const organizations = filtersData?.organizations.edges ?? []

    return getLocationOptionCount<
      GetAnnotationSearchFilters_organizations_edges,
      GetAnnotationTasks_annotationTasks_counts_orgId
    >(organizations, tasksCountsData?.orgId)
  }, [filtersData?.organizations.edges, tasksCountsData?.orgId])

  const sites = useMemo(() => {
    let sites: GetAnnotationSearchFilters_sites_edges[]
    if (organizationId) {
      sites =
        filtersData?.sites.edges.filter(
          (e) => e.node.organizationId === organizationId
        ) ?? []
    } else {
      sites = filtersData?.sites.edges ?? []
    }

    return getLocationOptionCount<
      GetAnnotationSearchFilters_sites_edges,
      GetAnnotationTasks_annotationTasks_counts_siteId
    >(sites, tasksCountsData?.siteId)
  }, [filtersData?.sites.edges, organizationId, tasksCountsData?.siteId])

  const rooms = useMemo(() => {
    let rooms: GetAnnotationSearchFilters_sites_edges_node_rooms_edges[] =
      sites.reduce(
        (acc, site) => acc.concat(site.node.rooms.edges),
        [] as GetAnnotationSearchFilters_sites_edges_node_rooms_edges[]
      )

    if (siteId) {
      rooms = rooms.filter((e) => e.node.siteId === siteId)
    }

    return getLocationOptionCount<
      GetAnnotationSearchFilters_sites_edges_node_rooms_edges,
      GetAnnotationTasks_annotationTasks_counts_roomId
    >(rooms, tasksCountsData?.roomId)
  }, [sites, siteId, tasksCountsData?.roomId])

  const taskStatuses = useMemo(
    () =>
      Object.entries(TaskStatusReadable).map(([status, statusName]) => {
        return {
          node: {
            id: status,
            name: statusName,
            count:
              tasksCountsData?.status?.find(
                (e) => e?.parentId?.split('.')[1] === status
              )?.count ?? 0,
          },
        }
      }),
    [tasksCountsData?.status]
  )
  const isStateDefault = useMemo(
    () => isEqual(state, defaultState),
    [defaultState, state]
  )

  return {
    state,
    isStateDefault,
    pageCursors,
    tasksLoading,
    tasksData,
    tasksCountsData,
    filtersLoading,
    filtersData,
    selectedDateTime,
    organizations,
    sites,
    rooms,
    taskTypesWithNode,
    annotators,
    reviewers,
    taskStatuses,
    refreshTasks,
    updateTableFilterSortState,
    onDateChange,
    onOrganizationIdChange,
    onSiteIdChange,
    onRoomIdChange,
    onTaskIdsChange,
    onAnnotatorsChange,
    onReviewersChange,
    onStatusesChange,
    onSortChange,
    resetFilters,
    rowKeySelector,
  }
}
