import React from 'react'
import { toast } from 'react-toastify'

import { DateTime } from 'luxon'

import {
  Button,
  Col,
  FlexContainer,
  H4,
  PaginatedTableColumn,
  RemotePaginatedTable,
  remSpacing,
  Row,
} from '@apella/component-library'
import { TwoLineCell } from 'src/components/atoms/TwoLineCell/TwoLineCell'
import {
  MultiFilterWithCount,
  SingleFilterWithCount,
} from 'src/components/molecules/Filters/FilterWithCount'
import { LocaleContext } from 'src/contexts/locale'
import { EVENT_TYPES, logEvent } from 'src/utils/analyticsEvents'

import { FilterBarPageTemplate } from '../../components/FilterBarPageTemplate'
import { FilterBarPageTemplateContent } from '../../components/FilterBarPageTemplateContent'
import { FilterBarPageTemplateFilters } from '../../components/FilterBarPageTemplateFilters'
import { DateFilter } from '../../components/molecules/Filters/DateFilter'
import { TaskAnnotatorSelect } from '../../components/TaskAnnotatorSelect'
import { TaskReviewerSelect } from '../../components/TaskReviewerSelect'
import { GetAnnotationTasks } from './__generated__'
import { ActionsCell } from './columns/ActionsCell'
import { DateCell } from './columns/DateCell'
import { OrgSiteRoomCell } from './columns/OrgSiteRoomCell'
import { AnnotatorsFilter } from './filters/AnnotatorsFilter'
import { ReviewersFilter } from './filters/ReviewersFilter'
import {
  ANNOTATOR_USER_ID,
  END_TIME,
  REVIEWER_USER_ID,
  ROOM_ID,
  START_TIME,
  STATUS,
  TYPE_ID,
  useAnnotationTaskSearch,
} from './hooks/useAnnotationTaskSearch'
import { StartAnnotatingButton, StartReviewingButton } from './StartButtons'
import {
  CancelledReasonReadable,
  TaskStatusReadable,
} from './TaskStatusReadable'

type GetAnnotationTasks_annotationTasks_edges_node =
  GetAnnotationTasks['annotationTasks']['edges'][number]['node']

export const AnnotationTasksPage = (): React.JSX.Element => {
  const locale = React.useContext(LocaleContext)
  const DEFAULT_START_TIME = DateTime.now()
    .setZone(locale)
    .minus({ weeks: 1 })
    .startOf('day')
  const DEFAULT_END_TIME = DateTime.now().setZone(locale).endOf('day')

  const {
    state,
    isStateDefault,
    pageCursors,
    tasksLoading,
    tasksData,
    tasksCountsData,
    filtersLoading,
    selectedDateTime,
    organizations,
    sites,
    rooms,
    taskTypesWithNode,
    annotators,
    reviewers,
    taskStatuses,
    refreshTasks,
    updateTableFilterSortState,
    onDateChange,
    onOrganizationIdChange,
    onSiteIdChange,
    onRoomIdChange,
    onTaskIdsChange,
    onAnnotatorsChange,
    onReviewersChange,
    onStatusesChange,
    onSortChange,
    resetFilters,
    rowKeySelector,
  } = useAnnotationTaskSearch({
    DEFAULT_START_TIME,
    DEFAULT_END_TIME,
  })

  const columns: PaginatedTableColumn<(typeof tasksData)[number]>[] =
    React.useMemo(
      () => [
        {
          name: 'Start time',
          contents: (
            rowData: GetAnnotationTasks_annotationTasks_edges_node
          ) => (
            <DateCell
              datetime={DateTime.fromISO(rowData.startTime).setZone(
                rowData.site.timezone
              )}
            />
          ),
          sortAttribute: START_TIME,
        },
        {
          name: 'End time',
          contents: (rowData) => (
            <DateCell
              datetime={DateTime.fromISO(rowData.endTime).setZone(
                rowData.site.timezone
              )}
            />
          ),
          sortAttribute: END_TIME,
        },
        {
          name: 'Room',
          contents: (rowData) => (
            <OrgSiteRoomCell
              org={rowData.organization.name}
              site={rowData.site.name}
              room={rowData.room.name}
            />
          ),
          sortAttribute: ROOM_ID,
        },
        {
          name: 'Type',
          selector: (rowData: GetAnnotationTasks_annotationTasks_edges_node) =>
            rowData.type.name,
          sortAttribute: TYPE_ID,
        },
        {
          name: 'Annotator',
          contents: (rowData) => (
            <TaskAnnotatorSelect
              taskId={rowData.id}
              selectedId={rowData.annotatorUserId || undefined}
              annotators={annotators}
              site={rowData.site.name}
              room={rowData.room.name}
            />
          ),
          sortAttribute: ANNOTATOR_USER_ID,
        },
        {
          name: 'Reviewer',
          contents: (rowData) => (
            <TaskReviewerSelect
              taskId={rowData.id}
              selectedId={rowData.reviewerUserId || undefined}
              reviewers={reviewers}
              site={rowData.site.name}
              room={rowData.room.name}
            />
          ),
          sortAttribute: REVIEWER_USER_ID,
        },

        {
          name: 'Status',
          contents: (
            rowData: GetAnnotationTasks_annotationTasks_edges_node
          ) => {
            const { status, cancelledReason } = rowData

            return (
              <TwoLineCell
                line1={
                  cancelledReason
                    ? CancelledReasonReadable[cancelledReason] ||
                      cancelledReason
                    : ''
                }
                line2={TaskStatusReadable[status] || ''}
              />
            )
          },
          sortAttribute: STATUS,
        },
        {
          name: 'Actions',
          contents: (rowData) => (
            <ActionsCell
              annotationTask={rowData}
              annotators={annotators}
              reviewers={reviewers}
            />
          ),
        },
      ],
      [annotators, reviewers]
    )

  React.useEffect(() => {
    logEvent(EVENT_TYPES.LOAD_ANNOTATION_TASKS_PAGE)
  }, [])

  const refetchTasksCallback = React.useCallback(() => {
    refreshTasks()
      .then(() => toast.success('Successfully refreshed tasks'))
      .catch(() => toast.error('Failed to refresh tasks'))
  }, [refreshTasks])

  return (
    <FilterBarPageTemplate>
      <FilterBarPageTemplateFilters>
        <FlexContainer justifyContent="space-between" alignItems="center">
          <H4 as="h1">Annotation Tasks</H4>
          <FlexContainer gap={remSpacing.small}>
            <StartReviewingButton />
            <StartAnnotatingButton />
          </FlexContainer>
        </FlexContainer>
        <Row gutter>
          <Col>
            <DateFilter
              selected={selectedDateTime}
              onChangeDate={onDateChange}
            />
          </Col>
          <Col>
            <SingleFilterWithCount
              items={organizations}
              value={state.organizationId ?? undefined}
              onChange={onOrganizationIdChange}
              label="Organization"
              selectAllLabel="All organizations"
            />
          </Col>
          <Col>
            <SingleFilterWithCount
              items={sites}
              value={state.siteId ?? undefined}
              onChange={onSiteIdChange}
              label="Site"
              selectAllLabel="All sites"
            />
          </Col>
          <Col>
            <SingleFilterWithCount
              items={rooms}
              value={state.roomId ?? undefined}
              onChange={onRoomIdChange}
              label="Rooms"
              selectAllLabel="All rooms"
            />
          </Col>
          <Col>
            <MultiFilterWithCount
              items={taskTypesWithNode}
              value={state.typeIds ?? undefined}
              onChange={onTaskIdsChange}
              label="All types"
            />
          </Col>
          <Col>
            <AnnotatorsFilter
              annotators={annotators}
              annotatorsCount={tasksCountsData?.annotatorUserId}
              selectedIds={state.annotatorUserIds ?? undefined}
              onChangeAnnotators={onAnnotatorsChange}
            />
          </Col>
          <Col>
            <ReviewersFilter
              reviewers={reviewers}
              reviewersCount={tasksCountsData?.reviewerUserId}
              selectedIds={state.reviewerUserIds ?? undefined}
              onChangeReviewers={onReviewersChange}
            />
          </Col>
          <Col>
            <MultiFilterWithCount
              items={taskStatuses}
              value={state.statuses ?? undefined}
              onChange={onStatusesChange}
              label="All statuses"
            />
          </Col>
          <Col>
            <Button
              onClick={resetFilters}
              disabled={filtersLoading || isStateDefault}
              appearance="link"
            >
              Reset filters
            </Button>
          </Col>
          <Col>
            <Button onClick={refetchTasksCallback} disabled={tasksLoading}>
              Refresh tasks
            </Button>
          </Col>
        </Row>
      </FilterBarPageTemplateFilters>
      <FilterBarPageTemplateContent>
        <RemotePaginatedTable
          visibleX={true}
          columns={columns}
          data={tasksData}
          isLoading={tasksLoading}
          rowKeySelector={rowKeySelector}
          sortOrder={state.orderBy}
          paginationType="cursor"
          onChangePage={(cursorItem) => {
            updateTableFilterSortState({ after: cursorItem.cursor })
          }}
          onChangeSort={onSortChange}
          {...pageCursors}
        />
      </FilterBarPageTemplateContent>
    </FilterBarPageTemplate>
  )
}
