import { useMemo } from 'react'

import { MultiSelect, Option } from '@apella/component-library'

import {
  DataNodeWithCount,
  getUserOptionCount,
} from '../../../components/molecules/Filters/helper'
import {
  GetAnnotationSearchFilters,
  GetAnnotationTasks,
} from '../__generated__'

type GetAnnotationSearchFilters_annotators_edges =
  GetAnnotationSearchFilters['annotators']['edges'][number]
type GetAnnotationTasks_annotationTasks_counts_annotatorUserId = NonNullable<
  GetAnnotationTasks['annotationTasks']['counts']
>['annotatorUserId'][number]

export const ANNOTATOR_UNASSIGNED_SPECIAL_VALUE = 'ANNOTATOR_FILTER_UNASSIGNED'

interface AnnotatorsFilterProps {
  annotators: GetAnnotationSearchFilters_annotators_edges[]
  annotatorsCount?:
    | (GetAnnotationTasks_annotationTasks_counts_annotatorUserId | null)[]
    | null
  onChangeAnnotators: (annotatorUserIds?: (string | null)[]) => void
  selectedIds?: (string | null)[]
}

export const AnnotatorsFilter = ({
  annotators,
  annotatorsCount,
  selectedIds,
  onChangeAnnotators,
}: AnnotatorsFilterProps): React.JSX.Element => {
  const annotatorsOptions: DataNodeWithCount<GetAnnotationSearchFilters_annotators_edges>[] =
    useMemo(() => {
      const unassignedOption: GetAnnotationSearchFilters_annotators_edges = {
        __typename: 'UserEdge',
        node: {
          __typename: 'User',
          id: ANNOTATOR_UNASSIGNED_SPECIAL_VALUE,
          name: 'Unassigned',
        },
      }

      return getUserOptionCount<
        GetAnnotationSearchFilters_annotators_edges,
        GetAnnotationTasks_annotationTasks_counts_annotatorUserId
      >(
        annotators.concat(unassignedOption),
        annotatorsCount,
        ANNOTATOR_UNASSIGNED_SPECIAL_VALUE
      )
    }, [annotators, annotatorsCount])

  return (
    <MultiSelect
      name={'annotators-filter'}
      label={'All annotators'}
      value={selectedIds?.map((i) => i ?? ANNOTATOR_UNASSIGNED_SPECIAL_VALUE)}
      search={true}
      onChange={(annotatorUserIds) =>
        onChangeAnnotators(
          annotatorUserIds?.map((i) =>
            i === ANNOTATOR_UNASSIGNED_SPECIAL_VALUE ? null : i
          )
        )
      }
    >
      {annotatorsOptions.map((annotator) => (
        <Option
          key={annotator.node.id}
          value={annotator.node.id}
          label={annotator.node.name}
          count={annotator.node.count}
        />
      ))}
    </MultiSelect>
  )
}
