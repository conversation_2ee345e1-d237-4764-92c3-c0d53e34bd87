import { useMemo } from 'react'

import { MultiSelect, Option } from '@apella/component-library'

import {
  DataNodeWithCount,
  getUserOptionCount,
} from '../../../components/molecules/Filters/helper'
import {
  GetAnnotationSearchFilters,
  GetAnnotationTasks,
} from '../__generated__'

type GetAnnotationSearchFilters_reviewers_edges =
  GetAnnotationSearchFilters['reviewers']['edges'][number]

type GetAnnotationTasks_annotationTasks_counts_reviewerUserId = NonNullable<
  GetAnnotationTasks['annotationTasks']['counts']
>['reviewerUserId'][number]

export const REVIEWER_UNASSIGNED_SPECIAL_VALUE = 'REVIEWER_FILTER_UNASSIGNED'

interface ReviewersFilterProps {
  onChangeReviewers: (reviewerUserIds?: (string | null)[]) => void
  reviewers: GetAnnotationSearchFilters_reviewers_edges[]
  reviewersCount?:
    | (GetAnnotationTasks_annotationTasks_counts_reviewerUserId | null)[]
    | null
  selectedIds?: (string | null)[]
}

export const ReviewersFilter = ({
  reviewers,
  reviewersCount,
  selectedIds,
  onChangeReviewers,
}: ReviewersFilterProps): React.JSX.Element => {
  const reviewersOptions: DataNodeWithCount<GetAnnotationSearchFilters_reviewers_edges>[] =
    useMemo(() => {
      const unassignedOption: GetAnnotationSearchFilters_reviewers_edges = {
        __typename: 'UserEdge',
        node: {
          __typename: 'User',
          id: REVIEWER_UNASSIGNED_SPECIAL_VALUE,
          name: 'Unassigned',
        },
      }

      return getUserOptionCount<
        GetAnnotationSearchFilters_reviewers_edges,
        GetAnnotationTasks_annotationTasks_counts_reviewerUserId
      >(
        reviewers.concat(unassignedOption),
        reviewersCount,
        REVIEWER_UNASSIGNED_SPECIAL_VALUE
      )
    }, [reviewers, reviewersCount])

  return (
    <MultiSelect
      name={'reviewers-filter'}
      label={'All reviewers'}
      value={selectedIds?.map((i) => i ?? REVIEWER_UNASSIGNED_SPECIAL_VALUE)}
      search={true}
      onChange={(reviewerUserIds) =>
        onChangeReviewers(
          reviewerUserIds?.map((i) =>
            i === REVIEWER_UNASSIGNED_SPECIAL_VALUE ? null : i
          )
        )
      }
    >
      {reviewersOptions.map((reviewer) => (
        <Option
          key={reviewer.node.id}
          value={reviewer.node.id}
          label={reviewer.node.name}
          count={reviewer.node.count}
        />
      ))}
    </MultiSelect>
  )
}
