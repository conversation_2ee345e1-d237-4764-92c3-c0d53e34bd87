import { gql } from '@apollo/client'

import { PAGE_CURSORS_FRAGMENT } from 'src/utils/graphqlFragments'

export const GET_ANNOTATION_SEARCH_FILTERS = gql`
  query GetAnnotationSearchFilters(
    $annotatorRole: String!
    $reviewerRole: String!
  ) {
    organizations {
      edges {
        node {
          id
          name
        }
      }
    }
    sites {
      edges {
        node {
          id
          name
          organizationId
          rooms {
            edges {
              node {
                id
                name
                siteId
              }
            }
          }
        }
      }
    }
    annotators: users(query: { role: $annotatorRole }) {
      edges {
        node {
          id
          name
        }
      }
    }
    reviewers: users(query: { role: $reviewerRole }) {
      edges {
        node {
          id
          name
        }
      }
    }
    types: annotationTaskTypes {
      id
      name
      archivedTime
    }
  }
`

export const GET_ANNOTATION_TASKS = gql`
  ${PAGE_CURSORS_FRAGMENT}
  query GetAnnotationTasks(
    $startTime: DateTime
    $endTime: DateTime
    $after: String
    $first: Int
    $organizationId: ID
    $siteId: ID
    $roomId: ID
    $typeIds: [ID!]
    $statuses: [TaskStatus!]
    $annotatorUserIds: [ID]
    $reviewerUserIds: [ID]
    $orderBy: [OrderBy!]
  ) {
    annotationTasks(
      query: {
        startTime: $startTime
        endTime: $endTime
        organizationId: $organizationId
        siteId: $siteId
        roomId: $roomId
        statuses: $statuses
        annotatorUserIds: $annotatorUserIds
        reviewerUserIds: $reviewerUserIds
        typeIds: $typeIds
      }
      first: $first
      after: $after
      orderBy: $orderBy
    ) {
      pageCursors {
        ...PageCursorsFragment
      }
      edges {
        node {
          id
          orgId
          siteId
          roomId
          organization {
            id
            name
          }
          site {
            id
            name
            timezone
          }
          room {
            id
            name
          }
          annotatorUserId
          reviewerUserId
          startTime
          endTime
          status
          cancelledReason
          type {
            id
            name
            archivedTime
          }
        }
      }
      counts {
        orgId {
          parentId
          count
        }
        siteId {
          parentId
          count
        }
        roomId {
          parentId
          count
        }
        annotatorUserId {
          parentId
          count
        }
        reviewerUserId {
          parentId
          count
        }
        status {
          parentId
          count
        }
      }
    }
  }
`
