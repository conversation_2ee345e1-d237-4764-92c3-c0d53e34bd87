import { CancelledReason, TaskStatus } from 'src/__generated__/globalTypes'

export const TaskStatusReadable: {
  [key in TaskStatus]?: string
} = {
  NOT_STARTED: 'Not started',
  IN_PROGRESS: 'In progress',
  READY_FOR_REVIEW: 'Ready for review',
  IN_REVIEW: 'In review',
  DONE: 'Done',
  CANCELLED: 'Cancelled',
  BLOCKED: 'Blocked',
}

export const CancelledReasonReadable: {
  [key in CancelledReason]?: string
} = {
  BLOCKED_CAMERAS: 'Blocked cameras',
  CAMERAS_OUT_OF_SYNC: 'Cameras out of sync',
  STAFF_IN_TRAINING: 'Staff in Training',
  IDLE: 'No task events',
  OUTAGE: 'Outage',
  SENSITIVE_CONTENT: 'Sensitive Content',
}
