import { memo, ReactNode } from 'react'

import { DateTime } from 'luxon'

import { TwoLineCell } from 'src/components/atoms/TwoLineCell/TwoLineCell'
import { dateTimeToHumanReadableDate } from 'src/utils/dates'

interface DateCellProps {
  children?: ReactNode
  datetime: DateTime
}

export const DateCell = memo(function DateCell({
  datetime,
}: DateCellProps): React.JSX.Element {
  const dayStr = dateTimeToHumanReadableDate(datetime)

  // e.g., 06:00 PST
  const timeStr = datetime.toFormat('T ZZZZ')

  return <TwoLineCell line1={timeStr} line2={dayStr} />
})
