import { useCallback, useMemo } from 'react'
import { generatePath } from 'react-router'
import { toast } from 'react-toastify'

import { useMutation } from '@apollo/client'
import { useAuth0 } from '@auth0/auth0-react'

import { ButtonLink, remSpacing } from '@apella/component-library'
import { TaskStatus } from 'src/__generated__/globalTypes'
import { UpdateTask, UpdateTaskVariables } from 'src/components/__generated__'
import { UPDATE_ANNOTATION_TASK } from 'src/components/taskMutations'
import { LocationPath } from 'src/router/LocationPath'

import {
  GetAnnotationSearchFilters,
  GetAnnotationTasks,
} from '../__generated__'

type GetAnnotationSearchFilters_annotators_edges =
  GetAnnotationSearchFilters['annotators']['edges'][number]

type GetAnnotationSearchFilters_reviewers_edges =
  GetAnnotationSearchFilters['reviewers']['edges'][number]
type GetAnnotationTasks_annotationTasks_edges_node =
  GetAnnotationTasks['annotationTasks']['edges'][number]['node']

interface ActionsCellProps {
  annotationTask: GetAnnotationTasks_annotationTasks_edges_node
  annotators: GetAnnotationSearchFilters_annotators_edges[]
  reviewers: GetAnnotationSearchFilters_reviewers_edges[]
}

export const ActionsCell = ({
  annotationTask,
  annotators,
  reviewers,
}: ActionsCellProps): React.JSX.Element => {
  const { user } = useAuth0()

  const getAnnotatorById = useCallback((): string | undefined => {
    return annotators?.find((a) => a.node.id === user?.sub)?.node.name
  }, [annotators, user?.sub])

  const getReviewerById = useCallback((): string | undefined => {
    return reviewers?.find((a) => a.node.id === user?.sub)?.node.name
  }, [reviewers, user?.sub])

  const videoPath = generatePath(LocationPath.AnnotationTaskEventsEditor, {
    annotationTaskId: annotationTask.id,
  })

  const [updateTask] = useMutation<UpdateTask, UpdateTaskVariables>(
    UPDATE_ANNOTATION_TASK
  )

  const isAnnotator = useCallback(() => {
    return user?.sub && annotators.map((e) => e.node.id).includes(user.sub)
  }, [user, annotators])

  const isReviewer = useCallback(() => {
    return user?.sub && reviewers.map((e) => e.node.id).includes(user.sub)
  }, [user, reviewers])

  const generateButton = useCallback(
    (label: string, onClick?: () => void) => (
      <ButtonLink
        target="_blank"
        rel="noreferrer"
        to={videoPath}
        color="alternate"
        onClick={onClick}
        style={{ marginRight: remSpacing.xsmall }}
      >
        {label}
      </ButtonLink>
    ),
    [videoPath]
  )

  const selfAssignLabeler = useCallback(async () => {
    if (user?.sub) {
      const response = await updateTask({
        variables: {
          taskUpdateInput: {
            id: annotationTask.id,
            annotatorUserId: user.sub,
            status: TaskStatus.IN_PROGRESS,
          },
        },
      })

      if (response.data?.annotationTaskUpdate?.success) {
        const annotatorName = getAnnotatorById()
        const taskName = `${annotationTask.site.name} \u2013 ${annotationTask.room.name}`
        const message = `Assigned ${annotatorName} as annotator to ${taskName}`

        toast.success(message)
      }
    }
  }, [annotationTask, getAnnotatorById, user, updateTask])

  const selfAssignReviewer = useCallback(async () => {
    if (user?.sub) {
      const response = await updateTask({
        variables: {
          taskUpdateInput: {
            id: annotationTask.id,
            reviewerUserId: user.sub,
            status: TaskStatus.IN_REVIEW,
          },
        },
      })

      if (response.data?.annotationTaskUpdate?.success) {
        const reviewerName = getReviewerById()
        const taskName = `${annotationTask.site.name} \u2013 ${annotationTask.room.name}`
        const message = `Assigned ${reviewerName} as reviewer to ${taskName}`

        toast.success(message)
      }
    }
  }, [annotationTask, getReviewerById, user, updateTask])

  const dynamicActionButtons = useMemo(() => {
    if (
      isAnnotator() &&
      annotationTask.annotatorUserId === null &&
      annotationTask.reviewerUserId !== user?.sub &&
      (annotationTask.status === TaskStatus.NOT_STARTED ||
        annotationTask.status === TaskStatus.IN_PROGRESS)
    ) {
      return generateButton('Annotate', selfAssignLabeler)
    } else if (
      isReviewer() &&
      annotationTask.reviewerUserId === null &&
      annotationTask.annotatorUserId !== user?.sub &&
      annotationTask.status === TaskStatus.READY_FOR_REVIEW
    ) {
      return generateButton('Review', selfAssignReviewer)
    }
    return null
  }, [
    user,
    annotationTask,
    isAnnotator,
    isReviewer,
    generateButton,
    selfAssignLabeler,
    selfAssignReviewer,
  ])

  return (
    <>
      {generateButton('Open')}
      {dynamicActionButtons}
    </>
  )
}
