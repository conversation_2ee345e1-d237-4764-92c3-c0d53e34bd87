import { generatePath, useNavigate } from 'react-router'
import { toast } from 'react-toastify'

import { gql, useQuery } from '@apollo/client'

import { Button, Progress } from '@apella/component-library'
import { useNextAnnotateTask, useNextReviewTask } from 'src/modules/nextTask'
import { LocationPath } from 'src/router/LocationPath'
import { REVIEWER_ROLE } from 'src/utils/constants'

import { GetReviewerUsers, GetReviewerUsersVariables } from './__generated__'

export const REVIEWER_USERS_QUERY = gql`
  query GetReviewerUsers($reviewerRole: String!) {
    me {
      id
    }
    reviewers: users(query: { role: $reviewerRole }) {
      edges {
        node {
          id
          name
        }
      }
    }
  }
`

export const StartReviewingButton = () => {
  const { next, loading } = useNextReviewTask()

  const { data } = useQuery<GetReviewerUsers, GetReviewerUsersVariables>(
    REVIEWER_USERS_QUERY,
    {
      variables: {
        reviewerRole: REVIEWER_ROLE,
      },
    }
  )

  const navigate = useNavigate()

  if (
    !data ||
    !data.reviewers.edges.some((edge) => edge.node.id === data.me?.id)
  )
    return null

  return (
    <Button
      disabled={loading}
      size="md"
      color="alternate"
      onClick={async () => {
        const nextTask = await next()

        if (nextTask) {
          navigate(
            generatePath(LocationPath.AnnotationTaskEventsEditor, {
              annotationTaskId: nextTask.id,
            })
          )
        } else {
          toast.success('No annotation tasks found, please check back later.')
        }
      }}
    >
      {loading && <Progress size="sm" />}
      Start Reviewing
    </Button>
  )
}

export const StartAnnotatingButton = () => {
  const { next, loading } = useNextAnnotateTask()

  const navigate = useNavigate()

  return (
    <Button
      disabled={loading}
      size="md"
      color="primary"
      onClick={async () => {
        const nextTask = await next()

        if (nextTask) {
          navigate(
            generatePath(LocationPath.AnnotationTaskEventsEditor, {
              annotationTaskId: nextTask.id,
            })
          )
        } else {
          toast.success('No annotation tasks found, please check back later.')
        }
      }}
    >
      {loading && <Progress size="sm" />}
      Start Annotating
    </Button>
  )
}
