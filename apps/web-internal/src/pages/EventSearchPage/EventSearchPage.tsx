import { useCallback, useContext, useMemo, useState } from 'react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import {
  Col,
  DatePicker,
  MultiSelect,
  Option,
  RemotePaginatedTable,
  Row,
  SingleSelect,
} from '@apella/component-library'
import { Direction, OrderBy } from 'src/__generated__/globalTypes'
import { DebounceSearchBar } from 'src/components/DebounceSearchBar'
import { EventTypesFilter } from 'src/components/molecules/Filters/EventTypesFilter'
import { LoadingOverlay } from 'src/components/molecules/LoadingOverlay'
import { LocaleContext } from 'src/contexts/locale'
import { useEventLabelOptions } from 'src/hooks/useEventLabelOptions'
import { EventSourceType } from 'src/types/event'
import { pageSize } from 'src/utils/constants'

import { FilterBarPageTemplate } from '../../components/FilterBarPageTemplate'
import { FilterBarPageTemplateContent } from '../../components/FilterBarPageTemplateContent'
import { FilterBarPageTemplateFilters } from '../../components/FilterBarPageTemplateFilters'
import {
  GetEventSearchData,
  GetEventSearchDataVariables,
  GetEventSearchFilters,
} from './__generated__'
import { renderActionComponents } from './ActionComponents'
import { GET_EVENT_SEARCH_DATA, GET_EVENT_SEARCH_FILTERS } from './queries'

type GetEventSearchData_eventSearch_edges_node_organization =
  GetEventSearchData['eventSearch']['edges'][number]['node']['organization']
type GetEventSearchData_eventSearch_edges_node_room =
  GetEventSearchData['eventSearch']['edges'][number]['node']['room']
type GetEventSearchData_eventSearch_edges_node_site =
  GetEventSearchData['eventSearch']['edges'][number]['node']['room']

type GetEventSearchFilters_organizations_edges_node_sites_edges =
  GetEventSearchFilters['organizations']['edges'][number]['node']['sites']['edges'][number]
type GetEventSearchFilters_organizations_edges_node_sites_edges_node_rooms_edges =
  GetEventSearchFilters_organizations_edges_node_sites_edges['node']['rooms']['edges']

const defaultPagingState = {
  after: undefined,
  first: pageSize,
}

const START_TIME = 'startTime'

const DEFAULT_ORDER_BY_STATE: Pick<GetEventSearchDataVariables, 'orderBy'> = {
  orderBy: [
    {
      sort: START_TIME,
      direction: Direction.DESC,
    },
  ],
}

export const EventSearchPage = (): React.JSX.Element => {
  const timezone = useContext(LocaleContext)
  const { eventLabelOptions } = useEventLabelOptions()

  const [state, setState] = useState<GetEventSearchDataVariables>({
    first: pageSize,
    minTime: DateTime.now()
      .setZone(timezone)
      .minus({ weeks: 2 })
      .startOf('day')
      .toISO(),
    maxTime: DateTime.now().setZone(timezone).endOf('day').toISO(),
    sourceType: EventSourceType.Prediction,
    orderBy: DEFAULT_ORDER_BY_STATE.orderBy,
  })

  // In the future when we want to include sharable URLs this callback will help
  const updateTableFilterSortState = useCallback(
    (newState: Partial<GetEventSearchDataVariables>) => {
      setState((prevState: GetEventSearchDataVariables) => ({
        ...prevState,
        ...newState,
      }))
    },
    [setState]
  )

  const { loading: filtersLoading, data: filterData } =
    useQuery<GetEventSearchFilters>(GET_EVENT_SEARCH_FILTERS)

  const { loading: eventsLoading, data: eventsData } = useQuery<
    GetEventSearchData,
    GetEventSearchDataVariables
  >(GET_EVENT_SEARCH_DATA, {
    variables: {
      ...state,
      orderBy: [
        ...(state.orderBy ?? []),
        {
          sort: 'id', // hidden tie-breaker
          direction: Direction.ASC,
        },
      ],
    },
  })

  const annotationUsers = useMemo(() => {
    const annotators = eventsData?.annotators.edges ?? []
    const reviewers = eventsData?.reviewers.edges ?? []
    return annotators
      .concat(reviewers)
      .reduce<Record<string, string>>((acc, user) => {
        acc[user.node.email] = user.node.name
        return acc
      }, {})
  }, [eventsData?.annotators.edges, eventsData?.reviewers.edges])

  // Getting the filters into a usable state
  const organizations = useMemo(
    () => filterData?.organizations.edges ?? [],
    [filterData?.organizations.edges]
  )

  const sites = useMemo(() => {
    const allSites = organizations.reduce(
      (acc, org) => acc.concat(org.node.sites.edges),
      [] as GetEventSearchFilters_organizations_edges_node_sites_edges[]
    )
    if (state.organizationId) {
      return (
        allSites.filter(
          (e) => e.node.organizationId === state.organizationId
        ) ?? []
      )
    }
    return allSites
  }, [organizations, state.organizationId])

  const rooms = useMemo(() => {
    const allRooms =
      sites.reduce<GetEventSearchFilters_organizations_edges_node_sites_edges_node_rooms_edges>(
        (acc, site) => acc.concat(site.node.rooms.edges),
        []
      )
    if (state.siteId) {
      return allRooms.filter((e) => e.node.siteId === state.siteId)
    }
    return allRooms
  }, [sites, state.siteId])

  // Getting the events data into a usable state
  const eventSearch = eventsData?.eventSearch ?? undefined
  const events = useMemo(() => eventSearch?.edges ?? [], [eventSearch?.edges])
  const pageCursors = eventsData?.eventSearch.pageCursors

  // Creating the filterable elements
  const orgDropdown = (
    <SingleSelect
      name={'dropdown-org'}
      value={state.organizationId ?? undefined}
      onChange={(organizationId) =>
        setState({
          ...state,
          ...defaultPagingState,
          organizationId: organizationId ?? undefined,
        })
      }
    >
      <Option label={'All organizations'} value={undefined} />
      {organizations.map((org) => (
        <Option key={org.node.id} value={org.node.id} label={org.node.name} />
      ))}
    </SingleSelect>
  )

  const siteDropdown = (
    <SingleSelect
      name={'dropdown-site'}
      value={state.siteId ?? undefined}
      onChange={(siteId) =>
        setState({
          ...state,
          ...defaultPagingState,
          siteId: siteId ?? undefined,
        })
      }
    >
      <Option label={'All sites'} value={undefined} />
      {sites.map((site) => (
        <Option
          key={site.node.id}
          value={site.node.id}
          label={site.node.name}
        />
      ))}
    </SingleSelect>
  )

  const roomDropdown = (
    <SingleSelect
      name={'dropdown-room'}
      value={state.roomId ?? undefined}
      onChange={(roomId) =>
        setState({
          ...state,
          ...defaultPagingState,
          roomId: roomId ?? undefined,
        })
      }
    >
      <Option label={'All rooms'} value={undefined} />
      {rooms.map((room) => (
        <Option key={room.node.id} value={room.node.id} label={room.node.id} />
      ))}
    </SingleSelect>
  )

  const sourceTypeDropdown = (
    <SingleSelect
      name={'dropdown-source-type'}
      value={state.sourceType}
      onChange={(sourceType) =>
        setState({
          ...state,
          ...defaultPagingState,
          sourceType: sourceType ?? '',
        })
      }
    >
      <Option value={EventSourceType.Human} label={EventSourceType.Human} />
      <Option
        value={EventSourceType.Prediction}
        label={EventSourceType.Prediction}
      />
    </SingleSelect>
  )

  const eventNamesFilter = (
    <EventTypesFilter
      value={state.eventNames ?? undefined}
      name={'eventName'}
      onChange={(eventNames) =>
        setState({
          ...state,
          ...defaultPagingState,
          eventNames: eventNames,
        })
      }
    />
  )

  const labelsFilter = (
    <MultiSelect
      name={'labels'}
      value={state.labels ?? undefined}
      label={'Labels'}
      onChange={(labels) =>
        setState({
          ...state,
          ...defaultPagingState,
          labels: labels,
        })
      }
    >
      {eventLabelOptions.map(({ name: uiLabel }) => (
        <Option key={uiLabel} value={uiLabel} label={uiLabel} />
      ))}
    </MultiSelect>
  )

  const notesFilter = (
    <DebounceSearchBar
      name={'notes'}
      onChange={(notes) =>
        setState((currentState) => ({
          ...currentState,
          ...defaultPagingState,
          notes: notes,
        }))
      }
    ></DebounceSearchBar>
  )

  const onSortChange = useCallback(
    (orderBy?: OrderBy[]) => {
      updateTableFilterSortState({
        orderBy,
      })
    },
    [updateTableFilterSortState]
  )

  const tableData = useMemo(() => events.map((event) => event.node), [events])
  return (
    <LoadingOverlay isLoading={filtersLoading}>
      <FilterBarPageTemplate>
        <FilterBarPageTemplateFilters>
          <Row gutter>
            <Col>
              <DatePicker
                value={[new Date(state.minTime), new Date(state.maxTime)]}
                selectRange={true}
                showPresets={true}
                setValue={(date) =>
                  date &&
                  Array.isArray(date) &&
                  date[0] &&
                  date[1] &&
                  setState({
                    ...state,
                    ...defaultPagingState,
                    minTime: DateTime.fromJSDate((date as Date[])[0])
                      .setZone(timezone)
                      .startOf('day')
                      .toISO(),
                    maxTime: DateTime.fromJSDate((date as Date[])[1])
                      .setZone(timezone)
                      .endOf('day')
                      .toISO(),
                  })
                }
                timezone={timezone}
              />
            </Col>
            <Col>{orgDropdown}</Col>
            <Col>{siteDropdown}</Col>
            <Col>{roomDropdown}</Col>
            <Col>{sourceTypeDropdown}</Col>
            <Col>{eventNamesFilter}</Col>
            <Col>{labelsFilter}</Col>
            {notesFilter}
          </Row>
        </FilterBarPageTemplateFilters>
        <FilterBarPageTemplateContent>
          <RemotePaginatedTable
            columns={[
              {
                name: 'ID',
                selector: 'id',
                formatter: (id: string) => id.substring(0, 8),
              },
              {
                name: 'Name',
                selector: 'label',
              },
              {
                name: 'Start time',
                selector: 'startTime',
                formatter: (date: string) =>
                  DateTime.fromISO(date)
                    .setZone(timezone)
                    .toLocaleString(DateTime.DATETIME_SHORT),
                sortAttribute: START_TIME,
              },
              {
                name: 'Labels',
                selector: 'labels',
                formatter: (labels: string[] | null) =>
                  (labels ?? []).join(','),
              },
              {
                name: 'Notes',
                selector: 'notes',
              },
              {
                name: 'Source',
                selector: 'source',
                formatter: (source: string) =>
                  annotationUsers[source] ?? source,
              },
              {
                name: 'Source type',
                selector: 'sourceType',
              },
              {
                name: 'Organization',
                selector: 'organization',
                formatter: (
                  org?: GetEventSearchData_eventSearch_edges_node_organization
                ) => org?.name ?? '',
              },
              {
                name: 'Site',
                selector: 'site',
                formatter: (
                  site?: GetEventSearchData_eventSearch_edges_node_site
                ) => site?.name ?? '',
              },
              {
                name: 'Room',
                selector: 'room',
                formatter: (
                  room?: GetEventSearchData_eventSearch_edges_node_room
                ) => room?.name ?? '',
              },
              {
                name: 'Actions',
                contents: (rowData) =>
                  renderActionComponents(rowData, timezone),
              },
            ]}
            data={tableData}
            isLoading={eventsLoading}
            sortOrder={state.orderBy!}
            paginationType="cursor"
            onChangePage={(cursorItem) =>
              updateTableFilterSortState({ after: cursorItem.cursor })
            }
            onChangeSort={onSortChange}
            striped
            {...pageCursors}
          />
        </FilterBarPageTemplateContent>
      </FilterBarPageTemplate>
    </LoadingOverlay>
  )
}
