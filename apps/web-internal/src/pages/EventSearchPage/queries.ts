import { gql } from '@apollo/client'

import { PAGE_CURSORS_FRAGMENT } from 'src/utils/graphqlFragments'

import { EVENT_FRAGMENT } from '../EventsEditorPage/queries'

export const GET_EVENT_SEARCH_FILTERS = gql`
  query GetEventSearchFilters {
    me {
      id
      name
      email
    }
    organizations {
      edges {
        node {
          id
          name
          sites {
            edges {
              node {
                id
                name
                organizationId
                rooms {
                  edges {
                    node {
                      id
                      name
                      siteId
                      organizationId
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`

export const GET_EVENT_SEARCH_DATA = gql`
  ${EVENT_FRAGMENT}
  ${PAGE_CURSORS_FRAGMENT}
  query GetEventSearchData(
    $organizationId: ID
    $siteId: ID
    $roomId: ID
    $minTime: DateTime!
    $maxTime: DateTime!
    $sourceType: String!
    $modelVersion: String
    $eventType: String
    $eventNames: [String!]
    $labels: [String!]
    $after: String
    $first: Int
    $orderBy: [OrderBy!]
    $notes: String
  ) {
    eventSearch(
      query: {
        organizationId: $organizationId
        siteId: $siteId
        roomId: $roomId
        minStartTime: $minTime
        maxStartTime: $maxTime
        sourceType: $sourceType
        modelVersion: $modelVersion
        eventType: $eventType
        eventNames: $eventNames
        labels: $labels
        notes: $notes
      }
      after: $after
      first: $first
      orderBy: $orderBy
    ) {
      pageCursors {
        ...PageCursorsFragment
      }
      edges {
        node {
          ...EventFragment
          modelVersion
          source
          organization {
            id
            name
          }
          site {
            id
            name
          }
          room {
            id
            name
          }
        }
      }
    }
    annotators: users(query: { role: "Apella Labeler" }) {
      edges {
        node {
          id
          name
          email
        }
      }
    }
    reviewers: users(query: { role: "Apella Label Reviewer" }) {
      edges {
        node {
          id
          name
          email
        }
      }
    }
  }
`
