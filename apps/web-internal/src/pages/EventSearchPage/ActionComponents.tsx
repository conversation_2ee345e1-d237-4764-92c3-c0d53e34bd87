import { generatePath } from 'react-router'

import { DateTime } from 'luxon'

import {
  ButtonLink,
  Camera,
  FlexContainer,
  Highlights,
  remSpacing,
} from '@apella/component-library'
import { LocationPath } from 'src/router/LocationPath'

import { GetEventSearchData } from './__generated__'

type GetEventSearchData_eventSearch_edges_node =
  GetEventSearchData['eventSearch']['edges'][number]['node']

export const renderActionComponents = (
  rowData: GetEventSearchData_eventSearch_edges_node,
  timezone: string
): React.JSX.Element => {
  const eventsEditorPageLocation = generatePath(LocationPath.EventsEditor, {
    organizationId: rowData.organization.id,
    siteId: rowData.site.id,
    date: DateTime.fromISO(rowData.startTime).setZone(timezone).toISO(),
    roomId: rowData.room.id,
  })

  const eventsEditorParams = new URLSearchParams({
    time: `${DateTime.fromISO(rowData.startTime)
      .setZone(timezone)
      .toSeconds()}`,
  })

  const highlightEditorPageLocation = generatePath(
    LocationPath.HighlightEditor,
    {
      highlightId: 'create',
    }
  )

  const highlightEditorParams = new URLSearchParams({
    organizationId: rowData.organization.id,
    siteId: rowData.site.id,
    startDate: DateTime.fromISO(rowData.startTime).setZone(timezone).toISO(),
    roomId: rowData.room.id,
  })

  return (
    <FlexContainer gap={remSpacing.small}>
      <ButtonLink
        color="alternate"
        to={`${eventsEditorPageLocation}?${eventsEditorParams.toString()}`}
        target="_blank"
        rel="noopener noreferrer"
      >
        <Camera size="sm" /> Video
      </ButtonLink>
      <ButtonLink
        color="alternate"
        to={`${highlightEditorPageLocation}?${highlightEditorParams.toString()}`}
        target="_blank"
        rel="noopener noreferrer"
      >
        <Highlights size="sm" />
        Create highlight
      </ButtonLink>
    </FlexContainer>
  )
}
