import { gql } from '@apollo/client'

import { PAGE_CURSORS_FRAGMENT } from 'src/utils/graphqlFragments'

export const GET_CAMERA_SEARCH_DATA = gql`
  ${PAGE_CURSORS_FRAGMENT}
  query GetCameraSearchData(
    $after: String
    $first: Int
    $orderBy: [OrderBy!]
    $organizationId: String
    $siteIds: [String!]
    $roomIds: [String!]
  ) {
    cameras(
      after: $after
      first: $first
      orderBy: $orderBy
      organizationId: $organizationId
      siteIds: $siteIds
      roomIds: $roomIds
    ) {
      pageCursors {
        ...PageCursorsFragment
      }
      edges {
        node {
          id
          name
          family
          rtspUrl
          organization {
            id
            name
          }
          site {
            id
            name
          }
          room {
            id
            name
          }
        }
      }
    }
  }
`
