import { useCallback, useMemo, useState } from 'react'
import { Link, generatePath } from 'react-router'

import { useQuery } from '@apollo/client'

import {
  Button,
  Col,
  Direction,
  Edit,
  H4,
  PaginatedTableColumn,
  RemotePaginatedTable,
  Row,
} from '@apella/component-library'
import { useQueryParamState } from '@apella/hooks'
import { FilterBarPageTemplate } from 'src/components/FilterBarPageTemplate'
import { FilterBarPageTemplateContent } from 'src/components/FilterBarPageTemplateContent'
import { FilterBarPageTemplateFilters } from 'src/components/FilterBarPageTemplateFilters'
import {
  GetOrgSiteRoomsOptionsComponent,
  OrgSiteRoomSelection,
} from 'src/components/OrgSiteRoomSelects'
import { LocationPath } from 'src/router/LocationPath'
import { pageSize } from 'src/utils/constants'

import {
  GetCameraSearchData,
  GetCameraSearchDataVariables,
} from './__generated__'
import { GET_CAMERA_SEARCH_DATA } from './queries'

const DEFAULT_PAGING_STATE = {
  after: undefined,
  first: pageSize,
}

const DEFAULT_ORDER_BY_STATE: Pick<GetCameraSearchDataVariables, 'orderBy'> = {
  orderBy: [
    {
      sort: 'id',
      direction: Direction.ASC,
    },
  ],
}

const ORGANIZATION_ID = 'organizationId'
const SITE_ID = 'siteId'
const ROOM_ID = 'roomId'

export const CameraSearchPage = (): React.JSX.Element => {
  const [organizationIdQueryString, setOrganizationIdQueryString] =
    useQueryParamState<string | undefined>(ORGANIZATION_ID, undefined)

  const [siteIdQueryString, setSiteIdQueryString] = useQueryParamState<
    string | undefined
  >(SITE_ID, undefined)

  const [roomIdQueryString, setRoomIdQueryString] = useQueryParamState<
    string | undefined
  >(ROOM_ID, undefined)

  const onChangeOrgSiteRoom = useCallback(
    (orgSiteRoomSelection: OrgSiteRoomSelection) => {
      setOrganizationIdQueryString(orgSiteRoomSelection.organization?.id)
      setSiteIdQueryString(orgSiteRoomSelection.site?.id)
      setRoomIdQueryString(orgSiteRoomSelection.room?.id)
    },
    [setOrganizationIdQueryString, setRoomIdQueryString, setSiteIdQueryString]
  )

  const [state, setState] = useState<GetCameraSearchDataVariables>({
    ...DEFAULT_PAGING_STATE,
    ...DEFAULT_ORDER_BY_STATE,
  })

  const { loading: camerasLoading, data: camerasData } = useQuery<
    GetCameraSearchData,
    GetCameraSearchDataVariables
  >(GET_CAMERA_SEARCH_DATA, {
    variables: {
      ...state,
      organizationId: organizationIdQueryString,
      siteIds: siteIdQueryString ? [siteIdQueryString] : undefined,
      roomIds: roomIdQueryString ? [roomIdQueryString] : undefined,
    },
  })

  const tableData = useMemo(
    () => camerasData?.cameras.edges.map((camera) => camera.node) ?? [],
    [camerasData?.cameras.edges]
  )

  const columns: PaginatedTableColumn<(typeof tableData)[number]>[] = useMemo(
    () => [
      {
        name: 'ID',
        selector: 'id',
        sortAttribute: 'id',
      },
      {
        name: 'Name',
        selector: 'name',
        sortAttribute: 'name',
      },
      {
        name: 'Family',
        selector: 'family',
        sortAttribute: 'family',
      },
      {
        name: 'IP Address',
        selector: 'rtspUrl',
        sortAttribute: 'rtspUrl',
      },
      {
        name: 'Organization',
        selector: 'organization.name',
        sortAttribute: 'organizationId',
      },
      {
        name: 'Site',
        selector: 'site.name',
        sortAttribute: 'siteId',
      },
      {
        name: 'Room',
        selector: 'room.name',
        sortAttribute: 'roomId',
      },
      {
        name: '',
        contents: (rowData) => renderActionComponents(rowData),
      },
    ],
    []
  )

  return (
    <FilterBarPageTemplate>
      <FilterBarPageTemplateFilters>
        <Row>
          <Col css={{ width: '100%' }}>
            <H4 as="h1">Cameras</H4>
          </Col>
        </Row>
        <Row gutter>
          <GetOrgSiteRoomsOptionsComponent
            organizationSelection={organizationIdQueryString}
            siteSelection={siteIdQueryString}
            roomSelection={roomIdQueryString}
            onChangeOrgSiteRoomSelection={onChangeOrgSiteRoom}
          />
        </Row>
      </FilterBarPageTemplateFilters>
      <FilterBarPageTemplateContent>
        <RemotePaginatedTable
          columns={columns}
          data={tableData}
          isLoading={camerasLoading}
          sortOrder={state.orderBy ?? undefined}
          paginationType="cursor"
          onChangePage={(cursorItem) => {
            setState((prev) => ({
              ...prev,
              after: cursorItem.cursor,
            }))
          }}
          onChangeSort={(orderBy) => {
            setState((prev) => ({
              ...prev,
              ...DEFAULT_PAGING_STATE,
              orderBy,
            }))
          }}
          {...camerasData?.cameras.pageCursors}
        />
      </FilterBarPageTemplateContent>
    </FilterBarPageTemplate>
  )
}

const renderActionComponents = (rowData: { id: string }): React.JSX.Element => (
  <Link
    to={generatePath(LocationPath.CameraEditor, {
      cameraId: rowData.id,
    })}
  >
    <Button size="md" color="black" appearance="secondary" buttonType="icon">
      <Edit size="sm" />
    </Button>
  </Link>
)
