import { useMemo } from 'react'
import { useParams } from 'react-router'

import { DateTime } from 'luxon'

import { EventsEditorPage } from 'src/pages/EventsEditorPage/EventsEditorPage'

type EventsEditorLocationParams =
  | 'date'
  | 'roomId'
  | 'organizationId'
  | 'siteId'

export const DateViewEventsEditorPage = (): React.JSX.Element => {
  const { organizationId, siteId, date, roomId } =
    useParams<EventsEditorLocationParams>()

  const { minTime, maxTime } = useMemo(() => {
    const dateTime = DateTime.fromISO(date!, { setZone: true })
    const minTime = dateTime.startOf('day')
    const maxTime = dateTime.endOf('day')

    return { minTime, maxTime }
  }, [date])

  return (
    <EventsEditorPage
      organizationId={organizationId!}
      siteId={siteId!}
      roomId={roomId!}
      minTime={minTime}
      maxTime={maxTime}
      key={`${organizationId}-${siteId}-${roomId}-${date}`}
    />
  )
}
