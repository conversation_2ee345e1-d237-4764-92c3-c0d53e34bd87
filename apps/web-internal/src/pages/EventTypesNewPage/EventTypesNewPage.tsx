import { useNavigate } from 'react-router'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import { useMutation } from '@apollo/client'

import { EventTypesForm } from 'src/modules/eventTypes/components/Form'
import { LocationPath } from 'src/router/LocationPath'

import {
  GET_EVENT_DASHBOARD_VISIBILITY,
  GET_EVENT_TYPES,
} from '../../hooks/useEventTypes/queries'
import {
  CreateEventType,
  CreateEventTypeVariables,
  UpsertEventDashboardVisibility,
  UpsertEventDashboardVisibilityVariables,
} from './__generated__'
import { CREATE_EVENT_TYPE, UPSERT_EVENT_DASHBOARD_VISIBILITY } from './queries'

export const EVENT_TYPE_NEW_PAGE_TITLE = 'New Event Type'

export const EventTypeNewPage = () => {
  const [createEventType] = useMutation<
    CreateEventType,
    CreateEventTypeVariables
  >(CREATE_EVENT_TYPE)
  const [upsertEventDashboardVisibility] = useMutation<
    UpsertEventDashboardVisibility,
    UpsertEventDashboardVisibilityVariables
  >(UPSERT_EVENT_DASHBOARD_VISIBILITY)
  const theme = useTheme()
  const navigate = useNavigate()

  return (
    <EventTypesForm
      title={EVENT_TYPE_NEW_PAGE_TITLE}
      initialValues={{
        id: '',
        name: '',
        type: 'uncategorized',
        description: '',
        color: theme.palette.background.alternate.toLowerCase(),
        shownInInternal: true,
        shownInDashboard: false,
        orgIdFilter: undefined,
      }}
      onSubmit={async (input) => {
        try {
          await createEventType({
            variables: {
              input: {
                id: input.id,
                name: input.name,
                type: input.type,
                description: input.description,
                color: input.color,
                hidden: !input.shownInInternal,
              },
            },
            refetchQueries: [GET_EVENT_TYPES],
          })

          if (input.shownInDashboard) {
            await upsertEventDashboardVisibility({
              variables: {
                input: {
                  eventTypeId: input.id,
                  orgIdFilter: input.orgIdFilter,
                },
              },
              refetchQueries: [GET_EVENT_DASHBOARD_VISIBILITY],
            })
          }

          toast.success(`Event type "${input.name}" created`)
          navigate(LocationPath.EventTypes)
        } catch (e) {
          toast.error('Error creating event type' + (e as any).message || '')
        }
      }}
      onBackButtonClick={() => navigate(LocationPath.EventTypes)}
    />
  )
}
