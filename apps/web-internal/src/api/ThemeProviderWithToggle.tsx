import { createContext, useCallback, useContext } from 'react'

import {
  darkTheme,
  theme,
  ThemeProvider as BaseThemeProvider,
} from '@apella/component-library'
import { useLocalStorageState } from '@apella/hooks'

export const ThemeProviderWithToggle = (props: React.PropsWithChildren) => {
  const [isDarkTheme, setIsDarkTheme] = useLocalStorageState(
    'isDarkTheme',
    false
  )
  const toggleTheme = useCallback(() => {
    setIsDarkTheme(!isDarkTheme)
  }, [setIsDarkTheme, isDarkTheme])

  return (
    <BaseThemeProvider theme={isDarkTheme ? darkTheme : theme}>
      <ToggleThemeContext.Provider value={{ toggleTheme }}>
        {props.children}
      </ToggleThemeContext.Provider>
    </BaseThemeProvider>
  )
}

const ToggleThemeContext = createContext({ toggleTheme: () => {} })

export const useToggleTheme = () => {
  const { toggleTheme } = useContext(ToggleThemeContext)

  return toggleTheme
}
