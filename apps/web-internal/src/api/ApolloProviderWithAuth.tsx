import { Props<PERSON>ith<PERSON>hildren, use<PERSON>emo } from 'react'

import {
  ApolloClient,
  ApolloLink,
  ApolloProvider,
  createHttpLink,
  from,
  InMemoryCache,
  ServerError,
  TypePolicies,
} from '@apollo/client'
import { setContext } from '@apollo/client/link/context'
import { onError } from '@apollo/client/link/error'
import { useAuth0 } from '@auth0/auth0-react'
import { addBreadcrumb, startSpanManual } from '@sentry/browser'
import { Kind, OperationDefinitionNode, OperationTypeNode } from 'graphql'

import { useApellaAuth0 } from '@apella/hooks'
import { settings } from 'src/settings'
import { logger } from 'src/utils/logging'

// https://github.com/apollographql/apollo-feature-requests/issues/153
// This is necessary as Apollo tries to parse an HTML file as JSON,
// and swallows the error that's returned from the server
const customFetch = (uri: RequestInfo | URL, options?: RequestInit) => {
  return fetch(uri, options).then(async (response) => {
    if (response.status >= 500) {
      // or handle 400 errors
      const responseBody = await response.text()
      const networkError: ServerError = {
        name: 'NetworkError',
        response,
        message: responseBody,
        statusCode: response.status,
        result: {},
      }
      return Promise.reject(networkError)
    }
    return response
  })
}

const graphQLUri = settings.api.domain + settings.api.graphQLUrl

const httpLink = createHttpLink({
  uri: graphQLUri,
  fetch: customFetch,
})

const typePolicies: TypePolicies = {
  Event: { keyFields: ['id', 'version'] },
}

const cache = new InMemoryCache({ typePolicies })

const getOperationType = (types: Set<OperationTypeNode>) => {
  if (types.size > 1) {
    return 'multiple'
  }

  if (types.size < 1) {
    return 'unknown'
  }

  const [type] = types

  return type
}

export const ApolloProviderWithAuth = ({ children }: PropsWithChildren) => {
  const { getAccessTokenSilently } = useAuth0()

  /**
   * Fetch the latest auth token and provide it as an Authorization header.
   * `getAccessTokenSilently()` will fetch the token from the in-memory cache, so calling on each
   * API request is not expensive. Further, if the token has expired, the method will call the Auth0
   * API to fetch a new token and set it to the in-memory cache.
   */
  const authLink = setContext(async (_, { headers }) => {
    const token = await getAccessTokenSilently()

    return {
      headers: {
        ...headers,
        Authorization: token ? `Bearer ${token}` : '',
      },
    }
  })
  const errorLink = useErrorLink()

  const graphqlClient = new ApolloClient({
    cache,
    link: from([sentrySpanLink, errorLink, authLink, httpLink]),
  })
  return <ApolloProvider client={graphqlClient}>{children}</ApolloProvider>
}

// Taken from Apollo Documentation Here https://www.apollographql.com/docs/react/data/error-handling/#advanced-error-handling-with-apollo-link
const useErrorLink = () => {
  const { user } = useApellaAuth0()
  const userId = user?.sub

  const errorLink = useMemo(
    () =>
      onError(({ graphQLErrors, networkError, operation }) => {
        if (graphQLErrors) {
          graphQLErrors.forEach(({ message, locations, path }) =>
            logger.warn(
              `[GraphQL error]: Message: ${message}`,
              {
                locations,
                path,
                operationName: operation.operationName,
              },
              userId,
              {
                operationName: operation.operationName,
              }
            )
          )
        }

        // This might change to a log or a warning in the future,
        // but because of 500 errors that we can't see returning to the client
        // I want an error for the time being
        if (networkError) {
          const extras =
            'response' in networkError
              ? Object.fromEntries(networkError.response.headers.entries())
              : {}

          logger.error(
            `[Network error]: ${networkError.message}`,
            {
              ...extras,
              operationName: operation.operationName,
            },
            userId,
            {
              operationName: operation.operationName,
            }
          )
        }
      }),
    [userId]
  )

  return errorLink
}

const sentrySpanLink = new ApolloLink((operation, forward) => {
  const operationName = operation.operationName || 'Unnamed Operation'

  // Check if the operation is a query/mutation/subscription/multiple
  const operationTypes = operation.query.definitions
    .filter(
      (def): def is OperationDefinitionNode =>
        def.kind === Kind.OPERATION_DEFINITION
    )
    .reduce<
      Set<OperationTypeNode>
    >((acc, def) => acc.add(def.operation), new Set())

  const operationType = getOperationType(operationTypes)

  addBreadcrumb({
    category: 'graphql',
    data: {
      operationName,
      operation: operationType,
      body: operation.query.loc?.source.body.slice(0, 100),
    },
  })

  return startSpanManual(
    {
      name: operationName,
      op: 'http.graphql.' + operationType,
      attributes: {
        body: operation.query.loc?.source.body.slice(0, 100),
      },
    },
    (span) =>
      forward(operation).map((request) => {
        span?.end()
        return request
      })
  )
})
