import { useMemo } from 'react'
import { toast } from 'react-toastify'

import { useMutation } from '@apollo/client'
import { useAuth0 } from '@auth0/auth0-react'

import { Option, SingleSelect } from '@apella/component-library'

import { GetAnnotationSearchFilters } from '../pages/AnnotationTasksPage/__generated__'
import { sortTaskUsers } from '../utils/sortTaskUsers'
import { UpdateTask, UpdateTaskVariables } from './__generated__'
import { UPDATE_ANNOTATION_TASK } from './taskMutations'

const UNASSIGN_SPECIAL_VALUE = 'TASK_REVIEWER_SELECT_UNASSIGN'

type GetAnnotationSearchFilters_reviewers_edges =
  GetAnnotationSearchFilters['reviewers']['edges'][number]

interface TaskReviewerSelectProps {
  reviewers?: GetAnnotationSearchFilters_reviewers_edges[]
  room: string
  selectedId?: string
  site: string
  taskId: string
}

export const TaskReviewerSelect = ({
  taskId,
  selectedId,
  reviewers,
  site,
  room,
}: TaskReviewerSelectProps): React.JSX.Element => {
  const [updateTask] = useMutation<UpdateTask, UpdateTaskVariables>(
    UPDATE_ANNOTATION_TASK
  )
  const { user } = useAuth0()

  const getReviewerById = (id?: string): string | undefined => {
    return reviewers?.find((a) => a.node.id === id)?.node.name
  }

  const sortedReviewers = useMemo(
    () => sortTaskUsers(reviewers, user),
    [reviewers, user]
  )

  return (
    <SingleSelect
      name="reviewer"
      label="Unassigned"
      value={selectedId}
      onChange={(value) => {
        updateTask({
          variables: {
            taskUpdateInput: {
              id: taskId,
              reviewerUserId: value === UNASSIGN_SPECIAL_VALUE ? null : value,
            },
          },
        }).then((response) => {
          if (response.data?.annotationTaskUpdate?.success) {
            const reviewerName = getReviewerById(value)
            const taskName = `${site} \u2013 ${room}`

            let message
            if (reviewerName === undefined) {
              message = `Unassigned ${taskName}`
            } else {
              message = `Assigned ${reviewerName} as reviewer to ${taskName}`
            }

            toast.success(message)
          }
        })
      }}
    >
      {!!selectedId && (
        <Option
          key={'unassigned'}
          label={'Unassigned'}
          value={UNASSIGN_SPECIAL_VALUE}
        />
      )}
      {sortedReviewers?.map((reviewer) => (
        <Option
          key={reviewer.node.id}
          value={reviewer.node.id}
          label={reviewer.node.name}
        />
      ))}
    </SingleSelect>
  )
}
