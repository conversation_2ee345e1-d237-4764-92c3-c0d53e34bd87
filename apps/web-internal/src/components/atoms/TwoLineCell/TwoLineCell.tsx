import { Fragment, memo, ReactNode } from 'react'

import { useTheme } from '@emotion/react'

import { fontRamp } from '@apella/component-library'

export const TwoLineCell = memo(function DateCell({
  line1,
  line2,
}: {
  line1: string
  line2: string
  children?: ReactNode
}): React.JSX.Element {
  const theme = useTheme()

  return (
    <Fragment>
      <div
        css={{
          color: theme.palette.text.tertiary,
          ...fontRamp['14px'],
        }}
      >
        {line1}
      </div>
      <div
        css={{
          color: theme.palette.text.primary,
          ...fontRamp['16px'],
        }}
      >
        {line2}
      </div>
    </Fragment>
  )
})
