import { useTheme } from '@emotion/react'

import { H5, remSpacing, Tile } from '@apella/component-library'
import { SvgIconProps } from '@apella/component-library/src/icons/SvgIcon'

export const FormTile = (props: React.ComponentProps<typeof Tile>) => {
  const theme = useTheme()
  return (
    <Tile
      gutter
      css={{ border: `1px solid ${theme.palette.gray[30]}` }}
      shadow={false}
      {...props}
    />
  )
}

export const TileHeading = ({
  title,
  icon: Icon,
  className,
}: {
  icon: React.ComponentType<SvgIconProps>
  title: string
  className?: string
}) => {
  const theme = useTheme()

  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.xsmall,
        alignItems: 'center',
      }}
      className={className}
    >
      <Icon color={theme.palette.gray[60]} size="sm" />
      <H5 as="h2">{title}</H5>
    </div>
  )
}
