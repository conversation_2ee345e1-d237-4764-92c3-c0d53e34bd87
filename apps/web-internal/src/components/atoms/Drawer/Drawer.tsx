import { PropsWithChildren } from 'react'

import { useTheme } from '@emotion/react'

import {
  Button,
  Col,
  ExpandLess,
  ExpandMore,
  remSpacing,
  Row,
} from '@apella/component-library'

export const Drawer = ({
  onClose,
  children,
}: PropsWithChildren & { onClose: () => void }) => {
  const theme = useTheme()

  return (
    <div
      css={{
        position: 'absolute',
        width: '100%',
        opacity: 0.99,
        borderBottom: `1px solid ${theme.palette.gray[20]}`,
        backgroundColor: theme.palette.background.primary,
        boxShadow: theme.shadows[2],
      }}
    >
      {children}
      <Row
        onClick={onClose}
        css={{
          cursor: 'pointer',
          '&:hover': { backgroundColor: theme.palette.gray[20] },
          '&:active': { backgroundColor: theme.palette.gray[30] },
          padding: remSpacing.xsmall,
          borderTop: `1px solid ${theme.palette.gray[20]}`,
        }}
      >
        <Col css={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
          <ExpandLess size="sm" />
        </Col>
      </Row>
    </div>
  )
}

export const DrawerToggleButton = ({
  isOpen,
  onClick,
}: {
  isOpen: boolean
  onClick: () => void
}) => {
  const ToggleIcon = isOpen ? ExpandLess : ExpandMore
  return (
    <Button color={'alternate'} buttonType="icon" onClick={onClick}>
      <ToggleIcon size="sm" />
    </Button>
  )
}
