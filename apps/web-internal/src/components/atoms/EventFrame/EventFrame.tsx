import { Dispatch, memo, ReactNode, SetStateAction, useMemo } from 'react'

import { useTheme } from '@emotion/react'

import {
  FlexContainer,
  FlexItem,
  remSpacing,
  shape,
} from '@apella/component-library'

export type EventFrameProps = {
  background?: string
  children: ReactNode
  colorBar: React.JSX.Element
  onClick?: () => void
  setRef?: Dispatch<SetStateAction<Element | null>>
}

export const EventFrame = memo(function EventFrame({
  children,
  colorBar,
  onClick,
  setRef,
  background,
}: EventFrameProps): React.JSX.Element {
  const theme = useTheme()
  return (
    <section
      css={{
        background,
        padding: `${remSpacing.small} ${remSpacing.xsmall} ${remSpacing.small} 0`,
        borderBottom: '1px',
        borderColor: theme.palette.gray[20],
        borderStyle: 'solid',
        cursor: !!onClick ? 'pointer' : undefined,
      }}
      onClick={onClick}
      ref={setRef}
    >
      <FlexContainer>
        <FlexItem>{colorBar}</FlexItem>
        <FlexItem
          grow
          css={{
            minWidth: 100,
            margin: 'auto',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {children}
        </FlexItem>
      </FlexContainer>
    </section>
  )
})

export const ColorBar = memo(function ColorBar({
  color,
  isSaved,
  outline,
  onClick,
}: {
  color: string
  /** Color of outline. If `undefined` is provided, then no outline is shown. */
  outline?: string
  isSaved: boolean
  onClick?: () => void
}): React.JSX.Element {
  const theme = useTheme()

  const outlineStyle = useMemo(() => {
    if (!outline) return {}

    return {
      outline: `${outline} solid 0.125rem`,
      outlineOffset: '0.125rem',
    }
  }, [outline])

  return (
    <div
      css={{
        height: '100%',
        minHeight: remSpacing.xlarge,
        paddingLeft: remSpacing.medium,
        marginRight: remSpacing.xsmall,
      }}
      style={{
        cursor: !!onClick ? 'pointer' : undefined,
      }}
      onClick={onClick}
    >
      <div
        css={{
          borderRadius: shape.borderRadius.xxsmall,
          height: '100%',
          width: remSpacing.xxsmall,
          marginLeft: remSpacing.xxsmall,
          marginRight: remSpacing.xxsmall,
        }}
        style={{
          ...outlineStyle,
          background: isSaved
            ? color
            : `repeating-linear-gradient(30deg, ${color}, ${color} 12px, ${theme.palette.red[50]} 12px,  ${theme.palette.red[50]} 16px)`,
        }}
      />
    </div>
  )
})
