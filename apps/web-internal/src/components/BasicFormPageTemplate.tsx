import {
  Button,
  flexboxgrid,
  FlexContainer,
  H4,
  remSpacing,
  Save,
} from '@apella/component-library'

import { HR } from './atoms/HR'

export function BasicFormPageTemplate({
  title,
  children,
}: React.PropsWithChildren<{ title: string }>) {
  return (
    <FlexContainer justifyContent="center">
      <div css={{ width: flexboxgrid.breakpoints['sm'] }}>
        <div css={{ margin: `${remSpacing.large} 0` }}>
          <H4 as="h1">{title}</H4>
        </div>
        {children}
      </div>
    </FlexContainer>
  )
}

export const FormSection = ({
  title,
  children,
}: {
  title?: string
  children: React.ReactNode
}) => {
  return (
    <>
      <section
        css={{
          display: 'flex',
          flexDirection: 'column',
          gap: remSpacing.medium,
          marginBottom: remSpacing.medium,
        }}
      >
        {title && (
          <div css={{ margin: `${remSpacing.medium} 0` }}>
            <H4 as="h2">{title}</H4>
          </div>
        )}
        {children}
      </section>
      <HR />
    </>
  )
}

export const SaveButton = (props: React.ComponentProps<typeof Button>) => {
  return (
    <Button type="submit" {...props}>
      <Save size="sm" /> Save
    </Button>
  )
}

export const ButtonToolbar = ({
  left,
  right,
}: {
  left?: React.ReactNode
  right?: React.ReactNode
}) => {
  return (
    <FlexContainer
      justifyContent="space-between"
      gap={remSpacing.xsmall}
      css={{
        margin: `${remSpacing.medium} 0 ${remSpacing.large}`,
      }}
    >
      <FlexContainer gap={remSpacing.xsmall}>{left}</FlexContainer>
      <FlexContainer gap={remSpacing.xsmall}>{right}</FlexContainer>
    </FlexContainer>
  )
}
