import { debounce } from 'lodash'

import { remSpacing, Search, SearchBar } from '@apella/component-library'

export const DebounceSearchBar = ({
  name,
  onChange,
}: {
  name: string
  onChange: (e: string) => void
}) => {
  const debouncedChangeHandler = debounce((e) => {
    onChange(e.target.value)
  }, 1000)

  return (
    <div
      style={{
        display: 'inline-block;',
        marginLeft: remSpacing.large,
        width: '28 rem',
        marginBottom: remSpacing.large,
      }}
    >
      <SearchBar
        name={name}
        placeholder="Search notes"
        onChange={debouncedChangeHandler}
      >
        <SearchBar.IconLeft>
          <Search></Search>
        </SearchBar.IconLeft>
      </SearchBar>
    </div>
  )
}
