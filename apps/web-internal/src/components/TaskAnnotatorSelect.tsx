import { useMemo } from 'react'
import { toast } from 'react-toastify'

import { useMutation } from '@apollo/client'
import { useAuth0 } from '@auth0/auth0-react'

import { Option, SingleSelect } from '@apella/component-library'

import { GetAnnotationSearchFilters } from '../pages/AnnotationTasksPage/__generated__'
import { sortTaskUsers } from '../utils/sortTaskUsers'
import { UpdateTask, UpdateTaskVariables } from './__generated__'
import { UPDATE_ANNOTATION_TASK } from './taskMutations'

type GetAnnotationSearchFilters_annotators_edges =
  GetAnnotationSearchFilters['annotators']['edges'][number]

const UNASSIGN_SPECIAL_VALUE = 'TASK_ANNOTATOR_SELECT_UNASSIGN'

interface TaskAnnotatorSelectProps {
  annotators?: GetAnnotationSearchFilters_annotators_edges[]
  room: string
  selectedId?: string
  site: string
  taskId: string
}

export const TaskAnnotatorSelect = ({
  taskId,
  selectedId,
  annotators,
  site,
  room,
}: TaskAnnotatorSelectProps): React.JSX.Element => {
  const { user } = useAuth0()
  const [updateTask] = useMutation<UpdateTask, UpdateTaskVariables>(
    UPDATE_ANNOTATION_TASK
  )
  const getAnnotatorById = (id?: string): string | undefined => {
    return annotators?.find((a) => a.node.id === id)?.node.name
  }

  const sortedAnnotators = useMemo(
    () => sortTaskUsers(annotators, user),
    [annotators, user]
  )

  return (
    <SingleSelect
      name="assignee"
      label="Unassigned"
      value={selectedId}
      onChange={(value) => {
        updateTask({
          variables: {
            taskUpdateInput: {
              id: taskId,
              annotatorUserId: value === UNASSIGN_SPECIAL_VALUE ? null : value,
            },
          },
        }).then((response) => {
          if (response.data?.annotationTaskUpdate?.success) {
            const annotatorName = getAnnotatorById(value)
            const taskName = `${site} \u2013 ${room}`

            let message
            if (annotatorName === undefined) {
              message = `Unassigned ${taskName}`
            } else {
              message = `Assigned ${annotatorName} as annotator to ${taskName}`
            }

            toast.success(message)
          }
        })
      }}
    >
      {!!selectedId && (
        <Option
          key={'unassigned'}
          label={'Unassigned'}
          value={UNASSIGN_SPECIAL_VALUE}
        />
      )}
      {sortedAnnotators?.map((annotator) => (
        <Option
          key={annotator.node.id}
          value={annotator.node.id}
          label={annotator.node.name}
        />
      ))}
    </SingleSelect>
  )
}
