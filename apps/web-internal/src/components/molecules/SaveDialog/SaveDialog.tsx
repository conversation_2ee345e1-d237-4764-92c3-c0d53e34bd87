import {
  Button,
  Dialog,
  FlexContainer,
  remSpacing,
  theme,
} from '@apella/component-library'

export interface SaveDialogProps {
  onDiscard: () => void
  onSave: () => void
}

export const SaveDialog = ({ onDiscard, onSave }: SaveDialogProps) => {
  return (
    <Dialog title="Save your changes?" isOpen={true} onClose={onSave}>
      <FlexContainer
        css={{
          flexDirection: 'column',
          color: theme.palette.text.secondary,
        }}
      >
        You have unsaved changes. Do you want to <br /> save?
      </FlexContainer>
      <div
        css={{
          gap: remSpacing.small,
          display: 'flex',
          paddingTop: remSpacing.large,
        }}
      >
        <Button onClick={onSave} size="md">
          Save
        </Button>
        <Button onClick={onDiscard} size="md" color="alternate">
          Discard Changes
        </Button>
      </div>
    </Dialog>
  )
}
