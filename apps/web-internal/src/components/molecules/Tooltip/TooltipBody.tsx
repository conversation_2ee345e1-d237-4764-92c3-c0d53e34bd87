import React from 'react'

import {
  Caps2,
  Caps2Bold,
  FlexContainer,
  FlexItem,
} from '@apella/component-library'

export type ToolTipType = 'default' | 'Verified' | 'Forecasted'

export const TooltipBody = React.memo(function TooltipBody({
  title,
  body,
  wrapBody = true,
}: {
  title: string
  body: string
  wrapBody?: boolean
}) {
  return (
    <React.Fragment>
      <FlexContainer
        css={{ whiteSpace: 'nowrap' }}
        justifyContent={'space-between'}
        alignItems={'center'}
      >
        <FlexItem>
          <Caps2Bold>{title}</Caps2Bold>
        </FlexItem>
      </FlexContainer>
      <Caps2 css={{ whiteSpace: !wrapBody ? 'nowrap' : undefined }}>
        {body}
      </Caps2>
    </React.Fragment>
  )
})
