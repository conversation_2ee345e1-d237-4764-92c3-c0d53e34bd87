import { useId } from 'react'

import { useTheme } from '@emotion/react'

import {
  pxSpacing,
  Span3,
  ToggleOn,
  ToggleOff,
} from '@apella/component-library'

export const ToggleButton = ({
  label,
  isOn,
  onToggle,
  ...props
}: {
  label: string
  isOn: boolean
  onToggle: () => void
}) => {
  const labelId = `toggle-switch-label-${useId()}`
  const theme = useTheme()

  return (
    <button
      role="switch"
      aria-checked={isOn}
      aria-labelledby={labelId}
      onClick={onToggle}
      css={{
        display: 'inline-flex',
        alignItems: 'center',
        backgroundColor: 'transparent',
        border: 'none',
        cursor: 'pointer',
        padding: 0,
        gap: pxSpacing.xxsmall,
      }}
      {...props}
    >
      {isOn ? (
        <ToggleOn color={theme.palette.blue[60]} size="lg" />
      ) : (
        <ToggleOff color={theme.palette.gray[60]} size="lg" />
      )}
      <Span3 id={labelId}>{label}</Span3>
    </button>
  )
}
