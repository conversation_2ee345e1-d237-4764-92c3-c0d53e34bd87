import { useQuery } from '@apollo/client'

import { Input, Option } from '@apella/component-library'

import { GetAnnotators } from './__generated__'
import { GET_ANNOTATORS } from './queries'

export const ANNOTATOR_UNASSIGNED_SPECIAL_VALUE = 'ANNOTATOR_FILTER_UNASSIGNED'

export const AnnotatorSelect = ({
  placeholder,
  ...props
}: Pick<
  React.ComponentPropsWithoutRef<typeof Input.MultiSelect>,
  'className' | 'value' | 'label' | 'name' | 'hint' | 'onChange' | 'placeholder'
>): React.JSX.Element => {
  const { loading, data } = useQuery<GetAnnotators>(GET_ANNOTATORS)

  const annotators = data?.annotators.edges ?? []

  return (
    <Input.MultiSelect
      readOnly={loading}
      placeholder={loading ? 'Loading...' : placeholder}
      {...props}
    >
      {annotators.map((annotator) => (
        <Option
          key={annotator.node.id}
          value={annotator.node.id}
          label={annotator.node.name}
        />
      ))}
    </Input.MultiSelect>
  )
}
