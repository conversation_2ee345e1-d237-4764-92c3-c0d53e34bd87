import { useMemo } from 'react'

import { gql, useQuery } from '@apollo/client'

import { Input, Option } from '@apella/component-library'

import { GetRoomTagsByOrg, GetRoomTagsByOrgVariables } from './__generated__'

const GET_ROOM_TAGS_BY_ORG = gql`
  query GetRoomTagsByOrg($orgId: String!) {
    roomTags(orgId: $orgId) {
      edges {
        node {
          id
          name
          orgId
        }
      }
    }
  }
`

export function RoomTagsInput({
  label = 'Tags',
  orgId,
  ...props
}: Omit<React.ComponentProps<typeof Input.MultiSelect>, 'children'> & {
  orgId: string
}): React.JSX.Element {
  const { data } = useQuery<GetRoomTagsByOrg, GetRoomTagsByOrgVariables>(
    GET_ROOM_TAGS_BY_ORG,
    { variables: { orgId } }
  )

  const roomTags = useMemo(
    () =>
      data?.roomTags.edges.flatMap(({ node }) => {
        return { ...node }
      }) ?? [],
    [data?.roomTags.edges]
  )

  return (
    <Input.MultiSelect bulkSelect label={label} {...props}>
      {roomTags.map((t) => (
        <Option key={t.id} value={t.id} label={t.name} />
      ))}
    </Input.MultiSelect>
  )
}
