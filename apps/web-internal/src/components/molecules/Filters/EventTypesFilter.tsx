import { Input, MultiSelect, Option, Progress } from '@apella/component-library'
import { useEventTypes } from 'src/hooks/useEventTypes'
import { UNIDENTIFIED } from 'src/types/event'

type Props = {
  /** Event type IDs. TODO: Make this type more specific than `string[]`? */
  value?: string[]
  filter?: (value?: string) => boolean
  onChange?: (value?: string[]) => void
  className?: string
}

export function EventTypesFilter({
  label = 'Event types',
  filter = () => true,
  ...props
}: Props &
  Omit<
    React.ComponentProps<typeof MultiSelect>,
    /** <PERSON>: couldn't get `css` prop to play nice w/TS - if you figure it out please remove */
    'children' | 'css' | 'onChange'
  >): React.JSX.Element {
  const { loading, eventTypes } = useEventTypes()

  if (loading) return <Progress />

  return (
    <MultiSelect label={label} {...props}>
      {eventTypes
        .filter(({ id }) => filter(id))
        .map((eventType) => (
          <Option
            key={eventType.id}
            value={eventType.id}
            label={eventType.name ?? UNIDENTIFIED}
          />
        ))}
    </MultiSelect>
  )
}

export function EventTypesInput({
  label = 'Event types',
  filter = () => true,
  ...props
}: Props &
  Omit<
    React.ComponentProps<typeof Input.MultiSelect>,
    /** Darren: couldn't get `css` prop to play nice w/TS - if you figure it out please remove */
    'children' | 'css' | 'onChange'
  >): React.JSX.Element {
  const { loading, eventTypes } = useEventTypes()

  if (loading) return <Progress />

  return (
    <Input.MultiSelect label={label} {...props}>
      {eventTypes
        .filter(({ id }) => filter(id))
        .map((eventType) => (
          <Option
            key={eventType.id}
            value={eventType.id}
            label={eventType.name ?? UNIDENTIFIED}
          />
        ))}
    </Input.MultiSelect>
  )
}
