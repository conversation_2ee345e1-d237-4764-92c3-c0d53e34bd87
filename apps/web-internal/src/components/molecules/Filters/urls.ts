import { DateTime } from 'luxon'

import { Direction, OrderBy } from 'src/__generated__/globalTypes'

// A human readable format for dates
const FORMAT = 'yyyy-MM-dd'

const dateTimeToUrlFriendlyDate = (datetime: DateTime): string =>
  datetime.toFormat(FORMAT)

const urlFriendlyDateToJSDate = (
  urlFriendlyDate: string,
  locale: string
): string =>
  DateTime.fromFormat(urlFriendlyDate, FORMAT, { zone: locale }).toISO()

/**
 * orderBy: [
    {
      sort: 'startTime',
      direction: Direction.ASC,
    },
    {
      sort: 'status',
      direction: Direction.DESC,
    },
  ]
  becomes 'startTime-ASC~status-DESC', vice versa
 */
const orderByToUrlFriendly = (orderBys?: OrderBy[]): string | undefined =>
  orderBys && orderBys.map((o) => `${o.sort}-${o.direction}`).join('~')

const urlFriendlyOrderStrToOrderBy = (
  urlFriendlyOrderByStr?: string,
  sortableFields?: readonly string[]
): OrderBy[] | undefined => {
  if (urlFriendlyOrderByStr && typeof urlFriendlyOrderByStr === 'string') {
    return urlFriendlyOrderByStr
      .split('~')
      .map((pair: string): OrderBy | undefined => {
        const [sort, direction] = pair.split('-')

        if (sortableFields?.includes(sort)) {
          return {
            sort,
            direction: direction === 'ASC' ? Direction.ASC : Direction.DESC,
          }
        }
      })
      .filter((o): o is OrderBy => o !== undefined)
  }
}

export {
  dateTimeToUrlFriendlyDate,
  orderByToUrlFriendly,
  urlFriendlyDateToJSDate,
  urlFriendlyOrderStrToOrderBy,
}
