import {
  Input,
  MultiSelect,
  Option,
  SingleSelect,
} from '@apella/component-library'
import { DataNodeWithCount } from 'src/components/molecules/Filters/helper'

interface SingleFilterWithCountProps<T>
  extends Pick<
    React.ComponentProps<typeof SingleSelect>,
    'onChange' | 'label' | 'value'
  > {
  as?: typeof SingleSelect | typeof Input.SingleSelect
  items: DataNodeWithCount<T>[]
  name?: string
  selectAllLabel?: string
}

export function SingleFilterWithCount<T>({
  items,
  onChange,
  as = SingleSelect,
  label,
  selectAllLabel,
  name,
  value,
  ...props
}: SingleFilterWithCountProps<T>): React.JSX.Element {
  const SelectComponent = as

  return (
    <SelectComponent
      name={name || `${label}-filter`}
      label={label}
      search={true}
      onChange={onChange}
      value={value}
      {...props}
    >
      <Option key={`${label}-all-option`} label={selectAllLabel ?? 'All'} />
      {items.map((item) => (
        <Option
          key={item.node.id}
          value={item.node.id}
          label={item.node.name}
          count={item.node.count}
          group={item.node.group}
        />
      ))}
    </SelectComponent>
  )
}

interface MultiFilterWithCountProps<T>
  extends Pick<
    React.ComponentProps<typeof MultiSelect>,
    'className' | 'bulkSelect' | 'onChange' | 'label' | 'value'
  > {
  as?: typeof MultiSelect | typeof Input.MultiSelect
  items: DataNodeWithCount<T>[]
  name?: string
  selectAllLabel?: string
}

export function MultiFilterWithCount<T>({
  items,
  as = MultiSelect,
  label,
  name,
  ...props
}: MultiFilterWithCountProps<T>): React.JSX.Element {
  const SelectComponent = as

  return (
    <SelectComponent
      label={label}
      name={name ?? `${label}-filter`}
      search
      {...props}
    >
      {items.map((item) => (
        <Option
          key={item.node.id}
          value={item.node.id}
          label={item.node.name}
          count={item.node.count}
          group={item.node.group}
        />
      ))}
    </SelectComponent>
  )
}
