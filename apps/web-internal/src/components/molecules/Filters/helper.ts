export const DASH = '\u2014'

export type DataNodeWithCount<DataType> = DataType & {
  node: { id: string; name: string; count?: number; group?: string }
}

type Count<CountType> = CountType & {
  parentId: string | null
  count: number | null
}

export const getUserOptionCount = function <DataType, CountType>(
  users: Array<DataNodeWithCount<DataType>>,
  countArray: Array<Count<CountType> | null> | null | undefined,
  unassigned_value: string
): Array<DataType> {
  if (countArray && countArray.length > 0) {
    return users.map((edge) => {
      const count =
        countArray.find((annotator) => {
          // Match Unassigned
          if (edge.node.id === unassigned_value && annotator?.parentId === null)
            return true
          return annotator?.parentId === edge.node.id
        })?.count ?? 0

      return {
        ...edge,
        node: {
          ...edge.node,
          count,
        },
      }
    })
  }
  return users
}

export const getLocationOptionCount = function <DataType, CountType>(
  array: Array<DataNodeWithCount<DataType>>,
  arrayCount: Array<Count<CountType> | null> | null | undefined
): Array<DataType> {
  return array.map((edge) => {
    const count =
      arrayCount?.find((e) => e?.parentId === edge.node.id)?.count ?? 0

    return {
      ...edge,
      node: {
        ...edge.node,
        count,
      },
    }
  })
}
