import { useContext } from 'react'

import { DatePicker } from '@apella/component-library'
import { LocaleContext } from 'src/contexts/locale'

interface DateFilterProps {
  onChangeDate: (dates: [Date, Date]) => void
  selected?: [Date, Date]
}

export const DateFilter = ({
  selected,
  onChangeDate,
}: DateFilterProps): React.JSX.Element => {
  const timezone = useContext(LocaleContext)
  return (
    <DatePicker
      value={selected}
      selectRange={true}
      showPresets={true}
      setValue={(dates) =>
        dates &&
        Array.isArray(dates) &&
        dates[0] &&
        dates[1] &&
        onChangeDate([dates[0], dates[1]])
      }
      timezone={timezone}
    />
  )
}
