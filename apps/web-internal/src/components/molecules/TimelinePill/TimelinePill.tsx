import React from 'react'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import { remSpacing, Tooltip, TooltipProps } from '@apella/component-library'
import { useEventsEditorContext } from 'src/pages/EventsEditorPage/EventsEditorContext'

const PILL_MARGIN = 6
const MIN_WIDTH_TO_SHOW_DETAILS = 100
const MIN_PILL_WIDTH_PX = 20

export type MouseMovementHandler = (id?: string) => void
export interface PillPositionAndSize {
  forecastedWidthPx: number
  left: string
  widthPct: string
  widthPx: number
}

export const calculatePillPositionAndSizeHelper = (
  startTime: DateTime,
  endTime: DateTime,
  timelineWidth: number,
  minHour: DateTime,
  maxHour: DateTime,
  minWidth: number = MIN_PILL_WIDTH_PX,
  liveTime?: DateTime
): PillPositionAndSize => {
  if (startTime > maxHour || endTime < minHour)
    return { left: '0', widthPct: '0%', widthPx: 0, forecastedWidthPx: 0 }

  const timelineWidthMillis = maxHour.diff(minHour).toMillis()

  const startFrac =
    Math.max(startTime.diff(minHour).toMillis(), 0) / timelineWidthMillis
  const endFrac =
    Math.min(
      (liveTime || endTime).diff(minHour).toMillis(),
      timelineWidthMillis
    ) / timelineWidthMillis
  const forecastedFrac = liveTime
    ? Math.min(endTime.diff(minHour).toMillis(), timelineWidthMillis) /
      timelineWidthMillis
    : 0

  const trueWidthFrac = endFrac - startFrac
  const minFinalWidth = minWidth / timelineWidth
  const finalWidthFrac = Math.max(trueWidthFrac, minFinalWidth)
  const forecastedWidthFrac = Math.max(0, forecastedFrac - endFrac)
  const trueLeftFrac = startFrac - (finalWidthFrac - trueWidthFrac) / 2
  const finalLeftFrac = Math.max(0, Math.min(trueLeftFrac, 1 - finalWidthFrac))

  return {
    left: `${finalLeftFrac * 100}%`,
    widthPct: `${finalWidthFrac * 100}%`,
    widthPx: Math.round(finalWidthFrac * timelineWidth),
    forecastedWidthPx: Math.round(forecastedWidthFrac * timelineWidth),
  }
}

export interface TimelinePillProps {
  displayText?: React.JSX.Element
  id?: string
  isHovered?: boolean
  onClick?: () => void
  onMouseEnter?: MouseMovementHandler
  onMouseLeave?: MouseMovementHandler
  pillPositionAndSize: PillPositionAndSize
  tooltipBody: React.JSX.Element
  tooltipPlacement?: TooltipProps['placement']
}

export const TimelinePill = React.memo(function TimelinePill({
  id,
  isHovered = false,
  pillPositionAndSize,
  onMouseEnter,
  onMouseLeave,
  onClick,
  tooltipBody,
  displayText,
  tooltipPlacement = 'bottom',
}: TimelinePillProps) {
  const { left, widthPct, widthPx } = pillPositionAndSize
  const theme = useTheme()
  const { isTheaterMode } = useEventsEditorContext()

  const { backgroundColor, hoverBackgroundColor, hoverBorderColor } = {
    backgroundColor: theme.palette.gray.background,
    hoverBackgroundColor: theme.palette.blue.background,
    hoverBorderColor: theme.palette.blue[20],
  }
  const height = isTheaterMode ? 28 : 48
  const coloringCss = {
    background: isHovered ? hoverBackgroundColor : backgroundColor,
    border: `solid 1px ${
      isHovered ? hoverBorderColor : theme.palette.gray[20]
    }`,
    borderRadius: remSpacing.xsmall,
  }

  const mouseEnterHandler = React.useCallback(
    () => onMouseEnter && onMouseEnter(id),
    [onMouseEnter, id]
  )
  const mouseLeaveHandler = React.useCallback(
    () => onMouseLeave && onMouseLeave(id),
    [onMouseLeave, id]
  )

  if (widthPx <= 0) return null

  const InnerComponent =
    widthPx < MIN_WIDTH_TO_SHOW_DETAILS && !isTheaterMode ? (
      <Tooltip body={tooltipBody} placement={tooltipPlacement}>
        <div
          css={{
            height,
            width: widthPx,
            ...coloringCss,
          }}
        />
      </Tooltip>
    ) : (
      <div css={coloringCss}>
        <div
          css={{
            padding: `4px ${remSpacing.xsmall}`,
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
          }}
          style={{
            width: widthPx,
          }}
        >
          {displayText}
        </div>
      </div>
    )

  return (
    <div
      onMouseEnter={mouseEnterHandler}
      onMouseLeave={mouseLeaveHandler}
      css={{
        position: 'absolute',
        borderRadius: remSpacing.xsmall,
        top: PILL_MARGIN,
        cursor: 'pointer',
        display: 'flex',
        height: height,
      }}
      style={{
        left,
        width: widthPct,
        zIndex: isHovered ? 10 : undefined,
      }}
      onClick={onClick}
    >
      {InnerComponent}
    </div>
  )
})
