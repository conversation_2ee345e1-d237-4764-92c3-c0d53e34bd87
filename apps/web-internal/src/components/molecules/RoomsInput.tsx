import { useMemo } from 'react'

import { gql, useQuery } from '@apollo/client'

import { Input, Option } from '@apella/component-library'

import { GetRoomData } from './__generated__'

const GET_ROOM_DATA = gql`
  query GetRoomData {
    rooms {
      edges {
        node {
          id
          name
          site {
            id
            name
          }
        }
      }
    }
  }
`

export function RoomsInput({
  label = 'Rooms',
  ...props
}: Omit<
  React.ComponentProps<typeof Input.MultiSelect>,
  'children'
>): React.JSX.Element {
  const { data } = useQuery<GetRoomData>(GET_ROOM_DATA)

  const items = useMemo(
    () =>
      data?.rooms.edges.flatMap(({ node }) => {
        return { ...node, group: node.site.name }
      }) ?? [],
    [data?.rooms.edges]
  )

  return (
    <Input.MultiSelect bulkSelect label={label} {...props}>
      {items.map((item) => (
        <Option
          key={item.id}
          value={item.id}
          label={item.name}
          group={item.group}
        />
      ))}
    </Input.MultiSelect>
  )
}
