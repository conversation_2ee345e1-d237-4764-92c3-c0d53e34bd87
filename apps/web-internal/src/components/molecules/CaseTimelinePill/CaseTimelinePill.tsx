import React from 'react'

import { DateTime } from 'luxon'

import {
  Caps3,
  Caps3Bold,
  remSpacing,
  TooltipProps,
} from '@apella/component-library'
import { useEventsEditorContext } from 'src/pages/EventsEditorPage/EventsEditorContext'

import { Case } from '../../../pages/EventsEditorPage/types'
import {
  MouseMovementHandler,
  PillPositionAndSize,
  TimelinePill,
} from '../TimelinePill/TimelinePill'
import { TooltipBody } from '../Tooltip/TooltipBody'

export interface CaseTimelinePillProps {
  calculatePillPositionAndSize: (
    startTime: DateTime,
    endTime: DateTime,
    minWidth?: number
  ) => PillPositionAndSize
  caseObj: Case
  isHovered: boolean
  onClick?: () => void
  onMouseEnter?: MouseMovementHandler
  onMouseLeave?: MouseMovementHandler
  tooltipPlacement?: TooltipProps['placement']
}

export const CaseTimelinePill = React.memo(function CaseTimelinePill({
  caseObj,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  onClick,
  calculatePillPositionAndSize,
  tooltipPlacement = 'bottom',
}: CaseTimelinePillProps) {
  const { isTheaterMode } = useEventsEditorContext()

  const pillPositionAndSize = React.useMemo(
    () =>
      calculatePillPositionAndSize(
        caseObj.scheduledStartTime,
        caseObj.scheduledEndTime
      ),
    [
      calculatePillPositionAndSize,
      caseObj.scheduledStartTime,
      caseObj.scheduledEndTime,
    ]
  )
  if (pillPositionAndSize.widthPx === 0) return null

  return (
    <TimelinePill
      id={caseObj.id}
      isHovered={isHovered}
      pillPositionAndSize={pillPositionAndSize}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
      tooltipBody={<CaseTooltipBody caseObj={caseObj} />}
      displayText={
        <CaseTooltipDisplayText
          caseObj={caseObj}
          isTheaterMode={isTheaterMode}
        />
      }
      tooltipPlacement={tooltipPlacement}
    />
  )
})

const CaseTooltipBody = React.memo(function CaseTooltipBody({
  caseObj,
}: {
  caseObj: Case
}) {
  return (
    <TooltipBody
      title={
        caseObj.staff.length
          ? `${
              caseObj.staff[0].lastName
            }, ${caseObj.scheduledStartTime.toFormat('TTT')}`
          : caseObj.scheduledStartTime.toFormat('TTT')
      }
      body={
        caseObj.procedures.length
          ? caseObj.procedures.join(', ')
          : 'Scheduled Case'
      }
    />
  )
})

const CaseTooltipDisplayText = React.memo(function CaseTooltipDisplayText({
  caseObj,
  isTheaterMode = false,
}: {
  caseObj: Case
  isTheaterMode?: boolean
}) {
  const startTimeStr = caseObj.scheduledStartTime.toFormat('TTT')

  const caseText = caseObj.staff.length
    ? `${caseObj.staff
        .map((s) => `${s.lastName}, ${s.firstName}`)
        .join('; ')}, ${startTimeStr}`
    : startTimeStr
  const caseProcedure = caseObj.procedures.length
    ? caseObj.procedures.join(', ')
    : 'Scheduled Case'

  return (
    <React.Fragment>
      <Caps3Bold>
        {caseText}
        {isTheaterMode && (
          <Caps3 style={{ marginLeft: remSpacing.xsmall }}>
            {caseProcedure}
          </Caps3>
        )}
      </Caps3Bold>
      {!isTheaterMode && (
        <>
          <br />
          <Caps3>{caseProcedure}</Caps3>
        </>
      )}
    </React.Fragment>
  )
})
