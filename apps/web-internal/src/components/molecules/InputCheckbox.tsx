import React, { useId } from 'react'

import { useTheme } from '@emotion/react'

import { useField } from 'formik'

import { remSpacing, Span3 } from '@apella/component-library'

/** TODO: reconcile component w/component-library version */
export const InputCheckbox = ({
  label,
  name,
  hint,
  className,
  children,
  ...props
}: React.PropsWithChildren<{
  label: string
  hint?: string
  name: string
  checked?: boolean
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  className?: string
}>) => {
  const theme = useTheme()
  const [{ onChange: fieldOnChange, ...field }] = useField({
    name,
    type: 'checkbox',
  })

  const id = useId()

  return (
    <div className={className}>
      <div
        css={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          gap: remSpacing.xxsmall,
        }}
      >
        <input
          id={id}
          type="checkbox"
          {...field}
          {...props}
          onChange={(e) => {
            fieldOnChange(e)
            props.onChange?.(e)
          }}
        />
        <label htmlFor={id}>
          {children ?? (
            <Span3 color={theme.palette.text.secondary}>{label}</Span3>
          )}
        </label>
      </div>
      <Span3 color={theme.palette.text.tertiary}>{hint}</Span3>
    </div>
  )
}
