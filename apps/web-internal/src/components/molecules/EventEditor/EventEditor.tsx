import {
  Dispatch,
  memo,
  SetStateAction,
  useCallback,
  useContext,
  useMemo,
} from 'react'

import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime } from 'luxon'
import { rem } from 'polished'

import {
  Button,
  Caps2,
  Col,
  Delete,
  FlexContainer,
  FlexItem,
  InputText,
  Option,
  pxSpacing,
  remSpacing,
  Row,
  Save,
  SingleSelect,
} from '@apella/component-library'
import { ColorBar, EventFrame } from 'src/components/atoms/EventFrame'
import { HR } from 'src/components/atoms/HR'
import { LocaleContext } from 'src/contexts/locale'
import { useEventsEditorContext } from 'src/pages/EventsEditorPage/EventsEditorContext'
import {
  EventProtoTileProps,
  EventsEditorEvent,
  PersistedEvent,
} from 'src/pages/EventsEditorPage/types'
import { UNIDENTIFIED } from 'src/types/event'
import { EVENT_TYPES, logEvent } from 'src/utils/analyticsEvents'

import { EventTimestampsRow } from '../EventTile/EventTimestampsRow'
import { EventEditorAddDetailGroup } from './EventEditorAddDetailGroup'

interface EventEditorProps extends EventProtoTileProps {
  onExitEvent: () => void
  onRemove: () => void
  onSave: () => void
  onToggleSyncPlaybackToEventTime: () => void
  setEventFrameRef?: Dispatch<SetStateAction<Element | null>>
  verified?: boolean
}

export const ColorBlock = styled.span`
  display: inline-block;
  background: ${(prop) => prop.color};
  border-radius: 2px;
  width: 10px;
  height: 10px;
  margin-right: 8px;
`

export const EventEditor = memo(function EventEditor({
  event,
  isSaved,
  color,
  syncedWithPlayback,
  verified,
  onExitEvent,
  onToggleSyncPlaybackToEventTime,
  onRemove,
  onSave,
  setEventFrameRef,
}: EventEditorProps): React.JSX.Element {
  const theme = useTheme()

  const { reviewerTimestamps: isReviewerTimestampsEnabled } =
    useFlags<WebInternalFeatureFlagSet>()

  const {
    annotationTask,
    organizationId,
    siteId,
    roomId,
    state: { eventTypes },
    dispatch,
  } = useEventsEditorContext()
  const locale = useContext(LocaleContext)

  const startTime = event.startTime
  const labels = event.labels ?? []
  const name = event.name
  const notes = event.notes ?? undefined

  const startDateTime = DateTime.fromISO(startTime)

  const getHighlightEditorParams = useCallback(
    (dateKey: string) =>
      new URLSearchParams({
        organizationId: organizationId,
        siteId: siteId,
        [dateKey]: startDateTime ? startDateTime.setZone(locale).toISO() : '',
        roomId: roomId,
      }),
    [organizationId, siteId, startDateTime, locale, roomId]
  )

  const onChange = useCallback(
    (event: EventsEditorEvent) => {
      dispatch({ type: 'UPDATE_EVENT', event })
    },
    [dispatch]
  )

  const eventTypeOptions = useMemo(
    () =>
      eventTypes.map<React.JSX.Element | undefined>((eventType) => {
        if (eventType.hidden && !annotationTask) return

        const label = eventType.name ?? UNIDENTIFIED

        return (
          <Option key={eventType.id} value={eventType.id} label={label}>
            <ColorBlock color={eventType.color} /> {label}
          </Option>
        )
      }),
    [annotationTask, eventTypes]
  )

  return (
    <EventFrame
      background={theme.palette.blue.background}
      colorBar={
        <ColorBar
          outline={theme.palette.blue[50]}
          color={color}
          isSaved={!!isSaved}
          onClick={onExitEvent}
        />
      }
      setRef={setEventFrameRef}
    >
      <Row css={{ width: '100%', maxWidth: rem('400px') }}>
        {isReviewerTimestampsEnabled && (
          <EventTimestampsRow
            css={{ marginBottom: remSpacing.small }}
            event={event}
          />
        )}
        <Col xs={12}>
          <label htmlFor="start">
            <Caps2>Time</Caps2>
          </label>
          <FlexContainer alignItems={'center'}>
            <FlexItem grow>
              <InputText
                name={'start'}
                readOnly
                value={
                  startDateTime !== undefined
                    ? startDateTime
                        .setZone(locale)
                        .toLocaleString(DateTime.TIME_24_WITH_SHORT_OFFSET)
                    : ''
                }
              />
            </FlexItem>
            <FlexItem css={{ marginLeft: '1rem' }}>
              {syncedWithPlayback ? (
                <Button
                  css={{ width: rem('60px') }}
                  color="error"
                  appearance="secondary"
                  onClick={onToggleSyncPlaybackToEventTime}
                >
                  stop
                </Button>
              ) : (
                <Button
                  css={{ width: rem('60px') }}
                  color="primary"
                  appearance="secondary"
                  onClick={onToggleSyncPlaybackToEventTime}
                >
                  edit
                </Button>
              )}
            </FlexItem>
          </FlexContainer>
          <HR />

          <Row>
            <label htmlFor="eventName">
              <Caps2>Name</Caps2>
            </label>
          </Row>
          <Row>
            <Col sm={12} css={{ margin: `${remSpacing.xsmall} 0` }}>
              <SingleSelect
                defaultOpen={!name}
                id="eventName"
                name="eventName"
                label={'Add an event name'}
                css={{ width: '100%' }}
                value={name}
                search
                onChange={(name) =>
                  name != undefined && onChange({ ...event, name })
                }
              >
                {eventTypeOptions}
              </SingleSelect>
            </Col>
          </Row>
          <HR />

          <EventEditorAddDetailGroup
            labels={labels}
            notes={notes}
            getHighlightEditorParams={getHighlightEditorParams}
            onChangeLabels={(labels) => onChange({ ...event, labels })}
            onChangeNotes={(notes) => onChange({ ...event, notes })}
            onExitEvent={onExitEvent}
          />

          <Row css={{ justifyContent: 'flex-end', gap: pxSpacing.xsmall }}>
            {'historyEvents' in event && (
              <ResetToPredictionButton event={event} onClick={onExitEvent} />
            )}
            <Col>
              <Button
                title="Delete Event"
                size="md"
                color="error"
                appearance="secondary"
                buttonType="icon"
                onClick={onRemove}
              >
                <Delete size="sm" css={{ display: 'flex' }} />
              </Button>
            </Col>
            <Col>
              <Button size="md" color="primary" onClick={onSave}>
                <Save size="sm" />
                {verified ? 'Save' : 'Verify & Save'}
              </Button>
            </Col>
          </Row>
        </Col>
      </Row>
    </EventFrame>
  )
})

const ResetToPredictionButton = ({
  event,
  onClick,
}: {
  event: PersistedEvent
  onClick: () => void
}) => {
  const { siteId, roomId, dispatch } = useEventsEditorContext()

  const onReset = useCallback(() => {
    if (confirm('Are you sure you want to reset this event to prediction?')) {
      logEvent(EVENT_TYPES.RESET_EVENT, {
        siteId,
        roomId,
        eventId: event.id,
      })

      dispatch({
        type: 'UPDATE_EVENT',
        event: { ...event, ...event.prediction },
      })

      onClick()
    }
  }, [siteId, roomId, event, dispatch, onClick])

  if (
    event.prediction === undefined ||
    (event.prediction.name === event.name &&
      event.prediction.startDateTime.equals(DateTime.fromISO(event.startTime)))
  )
    return null

  return (
    <Col>
      <Button onClick={onReset} color="black" size="md" appearance="link">
        Reset to prediction
      </Button>
    </Col>
  )
}
