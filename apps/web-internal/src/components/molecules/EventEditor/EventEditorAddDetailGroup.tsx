import { memo, useState } from 'react'

import { useTheme } from '@emotion/react'

import {
  Assistant,
  Button,
  ButtonLink,
  Caps2,
  Col,
  FlexContainer,
  InputText,
  MultiSelect,
  Option,
  Plus,
  remSpacing,
  Row,
} from '@apella/component-library'
import { useEventLabelOptions } from 'src/hooks/useEventLabelOptions'
import { LocationPath } from 'src/router/LocationPath'

interface EventEditorAddDetailGroupProps {
  getHighlightEditorParams: (dateKey: string) => URLSearchParams
  labels: (string | null)[]
  notes?: string
  onChangeLabels: (labels: string[]) => void
  onChangeNotes: (notes: string) => void
  onExitEvent: () => void
}

const HR = () => {
  const theme = useTheme()
  return <hr css={{ border: `1px solid ${theme.palette.gray[20]}` }} />
}

export const EventEditorAddDetailGroup = memo(
  function EventEditorAddDetailGroup({
    labels,
    notes,
    getHighlightEditorParams,
    onChangeLabels,
    onChangeNotes,
    onExitEvent,
  }: EventEditorAddDetailGroupProps): React.JSX.Element {
    const [showLabels, setShowLabels] = useState(labels.length > 0)
    const [showHighlights, setShowHighlights] = useState(false)
    const [showNotes, setShowNotes] = useState(notes && notes.trim() !== '')
    const [notesAutofocus, setNotesAutofocus] = useState(false)

    const { eventLabelOptions } = useEventLabelOptions()

    const EventLabelOptions = eventLabelOptions.map(({ name: uiLabel }) => (
      <Option key={uiLabel} value={uiLabel} label={uiLabel} />
    ))

    return (
      <>
        {showLabels && (
          <>
            <Row>
              <label htmlFor="labels">
                <Caps2>Labels</Caps2>
              </label>
            </Row>
            <Row>
              {/* TODO: move margin elsewhere? */}
              <Col css={{ margin: `${remSpacing.xsmall} 0` }} sm={12}>
                <MultiSelect
                  id="labels"
                  name="labels"
                  defaultOpen={labels.length === 0}
                  css={{ width: '100%' }}
                  search
                  value={
                    labels
                      ? labels.filter(
                          (label): label is string => label != undefined
                        )
                      : labels
                  }
                  label={'Add a label'}
                  onChange={(labels) => {
                    // Their docs and types are wrong
                    onChangeLabels(labels ?? [])
                  }}
                >
                  {EventLabelOptions}
                </MultiSelect>
              </Col>
            </Row>
            <HR />
          </>
        )}

        {showNotes && (
          <>
            <Row>
              <label htmlFor="notes">
                <Caps2>Notes</Caps2>
              </label>
            </Row>
            <InputText
              name={'notes'}
              autofocus={notesAutofocus}
              placeholder="Write something here..."
              multiline
              value={notes}
              rows={3}
              onChange={(e) => onChangeNotes(e.target.value)}
            />
            <HR />
          </>
        )}
        {showHighlights && (
          <>
            <Caps2 as="h5">Create highlight with this event</Caps2>
            <Row>
              <Col css={{ margin: `${remSpacing.xsmall} 0` }} xs={12}>
                <ButtonLink
                  to={`${
                    LocationPath.HighlightsNew
                  }?${getHighlightEditorParams('startDate').toString()}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  color="alternate"
                  size="md"
                  onClick={onExitEvent}
                >
                  <Assistant color="black" size="sm" />
                  Start
                </ButtonLink>
                <ButtonLink
                  css={{ marginLeft: remSpacing.xxsmall }}
                  to={`${
                    LocationPath.HighlightsNew
                  }?${getHighlightEditorParams('endDate').toString()}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  color="alternate"
                  size="md"
                  onClick={onExitEvent}
                >
                  <Assistant color="black" size="sm" />
                  End
                </ButtonLink>
              </Col>
            </Row>
            <HR />
          </>
        )}

        {(!showLabels || !showNotes || !showHighlights) && (
          <>
            <FlexContainer css={{ flexWrap: 'wrap', gap: remSpacing.xsmall }}>
              {!showLabels && (
                <AddDetailButton
                  onClick={() => setShowLabels(true)}
                  label="Add labels"
                />
              )}

              {!showNotes && (
                <AddDetailButton
                  onClick={() => {
                    setShowNotes(true)
                    setNotesAutofocus(true)
                  }}
                  label="Add notes"
                />
              )}

              {!showHighlights && (
                <AddDetailButton
                  onClick={() => setShowHighlights(true)}
                  label="Add highlights"
                />
              )}
            </FlexContainer>
            <HR />
          </>
        )}
      </>
    )
  }
)

const AddDetailButton = ({
  onClick,
  label,
}: {
  onClick: React.ComponentProps<typeof Button>['onClick']
  label: string
}) => (
  <Button size="sm" color="primary" appearance="secondary" onClick={onClick}>
    <Plus size="xs" />
    {label}
  </Button>
)
