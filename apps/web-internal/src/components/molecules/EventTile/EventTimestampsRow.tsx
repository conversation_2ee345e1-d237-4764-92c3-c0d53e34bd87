import { useContext, Fragment } from 'react'

import { useTheme } from '@emotion/react'

import { offset } from '@floating-ui/react'
import { DateTime } from 'luxon'

import {
  H6,
  Span4,
  Tooltip,
  pxSpacing,
  remSpacing,
} from '@apella/component-library'
import { LocaleContext } from 'src/contexts/locale'
import { useEventsEditorContext } from 'src/pages/EventsEditorPage/EventsEditorContext'
import { EventsEditorEvent } from 'src/pages/EventsEditorPage/types'

import { CameraIndicator } from './CameraIndicator'
import { useEventStatus } from './getEventStatus'

interface TimestampProps {
  color?: string
  label: string
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void
  value: DateTime
}

export const Timestamp = ({ value, label, color, onClick }: TimestampProps) => {
  const locale = useContext(LocaleContext)
  const theme = useTheme()

  return (
    <Tooltip
      middleware={[offset(6)]}
      body={<Span4 color={theme.palette.gray[10]}>{label}</Span4>}
      gutter={remSpacing.xsmall}
      css={{ background: theme.palette.gray[60], opacity: 0.9 }}
    >
      {onClick ? (
        <button
          type="button"
          css={{
            background: 'none',
            color: 'inherit',
            border: 'none',
            padding: 0,
            font: 'inherit',
            cursor: 'pointer',
            '&:focus': { outline: `2px solid ${theme.palette.blue[50]}` },
          }}
          onClick={onClick}
        >
          <H6
            as="span"
            css={{
              color,
            }}
          >
            {value
              .setZone(locale)
              .toLocaleString(DateTime.TIME_24_WITH_SHORT_OFFSET)}
          </H6>
        </button>
      ) : (
        <H6
          as="span"
          css={{
            color,
          }}
        >
          {value
            .setZone(locale)
            .toLocaleString(DateTime.TIME_24_WITH_SHORT_OFFSET)}
        </H6>
      )}
    </Tooltip>
  )
}

export const EventTimestampsRow = ({
  className,
  event,
  onClick = () => {},
}: {
  className?: string
  event: EventsEditorEvent
  onClick?: () => void
}) => {
  return (
    <div
      className={className}
      css={{ display: 'flex', alignItems: 'center', gap: pxSpacing.xsmall }}
    >
      <EventTimestamps event={event} onClick={onClick} />

      <CameraIndicator event={event} />
    </div>
  )
}

const EventTimestamps = ({
  event,
  onClick = () => {},
}: {
  event: EventsEditorEvent
  onClick?: () => void
}) => {
  const theme = useTheme()
  const timestamps = []

  const { seekToTime } = useEventsEditorContext()
  const status = useEventStatus(event)

  let predicted: DateTime | undefined = undefined
  let lastAnnotated: DateTime | undefined = undefined
  let lastReviewed: DateTime | undefined = undefined
  const eventTime = DateTime.fromISO(event.startTime)

  if ('historyEvents' in event) {
    predicted = event.prediction?.startDateTime
    lastAnnotated = event.lastAnnotatedEvent?.startDateTime
    lastReviewed = event.lastReviewedEvent?.startDateTime
  }

  if (predicted && !predicted.equals(eventTime)) {
    timestamps.push({
      color: theme.palette.text.tertiary,
      label: 'Predicted time',
      value: predicted,
    })
  }

  if (lastAnnotated) {
    timestamps.push({
      color: theme.palette.blue[60],
      label: 'Annotated time',
      value: lastAnnotated,
    })
  }

  if (lastReviewed && (!lastAnnotated || !lastReviewed.equals(lastAnnotated))) {
    timestamps.push({
      color: theme.palette.red[60],
      label: 'Reviewed time',
      value: lastReviewed,
    })
  }

  if (
    timestamps.length === 0 ||
    !timestamps[timestamps.length - 1].value.equals(eventTime)
  ) {
    timestamps.push({
      label: status === 'VERIFIED' ? 'Verified time' : 'Event time',
      value: eventTime,
    })
  }

  return (
    <div
      css={{ display: 'flex', alignItems: 'center', gap: pxSpacing.xxsmall }}
    >
      {timestamps.map((timestamp, idx) => (
        <Fragment key={timestamp.label}>
          <Timestamp
            {...timestamp}
            onClick={(e) => {
              e.stopPropagation()
              onClick()
              seekToTime(timestamp.value)
            }}
          />
          {timestamps.length - 1 !== idx && <>&rarr;</>}
        </Fragment>
      ))}
    </div>
  )
}
