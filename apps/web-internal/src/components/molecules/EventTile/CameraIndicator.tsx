import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { Camera, P4, remSpacing } from '@apella/component-library'
import { useEventsEditorContext } from 'src/pages/EventsEditorPage/EventsEditorContext'
import { EventsEditorEvent } from 'src/pages/EventsEditorPage/types'

import { CameraTile } from './EventTile'

export const CameraIndicator = ({ event }: { event: EventsEditorEvent }) => {
  const { cameras } = useEventsEditorContext()
  const theme = useTheme()

  const cameraName = useMemo(
    () => cameras?.find((c) => c.id === event.cameraId)?.shortName,
    [cameras, event.cameraId]
  )

  if (!cameraName) return null

  return (
    <CameraTile
      css={{
        display: 'flex',
        flexDirection: 'row',
        padding: '4px 8px',
        gap: 2,
        color: theme.palette.gray[50],
      }}
    >
      <Camera size="xs" />
      <P4
        css={{
          verticalAlign: 'sub',
          lineHeight: remSpacing.xxsmall,
          paddingTop: remSpacing.xsmall,
          color: theme.palette.gray[70],
        }}
      >
        {cameraName}
      </P4>
    </CameraTile>
  )
}
