import { useMemo } from 'react'

import { EventsEditorEvent } from 'src/pages/EventsEditorPage/types'
import { EventSourceType } from 'src/types/event'

export type EventStatus = 'VERIFIED' | 'REMOVED' | 'PREDICTION'

export const getEventStatus = (event: EventsEditorEvent): EventStatus =>
  event.deletedAt
    ? 'REMOVED'
    : event.sourceType === EventSourceType.Human.toString()
      ? 'VERIFIED'
      : 'PREDICTION'

export const useEventStatus = (event: EventsEditorEvent): EventStatus =>
  useMemo(() => getEventStatus(event), [event])
