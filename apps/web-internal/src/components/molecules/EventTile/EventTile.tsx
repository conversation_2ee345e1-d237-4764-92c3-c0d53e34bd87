import { Dispatch, memo, MouseEvent, SetStateAction, useMemo } from 'react'

import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime } from 'luxon'

import {
  Button,
  Cancel,
  Check,
  CheckCircle,
  Col,
  Edit,
  P3,
  remSpacing,
  Row,
  shape,
} from '@apella/component-library'
import { ColorBar, EventFrame } from 'src/components/atoms/EventFrame'
import { useEventTypes } from 'src/hooks/useEventTypes'
import { useEventsEditorContext } from 'src/pages/EventsEditorPage/EventsEditorContext'
import {
  EventHistory,
  EventProtoTileProps,
} from 'src/pages/EventsEditorPage/types'
import { UNIDENTIFIED } from 'src/types/event'

import { CameraIndicator } from './CameraIndicator'
import { EventTimestampsRow, Timestamp } from './EventTimestampsRow'
import { useEventStatus } from './getEventStatus'

interface EventTileProps extends EventProtoTileProps {
  isSelected?: boolean
  lastAnnotatedEvent?: EventHistory
  lastReviewedEvent?: EventHistory
  onEditTimestamp?: () => void
  onEnterEvent?: () => void
  onSave?: () => void
  setEventFrameRef?: Dispatch<SetStateAction<Element | null>>
}

export const CameraTile = styled.div((props) => ({
  backgroundColor: props.theme.palette.gray[10],
  borderRadius: shape.borderRadius.xxsmall,
}))

export const EventTile = memo(function EventTile({
  isSelected = false,
  event,
  isSaved = false,
  color,
  syncedWithPlayback,
  onEditTimestamp,
  onEnterEvent,
  onSave,
  setEventFrameRef,
}: EventTileProps): React.JSX.Element {
  const theme = useTheme()
  const { dispatch } = useEventsEditorContext()
  const { eventTypes } = useEventTypes()
  const { reviewerTimestamps: isReviewerTimestampsEnabled } =
    useFlags<WebInternalFeatureFlagSet>()

  const labels = event.labels ?? []
  const name =
    eventTypes.find((et) => et.id === event.name)?.name ?? UNIDENTIFIED
  const notes = event.notes ?? undefined

  const startDateTime = DateTime.fromISO(event.startTime)
  const predictedStartTime =
    'historyEvents' in event ? event.prediction?.startDateTime : undefined

  const status = useEventStatus(event)

  const isEventChangedFromPrediction: boolean = useMemo(() => {
    return !predictedStartTime?.equals(startDateTime)
  }, [predictedStartTime, startDateTime])

  return (
    <EventFrame
      background={
        status === 'REMOVED'
          ? isSelected
            ? theme.palette.red[10]
            : theme.palette.red.background
          : isSelected
            ? theme.palette.blue[10]
            : undefined
      }
      onClick={onEnterEvent}
      colorBar={
        <ColorBar
          color={color}
          outline={
            isSelected
              ? status === 'REMOVED'
                ? theme.palette.red[50]
                : theme.palette.blue[50]
              : undefined
          }
          isSaved={isSaved}
        />
      }
      setRef={isSelected ? setEventFrameRef : undefined}
    >
      <Col css={{ flexShrink: 1, minWidth: '25%' }}>
        <Row css={{ alignItems: 'center', gap: 4 }}>
          {isReviewerTimestampsEnabled ? (
            <EventTimestampsRow
              event={event}
              onClick={() => {
                dispatch({ type: 'UNSELECT_EVENT' })
              }}
            />
          ) : (
            <>
              {predictedStartTime && isEventChangedFromPrediction && (
                <>
                  <Timestamp
                    label="predicted time"
                    color={theme.palette.text.tertiary}
                    value={predictedStartTime}
                  />
                  &rarr;
                </>
              )}

              <Timestamp
                label={status === 'VERIFIED' ? 'verified time' : 'event time'}
                value={startDateTime}
              />

              <CameraIndicator event={event} />
            </>
          )}
        </Row>
        <Row>
          <P3
            css={{
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              display: 'block',
            }}
          >
            {name}
          </P3>
        </Row>
        {labels.length === 0 ? null : (
          <Row>
            <P3 css={{ color: theme.palette.text.tertiary }}>
              {labels?.join(', ')}
            </P3>
          </Row>
        )}
        <Row>
          <P3
            css={{
              color: theme.palette.text.tertiary,
              fontStyle: 'italic',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              display: 'block',
            }}
          >
            {notes}
          </P3>
        </Row>
      </Col>
      <Col
        css={{
          display: 'flex',
          height: '100%',
          alignItems: 'center',
          marginLeft: remSpacing.xsmall,
        }}
      >
        {isSelected && syncedWithPlayback && onSave ? (
          <Button
            color="primary"
            appearance="secondary"
            onClick={(e) => {
              e.stopPropagation()
              onSave()
            }}
          >
            Save
          </Button>
        ) : (
          <>
            {onEditTimestamp && isSelected && (
              <Button
                title="Edit Event Time"
                appearance="link"
                buttonType="icon"
                color="black"
                onClick={(e: MouseEvent) => {
                  e.stopPropagation()
                  onEditTimestamp?.()
                }}
              >
                <Edit size="sm" />
              </Button>
            )}
            {status === 'VERIFIED' ? (
              <IconContainer title="Verified">
                <CheckCircle color="green" size="sm" />
              </IconContainer>
            ) : status === 'REMOVED' ? (
              <IconContainer title="Verified">
                <Cancel color="red" size="sm" />
              </IconContainer>
            ) : (
              <Button
                title="Verify Event"
                appearance="link"
                color="black"
                buttonType="icon"
                onClick={(e: MouseEvent) => {
                  e.stopPropagation()
                  onSave?.()
                }}
              >
                <Check size="sm" />
              </Button>
            )}
          </>
        )}
      </Col>
    </EventFrame>
  )
})

const IconContainer = styled.div({
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '10px',
})
