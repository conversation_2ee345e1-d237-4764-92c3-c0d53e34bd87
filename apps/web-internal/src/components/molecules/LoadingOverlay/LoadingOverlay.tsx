import { Progress, ProgressProps } from '@apella/component-library'

import { styles } from './styles'

interface LoadingOverlayProps
  extends Pick<ProgressProps, 'size'>,
    React.PropsWithChildren {
  alwaysShowChildren?: boolean
  isLoading: boolean
}

export const LoadingOverlay = ({
  isLoading,
  size = 'lg',
  alwaysShowChildren = false,
  children,
}: LoadingOverlayProps): React.JSX.Element => (
  <>
    {isLoading && (
      <div css={styles.container}>
        <Progress size={size} />
      </div>
    )}
    {(alwaysShowChildren || !isLoading) && children}
  </>
)
