import { useField } from 'formik'

import { Input, InputSingleSelect, Option } from '@apella/component-library'

interface IntervalInputProps
  extends Omit<
    React.ComponentProps<typeof Input.SingleSelect>,
    'multiple' | 'value' | 'children' | 'onChange'
  > {
  onChange?: (value?: number) => void
  value?: number
}

function IntervalSelect({ value, onChange, ...props }: IntervalInputProps) {
  return (
    <InputSingleSelect
      value={value?.toString()}
      onChange={(value?: string) => {
        if (onChange) {
          onChange(value === undefined ? value : parseInt(value, 10))
        }
      }}
      {...props}
    >
      {[1, 2, 3, 4, 6, 8, 12, 24].map((i) => {
        const hour = i.toString()

        return <Option key={hour} label={`${hour} hours`} value={hour} />
      })}
    </InputSingleSelect>
  )
}

export function IntervalInput(
  props: React.ComponentProps<typeof IntervalSelect>
) {
  const [{ ...field }, meta, { setValue }] = useField<number | undefined>({
    multiple: false,
    name: props.name,
  })

  const errorMessage = meta.error && meta.touched ? meta.error : ''

  return (
    <IntervalSelect
      {...field}
      onChange={(val) => setValue(val)}
      error={errorMessage}
      {...props}
    />
  )
}
