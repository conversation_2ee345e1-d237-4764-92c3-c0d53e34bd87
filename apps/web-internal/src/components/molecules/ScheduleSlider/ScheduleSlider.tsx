import React, { memo, useContext } from 'react'

import { DateTime } from 'luxon'

import { Size } from '@apella/hooks'
import { CaseTimelinePill } from 'src/components/molecules/CaseTimelinePill/CaseTimelinePill'
import { LocaleContext } from 'src/contexts/locale'
import { useEventsEditorContext } from 'src/pages/EventsEditorPage/EventsEditorContext'
import {
  ROW_HEIGHT,
  ROW_HEIGHT_THEATER,
} from 'src/pages/EventsEditorPage/Timeline/cssConsts'
import { Case } from 'src/pages/EventsEditorPage/types'

import {
  calculatePillPositionAndSizeHelper,
  MouseMovementHandler,
  PillPositionAndSize,
} from '../TimelinePill/TimelinePill'

interface ScheduleSliderProps {
  cases: Case[]
  endViewTime: DateTime
  startViewTime: DateTime
  timelineSize?: Size
}

export const ScheduleSlider = memo(function ScheduleSlider({
  cases,
  timelineSize,
  startViewTime,
  endViewTime,
}: ScheduleSliderProps): React.JSX.Element {
  const timezone = useContext(LocaleContext)
  const [hoveredId, setHoveredId] = React.useState<string>()
  const { isTheaterMode } = useEventsEditorContext()

  const calculatePillPositionAndSize = React.useCallback(
    (
      startTime: DateTime,
      endTime: DateTime,
      minWidth?: number,
      liveTime?: DateTime
    ): PillPositionAndSize => {
      if (!timelineSize?.width)
        return { left: '0', widthPct: '0%', widthPx: 0, forecastedWidthPx: 0 }

      const minHour = startViewTime.setZone(timezone)
      const maxHour = endViewTime.setZone(timezone)

      return calculatePillPositionAndSizeHelper(
        startTime,
        endTime,
        timelineSize.width,
        minHour,
        maxHour,
        minWidth,
        liveTime
      )
    },
    [timelineSize?.width, startViewTime, timezone, endViewTime]
  )

  const onMouseEnterPill: MouseMovementHandler = (id?: string) =>
    id && setHoveredId(id)
  const onMouseLeavePill: MouseMovementHandler = () => setHoveredId(undefined)

  return (
    <div
      css={{
        width: '100%',
        height: isTheaterMode ? ROW_HEIGHT_THEATER : ROW_HEIGHT,
        position: 'relative',
        cursor: 'pointer',
      }}
    >
      {cases.map((c) => (
        <CaseTimelinePill
          key={c.id}
          caseObj={c}
          isHovered={hoveredId === c.id}
          onMouseEnter={onMouseEnterPill}
          onMouseLeave={onMouseLeavePill}
          calculatePillPositionAndSize={calculatePillPositionAndSize}
        />
      ))}
    </div>
  )
})
