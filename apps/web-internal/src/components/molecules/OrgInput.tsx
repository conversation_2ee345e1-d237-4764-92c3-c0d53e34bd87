import { useMemo } from 'react'

import { gql, useQuery } from '@apollo/client'

import { Input, Option } from '@apella/component-library'

import { GetOrgs } from './__generated__'

const GET_ORGS = gql`
  query GetOrgs {
    organizations {
      edges {
        node {
          id
          name
        }
      }
    }
  }
`

const useOrgOptions = () => {
  const { data } = useQuery<GetOrgs>(GET_ORGS)
  return useMemo(
    () => data?.organizations.edges.flatMap(({ node }) => node) ?? [],
    [data?.organizations.edges]
  )
}

export function OrgInput({
  label = 'Organization',
  ...props
}: Omit<React.ComponentProps<typeof Input.SingleSelect>, 'children'>) {
  const items = useOrgOptions()
  return (
    <Input.SingleSelect label={label} {...props}>
      {items.map((item) => (
        <Option key={item.id} value={item.id} label={item.name} />
      ))}
    </Input.SingleSelect>
  )
}

export function OrgInputMultiple({
  label = 'Organizations',
  ...props
}: Omit<React.ComponentProps<typeof Input.MultiSelect>, 'children'>) {
  const items = useOrgOptions()
  return (
    <Input.MultiSelect label={label} {...props}>
      {items.map((item) => (
        <Option key={item.id} value={item.id} label={item.name} />
      ))}
    </Input.MultiSelect>
  )
}
