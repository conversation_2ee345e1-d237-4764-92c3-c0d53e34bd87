import { useMemo, useState } from 'react'
import { generatePath, useNavigate } from 'react-router'

import { DateTime } from 'luxon'

import {
  Button,
  DatePicker,
  Dialog,
  ExitToApp,
  FlexContainer,
  remSpacing,
} from '@apella/component-library'
import {
  GetOrgSiteRoomsOptionsComponent,
  OrgSiteRoomSelection,
} from 'src/components/OrgSiteRoomSelects'
import { LocationPath } from 'src/router/LocationPath'

export const VideoNavDialog = ({
  organizationId,
  siteId,
  date,
  roomId,
}: {
  organizationId?: string
  siteId?: string
  date?: DateTime
  roomId?: string
}) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        color="alternate"
        buttonType="icon"
      >
        <ExitToApp size="sm" />
      </Button>
      <Dialog title="Navigate to Video" isOpen={isOpen}>
        <VideoNavDialogContent
          organizationId={organizationId}
          siteId={siteId}
          roomId={roomId}
          date={date}
          onClose={() => {
            setIsOpen(false)
          }}
        />
      </Dialog>
    </>
  )
}

const VideoNavDialogContent = ({
  organizationId,
  siteId,
  roomId,
  date,
  onClose,
}: {
  organizationId?: string
  siteId?: string
  roomId?: string
  date?: DateTime
  onClose: () => void
}) => {
  const navigate = useNavigate()

  const [orgSiteRoomSelection, setOrgSiteRoomSelection] =
    useState<OrgSiteRoomSelection>({
      organization: organizationId ? { id: organizationId } : undefined,
      site: siteId ? { id: siteId } : undefined,
      room: roomId ? { id: roomId } : undefined,
    })

  const [dateSelection, setDateSelection] = useState<Date | undefined>(
    // The DatePicker expects local time, so we need to convert the date to local time, but keep the time the same
    date?.setZone(DateTime.local().zoneName, { keepLocalTime: true }).toJSDate()
  )

  const requiredFiltersSelected = useMemo(
    () => !!orgSiteRoomSelection.room && !!dateSelection,
    [dateSelection, orgSiteRoomSelection.room]
  )

  const onNavigate = () => {
    if (!requiredFiltersSelected) {
      return
    }

    onClose()

    if (
      !(
        orgSiteRoomSelection.organization?.id &&
        orgSiteRoomSelection.site?.id &&
        dateSelection &&
        orgSiteRoomSelection.room?.id
      )
    ) {
      return
    }

    const path = generatePath(LocationPath.EventsEditor, {
      organizationId: orgSiteRoomSelection.organization.id,
      siteId: orgSiteRoomSelection.site.id,
      roomId: orgSiteRoomSelection.room.id,
      // We need to use the timezone from the site, but still have the video start at midnight (keepLocalTime)
      date: DateTime.fromJSDate(dateSelection)
        .setZone(orgSiteRoomSelection.site.timezone, { keepLocalTime: true })
        .toISO(),
    })

    navigate(path)
  }

  return (
    <FlexContainer
      css={{
        flexDirection: 'column',
        gap: remSpacing.small,
      }}
    >
      <GetOrgSiteRoomsOptionsComponent
        siteSelection={orgSiteRoomSelection.site?.id}
        roomSelection={orgSiteRoomSelection.room?.id}
        organizationSelection={orgSiteRoomSelection.organization?.id}
        onChangeOrgSiteRoomSelection={setOrgSiteRoomSelection}
      />
      <DatePicker
        value={dateSelection}
        setValue={(newDate) => {
          !Array.isArray(newDate) && setDateSelection(newDate ?? undefined)
        }}
        selectRange={false}
        calendarProps={{
          maxDate: new Date(),
        }}
      />
      <FlexContainer
        css={{
          gap: remSpacing.small,
          justifyContent: 'flex-end',
        }}
      >
        <Button onClick={onClose} color="alternate" css={{}}>
          Cancel
        </Button>
        <Button
          disabled={!requiredFiltersSelected}
          color="success"
          onClick={() => {
            onNavigate()
          }}
        >
          Go <ExitToApp size="sm" />
        </Button>
      </FlexContainer>
    </FlexContainer>
  )
}
