import { useMemo } from 'react'

import { gql, useQuery } from '@apollo/client'

import { Input, Option } from '@apella/component-library'

import { GetSiteData } from './__generated__'

const GET_SITE_DATA = gql`
  query GetSiteData {
    organizations {
      edges {
        node {
          id
          name
          sites {
            edges {
              node {
                id
                name
              }
            }
          }
        }
      }
    }
  }
`

export function SitesInput({
  label = 'Sites',
  ...props
}: Omit<
  React.ComponentProps<typeof Input.MultiSelect>,
  'children'
>): React.JSX.Element {
  const { data } = useQuery<GetSiteData>(GET_SITE_DATA)

  const items = useMemo(
    () =>
      data?.organizations.edges.flatMap((org) =>
        org.node.sites.edges.flatMap((site) => ({
          ...site.node,
          group: org.node.name,
        }))
      ) ?? [],
    [data?.organizations.edges]
  )

  return (
    <Input.MultiSelect bulkSelect label={label} {...props}>
      {items.map((item) => (
        <Option
          key={item.id}
          value={item.id}
          label={item.name}
          group={item.group}
        />
      ))}
    </Input.MultiSelect>
  )
}
