import { ComponentProps, Fragment, PropsWithChildren, useEffect } from 'react'

import { gql, useQuery } from '@apollo/client'

import { Col, Option, SingleSelect } from '@apella/component-library'

import { GetOrgSiteRooms } from './__generated__'

type GetOrgSiteRooms_organizations_edges_node_sites_edges_node_rooms_edges =
  GetOrgSiteRooms['organizations']['edges'][number]['node']['sites']['edges'][number]['node']['rooms']['edges'][number]

const GET_ORG_SITE_ROOMS = gql`
  query GetOrgSiteRooms {
    organizations {
      edges {
        node {
          id
          name
          sites {
            edges {
              node {
                id
                name
                timezone
                rooms {
                  edges {
                    node {
                      id
                      name
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`

export interface OrgSiteRoomSelection {
  organization?: { id: string }
  room?: { id: string }
  site?: { id: string; timezone?: string }
}

export const GetOrgSiteRoomsOptionsComponent = ({
  siteSelection,
  roomSelection,
  organizationSelection,
  onChangeOrgSiteRoomSelection,
  disableSiteSelection,
  disableRoomSelection,
}: {
  siteSelection?: string
  roomSelection?: string
  organizationSelection?: string
  onChangeOrgSiteRoomSelection: (
    orgSiteRoomSelection: OrgSiteRoomSelection
  ) => void
  disableSiteSelection?: boolean
  disableRoomSelection?: boolean
}): React.JSX.Element => {
  const { data: filterData, loading } =
    useQuery<GetOrgSiteRooms>(GET_ORG_SITE_ROOMS)

  const organizations = filterData?.organizations.edges ?? []
  const sites = organizations.flatMap((org) => org.node.sites.edges)

  //This makes a filter operation on sites and rooms a little easier
  const selectedSite =
    sites?.find((site) => site.node.id === siteSelection) ||
    sites?.find((site) =>
      site.node.rooms.edges.find((room) => room.node.id === roomSelection)
    )

  const selectedOrganization =
    organizations?.find(
      (organization) => organization.node.id === organizationSelection
    ) ||
    organizations?.find((organization) =>
      organization.node.sites.edges.find(
        (site) => site.node.id === siteSelection
      )
    )

  // This ensures that users can only select valid Organization+Site+Room combinations
  const siteOptions = !!selectedOrganization
    ? selectedOrganization.node.sites.edges
    : sites

  const roomOptions = !!selectedSite
    ? selectedSite.node.rooms.edges
    : siteOptions.reduce(
        (rooms, site) => rooms.concat(site.node.rooms.edges),
        [] as GetOrgSiteRooms_organizations_edges_node_sites_edges_node_rooms_edges[]
      )

  const selectedRoom = roomOptions?.find(
    (room) => room.node.id === roomSelection
  )

  // If any of the selected options changed from the last render, update the orgSiteRoomSelection
  useEffect(() => {
    !loading &&
      onChangeOrgSiteRoomSelection({
        organization: selectedOrganization?.node,
        site: selectedSite?.node,
        room: selectedRoom?.node,
      })
  }, [
    selectedOrganization?.node,
    selectedRoom?.node,
    selectedSite?.node,
    onChangeOrgSiteRoomSelection,
    loading,
  ])

  // This prevents us from sending nonsensical id filter combinations to graphql, if the org changes, then reset the site and room
  const handleChangeOrganizationSelection = (organizationId?: string) => {
    if (organizationId !== organizationSelection) {
      onChangeOrgSiteRoomSelection({
        organization: organizations?.find(
          (organization) => organization.node.id === organizationId
        )?.node,
        site: undefined,
        room: undefined,
      })
    }
  }

  // Same here, if the site changes, reset the room
  const handleChangeSiteSelection = (siteId?: string) => {
    if (siteId !== siteSelection) {
      onChangeOrgSiteRoomSelection({
        organization: organizations?.find((organization) =>
          organization.node.sites.edges.find((site) => site.node.id === siteId)
        )?.node,
        site: sites?.find((site) => site.node.id === siteId)?.node,
        room: undefined,
      })
    }
  }

  // If the room changes, just update the room
  const handleChangeRoomSelection = (roomId?: string) => {
    onChangeOrgSiteRoomSelection({
      organization: selectedOrganization?.node,
      site: selectedSite?.node,
      room: roomOptions?.find((room) => room.node.id === roomId)?.node,
    })
  }

  return (
    <Fragment>
      <Col>
        <OrgSiteRoomSelect
          name={'dropdown-ORGANIZATION'}
          label={'Select an organization'}
          value={selectedOrganization?.node.id}
          onChange={handleChangeOrganizationSelection}
          loading={loading}
        >
          {organizations.map((organization) => (
            <Option
              key={organization.node.id}
              value={organization.node.id}
              label={organization.node.name}
            />
          ))}
        </OrgSiteRoomSelect>
      </Col>
      {disableSiteSelection ? null : (
        <Col>
          <OrgSiteRoomSelect
            name={'dropdown-SITE'}
            label={'Select a site'}
            value={selectedSite?.node.id}
            onChange={handleChangeSiteSelection}
            loading={loading}
          >
            {siteOptions.map((site) => (
              <Option
                key={site.node.id}
                value={site.node.id}
                label={site.node.name}
              />
            ))}
          </OrgSiteRoomSelect>
        </Col>
      )}
      {disableRoomSelection ? null : (
        <Col>
          <OrgSiteRoomSelect
            name={'dropdown-OR'}
            label={'Select an operating room'}
            value={roomSelection}
            onChange={handleChangeRoomSelection}
            loading={loading}
          >
            {roomOptions.map((room) => (
              <Option
                key={room.node.id}
                value={room.node.id}
                label={room.node.name}
              />
            ))}
          </OrgSiteRoomSelect>
        </Col>
      )}
    </Fragment>
  )
}

const LOADING_TEXT = 'Loading...'

type OrgSiteRomSelectProps = PropsWithChildren<
  Pick<
    ComponentProps<typeof SingleSelect>,
    'name' | 'label' | 'value' | 'onChange'
  > & { loading: boolean }
>

const OrgSiteRoomSelect = ({
  name,
  label,
  value,
  onChange,
  loading,
  children,
}: OrgSiteRomSelectProps) => {
  return (
    <SingleSelect
      name={name}
      label={loading ? LOADING_TEXT : label}
      value={value}
      onChange={onChange}
      search
      readOnly={loading}
    >
      {children}
    </SingleSelect>
  )
}
