import { useMemo } from 'react'

import { useQuery } from '@apollo/client'

import { GetEventTypeDashboardVisibility, GetEventTypes } from './__generated__'
import { GET_EVENT_DASHBOARD_VISIBILITY, GET_EVENT_TYPES } from './queries'

export function useEventTypes() {
  const { data, ...useQueryResult } = useQuery<GetEventTypes>(GET_EVENT_TYPES)

  const eventTypes = useMemo(() => {
    return (
      data?.eventTypes?.edges?.map((e) => ({
        ...e.node,
      })) ?? []
    )
  }, [data?.eventTypes])

  return { ...useQueryResult, eventTypes }
}

export function useEventDashboardVisibility() {
  const { data, ...useQueryResult } = useQuery<GetEventTypeDashboardVisibility>(
    GET_EVENT_DASHBOARD_VISIBILITY
  )

  return {
    ...useQueryResult,
    eventDashboardVisibility: data?.dashboardEvents ?? [],
  }
}
