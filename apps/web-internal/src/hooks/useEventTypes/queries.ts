import { gql } from '@apollo/client'

export const GET_EVENT_TYPES = gql`
  query GetEventTypes {
    eventTypes {
      edges {
        node {
          id
          type
          name
          description
          color
          hidden
        }
      }
    }
  }
`

export const GET_EVENT_DASHBOARD_VISIBILITY = gql`
  query GetEventTypeDashboardVisibility {
    dashboardEvents {
      eventTypeId
      orgIdFilter
    }
  }
`
