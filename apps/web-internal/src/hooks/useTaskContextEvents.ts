import { useMemo } from 'react'

import { gql, useQuery } from '@apollo/client'

import { EVENT_FRAGMENT } from 'src/pages/EventsEditorPage/queries'

import {
  GetTaskContextEvents,
  GetTaskContextEventsVariables,
} from './__generated__'

const GET_CONTEXT_EVENTS = gql`
  ${EVENT_FRAGMENT}
  query GetTaskContextEvents($id: ID!) {
    annotationTask(id: $id) {
      id
      contextEvents {
        ...EventFragment
      }
    }
  }
`

export function useTaskContextEvents(annotationTaskId?: string) {
  const { data: taskData } = useQuery<
    GetTaskContextEvents,
    GetTaskContextEventsVariables
  >(GET_CONTEXT_EVENTS, {
    skip: !annotationTaskId,
    variables: {
      id: annotationTaskId ?? '',
    },
  })

  return useMemo(() => {
    return taskData?.annotationTask?.contextEvents ?? []
  }, [taskData?.annotationTask?.contextEvents])
}
