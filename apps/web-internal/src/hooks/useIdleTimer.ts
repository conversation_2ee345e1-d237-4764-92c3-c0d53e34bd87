import { useEffect, useRef } from 'react'

const events = ['load', 'mousemove', 'mousedown', 'click', 'scroll', 'keypress']

/**
 * Given a callback function and a timeout, this hook will call the callback
 * if no activity is detected after the timeout.
 */
export function useIdleTimer(onIdle: () => void, timeoutMs = 1000 * 30) {
  const timeoutId = useRef<number | undefined>(undefined)
  const onIdleRef = useRef(onIdle)

  useEffect(() => {
    onIdleRef.current = onIdle
  }, [onIdle])

  useEffect(() => {
    const resetTimeout = () => {
      window.clearTimeout(timeoutId.current)

      timeoutId.current = window.setTimeout(() => {
        onIdleRef.current()
      }, timeoutMs)
    }

    timeoutId.current = window.setTimeout(() => {
      onIdleRef.current()
    }, timeoutMs)

    for (const event of events) {
      window.addEventListener(event, resetTimeout)
    }

    return () => {
      for (const event of events) {
        window.removeEventListener(event, resetTimeout)
      }

      window.clearTimeout(timeoutId.current)
    }
  }, [timeoutMs])
}

/** Component version of useIdleTimer. Useful when conditionally applying timer */
export const IdleTimer = ({
  onIdle,
  timeoutMs = 1000 * 30,
}: {
  onIdle: () => void
  timeoutMs?: number
}) => {
  useIdleTimer(onIdle, timeoutMs)

  return null
}
