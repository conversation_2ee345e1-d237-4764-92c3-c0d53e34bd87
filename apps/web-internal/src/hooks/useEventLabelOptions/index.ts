import { useMemo } from 'react'

import { useMutation, useQuery } from '@apollo/client'

import {
  EventLabelOptionCreate,
  EventLabelOptionCreateVariables,
  EventLabelOptionUpdate,
  EventLabelOptionUpdateVariables,
  GetEventLabelOptions,
  GetEventLabelOptionsVariables,
  EventLabelOptionDelete,
  EventLabelOptionDeleteVariables,
} from './__generated__'
import {
  CREATE_EVENT_LABEL_OPTION,
  DELETE_EVENT_LABEL_OPTION,
  GET_EVENT_LABEL_OPTIONS,
  RENAME_EVENT_LABEL_OPTION,
} from './queries'

export function useEventLabelOptions() {
  const { data, loading } = useQuery<
    GetEventLabelOptions,
    GetEventLabelOptionsVariables
  >(GET_EVENT_LABEL_OPTIONS)

  const [createEventLabel, { loading: isCreatingLabel }] = useMutation<
    EventLabelOptionCreate,
    EventLabelOptionCreateVariables
  >(CREATE_EVENT_LABEL_OPTION, {
    refetchQueries: [{ query: GET_EVENT_LABEL_OPTIONS }],
  })

  const [updateEventLabel, { loading: isRenamingLabel }] = useMutation<
    EventLabelOptionUpdate,
    EventLabelOptionUpdateVariables
  >(RENAME_EVENT_LABEL_OPTION, {
    refetchQueries: [{ query: GET_EVENT_LABEL_OPTIONS }],
  })

  const [deleteEventLabel, { loading: isDeletingLabel }] = useMutation<
    EventLabelOptionDelete,
    EventLabelOptionDeleteVariables
  >(DELETE_EVENT_LABEL_OPTION, {
    refetchQueries: [{ query: GET_EVENT_LABEL_OPTIONS }],
  })

  return {
    isGetting: loading,
    eventLabelOptions: useMemo(() => {
      return data?.eventLabelOptions ?? []
    }, [data?.eventLabelOptions]),
    isMutating: isCreatingLabel || isRenamingLabel || isDeletingLabel,
    createEventLabel,
    updateEventLabel,
    deleteEventLabel,
  }
}
