import { gql } from '@apollo/client'

export const GET_EVENT_LABEL_OPTIONS = gql`
  query GetEventLabelOptions {
    eventLabelOptions {
      id
      name
    }
  }
`

export const CREATE_EVENT_LABEL_OPTION = gql`
  mutation EventLabelOptionCreate($name: String!) {
    eventLabelOptionCreate(input: { name: $name }) {
      eventLabelOption {
        id
        name
      }
    }
  }
`

export const RENAME_EVENT_LABEL_OPTION = gql`
  mutation EventLabelOptionUpdate($id: ID!, $name: String!) {
    eventLabelOptionUpdate(input: { id: $id, name: $name }) {
      eventLabelOption {
        id
        name
      }
    }
  }
`

export const DELETE_EVENT_LABEL_OPTION = gql`
  mutation EventLabelOptionDelete($id: ID!) {
    eventLabelOptionDelete(input: { id: $id }) {
      id
    }
  }
`
