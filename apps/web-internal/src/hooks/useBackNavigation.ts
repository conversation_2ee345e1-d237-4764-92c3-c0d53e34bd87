import { useState } from 'react'
import { useNavigate } from 'react-router'

export const useBackNavigation = (targetPath: string, hasChanges: boolean) => {
  const [showModal, setShowModal] = useState(false)
  const navigate = useNavigate()

  const onBack = () => {
    if (hasChanges) {
      setShowModal(true)
    } else {
      navigate(targetPath)
    }
  }

  return { onBack, showModal, setShowModal }
}
