<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8" />
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1, shrink-to-fit=no"
		/>
		<link rel="icon" href="/favicon.ico" sizes="32x32">
		<link rel="icon" href="https://assets.apella.io/logos/favicons/icon.svg" type="image/svg+xml">
		<link rel="apple-touch-icon" href="https://assets.apella.io/logos/favicons/apple-touch-icon.svg">
		<link rel="manifest" href="/manifest.webmanifest">
		<title>Apella Customer Dashboard</title>
		<!-- Global site tag (gtag.js) - Google Analytics -->
		<script
			async
			src="https://www.googletagmanager.com/gtag/js?id=G-MCXPY123JT"
		></script>
		<script>
			window.dataLayer = window.dataLayer || []
			function gtag() {
				dataLayer.push(arguments)
			}
			gtag('js', new Date())

			gtag('config', 'G-MCXPY123JT')
		</script>
		<script type="text/javascript">!function(e,t,n){function a(){var e=t.getElementsByTagName("script")[0],n=t.createElement("script");n.type="text/javascript",n.async=!0,n.src="https://beacon-v2.helpscout.net",e.parentNode.insertBefore(n,e)}if(e.Beacon=n=function(t,n,a){e.Beacon.readyQueue.push({method:t,options:n,data:a})},n.readyQueue=[],"complete"===t.readyState)return a();e.attachEvent?e.attachEvent("onload",a):e.addEventListener("load",a,!1)}(window,document,window.Beacon||function(){});
		</script><script type="text/javascript">window.Beacon('init', '2b3d512a-8aab-437e-86fe-d7d251811586')</script>
	</head>

	<body>
		<div id="root"></div>
		<noscript> You need to enable JavaScript to run this app. </noscript>
		<script type="module" src="src/index.tsx"></script>
	</body>
</html>
