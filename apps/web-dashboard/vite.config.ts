import { defineConfig } from 'vite'
import checker from 'vite-plugin-checker'
import { createHtmlPlugin } from 'vite-plugin-html'
import react from '@vitejs/plugin-react'
import path from 'path'

import { getConfigByEnvironment } from './src/settings/get_config'

const projectRootDir = path.resolve(__dirname)

const apella_env = process.env.APELLA_ENV ?? 'development'
const settings = {
  ...getConfigByEnvironment(apella_env),
  version: process.env.APP_VERSION, // set as part of deployment
}
const proxyTarget = settings.api.domain
const port = 3020

const resolveApellaLibPath = (libName: string, entry: string) =>
  path.resolve(__dirname, `../../libs/${libName}`, entry)

export default defineConfig(({ command }) => {
  if (command === 'serve') {
    settings.api.domain = `http://localhost:${port}`
  }

  return {
    server: {
      port,
      proxy: {
        '/v1': {
          target: proxyTarget,
          changeOrigin: true,
        },
      },
    },

    define: {
      APELLA_ENVIRONMENT: JSON.stringify(settings),
      'process.env': process.env,
    },

    resolve: {
      alias: [
        {
          // Enable bare specifier module imports
          find: 'src',
          replacement: path.resolve(projectRootDir, 'src'),
        },

        // Alias monorepo projects so that the dev server detects changes
        {
          find: '@apella/logger',
          replacement: resolveApellaLibPath('logger', 'src/index.ts'),
        },
        {
          find: '@apella/hooks',
          replacement: resolveApellaLibPath('hooks', 'src/index.ts'),
        },
        {
          find: '@apella/component-library',
          replacement: resolveApellaLibPath(
            'component-library',
            'src/index.ts'
          ),
        },
        {
          find: '@apella/react-router-storybook',
          replacement: resolveApellaLibPath(
            'react-router-storybook',
            'src/index.tsx'
          ),
        },
      ],
    },

    build: {},

    test: {
      alias: [
        {
          find: 'test-utils',
          replacement: path.resolve(__dirname, 'src/test/test-utils'),
        },
      ],
      globals: true,
      environment: 'jsdom',
      setupFiles: [path.resolve(__dirname, 'src/test/setupTest.ts')],
    },

    plugins: [
      command === 'build' && createHtmlPlugin({ minify: true }),

      // tsc already runs before build
      command === 'serve' && checker({ typescript: { buildMode: true } }),

      react({
        jsxImportSource: '@emotion/react',
        babel: {
          plugins: ['@emotion/babel-plugin'],
        },
      }),
    ],
  }
})
