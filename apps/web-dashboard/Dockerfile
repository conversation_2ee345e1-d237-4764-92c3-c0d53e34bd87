########################################################
# Use the official full Node.js lts image for the build stage
# https://hub.docker.com/_/node
FROM node:lts AS build

# The Apella Environment to compile to
ARG APELLA_ENV
ARG APP_VERSION
ARG APOLLO_KEY
ENV APOLLO_KEY $APOLLO_KEY

# Create and change to the app directory.
WORKDIR /usr/src/app

RUN  mkdir -p /root/.config/gcloud

# Copy application dependency manifests to the container image.
# Copying this separately prevents re-running npm install on every code change.
COPY yarn.lock .
COPY package.json .

# TODO: DRY this up so we don't need to modify every time a new workspace is added
COPY apps/web-internal/package.json apps/web-internal/
COPY apps/web-dashboard/package.json apps/web-dashboard/
COPY libs/eslint-config-custom/package.json libs/eslint-config-custom/
COPY libs/logger/package.json libs/logger/
COPY libs/component-library/package.json libs/component-library/
COPY libs/hooks/package.json libs/hooks/
COPY libs/react-router-storybook/package.json libs/react-router-storybook/

# Install the dev dependencies
RUN --mount=type=cache,target=/root/.yarn YARN_CACHE_FOLDER=/root/.yarn \
  yarn install --frozen-lockfile

# Copy application code
COPY . .

# Build the application
RUN --mount=type=cache,target=./node_modules/.cache/webpack \
  NODE_ENV=production APP_VERSION=$APP_VERSION APELLA_ENV=$APELLA_ENV npx nx run-many -t build -p web-dashboard

########################################################
FROM nginx:stable

COPY ./apps/web-dashboard/nginx.conf /etc/nginx/nginx.conf

# Copy over the build files from the build phase
COPY --from=build /usr/src/app/apps/web-dashboard/dist/ /var/www

EXPOSE 8443

# Run the web service on container startup.
CMD ["nginx", "-g", "daemon off;"]
