worker_processes auto;

events {
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;

  server_tokens off;
  server {
    listen 8443;
    root /var/www;

    gzip on;
    gzip_types text/html application/javascript application/json text/css;

    location /healthz/live {
      return 200 'OK';
      add_header Content-Type text/plain;
    }

    location /healthz/ready {
      return 200 'OK';
      add_header Content-Type text/plain;
    }

    location / {
      try_files $uri $uri/ /index.html;
    }

  }
}
