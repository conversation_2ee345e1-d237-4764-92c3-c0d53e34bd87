import type { CodegenConfig } from '@graphql-codegen/cli'

import { getConfigByEnvironment } from './src/settings/get_config'

const apellaEnv = process.env.APELLA_ENV ?? 'development'

const autoGeneratedHeaderPlugin = {
  add: {
    content: `
/** THIS FILE IS AUTO-GENERATED **/
/** EDITS WILL NOT BE PERSISTED **/
`,
    placement: 'prepend',
  },
}

const apolloKey = process.env.APOLLO_KEY

const config: CodegenConfig = {
  overwrite: true,
  schema: getSchemaFromEnv(apellaEnv),
  documents: ['src/**/*.tsx', 'src/**/*.ts'],
  // See https://the-guild.dev/graphql/codegen/plugins/typescript/typescript-operations#config-api-reference
  config: {
    // Enforce Array<T> types to be Array<T> instead of Array<T> | T
    arrayInputCoercion: false,
    preResolveTypes: true,
    namingConvention: 'keep',
    avoidOptionals: { field: true },
    nonOptionalTypename: true,
    skipTypeNameForRoot: true,
    dedupeOperationSuffix: true,
    omitOperationSuffix: true,
    strictScalars: true,
    // See src/global.d.ts
    scalars: {
      DateTime: 'string',
      Date: 'string',
      UUID: 'string',
      Duration: 'string',
      Time: 'string',
      JSON: 'string',
    },
  },

  generates: {
    './src/__generated__/globalTypes.ts': {
      plugins: ['typescript', autoGeneratedHeaderPlugin],
    },
    'src/': {
      plugins: ['typescript-operations', autoGeneratedHeaderPlugin, 'typed-document-node'],
      preset: 'near-operation-file',
      presetConfig: {
        extension: '.ts',
        fileName: 'index',
        baseTypesPath: './__generated__/globalTypes.ts',
        folder: '__generated__',
      },
    },
    // See https://the-guild.dev/graphql/codegen/plugins/typescript/typescript-mock-data for more options
    'src/__generated__/generated-mocks.ts': {
      plugins: [
        {
          'typescript-mock-data': {
            typesFile: 'src/__generated__/globalTypes.ts',
            enumValues: 'upper-case#upperCase',
            addTypename: true,
            typeNames: 'keep',
            terminateCircularRelationships: true, // We have a lot of circular relationships in our graph, so this is necessary to avoid call stack errors
            scalars: {
              DateTime: 'string',
              Date: 'string',
              UUID: 'string',
              Duration: 'string',
              Time: 'string',
              JSON: 'string',
            },
            fieldGeneration: {
              _all: {
                id: 'faker.string.uuid()',
              },
              Site: {
                id: "'lab_1'",
                name: "'Apella Lab'",
              },
              Organization: {
                id: "'apella_internal_0'",
              },
              CaseFlagUpsertInput: {
                orgId: "'apella_internal_0'",
                siteId: "'lab_1'",
              },
              CaseNotePlanUpsertInput: {
                orgId: "'apella_internal_0'",
                siteId: "'lab_1'",
              },
            },
          },
        },
        {
          add: {
            content: `/* eslint-disable */
import { faker } from '@faker-js/faker';
            `,
            placement: 'prepend',
          },
        },
      ],
    },
  },
}

function getSchemaFromApollo(
  env: string,
  schemaVariant: 'prod' | 'current'
): NonNullable<CodegenConfig['schema']> {
  if (!apolloKey)
    throw new Error(`APOLLO_KEY is required to be set when APELLA_ENV=${env}`)

  return {
    'apollo-engine': {
      engine: {
        apiKey: apolloKey,
      },
      variant: schemaVariant,
      graph: 'api-server-ncdndm',
    },
  }
}

function getSchemaFromEnv(env: string): NonNullable<CodegenConfig['schema']> {
  switch (env) {
    case 'development': {
      return getSchemaFromApollo(env, 'current')
    }
    case 'staging': {
      return getSchemaFromApollo(env, 'current')
    }
    case 'production': {
      return getSchemaFromApollo(env, 'prod')
    }
    default: {
      const settings = getConfigByEnvironment(apellaEnv)

      const localSchemaFile = process.argv
        .find((arg) => arg?.startsWith('--localSchemaFile'))
        ?.split('=')?.[1]

      return (
        localSchemaFile || `${settings.api.domain}${settings.api.graphQLUrl}`
      )
    }
  }
}

// eslint-disable-next-line no-restricted-exports
export default config
