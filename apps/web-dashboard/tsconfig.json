{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "useDefineForClassFields": true,
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ] /* Specify library files to be included in the compilation. */,
    "allowJs": true /* Allow javascript files to be compiled. */,
    "jsx": "react-jsx" /* Specify JSX code generation: 'preserve', 'react-native', or 'react'. */,
    "jsxImportSource": "@emotion/react",
    "noEmit": true /* Do not emit outputs. */,
    "isolatedModules": true /* Transpile each file as a separate module (similar to 'ts.transpileModule'). */,

    /* Module Resolution Options */
    "baseUrl": "./" /* Base directory to resolve non-absolute module names. */,
    "paths": {
      "test-utils": ["./src/test/test-utils"]
    } /* A series of entries which re-map imports to lookup locations relative to the 'baseUrl'. */,
    "types": [
      "node",
      "vitest/globals",
      "@testing-library/jest-dom",
      "react"
    ] /* Type declaration files to be included in compilation. */
  },
  "references": [
    {
      "path": "../../libs/component-library"
    },
    {
      "path": "../../libs/hooks"
    },
    {
      "path": "../../libs/logger"
    },
    {
      "path": "../../libs/react-router-storybook"
    }
  ],
  "include": ["src/**/*", "lib.es5.d.ts", "codegen.ts"]
}
