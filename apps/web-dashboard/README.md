## Development

To get started login to gcloud

```
gcloud auth login
```

download all of the dependencies

```
yarn
```

When developing run the following command to have your changes hot reload while you're working

```
yarn nx run web-dashboard:dev
```

### Running against different environments

To allow pointing your application at different environments you can set the APELLA_ENV environmental variable to development, staging, or production. The default value will be development

```
APELLA_ENV=production yarn nx run web-dashboard:dev
```

### Running locally with dev api-server

1. In cloud-api-server run `make generate-graphql`
2. Copy over `schema.graphql` to your `web-dashboard` directory.
3. IF running for first time, make sure to ask someone to add you in to [auth0](https://manage.auth0.com/).
4. Start cloud-api-server:
   - Run `gcloud auth application-default login`
   - Run `dev-tunnel` to connect to the dev database and leave it running
   - In a new terminal, run `make run-dev`
5. Start web-dashboard:
   - Run `yarn nx run web-dashboard:dev:local`

This will use locally defined feature flags (as of 3/23/23 located [here](/src/modules/feature/flags.ts)). This feature is only supported in development mode, and not in production mode.

### Running with local Cube

If you are adding new Cube definitions, and would like to develop against them before they are merged,
you can run against production api-server, but point to a locally running Cube instance.

1. Configure your local Cube instance to run against the appropriate database, e.g. production BigQuery, and start it by following [these instructions](https://github.com/Apella-Technology/cubejs/blob/main/README.md).
2. Change the Cube URL in the [production config](https://github.com/Apella-Technology/lib-web/blob/23a82b44b0de9abe49b24e12e0cdf1b1b56c0b53/apps/web-dashboard/src/settings/production.ts#L28) to match the [localhost config](https://github.com/Apella-Technology/lib-web/blob/c1b81a12dc2dded5769399865ee7c5b5bfdfe410/apps/web-dashboard/src/settings/localhost.ts#L26).
3. Run your local server against the production environment, `APELLA_ENV=production yarn nx run web-dashboard:dev`.

**Make sure you do NOT include the change to Cube URL in your PR.**

### Deployments

Merge your PR which will commit your code to `main`. Every commit pushed to `main` will
automatically trigger the deployment workflow which publishes the new assets to the
`dashboard.dev.apella.io` and `dashboard.staging.apella.io` GCP buckets. If the deployments are
successful, the workflow will automatically publish the production assets to `dashboard.apella.io`.

### Snapshot Testing

If you make a change that correctly breaks a snapshot you will need to update snapshots. To do so:

```
yarn test:update-snapshots
```

Learn more about [snapshot testing](https://jestjs.io/docs/snapshot-testing).

### Smoketesting

For the following tests, use the Apella Internal org for dev and HMH org for Prod.
Please run these tests before merging into main.

#### Tabs

##### Schedule

- Verify that the schedule page loads for today

  - Verify that Cases and phases appear

  - Verify that forecasted phases appear

  - Verify that view live appears

    - Verify that clicking it shows the live feed

    - Verify that a schedule appears in the side view

  - Verify that clicking at on the schedule at least 3 hours in the past

    - Verify that it shows an occupancy graph

    - Verify that it shows the events as generated

  - Verify that
    - When the page is visible GraphQL queries are generated every minute
    - When the page isn't visible(swtich to another tab to test this) GraphQL queries aren't generated

- Verify that schedule loads for prior days

  - Verify that Cases and phases appear

  - Verify that forecasted phases don’t appear

  - Verify that clicking at on the schedule

    - Verify that it shows an occupancy graph

    - Verify that it shows the events as generated

- Very that the schedule loads for future days

  - Verify that Cases and phases appear

- Verify that site and room filters work

##### Highlights

- Verify that the highlights page loads

##### Insights

###### Turnovers

- Verify that the page loads

- When selecting a procedure with >0 occurrences

  - Verify that the turnover graphs show up

  - Verify that the table below populates

  - Verify that the Averages give coherent, >0 minutes, values

- When selecting a surgeon with >0 procedures

  - Verify that the turnover graphs show up

  - Verify that the table below populates

  - Verify that the Averages give coherent, >0 minutes, values

- Verify that the filters correctly filter out dates and sites

###### First Case Starts

- Verify that the page loads

- When selecting a procedure with >0 occurrences

  - Verify that the turnover graphs show up

  - Verify that the table below populates

  - Verify that the Averages give coherent, >0 minute, values

- When selecting a surgeon with >0 procedures

  - Verify that the turnover graphs show up

  - Verify that the table below populates

  - Verify that the Averages give coherent, >0 minute, values

- Verify that the filters correctly filter out dates and sites

###### Case Lengths

- Verify that the page loads

- When selecting a procedure with >0 occurrences

  - Verify that the turnover graphs show up

  - Verify that the table below populates

  - Verify that the Averages give coherent, >0 minute, values

- When selecting a surgeon with >0 procedures

  - Verify that the turnover graphs show up

  - Verify that the table below populates

  - Verify that the Averages give coherent, >0 minute, values

- Verify that the filters correctly filter out dates and sites

##### Live

- Verify that the live page loads and all squares have video

- Verify that the most recent event is one of

  - `Idle`

  - An event that happened in the past

- Verify that clicking on any video brings up a video blade

  - The blade has schedule in it

- Verify that snoozing live squares hides them

- Verify that
  - When the page is visible GraphQL queries are generated every 5 seconds
  - When the page isn't visible(swtich to another tab to test this) GraphQL queries aren't generated
