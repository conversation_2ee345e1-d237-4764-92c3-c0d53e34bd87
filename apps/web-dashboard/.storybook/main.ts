import { StorybookConfig } from '@storybook/react-vite'

const config: StorybookConfig = {
  stories: ['../src/**/*.stories.@(js|jsx|ts|tsx)'],

  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-storysource',
    '@chromatic-com/storybook',
    'storybook-addon-mock-date',
  ],

  framework: '@storybook/react-vite',
  async viteFinal(config) {
    const { mergeConfig } = await import('vite')

    return mergeConfig(config, {
      resolve: {
        alias: [
          {
            // Fixes a compatibility issue with storybook-remix-react-router and react-router v7.
            // TODO: remove this if the issue is resolved.
            find: 'react-router-dom',
            replacement: 'react-router',
          },
        ],
      },
    })
  },

  staticDirs: ['../public'],
}

export default config
