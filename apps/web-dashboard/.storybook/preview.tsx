import React from 'react'

import { ThemeProvider } from '@emotion/react'
import { makeDecorator } from '@storybook/preview-api'
import { Preview, StoryContext, StoryFn, Parameters } from '@storybook/react'
import MockDate from 'mockdate'
import { initialize, mswLoader } from 'msw-storybook-addon'
import { ToastContainer } from 'react-toastify'

import {
  darkTheme as Dark,
  GlobalStyles,
  theme as Light,
} from '@apella/component-library'
import { viewports } from '../src/test/storybookHelpers'

import 'react-loading-skeleton/dist/skeleton.css'

const themes = {
  Light,
  Dark,
}

const themeNames = Object.keys(themes)
const defaultTheme = themeNames[0]

/*
 * Initializes MSW
 * See https://github.com/mswjs/msw-storybook-addon#configuring-msw
 * to learn how to customize it
 */
initialize({
  onUnhandledRequest: 'bypass',
})

const withCleanLocalStorage = (storyFn) => {
  window.localStorage.clear()
  return storyFn()
}

const decorators = [
  (Story: StoryFn, context: StoryContext) => {
    const theme = themes[context.globals.theme] ?? themes[defaultTheme]

    return (
      <ThemeProvider theme={theme}>
        <GlobalStyles />
        <ToastContainer />
        <main
          style={{
            backgroundColor: theme.palette.background.primary,
            height: '100vh',
          }}
          id="root"
        >
          <Story />
        </main>
      </ThemeProvider>
    )
  },
  makeDecorator({
    name: 'withDate',
    parameterName: 'date',
    wrapper: (storyFn, context, { parameters: date }) => {
      MockDate.reset()
      if (date instanceof Date) {
        MockDate.set(date)
      }
      return storyFn(context)
    },
  }),
  withCleanLocalStorage,
]

const globalTypes = {
  theme: {
    name: 'Theme',
    description: 'Global theme for components',
    defaultValue: defaultTheme,
    toolbar: {
      icon: 'photo',
      items: themeNames,
    },
  },
}

const parameters: Parameters = {
  backgrounds: {
    disable: true,
  },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
  },
  layout: 'fullscreen',
  viewport: {
    viewports,
  },
}

const preview: Preview = {
  // ... rest of preview configuration
  loaders: [mswLoader], // 👈 Add the MSW loader to all stories
  parameters,
  globalTypes,
  decorators,
}

export default preview
