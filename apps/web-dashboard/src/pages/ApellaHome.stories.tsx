import { <PERSON><PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import { Auth0Context, initialContext } from '@auth0/auth0-react'
import { Meta, StoryObj } from '@storybook/react'
import { delay, graphql, http, HttpResponse } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'
import { GetSiteOptionsFilterDocument } from 'src/modules/site/__generated__'
import { GetSiteOptionsFilterData } from 'src/modules/site/__mocks__/GetSiteOptionsFilterData'
import { GetCurrentUserDocument } from 'src/modules/user/__generated__'
import MockLDProvider from 'src/test/MockLDProvider'
import { modes } from 'src/test/storybookHelpers'

import { ApellaHome } from './ApellaHome'
import { GetCurrentUserData, UserType } from './test_mocks/GetCurrentUserData'

const mockedClient = new ApolloClient({
  uri: 'https://your-graphql-endpoint',
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
    query: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
  },
})

const meta: Meta<typeof ApellaHome> = {
  title: 'Pages/ApellaHome',
  component: ApellaHome,
  decorators: [
    (Story, { parameters }) => (
      <TimezoneProvider>
        <MockLDProvider flags={parameters.ldFlags}>
          <Auth0Context.Provider
            value={{
              ...initialContext,
              isAuthenticated: true,
              isLoading: false,
            }}
          >
            <ApolloProvider client={mockedClient}>
              <Story />
            </ApolloProvider>
          </Auth0Context.Provider>
        </MockLDProvider>
      </TimezoneProvider>
    ),
    withRouter,
  ],
  parameters: {
    chromatic: {
      modes: {
        mobile: modes.mobile,
        xLarge: modes.xLarge,
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof ApellaHome>
const routing = {
  path: '/',
  handle: 'ApellaHome',
}

const msw = (wait?: number) => ({
  handlers: [
    http.get('https://events.launchdarkly.com/events/diagnostic/', () => {
      return HttpResponse.json({})
    }),
    http.get('https://app.launchdarkly.com/sdk/goals/', () => {
      return HttpResponse.json({})
    }),
    graphql.query(GetCurrentUserDocument, () => {
      return HttpResponse.json(GetCurrentUserData(UserType.ALL_ACCESS))
    }),
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
  ],
})

export const ApellaHomePage: Story = {
  parameters: {
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/',
      },
      routing,
    },
  },
}
