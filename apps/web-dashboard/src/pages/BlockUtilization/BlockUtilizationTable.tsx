import { generatePath, useNavigate } from 'react-router'

import { DateTime } from 'luxon'

import {
  CsvExportButton,
  H4,
  P2,
  pxSpacing,
  remSpacing,
  Table,
  TBody,
  TD,
  TH,
  THead,
  theme,
  TR,
} from '@apella/component-library'
import { LocationPath } from 'src/router/types'

import {
  FILE_NAME_DATE_FORMAT,
  PASSING_UTILIZATION_THRESHOLD,
  WARNING_UTILIZATION_THRESHOLD,
} from './constants'
import { BlockUtilizationByBlock } from './types'
import { useBlockUtilizationFilters } from './useBlockUtilizationFilters'
import { secondsToMinutesAsString } from './utils'

const getStatusText = (utilizationPercentage: number) => {
  return utilizationPercentage >= PASSING_UTILIZATION_THRESHOLD
    ? 'Meets goal'
    : utilizationPercentage >= WARNING_UTILIZATION_THRESHOLD
      ? 'Near goal'
      : 'Below goal'
}

const Status = ({
  utilizationPercentage,
}: {
  utilizationPercentage: number
}) => {
  const status = getStatusText(utilizationPercentage)
  const color =
    utilizationPercentage >= PASSING_UTILIZATION_THRESHOLD
      ? theme.palette.green[50]
      : utilizationPercentage >= WARNING_UTILIZATION_THRESHOLD
        ? theme.palette.yellow[50]
        : theme.palette.red[50]
  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.xsmall,
        alignItems: 'center',
        textWrapMode: 'nowrap',
      }}
    >
      <span
        css={{
          height: pxSpacing.xsmall,
          width: pxSpacing.xsmall,
          background: color,
          borderRadius: '50%',
        }}
      />
      <P2>{status}</P2>
    </div>
  )
}

export const BlockUtilizationTable = ({
  blockUtilizations,
}: {
  blockUtilizations: BlockUtilizationByBlock[]
}) => {
  const navigate = useNavigate()
  const { selectedDateRange, selectedSiteId } = useBlockUtilizationFilters()
  const filename = `BlockUtilization-${DateTime.now().toFormat(FILE_NAME_DATE_FORMAT)}`
  const columns = [
    {
      name: 'Block',
      exportFormatter: (rowData: BlockUtilizationByBlock) => rowData.blockName,
    },
    {
      name: 'Status',
      exportFormatter: (rowData: BlockUtilizationByBlock) =>
        getStatusText(rowData.utilizationPercentage),
    },
    {
      name: 'Utilization (%)',
      exportFormatter: (rowData: BlockUtilizationByBlock) =>
        `${rowData.utilizationPercentage}`,
    },
    {
      name: 'Available minutes',
      exportFormatter: (rowData: BlockUtilizationByBlock) =>
        secondsToMinutesAsString(rowData.totalAvailableSeconds),
    },
    {
      name: 'Utilized minutes',
      exportFormatter: (rowData: BlockUtilizationByBlock) =>
        secondsToMinutesAsString(rowData.totalUtilizedSeconds),
    },
    {
      name: 'Scheduled minutes',
      exportFormatter: (rowData: BlockUtilizationByBlock) =>
        secondsToMinutesAsString(rowData.totalScheduledSeconds),
    },
    {
      name: 'Released minutes',
      exportFormatter: (rowData: BlockUtilizationByBlock) =>
        secondsToMinutesAsString(
          rowData.totalBlockSeconds - rowData.totalAvailableSeconds
        ),
    },
  ]

  return (
    <div
      css={{
        border: `1px solid ${theme.palette.gray[30]}`,
        borderRadius: pxSpacing.xsmall,
        padding: remSpacing.gutter,
        gap: remSpacing.medium,
        display: 'flex',
        flexDirection: 'column',
        overflowX: 'auto',
      }}
    >
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <H4>Blocks</H4>
        <CsvExportButton
          rows={blockUtilizations}
          columns={columns}
          filename={filename}
        />
      </div>
      <Table>
        <THead>
          <TR>
            {columns.map(({ name }) => (
              <TH key={name} css={{ cursor: 'default' }}>
                {name}
              </TH>
            ))}
          </TR>
        </THead>
        <TBody>
          {blockUtilizations.map(
            ({
              blockName,
              blockId,
              utilizationPercentage,
              totalAvailableSeconds,
              totalUtilizedSeconds,
              totalScheduledSeconds,
              totalBlockSeconds,
            }) => {
              return (
                <TR
                  key={blockName}
                  css={{ cursor: 'pointer' }}
                  onClick={() => {
                    if (blockId) {
                      const path = generatePath(
                        LocationPath.BlockUtilizationForBlock,
                        {
                          blockId: blockId,
                        }
                      )
                      navigate({
                        pathname: path,
                        search: new URLSearchParams({
                          'dateRange[0]': selectedDateRange[0],
                          'dateRange[1]': selectedDateRange[1],
                          site: selectedSiteId,
                        }).toString(),
                      })
                    }
                  }}
                >
                  <TD>
                    <span
                      css={{
                        display: '-webkit-box',
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        WebkitLineClamp: 2,
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {blockName}
                    </span>
                  </TD>
                  <TD>
                    <Status utilizationPercentage={utilizationPercentage} />
                  </TD>
                  <TD>{utilizationPercentage}</TD>
                  <TD>{secondsToMinutesFloored(totalAvailableSeconds)}</TD>
                  <TD>{secondsToMinutesFloored(totalUtilizedSeconds)}</TD>
                  <TD>{secondsToMinutesFloored(totalScheduledSeconds)}</TD>
                  <TD>
                    {secondsToMinutesFloored(
                      totalBlockSeconds - totalAvailableSeconds
                    )}
                  </TD>
                </TR>
              )
            }
          )}
        </TBody>
      </Table>
    </div>
  )
}

const secondsToMinutesFloored = (seconds: number) =>
  Math.floor(seconds / 60).toLocaleString()
