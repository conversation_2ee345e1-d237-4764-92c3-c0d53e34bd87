import { ApolloClient, NormalizedCacheObject } from '@apollo/client'
import { groupBy } from 'lodash'

import { logger } from 'src/utils/exceptionLogging'

import {
  GetBlockUtilization,
  GetBlockUtilizationVariables,
} from './__generated__'
import { ALPHABETICAL, HIGH_TO_LOW } from './constants'
import { GET_BLOCK_UTILIZATIONS } from './queries'
import { BlockUtilizationByBlock } from './types'
import { getBlockUtilizationTotals } from './utils'

export const fetchBlockUtilizationsForSite = async (
  client: ApolloClient<NormalizedCacheObject>,
  {
    selectedDateRange,
    selectedSiteId,
    selectedSiteSortType,
  }: {
    selectedDateRange: string[]
    selectedSiteId: string
    selectedSiteSortType: string
  }
) => {
  const { data, error } = await client.query<
    GetBlockUtilization,
    GetBlockUtilizationVariables
  >({
    query: GET_BLOCK_UTILIZATIONS,
    variables: {
      query: {
        minDate: selectedDateRange[0],
        maxDate: selectedDateRange[1],
        siteId: selectedSiteId,
      },
    },
  })
  if (error) {
    logger.log(`Error getting Block Utilization data: ${error.message}`)
    throw error
  }

  // filter out blocks with 0 surgeons linked to them
  const blockUtilizationsData = data.blockUtilizations?.filter(
    (blockUtilization) => blockUtilization.block.surgeonIds.length > 0
  )

  const splitWord = '-APELLA_SPACE-'
  const blockUtilizationsbyBlock = groupBy(
    blockUtilizationsData,
    (blockUtilization) =>
      `${blockUtilization.block.name}${splitWord}${blockUtilization.blockId}`
  )
  const blockUtilizations: BlockUtilizationByBlock[] = Object.entries(
    blockUtilizationsbyBlock
  ).map(([blockNameId, utilizations]) => {
    const blockNameIdList = blockNameId.split(splitWord)
    const blockName = blockNameIdList[0]
    const blockId = blockNameIdList[1]
    const totals = getBlockUtilizationTotals(utilizations)
    return {
      ...totals,
      blockName,
      blockId,
    }
  })

  const sortedBlockUtilization = [...blockUtilizations].sort((a, b) => {
    if (selectedSiteSortType === ALPHABETICAL) {
      return String(a.blockName).localeCompare(String(b.blockName))
    }
    if (selectedSiteSortType === HIGH_TO_LOW) {
      return b.utilizationPercentage - a.utilizationPercentage
    }
    return a.utilizationPercentage - b.utilizationPercentage
  })

  const overallBlockUtilizationForSite = blockUtilizationsData
    ? getBlockUtilizationTotals(blockUtilizationsData)
    : undefined

  return {
    overallBlockUtilizationForSite,
    blockUtilizations: sortedBlockUtilization,
  }
}
