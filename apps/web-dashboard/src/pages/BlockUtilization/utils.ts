import { DateTime } from 'luxon'

import { theme } from '@apella/component-library'

import { CaseStatus } from '../BlockUtilizationManagement/constants'
import {
  GetBlockUtilization,
  GetBlockUtilizationForBlockView,
} from './__generated__'
import {
  PASSING_UTILIZATION_THRESHOLD,
  WARNING_UTILIZATION_THRESHOLD,
} from './constants'
import { BlockUtilizationTotals } from './types'

export const secondsToMinutesAsString = (seconds: number): string => {
  return Math.floor(seconds / 60).toLocaleString()
}

export const getColorPalette = (utilizationPercentage: number) => {
  const utilizationColorPalette =
    utilizationPercentage >= PASSING_UTILIZATION_THRESHOLD
      ? theme.palette.green
      : utilizationPercentage >= WARNING_UTILIZATION_THRESHOLD
        ? theme.palette.yellow
        : theme.palette.red

  return utilizationColorPalette
}

const getPercentage = ({
  numerator,
  denominator,
}: {
  numerator: number
  denominator: number
}): number => {
  if (denominator === 0) return 0
  return Math.floor((numerator / denominator) * 100)
}

export const getBlockUtilizationTotals = (
  blockUtilizations:
    | GetBlockUtilization['blockUtilizations']
    | GetBlockUtilizationForBlockView['blockUtilizations']
): BlockUtilizationTotals => {
  let totals = {
    totalUtilizedSeconds: 0,
    totalAvailableSeconds: 0,
    totalScheduledSeconds: 0,
    totalBlockSeconds: 0,
    totalCases: 0,
    totalCancelledCases: 0,
    totalCappedUtilizedSeconds: 0,
  }
  if (blockUtilizations) {
    totals = blockUtilizations.reduce<
      Omit<BlockUtilizationTotals, 'utilizationPercentage'>
    >((acc, blockUtilization) => {
      acc.totalAvailableSeconds += blockUtilization.availableSeconds
      acc.totalUtilizedSeconds += blockUtilization.utilizedSeconds
      acc.totalScheduledSeconds += blockUtilization.totalScheduledCaseSeconds
      acc.totalCases += blockUtilization.casesForBlockDay.length
      acc.totalCancelledCases += blockUtilization.casesForBlockDay.filter(
        (caseToBlock) =>
          caseToBlock?.scheduledCase?.status === CaseStatus.CANCELLED
      ).length
      acc.totalBlockSeconds += blockUtilization.totalBlockSeconds
      // adjust utilized seconds if greater than available seconds
      acc.totalCappedUtilizedSeconds +=
        blockUtilization.utilizedSeconds > blockUtilization.availableSeconds
          ? blockUtilization.availableSeconds
          : blockUtilization.utilizedSeconds
      return acc
    }, totals)
  }
  return {
    ...totals,
    utilizationPercentage: getPercentage({
      numerator: totals.totalCappedUtilizedSeconds,
      denominator: totals.totalAvailableSeconds,
    }),
  }
}

const formatWeekday = (day: string): string => {
  switch (day) {
    case 'Tue':
      return 'Tues'
    case 'Thu':
      return 'Thurs'
    default:
      return day
  }
}

export const getByDayFormattedDateTime = (dt: DateTime) => {
  const weekday = formatWeekday(dt.toFormat('ccc'))
  return `${weekday}, ${dt.toFormat('MM/dd')}`
}
