import { <PERSON><PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import cubejs from '@cubejs-client/core'
import { CubeProvider } from '@cubejs-client/react'
import { Meta, StoryObj } from '@storybook/react'
import { delay, graphql, HttpResponse } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'
import { GetSiteOptionsFilterDocument } from 'src/modules/site/__generated__'
import { GetSiteOptionsFilterData } from 'src/modules/site/__mocks__/GetSiteOptionsFilterData'
import { ApellaRouteContext } from 'src/router/types'
import { DEFAULT_STORYBOOK_USER } from 'src/test/constants'
import MockLDProvider from 'src/test/MockLDProvider'

import { GetBlockUtilizationDocument } from './__generated__'
import {
  loader as blockUtilizationForSiteLoader,
  BlockUtilizationForSiteLayout,
} from './BlockUtilizationForSite'

const mockDataDependencies = (): Partial<ApellaRouteContext> => ({
  apolloClient: new ApolloClient({
    uri: 'https://your-graphql-endpoint',
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
      query: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
    },
  }),
  cube: cubejs({
    apiUrl: `dummy/cubejs-api/v1`,
    headers: { Authorization: `accessToken` },
  }),
  user: DEFAULT_STORYBOOK_USER,
})

const meta: Meta<typeof BlockUtilizationForSiteLayout> = {
  title: 'Pages/BlockUtilizationDashboard/DashboardForSite',
  component: BlockUtilizationForSiteLayout,
  decorators: [
    (Story, { parameters }) => (
      <TimezoneProvider>
        <MockLDProvider flags={parameters.ldFlags}>
          <CubeProvider cubeApi={parameters.reactRouter.context.cube}>
            <ApolloProvider
              client={parameters.reactRouter.context.apolloClient}
            >
              <Story />
            </ApolloProvider>
          </CubeProvider>
        </MockLDProvider>
      </TimezoneProvider>
    ),
    withRouter,
  ],
}

export default meta
type Story = StoryObj<typeof BlockUtilizationForSiteLayout>

const routing = {
  path: '/block-utilization',
  handle: 'BlockUtilizationForSiteLayout',
  Component: BlockUtilizationForSiteLayout,
  loader: blockUtilizationForSiteLoader,
}

const BlockUtilizationData = {
  data: {
    blockUtilizations: [
      {
        availableSeconds: 30 * 60,
        totalScheduledCaseSeconds: 50 * 60,
        utilizedSeconds: 40 * 60,
        totalBlockSeconds: 40 * 60,
        casesForBlockDay: [
          {
            caseId: '1',
            scheduledCase: {
              status: 'scheduled',
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
        ],
        blockId: 'block-id-1',
        date: '2025-02-05',
        block: {
          name: 'block 1',
          surgeonIds: ['surgeon-1'],
          __typename: 'Block' as const,
        },
        __typename: 'BlockUtilization' as const,
      },
      {
        availableSeconds: 100 * 60,
        totalScheduledCaseSeconds: 70 * 60,
        utilizedSeconds: 50 * 60,
        totalBlockSeconds: 150 * 60,
        casesForBlockDay: [
          {
            caseId: '2',
            scheduledCase: {
              status: 'scheduled',
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
        ],
        blockId: 'block-id-2',
        date: '2025-02-05',
        block: {
          name: 'block 2',
          surgeonIds: ['surgeon-2'],
          __typename: 'Block' as const,
        },
        __typename: 'BlockUtilization' as const,
      },
      {
        availableSeconds: 100 * 60,
        totalScheduledCaseSeconds: 70 * 60,
        utilizedSeconds: 60 * 60,
        totalBlockSeconds: 120 * 60,
        casesForBlockDay: [
          {
            caseId: '3',
            scheduledCase: {
              status: 'scheduled',
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
        ],
        blockId: 'block-id-3',
        date: '2025-02-05',
        block: {
          name: 'block 3',
          surgeonIds: ['surgeon-3'],
          __typename: 'Block' as const,
        },
        __typename: 'BlockUtilization' as const,
      },
      {
        availableSeconds: 100 * 60,
        totalScheduledCaseSeconds: 70 * 60,
        utilizedSeconds: 60 * 60,
        totalBlockSeconds: 120 * 60,
        casesForBlockDay: [
          {
            caseId: '4',
            scheduledCase: {
              status: 'scheduled',
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
        ],
        blockId: 'NO-SURGEONS-BLOCK',
        date: '2025-02-05',
        block: {
          name: 'no surgeons block',
          surgeonIds: [],
          __typename: 'Block' as const,
        },
        __typename: 'BlockUtilization' as const,
      },
    ],
  },
}
const msw = (
  wait: number,
  { getBlockUtilizationData = BlockUtilizationData } = {}
) => ({
  handlers: [
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      await delay(wait)
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
    graphql.query(GetBlockUtilizationDocument, async () => {
      await delay(wait)
      return HttpResponse.json(getBlockUtilizationData)
    }),
  ],
})

const mswError = () => ({
  handlers: [
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
    graphql.query(GetBlockUtilizationDocument, async () => {
      throw new Response('error getting data')
    }),
  ],
})

export const DashboardEnabled: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization',
        searchParams: {
          'dateRange[0]': '2025-02-05',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const DashboardNoBlocks: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      getBlockUtilizationData: { data: { blockUtilizations: [] } },
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization',
        searchParams: {
          'dateRange[0]': '2025-02-05',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const DashboardNoSiteSelected: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      getBlockUtilizationData: { data: { blockUtilizations: [] } },
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization',
        searchParams: {
          'dateRange[0]': '2025-02-05',
          'dateRange[1]': '2025-02-12',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const DashboardError: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: mswError(),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization',
        searchParams: {
          'dateRange[0]': '2025-02-05',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}
