import { generatePath } from 'react-router'

import { DateTime } from 'luxon'

import {
  Badge,
  Button,
  ButtonLink,
  CsvExportButton,
  Edit,
  H4,
  H6,
  Plus,
  pxSpacing,
  remSpacing,
  Table,
  TBody,
  TD,
  TH,
  THead,
  theme,
  TR,
} from '@apella/component-library'
import { LocationPath } from 'src/router/types'

import { CaseStatus } from '../BlockUtilizationManagement/constants'
import { UtilizedMinutesToolTip } from './BlockUtilizationToolTips'
import { CASE_DATE_FORMAT, FILE_NAME_DATE_FORMAT } from './constants'
import { CaseForBlock } from './types'
import { useBlockUtilizationFilters } from './useBlockUtilizationFilters'
import { secondsToMinutesAsString } from './utils'

export const CasesTable = ({
  casesForBlock,
  blockName,
  editView = false,
  editOnClick = () => {},
}: {
  casesForBlock: CaseForBlock[]
  blockName: string
  editView?: boolean
  editOnClick?: React.MouseEventHandler<HTMLButtonElement>
}) => {
  const { selectedDateRange, selectedSiteId } = useBlockUtilizationFilters()

  const filename = `Cases-${blockName}-${DateTime.now().toFormat(FILE_NAME_DATE_FORMAT)}`

  const getStatusText = (status: string) => {
    return status === CaseStatus.CANCELLED ? 'Cancelled' : 'Complete'
  }
  const columns = [
    {
      name: 'Case ID',
      exportFormatter: (rowData: CaseForBlock) => rowData.externalCaseId,
    },
    {
      name: 'Status',
      exportFormatter: (rowData: CaseForBlock) => getStatusText(rowData.status),
    },
    {
      name: 'Date',
      exportFormatter: (rowData: CaseForBlock) =>
        rowData.date.toFormat(CASE_DATE_FORMAT),
    },
    {
      name: 'Procedure',
      exportFormatter: (rowData: CaseForBlock) => rowData.procedures.join(', '),
    },
    {
      name: 'Scheduled duration (min)',
      exportFormatter: (rowData: CaseForBlock) =>
        secondsToMinutesAsString(rowData.scheduledDurationSeconds),
    },
    {
      name: 'Actual duration (min)',
      exportFormatter: (rowData: CaseForBlock) =>
        secondsToMinutesAsString(rowData.actualDurationSeconds),
    },
    {
      name: 'Utilized minutes',
      exportFormatter: (rowData: CaseForBlock) =>
        secondsToMinutesAsString(rowData.utilizedDurationSeconds),
    },
  ]

  return (
    <div
      css={{
        border: `1px solid ${theme.palette.gray[30]}`,
        borderRadius: pxSpacing.xsmall,
        padding: remSpacing.gutter,
        gap: remSpacing.medium,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <H4>Cases</H4>
        {editView ? (
          <Button color="alternate" size="sm" onClick={editOnClick}>
            <Plus color={theme.palette.gray[70]} />
            <H6>Add cases to block</H6>
          </Button>
        ) : (
          <div
            css={{
              display: 'flex',
              gap: remSpacing.small,
            }}
          >
            <ButtonLink
              color="alternate"
              size="sm"
              to={{
                pathname: generatePath(
                  LocationPath.BlockUtilizationVerifyReport
                ),
                search: new URLSearchParams({
                  'dateRange[0]': selectedDateRange[0],
                  'dateRange[1]': selectedDateRange[1],
                  site: selectedSiteId,
                }).toString(),
              }}
            >
              <Edit size={'sm'} />
            </ButtonLink>
            <CsvExportButton
              rows={casesForBlock}
              columns={columns}
              filename={filename}
            />
          </div>
        )}
      </div>
      <Table>
        <THead>
          <TR>
            {columns.map(({ name }) =>
              name == 'Utilized minutes' ? (
                <TH key={name}>
                  <span
                    css={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: remSpacing.xsmall,
                    }}
                  >
                    {name}
                    <UtilizedMinutesToolTip />
                  </span>
                </TH>
              ) : (
                <TH key={name}>{name}</TH>
              )
            )}
          </TR>
        </THead>
        <TBody>
          {casesForBlock.map((caseForBlock) => {
            return (
              <TR key={caseForBlock.caseId}>
                <TD>{caseForBlock.externalCaseId}</TD>
                <TD>{getStatusText(caseForBlock.status)}</TD>
                <TD>{caseForBlock.date.toFormat(CASE_DATE_FORMAT)}</TD>
                <TD>{caseForBlock.procedures.join(', ')}</TD>
                <TD>
                  {Math.floor(
                    caseForBlock.scheduledDurationSeconds / 60
                  ).toLocaleString()}
                </TD>
                <TD>
                  {Math.floor(
                    caseForBlock.actualDurationSeconds / 60
                  ).toLocaleString()}
                </TD>
                <TD>
                  <span
                    css={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: remSpacing.xsmall,
                    }}
                  >
                    {Math.floor(
                      (caseForBlock.utilizedDurationSeconds +
                        caseForBlock.turnoverDurationSeconds) /
                        60
                    ).toLocaleString()}
                    {editView && caseForBlock.override && (
                      <Badge color="orange" variant="secondary">
                        Added
                      </Badge>
                    )}
                  </span>
                </TD>
              </TR>
            )
          })}
        </TBody>
      </Table>
    </div>
  )
}
