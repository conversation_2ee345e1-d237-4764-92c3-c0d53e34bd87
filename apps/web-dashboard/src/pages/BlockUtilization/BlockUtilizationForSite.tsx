import { Suspense, useMemo } from 'react'
import {
  Await,
  LoaderFunctionArgs,
  replace,
  useAsyncValue,
  useLoaderData,
} from 'react-router'

import { remSpacing, Union } from '@apella/component-library'
import { parseParams } from '@apella/hooks'
import { EmptyState } from 'src/components/EmptyState'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { fetchSites } from 'src/modules/site/fetchSites'
import { getRouteContext, LocationPath } from 'src/router/types'

import { BlockUtilizationErrorBoundary } from './BlockUtilizationErrorBoundary'
import { BlockUtilizationFilters } from './BlockUtilizationFilters'
import { BlockUtilizationTable } from './BlockUtilizationTable'
import { SITE_SORT_OPTIONS } from './constants'
import { fetchBlockUtilizationsForSite } from './fetchBlockUtilizationsForSite'
import { SummaryCard } from './SummaryCard'
import {
  castParams,
  SITE_FILTER,
  SITE_SORT_TYPE,
  useBlockUtilizationFilters,
} from './useBlockUtilizationFilters'
import { UtilizationChart } from './UtilizationChart'
import { getColorPalette, secondsToMinutesAsString } from './utils'

export const loader = async (
  { request }: LoaderFunctionArgs,
  context: unknown
) => {
  const { flags, apolloClient, user } = getRouteContext(context)
  if (!flags.enableBlockUtilizationDashboard) {
    throw replace(LocationPath.Home)
  }
  const organizationName = user.currentOrganization.node.name
  const requestURL = new URL(request.url)
  const params = parseParams(requestURL.search)
  const { selectedSiteId, selectedDateRange, selectedSiteSortType } =
    castParams(params)

  const { sites } = await fetchSites(apolloClient)
  if (sites.length === 1 && !selectedSiteId) {
    requestURL.searchParams.set(SITE_FILTER, sites[0].node.id)
    throw replace(
      `${requestURL.pathname}?${requestURL.searchParams.toString()}`
    )
  }

  if (!selectedSiteId || !selectedDateRange || !selectedSiteSortType) {
    return {
      sites,
      organizationName,
      blockUtilizationPromise: Promise.resolve({
        overallBlockUtilizationForSite: undefined,
        blockUtilizations: undefined,
      }),
    }
  }

  return {
    sites,
    organizationName,
    blockUtilizationPromise: fetchBlockUtilizationsForSite(apolloClient, {
      selectedDateRange,
      selectedSiteId,
      selectedSiteSortType,
    }),
  }
}

export const BlockUtilizationForSiteLayout = () => {
  const { sites, organizationName, blockUtilizationPromise } =
    useLoaderData<Awaited<ReturnType<typeof loader>>>()

  return (
    <PageContentTemplate
      title="Block Utilization"
      filters={
        <BlockUtilizationFilters
          sites={sites}
          title={sites.length > 1 ? organizationName : sites[0].node.name}
          hideSites={sites.length === 1}
        />
      }
    >
      <Suspense fallback={<LoadingOverlay />}>
        <Await
          errorElement={<BlockUtilizationErrorBoundary />}
          resolve={blockUtilizationPromise}
        >
          <BlockUtilizationForSite />
        </Await>
      </Suspense>
    </PageContentTemplate>
  )
}

const BlockUtilizationForSite = () => {
  const { overallBlockUtilizationForSite, blockUtilizations } =
    useAsyncValue() as Awaited<
      Awaited<ReturnType<typeof loader>>['blockUtilizationPromise']
    >

  const utilizationColorPalette = useMemo(() => {
    return getColorPalette(
      overallBlockUtilizationForSite?.utilizationPercentage ?? 0
    )
  }, [overallBlockUtilizationForSite])

  const { selectedSiteSortType } = useBlockUtilizationFilters()

  if (!overallBlockUtilizationForSite && !blockUtilizations) {
    return (
      <EmptyState
        message="Select a site"
        subtext="to view block utilization for the site"
        Icon={Union}
      />
    )
  }

  return overallBlockUtilizationForSite && blockUtilizations.length > 0 ? (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.gutter,
        flexDirection: 'column',
      }}
    >
      <div
        css={{
          display: 'flex',
          gap: remSpacing.medium,
          justifyContent: 'space-evenly',
        }}
      >
        <SummaryCard
          value={`${overallBlockUtilizationForSite.utilizationPercentage}%`}
          description="utilization"
          tooltipDescription="Utilized minutes divided by available minutes (block time minus released)"
          colorPalette={utilizationColorPalette}
          background={utilizationColorPalette.background}
        />
        <SummaryCard
          value={secondsToMinutesAsString(
            overallBlockUtilizationForSite.totalAvailableSeconds
          )}
          description="available minutes"
          tooltipDescription="Total in-block minutes available within the selected time range"
        />
        <SummaryCard
          value={secondsToMinutesAsString(
            overallBlockUtilizationForSite.totalCappedUtilizedSeconds
          )}
          description="utilized minutes"
          tooltipDescription="Total in-block minutes utilized within the selected time range. Actual case duration and a standard turnover time are used."
        />
      </div>
      <UtilizationChart
        blockUtilizations={blockUtilizations}
        totalCases={overallBlockUtilizationForSite.totalCases}
        totalCancelledCases={overallBlockUtilizationForSite.totalCancelledCases}
        sortType={SITE_SORT_TYPE}
        selectedSortType={selectedSiteSortType}
        sortOptions={SITE_SORT_OPTIONS}
      />
      <BlockUtilizationTable blockUtilizations={blockUtilizations} />
    </div>
  ) : (
    <EmptyState
      message="No block data found"
      subtext="The selected date range doesn't appear to have block data"
      Icon={Union}
    />
  )
}
