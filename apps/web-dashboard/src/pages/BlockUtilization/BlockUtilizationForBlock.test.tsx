import { loader } from './BlockUtilizationForBlock'

describe('BlockUtilizationForBlock', () => {
  const mockApolloClient = {
    query: vi.fn().mockResolvedValue({
      data: {
        block: {
          name: 'test block',
        },
      },
    }),
  }

  describe('loader', () => {
    it('redirects to home page if feature flag is disabled', async () => {
      const initialUrl = 'https://example.com/block-utilization'
      const request = new Request(initialUrl)
      const context = {
        flags: { enableBlockUtilizationDashboard: false },
        apolloClient: mockApolloClient,
      }
      try {
        await loader({ request, params: {}, context }, context)
      } catch (error) {
        expect(error).toBeInstanceOf(Response)
        const thrown = error as Response
        expect(thrown.status).toBe(302)
        const expectedPathName = '/home'
        expect(thrown.headers.get('Location')).toBe(expectedPathName)
      }
    })
    it('redirects to block utilization page if no site is selected', async () => {
      const initialUrl = 'https://example.com/block-utilization/block-id-1'
      const request = new Request(initialUrl)
      const context = {
        flags: { enableBlockUtilizationDashboard: true },
        apolloClient: mockApolloClient,
      }
      try {
        await loader({ request, params: {}, context }, context)
      } catch (error) {
        expect(error).toBeInstanceOf(Response)
        const thrown = error as Response
        expect(thrown.status).toBe(302)
        const expectedPathName = '/block-utilization'
        expect(thrown.headers.get('Location')).toBe(expectedPathName)
      }
    })
  })
})
