import { DateTime } from 'luxon'

export interface BlockUtilizationTotals {
  totalAvailableSeconds: number
  totalBlockSeconds: number
  totalCancelledCases: number
  totalCappedUtilizedSeconds: number
  totalCases: number
  totalScheduledSeconds: number
  totalUtilizedSeconds: number
  utilizationPercentage: number
}

export interface BlockUtilizationByBlock extends BlockUtilizationTotals {
  blockId?: string
  blockName: string
}

export interface CaseForBlock {
  actualDurationSeconds: number
  caseId: string
  date: DateTime
  externalCaseId: string
  override: boolean
  procedures: string[]
  scheduledDurationSeconds: number
  status: string
  turnoverDurationSeconds: number
  utilizedDurationSeconds: number
}
