import { useRevalidator } from 'react-router'

import { Button, Error, remSpacing } from '@apella/component-library'
import { EmptyState } from 'src/components/EmptyState'

export const BlockUtilizationErrorBoundary = () => {
  const revalidator = useRevalidator()

  const reloadPage = async () => {
    await revalidator.revalidate()
  }
  return (
    <EmptyState
      message="Unable to load data"
      subtext="We encountered an issue while generating your report"
      Icon={Error}
    >
      <Button
        size="sm"
        css={{ width: '98px', marginTop: remSpacing.small }}
        disabled={revalidator.state === 'loading'}
        onClick={reloadPage}
      >
        Try again
      </Button>
    </EmptyState>
  )
}
