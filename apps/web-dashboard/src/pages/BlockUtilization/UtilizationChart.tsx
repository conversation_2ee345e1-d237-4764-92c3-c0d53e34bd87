import { useMemo } from 'react'

import { Bar } from 'recharts'
import { CartesianViewBox } from 'recharts/types/util/types'

import {
  Caps2,
  ChartMeta,
  H4,
  pxSpacing,
  remSpacing,
  SingleSelect,
  Option,
  theme,
  BarChart,
  shape,
  SVGCaps2,
} from '@apella/component-library'

import {
  PASSING_UTILIZATION_THRESHOLD,
  WARNING_UTILIZATION_THRESHOLD,
} from './constants'
import { BlockUtilizationByBlock } from './types'
import { useBlockUtilizationFilters } from './useBlockUtilizationFilters'

export const UtilizationChart = ({
  blockUtilizations,
  totalCases,
  totalCancelledCases,
  sortType,
  selectedSortType,
  sortOptions,
}: {
  blockUtilizations: BlockUtilizationByBlock[]
  totalCases: number
  totalCancelledCases: number
  sortType: string
  selectedSortType: string
  sortOptions: { label: string; value: string }[]
}) => {
  const { onSearch } = useBlockUtilizationFilters()
  const utilizationChartData = useMemo(() => {
    return blockUtilizations.map((blockUtilization) => {
      const identifier =
        blockUtilization.utilizationPercentage >= PASSING_UTILIZATION_THRESHOLD
          ? 'Meets goal'
          : blockUtilization.utilizationPercentage >=
              WARNING_UTILIZATION_THRESHOLD
            ? 'Near goal'
            : 'Below goal'
      return {
        dimension: blockUtilization.blockName,
        [identifier]: blockUtilization.utilizationPercentage / 100,
      }
    })
  }, [blockUtilizations])

  const utilizationChartMeta: ChartMeta = {
    'Meets goal': {
      type: 'percent',
      color: theme.palette.green[30],
    },
    'Near goal': {
      type: 'percent',
      color: theme.palette.yellow[40],
    },
    'Below goal': {
      type: 'percent',
      color: theme.palette.red[50],
    },
  }

  return (
    <div
      css={{
        border: `1px solid ${theme.palette.gray[30]}`,
        borderRadius: pxSpacing.xsmall,
        padding: remSpacing.gutter,
        display: 'grid',
        gridTemplateAreas: `"titleHeader casesHeader" "chart ."`,
        rowGap: remSpacing.medium,
        columnGap: '60px',
      }}
    >
      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
          gridArea: 'titleHeader',
          gap: remSpacing.xsmall,
        }}
      >
        <div
          css={{
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <H4>Block Utilization</H4>
          <SingleSelect
            name={'SortChart'}
            value={selectedSortType}
            onChange={(newSortType) => onSearch({ [sortType]: newSortType })}
            css={{
              paddingRight: 0,
            }}
            buttonSize="sm"
          >
            {sortOptions.map((option) => (
              <Option
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </SingleSelect>
        </div>
        <RangeLegend />
      </div>
      <TotalCasesCard
        totalCases={totalCases}
        totalCancelledCases={totalCancelledCases}
      />

      <div
        css={{
          height: `${16 * utilizationChartData.length + 100}px`,
          gridArea: 'chart',
        }}
      >
        <BarChart
          barChartMargin={{
            top: 20,
          }}
          TickComponent={SVGCaps2}
          bars={Object.entries(utilizationChartMeta).map(
            ([identifier, values]) => {
              return (
                <Bar
                  // While this is not a stacked bar chart, we want each individual bar to have the same placement.
                  // Without a stackId, room will be left in each block row for the other identifier bars (Meets goal, Near goal, Below goal).
                  stackId="utilization"
                  key={identifier}
                  dataKey={identifier}
                  barSize={16}
                  isAnimationActive
                  fill={values.color}
                  radius={shape.borderRadiusPx.xxsmall}
                />
              )
            }
          )}
          layout="vertical"
          valueAxisType="percent"
          data={utilizationChartData}
          meta={utilizationChartMeta}
          valueAxisScale="auto"
          valueAxisDomain={[0, 1]}
          dimensionAxisInterval={0}
          referenceLines={[
            {
              x: PASSING_UTILIZATION_THRESHOLD / 100,
              stroke: theme.palette.green[40],
              strokeWidth: '2',
              strokeDasharray: '4 4',
              label: ({ viewBox }: { viewBox: CartesianViewBox }) => {
                const x = viewBox.x ?? 0
                const y = viewBox.y ?? 0
                return (
                  <g>
                    <rect
                      x={x + 1}
                      y={y - 26}
                      width={80}
                      height={26}
                      fill={theme.palette.green.background}
                      rx={4}
                    />
                    <text
                      x={x + 10}
                      y={y - 9}
                      textAnchor="right"
                      dominantBaseline="middle"
                      fill={theme.palette.green[50]}
                      fontSize={13}
                      fontFamily="TT Norms Pro Apella"
                      fontStyle="normal"
                      fontWeight={600}
                    >
                      Goal: 65%
                    </text>
                  </g>
                )
              },
            },
          ]}
        />
      </div>
    </div>
  )
}

const TotalCasesCard = ({
  totalCases,
  totalCancelledCases,
}: {
  totalCases: number
  totalCancelledCases: number
}) => {
  return (
    <div
      css={{
        gridArea: 'casesHeader',
        display: 'flex',
        borderRadius: '4px',
        border: `1px solid ${theme.palette.gray[30]}`,
        gap: remSpacing.medium,
        padding: `${remSpacing.medium} ${remSpacing.gutter}`,
      }}
    >
      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <H4 color={theme.palette.gray[70]}>{totalCases}</H4>
        <Caps2>cases complete</Caps2>
      </div>

      <div
        css={{
          width: '1px',
          backgroundColor: theme.palette.gray[30],
        }}
      />
      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <H4 color={theme.palette.gray[70]}>{totalCancelledCases}</H4>
        <Caps2>cancelled cases</Caps2>
      </div>
    </div>
  )
}

const RangeLegend = () => {
  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.gutter,
      }}
    >
      <PercentageRangeWithRectangle
        percentageRange="65 - 100%"
        rectangleColor={theme.palette.green[30]}
      />
      <PercentageRangeWithRectangle
        percentageRange="60 - 64%"
        rectangleColor={theme.palette.yellow[50]}
      />
      <PercentageRangeWithRectangle
        percentageRange="< 59%"
        rectangleColor={theme.palette.red[50]}
      />
    </div>
  )
}

const PercentageRangeWithRectangle = ({
  percentageRange,
  rectangleColor,
}: {
  percentageRange: string
  rectangleColor: string
}) => {
  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.xsmall,
        alignItems: 'center',
      }}
    >
      <div
        css={{
          borderRadius: pxSpacing.xxsmall,
          background: rectangleColor,
          height: remSpacing.small,
          width: remSpacing.small,
        }}
      />
      <Caps2>{percentageRange}</Caps2>
    </div>
  )
}
