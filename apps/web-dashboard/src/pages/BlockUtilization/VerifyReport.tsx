import { Suspense, useMemo, useRef, useState } from 'react'
import {
  ActionFunctionArgs,
  Await,
  Form,
  generatePath,
  LoaderFunctionArgs,
  redirect,
  replace,
  useActionData,
  useAsyncValue,
  useLoaderData,
  useNavigate,
  useRouteLoaderData,
} from 'react-router'

import { getFormProps, useForm } from '@conform-to/react'
import { parseWithYup } from '@conform-to/yup'
import { DateTime } from 'luxon'
import * as yup from 'yup'

import {
  Button,
  ButtonLink,
  Dialog,
  P2,
  P3,
  pxSpacing,
  remSpacing,
  SelectToggleIcon,
  Table,
  TBody,
  TD,
  TH,
  THead,
  theme,
  TR,
} from '@apella/component-library'
import { parseParams } from '@apella/hooks'
import { CaseToBlockOverrideUpsertInput } from 'src/__generated__/globalTypes'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { defaultTimezone } from 'src/Contexts'
import { fetchCasesToBlocks } from 'src/pages/BlockUtilizationManagement/fetchCasesToBlocks'
import { getRouteContext, LocationPath } from 'src/router/types'

import {
  UpsertCaseToBlockOverrides,
  UpsertCaseToBlockOverridesVariables,
} from '../BlockUtilizationManagement/__generated__'
import { DEFAULT_TURNOVER_DURATION_IN_MINUTES } from '../BlockUtilizationManagement/constants'
import { UPSERT_CASES_TO_BLOCKS_OVERRIDES } from '../BlockUtilizationManagement/queries'
import { CaseToBlockType } from '../BlockUtilizationManagement/types'
import { BlockUtilizationErrorBoundary } from './BlockUtilizationErrorBoundary'
import { loader as blockUtilizationForBlockLoader } from './BlockUtilizationForBlock'
import { CasesTable } from './CasesTable'
import { DailyUtilizationTable } from './DailyUtilizationTable'
import { CaseForBlock } from './types'
import {
  castParams,
  useBlockUtilizationFilters,
} from './useBlockUtilizationFilters'

export const loader = async (
  { request, params }: LoaderFunctionArgs,
  context: unknown
) => {
  const { flags, apolloClient, user } = getRouteContext(context)
  if (!flags.enableBlockUtilizationDashboard) {
    throw replace(LocationPath.Home)
  }
  const selectedBlockId = params.blockId
  const requestURL = new URL(request.url)
  const searchParams = parseParams(requestURL.search)
  const { selectedSiteId, selectedDateRange } = castParams(searchParams)
  if (!selectedSiteId || !selectedDateRange || !selectedBlockId) {
    throw replace(LocationPath.BlockUtilization)
  }

  const allCasesForBlockPromise = fetchCasesToBlocks(apolloClient, {
    selectedDateRange,
    selectedSiteId,
    timezone:
      user.currentOrganization?.node.sites.edges[0]?.node.timezone ||
      defaultTimezone,
    selectedBlockId,
  })

  return {
    allCasesForBlockPromise,
    selectedBlockId,
  }
}

export const VerifyReportLayout = () => {
  const { blockUtilizationPromise, blockName } = useRouteLoaderData(
    'blockUtilizationForBlock'
  ) as Awaited<ReturnType<typeof blockUtilizationForBlockLoader>>

  const { allCasesForBlockPromise, selectedBlockId } =
    useLoaderData<Awaited<ReturnType<typeof loader>>>()

  const dataRef = useRef(
    Promise.all([blockUtilizationPromise, allCasesForBlockPromise])
  )

  return (
    <PageContentTemplate title="Verify Utilization">
      <Suspense fallback={<LoadingOverlay />}>
        <Await
          errorElement={<BlockUtilizationErrorBoundary />}
          resolve={dataRef.current}
        >
          <VerifyReport blockName={blockName} blockId={selectedBlockId} />
        </Await>
      </Suspense>
    </PageContentTemplate>
  )
}

const schema = yup.object({
  casesToAdd: yup.array().of(
    yup.object({
      caseId: yup.string().required(),
      date: yup.string().required(),
    })
  ),
})

type FormData = yup.InferType<typeof schema>

export const action = async (
  { request, params }: ActionFunctionArgs,
  context: unknown
) => {
  const formData = await request.formData()
  const submission = parseWithYup(formData, { schema })
  if (submission.status !== 'success') {
    return submission.reply()
  }

  const blockId = params.blockId
  if (!blockId) {
    throw replace(LocationPath.BlockUtilization)
  }
  const { apolloClient, user } = getRouteContext(context)

  const caseToBlockOverrides: CaseToBlockOverrideUpsertInput[] = (
    submission.value.casesToAdd || []
  ).map((caseToAdd) => {
    return {
      caseId: caseToAdd.caseId,
      blockId,
      blockDate: caseToAdd.date,
      note: `case was added to block ${blockId} on ${DateTime.now()}`,
      userId: user.userId,
    }
  })

  const { data } = await apolloClient.mutate<
    UpsertCaseToBlockOverrides,
    UpsertCaseToBlockOverridesVariables
  >({
    mutation: UPSERT_CASES_TO_BLOCKS_OVERRIDES,
    variables: {
      input: caseToBlockOverrides,
    },
  })
  if (data?.caseToBlockOverridesUpsert?.success) {
    const requestURL = new URL(request.url)
    return redirect(
      generatePath(
        `${LocationPath.BlockUtilization}/${LocationPath.BlockUtilizationForBlock}`,
        { blockId }
      ) +
        '?' +
        requestURL.searchParams.toString()
    )
  }
  return submission.reply({
    formErrors: [`Failed to edit report`],
  })
}

const VERIFY_REPORT_MODAL = {
  AddCasesToBlock: 'AddCasesToBlock',
  AdjustBlockDay: 'AdjustBlockDay',
} as const
type VerifyReportModal =
  (typeof VERIFY_REPORT_MODAL)[keyof typeof VERIFY_REPORT_MODAL]

const VerifyReport = ({
  blockName,
  blockId,
}: {
  blockName: string
  blockId: string
}) => {
  const [{ dailyBlockUtilizations, casesForBlock }, { casesForBlocks }] =
    useAsyncValue() as [
      Awaited<
        Awaited<
          ReturnType<typeof blockUtilizationForBlockLoader>
        >['blockUtilizationPromise']
      >,
      Awaited<Awaited<ReturnType<typeof loader>>['allCasesForBlockPromise']>,
    ]

  const [openModal, setOpenModal] = useState<VerifyReportModal | undefined>(
    undefined
  )
  const [selectedCaseIds, setSelectedCaseIds] = useState<Set<string>>(new Set())
  const [confirmationDialog, setConfirmationDialog] = useState<boolean>(false)

  const editOnAddCasesClick = () => {
    setOpenModal(VERIFY_REPORT_MODAL.AddCasesToBlock)
  }
  const editOnBlockUtilizationClick = () => {
    setOpenModal(VERIFY_REPORT_MODAL.AdjustBlockDay)
  }

  const caseIdsForBlock = casesForBlock.map(
    (caseForBlock) => caseForBlock.caseId
  )
  const allCasesToAdd = useMemo(() => {
    return casesForBlocks.filter(
      (caseToBlock: CaseToBlockType) =>
        !caseIdsForBlock.includes(caseToBlock.caseId)
    )
  }, [casesForBlocks, caseIdsForBlock])

  const lastResult = useActionData()
  const [form] = useForm<FormData>({
    lastResult,
  })

  const navigate = useNavigate()
  const { selectedDateRange, selectedSiteId } = useBlockUtilizationFilters()

  const toggleSelection = (caseId: string) => {
    setSelectedCaseIds((prev) => {
      const next = new Set(prev)
      next.has(caseId) ? next.delete(caseId) : next.add(caseId)
      return next
    })
  }

  const addedCases: CaseForBlock[] = useMemo(() => {
    if (selectedCaseIds.size > 0) {
      return casesForBlocks
        .filter(({ caseId }) => selectedCaseIds.has(caseId))
        .map((caseToBlock) => {
          const scheduledDurationSeconds = caseToBlock.scheduledEndTime
            .diff(caseToBlock.scheduledStartTime)
            .as('seconds')
          return {
            actualDurationSeconds: (caseToBlock?.actualMinutes ?? 0) * 60,
            caseId: caseToBlock.caseId,
            date: caseToBlock.scheduledEndTime,
            externalCaseId: caseToBlock.externalId,
            procedures: caseToBlock.procedures,
            scheduledDurationSeconds,
            status: caseToBlock.status,
            utilizedDurationSeconds: (caseToBlock.caseMinutes ?? 0) * 60,
            turnoverDurationSeconds:
              (caseToBlock.utilizedTurnoverMinutes ??
                DEFAULT_TURNOVER_DURATION_IN_MINUTES) * 60,
            override: true,
          }
        })
    }
    return []
  }, [selectedCaseIds, casesForBlocks])

  const blockUtilizationForBlockPath = {
    pathname: generatePath(
      `${LocationPath.BlockUtilization}/${LocationPath.BlockUtilizationForBlock}`,
      {
        blockId,
      }
    ),
    search: new URLSearchParams({
      'dateRange[0]': selectedDateRange[0],
      'dateRange[1]': selectedDateRange[1],
      site: selectedSiteId,
    }).toString(),
  }
  return (
    <>
      <CasesTable
        blockName={blockName}
        casesForBlock={casesForBlock.concat(addedCases)}
        editView
        editOnClick={editOnAddCasesClick}
      />
      <DailyUtilizationTable
        blockName={blockName}
        blockUtilizations={dailyBlockUtilizations}
        editView
        editOnClick={editOnBlockUtilizationClick}
      />
      <Dialog
        isOpen={openModal == VERIFY_REPORT_MODAL.AddCasesToBlock}
        onClose={() => {
          setSelectedCaseIds(new Set())
          setOpenModal(undefined)
        }}
        title="Add cases to block report"
        overflow="scroll"
        isFullScreen
      >
        <div
          css={{
            border: `1px solid ${theme.palette.gray[30]}`,
            borderRadius: pxSpacing.xsmall,
            padding: `${remSpacing.small} ${remSpacing.gutter}`,
            display: 'flex',
            alignSelf: 'stretch',
            background: theme.palette.gray.background,
          }}
        >
          <P3 color={theme.palette.gray[60]}>
            Cases added will be counted towards block utilization
          </P3>
        </div>
        <Table>
          <THead>
            <TR>
              <TH />
              <TH>Case Id</TH>
              <TH>Primary Surgeon</TH>
              <TH>Date</TH>
              <TH>Procedure</TH>
              <TH>Actual duration (min)</TH>
            </TR>
          </THead>
          <TBody>
            {allCasesToAdd.map((caseForBlock) => {
              return (
                <TR key={caseForBlock.caseId}>
                  <TD>
                    <Button
                      appearance="link"
                      onClick={() => toggleSelection(caseForBlock.caseId)}
                    >
                      <SelectToggleIcon
                        type={
                          selectedCaseIds.has(caseForBlock.caseId)
                            ? 'selected'
                            : undefined
                        }
                        size={'sm'}
                      />
                    </Button>
                  </TD>
                  <TD>{caseForBlock.externalId}</TD>
                  <TD>{caseForBlock.primaryStaffNames}</TD>
                  <TD>{caseForBlock.scheduledEndTime.toISODate()}</TD>
                  <TD>{caseForBlock.procedures.join(', ')}</TD>
                  <TD>{caseForBlock.caseMinutes}</TD>
                </TR>
              )
            })}
          </TBody>
        </Table>
        <div
          css={{
            paddingTop: remSpacing.gutter,
            display: 'flex',
            justifyContent: 'flex-end',
            gap: remSpacing.xsmall,
          }}
        >
          <Button
            color="alternate"
            onClick={() => {
              setSelectedCaseIds(new Set())
              setOpenModal(undefined)
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              setOpenModal(undefined)
            }}
          >
            Add case(s){' '}
          </Button>
        </div>
      </Dialog>

      <Dialog
        isOpen={openModal == VERIFY_REPORT_MODAL.AdjustBlockDay}
        onClose={() => setOpenModal(undefined)}
        title="Adjust block day"
      >
        <div
          css={{
            border: `1px solid ${theme.palette.gray[30]}`,
            borderRadius: pxSpacing.xsmall,
            padding: `${remSpacing.small} ${remSpacing.gutter}`,
            display: 'flex',
            alignSelf: 'stretch',
            background: theme.palette.gray.background,
          }}
        >
          <P3 color={theme.palette.gray[60]}>
            The denominator for block utilization calculations will be impacted
            by adjusting the day&apos;s block time
          </P3>
        </div>
      </Dialog>
      <Form method="post" {...getFormProps(form)}>
        <div
          css={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: remSpacing.small,
          }}
        >
          {addedCases?.map((caseToAdd, index) => (
            <div key={index}>
              <input
                type="hidden"
                name={`casesToAdd[${index}].caseId`}
                value={caseToAdd.caseId}
              />
              <input
                type="hidden"
                name={`casesToAdd[${index}].date`}
                value={caseToAdd.date.toISODate()}
              />
            </div>
          ))}
          <Button
            color="alternate"
            onClick={() => {
              if (selectedCaseIds.size > 0) {
                setConfirmationDialog(true)
              } else {
                navigate(blockUtilizationForBlockPath)
              }
            }}
            type="button"
          >
            Discard changes
          </Button>
          <Button type="submit">Save</Button>
        </div>
        <Dialog
          title="Save your changes?"
          isOpen={confirmationDialog}
          onClose={() => setConfirmationDialog(false)}
        >
          <div
            css={{
              display: 'flex',
              gap: remSpacing.small,
              flexDirection: 'column',
            }}
          >
            <P2 color={theme.palette.gray[70]}>
              {' '}
              You have unsaved changes. Do you want to save?
            </P2>
            <div
              css={{
                display: 'flex',
                gap: remSpacing.xsmall,
                justifyContent: 'flex-end',
              }}
            >
              <ButtonLink
                color="alternate"
                type="button"
                to={blockUtilizationForBlockPath}
                onClick={() => {
                  setConfirmationDialog(false)
                  setSelectedCaseIds(new Set())
                }}
              >
                Discard changes
              </ButtonLink>
              <Button type="submit">Save</Button>
            </div>
          </div>
        </Dialog>
      </Form>
    </>
  )
}
