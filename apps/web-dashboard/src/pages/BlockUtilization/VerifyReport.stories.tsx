import { Outlet, RouteObject } from 'react-router'

import { Apollo<PERSON>lient, ApolloProvider, InMemoryCache } from '@apollo/client'
import cubejs from '@cubejs-client/core'
import { CubeProvider } from '@cubejs-client/react'
import { Meta, StoryObj } from '@storybook/react'
import { delay, graphql, HttpResponse } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'
import { GetSiteOptionsFilterDocument } from 'src/modules/site/__generated__'
import { GetSiteOptionsFilterData } from 'src/modules/site/__mocks__/GetSiteOptionsFilterData'
import { ApellaRouteContext } from 'src/router/types'
import { DEFAULT_STORYBOOK_USER } from 'src/test/constants'
import MockLDProvider from 'src/test/MockLDProvider'

import { CasesToBlocksDocument } from '../BlockUtilizationManagement/__generated__'
import { CaseStatus } from '../BlockUtilizationManagement/constants'
import {
  GetBlockInfoDocument,
  GetBlockUtilizationForBlockViewDocument,
} from './__generated__'
import { loader as blockUtilizationForBlockLoader } from './BlockUtilizationForBlock'
import {
  loader as verifyReportLoader,
  VerifyReportLayout,
} from './VerifyReport'

const mockDataDependencies = (): Partial<ApellaRouteContext> => ({
  apolloClient: new ApolloClient({
    uri: 'https://your-graphql-endpoint',
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
      query: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
    },
  }),
  cube: cubejs({
    apiUrl: `dummy/cubejs-api/v1`,
    headers: { Authorization: `accessToken` },
  }),
  user: DEFAULT_STORYBOOK_USER,
})

const meta: Meta<typeof VerifyReportLayout> = {
  title: 'Pages/BlockUtilizationDashboard/VerifyReport',
  component: Outlet,
  decorators: [
    (Story, { parameters }) => (
      <TimezoneProvider>
        <MockLDProvider flags={parameters.ldFlags}>
          <CubeProvider cubeApi={parameters.reactRouter.context.cube}>
            <ApolloProvider
              client={parameters.reactRouter.context.apolloClient}
            >
              <Story />
            </ApolloProvider>
          </CubeProvider>
        </MockLDProvider>
      </TimezoneProvider>
    ),
    withRouter,
  ],
}

export default meta
type Story = StoryObj<typeof VerifyReportLayout>

const routing: RouteObject = {
  path: '/block-utilization',
  children: [
    {
      id: 'blockUtilizationForBlock',
      path: ':blockId',
      loader: blockUtilizationForBlockLoader,
      children: [
        {
          id: 'verifyReport',
          path: 'verify-report',
          Component: VerifyReportLayout,
          loader: verifyReportLoader,
        },
      ],
    },
  ],
}
const BlockUtilizationForBlockViewData = {
  data: {
    blockUtilizations: [
      {
        availableSeconds: 30 * 60,
        totalScheduledCaseSeconds: 25 * 60,
        utilizedSeconds: 50 * 60,
        totalBlockSeconds: 40 * 60,
        casesForBlockDay: [
          {
            caseId: '1',
            actualCaseSeconds: 20 * 60,
            utilizedCaseSeconds: 15 * 60,
            override: null,
            scheduledCase: {
              externalCaseId: '1',
              status: 'scheduled',
              scheduledEndTime: '2025-02-05T17:36:49.729884+00:00',
              scheduledStartTime: '2025-02-05T16:13:49.729884+00:00',
              primaryCaseProcedures: [
                {
                  procedure: {
                    name: 'procedure #1 name',
                    __typename: 'Procedure' as const,
                  },
                  __typename: 'CaseProcedure' as const,
                },
              ],
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
          {
            caseId: '2',
            actualCaseSeconds: null,
            utilizedCaseSeconds: 15 * 60,
            override: {
              blockId: 'block1-id',
              utilizedProcedureMinutes: 99,
              utilizedTurnoverMinutes: null,
              __typename: 'CaseToBlockOverride' as const,
            },
            scheduledCase: {
              externalCaseId: '2',
              status: 'cancelled',
              scheduledEndTime: '2025-02-06T17:36:49.729884+00:00',
              scheduledStartTime: '2025-02-06T16:13:49.729884+00:00',
              primaryCaseProcedures: [
                {
                  procedure: {
                    name: 'procedure #1 name',
                    __typename: 'Procedure' as const,
                  },
                  __typename: 'CaseProcedure' as const,
                },
              ],
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
        ],
        blockId: 'block-id-1',
        date: '2025-02-06',
        block: {
          name: 'block 1',
          surgeonIds: ['surgeon-1'],
          __typename: 'Block' as const,
        },
        __typename: 'BlockUtilization' as const,
      },
      {
        availableSeconds: 40 * 60,
        totalScheduledCaseSeconds: 25 * 60,
        utilizedSeconds: 20 * 60,
        totalBlockSeconds: 40 * 60,
        casesForBlockDay: [
          {
            caseId: '2',
            utilizedCaseSeconds: 15 * 60,
            actualCaseSeconds: 20 * 60,
            override: null,
            scheduledCase: {
              externalCaseId: '2',
              status: 'scheduled',
              scheduledEndTime: '2025-02-10T17:36:49.729884+00:00',
              scheduledStartTime: '2025-02-10T16:13:49.729884+00:00',
              primaryCaseProcedures: [
                {
                  procedure: {
                    name: 'procedure #2 name',
                    __typename: 'Procedure' as const,
                  },
                  __typename: 'CaseProcedure' as const,
                },
              ],
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
        ],
        blockId: 'block-id-1',
        date: '2025-02-10',
        block: {
          name: 'block 1',
          surgeonIds: ['surgeon-1'],
          __typename: 'Block' as const,
        },
        __typename: 'BlockUtilization' as const,
      },
    ],
  },
}
const msw = (
  wait: number,
  {
    getBlockUtilizationForBlockViewData = BlockUtilizationForBlockViewData,
  } = {}
) => ({
  handlers: [
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      await delay(wait)
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
    graphql.query(GetBlockUtilizationForBlockViewDocument, async () => {
      await delay(wait)
      return HttpResponse.json(getBlockUtilizationForBlockViewData)
    }),
    graphql.query(GetBlockInfoDocument, async () => {
      await delay(wait)
      return HttpResponse.json({
        data: {
          block: {
            name: 'block 1',
            __typename: 'Block',
          },
        },
      })
    }),
    graphql.query(CasesToBlocksDocument, async () => {
      await delay(wait)
      return HttpResponse.json({
        data: {
          casesToBlocks: {
            __typename: 'CaseToBlockConnection',
            edges: [
              {
                node: {
                  __typename: 'CaseToBlock',
                  actualCaseSeconds: 99 * 60,
                  utilizedCaseSeconds: 99 * 60,
                  scheduledCase: {
                    externalCaseId: 'external-1',
                    primaryCaseProcedures: [
                      {
                        procedure: {
                          name: 'procedure #1 name',
                          __typename: 'Procedure' as const,
                        },
                        __typename: 'CaseProcedure' as const,
                      },
                    ],
                    status: CaseStatus.SCHEDULED,
                    scheduledEndTime: '2025-02-06T17:36:49.729884+00:00',
                    scheduledStartTime: '2025-02-06T16:13:49.729884+00:00',
                    caseStaff: [
                      {
                        role: 'Primary',
                        staff: {
                          id: 'staff-1',
                          firstName: 'Thanos',
                          lastName: 'Last',
                          __typename: 'Staff',
                        },
                        __typename: 'CaseStaff',
                      },
                    ],
                    __typename: 'ScheduledCase',
                  },
                  blockId: 'block1-id',
                  score: 4,
                  caseId: '123',
                  override: {
                    blockId: 'block1-id',
                    utilizedProcedureMinutes: 99,
                    utilizedTurnoverMinutes: null,
                    __typename: 'CaseToBlockOverride',
                  },
                },
                __typename: 'CaseToBlockEdge',
              },
              {
                node: {
                  __typename: 'CaseToBlock',
                  actualCaseSeconds: 4320,
                  utilizedCaseSeconds: 4320,
                  scheduledCase: {
                    externalCaseId: 'external-2',
                    primaryCaseProcedures: [],
                    status: CaseStatus.SCHEDULED,
                    scheduledEndTime: '2025-02-07T17:36:49.729884+00:00',
                    scheduledStartTime: '2025-02-07T16:13:49.729884+00:00',
                    caseStaff: [],
                    __typename: 'ScheduledCase',
                  },
                  blockId: 'block1-id',
                  score: 2,
                  caseId: 'ignore-this-case',
                  override: null,
                },
                __typename: 'CaseToBlockEdge',
              },
            ],
          },
        },
      })
    }),
  ],
})

export const VerifyReport: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization/block-id-1/verify-report',
        searchParams: {
          'dateRange[0]': '2025-02-05',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}
