import {
  Caps3,
  H2,
  H6,
  Info,
  pxSpacing,
  remSpacing,
  theme,
  Tooltip,
} from '@apella/component-library'

export const SummaryCard = ({
  value,
  description,
  tooltipDescription,
  colorPalette = theme.palette.gray,
  background,
}: {
  value: string
  description: string
  tooltipDescription?: string
  colorPalette?: { [key in 30 | 70]: string }
  background?: string
}) => {
  const tooltipBody = (
    <H6 color={theme.palette.text.secondary}>{tooltipDescription}</H6>
  )
  return (
    <div
      css={{
        display: 'flex',
        padding: remSpacing.gutter,
        border: `1px solid ${colorPalette[30]}`,
        borderRadius: pxSpacing.xsmall,
        flexDirection: 'column',
        width: '100%',
        background,
      }}
    >
      <H2 color={colorPalette[70]}>{value}</H2>
      <div
        css={{
          display: 'flex',
          gap: remSpacing.xxsmall,
        }}
      >
        <Caps3 color={colorPalette[70]}>{description}</Caps3>
        {tooltipDescription && (
          <Tooltip body={tooltipBody}>
            <Info color={colorPalette[70]} size="xxs" />
          </Tooltip>
        )}
      </div>
    </div>
  )
}
