import { generatePath } from 'react-router'

import { DateTime } from 'luxon'

import {
  ButtonLink,
  DatePicker,
  Edit,
  H3,
  H6,
  remSpacing,
} from '@apella/component-library'
import { FilterLabel } from 'src/components/FilterLabel'
import { SitesFilter, SiteShape } from 'src/components/SitesFilter'
import { useTimezone } from 'src/Contexts'
import { LocationPath } from 'src/router/types'

import {
  DATE_RANGE_FILTER,
  SITE_FILTER,
  useBlockUtilizationFilters,
} from './useBlockUtilizationFilters'

export const BlockUtilizationFilters = ({
  sites,
  title,
  hideSites = false,
  verifyReport = false,
}: {
  title: string
  sites?: SiteShape[]
  hideSites?: boolean
  verifyReport?: boolean
}) => {
  const { onSearch, selectedDateRange, selectedSiteId } =
    useBlockUtilizationFilters()
  const { timezone } = useTimezone()
  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.medium,
        flexDirection: 'column',
        width: '100%',
      }}
    >
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        <H3>{title}</H3>
        {verifyReport && (
          <ButtonLink
            size="sm"
            to={{
              pathname: generatePath(LocationPath.BlockUtilizationVerifyReport),
              search: new URLSearchParams({
                'dateRange[0]': selectedDateRange[0],
                'dateRange[1]': selectedDateRange[1],
                site: selectedSiteId,
              }).toString(),
            }}
          >
            <Edit size="sm" />
            <H6>Verify report</H6>
          </ButtonLink>
        )}
      </div>
      <div
        css={{
          display: 'flex',
          gap: remSpacing.medium,
        }}
      >
        {/* TODO: update to month picker */}
        <FilterLabel label="Date range">
          <DatePicker
            value={[
              DateTime.fromISO(selectedDateRange[0], {
                zone: timezone,
              }).toJSDate(),
              DateTime.fromISO(selectedDateRange[1], {
                zone: timezone,
              }).toJSDate(),
            ]}
            selectRange
            showPresets
            timezone={timezone}
            setValue={(dates) => {
              if (Array.isArray(dates) && dates[0] && dates[1]) {
                const startDate = DateTime.fromJSDate(dates[0], {
                  zone: timezone,
                })
                const endDate = DateTime.fromJSDate(dates[1], {
                  zone: timezone,
                })
                // check to make sure range is less than 1 month for now
                if (endDate.diff(startDate, 'days').days >= 32) {
                  alert('Invalid date range 1 month is the max range')
                } else {
                  onSearch({
                    [DATE_RANGE_FILTER]: [
                      startDate.toISODate(),
                      endDate.toISODate(),
                    ],
                  })
                }
              }
            }}
          />
        </FilterLabel>
        {!hideSites && (
          <FilterLabel label="Site">
            <SitesFilter
              label="Select a site"
              sites={sites ?? []}
              selectedSiteIds={selectedSiteId}
              onChangeSites={(siteId) => {
                onSearch({ [SITE_FILTER]: siteId ?? '' })
              }}
              disableSelectAllOption
              disableSearch
            />
          </FilterLabel>
        )}
      </div>
    </div>
  )
}
