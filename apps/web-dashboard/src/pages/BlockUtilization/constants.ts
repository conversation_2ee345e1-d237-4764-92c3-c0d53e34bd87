export const PASSING_UTILIZATION_THRESHOLD = 65
export const WARNING_UTILIZATION_THRESHOLD = 60

export const LOW_TO_HIGH = 'lowToHigh'
export const HIGH_TO_LOW = 'highToLow'
export const ALPHABETICAL = 'alphabetical'

export const BY_WEEK = 'byWeek'
export const BY_DAY = 'byDay'
export const BY_DAY_OF_WEEK = 'byDayofWeek'

export const VALID_SITE_SORT_TYPES = [LOW_TO_HIGH, HIGH_TO_LOW, ALPHABETICAL]

export const VALID_BLOCK_SORT_TYPES = [BY_WEEK, BY_DAY, BY_DAY_OF_WEEK]

export const SITE_SORT_OPTIONS = [
  {
    label: 'Low to high',
    value: LOW_TO_HIGH,
  },
  {
    label: 'High to low',
    value: HIGH_TO_LOW,
  },
  {
    label: 'Alphabetical',
    value: ALPHABETICAL,
  },
]

export const BLOCK_SORT_OPTIONS = [
  {
    label: 'Day of week',
    value: BY_DAY_OF_WEEK,
  },
  {
    label: 'Week',
    value: BY_WEEK,
  },
  {
    label: 'Day',
    value: BY_DAY,
  },
]

export const FILE_NAME_DATE_FORMAT = 'yyyy-MM-dd'
export const CASE_DATE_FORMAT = 'MM/dd/yy'
