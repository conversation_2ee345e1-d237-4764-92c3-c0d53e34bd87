import { gql } from '@apollo/client'

export const GET_BLOCK_UTILIZATIONS = gql`
  query GetBlockUtilization($query: BlockUtilizationInput!) {
    blockUtilizations(query: $query) {
      date
      blockId
      utilizedSeconds
      availableSeconds
      totalBlockSeconds
      totalScheduledCaseSeconds
      casesForBlockDay {
        scheduledCase {
          status
        }
      }
      block {
        name
        surgeonIds
      }
    }
  }
`

export const GET_BLOCK_UTILIZATIONS_FOR_BLOCK_VIEW = gql`
  query GetBlockUtilizationForBlockView($query: BlockUtilizationInput!) {
    blockUtilizations(query: $query) {
      date
      blockId
      utilizedSeconds
      availableSeconds
      totalBlockSeconds
      totalScheduledCaseSeconds
      block {
        name
      }
      casesForBlockDay {
        caseId
        actualCaseSeconds
        utilizedCaseSeconds
        override {
          blockId
          utilizedTurnoverMinutes
          utilizedProcedureMinutes
        }
        scheduledCase {
          externalCaseId
          primaryCaseProcedures {
            procedure {
              name
            }
          }
          scheduledStartTime
          scheduledEndTime
          status
        }
      }
    }
  }
`

export const GET_BLOCK_INFO = gql`
  query GetBlockInfo($id: ID!) {
    block(id: $id) {
      name
    }
  }
`
