import { loader } from './BlockUtilizationForSite'

describe('BlockUtilizationForSite', () => {
  describe('loader', () => {
    const mockApolloClient = {
      query: vi.fn().mockResolvedValue({
        data: {
          sites: {
            edges: [{ node: { id: 'test-id-1' } }],
          },
        },
      }),
    }

    it('redirects to home page if feature flag is disabled', async () => {
      const initialUrl = 'https://example.com/block-utilization'
      const request = new Request(initialUrl)

      try {
        const context = { flags: { enableBlockUtilizationDashboard: false } }
        await loader({ request, params: {}, context }, context)
      } catch (error) {
        const thrown = error as Response
        expect(thrown).toBeInstanceOf(Response)
        expect(thrown.status).toBe(302)
        const expectedPathName = '/home'
        expect(thrown.headers.get('Location')).toBe(expectedPathName)
      }
    })

    it('add site selection for single site orgs', async () => {
      const initialUrl = 'https://example.com/block-utilization'
      const request = new Request(initialUrl)

      try {
        const context = {
          flags: { enableBlockUtilizationDashboard: true },
          apolloClient: mockApolloClient,
          user: {
            currentOrganization: { node: { name: 'test organization' } },
          },
        }
        await loader({ request, params: {}, context }, context)
      } catch (error) {
        const thrown = error as Response
        expect(thrown).toBeInstanceOf(Response)
        expect(thrown.status).toBe(302)
        const expectedPathName = '/block-utilization?site=test-id-1'
        expect(thrown.headers.get('Location')).toBe(expectedPathName)
      }
    })
  })
})
