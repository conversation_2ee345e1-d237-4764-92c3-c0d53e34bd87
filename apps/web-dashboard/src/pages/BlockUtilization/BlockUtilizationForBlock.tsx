import { Suspense, useMemo } from 'react'
import {
  Await,
  LoaderFunctionArgs,
  replace,
  useAsyncValue,
  useRouteLoaderData,
} from 'react-router'

import { remSpacing, Union } from '@apella/component-library'
import { parseParams } from '@apella/hooks'
import { EmptyState } from 'src/components/EmptyState'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { SecondaryView } from 'src/components/types'
import { defaultTimezone } from 'src/Contexts'
import { getRouteContext, LocationPath } from 'src/router/types'
import { logger } from 'src/utils/exceptionLogging'

import { BlockUtilizationErrorBoundary } from './BlockUtilizationErrorBoundary'
import { BlockUtilizationFilters } from './BlockUtilizationFilters'
import { CasesTable } from './CasesTable'
import { BLOCK_SORT_OPTIONS } from './constants'
import { DailyUtilizationTable } from './DailyUtilizationTable'
import { fetchBlock } from './fetchBlock'
import { fetchBlockUtilizationsForBlock } from './fetchBlockUtilizationsForBlock'
import { SummaryCard } from './SummaryCard'
import {
  BLOCK_SORT_TYPE,
  castParams,
  useBlockUtilizationFilters,
} from './useBlockUtilizationFilters'
import { UtilizationChart } from './UtilizationChart'
import { getColorPalette, secondsToMinutesAsString } from './utils'

export const loader = async (
  { request, params }: LoaderFunctionArgs,
  context: unknown
) => {
  const { flags, apolloClient, user } = getRouteContext(context)
  if (!flags.enableBlockUtilizationDashboard) {
    throw replace(LocationPath.Home)
  }
  const selectedBlockId = params.blockId
  const requestURL = new URL(request.url)
  const searchParams = parseParams(requestURL.search)
  const { selectedSiteId, selectedDateRange, selectedBlockSortType } =
    castParams(searchParams)
  if (
    !selectedSiteId ||
    !selectedDateRange ||
    !selectedBlockId ||
    !selectedBlockSortType
  ) {
    throw replace(LocationPath.BlockUtilization)
  }
  const { name: blockName } = await fetchBlock(apolloClient, {
    blockId: selectedBlockId,
  })
  if (!blockName) {
    logger.log(`Failed to get block name for blockId: ${selectedBlockId}`)
    throw replace(LocationPath.BlockUtilization)
  }

  return {
    blockUtilizationPromise: fetchBlockUtilizationsForBlock(apolloClient, {
      selectedBlockId,
      selectedDateRange,
      selectedSiteId,
      selectedBlockSortType,
      timezone:
        user.currentOrganization?.node.sites.edges[0]?.node.timezone ||
        defaultTimezone,
    }),
    organizationName: user.currentOrganization.node.name,
    currentSearchParams: requestURL.search.toString(),
    blockName,
  }
}

export const BlockUtilizationForBlockLayout = () => {
  const {
    blockUtilizationPromise,
    organizationName,
    currentSearchParams,
    blockName,
  } = useRouteLoaderData('blockUtilizationForBlock') as Awaited<
    ReturnType<typeof loader>
  >

  const breadcrumbs: SecondaryView[] = [
    {
      to: {
        pathname: LocationPath.BlockUtilization,
        search: currentSearchParams,
      },
      id: 'blockUtilization',
      display: organizationName ?? '',
    },
  ]
  return (
    <PageContentTemplate
      title={blockName}
      breadcrumbs={breadcrumbs}
      filters={
        <BlockUtilizationFilters title={blockName} hideSites verifyReport />
      }
    >
      <Suspense fallback={<LoadingOverlay />}>
        <Await
          errorElement={<BlockUtilizationErrorBoundary />}
          resolve={blockUtilizationPromise}
        >
          <BlockUtilizationForBlock blockName={blockName} />
        </Await>
      </Suspense>
    </PageContentTemplate>
  )
}
const BlockUtilizationForBlock = ({ blockName }: { blockName: string }) => {
  const {
    overallBlockUtilizationForBlock,
    blockUtilizations,
    dailyBlockUtilizations,
    casesForBlock,
  } = useAsyncValue() as Awaited<
    Awaited<ReturnType<typeof loader>>['blockUtilizationPromise']
  >
  const utilizationColorPalette = useMemo(() => {
    return getColorPalette(
      overallBlockUtilizationForBlock?.utilizationPercentage ?? 0
    )
  }, [overallBlockUtilizationForBlock])

  const { selectedBlockSortType } = useBlockUtilizationFilters()

  return overallBlockUtilizationForBlock ? (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.gutter,
        flexDirection: 'column',
      }}
    >
      <div
        css={{
          display: 'flex',
          gap: remSpacing.medium,
          justifyContent: 'space-evenly',
        }}
      >
        <SummaryCard
          value={`${overallBlockUtilizationForBlock.utilizationPercentage}%`}
          description="utilization"
          colorPalette={utilizationColorPalette}
          background={utilizationColorPalette.background}
        />
        <SummaryCard
          value={secondsToMinutesAsString(
            overallBlockUtilizationForBlock.totalAvailableSeconds
          )}
          description="available minutes"
        />
        <SummaryCard
          value={secondsToMinutesAsString(
            overallBlockUtilizationForBlock.totalCappedUtilizedSeconds
          )}
          description="utilized minutes"
        />
        <SummaryCard
          value={secondsToMinutesAsString(
            overallBlockUtilizationForBlock.totalBlockSeconds -
              overallBlockUtilizationForBlock.totalAvailableSeconds
          )}
          description="released minutes"
        />
      </div>
      <UtilizationChart
        blockUtilizations={blockUtilizations}
        totalCases={overallBlockUtilizationForBlock.totalCases}
        totalCancelledCases={
          overallBlockUtilizationForBlock.totalCancelledCases
        }
        selectedSortType={selectedBlockSortType}
        sortType={BLOCK_SORT_TYPE}
        sortOptions={BLOCK_SORT_OPTIONS}
      />
      <CasesTable casesForBlock={casesForBlock} blockName={blockName} />
      <DailyUtilizationTable
        blockName={blockName}
        blockUtilizations={dailyBlockUtilizations}
      />
    </div>
  ) : (
    <EmptyState
      message="No block data found"
      subtext="The selected date range doesn't appear to have block data"
      Icon={Union}
    />
  )
}
