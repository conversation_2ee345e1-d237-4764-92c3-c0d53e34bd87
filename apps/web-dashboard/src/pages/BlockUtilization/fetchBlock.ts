import { ApolloClient, NormalizedCacheObject } from '@apollo/client'

import { logger } from 'src/utils/exceptionLogging'

import { GetBlockInfo, GetBlockInfoVariables } from './__generated__'
import { GET_BLOCK_INFO } from './queries'

export const fetchBlock = async (
  client: ApolloClient<NormalizedCacheObject>,
  { blockId }: { blockId: string }
) => {
  const { data, error } = await client.query<
    GetBlockInfo,
    GetBlockInfoVariables
  >({
    query: GET_BLOCK_INFO,
    variables: {
      id: blockId,
    },
  })
  if (error) {
    logger.log(`Error getting Block Info: ${error.message}`)
    throw error
  }
  return {
    name: data.block?.name,
  }
}
