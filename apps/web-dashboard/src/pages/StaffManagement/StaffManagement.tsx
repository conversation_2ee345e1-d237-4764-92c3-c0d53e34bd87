import { useCallback, useMemo } from 'react'

import { useMutation, useQuery } from '@apollo/client'
import { debounce } from 'lodash'
import { DateTime } from 'luxon'

import { remSpacing } from '@apella/component-library'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { TimePeriod } from 'src/components/TimePeriodFilter'
import { useCurrentUser } from 'src/modules/user/hooks'
import {
  ScheduleFilterProviderContext,
  useScheduleFilterContext,
} from 'src/pages/Schedule/ScheduleFilterContext'
import { ScheduleFilters } from 'src/pages/Schedule/ScheduleFilters'
import SchedulePageHeader from 'src/pages/Schedule/SchedulePageHeader'
import { dateTimeToUrlFriendlyDate } from 'src/utils/urlFriendlyDate'
import { useCurrentMinute } from 'src/utils/useCurrentTime'

import { useTimezone } from '../../Contexts'
import { EVENTS, useAnalyticsEventLogger } from '../../utils/analyticsEvents'
import {
  ScheduleProvider,
  useScheduleContext,
} from '../Schedule/ScheduleContextProvider'
import { ScheduleFilterProvider } from '../Schedule/ScheduleFilterProvider'
import {
  GetStaffingNeedsData,
  GetStaffingNeedsDataVariables,
} from './__generated__'
import { GET_STAFFING_NEEDS_DATA, SET_STAFFING_NEEDS_RATIO } from './queries'
import StaffManagementTable from './StaffManagementTable'

const getTimePeriodFromBounds = (
  minHour: DateTime,
  maxHour: DateTime,
  timezone: string
): TimePeriod<string> => {
  return {
    min: minHour.setZone(timezone).startOf('hour').toISOTime({
      includeOffset: false,
    }),
    max: maxHour
      .setZone(timezone)
      .minus({ minutes: 30 })
      .startOf('hour')
      .toISOTime({
        includeOffset: false,
      }),
  }
}

const StaffManagementPage = (): React.JSX.Element => {
  // Global state
  const { timezone } = useTimezone()
  const eventsLogger = useAnalyticsEventLogger()
  const currentMinute = useCurrentMinute()
  const { currentOrganization } = useCurrentUser()
  const orgId = currentOrganization?.node?.id

  const { siteIds, roomIds, minTime, maxTime } = useScheduleFilterContext()

  const { timelineHourBounds: hourBounds, timelineState } = useScheduleContext()

  const timePeriod = hourBounds
    ? getTimePeriodFromBounds(
        hourBounds.minDateTime,
        hourBounds.maxDateTime,
        timezone
      )
    : undefined

  // Apollo
  const { data: staffingNeedsData } = useQuery<
    GetStaffingNeedsData,
    GetStaffingNeedsDataVariables
  >(GET_STAFFING_NEEDS_DATA, {
    variables: { siteIds },
  })
  const [setStaffingRatiosMutationFn] = useMutation(SET_STAFFING_NEEDS_RATIO)

  const staffingNeedsRoles = staffingNeedsData?.staffingNeedsRoles ?? []

  // Setters
  const syncToBackend = useCallback(
    (siteId: string, staffRoleId: string, ratio: number | undefined) => {
      const loggingState = {
        date: dateTimeToUrlFriendlyDate(DateTime.fromISO(minTime)),
        siteId,
        roomIds: JSON.stringify(roomIds),
        minHour: hourBounds?.minDateTime,
        maxHour: hourBounds?.maxDateTime,
        staffRoleId: ratio,
      }

      setStaffingRatiosMutationFn({
        variables: {
          staffingNeedsRatioCreateInput: {
            id: crypto.randomUUID(),
            organizationId: orgId,
            ratio,
            staffRoleId,
            siteId,
          },
        },
      })

      const newState = { ...loggingState, [staffRoleId]: ratio }
      eventsLogger(EVENTS.STAFF_MANAGEMENT_CHANGE_RATIO, newState)
    },
    [
      roomIds,
      setStaffingRatiosMutationFn,
      eventsLogger,
      orgId,
      minTime,
      hourBounds?.minDateTime,
      hourBounds?.maxDateTime,
    ]
  )

  const syncToBackendDebounced = useMemo(
    () => debounce(syncToBackend, 1000),
    [syncToBackend]
  )

  const isTileDisabled = useCallback(
    ({ date }: { date: Date }) =>
      DateTime.fromJSDate(date) > currentMinute.plus({ days: 7 }).endOf('day'),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [currentMinute.day]
  )

  return (
    <PageContentTemplate>
      <SchedulePageHeader selectedSubNavId="staff" />
      <ScheduleFilters
        showToggleScheduledCases={false}
        showSurgeonFilter={false}
        dateChangeOptions={{ tileDisabled: isTileDisabled }}
        showTimePeriodFilter
      />
      <div
        css={{
          display: 'grid',
          gridTemplateColumns: 'minmax(0, 1fr)',
          gap: remSpacing.gutter,
        }}
      >
        {!!timePeriod &&
          siteIds
            ?.sort()
            .map((siteId) => (
              <StaffManagementTable
                key={siteId}
                siteId={siteId}
                minTime={minTime}
                maxTime={maxTime}
                timePeriod={timePeriod}
                timelineState={timelineState}
                staffRoles={staffingNeedsRoles}
                syncToBackend={syncToBackendDebounced}
                initialStaffingNeeds={
                  staffingNeedsData?.sites.edges.find(
                    ({ node }) => node.id === siteId
                  )?.node.staffingNeedsRatios
                }
              />
            ))}
      </div>
    </PageContentTemplate>
  )
}

const StaffManagement = () => {
  return (
    <ScheduleFilterProvider
      context={ScheduleFilterProviderContext.STAFF_MANAGEMENT}
    >
      <ScheduleProvider>
        <StaffManagementPage />
      </ScheduleProvider>
    </ScheduleFilterProvider>
  )
}
export default StaffManagement
