import {
  Blade,
  H6,
  InputNumber,
  People,
  remSpacing,
} from '@apella/component-library'

import { GetStaffingNeedsData } from './__generated__'
import { StaffRatios } from './types'

export interface StaffManagementBladeProps {
  isOpen: boolean
  onChangeStaffRatio: (staffRoleId: string, ratio: number | undefined) => void
  onClose: () => void
  siteName?: string
  staffRatios: StaffRatios
  staffRoles: GetStaffingNeedsData['staffingNeedsRoles']
}

const StaffManagementBlade = ({
  isOpen,
  onClose,
  staffRoles,
  staffRatios,
  onChangeStaffRatio,
  siteName,
}: StaffManagementBladeProps) => {
  return (
    <Blade
      isOpen={isOpen}
      onClose={onClose}
      size={'xs'}
      side={'right'}
      overlay={true}
    >
      <Blade.Header>
        <Blade.Title title="Staff Ratios" icon={<People size={'xs'} />} />
        <Blade.CloseButton onClose={onClose} />
      </Blade.Header>

      <div
        css={{
          display: 'grid',
          gridTemplateColumns: 'minmax(0, 1fr)',
          gap: remSpacing.xxsmall,
          padding: remSpacing.xsmall,
        }}
      >
        <H6 as="h3">{siteName}</H6>
        {staffRoles.map((role) => (
          <InputNumber
            key={role.id}
            name={role.id}
            label={`${role.name}s needed per room`}
            value={staffRatios[role.id]}
            onChange={(newNum) => onChangeStaffRatio(role.id, newNum)}
            min={0}
            step={0.1}
          />
        ))}
      </div>
    </Blade>
  )
}

export default StaffManagementBlade
