import { gql } from '@apollo/client'

export const GET_STAFFING_NEEDS_DATA = gql`
  query GetStaffingNeedsData($siteIds: [String!]) {
    staffingNeedsRoles {
      id
      name
    }
    sites(siteIds: $siteIds) {
      edges {
        node {
          id
          staffingNeedsRatios {
            staffRole {
              id
            }
            ratio
          }
        }
      }
    }
  }
`

export const SET_STAFFING_NEEDS_RATIO = gql`
  mutation SetStaffingNeedsRatio(
    $staffingNeedsRatioCreateInput: StaffingNeedsRatioCreateInput
  ) {
    staffingNeedsRatioCreate(input: $staffingNeedsRatioCreateInput) {
      success
    }
  }
`
