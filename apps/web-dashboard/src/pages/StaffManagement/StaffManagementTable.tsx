import { useCallback, useEffect, useMemo, useState } from 'react'

import { css, useTheme } from '@emotion/react'

import { chain, DebouncedFunc, flatMap } from 'lodash'
import { DateTime } from 'luxon'

import {
  <PERSON>ton,
  Caps1,
  <PERSON>lex<PERSON>ontainer,
  H3,
  H4,
  P2,
  P3,
  remSpacing,
  Settings,
  shape,
  Span3,
} from '@apella/component-library'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { TimePeriod } from 'src/components/TimePeriodFilter'
import { useTimezone } from 'src/Contexts'

import { useSiteName } from '../../utils/hooks'
import { TimelineState } from '../Schedule/useTimelineState'
import { GetStaffingNeedsData } from './__generated__'
import StaffManagementBlade, {
  StaffManagementBladeProps,
} from './StaffManagementBlade'
import { StaffRatios } from './types'

interface StaffManagementTableProps
  extends Pick<StaffManagementBladeProps, 'staffRoles'> {
  initialStaffingNeeds?: GetStaffingNeedsData['sites']['edges'][number]['node']['staffingNeedsRatios']
  maxTime: string
  minTime: string
  siteId: string
  syncToBackend: DebouncedFunc<
    (siteId: string, staffRoleId: string, ratio: number | undefined) => void
  >
  timelineState: TimelineState
  timePeriod: TimePeriod<string>
}

const StaffManagementTable = ({
  siteId,
  minTime,
  maxTime,
  timePeriod,
  timelineState,
  staffRoles,
  syncToBackend,
  initialStaffingNeeds,
}: StaffManagementTableProps): React.JSX.Element => {
  const { timezone } = useTimezone()
  const theme = useTheme()
  const [isBladeOpen, setIsBladeOpen] = useState(false)
  const [staffRatios, setStaffRatios] = useState<StaffRatios>({})
  const siteName = useSiteName(siteId)

  useEffect(() => {
    if (!initialStaffingNeeds) {
      return
    }

    setStaffRatios(
      chain(initialStaffingNeeds)
        .filter(Boolean)
        .keyBy((r) => r.staffRole.id)
        .mapValues((v) => v?.ratio ?? undefined)
        .value()
    )
  }, [setStaffRatios, initialStaffingNeeds])

  const onChangeStaffRatio = useCallback(
    (staffRoleId: string, ratio: number | undefined) => {
      setStaffRatios({
        ...staffRatios,
        [staffRoleId]: ratio,
      })
      syncToBackend(siteId, staffRoleId, ratio)
    },
    [staffRatios, syncToBackend, siteId]
  )

  const minTimeDT = DateTime.fromISO(minTime)
    .setZone(timezone)
    .set({
      hour: DateTime.fromISO(timePeriod.min).hour,
    })
  const maxTimeDT = DateTime.fromISO(maxTime)
    .setZone(timezone)
    .set({
      hour: DateTime.fromISO(timePeriod.max).hour,
    })

  const hours = useMemo(() => {
    const result = []
    let workingTime = minTimeDT

    while (workingTime <= maxTimeDT) {
      result.push(workingTime.hour)
      workingTime = workingTime.plus({ hours: 1 })
    }

    return result
  }, [minTimeDT, maxTimeDT])

  const openRooms = useMemo(() => {
    const result: Record<string, number> = {}

    const siteRooms = timelineState.rooms.filter((r) => r.site.id === siteId)
    const apellaCases = flatMap(siteRooms, (r) => r.cases)

    hours.forEach((hour) => {
      const startDT = minTimeDT.set({ hour })
      const endDT = startDT.endOf('hour')

      // sum up all phases that overlap this hour
      result[`${hour}`] = chain(apellaCases)
        .filter(
          (p) =>
            p.endTime !== undefined &&
            p.endTime >= startDT &&
            p.startTime <= endDT
        )
        .uniqBy((p) => p.room.id)
        .value().length
    })

    return result
  }, [hours, timelineState.rooms, siteId, minTimeDT])

  const tableRow = css`
    border-bottom: 1px solid ${theme.palette.gray[30]};
  `

  const toggleBladeOpen = useCallback(
    () => setIsBladeOpen(!isBladeOpen),
    [isBladeOpen]
  )

  return (
    <div css={{ display: 'grid', gap: remSpacing.small }}>
      {timelineState.isLoading && <LoadingOverlay />}
      <StaffManagementBlade
        isOpen={isBladeOpen}
        onClose={toggleBladeOpen}
        staffRatios={staffRatios}
        staffRoles={staffRoles}
        onChangeStaffRatio={onChangeStaffRatio}
        siteName={siteName}
      />
      <H4>{siteName}</H4>
      <div
        css={{
          position: 'relative',
          overflowX: 'auto',
          margin: `${remSpacing.small} 0`,
        }}
      >
        <table css={{ width: '100%', textAlign: 'center' }}>
          <thead>
            <tr css={tableRow}>
              <th
                css={{
                  width: 200,
                  position: 'sticky',
                  left: 0,
                  background: theme.palette.background.primary,
                }}
              />
              {hours.map((h) => (
                <th key={h} css={{ padding: `${remSpacing.xsmall}` }}>
                  <Span3 css={{ color: theme.palette.gray[50] }}>{h}:00</Span3>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            <tr css={tableRow}>
              <td
                css={{
                  textAlign: 'left',
                  position: 'sticky',
                  left: 0,
                  background: theme.palette.background.primary,
                  paddingRight: remSpacing.xsmall,
                }}
              >
                <Caps1>Open rooms</Caps1>
              </td>
              {hours.map((h) => (
                <td
                  key={h}
                  css={{
                    padding: remSpacing.xsmall,
                    borderLeft: `1px dashed ${theme.palette.gray[30]}`,
                  }}
                >
                  <H3
                    css={{
                      background: theme.palette.gray[10],
                      padding: `${remSpacing.large} 0`,
                      borderRadius: shape.borderRadiusPx.xsmall,
                    }}
                  >
                    {timelineState.isLoading ? '-' : openRooms[h]}
                  </H3>
                </td>
              ))}
            </tr>
            {staffRoles.map((role) => (
              <tr css={tableRow} key={role.id}>
                <td
                  css={{
                    textAlign: 'left',
                    verticalAlign: 'middle',
                    position: 'sticky',
                    left: 0,
                    background: theme.palette.background.primary,
                    paddingRight: remSpacing.xsmall,
                  }}
                >
                  <Caps1>{role.name}</Caps1>
                  <FlexContainer alignItems={'center'}>
                    <StaffManagementRatioText ratio={staffRatios[role.id]} />
                    <Button
                      appearance={'link'}
                      color={'alternate'}
                      onClick={toggleBladeOpen}
                    >
                      <Settings size={'xs'} color={theme.palette.gray[40]} />
                    </Button>
                  </FlexContainer>
                </td>
                {hours.map((h) => (
                  <td
                    key={h}
                    css={{
                      padding: remSpacing.xsmall,
                      borderLeft: `1px dashed ${theme.palette.gray[30]}`,
                    }}
                  >
                    <P2 css={{ padding: `${remSpacing.medium} 0` }}>
                      {timelineState.isLoading ||
                      staffRatios[role.id] === undefined
                        ? '-'
                        : Math.ceil(staffRatios[role.id]! * openRooms[h])}
                    </P2>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

interface StaffManagementRatioTextProps {
  ratio?: number
}

const StaffManagementRatioText = ({ ratio }: StaffManagementRatioTextProps) => {
  const theme = useTheme()
  return (
    <div>
      {ratio ? (
        <P3 css={{ color: theme.palette.gray[60] }}>
          Need{' '}
          <span css={{ color: theme.palette.text.secondary }}>{ratio}</span> per
          room
        </P3>
      ) : (
        <P3
          css={{
            fontStyle: 'italic',
            display: 'inline-block',
            color: theme.palette.gray[40],
          }}
        >
          No room staffing set{' '}
        </P3>
      )}
    </div>
  )
}

export default StaffManagementTable
