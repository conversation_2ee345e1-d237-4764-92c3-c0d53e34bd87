import { useCallback, useEffect, useState } from 'react'
import { useParams } from 'react-router'

import { chunk } from 'lodash'

import {
  Button,
  ChevronLeft,
  ChevronRight,
  fontWeights,
} from '@apella/component-library'
import { BoardViewType } from 'src/__generated__/globalTypes'
import { useTimezone } from 'src/Contexts'
import { BigBoardView } from 'src/modules/board/components/BigBoardView'
import { Footer } from 'src/modules/board/components/Footer'
import { Header } from 'src/modules/board/components/Header'
import { screenSizes } from 'src/modules/board/constants'
import {
  BoardProvider,
  useBoardContext,
} from 'src/modules/board/hooks/BoardContextProvider'
import { useBigBoardState } from 'src/modules/board/hooks/useBigBoardState'
import { useBoardZoom } from 'src/modules/board/hooks/useBoardZoom'
import { useSwipeEvent } from 'src/modules/board/hooks/useSwipeEvent'
import { pxToVh } from 'src/modules/board/pxToVh'
import { pxToVw, useFontSizes } from 'src/modules/board/styles'
import { useAppRefreshContext } from 'src/router/components/AppRefresh'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { useCurrentMinute } from 'src/utils/useCurrentTime'

import { useBoardConfig } from '../../modules/board/hooks/useBoardConfigsState'

type BoardParams = {
  boardId: string
}

export const BoardView = () => {
  const { boardId = '' } = useParams<BoardParams>()

  const { boardConfig, isLoading } = useBoardConfig({ boardId })
  const eventLogger = useAnalyticsEventLogger()
  useEffect(() => {
    if (boardConfig && !isLoading) {
      const { name, siteId, roomIds } = boardConfig
      const rooms = roomIds?.length ?? 0
      eventLogger(EVENTS.BOARD_VIEWED, {
        boardName: name,
        rooms,
        siteId,
      })
    }
    return () => {}
  }, [boardConfig, isLoading, eventLogger])

  return (
    <BoardProvider config={boardConfig}>
      <Board />
    </BoardProvider>
  )
}

const Board = () => {
  const { timezone } = useTimezone()

  const {
    boardConstants: { headerHeight, footerHeight },
    config,
  } = useBoardContext()

  const swipeDirection = useSwipeEvent()

  const now = useCurrentMinute().setZone(timezone)
  const {
    name,
    siteId,
    roomIds,
    roomsPerPage,
    pageDurationSeconds: pageDuration,
    boardViewType,
  } = config

  const { zoom } = useBoardZoom({
    zoomMultiplier: 1,
  })

  const fontSizes = useFontSizes({ zoom })

  const { refresh } = useAppRefreshContext()

  useEffect(() => {
    if (refresh) {
      window.location.reload()
    }
  }, [refresh])
  // We need to update our days when the time rolls past midnight, but anchoring it to the start/end of days allows us to use polling
  // as min/max times only change once at midnight and not every minute. Also expanding the range to 3 days in total ensures that we
  // can always support our look back and forward times relatively safely. This is a fairly naive approach to optimizing
  // polling and overflow data, but for now it works
  const minTime =
    boardViewType === BoardViewType.TILE
      ? now.startOf('day').toISO()
      : now.minus({ days: 1 }).startOf('day').toISO()
  const maxTime =
    boardViewType === BoardViewType.TILE
      ? now.plus({ days: 1 }).startOf('day').toISO()
      : now.plus({ days: 2 }).endOf('day').toISO()

  const { data, isLoading } = useBigBoardState({
    minTime,
    maxTime,
    siteIds: siteId ? [siteId] : [],
    roomIds: roomIds ?? [],
  })
  const [currPage, setCurrPage] = useState(0)
  const rooms = data.flatMap((bb) => bb.rooms)
  const numRooms = rooms.length

  const pages = chunk(rooms, roomsPerPage)
  const numPages = pages.length

  const firstShownRoomIdx = Math.floor(currPage * roomsPerPage)
  const lastShownRoomIdx = (currPage + 1) * roomsPerPage

  const goToNextPage = useCallback(() => {
    setCurrPage((currPage) => (currPage + 1 >= numPages ? 0 : currPage + 1))
  }, [numPages])

  const goToPrevPage = useCallback(() => {
    setCurrPage((currPage) => (currPage - 1 < 0 ? numPages - 1 : currPage - 1))
  }, [numPages])

  useEffect(() => {
    if (swipeDirection === 'left') {
      goToNextPage()
    }

    if (swipeDirection === 'right') {
      goToPrevPage()
    }
  }, [goToNextPage, goToPrevPage, swipeDirection])

  const currentPageText =
    numRooms > roomsPerPage ? (
      <>
        <Button
          size="sm"
          color="alternate"
          onClick={goToPrevPage}
          css={{ padding: pxToVw({ pixels: 4 }) }}
        >
          <ChevronLeft size={fontSizes.t2000.fontSize} />
        </Button>
        <p css={{ ...fontSizes.t1500 }}>
          Showing rooms{' '}
          <span css={{ ...fontWeights.semibold }}>
            {firstShownRoomIdx + 1}-
            {Math.floor(Math.min(lastShownRoomIdx, numRooms))}
          </span>{' '}
          of <span css={{ ...fontWeights.semibold }}>{numRooms}</span>
        </p>
        <Button
          size="sm"
          color="alternate"
          onClick={goToNextPage}
          css={{ padding: pxToVw({ pixels: 4 }) }}
        >
          <ChevronRight size={fontSizes.t2000.fontSize} />
        </Button>
      </>
    ) : undefined

  return (
    <div
      css={{
        display: 'grid',
        gridTemplateRows: `${pxToVh({ pixels: headerHeight })} ${pxToVh({
          pixels: screenSizes.fourK.height - headerHeight - footerHeight,
        })} ${pxToVh({
          pixels: footerHeight,
        })}`,
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      <Header boardName={name} siteId={siteId} roomIds={roomIds} />
      <BigBoardView
        rooms={pages[currPage] ?? []}
        minTime={minTime}
        maxTime={maxTime}
        isLoading={isLoading}
        boardViewType={boardViewType}
      />
      <Footer
        goToNextPage={goToNextPage}
        currentPageText={currentPageText}
        pageDuration={pageDuration}
        currentPage={currPage}
      />
    </div>
  )
}
