export const TimelineSVG = () => {
  return (
    <svg viewBox="0 0 139 94" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="139" height="94" fill="white" />
      <rect
        y="9"
        width="81"
        height="19"
        rx="2"
        fill="black"
        fillOpacity="0.03"
      />
      <rect
        x="0.5"
        y="9.5"
        width="80"
        height="18"
        rx="1.5"
        stroke="black"
        strokeOpacity="0.1"
      />
      <rect
        x="25"
        y="38.2344"
        width="82"
        height="18.708"
        rx="2"
        fill="black"
        fillOpacity="0.03"
      />
      <rect
        x="25.5"
        y="38.7344"
        width="81"
        height="17.708"
        rx="1.5"
        stroke="black"
        strokeOpacity="0.1"
      />
      <rect
        x="58"
        y="66"
        width="81"
        height="19"
        rx="2"
        fill="black"
        fillOpacity="0.03"
      />
      <rect
        x="58.5"
        y="66.5"
        width="80"
        height="18"
        rx="1.5"
        stroke="black"
        strokeOpacity="0.1"
      />
    </svg>
  )
}

export const TileSVG = () => {
  return (
    <svg viewBox="0 0 139 94" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_4393_65400)">
        <rect width="139" height="94" fill="white" />
        <rect
          width="52"
          height="28.4486"
          rx="2"
          fill="black"
          fillOpacity="0.03"
        />
        <rect
          x="0.5"
          y="0.5"
          width="51"
          height="27.4486"
          rx="1.5"
          stroke="black"
          strokeOpacity="0.1"
        />
        <rect
          y="32.1562"
          width="52"
          height="29.6855"
          rx="2"
          fill="black"
          fillOpacity="0.03"
        />
        <rect
          x="0.5"
          y="32.6562"
          width="51"
          height="28.6855"
          rx="1.5"
          stroke="black"
          strokeOpacity="0.1"
        />
        <rect
          y="65.5469"
          width="52"
          height="28.4486"
          rx="2"
          fill="black"
          fillOpacity="0.03"
        />
        <rect
          x="0.5"
          y="66.0469"
          width="51"
          height="27.4486"
          rx="1.5"
          stroke="black"
          strokeOpacity="0.1"
        />
        <rect x="57" width="39" height="28.4486" rx="2" fill="white" />
        <rect
          x="57.5"
          y="0.5"
          width="38"
          height="27.4486"
          rx="1.5"
          stroke="black"
          strokeOpacity="0.1"
        />
        <rect x="100" width="39" height="28.4486" rx="2" fill="white" />
        <rect
          x="100.5"
          y="0.5"
          width="38"
          height="27.4486"
          rx="1.5"
          stroke="black"
          strokeOpacity="0.1"
        />
        <rect
          x="57"
          y="32.1562"
          width="39"
          height="29.6855"
          rx="2"
          fill="white"
        />
        <rect
          x="57.5"
          y="32.6562"
          width="38"
          height="28.6855"
          rx="1.5"
          stroke="black"
          strokeOpacity="0.1"
        />
        <rect
          x="100"
          y="32.1562"
          width="39"
          height="29.6855"
          rx="2"
          fill="white"
        />
        <rect
          x="100.5"
          y="32.6562"
          width="38"
          height="28.6855"
          rx="1.5"
          stroke="black"
          strokeOpacity="0.1"
        />
        <rect
          x="57"
          y="65.5469"
          width="39"
          height="28.4486"
          rx="2"
          fill="white"
        />
        <rect
          x="57.5"
          y="66.0469"
          width="38"
          height="27.4486"
          rx="1.5"
          stroke="black"
          strokeOpacity="0.1"
        />
        <rect
          x="100"
          y="65.5469"
          width="39"
          height="28.4486"
          rx="2"
          fill="white"
        />
        <rect
          x="100.5"
          y="66.0469"
          width="38"
          height="27.4486"
          rx="1.5"
          stroke="black"
          strokeOpacity="0.1"
        />
      </g>
      <defs>
        <clipPath id="clip0_4393_65400">
          <rect width="139" height="94" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export const CustomSVG = () => {
  return (
    <svg viewBox="0 0 111 104" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="111" height="32" rx="1" fill="white" />
      <rect width="111" height="32" rx="1" fill="#F6F6F7" fillOpacity="0.5" />
      <rect
        x="0.5"
        y="0.5"
        width="110"
        height="31"
        rx="0.5"
        stroke="black"
        strokeOpacity="0.1"
      />
      <path
        d="M0 1C0 0.447715 0.447715 0 1 0H48V32H0.999999C0.447714 32 0 31.5523 0 31V1Z"
        fill="black"
        fillOpacity="0.05"
      />
      <path
        d="M1 0.5H47.5V31.5H0.999999C0.723857 31.5 0.5 31.2761 0.5 31V1C0.5 0.723858 0.723858 0.5 1 0.5Z"
        stroke="black"
        strokeOpacity="0.1"
      />
      <path
        d="M0 24C0 23.4477 0.447715 23 1 23H48V32H0.999999C0.447714 32 0 31.5523 0 31V24Z"
        fill="black"
        fillOpacity="0.05"
      />
      <path
        d="M1 23.5H47.5V31.5H0.999999C0.723857 31.5 0.5 31.2761 0.5 31V24C0.5 23.7239 0.723858 23.5 1 23.5Z"
        stroke="black"
        strokeOpacity="0.1"
      />
      <rect x="50" y="3" width="9" height="1" fill="#D9D9D9" />
      <rect x="106" y="3" width="2" height="1" fill="#D9D9D9" />
      <rect x="103" y="3" width="2" height="1" fill="#D9D9D9" />
      <rect x="100" y="3" width="2" height="1" fill="#D9D9D9" />
      <rect x="50" y="14" width="58" height="1" fill="#D9D9D9" />
      <rect x="50" y="16" width="18" height="1" fill="#D9D9D9" />
      <rect x="50" y="24" width="58" height="1" fill="#D9D9D9" />
      <rect x="50" y="26" width="58" height="1" fill="#D9D9D9" />
      <rect x="50" y="28" width="35" height="1" fill="#D9D9D9" />
      <rect
        x="50"
        y="6"
        width="31"
        height="2"
        fill="black"
        fillOpacity="0.25"
      />
      <rect
        x="50"
        y="10"
        width="13"
        height="1"
        fill="black"
        fillOpacity="0.1"
      />
      <rect
        x="50"
        y="20"
        width="13"
        height="2"
        fill="black"
        fillOpacity="0.1"
      />
      <rect
        x="65"
        y="10"
        width="13"
        height="1"
        fill="black"
        fillOpacity="0.1"
      />
      <rect
        x="81"
        y="10"
        width="12"
        height="1"
        fill="black"
        fillOpacity="0.1"
      />
      <rect
        x="96"
        y="10"
        width="12"
        height="1"
        fill="black"
        fillOpacity="0.1"
      />
      <rect y="36" width="111" height="32" rx="1" fill="white" />
      <rect
        y="36"
        width="111"
        height="32"
        rx="1"
        fill="#F6F6F7"
        fillOpacity="0.5"
      />
      <rect
        x="0.5"
        y="36.5"
        width="110"
        height="31"
        rx="0.5"
        stroke="black"
        strokeOpacity="0.1"
      />
      <path
        d="M0 37C0 36.4477 0.447715 36 1 36H48V68H0.999999C0.447714 68 0 67.5523 0 67V37Z"
        fill="black"
        fillOpacity="0.05"
      />
      <path
        d="M1 36.5H47.5V67.5H0.999999C0.723857 67.5 0.5 67.2761 0.5 67V37C0.5 36.7239 0.723858 36.5 1 36.5Z"
        stroke="black"
        strokeOpacity="0.1"
      />
      <path
        d="M0 60C0 59.4477 0.447715 59 1 59H48V68H0.999999C0.447714 68 0 67.5523 0 67V60Z"
        fill="black"
        fillOpacity="0.05"
      />
      <path
        d="M1 59.5H47.5V67.5H0.999999C0.723857 67.5 0.5 67.2761 0.5 67V60C0.5 59.7239 0.723858 59.5 1 59.5Z"
        stroke="black"
        strokeOpacity="0.1"
      />
      <rect x="50" y="39" width="9" height="1" fill="#D9D9D9" />
      <rect x="106" y="39" width="2" height="1" fill="#D9D9D9" />
      <rect x="103" y="39" width="2" height="1" fill="#D9D9D9" />
      <rect x="100" y="39" width="2" height="1" fill="#D9D9D9" />
      <rect x="50" y="50" width="58" height="1" fill="#D9D9D9" />
      <rect x="50" y="52" width="18" height="1" fill="#D9D9D9" />
      <rect x="50" y="60" width="58" height="1" fill="#D9D9D9" />
      <rect x="50" y="62" width="58" height="1" fill="#D9D9D9" />
      <rect x="50" y="64" width="35" height="1" fill="#D9D9D9" />
      <rect
        x="50"
        y="42"
        width="31"
        height="2"
        fill="black"
        fillOpacity="0.25"
      />
      <rect x="63" y="87" width="12" height="2" fill="#BFBFBF" />
      <rect x="35" y="87" width="12" height="2" fill="#BFBFBF" />
      <rect
        x="42"
        y="82"
        width="12"
        height="2"
        transform="rotate(90 42 82)"
        fill="#BFBFBF"
      />
      <rect
        x="50"
        y="46"
        width="13"
        height="1"
        fill="black"
        fillOpacity="0.1"
      />
      <rect
        x="50"
        y="56"
        width="13"
        height="2"
        fill="black"
        fillOpacity="0.1"
      />
      <rect
        x="65"
        y="46"
        width="13"
        height="1"
        fill="black"
        fillOpacity="0.1"
      />
      <rect
        x="81"
        y="46"
        width="12"
        height="1"
        fill="black"
        fillOpacity="0.1"
      />
      <rect
        x="96"
        y="46"
        width="12"
        height="1"
        fill="black"
        fillOpacity="0.1"
      />
      <rect
        x="0.5"
        y="72.5"
        width="110"
        height="31"
        rx="0.5"
        stroke="#999999"
        strokeDasharray="2 2"
      />
    </svg>
  )
}
