import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router'

import { ThemeProvider } from '@emotion/react'

import { InMemoryCache } from '@apollo/client'
import { MockedProvider } from '@apollo/client/testing'
import { render, screen, waitFor } from '@testing-library/react'

import { theme } from '@apella/component-library'
import { Boards } from 'src/pages/Boards/Boards'

import {
  BoardsMockMultipleSites,
  BoardsMockMultipleSitesWithoutWritePermission,
  BoardsMockSingleSite,
  BoardsMockSingleSiteMultipleConfigWithoutWritePermission,
  BoardsMockSingleSiteNoParam,
  BoardsMockSingleSiteSingleConfigWithoutWritePermission,
  MockBoardConfig,
} from '../test_mocks/Boards.mock'

vi.mock('@auth0/auth0-react', () => ({
  useAuth0: vi.fn(() => ({
    user: {
      org_id: 'apella_internal_0',
    },
  })),
}))

const cache = new InMemoryCache()

const navigate = vi.fn()

vi.mock('react-router', async (importOriginal) => ({
  _esModule: true,
  ...(await importOriginal<typeof import('react-router')>()),
  useNavigate: () => navigate,
}))

describe('Boards page', () => {
  beforeAll(() => {
    Object.defineProperty(globalThis, 'crypto', {
      value: {
        randomUUID: () => '00000000-0000-0000-0000-000000000000',
      },
    })
  })

  beforeEach(() => {
    vi.resetModules()
    navigate.mockClear()

    const location = {
      ...window.location,
      search: '',
    }
    Object.defineProperty(window, 'location', {
      writable: true,
      value: location,
    })
  })

  afterAll(() => {
    vi.resetModules()
  })

  describe('for user with write permissions', () => {
    it('renders a list of sites with links to view boards when multiple sites exist', async () => {
      render(
        <MockedProvider
          mocks={BoardsMockMultipleSites}
          addTypename={true}
          cache={cache}
          defaultOptions={{
            watchQuery: {
              fetchPolicy: 'no-cache',
            },
          }}
        >
          <ThemeProvider theme={theme}>
            <Boards />
          </ThemeProvider>
        </MockedProvider>,
        { wrapper: BrowserRouter }
      )

      expect(await screen.findAllByText('Boards')).toHaveLength(2)
      expect(screen.getByText('Lab 1').closest('a')).toHaveAttribute(
        'href',
        '/boards?siteId=lab_1'
      )
      expect(screen.getByText('Lab 2').closest('a')).toHaveAttribute(
        'href',
        '/boards?siteId=lab_2'
      )

      const allViewBoards = await screen.findAllByText('View Boards')

      allViewBoards.forEach((vb) => expect(vb).toHaveAttribute('href'))
    })

    it('renders a list of board configs with links to view configured boards and create board when single site exists', async () => {
      const location = {
        ...window.location,
        search: `siteId=lab_1`,
      }
      Object.defineProperty(window, 'location', {
        writable: true,
        value: location,
      })
      render(
        <MockedProvider
          mocks={BoardsMockSingleSite}
          addTypename={true}
          cache={cache}
          defaultOptions={{
            watchQuery: {
              fetchPolicy: 'no-cache',
            },
          }}
        >
          <ThemeProvider theme={theme}>
            <Boards />
          </ThemeProvider>
        </MockedProvider>,
        { wrapper: BrowserRouter }
      )

      expect(await screen.findAllByText('Boards')).toHaveLength(2)
      expect(await screen.findByText('Create board')).toBeInTheDocument()
      const allViewBoards = await screen.findAllByText('View Board')

      allViewBoards.forEach((vb) => expect(vb).toHaveAttribute('href'))
    })

    it('redirects to site when only a single site exists and url param is not set', async () => {
      const location = {
        ...window.location,
      }
      Object.defineProperty(window, 'location', {
        writable: true,
        value: location,
      })
      render(
        <MockedProvider
          mocks={BoardsMockSingleSiteNoParam}
          addTypename={true}
          cache={cache}
          defaultOptions={{
            watchQuery: {
              fetchPolicy: 'no-cache',
            },
          }}
        >
          <ThemeProvider theme={theme}>
            <Boards />
          </ThemeProvider>
        </MockedProvider>,
        { wrapper: BrowserRouter }
      )

      await waitFor(() => {
        expect(navigate).toHaveBeenCalledWith({
          pathname: '/boards',
          search: 'siteId=lab_1',
        })
      })
    })
  })

  describe('for user without write permissions', () => {
    it('renders a list of sites with links to view boards when multiple sites exist', async () => {
      render(
        <MockedProvider
          mocks={BoardsMockMultipleSitesWithoutWritePermission}
          addTypename={true}
          cache={cache}
          defaultOptions={{
            watchQuery: {
              fetchPolicy: 'no-cache',
            },
          }}
        >
          <ThemeProvider theme={theme}>
            <Boards />
          </ThemeProvider>
        </MockedProvider>,
        { wrapper: BrowserRouter }
      )

      expect(await screen.findAllByText('Boards')).toHaveLength(2)
      expect(screen.getByText('Lab 1').closest('a')).toHaveAttribute(
        'href',
        '/boards?siteId=lab_1'
      )
      expect(screen.getByText('Lab 2').closest('a')).toHaveAttribute(
        'href',
        '/boards?siteId=lab_2'
      )

      const allViewBoards = await screen.findAllByText('View Boards')

      allViewBoards.forEach((vb) => expect(vb).toHaveAttribute('href'))
    })

    it('renders a list of board configs with links to view configured boards when single site exists with no ability to create board', async () => {
      const location = {
        ...window.location,
        search: `siteId=lab_1`,
      }
      Object.defineProperty(window, 'location', {
        writable: true,
        value: location,
      })
      render(
        <MockedProvider
          mocks={BoardsMockSingleSiteMultipleConfigWithoutWritePermission}
          addTypename={true}
          cache={cache}
          defaultOptions={{
            watchQuery: {
              fetchPolicy: 'no-cache',
            },
          }}
        >
          <ThemeProvider theme={theme}>
            <Boards />
          </ThemeProvider>
        </MockedProvider>,
        { wrapper: BrowserRouter }
      )

      expect(await screen.findAllByText('Boards')).toHaveLength(2)
      expect(screen.queryByText('Create board')).not.toBeInTheDocument()
      const allViewBoards = await screen.findAllByText('View Board')

      allViewBoards.forEach((vb) => expect(vb).toHaveAttribute('href'))
    })

    it('redirects to board view when only 1 site and board config exist', async () => {
      const location = {
        ...window.location,
        search: `siteId=lab_1`,
      }
      Object.defineProperty(window, 'location', {
        writable: true,
        value: location,
      })
      render(
        <MockedProvider
          mocks={BoardsMockSingleSiteSingleConfigWithoutWritePermission}
          addTypename={true}
          cache={cache}
          defaultOptions={{
            watchQuery: {
              fetchPolicy: 'no-cache',
            },
          }}
        >
          <ThemeProvider theme={theme}>
            <Boards />
          </ThemeProvider>
        </MockedProvider>,
        { wrapper: BrowserRouter }
      )

      await waitFor(() => {
        expect(navigate).toHaveBeenCalledWith(`/boards/${MockBoardConfig.id}`, {
          replace: true,
        })
      })
    })
  })
})
