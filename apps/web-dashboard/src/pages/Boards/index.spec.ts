import { boardsHosts, isBoardsHost } from './index'

describe('boards', () => {
  describe('isBigBoard', () => {
    it.each(boardsHosts)('returns true for valid hosts', (host) => {
      expect(isBoardsHost(host)).toBe(true)
    })

    it.each(['', 'random-host.com'])(
      'returns false for invalid hosts',
      (invalidHost) => {
        expect(isBoardsHost(invalidHost)).toBe(false)
      }
    )
  })
})
