export const BoardConfigData = {
  data: {
    boardConfigs: {
      edges: [
        {
          node: {
            id: '7fbb32d6-b6b2-42fc-87f0-0fe99de690e8',
            name: 'Timeline with Video',
            enableVideo: true,
            site: {
              id: 'palo_alto_1',
              __typename: 'Site',
            },
            updatedTime: '2024-06-13T21:19:05.839599+00:00',
            updatedByUser: {
              name: '<PERSON> <PERSON>',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: false,
            pageDuration: 30,
            pageSize: 10,
            boardViewType: 'TIMELINE',
            zoomPercent: 100,
            showClosedRooms: true,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: '1eb36914-21a2-453a-a91f-6ffe26d354f7',
            name: 'Tile No Video or Closed Rooms',
            enableVideo: false,
            site: {
              id: 'lab_1',
              __typename: 'Site',
            },
            updatedTime: '2024-11-01T16:24:39.355881+00:00',
            updatedByUser: {
              name: '<PERSON>',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: true,
            pageDuration: 10,
            pageSize: 10,
            boardViewType: 'TILE',
            zoomPercent: 200,
            showClosedRooms: true,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: 'aa6f3032-4aad-4890-8d02-60d1d3dfeb9f',
            name: 'Apella Palo Alto',
            enableVideo: true,
            site: {
              id: 'palo_alto_1',
              __typename: 'Site',
            },
            updatedTime: '2024-06-13T21:19:37.962464+00:00',
            updatedByUser: {
              name: 'Na Zhang',
              __typename: 'User',
            },
            rooms: [
              {
                id: 'palo_alto_room_0',
                __typename: 'Room',
              },
              {
                id: 'palo_alto_room_1',
                __typename: 'Room',
              },
            ],
            blurVideo: true,
            pageDuration: 28,
            pageSize: 10,
            boardViewType: 'TILE',
            zoomPercent: 100,
            showClosedRooms: true,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: '11a96433-1d21-4589-9c0a-6a0d3501b6c3',
            name: 'Tile No Videos',
            enableVideo: false,
            site: {
              id: 'lab_1',
              __typename: 'Site',
            },
            updatedTime: '2024-10-09T14:17:03.787328+00:00',
            updatedByUser: {
              name: 'Juan Diego Castrillon',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: true,
            pageDuration: 30,
            pageSize: 10,
            boardViewType: 'TILE',
            zoomPercent: 100,
            showClosedRooms: true,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: '521ec12b-2ed6-4c8c-b0c6-d9ac088f2d5b',
            name: 'Tile With Video',
            enableVideo: true,
            site: {
              id: 'lab_1',
              __typename: 'Site',
            },
            updatedTime: '2024-06-17T22:45:58.511739+00:00',
            updatedByUser: {
              name: 'Juan Diego Castrillon',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: true,
            pageDuration: 35,
            pageSize: 10,
            boardViewType: 'TILE',
            zoomPercent: 120,
            showClosedRooms: true,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: '9ccd5722-81de-461c-8172-d0937ec905c2',
            name: 'Timeline With Video',
            enableVideo: true,
            site: {
              id: 'lab_1',
              __typename: 'Site',
            },
            updatedTime: '2024-05-28T21:49:52.974609+00:00',
            updatedByUser: {
              name: 'Maurice Carey',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: true,
            pageDuration: 30,
            pageSize: 10,
            boardViewType: 'TIMELINE',
            zoomPercent: 100,
            showClosedRooms: true,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: 'bc6a5ef5-677d-40b7-8d0d-ff2a81829fa0',
            name: 'Timeline No Video',
            enableVideo: false,
            site: {
              id: 'lab_1',
              __typename: 'Site',
            },
            updatedTime: '2024-05-28T21:59:22.205246+00:00',
            updatedByUser: {
              name: 'Maurice Carey',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: true,
            pageDuration: 30,
            pageSize: 10,
            boardViewType: 'TIMELINE',
            zoomPercent: 100,
            showClosedRooms: true,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: 'cb9c4daf-73ea-42b1-9efb-efae45d4801b',
            name: "Sam's 2nd Test Board",
            enableVideo: false,
            site: {
              id: 'palo_alto_1',
              __typename: 'Site',
            },
            updatedTime: '2023-12-19T19:17:45.373930+00:00',
            updatedByUser: {
              name: 'Sam Raper',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: true,
            pageDuration: 30,
            pageSize: 10,
            boardViewType: 'TILE',
            zoomPercent: 100,
            showClosedRooms: true,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: 'cbe1bb8b-a30b-4797-a174-f51dd1955034',
            name: 'Timeline No Video or Closed Rooms',
            enableVideo: false,
            site: {
              id: 'lab_1',
              __typename: 'Site',
            },
            updatedTime: '2024-05-31T23:08:27.755474+00:00',
            updatedByUser: {
              name: 'Maurice Carey',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: true,
            pageDuration: 30,
            pageSize: 10,
            boardViewType: 'TIMELINE',
            zoomPercent: 100,
            showClosedRooms: false,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: '8b0e5546-871f-411f-a625-caaecf92aa5e',
            name: 'Timeline With Video and No Closed Rooms',
            enableVideo: true,
            site: {
              id: 'lab_1',
              __typename: 'Site',
            },
            updatedTime: '2024-05-31T23:05:55.605766+00:00',
            updatedByUser: {
              name: 'Maurice Carey',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: true,
            pageDuration: 35,
            pageSize: 10,
            boardViewType: 'TIMELINE',
            zoomPercent: 100,
            showClosedRooms: false,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
        {
          node: {
            id: '75c613e4-824f-4ee7-8385-232918c20bda',
            name: 'Tile with Video and No Closed Rooms',
            enableVideo: true,
            site: {
              id: 'lab_1',
              __typename: 'Site',
            },
            updatedTime: '2024-05-31T23:04:12.535828+00:00',
            updatedByUser: {
              name: 'Maurice Carey',
              __typename: 'User',
            },
            rooms: null,
            blurVideo: true,
            pageDuration: 35,
            pageSize: 10,
            boardViewType: 'TILE',
            zoomPercent: 100,
            showClosedRooms: false,
            __typename: 'BoardConfig',
          },
          __typename: 'BoardConfigEdge',
        },
      ],
      __typename: 'BoardConfigConnection',
    },
  },
}

export const BigBoardData = {
  data: {
    sites: {
      edges: [
        {
          node: {
            name: 'Apella Lab',
            id: 'lab_1',
            rooms: {
              edges: [
                {
                  node: {
                    id: 'garage_0',
                    name: 'Garage 0-test',
                    status: {
                      name: 'IN_CASE',
                      since: '2025-01-08T19:08:17.844295+00:00',
                      inProgressApellaCase: {
                        id: 'case:34c92dd6-2699-4515-a3fb-3e9299a16a06',
                        status: {
                          name: 'SURGERY',
                          since: '2025-01-08T19:24:48.191654+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:34c92dd6-2699-4515-a3fb-3e9299a16a06',
                            type: 'LIVE',
                            startTime: '2025-01-08T19:08:17.844295+00:00',
                            endTime: '2025-01-08T19:43:59.385680+00:00',
                            status: {
                              name: 'SURGERY',
                              since: '2025-01-08T19:24:48.191654+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '34c92dd6-2699-4515-a3fb-3e9299a16a06',
                              isAddOn: null,
                              scheduledStartTime:
                                '2025-01-08T18:59:57.160693+00:00',
                              scheduledEndTime:
                                '2025-01-08T20:19:57.160693+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'f8c835e2-72c0-4f35-be14-aa1962371787',
                                    firstName: 'John',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              patient: null,
                              notePlan: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:f6beda2e-9f36-4d30-a571-0cdac73cd09c',
                            type: 'FORECAST',
                            startTime: '2025-01-08T20:36:50.156573+00:00',
                            endTime: '2025-01-08T21:04:00.956573+00:00',
                            status: {
                              name: 'SCHEDULED',
                              since: '2025-01-08T12:50:04.291260+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: 'f6beda2e-9f36-4d30-a571-0cdac73cd09c',
                              isAddOn: null,
                              scheduledStartTime:
                                '2025-01-08T20:33:41.756573+00:00',
                              scheduledEndTime:
                                '2025-01-08T21:56:41.756573+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'a7d26be7-92ad-41bc-927e-f95fd9a3af96',
                                    firstName: 'Gregory',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Colonoscopy',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              patient: null,
                              notePlan: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:895acf0d-e0e9-44db-8f8e-ce25ef470759',
                            type: 'FORECAST',
                            startTime: '2025-01-08T22:13:03.762466+00:00',
                            endTime: '2025-01-08T22:40:14.562466+00:00',
                            status: {
                              name: 'SCHEDULED',
                              since: '2025-01-08T12:50:04.277131+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '895acf0d-e0e9-44db-8f8e-ce25ef470759',
                              isAddOn: null,
                              scheduledStartTime:
                                '2025-01-08T22:09:55.362466+00:00',
                              scheduledEndTime:
                                '2025-01-08T23:44:55.362466+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '8e971e0d-9be7-4129-82b7-3c9db7f6cde8',
                                    firstName: 'Gregory',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              patient: null,
                              notePlan: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:8e1297a5-bba7-4cd7-831c-269da0f10fc9',
                            type: 'FORECAST',
                            startTime: '2025-01-08T23:57:07.112065+00:00',
                            endTime: '2025-01-09T00:24:17.912065+00:00',
                            status: {
                              name: 'SCHEDULED',
                              since: '2025-01-08T12:50:04.494493+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '8e1297a5-bba7-4cd7-831c-269da0f10fc9',
                              isAddOn: null,
                              scheduledStartTime:
                                '2025-01-08T23:53:58.712065+00:00',
                              scheduledEndTime:
                                '2025-01-09T01:30:58.712065+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '0448e968-c4f7-4982-9466-6fedceb1c7c3',
                                    firstName: 'John',
                                    lastName: 'Dorian',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'f8c835e2-72c0-4f35-be14-aa1962371787',
                                    firstName: 'John',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Angiogram',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              patient: null,
                              notePlan: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_1',
                    name: 'Garage 1',
                    status: {
                      name: 'IN_CASE',
                      since: '2025-01-08T18:53:00.206602+00:00',
                      inProgressApellaCase: {
                        id: 'case:886b76d8-8761-44b8-b7f0-f297233207db',
                        status: {
                          name: 'SURGERY',
                          since: '2025-01-08T19:18:57.970494+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:886b76d8-8761-44b8-b7f0-f297233207db',
                            type: 'LIVE',
                            startTime: '2025-01-08T18:53:00.206602+00:00',
                            endTime: '2025-01-08T19:43:59.385680+00:00',
                            status: {
                              name: 'SURGERY',
                              since: '2025-01-08T19:18:57.970494+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '886b76d8-8761-44b8-b7f0-f297233207db',
                              isAddOn: null,
                              scheduledStartTime:
                                '2025-01-08T18:55:23.767902+00:00',
                              scheduledEndTime:
                                '2025-01-08T20:33:23.767902+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '3008c147-f8b4-4b07-9e58-49b4fb333c77',
                                    firstName: 'Miranda',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'bca3eebb-aa9d-4185-9007-0555b63db319',
                                    firstName: 'Henry',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              patient: {
                                id: 'p2',
                                personalInfo: {
                                  firstNameAbbreviated: 'E',
                                  lastNameAbbreviated: 'Doe',
                                  age: 36,
                                  administrativeSex: {
                                    text: 'F',
                                    __typename: 'AdministrativeSex',
                                  },
                                  __typename: 'PersonalInfo',
                                },
                                __typename: 'Patient',
                              },
                              notePlan: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:e13895a6-2357-4184-85dc-6df13753e23b',
                            type: 'FORECAST',
                            startTime: '2025-01-08T20:47:07.328349+00:00',
                            endTime: '2025-01-08T21:14:18.128349+00:00',
                            status: {
                              name: 'SCHEDULED',
                              since: '2025-01-08T12:16:36.775298+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: 'e13895a6-2357-4184-85dc-6df13753e23b',
                              isAddOn: null,
                              scheduledStartTime:
                                '2025-01-08T20:43:58.928349+00:00',
                              scheduledEndTime:
                                '2025-01-08T22:16:58.928349+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '63ece80a-d0c6-4b1e-bc96-dc81c16c7100',
                                    firstName: 'Henry',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'facf1cac-4807-44bb-afbf-b562fdb1363d',
                                    firstName: 'Miranda',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Angiogram',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              patient: null,
                              notePlan: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:39b05680-8824-42c6-89ea-652dbb82e95e',
                            type: 'FORECAST',
                            startTime: '2025-01-08T22:26:45.822600+00:00',
                            endTime: '2025-01-08T22:53:56.622600+00:00',
                            status: {
                              name: 'SCHEDULED',
                              since: '2025-01-08T12:16:36.743509+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '39b05680-8824-42c6-89ea-652dbb82e95e',
                              isAddOn: null,
                              scheduledStartTime:
                                '2025-01-08T22:23:37.422600+00:00',
                              scheduledEndTime:
                                '2025-01-08T23:54:37.422600+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'e2da4139-4395-45d1-9fef-8c81b1b0d350',
                                    firstName: 'John',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'bca3eebb-aa9d-4185-9007-0555b63db319',
                                    firstName: 'Henry',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'I & D Abdomen',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              patient: null,
                              notePlan: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:db16f7a0-4758-4c70-9122-669c28bbe1c1',
                            type: 'FORECAST',
                            startTime: '2025-01-09T00:12:18.753027+00:00',
                            endTime: '2025-01-09T00:39:29.553027+00:00',
                            status: {
                              name: 'SCHEDULED',
                              since: '2025-01-08T12:16:46.821654+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: 'db16f7a0-4758-4c70-9122-669c28bbe1c1',
                              isAddOn: null,
                              scheduledStartTime:
                                '2025-01-09T00:09:10.353027+00:00',
                              scheduledEndTime:
                                '2025-01-09T01:26:10.353027+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '6d844577-72c8-4e43-8763-dd95d60526df',
                                    firstName: 'Gregory',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Colonoscopy',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              patient: null,
                              notePlan: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_2',
                    name: 'Garage 2',
                    status: {
                      name: 'IN_CASE',
                      since: '2025-01-08T19:12:35.308133+00:00',
                      inProgressApellaCase: {
                        id: 'phase:213cd096-500a-4846-8f32-57008b69f62d',
                        status: {
                          name: 'PREP',
                          since: '2025-01-08T19:28:16.344027+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'phase:213cd096-500a-4846-8f32-57008b69f62d',
                            type: 'LIVE',
                            startTime: '2025-01-08T19:12:35.308133+00:00',
                            endTime: null,
                            status: {
                              name: 'PREP',
                              since: '2025-01-08T19:28:16.344027+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'AT-DEV-OR1',
                    name: 'Test',
                    status: {
                      name: 'CLOSED',
                      since: '2025-01-08T00:00:23-08:00',
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus',
                    },
                    apellaCases: {
                      edges: [],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
              ],
              __typename: 'RoomConnection',
            },
            __typename: 'Site',
          },
          __typename: 'SiteEdge',
        },
      ],
      __typename: 'SiteConnection',
    },
  },
}

export const MetricsCaseData = {
  data: {
    apellaCases: {
      edges: [
        {
          node: {
            id: 'case:45f1a08c-6768-4c49-89e6-152d3cd891cf',
            case: {
              id: '45f1a08c-6768-4c49-89e6-152d3cd891cf',
              isFirstCase: true,
              scheduledStartTime: '2025-01-08T16:14:02.101284+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_1',
              __typename: 'Room' as const,
            },
            type: 'COMPLETE',
            startTime: '2025-01-08T16:08:53.417894+00:00',
            endTime: '2025-01-08T17:17:16.542235+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:0a58670a-35ba-47f7-a78f-3a3c594acfff',
            case: {
              id: '0a58670a-35ba-47f7-a78f-3a3c594acfff',
              isFirstCase: true,
              scheduledStartTime: '2025-01-08T16:12:46.001780+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_0',
              __typename: 'Room' as const,
            },
            type: 'COMPLETE',
            startTime: '2025-01-08T16:10:02.109675+00:00',
            endTime: '2025-01-08T16:51:31.938243+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'phase:8ab06d71-ee6a-4b6f-a5e9-a874ed4b5f6c',
            case: null,
            room: {
              id: 'garage_2',
              __typename: 'Room' as const,
            },
            type: 'COMPLETE',
            startTime: '2025-01-08T16:10:04.344087+00:00',
            endTime: '2025-01-08T17:08:44.544660+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:374e97b4-9568-4520-9fe2-da48030630d0',
            case: {
              id: '374e97b4-9568-4520-9fe2-da48030630d0',
              isFirstCase: null,
              scheduledStartTime: '2025-01-08T17:12:53.973687+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_0',
              __typename: 'Room' as const,
            },
            type: 'COMPLETE',
            startTime: '2025-01-08T17:08:10.694541+00:00',
            endTime: '2025-01-08T18:15:41.685362+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'phase:ccff2a78-ccff-4fc9-8509-1f053f9aa736',
            case: null,
            room: {
              id: 'garage_2',
              __typename: 'Room' as const,
            },
            type: 'COMPLETE',
            startTime: '2025-01-08T17:43:35.578847+00:00',
            endTime: '2025-01-08T19:00:30.544188+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:93111328-f923-4fd4-8ff3-c375e01294c9',
            case: {
              id: '93111328-f923-4fd4-8ff3-c375e01294c9',
              isFirstCase: false,
              scheduledStartTime: '2025-01-08T17:56:29.315004+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_1',
              __typename: 'Room' as const,
            },
            type: 'COMPLETE',
            startTime: '2025-01-08T18:06:07.862449+00:00',
            endTime: '2025-01-08T18:50:47.894076+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:886b76d8-8761-44b8-b7f0-f297233207db',
            case: {
              id: '886b76d8-8761-44b8-b7f0-f297233207db',
              isFirstCase: false,
              scheduledStartTime: '2025-01-08T18:55:23.767902+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_1',
              __typename: 'Room' as const,
            },
            type: 'LIVE',
            startTime: '2025-01-08T18:53:00.206602+00:00',
            endTime: '2025-01-08T19:43:59.385680+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:34c92dd6-2699-4515-a3fb-3e9299a16a06',
            case: {
              id: '34c92dd6-2699-4515-a3fb-3e9299a16a06',
              isFirstCase: null,
              scheduledStartTime: '2025-01-08T18:59:57.160693+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_0',
              __typename: 'Room' as const,
            },
            type: 'LIVE',
            startTime: '2025-01-08T19:08:17.844295+00:00',
            endTime: '2025-01-08T19:43:59.385680+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'phase:213cd096-500a-4846-8f32-57008b69f62d',
            case: null,
            room: {
              id: 'garage_2',
              __typename: 'Room' as const,
            },
            type: 'LIVE',
            startTime: '2025-01-08T19:12:35.308133+00:00',
            endTime: null,
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:f6beda2e-9f36-4d30-a571-0cdac73cd09c',
            case: {
              id: 'f6beda2e-9f36-4d30-a571-0cdac73cd09c',
              isFirstCase: null,
              scheduledStartTime: '2025-01-08T20:33:41.756573+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_0',
              __typename: 'Room' as const,
            },
            type: 'FORECAST',
            startTime: '2025-01-08T20:36:50.156573+00:00',
            endTime: '2025-01-08T21:04:00.956573+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:e13895a6-2357-4184-85dc-6df13753e23b',
            case: {
              id: 'e13895a6-2357-4184-85dc-6df13753e23b',
              isFirstCase: false,
              scheduledStartTime: '2025-01-08T20:43:58.928349+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_1',
              __typename: 'Room' as const,
            },
            type: 'FORECAST',
            startTime: '2025-01-08T20:47:07.328349+00:00',
            endTime: '2025-01-08T21:14:18.128349+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:895acf0d-e0e9-44db-8f8e-ce25ef470759',
            case: {
              id: '895acf0d-e0e9-44db-8f8e-ce25ef470759',
              isFirstCase: null,
              scheduledStartTime: '2025-01-08T22:09:55.362466+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_0',
              __typename: 'Room' as const,
            },
            type: 'FORECAST',
            startTime: '2025-01-08T22:13:03.762466+00:00',
            endTime: '2025-01-08T22:40:14.562466+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:39b05680-8824-42c6-89ea-652dbb82e95e',
            case: {
              id: '39b05680-8824-42c6-89ea-652dbb82e95e',
              isFirstCase: false,
              scheduledStartTime: '2025-01-08T22:23:37.422600+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_1',
              __typename: 'Room' as const,
            },
            type: 'FORECAST',
            startTime: '2025-01-08T22:26:45.822600+00:00',
            endTime: '2025-01-08T22:53:56.622600+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:8e1297a5-bba7-4cd7-831c-269da0f10fc9',
            case: {
              id: '8e1297a5-bba7-4cd7-831c-269da0f10fc9',
              isFirstCase: null,
              scheduledStartTime: '2025-01-08T23:53:58.712065+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_0',
              __typename: 'Room' as const,
            },
            type: 'FORECAST',
            startTime: '2025-01-08T23:57:07.112065+00:00',
            endTime: '2025-01-09T00:24:17.912065+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
        {
          node: {
            id: 'case:db16f7a0-4758-4c70-9122-669c28bbe1c1',
            case: {
              id: 'db16f7a0-4758-4c70-9122-669c28bbe1c1',
              isFirstCase: false,
              scheduledStartTime: '2025-01-09T00:09:10.353027+00:00',
              isAddOn: null,
              caseClassificationType: null,
              __typename: 'ScheduledCase' as const,
            },
            room: {
              id: 'garage_1',
              __typename: 'Room' as const,
            },
            type: 'FORECAST',
            startTime: '2025-01-09T00:12:18.753027+00:00',
            endTime: '2025-01-09T00:39:29.553027+00:00',
            __typename: 'ApellaCase' as const,
          },
          __typename: 'ApellaCaseEdge' as const,
        },
      ],
      totalRecords: 15,
      __typename: 'ApellaCaseConnection' as const,
    },
  },
}

export const RoomDetails = {
  data: {
    room: {
      name: 'Garage 0-test',
      __typename: 'Room' as const,
      defaultCamera: {
        id: 'at_gar_cam1_720p',
        __typename: 'Camera' as const,
      },
      site: {
        id: 'lab_1',
        name: 'Apella Lab',
        turnoverGoals: {
          goalMinutes: 35,
          maxMinutes: 80,
          __typename: 'TurnoverGoals' as const,
        },
        __typename: 'Site' as const,
      },
      cameras: {
        edges: [
          {
            node: {
              id: 'at-gar-cam1-720p',
              __typename: 'Camera' as const,
            },
            __typename: 'CameraEdge' as const,
          },
          {
            node: {
              id: 'at-gar-cam2-720p',
              __typename: 'Camera' as const,
            },
            __typename: 'CameraEdge' as const,
          },
          {
            node: {
              id: 'at-gar-cam3-720p',
              __typename: 'Camera' as const,
            },
            __typename: 'CameraEdge' as const,
          },
          {
            node: {
              id: 'at_gar_cam0_720p',
              __typename: 'Camera' as const,
            },
            __typename: 'CameraEdge' as const,
          },
          {
            node: {
              id: 'at_gar_cam1_720p',
              __typename: 'Camera' as const,
            },
            __typename: 'CameraEdge' as const,
          },
        ],
        __typename: 'CameraConnection' as const,
      },
    },
  },
}

export const getLiveImage = {
  data: {
    camera: {
      id: 'at_gar_cam1_720p',
      latestImage: null,
      room: {
        id: 'garage_0',
        privacyEnabled: false,
        __typename: 'Room',
      },
      __typename: 'Camera',
    },
  },
}
