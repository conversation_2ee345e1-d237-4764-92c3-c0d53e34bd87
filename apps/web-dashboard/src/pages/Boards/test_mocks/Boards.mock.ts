import {
  aBoardConfig,
  aBoardConfigConnection,
  aBoardConfigEdge,
  anOrganization,
  anOrganizationConnection,
  anOrganizationEdge,
  aSite,
  aSiteConnection,
  aSiteEdge,
  aUser,
  aUserUiPermissions,
} from 'src/__generated__/generated-mocks'
import { GET_BOARD_CONFIGS_WITH_SITES } from 'src/modules/board/queries'
import { GET_CURRENT_USER } from 'src/modules/user/queries'

const UserDataWithWritePermission = {
  data: {
    me: aUser({
      organizations: anOrganizationConnection({
        edges: [
          anOrganizationEdge({
            node: anOrganization({
              id: 'apella_internal_0',
              auth0OrgId: 'apella_internal_0',
            }),
          }),
        ],
      }),
      uiPermissions: aUserUiPermissions({
        bigBoardWriteEnabled: true,
        bigBoardEnabled: true,
      }),
    }),
  },
}

const UserDataWithoutWritePermission = {
  data: {
    me: aUser({
      organizations: anOrganizationConnection({
        edges: [
          anOrganizationEdge({
            node: anOrganization({
              id: 'apella_internal_0',
              auth0OrgId: 'apella_internal_0',
            }),
          }),
        ],
      }),
      uiPermissions: aUserUiPermissions({
        bigBoardWriteEnabled: false,
        bigBoardEnabled: true,
      }),
    }),
  },
}

export const MockBoardConfig = aBoardConfig({ name: 'dummy board config' })

export const BoardsMockMultipleSites = [
  {
    request: {
      query: GET_BOARD_CONFIGS_WITH_SITES,
      variables: {
        siteIds: null,
      },
    },
    result: {
      data: {
        sites: aSiteConnection({
          edges: [
            aSiteEdge({ node: aSite({ id: 'lab_1', name: 'Lab 1' }) }),
            aSiteEdge({ node: aSite({ id: 'lab_2', name: 'Lab 2' }) }),
          ],
        }),
        boardConfigs: aBoardConfigConnection({
          edges: [aBoardConfigEdge({ node: aBoardConfig() })],
        }),
      },
    },
  },
  {
    request: {
      query: GET_CURRENT_USER,
      variables: {},
    },
    result: UserDataWithWritePermission,
  },
]

export const BoardsMockSingleSite = [
  {
    request: {
      query: GET_BOARD_CONFIGS_WITH_SITES,
      variables: {
        siteIds: ['lab_1'],
      },
    },
    result: {
      data: {
        sites: aSiteConnection({
          edges: [aSiteEdge({ node: aSite({ id: 'lab_1', name: 'Lab 1' }) })],
        }),
        boardConfigs: aBoardConfigConnection({
          edges: [aBoardConfigEdge({ node: MockBoardConfig })],
        }),
      },
    },
  },
  {
    request: {
      query: GET_CURRENT_USER,
      variables: {},
    },
    result: UserDataWithWritePermission,
  },
]

export const BoardsMockSingleSiteNoParam = [
  {
    request: {
      query: GET_BOARD_CONFIGS_WITH_SITES,
      variables: {
        siteIds: null,
      },
    },
    result: {
      data: {
        sites: aSiteConnection({
          edges: [aSiteEdge({ node: aSite({ id: 'lab_1', name: 'Lab 1' }) })],
        }),
        boardConfigs: aBoardConfigConnection({
          edges: [aBoardConfigEdge({ node: MockBoardConfig })],
        }),
      },
    },
  },
  {
    request: {
      query: GET_CURRENT_USER,
      variables: {},
    },
    result: UserDataWithWritePermission,
  },
]

export const BoardsMockSingleSiteMultipleConfigWithoutWritePermission = [
  {
    request: {
      query: GET_BOARD_CONFIGS_WITH_SITES,
      variables: {
        siteIds: ['lab_1'],
      },
    },
    result: {
      data: {
        sites: aSiteConnection({
          edges: [aSiteEdge({ node: aSite({ id: 'lab_1', name: 'Lab 1' }) })],
        }),
        boardConfigs: aBoardConfigConnection({
          edges: [
            aBoardConfigEdge({ node: aBoardConfig() }),
            aBoardConfigEdge({ node: aBoardConfig() }),
          ],
        }),
      },
    },
  },
  {
    request: {
      query: GET_CURRENT_USER,
      variables: {},
    },
    result: UserDataWithoutWritePermission,
  },
]

export const BoardsMockSingleSiteSingleConfigWithoutWritePermission = [
  {
    request: {
      query: GET_BOARD_CONFIGS_WITH_SITES,
      variables: {
        siteIds: ['lab_1'],
      },
    },
    result: {
      data: {
        sites: aSiteConnection({
          edges: [aSiteEdge({ node: aSite({ id: 'lab_1', name: 'Lab 1' }) })],
        }),
        boardConfigs: aBoardConfigConnection({
          edges: [aBoardConfigEdge({ node: MockBoardConfig })],
        }),
      },
    },
  },
  {
    request: {
      query: GET_CURRENT_USER,
      variables: {},
    },
    result: UserDataWithoutWritePermission,
  },
]

export const BoardsMockMultipleSitesWithoutWritePermission = [
  {
    request: {
      query: GET_BOARD_CONFIGS_WITH_SITES,
      variables: {
        siteIds: null,
      },
    },
    result: {
      data: {
        sites: aSiteConnection({
          edges: [
            aSiteEdge({ node: aSite({ id: 'lab_1', name: 'Lab 1' }) }),
            aSiteEdge({ node: aSite({ id: 'lab_2', name: 'Lab 2' }) }),
          ],
        }),
        boardConfigs: aBoardConfigConnection({
          edges: [aBoardConfigEdge({ node: aBoardConfig() })],
        }),
      },
    },
  },
  {
    request: {
      query: GET_CURRENT_USER,
      variables: {},
    },
    result: UserDataWithoutWritePermission,
  },
]
