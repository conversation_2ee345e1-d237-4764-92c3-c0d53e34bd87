import { Apollo<PERSON>lient, ApolloProvider, InMemoryCache } from '@apollo/client'
import { Meta, StoryObj } from '@storybook/react'
import { graphql, http, HttpResponse } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { ShowVideoContext, TimezoneProvider } from 'src/Contexts'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { GetCurrentUserDocument } from 'src/modules/user/__generated__'
import { GetRoomDetailsDocument } from 'src/pages/__generated__'
import { GetLiveImageDocument } from 'src/pages/Live/__generated__'
import {
  UserType,
  GetCurrentUserData,
} from 'src/pages/test_mocks/GetCurrentUserData'
import MockLDProvider from 'src/test/MockLDProvider'
import { modes } from 'src/test/storybookHelpers'

import { BoardView } from '../Board'
import {
  BoardConfigData,
  BigBoardData,
  MetricsCaseData,
  RoomDetails,
} from './BoardMockData'

const mockedClient = new ApolloClient({
  uri: 'https://your-graphql-endpoint',
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-first',
    },
    query: {
      fetchPolicy: 'cache-first',
    },
  },
})

const ldFlags: Partial<WebDashboardFeatureFlagSet> = {
  showPatientData: true,
}

const meta: Meta<typeof BoardView> = {
  title: 'Pages/Boards',
  component: BoardView,
  parameters: {
    chromatic: {
      modes: {
        xLarge: modes.xxLarge,
      },
      delay: 4000,
    },
  },
  decorators: [
    (Story, { parameters }) => (
      <ShowVideoContext.Provider value={0}>
        <TimezoneProvider>
          <MockLDProvider flags={parameters.ldFlags || ldFlags}>
            <ApolloProvider client={mockedClient}>
              <Story />
            </ApolloProvider>
          </MockLDProvider>
        </TimezoneProvider>
      </ShowVideoContext.Provider>
    ),
    withRouter,
  ],
}

export default meta
type Story = StoryObj<typeof BoardView>
const routing = {
  path: '/board/:boardId',
  handle: 'Boards',
}

const msw = ({ userType = UserType.ALL_ACCESS } = {}) => ({
  handlers: [
    http.get('https://events.launchdarkly.com/events/diagnostic/', () => {
      return HttpResponse.json({})
    }),
    http.get('https://app.launchdarkly.com/sdk/goals/', () => {
      return HttpResponse.json({})
    }),
    graphql.query(GetCurrentUserDocument, () => {
      return HttpResponse.json(GetCurrentUserData(userType))
    }),
    graphql.query('BoardConfigs', () => {
      return HttpResponse.json(BoardConfigData)
    }),
    graphql.query('BigBoardData', () => {
      return HttpResponse.json(BigBoardData)
    }),
    graphql.query('GetDailyMetricCaseData', () => {
      return HttpResponse.json(MetricsCaseData)
    }),
    graphql.query(GetLiveImageDocument, () => {
      return HttpResponse.json({ data: null })
    }),
    graphql.query(GetRoomDetailsDocument, () => {
      return HttpResponse.json(RoomDetails)
    }),
  ],
})

const date = new Date('2025-01-08T11:20:00.518-07:00')

export const TimelineWithVideo: Story = {
  parameters: {
    date,
    msw: msw({ userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/board/7fbb32d6-b6b2-42fc-87f0-0fe99de690e8',
      },
      routing,
    },
  },
}

export const TileWithVideo: Story = {
  parameters: {
    date,
    msw: msw({ userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/board/521ec12b-2ed6-4c8c-b0c6-d9ac088f2d5b',
      },
      routing,
    },
  },
}

export const TimelineNoVideo: Story = {
  parameters: {
    date,
    msw: msw({ userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/board/bc6a5ef5-677d-40b7-8d0d-ff2a81829fa0',
      },
      routing,
    },
  },
}

export const TileNoVideo: Story = {
  parameters: {
    date,
    msw: msw({ userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/board/11a96433-1d21-4589-9c0a-6a0d3501b6c3',
      },
      routing,
    },
  },
}
