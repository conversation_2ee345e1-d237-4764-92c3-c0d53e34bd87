import { Fragment, ReactNode, useCallback } from 'react'

import { useTheme } from '@emotion/react'

import { Field, useField } from 'formik'

import {
  Caps3,
  FlexContainer,
  H6,
  Label,
  MultiSelect,
  P3,
  remSpacing,
  shape,
} from '@apella/component-library'

export const CheckboxField = ({
  name,
  label,
  disabled,
}: {
  name: string
  label: string
  disabled?: boolean
}) => (
  <FlexContainer alignItems={'center'} gap={remSpacing.xsmall}>
    <Field id={name} type="checkbox" name={name} disabled={disabled} />
    <Label htmlFor={name}>{label}</Label>
  </FlexContainer>
)

export const RoomSelectField = ({
  name,
  children,
}: {
  name: string
  children: React.ReactNode
}) => {
  const fieldArr = useField<string[] | undefined>(name)
  const field = fieldArr[0]
  const { setValue } = fieldArr[2]

  const onChange = useCallback(
    (value: string[] | undefined) => {
      setValue(value)
    },
    [setValue]
  )

  return (
    <FlexContainer direction={'column'} gap={remSpacing.xsmall}>
      <Label htmlFor={'roomSelect'}>Rooms</Label>
      <MultiSelect
        label={'All rooms'}
        bulkSelect={true}
        id={'roomSelect'}
        {...field}
        onChange={onChange}
      >
        {children}
      </MultiSelect>
    </FlexContainer>
  )
}

export const RadioWithDescriptionField = ({
  name,
  options,
}: {
  name: string
  options: {
    id: string
    label: string
    description: string
    img?: ReactNode
    children?: ReactNode
    isRecommended?: boolean
  }[]
}) => {
  const theme = useTheme()
  const [field] = useField<string | undefined>(name)

  return (
    <FlexContainer direction={'column'} gap={remSpacing.xsmall}>
      {options.map((o) => {
        const isSelected = field.value === o.id
        return (
          <Fragment key={o.id}>
            <Label htmlFor={`${name}-${o.id}`}>
              <div
                css={{
                  padding: remSpacing.small,
                  border: `2px solid ${
                    isSelected ? theme.palette.blue[50] : theme.palette.gray[30]
                  }`,
                  borderRadius: shape.borderRadius.xsmall,
                  background: isSelected ? '#006fff0d' : undefined,
                  cursor: 'pointer',
                  position: 'relative',
                }}
              >
                {o.isRecommended && (
                  <Caps3
                    css={{
                      position: 'absolute',
                      left: 0,
                      top: 0,
                      background: isSelected
                        ? theme.palette.blue[50]
                        : theme.palette.gray[30],
                      padding: `${remSpacing.xxsmall} ${remSpacing.xsmall}`,
                      borderBottomRightRadius: shape.borderRadius.xsmall,
                      color: isSelected ? 'white' : theme.palette.text.primary,
                    }}
                  >
                    Recommended
                  </Caps3>
                )}
                <div
                  css={{
                    display: 'grid',
                    gridTemplateColumns: 'minmax(0, 1fr) minmax(0, 3fr)',
                    columnGap: remSpacing.medium,
                  }}
                >
                  <div>{o.img}</div>
                  <div>
                    <H6>{o.label}</H6>
                    <P3 css={{ color: theme.palette.gray[60] }}>
                      {o.description}
                    </P3>
                    {o.children}
                  </div>
                </div>
              </div>
            </Label>
            <Field
              id={`${name}-${o.id}`}
              type={'radio'}
              name={name}
              value={o.id}
              css={{ display: 'none' }}
            />
          </Fragment>
        )
      })}
    </FlexContainer>
  )
}
