import { useCallback, useEffect, useMemo } from 'react'
import { useNavigate, useParams } from 'react-router'

import { useTheme } from '@emotion/react'

import { useMutation } from '@apollo/client'
import { Form, Formik, useField } from 'formik'
import { rem } from 'polished'
import * as Yup from 'yup'

import {
  Button,
  ButtonLink,
  Delete,
  FlexContainer,
  H4,
  Input,
  Label,
  Option,
  P2,
  P3,
  Progress,
  remSpacing,
  Save,
  Slider,
  ZoomIn,
  ZoomOut,
} from '@apella/component-library'
import { BoardViewType } from 'src/__generated__/globalTypes'
import { Section } from 'src/components/FormLayout/FormLayout'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { MaxZoom, MinZoom } from 'src/modules/board/hooks/useBoardZoom'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

import { useBoardConfig } from '../../modules/board/hooks/useBoardConfigsState'
import {
  BOARD_CONFIG_FRAGMENT,
  CREATE_BOARD_CONFIG,
  DELETE_BOARD_CONFIG,
  UPDATE_BOARD_CONFIG,
} from '../../modules/board/queries'
import {
  CreateBoardConfig,
  CreateBoardConfigVariables,
  DeleteBoardConfig,
  DeleteBoardConfigVariables,
  UpdateBoardConfig,
  UpdateBoardConfigVariables,
} from '../../modules/board/queries/__generated__'
import { useCurrentUser } from '../../modules/user/hooks'
import { LocationPath } from '../../router/types'
import { useSiteName } from '../../utils/hooks'
import { useRoomOptions } from '../../utils/useSiteRoomsOptions'
import { RadioWithDescriptionField, RoomSelectField } from './BoardEditFields'
import { DENSITY, DENSITY_MAP } from './DensityMap'
import { ComfortableSVG, CompactSVG, CozySVG } from './DensitySvgs'
import { BoardEditResponse } from './types'
import { CustomSVG, TileSVG, TimelineSVG } from './ViewTypeSvgs'

const BOARD_CONFIG_DEFAULTS = {
  roomsPerPage: 10,
  pageDurationSeconds: 30,
  enableVideo: true,
  blurVideo: true,
  showClosedRooms: true,
}

type BoardEditParams = {
  siteId: string
  boardId: string
}

interface FormValues {
  blurVideo: boolean
  boardViewType: BoardViewType
  density: DENSITY | undefined
  enableVideo: boolean
  name: string | undefined
  pageDuration: number
  roomIds: string[] | undefined
  showClosedRooms: boolean
  zoom?: number
}

const APPEARANCE_OPTIONS = [
  {
    id: BoardViewType.TILE,
    label: 'Sequence tiles',
    description:
      'Cases are a fixed size ordered in sequence, prioritizing case information over overview of day.',
    img: <TileSVG />,
    isRecommended: true,
  },
  {
    id: BoardViewType.TIMELINE,
    label: 'Schedule timeline',
    description:
      'Cases are a relative size, fixed to a forecasted timeline of the day.',
    img: <TimelineSVG />,
  },
]

const DENSITY_FIELD_NAME = 'density'
const ZOOM_SLIDER_STEP = 0.01
const SLIDER_MAX_ZOOM = 2

const CustomZoomSlider = () => {
  const [{ value: densityValue }, , { setValue: setDensity }] = useField<
    DENSITY | undefined
  >(DENSITY_FIELD_NAME)

  const [{ value = 1 }, , { setValue }] = useField<number | undefined>('zoom')

  const marks = useMemo(() => {
    const id = Object.entries(DENSITY_MAP).find(
      (entry) => entry[1] === value
    )?.[0]

    const label = DENSITY_OPTIONS.find((opt) => opt.id === id)?.label

    return { [value]: label || `${Math.round(value * 100)}%` }
  }, [value])

  useEffect(() => {
    if (densityValue !== 'custom') {
      const value = Object.entries(DENSITY_MAP).find(
        (entry) => entry[0] === densityValue
      )?.[1]

      if (value !== undefined) {
        setValue(value)
      }
    }
  }, [densityValue, setValue])

  const onChange = useCallback(
    (val: number) => {
      setDensity('custom')
      setValue(
        Math.round(Math.max(MinZoom, Math.min(val, SLIDER_MAX_ZOOM)) * 100) /
          100
      )
    },
    [setValue, setDensity]
  )

  const onClick = useCallback(
    ({ value, direction = 1 }: { value: number; direction?: 1 | -1 }) =>
      () => {
        return onChange(value + ZOOM_SLIDER_STEP * direction)
      },
    [onChange]
  )

  return (
    <FlexContainer
      gap={remSpacing.small}
      alignItems="center"
      css={{ paddingBottom: rem('20px') }}
    >
      <Button
        buttonType="icon"
        size="sm"
        color="alternate"
        onClick={onClick({ value, direction: -1 })}
        type="button"
      >
        <ZoomOut size="xs" />
      </Button>
      <Slider
        min={MinZoom}
        max={Math.min(MaxZoom, SLIDER_MAX_ZOOM)}
        marks={marks}
        step={ZOOM_SLIDER_STEP}
        value={value}
        onChange={setValue}
      />
      <Button
        buttonType="icon"
        size="sm"
        color="alternate"
        onClick={onClick({ value })}
        type="button"
      >
        <ZoomIn size="xs" />
      </Button>
    </FlexContainer>
  )
}

const DENSITY_OPTIONS = [
  {
    id: 'comfortable',
    label: 'Comfortable',
    description:
      'Best used when a Board will need to be legible from a long distance. This option will display fewer rooms at a time.',
    img: <ComfortableSVG />,
    isRecommended: true,
  },
  {
    id: 'cozy',
    label: 'Cozy',
    description:
      'Best used when a Board will need to be legible from a medium distance. This option is similar to “comfortable” with smaller text.',
    img: <CozySVG />,
  },
  {
    id: 'compact',
    label: 'Compact',
    description:
      'Best used when a Board will be viewed from a close distance. This option will display more rooms at a time.',
    img: <CompactSVG />,
  },
  {
    id: 'custom',
    label: 'Custom',
    description:
      'Fine tune the size of items on the Board to get the amount of information that works for your space.',
    img: <CustomSVG />,
    children: <CustomZoomSlider />,
  },
]

export const BoardEdit = () => {
  const { siteId, boardId } = useParams() as BoardEditParams
  const theme = useTheme()
  const navigate = useNavigate()
  const { currentOrganization } = useCurrentUser()
  const orgId = currentOrganization?.node?.id

  const rooms = useRoomOptions()
  const siteName = useSiteName(siteId)

  const { boardConfig, isLoading } = useBoardConfig({ boardId })
  const isNewBoard = boardConfig === undefined && !isLoading
  const eventsLogger = useAnalyticsEventLogger()

  const selectedDensity: DENSITY | undefined = useMemo(() => {
    if (isNewBoard) {
      return 'comfortable'
    } else if (!boardConfig?.zoom) {
      return undefined
    }

    const matchedEntry = Object.entries(DENSITY_MAP).find(
      (kv) => kv[1] === boardConfig.zoom
    )

    return !!matchedEntry ? (matchedEntry[0] as DENSITY) : 'custom'
  }, [isNewBoard, boardConfig?.zoom])

  const [createBoardConfig, { loading: createLoading }] = useMutation<
    CreateBoardConfig,
    CreateBoardConfigVariables
  >(CREATE_BOARD_CONFIG, {
    update: (cache, { data }) => {
      cache.modify({
        fields: {
          boardConfigs(existingBoardConfigs) {
            const newConfigRef = cache.writeFragment({
              id: `BoardConfig:${data?.boardConfigCreate?.boardConfig?.id}`,
              fragment: BOARD_CONFIG_FRAGMENT,
              data: data?.boardConfigCreate?.boardConfig,
            })

            return existingBoardConfigs.edges.concat({
              __typename: 'BoardConfigEdge',
              node: newConfigRef,
            })
          },
        },
      })
    },
  })

  const [updateBoardConfig, { loading: updateLoading }] = useMutation<
    UpdateBoardConfig,
    UpdateBoardConfigVariables
  >(UPDATE_BOARD_CONFIG, {
    update: (cache, { data }) => {
      cache.updateFragment(
        {
          id: `BoardConfig:${data?.boardConfigUpdate?.boardConfig?.id}`,
          fragment: BOARD_CONFIG_FRAGMENT,
        },
        () => data?.boardConfigUpdate?.boardConfig
      )
    },
  })

  const [deleteBoardConfig, { loading: deleteLoading }] = useMutation<
    DeleteBoardConfig,
    DeleteBoardConfigVariables
  >(DELETE_BOARD_CONFIG)

  const onResponse = useCallback(
    (responseCode: BoardEditResponse) => {
      const searchParams = new URLSearchParams({
        s: responseCode,
        siteId,
      })
      navigate({
        pathname: LocationPath.Boards,
        search: searchParams.toString(),
      })
    },
    [siteId, navigate]
  )

  const onSubmit = useCallback(
    (values: FormValues) => {
      if (!orgId || values.name === undefined) {
        return
      }

      const roomIdsForSite = values.roomIds?.filter((rId) => {
        const roomObj = rooms.find((r) => r.node.id === rId)
        return roomObj?.node.site.id === siteId
      })
      const finalRoomIds =
        roomIdsForSite?.length === 0 ? undefined : roomIdsForSite

      const density =
        values.density &&
        values.density !== 'custom' &&
        DENSITY_MAP[values.density]
          ? DENSITY_MAP[values.density]
          : undefined

      // We need to round here due to JS math issues.
      // E.g. 0.56 + 0.01 = 0.5700000000000001 in JS :(
      const zoomPercent = Math.round(
        values.density === 'custom' && values.zoom !== undefined
          ? values.zoom * 100
          : density !== undefined
            ? density * 100
            : 100
      )

      if (isNewBoard) {
        eventsLogger(EVENTS.CREATE_BOARD, {
          id: boardId,
          boardName: values.name,
          siteId,
          roomIds: finalRoomIds,
          boardViewType: values.boardViewType,
          density: values.density,
        })
        createBoardConfig({
          variables: {
            ...values,
            id: boardId,
            orgId,
            pageSize: 10,
            name: values.name,
            siteId: siteId,
            roomIds: finalRoomIds,
            boardViewType: values.boardViewType,
            zoomPercent,
          },
        }).then((response) =>
          onResponse(
            !!response.data?.boardConfigCreate?.success
              ? BoardEditResponse.CreateSuccess
              : BoardEditResponse.CreateFailure
          )
        )
      } else {
        eventsLogger(EVENTS.UPDATE_BOARD, {
          id: boardId,
          boardName: values.name,
          siteId,
          roomIds: finalRoomIds,
          boardViewType: values.boardViewType,
          density: values.density,
        })
        updateBoardConfig({
          variables: {
            ...values,
            pageSize: 10,
            boardConfigId: boardId,
            name: values.name,
            roomIds: finalRoomIds,
            boardViewType: values.boardViewType,
            zoomPercent,
          },
        }).then((response) => {
          onResponse(
            !!response.data?.boardConfigUpdate?.success
              ? BoardEditResponse.UpdateSuccess
              : BoardEditResponse.UpdateFailure
          )
        })
      }
    },
    [
      orgId,
      isNewBoard,
      rooms,
      siteId,
      eventsLogger,
      boardId,
      createBoardConfig,
      onResponse,
      updateBoardConfig,
    ]
  )

  const onDelete = useCallback(() => {
    if (
      boardConfig?.id &&
      confirm(`Are you sure you want to delete "${boardConfig?.name}"`)
    ) {
      eventsLogger(EVENTS.DELETE_BOARD, {
        id: boardConfig.id,
        boardName: boardConfig.name,
        siteId,
      })
      deleteBoardConfig({
        variables: { boardConfigId: boardConfig?.id },
        update: (cache) => {
          const normalizedId = cache.identify({
            id: boardConfig.id,
            __typename: 'BoardConfig',
          })
          cache.evict({ id: normalizedId })
          cache.gc()
        },
      }).then((response) =>
        onResponse(
          !!response.data?.boardConfigDelete?.success
            ? BoardEditResponse.DeleteSuccess
            : BoardEditResponse.DeleteFailure
        )
      )
    }
  }, [
    boardConfig?.id,
    boardConfig?.name,
    eventsLogger,
    siteId,
    deleteBoardConfig,
    onResponse,
  ])

  const pageTitle = `Set up ${boardConfig?.name ?? 'new'} Board`

  const LastCrumb = {
    to: {
      pathname: LocationPath.Boards,
      search: new URLSearchParams({
        siteId: siteId,
      }).toString(),
    },
    id: 'boards',
    display: `${siteName} Boards`,
  }

  return (
    <PageContentTemplate
      title={pageTitle}
      breadcrumbs={[LastCrumb]}
      maxContainerSize="form"
    >
      <Formik
        initialValues={{
          roomIds: boardConfig?.roomIds,
          name: boardConfig?.name,
          pageSize:
            boardConfig?.roomsPerPage ?? BOARD_CONFIG_DEFAULTS.roomsPerPage,
          pageDuration:
            boardConfig?.pageDurationSeconds ??
            BOARD_CONFIG_DEFAULTS.pageDurationSeconds,
          blurVideo: boardConfig?.blurVideo ?? BOARD_CONFIG_DEFAULTS.blurVideo,
          boardViewType: boardConfig?.boardViewType ?? BoardViewType.TILE,
          enableVideo:
            boardConfig?.enableVideo ?? BOARD_CONFIG_DEFAULTS.enableVideo,
          density: selectedDensity,
          zoom: selectedDensity
            ? DENSITY_MAP[selectedDensity] || boardConfig?.zoom
            : 1,
          showClosedRooms:
            boardConfig?.showClosedRooms ??
            BOARD_CONFIG_DEFAULTS.showClosedRooms,
        }}
        validationSchema={Yup.object({
          name: Yup.string().required('Please choose a name'),
        })}
        enableReinitialize
        validateOnBlur={true}
        onSubmit={onSubmit}
      >
        {({ isValid, dirty, values }) => {
          return (
            <Form
              css={{
                color: theme.palette.text.secondary,
              }}
            >
              <Section>
                <Input.Text name={'name'} label={'Name'} required />
                <FlexContainer direction={'column'} gap={remSpacing.xsmall}>
                  <Label htmlFor={'siteName'}>Site</Label>
                  <P2
                    id={'siteName'}
                    css={{
                      marginLeft: remSpacing.xsmall,
                    }}
                  >
                    {siteName}
                  </P2>
                </FlexContainer>
                <RoomSelectField name={'roomIds'}>
                  {rooms
                    .filter((r) => r.node.site.id === siteId)
                    .map((room) => (
                      <Option
                        key={room.node.id}
                        value={room.node.id}
                        label={room.node.name}
                      />
                    ))}
                </RoomSelectField>
              </Section>
              <Section>
                <H4>Pagination</H4>
                <Input.Number
                  name={'pageDuration'}
                  label={'Page duration (seconds)'}
                />
              </Section>
              <Section>
                <H4>Video</H4>
                <Input.Checkbox
                  name={'enableVideo'}
                  ariaLabel="Enable video checkbox"
                  value={values.enableVideo}
                  as={FlexContainer}
                  asProps={{ alignItems: 'center', gap: remSpacing.xsmall }}
                >
                  Enable video
                </Input.Checkbox>
                <Input.Checkbox
                  name={'blurVideo'}
                  disabled={!values.enableVideo}
                  ariaLabel="Blur video between Pt wheeled in and Pt wheeled out"
                  value={values.blurVideo}
                  as={FlexContainer}
                  asProps={{ alignItems: 'center', gap: remSpacing.xsmall }}
                >
                  Blur video between Pt wheeled in and Pt wheeled out
                </Input.Checkbox>
              </Section>
              <Section>
                <H4>Rooms</H4>
                <Input.Checkbox
                  name={'showClosedRooms'}
                  ariaLabel="Show closed rooms checkbox"
                  value={values.showClosedRooms}
                  as={FlexContainer}
                  asProps={{ alignItems: 'center', gap: remSpacing.xsmall }}
                >
                  Show closed rooms
                </Input.Checkbox>
              </Section>
              <Section>
                <H4>Appearance</H4>
                <P3>
                  Select a Board layout below. Some information may not be
                  included in different layouts.
                </P3>
                <RadioWithDescriptionField
                  name={'boardViewType'}
                  options={APPEARANCE_OPTIONS}
                />
              </Section>
              <Section>
                <H4>Density</H4>
                <P3>
                  Select the density that would work best for this Board based
                  on the context in which it is displayed. For example, if this
                  Board will need to be viewed from a distance, “Comfortable”
                  might be the best option for that context.
                </P3>
                <RadioWithDescriptionField
                  name={DENSITY_FIELD_NAME}
                  options={DENSITY_OPTIONS}
                />
              </Section>
              <FlexContainer
                justifyContent={'flex-end'}
                css={{ marginTop: remSpacing.gutter }}
                gap={remSpacing.xsmall}
              >
                <ButtonLink appearance="link" to={LastCrumb.to}>
                  Cancel
                </ButtonLink>
                {!isNewBoard && (
                  <Button type="button" color={'error'} onClick={onDelete}>
                    {deleteLoading ? (
                      <Progress size={'sm'} />
                    ) : (
                      <Delete size={'sm'} />
                    )}
                    Delete
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={!isValid || !dirty || isLoading}
                >
                  {createLoading || updateLoading ? (
                    <Progress size={'sm'} />
                  ) : (
                    <Save size={'sm'} />
                  )}
                  Save
                </Button>
              </FlexContainer>
            </Form>
          )
        }}
      </Formik>
    </PageContentTemplate>
  )
}
