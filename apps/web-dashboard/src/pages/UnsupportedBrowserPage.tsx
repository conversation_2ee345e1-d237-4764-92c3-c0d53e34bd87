import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'

import {
  ErrorPage,
  P2,
  remSpacing,
  shape,
  UnsupportedBrowser,
} from '@apella/component-library'

import { SUPPORTED_BROWSERS } from '../utils/supportedBrowsers'

interface ExternalLinkProps {
  primary?: boolean
}

const ExternalLink = styled.a<ExternalLinkProps>(({ theme, primary }) => ({
  textDecoration: 'none',
  width: '100%',
  marginTop: primary ? remSpacing.gutter : remSpacing.small,
  lineHeight: '24px',
  // Copied from component library
  alignItems: 'center',
  border: `1px solid ${theme.palette.gray[20]}`,
  borderRadius: shape.borderRadius.xsmall,
  boxShadow: theme.shadows[1],
  cursor: 'pointer',
  display: 'inline-flex',
  justifyContent: 'center',
  padding: remSpacing.small,
  color: primary ? theme.palette.text.alternate : theme.palette.text.primary,
  backgroundColor: primary
    ? theme.palette.blue[40]
    : theme.palette.background.primary,
  ':hover': {
    backgroundColor: primary
      ? theme.palette.blue[30]
      : theme.palette.gray.background,
  },
  ':active': {
    backgroundColor: primary
      ? theme.palette.blue[50]
      : theme.palette.gray.background,
  },
}))

export const UnsupportedBrowserPage = (): React.JSX.Element => {
  const theme = useTheme()

  const suggestedBrowsers = SUPPORTED_BROWSERS.filter((b) => !!b.url)

  return (
    <ErrorPage
      icon={<UnsupportedBrowser />}
      header={'This browser is not supported'}
      body={
        <P2 color={theme.palette.text.tertiary}>
          This website requires a newer browser to display properly. Please
          update your browser or download the latest one online.
        </P2>
      }
      actions={
        <div>
          {suggestedBrowsers.map((b, i) => (
            <ExternalLink key={b.name} href={b.url} primary={i === 0}>
              Download {b.name}
            </ExternalLink>
          ))}
        </div>
      }
    />
  )
}
