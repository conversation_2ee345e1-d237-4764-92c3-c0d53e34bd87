import { ChangeEvent, useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { ErrorMessage, Form, Formik } from 'formik'
import { DateTime } from 'luxon'
import { rem } from 'polished'
import { date as YupDate, object as YupObject, string as YupString } from 'yup'

import {
  Blade,
  Button,
  ComponentTheme,
  DatePicker,
  Error,
  FlexContainer,
  FlexItem,
  fontRamp,
  fontWeights,
  Input,
  InputTime,
  Label,
  Option,
  remSpacing,
  SingleSelect,
  Span3,
} from '@apella/component-library'
import { DashComponent } from 'src/components/DashComponent'
import { Section } from 'src/components/FormLayout/FormLayout'
import { useTimezone } from 'src/Contexts'

import { formatHourMinute } from '../Schedule/utils'
import { GetCurrentUserName } from './__generated__'
import { DateTimePicker } from './DateTimePicker/DateTimePicker'
import { RoomSelector } from './RoomSelector/RoomSelector'
import { BlockTimeRelease, BlockTimeWithSiteInfo } from './types'
import { useBlockNames } from './useBlockNames'

const ELEMENT_WIDTH = rem('172px')

interface BlockTimeReleaseBladeProps {
  blockName: string
  blockTime: BlockTimeWithSiteInfo
  isBladeOpen: boolean
  onBladeClose: () => void
  originalIdx: number
  replace: (idx: number, blockTime: BlockTimeWithSiteInfo) => void
  userData?: GetCurrentUserName['me']
}

export const BlockTimeReleaseBlade = ({
  isBladeOpen,
  onBladeClose,
  ...props
}: BlockTimeReleaseBladeProps) => {
  return (
    <Blade size="xs" side="right" onClose={onBladeClose} isOpen={isBladeOpen}>
      <BlockTimeReleaseBladeContent {...props} onBladeClose={onBladeClose} />
    </Blade>
  )
}

const BlockTimeReleaseBladeContent = ({
  userData,
  originalIdx,
  blockTime,
  blockName,
  onBladeClose: onClose,
  replace,
}: Omit<BlockTimeReleaseBladeProps, 'isBladeOpen'>) => {
  const theme: ComponentTheme = useTheme()
  const { timezone } = useTimezone()

  const isBlockTimeReleased = blockTime.releases.length === 1

  const { blockNodes } = useBlockNames({
    siteIds: [blockTime.room.site.id],
    includeArchived: false,
  })

  const blocks = blockNodes?.filter((block) => block.node.name !== blockName)

  const releaseSchema = useMemo(
    () =>
      YupObject({
        id: YupString(),
        blockTimeId: YupString(),
        reason: YupString()
          .required('Release reason is required')
          .min(5, 'Release reason is too short'),
        startTime: YupDate().min(
          blockTime.startTime,
          'Release start time must be after block start time'
        ),
        endTime: YupDate().when('startTime', (startTime, schema) => {
          if (startTime) {
            return YupDate()
              .min(startTime, 'End time must be after Start time')
              .max(
                blockTime.endTime,
                'Release end time must be before block end time'
              )
              .typeError('End time is required')
          }
          return schema
        }),
        releasedTime: YupDate().required('Release date is required'),
        source: YupString(),
        unreleasedTime: YupDate(),
        unreleasedSource: YupString(),
      }),
    [blockTime]
  )

  return (
    <Formik<BlockTimeRelease>
      initialValues={
        blockTime.releases[0] ?? {
          id: crypto.randomUUID(),
          reason: '',
          toBlock: '',
          blockTimeId: blockTime.id,
          startTime: blockTime.startTime,
          endTime: blockTime.endTime,
          releasedTime: DateTime.now().setZone(timezone).toISO(),
          source: userData?.name,
        }
      }
      validationSchema={releaseSchema}
      onSubmit={(values) => {
        replace(originalIdx, {
          ...blockTime,
          releases: [
            {
              ...values,
            },
          ],
        })
        onClose()
      }}
    >
      {({ values, setFieldValue }) => {
        return (
          <Form css={{ height: '100%' }}>
            <FlexContainer
              direction="column"
              justifyContent="space-between"
              css={{ height: '100%' }}
            >
              <Blade.Header>
                <Blade.Title title={`Release block time?`} />
                <Blade.CloseButton onClose={onClose} />
              </Blade.Header>

              <FlexContainer
                css={{
                  padding: remSpacing.medium,
                }}
                justifyContent="flex-start"
                direction={'column'}
                gap="8px"
              >
                <Section>
                  <Span3>
                    Release block time by filling out the available information
                    below. Once done, saving the block that is being edited will
                    finalize this release.
                  </Span3>

                  <Input.Text
                    disabled={isBlockTimeReleased}
                    multiline
                    label="Release reason"
                    name="reason"
                    required
                  />

                  <Label htmlFor={'toBlock'}>{'Release to'}</Label>
                  <SingleSelect
                    name="toBlock"
                    label="Release to block"
                    css={{ width: 230, height: 40 }}
                    onChange={(val) => setFieldValue('toBlock', val)}
                    search
                    value={values.toBlock ? values.toBlock : undefined}
                    disabled={blocks.length === 0}
                  >
                    {blocks.map((block) => (
                      <Option
                        key={block.node.id}
                        value={block.node.name}
                        label={block.node.name}
                      />
                    ))}
                  </SingleSelect>

                  <DateTimePicker
                    name="releasedTime"
                    label="Release Time (local)"
                    disabled={isBlockTimeReleased}
                    required
                    value={values.releasedTime}
                    onChange={(val) =>
                      setFieldValue('releasedTime', val.toISO())
                    }
                  />
                </Section>

                <FlexContainer direction="column" gap="8px">
                  <Span3 style={{ ...fontWeights.semibold }}>Date</Span3>
                  <FlexItem
                    style={{
                      minWidth: ELEMENT_WIDTH,
                    }}
                  >
                    <DatePicker
                      disabled
                      inline={false}
                      buttonAlignment="space-between"
                      buttonFullWidth={true}
                      showIcon={false}
                      showArrow={true}
                      timezone={timezone}
                      value={DateTime.fromISO(values.startTime)
                        .setZone(timezone)
                        .startOf('day')
                        .toJSDate()}
                      setValue={(
                        arg0:
                          | Date
                          | [Date | null, Date | null]
                          | null
                          | undefined
                      ) => {
                        return arg0
                      }}
                    />
                  </FlexItem>
                </FlexContainer>

                <FlexContainer direction="column" gap="8px">
                  <Span3 style={{ ...fontWeights.semibold }}>Room</Span3>
                  <FlexItem
                    style={{
                      minWidth: ELEMENT_WIDTH,
                    }}
                  >
                    <RoomSelector
                      disabled
                      width={'100%'}
                      name={`roomId`}
                      value={blockTime.roomId}
                      onChange={(newRoomId) => {
                        return newRoomId
                      }}
                    />
                  </FlexItem>
                </FlexContainer>

                <FlexContainer direction="column">
                  <Span3 style={{ ...fontWeights.semibold }}>Hours</Span3>
                  <FlexContainer direction="row" alignItems="center" gap="8px">
                    <FlexItem
                      style={{
                        minWidth: ELEMENT_WIDTH,
                      }}
                    >
                      <InputTime
                        disabled={isBlockTimeReleased}
                        size="md"
                        value={formatHourMinute(values.startTime, timezone)}
                        name={`startTime`}
                        onChange={(e: ChangeEvent<HTMLInputElement>) => {
                          const [hour, minute] = e.target.value
                            .split(':')
                            .map(Number)

                          setFieldValue(
                            'startTime',
                            DateTime.fromISO(values.startTime)
                              .setZone(timezone)
                              .startOf('day')
                              .plus({
                                hour,
                                minute,
                              })
                              .toISO()
                          )
                        }}
                      />
                    </FlexItem>
                    <DashComponent />
                    <FlexItem
                      style={{
                        minWidth: ELEMENT_WIDTH,
                      }}
                    >
                      <InputTime
                        disabled={isBlockTimeReleased}
                        value={formatHourMinute(values.endTime, timezone)}
                        name={`endTime`}
                        onChange={(e: ChangeEvent<HTMLInputElement>) => {
                          const [hour, minute] = e.target.value
                            .split(':')
                            .map(Number)

                          setFieldValue(
                            'endTime',
                            DateTime.fromISO(values.startTime)
                              .setZone(timezone)
                              .startOf('day')
                              .plus({
                                hour,
                                minute,
                              })
                              .toISO()
                          )
                        }}
                      />
                    </FlexItem>
                  </FlexContainer>

                  <ErrorMessage name={`startTime`}>
                    {(msg) => (
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          marginTop: remSpacing.xsmall,
                          color: theme.palette.red[50],
                          ...fontRamp['13px'],
                          gap: remSpacing.xxsmall,
                        }}
                      >
                        <Error size="sm" color={theme.palette.red[50]} />
                        {msg}
                      </div>
                    )}
                  </ErrorMessage>

                  <ErrorMessage name={`endTime`}>
                    {(msg) => (
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          marginTop: remSpacing.xsmall,
                          color: theme.palette.red[50],
                          ...fontRamp['13px'],
                          gap: remSpacing.xxsmall,
                        }}
                      >
                        <Error size="sm" color={theme.palette.red[50]} />
                        {msg}
                      </div>
                    )}
                  </ErrorMessage>
                </FlexContainer>
              </FlexContainer>

              <FlexContainer
                justifyContent="flex-end"
                gap={remSpacing.small}
                css={{
                  marginTop: 'auto',
                  borderTop: `1px solid ${theme.palette.gray[20]}`,
                  padding: remSpacing.medium,
                }}
              >
                {!isBlockTimeReleased && <Button type="submit">Release</Button>}
              </FlexContainer>
            </FlexContainer>
          </Form>
        )
      }}
    </Formik>
  )
}
