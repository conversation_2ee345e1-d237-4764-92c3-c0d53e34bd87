import { useCallback, useMemo, useState } from 'react'
import { useSearchParams } from 'react-router'

import { isEqual } from 'lodash'
import { DateTime } from 'luxon'

import { OrderBy } from '@apella/component-library'
import {
  useLocalStorageState,
  useQueryParamAndLocalStorageState,
  useUrlCleanParamAndLocalStorageState,
} from '@apella/hooks'
import { ISO_WEEKDAYS as DEFAULT_DAYS_OF_WEEK } from 'src/components/DayOfWeekFilter'
import { useTimezone } from 'src/Contexts'
import { PageSize } from 'src/modules/board/constants'
import { useCurrentUser } from 'src/modules/user/hooks'
import { getCleanedUrlParam } from 'src/utils/getCleanedUrlParam'
import useSortOrderConversion from 'src/utils/sortOrderHelpers'
import {
  dateTimeToUrlFriendlyDate,
  urlFriendlyDateToDateTime,
} from 'src/utils/urlFriendlyDate'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { END_DATE, START_DATE } from '../Insights/constants'
import { ISOWeekDate } from '../Insights/types'
import { END_MONTH_OFFSET, START_MONTH_OFFSET } from './constants'
import { useBlockNames } from './useBlockNames'
import { useSurgeonOptions } from './useSurgeonOptions'

const DEFAULT_PAGING_STATE: {
  after: string | undefined
  first: number
} = {
  after: undefined,
  first: PageSize,
}

const DEFAULT_ORDER_BY_STATE: OrderBy[] = []

export const useBlockManagementPageFilters = () => {
  const { timezone } = useTimezone()
  const [searchParams, setSearchParams] = useSearchParams()
  const { currentOrganization } = useCurrentUser()

  const defaultMinTime = DateTime.now()
    .minus({ months: START_MONTH_OFFSET })
    .startOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  const defaultMaxTime = DateTime.now()
    .plus({ months: END_MONTH_OFFSET })
    .endOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  const minTimeFromUrl = getCleanedUrlParam<string | undefined>({
    searchParams,
    key: START_DATE,
    defaultValue: undefined,
  })
  const maxTimeFromUrl = getCleanedUrlParam<string | undefined>({
    searchParams,
    key: END_DATE,
    defaultValue: undefined,
  })

  const minTime = (
    minTimeFromUrl
      ? urlFriendlyDateToDateTime(minTimeFromUrl)
      : DateTime.fromISO(defaultMinTime)
  )
    .startOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  const maxTime = (
    maxTimeFromUrl
      ? urlFriendlyDateToDateTime(maxTimeFromUrl)
      : DateTime.fromISO(defaultMaxTime)
  )
    .endOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  const onChangeDateRanges = useCallback(
    (minTime: DateTime, maxTime: DateTime) => {
      searchParams.set(
        START_DATE,
        JSON.stringify(dateTimeToUrlFriendlyDate(minTime))
      )
      searchParams.set(
        END_DATE,
        JSON.stringify(dateTimeToUrlFriendlyDate(maxTime))
      )
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams]
  )

  // Filter Keys
  const orgId = currentOrganization?.node?.id
  const BlockMgmtPrefix = `${orgId}_Blocks`

  const ORDER_BY = `${BlockMgmtPrefix}_orderBy`
  const WEEKDAYS = `${BlockMgmtPrefix}_weekdays`
  const SITE_IDS = `${BlockMgmtPrefix}_sites`
  const ROOM_IDS = `${BlockMgmtPrefix}_rooms`
  const SURGEON_IDS = `${BlockMgmtPrefix}_surgeons`
  const BLOCK_IDS = `${BlockMgmtPrefix}_blocks`

  // State
  const [pagingState, setPagingState] = useState(DEFAULT_PAGING_STATE)
  const [orderByState, setOrderByState] = useSortOrderConversion(
    ORDER_BY,
    DEFAULT_ORDER_BY_STATE
  )

  const [siteIds, setSiteIds] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(SITE_IDS, undefined)

  const [roomIds, setRoomIds] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(ROOM_IDS, undefined)

  const [surgeonIds, setSurgeonIds] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(SURGEON_IDS, undefined)

  const [blockIds, setBlockIds] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(BLOCK_IDS, undefined)

  const [daysOfWeek, setDaysOfWeek] = useUrlCleanParamAndLocalStorageState<
    ISOWeekDate[] | undefined
  >(WEEKDAYS, DEFAULT_DAYS_OF_WEEK)

  const [showArchived, setShowArchived] = useLocalStorageState<boolean>(
    'showArchived',
    false
  )

  const { sites } = useSiteOptions()
  const rooms = useRoomOptions()
  const surgeons = useSurgeonOptions()
  const { blockNodes: blocks } = useBlockNames({
    minEndTime: minTime,
    maxStartTime: maxTime,
    daysOfWeek,
    siteIds,
    roomIds,
    includeArchived: showArchived,
  })

  // Handlers
  const onChangeSort = useCallback(
    (sortOrder: OrderBy[] | undefined) => {
      setOrderByState(sortOrder)
    },
    [setOrderByState]
  )

  const onChangePage = useCallback(
    (newCursor?: string) => {
      setPagingState({ ...pagingState, after: newCursor })
    },
    [pagingState, setPagingState]
  )

  // Reset the table properties if the date range is changed.
  const localOnChangeDateRanges = useCallback(
    (dates: Date[]) => {
      const minTime = DateTime.fromJSDate(dates[0])
        .setZone(timezone)
        .startOf('day')
      const maxTime = DateTime.fromJSDate(dates[1])
        .setZone(timezone)
        .endOf('day')

      setOrderByState(DEFAULT_ORDER_BY_STATE)
      setPagingState(DEFAULT_PAGING_STATE)

      onChangeDateRanges(minTime, maxTime)
    },
    [timezone, setOrderByState, onChangeDateRanges]
  )

  const onChangeSites = useCallback(
    (siteIds?: string[]) => {
      setSiteIds(siteIds)
      setOrderByState(DEFAULT_ORDER_BY_STATE)
      setPagingState(DEFAULT_PAGING_STATE)
    },
    [setSiteIds, setOrderByState]
  )

  const onChangeRooms = useCallback(
    (roomIds?: string[]) => {
      setRoomIds(roomIds)
      setOrderByState(DEFAULT_ORDER_BY_STATE)
      setPagingState(DEFAULT_PAGING_STATE)
    },
    [setRoomIds, setOrderByState]
  )

  const onChangeSurgeons = useCallback(
    (surgeonIds?: string[]) => {
      setSurgeonIds(surgeonIds)
      setOrderByState(DEFAULT_ORDER_BY_STATE)
      setPagingState(DEFAULT_PAGING_STATE)
    },
    [setSurgeonIds, setOrderByState]
  )

  const onChangeBlocks = useCallback(
    (blockIds?: string[]) => {
      setBlockIds(blockIds)
      setOrderByState(DEFAULT_ORDER_BY_STATE)
      setPagingState(DEFAULT_PAGING_STATE)
    },
    [setBlockIds, setOrderByState]
  )

  const onChangeDaysOfWeek = useCallback(
    (newDaysOfWeek: ISOWeekDate[] | undefined) => {
      setDaysOfWeek(newDaysOfWeek)
    },
    [setDaysOfWeek]
  )

  const showResetFiltersButton = useMemo(() => {
    return (
      !isEqual(daysOfWeek, DEFAULT_DAYS_OF_WEEK) ||
      !isEqual(minTime, defaultMinTime) ||
      !isEqual(maxTime, defaultMaxTime) ||
      !isEqual(siteIds, undefined) ||
      !isEqual(roomIds, undefined) ||
      !isEqual(surgeonIds, undefined) ||
      !isEqual(blockIds, undefined)
    )
  }, [
    defaultMaxTime,
    defaultMinTime,
    maxTime,
    minTime,
    roomIds,
    siteIds,
    surgeonIds,
    blockIds,
    daysOfWeek,
  ])

  const resetActions = useCallback(() => {
    onChangeDaysOfWeek(DEFAULT_DAYS_OF_WEEK)
    localOnChangeDateRanges([
      new Date(defaultMinTime),
      new Date(defaultMaxTime),
    ])
    onChangeSites()
    onChangeRooms()
    onChangeSurgeons()
    onChangeBlocks()
  }, [
    defaultMaxTime,
    defaultMinTime,
    localOnChangeDateRanges,
    onChangeDaysOfWeek,
    onChangeRooms,
    onChangeSites,
    onChangeSurgeons,
    onChangeBlocks,
  ])

  return {
    minEndTime: minTime,
    maxStartTime: maxTime,
    showResetFiltersButton,
    sites,
    siteIds,
    rooms,
    roomIds,
    surgeons,
    surgeonIds,
    blocks,
    blockIds,
    daysOfWeek,
    pagingState,
    orderByState,
    showArchived,
    setShowArchived,
    localOnChangeDateRanges,
    onChangeDaysOfWeek,
    onChangeRooms,
    onChangeSites,
    onChangeSurgeons,
    onChangeBlocks,
    onChangeSort,
    onChangePage,
    resetActions,
  }
}
