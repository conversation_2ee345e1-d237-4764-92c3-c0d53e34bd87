import { generatePath, useNavigate, useParams } from 'react-router'
import { toast } from 'react-toastify'

import { ApolloError, useMutation, useQuery } from '@apollo/client'

import { LoadingBackdrop } from 'src/components/LoadingBackdrop'
import { useTimezone } from 'src/Contexts'
import { LocationPath } from 'src/router/types'

import { BlockUpdate, GetBlock, GetBlockVariables } from './__generated__'
import { BlockForm } from './BlockForm'
import { DEFAULT_BLOCK_COLOR } from './constants'
import { GET_BLOCK, UPDATE_BLOCK } from './queries'
import { BlockFormTitle } from './types'
import { parseBackendErrorMsg } from './utils'

export type BlockEditIdUrlParam = 'blockId'

export const BlockEditPage = () => {
  const { timezone } = useTimezone()
  const navigate = useNavigate()
  const { blockId } = useParams<BlockEditIdUrlParam>()

  const { data: blockData, loading } = useQuery<GetBlock, GetBlockVariables>(
    GET_BLOCK,
    {
      variables: {
        id: blockId ?? '',
      },
      skip: !blockId,
    }
  )

  const onUpdateBlock = (data: BlockUpdate) => {
    if (data.blockUpdate?.block?.id) {
      toast.success(`Block '${data.blockUpdate.block.name}' updated`)
      navigate(generatePath(LocationPath.Blocks))
    }
  }

  const onError = (error: ApolloError) => {
    const errMsg = parseBackendErrorMsg(error.message, timezone)
    toast.error(errMsg)
  }

  const [updateBlock] = useMutation<BlockUpdate>(UPDATE_BLOCK, {
    onCompleted: onUpdateBlock,
    onError,
  })

  if (loading) {
    return <LoadingBackdrop />
  }

  if (!blockData?.block) {
    return <div>Block not found</div>
  }

  return (
    <BlockForm
      title={BlockFormTitle.Edit}
      initialValues={{
        ...blockData.block,
        color:
          blockData.block.color === DEFAULT_BLOCK_COLOR
            ? ''
            : blockData.block.color,
      }}
      onSubmit={async (value) => {
        await updateBlock({
          variables: {
            input: {
              ...value,
            },
          },
        })
      }}
    />
  )
}
