import Papa from 'papaparse'

import { Scalars } from 'src/__generated__/globalTypes'

import { ISOWeekDate } from '../Insights/types'

export enum BlockFormTitle {
  Create = 'Create Block',
  Edit = 'Edit Block',
}

export type OrderByKey =
  | keyof Pick<BlockTimeWithSiteInfo, 'startTime' | 'endTime' | 'roomId'>
  | 'dayOfWeek'
  | 'siteId'

export enum OrderByValue {
  ASC = 'asc',
  DESC = 'desc',
}

export interface OrderBy {
  field: OrderByKey
  value: OrderByValue
}

export interface FilterBy {
  dateRange: [Date, Date]
  daysOfWeek: ISOWeekDate[]
  roomIds: string[]
  siteIds: string[] | undefined
}

export interface BlockTimeRelease {
  blockTimeId: string
  endTime: Scalars['DateTime']['input']
  id: string
  reason: string
  releasedTime: Scalars['DateTime']['input']
  source: string
  startTime: Scalars['DateTime']['input']
  toBlock?: string | null
  unreleasedSource?: string | null
  unreleasedTime?: Scalars['DateTime']['input'] | null
}

export interface BlockTime {
  endTime: Scalars['DateTime']['input']
  id: string
  releases: BlockTimeRelease[]
  roomId: string
  startTime: Scalars['DateTime']['input']
}

export type BlockTimeWithoutReleases = Omit<BlockTime, 'releases'>

export interface BlockTimeWithSiteInfo extends BlockTime {
  room: {
    id: string
    site: {
      id: string
      name: string
    }
  }
}

export interface Block {
  archivedTime?: Scalars['DateTime']['input'] | null
  color: string
  id: string
  name: string
  siteIds?: string[] | null
  surgeonIds: string[]
}

export interface BlockWithBlockTime extends Block {
  blockTimes: BlockTime[]
}

export interface BlockWithBlockTimeSiteInfo extends Block {
  blockTimes: BlockTimeWithSiteInfo[]
}

export enum CSVContentType {
  BlockTime,
  BlockRelease,
}

export type FormikBlockValue = BlockWithBlockTimeSiteInfo & { orgId?: string }

export type CSVContent = Papa.ParseResult<string[]>
