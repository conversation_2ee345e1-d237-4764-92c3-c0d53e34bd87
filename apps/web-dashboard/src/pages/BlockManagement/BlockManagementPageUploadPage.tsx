import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react'
import { generatePath, useLocation, useNavigate } from 'react-router'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import { ApolloError, useMutation } from '@apollo/client'
import { FieldArray, Form, Formik, FormikErrors } from 'formik'
import { isEqual, omit, orderBy } from 'lodash'
import { DateTime } from 'luxon'
import {
  array as YupArray,
  date as YupDate,
  object as YupObject,
  string as YupString,
} from 'yup'

import {
  Button,
  ButtonLink,
  ComponentTheme,
  flexboxgrid,
  FlexContainer,
  FlexItem,
  H4,
  remSpacing,
  Save,
  Span2,
  Table,
  TBody,
  TD,
  TH,
  THead,
  Tile,
  TR,
} from '@apella/component-library'
import {
  ISODayOfWeekFilter,
  isoWeekDateToDayOfWeek,
} from 'src/components/DayOfWeekFilter'
import { Section } from 'src/components/FormLayout/FormLayout'
import { BoldedSpan } from 'src/components/InsightsTile/BoldedSpan'
import { ResetFilters } from 'src/components/ResetFilters'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { useTimezone } from 'src/Contexts'
import { LocationPath } from 'src/router/types'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { getStartToEndTimeString } from '../Schedule/utils'
import {
  BlockTimeBulkCreate,
  BlockTimeBulkCreateVariables,
} from './__generated__'
import { ClickableHeader } from './ClickableHeader'
import { DEFAULT_FILTER_BY } from './constants'
import { BLOCK_TIME_BULK_CREATE } from './queries'
import { BlockTimeWithoutReleases, FilterBy, OrderBy } from './types'
import { useBlockNames } from './useBlockNames'
import { parseBackendErrorMsg, validateTimeRanges } from './utils'

const transformCSVtoFormData = (
  csv: Papa.ParseResult<string> | null,
  timezone: string
): FormikBlockTimeData => {
  const blockTimes = (csv?.data ?? []).flatMap((row, index) => {
    if (index === 0) {
      return []
    }

    const [startHour, startMinute] = row[4].split(':')
    const [endHour, endMinute] = row[5].split(':')

    return {
      id: crypto.randomUUID(),
      blockName: row[0],
      roomId: row[2],
      startTime: DateTime.fromFormat(row[3], 'yyyy-MM-dd', { zone: timezone })
        .startOf('day')
        .plus({ hours: Number(startHour), minutes: Number(startMinute) })
        .toISO(),
      endTime: DateTime.fromFormat(row[3], 'yyyy-MM-dd', { zone: timezone })
        .startOf('day')
        .plus({ hours: Number(endHour), minutes: Number(endMinute) })
        .toISO(),
    }
  })

  return {
    blockTimes,
  }
}

export const BlockManagementPageUploadPage = () => {
  const { timezone } = useTimezone()
  const theme: ComponentTheme = useTheme()
  const location = useLocation()
  const navigate = useNavigate()
  const { nameToBlockIdMap } = useBlockNames()

  useEffect(() => {
    if (!location.state?.csv) {
      toast.warn('Invalid CSV file. Please re-upload.')
      navigate(LocationPath.Blocks)
    }
  }, [location.state?.csv, navigate])

  const onBlockTimeBulkCreate = (data: BlockTimeBulkCreate) => {
    if (data?.blockTimeBulkCreate?.success) {
      toast.success(`Block times uploaded successfully`)
      navigate(generatePath(LocationPath.Blocks))
    }
  }

  const onError = (error: ApolloError) => {
    const errMsg = parseBackendErrorMsg(error.message, timezone)
    toast.error(errMsg)
  }

  const [bulkCreate] = useMutation<
    BlockTimeBulkCreate,
    BlockTimeBulkCreateVariables
  >(BLOCK_TIME_BULK_CREATE, {
    onCompleted: onBlockTimeBulkCreate,
    onError,
  })

  const initialValues = useMemo(() => {
    return transformCSVtoFormData(location.state?.csv, timezone)
  }, [location.state?.csv, timezone])

  const { sites } = useSiteOptions()
  const rooms = useRoomOptions()

  const [orderBys, setOrderBys] = useState<OrderBy[]>([])
  const [filterBys, setFilterBys] = useState<FilterBy>(DEFAULT_FILTER_BY)

  const showResetFiltersButton = useMemo(() => {
    return !isEqual(filterBys, DEFAULT_FILTER_BY)
  }, [filterBys])

  const [numOfHiddenEntries, setNumOfHiddenEntries] = useState(0)

  return (
    <FlexContainer justifyContent="center">
      <FlexContainer
        direction="column"
        css={{ width: flexboxgrid.breakpoints.lg }}
      >
        <FlexItem>
          <H4 as="h1" style={{ margin: `${remSpacing.large} 0` }}>
            Upload Block Times
          </H4>
        </FlexItem>

        <Formik
          validateOnMount={true}
          initialValues={initialValues}
          validationSchema={blockFormUploadSchema}
          onSubmit={async (values) => {
            const blockTimes = values.blockTimes.map((blockTime) => ({
              ...omit(blockTime, 'blockName'),
              blockId: nameToBlockIdMap[blockTime.blockName],
            }))

            await bulkCreate({
              variables: {
                input: { blockTimes },
              },
            })
          }}
          validate={validateTimeRanges}
        >
          {({ values, isSubmitting, errors }) => {
            return (
              <Form>
                <Section>
                  <Tile>
                    <FlexContainer
                      css={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: `${remSpacing.small} ${remSpacing.medium}`,
                        height: remSpacing.xlarge,
                        gap: remSpacing.small,
                      }}
                    >
                      <ISODayOfWeekFilter
                        selected={filterBys.daysOfWeek}
                        onChange={(daysOfWeek) => {
                          setFilterBys((prev) => ({
                            ...prev,
                            daysOfWeek,
                          }))
                        }}
                      />

                      <SitesRoomsFilter
                        sites={sites}
                        selectedSiteIds={filterBys.siteIds}
                        onChangeSites={(siteIds: string[] | undefined) =>
                          setFilterBys((prev) => {
                            if (siteIds)
                              return {
                                ...prev,
                                siteIds,
                              }
                            return { ...prev, siteIds: [] }
                          })
                        }
                        rooms={rooms}
                        selectedRoomIds={filterBys.roomIds}
                        onChangeRooms={(roomIds: string[] | undefined) =>
                          setFilterBys((prev) => {
                            if (roomIds)
                              return {
                                ...prev,
                                roomIds,
                              }
                            return { ...prev, roomIds: [] }
                          })
                        }
                        multipleSites
                        bulkSelectSites
                        bulkSelectRooms
                      />

                      {showResetFiltersButton && (
                        <ResetFilters
                          type="button"
                          resetActions={() => setFilterBys(DEFAULT_FILTER_BY)}
                        />
                      )}
                    </FlexContainer>

                    <Table css={{ overflowX: 'auto' }}>
                      <THead
                        css={{
                          position: 'sticky',
                          top: 0,
                          background: theme.palette.background.primary,
                        }}
                      >
                        <TR>
                          <TH
                            css={{
                              alignContent: 'center',
                            }}
                          >
                            <BoldedSpan>Name</BoldedSpan>
                          </TH>
                          <TH>
                            <ClickableHeader
                              label="Date"
                              fieldName="startTime"
                              orderBys={orderBys}
                              setOrderBys={setOrderBys}
                            />
                          </TH>
                          <TH>
                            <ClickableHeader
                              label="Day"
                              fieldName="dayOfWeek"
                              orderBys={orderBys}
                              setOrderBys={setOrderBys}
                            />
                          </TH>

                          <TH
                            css={{
                              alignContent: 'center',
                            }}
                          >
                            <BoldedSpan>Hours</BoldedSpan>
                          </TH>

                          <TH>
                            <ClickableHeader
                              label="Site"
                              fieldName="siteId"
                              orderBys={orderBys}
                              setOrderBys={setOrderBys}
                            />
                          </TH>

                          <TH>
                            <ClickableHeader
                              label="Room"
                              fieldName="roomId"
                              orderBys={orderBys}
                              setOrderBys={setOrderBys}
                            />
                          </TH>
                        </TR>
                      </THead>

                      <TBody>
                        <BlockUploadBlockTimeArray
                          values={values}
                          errors={errors}
                          orderBys={orderBys}
                          filterBys={filterBys}
                          setNumOfHiddenEntries={setNumOfHiddenEntries}
                        />
                      </TBody>
                    </Table>

                    <FlexContainer css={{ justifyContent: 'space-between' }}>
                      <FlexItem
                        style={{
                          padding: `${remSpacing.xxsmall} ${remSpacing.small}`,
                        }}
                      >
                        {numOfHiddenEntries > 0 && (
                          <Span2
                            style={{
                              display: 'block',
                              width: '100%',
                              background: `${theme.palette.red[20]}40`,
                              color: theme.palette.text.primary,
                              padding: remSpacing.xxsmall,
                            }}
                          >
                            {`${numOfHiddenEntries} ${
                              numOfHiddenEntries > 1
                                ? 'entries are'
                                : 'entry is'
                            } hidden due to filters`}
                          </Span2>
                        )}
                      </FlexItem>
                    </FlexContainer>
                  </Tile>
                </Section>

                <FlexContainer
                  justifyContent="space-between"
                  gap={remSpacing.small}
                  css={{
                    margin: `${remSpacing.medium} 0 ${remSpacing.large}`,
                  }}
                >
                  <ButtonLink
                    appearance="link"
                    to={{
                      pathname: LocationPath.Blocks,
                    }}
                  >
                    Back
                  </ButtonLink>

                  <Button
                    type="submit"
                    disabled={!isEqual(errors, {}) || isSubmitting}
                  >
                    <Save size="sm" />
                    Submit
                  </Button>
                </FlexContainer>
              </Form>
            )
          }}
        </Formik>
      </FlexContainer>
    </FlexContainer>
  )
}

const blockFormUploadSchema = YupObject({
  blockTimes: YupArray()
    .of(
      YupObject().shape({
        blockName: YupString().required(),
        roomId: YupString().required('Room ID is required'),
        startTime: YupDate().required('Start time is required'),
        endTime: YupDate()
          .required('End time is required')
          .when('startTime', (startTime, schema) => {
            if (startTime) {
              return YupDate()
                .min(startTime, 'End time must be after Start time')
                .typeError('End time is required')
            }
            return schema
          }),
      })
    )
    .min(1, "Block time can't be empty"),
})

interface CSVBlockTime extends BlockTimeWithoutReleases {
  blockName: string
}

interface FormikBlockTimeData {
  blockTimes: CSVBlockTime[]
}

export const BlockUploadBlockTimeArray = ({
  values,
  errors,
  orderBys,
  filterBys,
  setNumOfHiddenEntries,
}: {
  values: FormikBlockTimeData
  errors: FormikErrors<FormikBlockTimeData>
  orderBys: OrderBy[]
  filterBys: FilterBy
  setNumOfHiddenEntries: Dispatch<SetStateAction<number>>
}) => {
  const { timezone } = useTimezone()
  const theme: ComponentTheme = useTheme()
  const rooms = useRoomOptions()

  const filteredAndSortedValues: FormikBlockTimeData = useMemo(() => {
    const filteredBlockTimes = values.blockTimes.filter((blockTime) => {
      if (filterBys.daysOfWeek.length > 0) {
        const dayOfWeek = DateTime.fromISO(blockTime.startTime).weekday
        if (!filterBys.daysOfWeek.includes(dayOfWeek)) {
          return false
        }
      }

      if (filterBys.roomIds.length > 0) {
        if (!filterBys.roomIds.includes(blockTime.roomId)) {
          return false
        }
      }

      return true
    })

    const orderByIteratees = orderBys.map(
      (orderBy) => (blockTime: CSVBlockTime) => {
        if (orderBy.field === 'dayOfWeek') {
          return DateTime.fromISO(blockTime.startTime).weekday
        }
        if (orderBy.field === 'siteId') {
          return blockTime.roomId
        }
        if (orderBy.field === 'startTime') {
          return DateTime.fromISO(blockTime.startTime).toMillis()
        }
        return blockTime[orderBy.field]
      }
    )

    return {
      ...values,
      blockTimes: orderBy<CSVBlockTime>(
        filteredBlockTimes,
        orderByIteratees,
        orderBys.map((orderBy) => orderBy.value)
      ),
    }
  }, [values, orderBys, filterBys])

  useEffect(
    () =>
      setNumOfHiddenEntries(
        values.blockTimes.length - filteredAndSortedValues.blockTimes.length
      ),
    [values, filteredAndSortedValues, setNumOfHiddenEntries]
  )

  return (
    <FieldArray name="blockTimes">
      {() => {
        return (
          <>
            {filteredAndSortedValues.blockTimes.length > 0 &&
              filteredAndSortedValues.blockTimes.map((blockTime, index) => {
                const originalIdx = values.blockTimes.findIndex(
                  (bt) => bt.id === blockTime.id
                )
                return (
                  <TR
                    key={blockTime.id}
                    css={{
                      backgroundColor:
                        index % 2 === 0
                          ? theme.palette.background.primary
                          : theme.palette.background.secondary,
                    }}
                  >
                    <TD>
                      <Span2>{blockTime.blockName}</Span2>
                    </TD>

                    <TD>
                      <Span2>
                        {DateTime.fromISO(blockTime.startTime)
                          .setZone(timezone)
                          .startOf('day')
                          .toFormat('MM/dd/yy')}
                      </Span2>
                    </TD>

                    <TD>
                      <Span2
                        style={{
                          textTransform: 'capitalize',
                        }}
                      >
                        {
                          isoWeekDateToDayOfWeek[
                            DateTime.fromISO(blockTime.startTime)
                              .setZone(timezone)
                              .startOf('day').weekday
                          ]
                        }
                      </Span2>
                    </TD>

                    <TD>
                      <Span2
                        style={{
                          color: errors.blockTimes?.[originalIdx]
                            ? 'red'
                            : undefined,
                        }}
                      >
                        {getStartToEndTimeString(
                          blockTime.startTime,
                          blockTime.endTime,
                          timezone
                        )}
                      </Span2>
                    </TD>

                    <TD>
                      <Span2>
                        {rooms.find((room) => room.node.id === blockTime.roomId)
                          ?.node.site.name ?? 'N/A'}
                      </Span2>
                    </TD>

                    <TD>
                      <Span2>
                        {rooms.find((room) => room.node.id === blockTime.roomId)
                          ?.node.name ?? 'N/A'}
                      </Span2>
                    </TD>
                  </TR>
                )
              })}
          </>
        )
      }}
    </FieldArray>
  )
}
