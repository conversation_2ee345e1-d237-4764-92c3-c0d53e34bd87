import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { omit } from 'lodash'

export const useBlockColors = (color: string) => {
  const theme = useTheme()
  const colors = useMemo(
    () => omit(theme.palette, ['background', 'gray', 'text']),
    [theme.palette]
  )

  for (const [, colorPalette] of Object.entries(colors)) {
    if (
      Object.values<string>(colorPalette)
        .map((c) => c.toLowerCase())
        .includes(color)
    ) {
      return {
        color,
        borderColor: colorPalette[50],
        backgroundColor: `${colorPalette[50]}10`,
      }
    }
  }

  return {
    color: theme.palette.gray[60],
    borderColor: theme.palette.gray[60],
    backgroundColor: `${theme.palette.gray[50]}10`,
  }
}
