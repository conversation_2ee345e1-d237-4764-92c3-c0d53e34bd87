import { useState } from 'react'

import { useQuery } from '@apollo/client'

import { BlockNode } from '../ScheduleAssistant/interfaces'
import { ListBlockNames, ListBlockNamesVariables } from './__generated__'
import { DEFAULT_BLOCK_COLOR } from './constants'
import { LIST_BLOCK_NAMES } from './queries'

interface BlockNameProps {
  daysOfWeek?: number[]
  includeArchived?: boolean
  maxStartTime?: string
  minEndTime?: string
  roomIds?: string[]
  siteIds?: string[]
}

export const useBlockNames = (props: BlockNameProps = {}) => {
  const [blockNodes, setBlockNodes] = useState<BlockNode[]>([])
  const [nameToBlockIdMap, setNameToBlockIdMap] = useState<{
    [key: string]: string
  }>({})

  useQuery<ListBlockNames, ListBlockNamesVariables>(LIST_BLOCK_NAMES, {
    variables: {
      query: props,
    },
    onCompleted: (data) => {
      const nodes = data.blocks.edges.map((block) => block.node)
      const mapping: { [key: string]: string } = {}

      for (let i = 0; i < nodes.length; i++) {
        mapping[nodes[i].name] = nodes[i].id
      }
      setNameToBlockIdMap(mapping)
      setBlockNodes(
        nodes.map((block) => ({
          node: { color: DEFAULT_BLOCK_COLOR, surgeonIds: [], ...block },
        }))
      )
    },
    skip: blockNodes.length > 0,
  })

  return { blockNodes, nameToBlockIdMap }
}
