import { useMemo, useState } from 'react'

import { useQuery } from '@apollo/client'
import { Form, Formik } from 'formik'
import { isEqual, pick } from 'lodash'
import { DateTime } from 'luxon'
import { rem } from 'polished'
import {
  array as Yup<PERSON>rra<PERSON>,
  date as YupDate,
  object as YupObject,
  string as YupString,
} from 'yup'

import {
  Button,
  ButtonLink,
  Col,
  DatePicker,
  H4,
  Input,
  Label,
  remSpacing,
  Row,
  Save,
  Span2,
  Tile,
} from '@apella/component-library'
import { ISODayOfWeekFilter } from 'src/components/DayOfWeekFilter'
import { Section } from 'src/components/FormLayout/FormLayout'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { ResetFilters } from 'src/components/ResetFilters'
import { SitesFilter } from 'src/components/SitesFilter'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { useTimezone } from 'src/Contexts'
import { useBreadCrumbs } from 'src/router/hooks/useBreadCrumbs'
import { LocationPath } from 'src/router/types'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { GetCurrentUserName } from './__generated__'
import {
  ArchiveButton,
  RestoreButton,
} from './ArchiveBlockButton/ArchiveBlockButton'
import { BlockFormBlockTimeArray } from './BlockFormBlockTimeArray'
import { ClickableHeader } from './ClickableHeader'
import { ColorSelector } from './ColorSelector/ColorSelector'
import { DEFAULT_FILTER_BY, DEFAULT_ORDER_BY } from './constants'
import { GET_CURRENT_USER_NAME } from './queries'
import { SurgeonSelector } from './SurgeonSelector/SurgeonSelector'
import {
  BlockFormTitle,
  BlockTimeWithSiteInfo,
  BlockWithBlockTime,
  FilterBy,
  FormikBlockValue,
  OrderBy,
} from './types'
import { validateTimeRanges } from './utils'

const blockFormSchema = YupObject({
  id: YupString(),
  name: YupString()
    .required('Name is required (e.g. My Block)')
    .min(3, 'Name must be at least 3 characters'),
  orgId: YupString(),
  siteIds: YupArray().of(YupString()).required('Site is required'),
  color: YupString()
    .required('Color is required')
    .matches(/^#([0-9a-f]{3}){1,2}$/i),
  blockTimes: YupArray().of(
    YupObject().shape({
      id: YupString(),
      blockId: YupString(),
      roomId: YupString().required('Room is required'),
      startTime: YupDate().required('Start time is required'),
      endTime: YupDate()
        .required('End time is required')
        .when('startTime', (startTime, schema) => {
          if (startTime) {
            return YupDate()
              .min(startTime, 'End time must be after Start time')
              .typeError('End time is required')
          }
          return schema
        }),
      releases: YupArray()
        .of(
          YupObject().shape({
            id: YupString(),
            blockTimeId: YupString(),
            reason: YupString(),
            startTime: YupDate(),
            endTime: YupDate().when('startTime', (startTime, schema) => {
              if (startTime) {
                return YupDate()
                  .min(startTime, 'End time must be after Start time')
                  .typeError('End time is required')
              }
              return schema
            }),
            source: YupString(),
          })
        )
        .optional(),
    })
  ),
  surgeonIds: YupArray().of(YupString()),
})

export const BlockForm = ({
  title,
  initialValues,
  onSubmit,
}: {
  title: BlockFormTitle
  initialValues: FormikBlockValue
  onSubmit: (
    data: Required<Omit<BlockWithBlockTime, 'archivedTime'>> & {
      orgId?: string
    }
  ) => Promise<void>
}) => {
  const { timezone } = useTimezone()
  const { sites } = useSiteOptions()
  const rooms = useRoomOptions()
  const breadcrumbs = useBreadCrumbs()

  const { data: userData } = useQuery<GetCurrentUserName>(GET_CURRENT_USER_NAME)
  const [activeReleaseModalId, setActiveReleaseModalId] = useState<
    string | undefined
  >(undefined)
  const [orderBys, setOrderBys] = useState<OrderBy[]>(DEFAULT_ORDER_BY)
  const [filterBys, setFilterBys] = useState<FilterBy>(DEFAULT_FILTER_BY)
  const autoSelectSiteId = sites.length === 1 ? sites[0].node.id : null

  const showResetFiltersButton = useMemo(() => {
    return !isEqual(filterBys, DEFAULT_FILTER_BY)
  }, [filterBys])

  return (
    <PageContentTemplate
      title={title}
      maxContainerSize="largeForm"
      breadcrumbs={breadcrumbs}
    >
      <Formik
        enableReinitialize
        initialValues={initialValues}
        validationSchema={blockFormSchema}
        onSubmit={async ({
          id,
          name,
          color,
          blockTimes,
          surgeonIds,
          siteIds,
        }) => {
          const blockTimesWithReleases = blockTimes.map((blockTime) => ({
            ...pick(blockTime, ['id', 'roomId', 'startTime', 'endTime']),
            releases: blockTime.releases?.map((r) =>
              pick(r, [
                'id',
                'blockTimeId',
                'reason',
                'toBlock',
                'startTime',
                'endTime',
                'releasedTime',
                'source',
                'unreleasedTime',
                'unreleasedSource',
              ])
            ),
          }))

          await onSubmit({
            id,
            name,
            color,
            surgeonIds,
            blockTimes: blockTimesWithReleases,
            siteIds: siteIds ? siteIds : [],
          })
        }}
        validate={validateTimeRanges}
      >
        {({ values, errors, setFieldValue, isSubmitting }) => {
          const futureBlockTimesExist = values.blockTimes?.some(
            (blockTime: BlockTimeWithSiteInfo) =>
              DateTime.fromISO(blockTime.startTime).startOf('day') >
              DateTime.local().startOf('day')
          )
          return (
            <Form>
              <Section>
                <fieldset
                  disabled={Boolean(values.archivedTime) || isSubmitting}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: remSpacing.small,
                    }}
                  >
                    <div>
                      <Label htmlFor="site" required>
                        Site(s)
                      </Label>
                      <div
                        style={{
                          display: 'inline-block',
                          padding: `${remSpacing.xsmall} 0`,
                        }}
                      >
                        <SitesFilter
                          sites={sites}
                          selectedSiteIds={
                            values.siteIds && values.siteIds.length > 0
                              ? values.siteIds
                              : autoSelectSiteId
                                ? [autoSelectSiteId]
                                : []
                          }
                          onChangeSites={(siteIds: string[] | undefined) => {
                            setFieldValue('siteIds', siteIds)
                          }}
                          multipleSites
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="name" required>
                        Name
                      </Label>
                      <div
                        style={{
                          display: 'inline-block',
                        }}
                      >
                        <Input.Text
                          width={rem('300px')}
                          name="name"
                          placeholder="Enter name"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="color" required>
                        Color
                      </Label>
                      <div
                        style={{
                          display: 'inline-block',
                          padding: `${remSpacing.xsmall} 0`,
                        }}
                      >
                        <ColorSelector
                          name="color"
                          initialValue={initialValues.color}
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="surgeons" required>
                        Surgeon(s)
                      </Label>
                      <div
                        style={{
                          display: 'inline-block',
                          padding: `${remSpacing.xsmall} 0`,
                        }}
                      >
                        <SurgeonSelector
                          placeholder="Select surgeon(s)"
                          name="surgeonIds"
                          selectedIds={values.surgeonIds ?? []}
                          onChange={(surgeonIds) => {
                            setFieldValue('surgeonIds', surgeonIds)
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </fieldset>
              </Section>

              <Section>
                <Row css={{ justifyContent: 'space-between' }}>
                  <Col>
                    <H4 as="h2">Block times</H4>
                  </Col>
                </Row>

                <Tile>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: `${remSpacing.small} ${remSpacing.medium}`,
                      height: remSpacing.xlarge,
                      gap: remSpacing.small,
                    }}
                  >
                    <DatePicker
                      value={filterBys.dateRange}
                      selectRange={true}
                      showPresets={true}
                      timezone={timezone}
                      setValue={(
                        dates:
                          | Date
                          | [Date | null, Date | null]
                          | null
                          | undefined
                      ) =>
                        Array.isArray(dates) &&
                        dates[0] &&
                        dates[1] &&
                        setFilterBys((prev) => ({
                          ...prev,
                          dateRange: dates as [Date, Date],
                        }))
                      }
                    />

                    <ISODayOfWeekFilter
                      selected={filterBys.daysOfWeek}
                      onChange={(daysOfWeek) => {
                        setFilterBys((prev) => ({
                          ...prev,
                          daysOfWeek,
                        }))
                      }}
                    />

                    <SitesRoomsFilter
                      sites={sites}
                      selectedSiteIds={filterBys.siteIds}
                      onChangeSites={(siteIds: string[] | undefined) =>
                        setFilterBys((prev) => {
                          if (siteIds)
                            return {
                              ...prev,
                              siteIds,
                            }
                          return { ...prev, siteIds: [] }
                        })
                      }
                      rooms={rooms}
                      selectedRoomIds={filterBys.roomIds}
                      onChangeRooms={(roomIds: string[] | undefined) =>
                        setFilterBys((prev) => {
                          if (roomIds)
                            return {
                              ...prev,
                              roomIds,
                            }
                          return { ...prev, roomIds: [] }
                        })
                      }
                      multipleSites
                      bulkSelectSites
                      bulkSelectRooms
                    />

                    {showResetFiltersButton && (
                      <ResetFilters
                        type="button"
                        resetActions={() => setFilterBys(DEFAULT_FILTER_BY)}
                      />
                    )}
                  </div>

                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: `${remSpacing.small} ${remSpacing.medium}`,
                      height: remSpacing.xlarge,
                      gap: remSpacing.small,
                    }}
                  >
                    <div style={{ width: rem('110px') }}>
                      <ClickableHeader
                        label="Date"
                        fieldName="startTime"
                        orderBys={orderBys}
                        setOrderBys={setOrderBys}
                      />
                    </div>

                    <div style={{ width: rem('85px') }}>
                      <ClickableHeader
                        label="Day"
                        fieldName="dayOfWeek"
                        orderBys={orderBys}
                        setOrderBys={setOrderBys}
                      />
                    </div>

                    <div style={{ width: rem('225px') }}>
                      <Span2 style={{ paddingLeft: rem('12px') }}>Hours</Span2>
                    </div>

                    <div style={{ width: rem('140px') }}>
                      <ClickableHeader
                        label="Site"
                        fieldName="siteId"
                        orderBys={orderBys}
                        setOrderBys={setOrderBys}
                      />
                    </div>

                    <div>
                      <ClickableHeader
                        label="Room"
                        fieldName="roomId"
                        orderBys={orderBys}
                        setOrderBys={setOrderBys}
                      />
                    </div>
                  </div>

                  <BlockFormBlockTimeArray
                    values={values}
                    errors={errors}
                    title={title}
                    userData={userData}
                    activeReleaseModalId={activeReleaseModalId}
                    setActiveReleaseModalId={setActiveReleaseModalId}
                    orderBys={orderBys}
                    filterBys={filterBys}
                  />
                </Tile>
              </Section>

              <div
                style={{
                  margin: `${remSpacing.medium} 0 ${remSpacing.large}`,
                  gap: remSpacing.small,
                  justifyContent: 'space-between',
                  display: 'flex',
                }}
              >
                <ButtonLink
                  appearance="link"
                  to={{ pathname: LocationPath.Blocks }}
                >
                  Back
                </ButtonLink>

                <div style={{ gap: remSpacing.small, display: 'flex' }}>
                  {title === BlockFormTitle.Edit &&
                    (!initialValues.archivedTime ? (
                      <ArchiveButton
                        blockId={values.id}
                        futureBlockTimesExist={futureBlockTimesExist}
                      />
                    ) : (
                      <RestoreButton blockId={values.id} />
                    ))}

                  <Button
                    type="submit"
                    disabled={
                      isEqual(initialValues, values) ||
                      !isEqual(errors, {}) ||
                      isSubmitting
                    }
                  >
                    <Save size="sm" />{' '}
                    {title === BlockFormTitle.Create ? 'Create' : 'Save'}
                  </Button>
                </div>
              </div>
            </Form>
          )
        }}
      </Formik>
    </PageContentTemplate>
  )
}
