import { useMemo } from 'react'

import { FormikErrors } from 'formik'
import { DateTime } from 'luxon'
import { rem } from 'polished'

import {
  AdditionalText,
  DatePicker,
  FlexContainer,
  FlexItem,
  remSpacing,
  Span2,
} from '@apella/component-library'
import { isoWeekDateToDayOfWeek } from 'src/components/DayOfWeekFilter'
import { useTimezone } from 'src/Contexts'
import { formatHourMinute } from 'src/pages/Schedule/utils'

import { BlockTime, BlockTimeWithoutReleases } from '../types'
import { HHmmInput } from './HHmmInput'

export interface DateTimeRangePickerProps {
  blockTime: BlockTimeWithoutReleases
  disabled?: boolean
  error?: string | FormikErrors<BlockTime>
  index: number
  readOnly?: boolean
  replace: <X = any>(index: number, value: X) => void
}

export const DateTimeRangePicker = ({
  blockTime,
  replace,
  index,
  error,
  readOnly = false,
  disabled = false,
}: DateTimeRangePickerProps) => {
  const { timezone } = useTimezone()
  const errorMsg = useMemo(() => {
    if (typeof error === 'string') {
      return error
    } else if (error?.endTime) {
      return error.endTime
    }
    return undefined
  }, [error])

  return (
    <FlexContainer gap={remSpacing.small}>
      <FlexItem style={{ width: rem('110px') }}>
        <DatePicker
          disabled={disabled}
          readOnly={readOnly}
          showIcon={false}
          showArrow={true}
          timezone={timezone}
          value={DateTime.fromISO(blockTime.startTime)
            .setZone(timezone)
            .startOf('day')
            .toJSDate()}
          setValue={(
            date: Date | [Date | null, Date | null] | null | undefined
          ) => {
            if (!!date && !Array.isArray(date)) {
              const startTime = DateTime.fromISO(blockTime.startTime)
              const endTime = DateTime.fromISO(blockTime.endTime)
              const [newDate] = DateTime.fromJSDate(date)
                .setZone(timezone)
                .toISO()
                .split('T')

              const newStartTime = DateTime.fromFormat(newDate, 'yyyy-MM-dd')
                .plus({
                  hour: startTime.get('hour'),
                  minute: startTime.get('minute'),
                })
                .toISO()
              const newEndTime = DateTime.fromFormat(newDate, 'yyyy-MM-dd')
                .plus({
                  hour: endTime.get('hour'),
                  minute: endTime.get('minute'),
                })
                .toISO()

              replace(index, {
                ...blockTime,
                startTime: newStartTime,
                endTime: newEndTime,
              })
            }
          }}
        />
      </FlexItem>

      <Span2
        style={{
          width: rem('85px'),
          textTransform: 'capitalize',
          paddingTop: remSpacing.xsmall,
        }}
      >
        {
          isoWeekDateToDayOfWeek[
            DateTime.fromISO(blockTime.startTime)
              .setZone(timezone)
              .startOf('day').weekday
          ]
        }
      </Span2>

      <FlexContainer direction="column">
        <FlexContainer
          style={{ width: rem('225px'), alignItems: 'top', gap: 8 }}
        >
          <HHmmInput
            disabled={disabled}
            name={`blockTimes.${index}.startTime`}
            width={80}
            value={formatHourMinute(blockTime.startTime, timezone)}
            onChange={(e) => {
              const [hour, minute] = e.target.value.split(':').map(Number)

              replace(index, {
                ...blockTime,
                startTime: DateTime.fromISO(blockTime.startTime)
                  .setZone(timezone)
                  .startOf('day')
                  .plus({ hour, minute })
                  .toISO(),
              })
            }}
            error={errorMsg}
          />
          <FlexItem style={{ marginTop: rem('12px') }}>-</FlexItem>

          <HHmmInput
            disabled={disabled}
            name={`blockTimes.${index}.endTime`}
            width={80}
            value={formatHourMinute(blockTime.endTime, timezone)}
            onChange={(e) => {
              const [hour, minute] = e.target.value.split(':').map(Number)

              replace(index, {
                ...blockTime,
                endTime: DateTime.fromISO(blockTime.endTime)
                  .setZone(timezone)
                  .startOf('day')
                  .plus({ hour, minute })
                  .toISO(),
              })
            }}
            error={errorMsg}
          />
        </FlexContainer>
        {errorMsg && (
          <FlexItem
            style={{
              maxWidth: rem('225px'),
            }}
          >
            <AdditionalText error={errorMsg} />
          </FlexItem>
        )}
      </FlexContainer>
    </FlexContainer>
  )
}
