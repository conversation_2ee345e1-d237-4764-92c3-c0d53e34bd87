import { ChangeEvent, useId, useState } from 'react'

import {
  AdditionalText,
  BaseInputProps,
  useInputCSS,
} from '@apella/component-library'

interface HHmmInputProps extends BaseInputProps {
  onChange?: React.ChangeEventHandler<HTMLInputElement>
}

export const HHmmInput = ({
  value,
  size = 'md',
  success,
  error = '',
  hint = '',
  width,
  name,
  id: idProp,
  onChange,
  ...props
}: HHmmInputProps) => {
  const inputCss = useInputCSS(size, success, error, width)

  const uniqueId = useId()
  const id = idProp || `${name}-${uniqueId}`

  const [time, setTime] = useState(value)
  const [isValid, setIsValid] = useState(true)

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value
    setTime(inputValue)

    // Regular expression to validate the format "hh:mm"
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/

    if (timeRegex.test(inputValue)) {
      setIsValid(true)
      onChange?.(event)
    } else {
      setIsValid(false)
    }
  }

  return (
    <div>
      <input
        maxLength={5}
        id={id}
        css={inputCss}
        type="text"
        value={time}
        onChange={handleInputChange}
        placeholder="hh:mm"
        style={{ marginTop: 0 }}
        {...props}
      />
      {!isValid && (
        <AdditionalText
          error={'Format (hh:mm)'}
          hint={hint}
          success={success}
        />
      )}
    </div>
  )
}
