import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { ErrorMessage, FieldArray, FormikErrors } from 'formik'
import { orderBy } from 'lodash'
import { DateTime } from 'luxon'
import { rem } from 'polished'

import {
  <PERSON><PERSON>,
  Close,
  ComponentTheme,
  <PERSON>rror,
  FlexContainer,
  FlexItem,
  Plus,
  Span2,
  VisuallyHidden,
  fontRamp,
  remSpacing,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'
import { useRoomOptions } from 'src/utils/useSiteRoomsOptions'

import { GetCurrentUserName } from './__generated__'
import { BlockTimeReleaseBlade } from './BlockTimeReleaseBlade'
import { DateTimeRangePicker } from './DateTimeRangePicker/DateTimeRangePicker'
import { ReleaseInfoLine } from './ReleaseInfoLine'
import { RoomSelector } from './RoomSelector/RoomSelector'
import {
  BlockFormTitle,
  BlockTimeWithSiteInfo,
  FilterBy,
  FormikBlockValue,
  OrderBy,
} from './types'

export const BlockFormBlockTimeArray = ({
  values,
  errors,
  title,
  userData,
  activeReleaseModalId,
  orderBys,
  filterBys,
  setActiveReleaseModalId: setActiveReleaseModal,
}: {
  values: FormikBlockValue
  errors: FormikErrors<FormikBlockValue>
  title: BlockFormTitle
  userData: GetCurrentUserName | undefined
  activeReleaseModalId: string | undefined
  orderBys: OrderBy[]
  filterBys: FilterBy
  setActiveReleaseModalId: (id: string | undefined) => void
}) => {
  const theme: ComponentTheme = useTheme()
  const { timezone } = useTimezone()
  const rooms = useRoomOptions()

  /**
   * filteredAndSortedValues are used to display the block times in the UI.
   * The mutation (push/replace/remove) will be applied to the original
   * values (values.blockTimes)
   */
  const filteredAndSortedValues: FormikBlockValue = useMemo(() => {
    const filteredBlockTimes = values.blockTimes.filter((blockTime) => {
      if (filterBys.daysOfWeek.length > 0) {
        const dayOfWeek = DateTime.fromISO(blockTime.startTime).weekday
        if (!filterBys.daysOfWeek.includes(dayOfWeek)) {
          return false
        }
      }

      if ((filterBys.siteIds ?? []).length > 0) {
        if (!filterBys.siteIds!.includes(blockTime.room.site.id)) {
          return false
        }
      }

      if (filterBys.roomIds.length > 0) {
        if (!filterBys.roomIds.includes(blockTime.roomId)) {
          return false
        }
      }

      return (
        DateTime.fromJSDate(filterBys.dateRange[0]).startOf('day') <=
          DateTime.fromISO(blockTime.startTime) &&
        DateTime.fromJSDate(filterBys.dateRange[1]).endOf('day') >=
          DateTime.fromISO(blockTime.endTime)
      )
    })

    const orderByIteratees = orderBys.map(
      (orderBy) => (blockTime: BlockTimeWithSiteInfo) => {
        if (orderBy.field === 'dayOfWeek') {
          return DateTime.fromISO(blockTime.startTime).weekday
        }
        if (orderBy.field === 'siteId') {
          return blockTime.room.site.id
        }
        if (orderBy.field === 'startTime') {
          return DateTime.fromISO(blockTime.startTime).toMillis()
        }
        return blockTime[orderBy.field]
      }
    )

    return {
      ...values,
      blockTimes: orderBy<BlockTimeWithSiteInfo>(
        filteredBlockTimes,
        orderByIteratees,
        orderBys.map((orderBy) => orderBy.value)
      ),
    }
  }, [values, orderBys, filterBys])

  const numOfHiddenEntries =
    values.blockTimes.length - filteredAndSortedValues.blockTimes.length

  const activeBlockTimeBasedOnActiveReleaseModalId = useMemo(
    () => values.blockTimes.find((bt) => bt.id === activeReleaseModalId),
    [values.blockTimes, activeReleaseModalId]
  )
  const originalIdx = activeBlockTimeBasedOnActiveReleaseModalId
    ? values.blockTimes.findIndex((bt) => bt.id === activeReleaseModalId)
    : -1

  return (
    <fieldset disabled={Boolean(values.archivedTime)}>
      <FieldArray name="blockTimes">
        {({ push, remove, replace }) => {
          return (
            <div>
              {filteredAndSortedValues.blockTimes.length === 0 && (
                <ErrorMessage
                  name="blockTimes"
                  component="div"
                  css={{
                    padding: `${remSpacing.small} ${remSpacing.medium}`,
                    color: theme.palette.red[50],
                    ...fontRamp['13px'],
                  }}
                />
              )}

              {activeBlockTimeBasedOnActiveReleaseModalId &&
                originalIdx > -1 && (
                  <BlockTimeReleaseBlade
                    userData={userData?.me}
                    isBladeOpen={true}
                    onBladeClose={() => setActiveReleaseModal(undefined)}
                    blockTime={activeBlockTimeBasedOnActiveReleaseModalId}
                    originalIdx={originalIdx}
                    blockName={values.name}
                    replace={replace}
                  />
                )}

              {filteredAndSortedValues.blockTimes.length > 0 &&
                filteredAndSortedValues.blockTimes.map(
                  (blockTime: BlockTimeWithSiteInfo) => {
                    const originalIdx = values.blockTimes.findIndex(
                      (bt) => bt.id === blockTime.id
                    )
                    return (
                      <FlexContainer key={blockTime.id} direction="column">
                        <FlexContainer
                          alignItems="top"
                          justifyContent="space-between"
                          css={{
                            borderTop: `1px solid ${theme.palette.gray[30]}`,
                            padding: `${remSpacing.small} ${remSpacing.medium}`,
                            gap: remSpacing.small,
                          }}
                        >
                          <FlexItem style={{ width: rem('444px') }}>
                            <DateTimeRangePicker
                              disabled={blockTime.releases.length > 0}
                              blockTime={blockTime}
                              replace={replace}
                              index={originalIdx}
                              error={errors.blockTimes?.[originalIdx]}
                            />
                          </FlexItem>

                          <FlexItem>
                            <RoomSelector
                              disabled={blockTime.releases.length > 0}
                              showSite
                              name={`blockTimes.${originalIdx}.roomId`}
                              value={blockTime.roomId}
                              search={true}
                              onChange={(newRoomId) => {
                                replace(originalIdx, {
                                  ...blockTime,
                                  roomId: newRoomId,
                                  room: {
                                    ...rooms.find(
                                      (r) => r.node.id === newRoomId
                                    )?.node,
                                  },
                                })
                              }}
                              error={
                                <ErrorMessage
                                  name={`blockTimes.${originalIdx}.roomId`}
                                >
                                  {(msg) => (
                                    <div
                                      style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        marginTop: remSpacing.xsmall,
                                        color: theme.palette.red[50],
                                        ...fontRamp['13px'],
                                        gap: remSpacing.xxsmall,
                                      }}
                                    >
                                      <Error
                                        size="sm"
                                        color={theme.palette.red[50]}
                                      />
                                      {msg}
                                    </div>
                                  )}
                                </ErrorMessage>
                              }
                            />
                          </FlexItem>

                          {blockTime.releases.length == 0 && (
                            <Button
                              style={{
                                height: rem('36px'),
                                visibility:
                                  title === BlockFormTitle.Edit
                                    ? 'visible'
                                    : 'hidden',
                              }}
                              type="button"
                              size="sm"
                              appearance="link"
                              buttonType="icon"
                              onClick={() =>
                                setActiveReleaseModal(blockTime.id)
                              }
                            >
                              {'Release'}
                            </Button>
                          )}

                          <FlexItem>
                            <Button
                              type="button"
                              size="sm"
                              appearance="link"
                              buttonType="icon"
                              onClick={() => {
                                remove(originalIdx)
                              }}
                            >
                              <Close size="md" color="red" />
                              <VisuallyHidden>Delete</VisuallyHidden>
                            </Button>
                          </FlexItem>
                        </FlexContainer>

                        <ReleaseInfoLine blockTime={blockTime} />
                      </FlexContainer>
                    )
                  }
                )}

              <FlexContainer css={{ justifyContent: 'space-between' }}>
                <FlexItem
                  style={{
                    padding: `0 ${remSpacing.medium} ${remSpacing.small}`,
                  }}
                >
                  {numOfHiddenEntries > 0 && (
                    <Span2
                      style={{
                        display: 'block',
                        width: '100%',
                        background: `${theme.palette.red[20]}40`,
                        color: theme.palette.text.primary,
                        padding: remSpacing.xxsmall,
                      }}
                    >
                      {numOfHiddenEntries}{' '}
                      {numOfHiddenEntries > 1 ? 'entries are' : 'entry is'}{' '}
                      hidden due to filters
                    </Span2>
                  )}
                </FlexItem>

                <Button
                  type="button"
                  appearance="link"
                  onClick={() => {
                    const newBlockTime = {
                      id: crypto.randomUUID(),
                      blockId: filteredAndSortedValues.id,
                      startTime: DateTime.local()
                        .setZone(timezone)
                        .startOf('day')
                        .plus({ hour: 7, minutes: 30 })
                        .toISO(),
                      endTime: DateTime.local()
                        .setZone(timezone)
                        .startOf('day')
                        .plus({ hour: 17 })
                        .toISO(),
                      roomId: '',
                      releases: [],
                    }

                    // Working with active list of block times rather than original values
                    if (filteredAndSortedValues.blockTimes.length === 0) {
                      push(newBlockTime)
                    } else {
                      // pre-populate using last block time attributes
                      push({
                        ...filteredAndSortedValues.blockTimes[
                          filteredAndSortedValues.blockTimes.length - 1
                        ],
                        id: newBlockTime.id,
                        releases: [],
                      })
                    }
                  }}
                >
                  <Plus size="sm" /> Add block time
                </Button>
              </FlexContainer>
            </div>
          )
        }}
      </FieldArray>
    </fieldset>
  )
}
