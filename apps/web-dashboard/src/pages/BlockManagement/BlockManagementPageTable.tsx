import { useCallback, useMemo } from 'react'
import { generatePath, useNavigate } from 'react-router'

import { uniq } from 'lodash'

import {
  Badge,
  Button,
  Edit,
  OrderBy,
  PaginatedTableColumn,
  RemotePaginatedTable,
} from '@apella/component-library'
import { LocationPath } from 'src/router/types'

import { GetPrimarySurgeons, ListBlocks } from './__generated__'

export interface BlockManagementPageTableProps {
  data: ListBlocks | undefined
  loading: boolean
  onChangePage: (after?: string) => void
  onChangeSort: (orderBy?: OrderBy[]) => void
  showArchived: boolean
  sortOrder: OrderBy[] | undefined
  surgeons: (GetPrimarySurgeons['staff']['edges'][number]['node'] & {
    name: string
  })[]
}

export const BlockManagementPageTable = ({
  showArchived,
  surgeons,
  data,
  loading,
  sortOrder,
  onChangeSort,
  onChangePage,
}: BlockManagementPageTableProps) => {
  const navigate = useNavigate()

  const pageCursors = data?.blocks.pageCursors

  const getSurgeonNameOrEmpty = useCallback(
    (surgeonId: string) => surgeons.find((s) => s.id === surgeonId)?.name ?? '',
    [surgeons]
  )

  const tableData = useMemo(() => {
    const rawData = data?.blocks?.edges?.map((edge) => edge?.node) ?? []

    return rawData
      .filter((block) => (showArchived ? true : !block.archivedTime)) // Help keeping up-to-date with recent archive/unarchive of blocks
      .map((block) => ({
        ...block,
        surgeons: block.surgeonIds.map(getSurgeonNameOrEmpty).join('; '),
        numOfBlockTimes: block.blockTimes.length,
        siteNames: uniq(
          block.blockTimes.map((blockTime) => blockTime.room.site.name)
        ).join(', '),
        roomNames: uniq(
          block.blockTimes.map((blockTime) => blockTime.room.name)
        ).join(', '),
      }))
  }, [data?.blocks, showArchived, getSurgeonNameOrEmpty])

  const columns = useMemo(() => {
    const cols: PaginatedTableColumn<(typeof tableData)[number]>[] = [
      {
        name: 'Name',
        selector: 'name',
        sortAttribute: 'name',
      },
      {
        name: 'Sites',
        selector: 'siteNames',
      },
      {
        name: 'Rooms',
        selector: 'roomNames',
      },
      { name: 'Surgeons', selector: 'surgeons' },
      {
        name: 'Block times',
        selector: 'numOfBlockTimes',
        formatter: (num: number) => (
          <Badge variant="secondary" color="gray">
            {num}
          </Badge>
        ),
      },
    ]

    if (showArchived) {
      cols.push({
        name: 'Archived',
        selector: 'archivedTime',
        formatter: (archivedTime: string) => (
          <Badge variant="secondary" color="gray">
            {archivedTime && 'Yes'}
          </Badge>
        ),
      })
    }

    cols.push({
      name: 'Actions',
      selector: 'id',
      formatter: (id: string) => (
        <Button
          color="alternate"
          appearance="link"
          onClick={() =>
            navigate(
              generatePath(LocationPath.BlockEditor, {
                blockId: id,
              })
            )
          }
        >
          <Edit size="sm" color="gray" />
        </Button>
      ),
    })

    return cols
  }, [navigate, showArchived])

  return (
    <RemotePaginatedTable
      columns={columns}
      data={tableData}
      sortOrder={sortOrder}
      onChangeSort={(orderBy?: OrderBy[]) => onChangeSort(orderBy)}
      isLoading={loading}
      paginationType="cursor"
      rowKeySelector={(rowData) => rowData.id}
      isMultiSort={false}
      onChangePage={(cursorItem) => {
        onChangePage(cursorItem.cursor)
      }}
      {...pageCursors}
    />
  )
}
