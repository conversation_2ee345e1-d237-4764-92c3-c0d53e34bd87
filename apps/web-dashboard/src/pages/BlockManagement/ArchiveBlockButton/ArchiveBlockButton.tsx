import { useCallback } from 'react'
import { toast } from 'react-toastify'

import { ApolloError, useMutation } from '@apollo/client'

import { Button } from '@apella/component-library'

import {
  ArchiveBlock,
  ArchiveBlockVariables,
  UnarchiveBlock,
  UnarchiveBlockVariables,
} from '../__generated__'
import { ARCHIVE_BLOCK, UNARCHIVE_BLOCK, GET_BLOCK } from '../queries'

function useArchiveBlockMutations(blockId: string) {
  const onCompleted = (data: any) => {
    if (data.block?.id) {
      toast.success(`Block '${data.block.name}' archived`)
    }
  }

  const onError = (error: ApolloError) => {
    toast.error(error.message)
  }

  const [archiveBlock] = useMutation<ArchiveBlock, ArchiveBlockVariables>(
    ARCHIVE_BLOCK,
    {
      onCompleted,
      onError,
      refetchQueries: [
        {
          query: GET_BLOCK,
          variables: { id: blockId },
        },
      ],
    }
  )

  const [unarchiveBlock] = useMutation<UnarchiveBlock, UnarchiveBlockVariables>(
    UNARCHIVE_BLOCK,
    {
      onCompleted,
      onError,
      refetchQueries: [
        {
          query: GET_BLOCK,
          variables: { id: blockId },
        },
      ],
    }
  )

  const archive = useCallback(() => {
    if (confirm('Are you sure you want to archive this block?')) {
      archiveBlock({ variables: { blockId } })
    }
  }, [archiveBlock, blockId])

  const unarchive = useCallback(() => {
    if (confirm('Are you sure you want to restore this block?')) {
      unarchiveBlock({ variables: { blockId } })
    }
  }, [unarchiveBlock, blockId])

  return { archive, unarchive }
}

export function ArchiveButton({
  blockId,
  futureBlockTimesExist,
}: Readonly<{ blockId: string; futureBlockTimesExist: boolean }>) {
  const { archive } = useArchiveBlockMutations(blockId)

  return (
    <Button
      type="button"
      color="error"
      onClick={() => {
        if (futureBlockTimesExist) {
          alert(
            'Cannot archive block because there still exist block times in the future. Please remove them to continue'
          )
        } else {
          archive()
        }
      }}
    >
      Archive
    </Button>
  )
}

export function RestoreButton({ blockId }: Readonly<{ blockId: string }>) {
  const { unarchive } = useArchiveBlockMutations(blockId)

  return (
    <Button color="alternate" type="button" onClick={unarchive}>
      Restore
    </Button>
  )
}
