import { ReactElement } from 'react'

import { rem } from 'polished'

import {
  FlexContainer,
  Option,
  remSpacing,
  SingleSelect,
  Span2,
} from '@apella/component-library'
import { useRoomOptions } from 'src/utils/useSiteRoomsOptions'

interface RoomSelectorProps {
  disabled?: boolean
  error?: ReactElement
  name: string
  onChange: (value?: string) => void
  readOnly?: boolean
  search?: boolean
  showSite?: boolean
  value: string
  width?: number | string
}

export function RoomSelector({
  showSite = false,
  width = 130,
  error,
  ...props
}: RoomSelectorProps) {
  const rooms = useRoomOptions()

  return (
    <FlexContainer gap="8px">
      {showSite && (
        <Span2
          style={{
            minWidth: rem('140px'),
            textTransform: 'capitalize',
            paddingTop: remSpacing.xsmall,
          }}
        >
          {rooms.find((room) => room.node.id === props.value)?.node.site.name ??
            'Select a room'}
        </Span2>
      )}

      <SingleSelect css={{ width, height: 40 }} {...props}>
        {rooms.map((room) => (
          <Option
            key={room.node.id}
            value={room.node.id}
            label={room.node.name}
            group={room.node.site.name}
          />
        ))}
      </SingleSelect>
      {error}
    </FlexContainer>
  )
}
