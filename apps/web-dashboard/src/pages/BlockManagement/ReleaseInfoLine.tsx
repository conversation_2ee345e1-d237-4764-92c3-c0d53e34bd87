import { useTheme } from '@emotion/react'

import {
  ComponentTheme,
  FlexItem,
  fontWeights,
  remSpacing,
  Span3,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'

import { formatHourMinute, getStartToEndTimeString } from '../Schedule/utils'
import { BlockTime } from './types'
import { getReleaseByAtDateString } from './utils'

export const ReleaseInfoLine = ({ blockTime }: { blockTime: BlockTime }) => {
  const theme: ComponentTheme = useTheme()
  const { timezone } = useTimezone()

  if (
    !blockTime.releases ||
    blockTime.releases.length === 0 ||
    !!blockTime.releases[0].unreleasedTime
  ) {
    return null
  }

  const blockTimeRelease = blockTime.releases[0]

  const blockTimeStart = formatHourMinute(blockTime.startTime, timezone)
  const blockTimeEnd = formatHourMinute(blockTime.endTime, timezone)
  const releaseStart = formatHourMinute(blockTimeRelease.startTime, timezone)
  const releaseEnd = formatHourMinute(blockTimeRelease.endTime, timezone)

  return (
    <FlexItem
      style={{
        padding: `0 ${remSpacing.medium} ${remSpacing.small}`,
      }}
    >
      <Span3
        style={{
          display: 'block',
          width: '100%',
          background: `${theme.palette.violet[10]}40`,
          color: theme.palette.violet[70],
          padding: remSpacing.xsmall,
        }}
      >
        <Span3 style={{ ...fontWeights.semibold }}>
          {blockTimeStart === releaseStart && blockTimeEnd === releaseEnd
            ? 'Released'
            : 'Partially released'}
        </Span3>
        {' from '}
        <Span3 style={{ ...fontWeights.semibold }}>
          {getStartToEndTimeString(
            blockTimeRelease.startTime,
            blockTimeRelease.endTime,
            timezone
          )}
        </Span3>{' '}
        {getReleaseByAtDateString(blockTimeRelease, timezone)}
      </Span3>
    </FlexItem>
  )
}
