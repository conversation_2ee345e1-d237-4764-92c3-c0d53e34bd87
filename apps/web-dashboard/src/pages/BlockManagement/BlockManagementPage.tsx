import { memo, ReactElement, useCallback, useState } from 'react'
import { generatePath } from 'react-router'

import { useTheme } from '@emotion/react'

import { useQuery } from '@apollo/client'
import { rem } from 'polished'

import {
  ArrowDropDown,
  Button,
  ButtonLink,
  CheckBox,
  CheckBoxOutlineBlank,
  DatePicker,
  Dropdown,
  DropdownWrapper,
  FlexContainer,
  H6,
  Plus,
  remSpacing,
  Upload,
  ZIndex,
} from '@apella/component-library'
import { ISODayOfWeekFilter } from 'src/components/DayOfWeekFilter'
import { MultiFilterWithCount } from 'src/components/Filters/FilterWithCount'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { ResetFilters } from 'src/components/ResetFilters'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { useTimezone } from 'src/Contexts'
import { LocationPath } from 'src/router/types'

import { ListBlocks, ListBlocksVariables } from './__generated__'
import { BlockManagementPageTable } from './BlockManagementPageTable'
import { BlockManagementUploadCSVBlade } from './BlockManagementUploadCSVBlade'
import { DEFAULT_BLOCK_FILTER_LABEL } from './constants'
import { LIST_BLOCKS } from './queries'
import { SurgeonSelector } from './SurgeonSelector/SurgeonSelector'
import { CSVContentType } from './types'
import { useBlockManagementPageFilters } from './useBlockManagementPageFilters'

interface BlockCsvUploadBladeState {
  contentType: CSVContentType
  isBladeOpen: boolean
}

export const BlockManagementPage = () => {
  const { timezone } = useTimezone()
  const theme = useTheme()
  const {
    minEndTime,
    maxStartTime,
    sites,
    siteIds,
    rooms,
    roomIds,
    surgeons,
    surgeonIds,
    blocks,
    blockIds,
    daysOfWeek,
    showResetFiltersButton,
    pagingState,
    orderByState,
    showArchived,
    setShowArchived,
    localOnChangeDateRanges,
    onChangeDaysOfWeek,
    onChangeRooms,
    onChangeSites,
    onChangeSurgeons,
    onChangeBlocks,
    onChangeSort,
    onChangePage,
    resetActions,
  } = useBlockManagementPageFilters()

  const onToggleArchived = useCallback(
    () => setShowArchived((prev) => !prev),
    [setShowArchived]
  )

  const { loading, data: blockData } = useQuery<
    ListBlocks,
    ListBlocksVariables
  >(LIST_BLOCKS, {
    variables: {
      query: {
        minEndTime,
        maxStartTime,
        siteIds,
        roomIds,
        surgeonIds,
        daysOfWeek,
        ids: blockIds,
        includeArchived: showArchived,
      },
      orderBy: orderByState,
      ...pagingState,
    },
    fetchPolicy: 'network-only',
  })

  const [bladeState, setBladeState] = useState<BlockCsvUploadBladeState>({
    isBladeOpen: false,
    contentType: CSVContentType.BlockTime,
  })

  const checkboxStyle = {
    color: showArchived ? theme.palette.blue[50] : theme.palette.gray[50],
    size: rem('20px'),
  }

  return (
    <PageContentTemplate
      title="Block Management"
      actions={
        <>
          <UploadCsvSelector label="Upload" icon={<Upload size="xs" />}>
            <FlexContainer
              gap={remSpacing.xxsmall}
              css={{
                flexDirection: 'column',
                alignItems: 'flex-start',
              }}
            >
              <UploadCsvButton
                label="Block times"
                onClick={() =>
                  setBladeState({
                    isBladeOpen: true,
                    contentType: CSVContentType.BlockTime,
                  })
                }
              />

              <UploadCsvButton
                label="Block releases"
                onClick={() =>
                  setBladeState({
                    isBladeOpen: true,
                    contentType: CSVContentType.BlockRelease,
                  })
                }
              />
            </FlexContainer>
          </UploadCsvSelector>
          <ButtonLink size="sm" to={generatePath(LocationPath.BlockNew)}>
            <Plus size="xs" />
            Create Block
          </ButtonLink>
        </>
      }
      filters={
        <>
          <DatePicker
            value={[new Date(minEndTime), new Date(maxStartTime)]}
            selectRange={true}
            showPresets={true}
            timezone={timezone}
            setValue={(dates) =>
              Array.isArray(dates) &&
              dates[0] &&
              dates[1] &&
              localOnChangeDateRanges([dates[0], dates[1]])
            }
          />
          <ISODayOfWeekFilter
            selected={daysOfWeek}
            onChange={onChangeDaysOfWeek}
          />
          <SitesRoomsFilter
            sites={sites}
            selectedSiteIds={siteIds}
            onChangeSites={(siteId) => onChangeSites(siteId)}
            rooms={rooms}
            selectedRoomIds={roomIds}
            onChangeRooms={onChangeRooms}
            multipleSites
            bulkSelectSites
            bulkSelectRooms
          />

          <SurgeonSelector
            name="surgeonIds"
            selectedIds={surgeonIds ?? []}
            onChange={onChangeSurgeons}
          />

          <MultiFilterWithCount
            items={blocks}
            selectedIds={blockIds}
            onChange={onChangeBlocks}
            label={DEFAULT_BLOCK_FILTER_LABEL}
            bulkSelect={true}
            selectedOnTop
          />

          {showResetFiltersButton && (
            <ResetFilters resetActions={resetActions} />
          )}
          <Button
            onClick={onToggleArchived}
            appearance="link"
            buttonType="icon"
            color="black"
            css={{
              height: rem('40px'),
            }}
          >
            <FlexContainer
              gap={remSpacing.small}
              css={{
                height: rem('36px'),
                alignItems: 'center',
              }}
            >
              {showArchived ? (
                <CheckBox {...checkboxStyle} />
              ) : (
                <CheckBoxOutlineBlank {...checkboxStyle} />
              )}
              <H6>Archived</H6>
            </FlexContainer>
          </Button>
        </>
      }
    >
      {bladeState.isBladeOpen && (
        <BlockManagementUploadCSVBlade
          isBladeOpen={bladeState.isBladeOpen}
          onBladeClose={() =>
            setBladeState((prev) => ({ ...prev, isBladeOpen: false }))
          }
          contentType={bladeState.contentType}
        />
      )}
      <BlockManagementPageTable
        showArchived={showArchived}
        surgeons={surgeons}
        data={blockData}
        loading={loading}
        sortOrder={orderByState}
        onChangeSort={onChangeSort}
        onChangePage={onChangePage}
      />
    </PageContentTemplate>
  )
}

interface UploadCsvSelectorProps {
  children: ReactElement
  icon?: ReactElement
  label: string
}

const UploadCsvSelector = memo(function UploadCsvSelector({
  icon,
  children,
  label,
}: UploadCsvSelectorProps): React.JSX.Element {
  const theme = useTheme()
  const [isOpen, setIsOpen] = useState(false)

  const onClose = useCallback(() => {
    setIsOpen(false)
  }, [setIsOpen])

  const onClick = useCallback(() => {
    setIsOpen((isOpen) => !isOpen)
  }, [])

  return (
    <DropdownWrapper
      display="block"
      onClose={onClose}
      open={isOpen}
      css={{ width: 'fit-content', zIndex: ZIndex.MAX }}
    >
      <Button
        type="button"
        color="alternate"
        appearance="link"
        size="sm"
        onClick={onClick}
      >
        {icon}
        {label}
        <ArrowDropDown color={theme.palette.gray[50]} size="xs" />
      </Button>
      <Dropdown open={isOpen} below above={false} right left={false}>
        {children}
      </Dropdown>
    </DropdownWrapper>
  )
})

const UploadCsvButton = ({
  label,
  onClick,
}: {
  label: string
  onClick: () => void
}) => {
  return (
    <Button
      type="button"
      size="sm"
      appearance="link"
      color="alternate"
      onClick={onClick}
    >
      {label}
    </Button>
  )
}
