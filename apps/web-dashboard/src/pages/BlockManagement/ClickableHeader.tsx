import { <PERSON>Down, Arrow<PERSON><PERSON>, Button, Span2 } from '@apella/component-library'

import { Order<PERSON>y, Order<PERSON>y<PERSON>ey, OrderByValue } from './types'

export const ClickableHeader = ({
  label,
  fieldName,
  orderBys,
  setOrderBys,
}: {
  label: string
  fieldName: OrderByKey
  orderBys: OrderBy[]
  setOrderBys: React.Dispatch<React.SetStateAction<OrderBy[]>>
}) => {
  const idx = orderBys.findIndex((p) => p.field === fieldName)

  return (
    <Button
      type="button"
      appearance="link"
      style={{
        cursor: 'pointer',
        alignItems: 'center',
      }}
      onClick={() =>
        setOrderBys((prev) => {
          const pIdx = prev.findIndex((p) => p.field === fieldName)

          if (pIdx === -1) {
            return [
              ...prev,
              {
                field: fieldName,
                value: OrderByValue.ASC,
              },
            ]
          }

          if (prev[pIdx].value === OrderByValue.ASC) {
            const newOrderBys = [...prev]
            newOrderBys[pIdx].value = OrderByValue.DESC
            return newOrderBys
          }

          return [...prev.slice(0, pIdx), ...prev.slice(pIdx + 1)]
        })
      }
    >
      <Span2>{label}</Span2>{' '}
      {idx === -1 ? null : orderBys[idx].value === OrderByValue.ASC ? (
        <ArrowUp size="xs" />
      ) : (
        <ArrowDown size="xs" />
      )}
    </Button>
  )
}
