import { DateTime, Interval } from 'luxon'

import { ApellaDateTimeFormats } from '@apella/component-library'
import { Scalars } from 'src/__generated__/globalTypes'

import {
  OVER_LAPPING_TIME_RANGE_ERROR,
  ZERO_TIME_RANGE_ERROR,
} from './constants'
import { BlockTimeRelease } from './types'

export const parseBackendErrorMsg = (msg: string, timezone: string): string => {
  const matches = msg.match(
    /TIME_RANGE_ERROR - BLOCK_NAME '(.+)' BLOCK_TIME_RANGE \[(.+), (.+)\)/
  )
  if (matches?.length === 4) {
    const [, blockName, startTime, endTime] = matches
    const formattedStartTime = DateTime.fromFormat(
      startTime,
      'yyyy-MM-dd HH:mm:ss+00:00',
      { zone: 'utc' }
    )
      .setZone(timezone)
      .toLocaleString(ApellaDateTimeFormats.DATETIME)
    const formattedEndTime = DateTime.fromFormat(
      endTime,
      'yyyy-MM-dd HH:mm:ss+00:00',
      { zone: 'utc' }
    )
      .setZone(timezone)
      .toLocaleString(ApellaDateTimeFormats.DATETIME)
    return `Submitted time range(s) overlap with existing block time from block '${blockName}' covering [${formattedStartTime}, ${formattedEndTime})`
  }
  return msg
}
// get the active gaps based on releases in a given block time
export const getActiveBlockTimeGaps = (
  currentStartTime: number,
  currentEndTime: number,
  releases: { startTime: string; endTime: string }[]
): { start: number; end: number }[] => {
  const blockInterval = Interval.fromDateTimes(
    DateTime.fromMillis(currentStartTime),
    DateTime.fromMillis(currentEndTime)
  )
  const releaseIntervals = releases.map((release) =>
    Interval.fromDateTimes(
      DateTime.fromISO(release.startTime),
      DateTime.fromISO(release.endTime)
    )
  )
  const gaps = blockInterval.difference(...releaseIntervals).map((gap) => ({
    start: gap.start.toMillis(),
    end: gap.end.toMillis(),
  }))

  return gaps
}

export const validateTimeRanges = function validate<
  T extends {
    blockTimes: {
      roomId: string
      startTime: Scalars['DateTime']['input']
      endTime: Scalars['DateTime']['input']
      releases?: BlockTimeRelease[]
    }[]
  },
>({ blockTimes }: T) {
  const errors: { blockTimes?: string | string[] } = {}

  const room_ranges: Record<
    string,
    { idx: number; start: number; end: number }[]
  > = {}

  for (let i = 0; i < blockTimes.length; i++) {
    if (!Object.keys(room_ranges).includes(blockTimes[i].roomId)) {
      room_ranges[blockTimes[i].roomId] = []
    }
    const currentStartTime = +DateTime.fromISO(blockTimes[i].startTime)
    const currentEndTime = +DateTime.fromISO(blockTimes[i].endTime)
    const releases = blockTimes[i].releases ?? []
    const gaps = getActiveBlockTimeGaps(
      currentStartTime,
      currentEndTime,
      releases
    )
    const fullyReleased = gaps.length === 0

    if (currentStartTime === currentEndTime) {
      errors.blockTimes = []
      errors.blockTimes[i] = ZERO_TIME_RANGE_ERROR
      return errors
    }
    if (fullyReleased) {
      continue
    }
    if (releases.length === 0) {
      const currentInterval = Interval.fromDateTimes(
        DateTime.fromMillis(currentStartTime),
        DateTime.fromMillis(currentEndTime)
      )
      for (const existing_range of room_ranges[blockTimes[i].roomId]) {
        const existingInterval = Interval.fromDateTimes(
          DateTime.fromMillis(existing_range.start),
          DateTime.fromMillis(existing_range.end)
        )
        if (currentInterval.overlaps(existingInterval)) {
          errors.blockTimes = []
          errors.blockTimes[i] = OVER_LAPPING_TIME_RANGE_ERROR
          errors.blockTimes[existing_range.idx] = OVER_LAPPING_TIME_RANGE_ERROR
          return errors
        }
      }

      room_ranges[blockTimes[i].roomId].push({
        idx: i,
        start: +currentStartTime,
        end: +DateTime.fromISO(blockTimes[i].endTime),
      })
    } else {
      // partially released, adds the active block time gaps for the room
      for (const gap of gaps) {
        room_ranges[blockTimes[i].roomId].push({
          idx: i,
          start: gap.start,
          end: gap.end,
        })
      }
    }
  }

  return errors
}

export const getReleaseByAtDateString = (
  release: BlockTimeRelease,
  timezone: string
) => {
  let source = ''

  if (release.source === 'csv upload') {
    // for historical block releases
    source = `by batch release `
  } else if (release.source !== 'csv') {
    // ignores block releases with no reason and no user
    source = `by ${release.source} `
  }

  return `${source}on
        ${DateTime.fromISO(release.releasedTime)
          .setZone(timezone)
          .toFormat('L/d/yy')}`
}
