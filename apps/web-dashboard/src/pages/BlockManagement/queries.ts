import { gql } from '@apollo/client'

import { PAGE_CURSORS_FRAGMENT } from 'src/utils/graphqlFragments'

export const GET_CURRENT_USER_NAME = gql`
  query GetCurrentUserName {
    me {
      id
      name
    }
  }
`

export const BLOCK_TIME_RELEASE_FRAGMENT = gql`
  fragment BlockTimeReleaseFragment on BlockTimeRelease {
    id
    blockTimeId
    reason
    releasedTime
    source
    startTime
    endTime
  }
`

export const BLOCK_TIME_FRAGMENT = gql`
  fragment BlockTimeFragment on BlockTime {
    id
    blockId
    startTime
    endTime
    roomId
    room {
      id
      name
      site {
        id
        name
      }
    }
  }
`

export const BLOCK_FRAGMENT = gql`
  fragment BlockFragment on Block {
    id
    name
    color
    siteIds
    surgeonIds
    archivedTime
  }
`

export const SYNC_BLOCK_FRAGMENT = gql`
  ${BLOCK_FRAGMENT}
  ${BLOCK_TIME_FRAGMENT}
  fragment SyncBlockFragment on Block {
    ...BlockFragment
    blockTimes {
      ...BlockTimeFragment
      releases(includeUnreleased: false) {
        id
      }
    }
  }
`

export const CREATE_BLOCK = gql`
  ${SYNC_BLOCK_FRAGMENT}
  mutation BlockCreate($input: BlockCreateInput!) {
    blockCreate(input: $input) {
      block {
        ...SyncBlockFragment
      }
    }
  }
`

export const LIST_BLOCKS = gql`
  ${BLOCK_FRAGMENT}
  ${BLOCK_TIME_FRAGMENT}
  ${PAGE_CURSORS_FRAGMENT}
  query ListBlocks(
    $query: BlockQueryInput!
    $after: String
    $first: Int
    $orderBy: [OrderBy!]
  ) {
    blocks(query: $query, after: $after, first: $first, orderBy: $orderBy) {
      pageCursors {
        ...PageCursorsFragment
      }
      edges {
        node {
          ...BlockFragment
          blockTimes {
            ...BlockTimeFragment
          }
        }
      }
    }
  }
`

export const LIST_BLOCK_NAMES = gql`
  query ListBlockNames($query: BlockQueryInput!) {
    blocks(query: $query) {
      edges {
        node {
          id
          name
        }
      }
    }
  }
`

export const GET_BLOCK = gql`
  ${BLOCK_FRAGMENT}
  ${BLOCK_TIME_RELEASE_FRAGMENT}
  ${BLOCK_TIME_FRAGMENT}
  query GetBlock($id: ID!) {
    block(id: $id) {
      ...BlockFragment
      blockTimes {
        ...BlockTimeFragment
        releases(includeUnreleased: false) {
          ...BlockTimeReleaseFragment
        }
      }
    }
  }
`

export const UPDATE_BLOCK = gql`
  ${SYNC_BLOCK_FRAGMENT}
  mutation BlockUpdate($input: BlockUpdateInput!) {
    blockUpdate(input: $input) {
      block {
        ...SyncBlockFragment
      }
    }
  }
`

export const ARCHIVE_BLOCK = gql`
  ${SYNC_BLOCK_FRAGMENT}
  mutation ArchiveBlock($blockId: ID!) {
    blockArchive(id: $blockId) {
      block {
        ...SyncBlockFragment
      }
    }
  }
`

export const UNARCHIVE_BLOCK = gql`
  ${SYNC_BLOCK_FRAGMENT}
  mutation UnarchiveBlock($blockId: ID!) {
    blockUnarchive(id: $blockId) {
      block {
        ...SyncBlockFragment
      }
    }
  }
`

export const GET_PRIMARY_SURGEONS = gql`
  query GetPrimarySurgeons($orgId: String) {
    staff(
      query: { orgId: $orgId, onlyPrimarySurgeons: true }
      orderBy: [
        { sort: "lastName", direction: ASC }
        { sort: "firstName", direction: ASC }
      ]
    ) {
      edges {
        node {
          id
          lastName
          firstName
        }
      }
    }
  }
`

export const BLOCK_TIME_BULK_CREATE = gql`
  mutation BlockTimeBulkCreate($input: BlockTimeBulkCreateInput!) {
    blockTimeBulkCreate(input: $input) {
      ids
      success
    }
  }
`
