import { ReactElement } from 'react'

import { Col, Label, MultiSelect, Option } from '@apella/component-library'

import { useSurgeonOptions } from '../useSurgeonOptions'

export function SurgeonSelector({
  label,
  name,
  selectedIds,
  onChange,
  required,
  placeholder = 'All surgeons',
}: {
  label?: string
  placeholder?: string
  name: string
  selectedIds: string[]
  onChange?: (value?: string[]) => void
  error?: ReactElement
  required?: boolean
}) {
  const surgeons = useSurgeonOptions()

  return (
    <Col>
      {label && (
        <Label htmlFor={name} required={required}>
          {label}
        </Label>
      )}
      <MultiSelect
        label={placeholder}
        name={name}
        value={selectedIds}
        search={true}
        bulkSelect={true}
        onChange={onChange}
      >
        {surgeons.map((surgeon) => (
          <Option key={surgeon.id} value={surgeon.id} label={surgeon.name} />
        ))}
      </MultiSelect>
    </Col>
  )
}
