import { useEffect, useState } from 'react'
import { Location, useLocation, useNavigate } from 'react-router'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import {
  Button,
  FlexContainer,
  FlexItem,
  H4,
  remSpacing,
  Span2,
  Tile,
  Table,
  THead,
  TR,
  TH,
  TBody,
  TD,
  CheckCircle,
  Error,
  ButtonGroup,
  Tooltip,
  flexboxgrid,
  Label,
} from '@apella/component-library'
import { useApellaAuth0 } from '@apella/hooks'
import { Scalars } from 'src/__generated__/globalTypes'
import { Section } from 'src/components/FormLayout/FormLayout'
import { BoldedSpan } from 'src/components/InsightsTile/BoldedSpan'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { useTimezone } from 'src/Contexts'
import { useCurrentUser } from 'src/modules/user/hooks'
import { LocationPath } from 'src/router/types'
import { settings } from 'src/settings'

interface BlockReleaseBulkCreateOutput {
  block_date?: Scalars['DateTime']['input']
  block_name?: string
  block_time_id?: string
  end_time?: Scalars['DateTime']['input']
  id: string
  rejected_reason?: string

  release_length?: number
  release_reason: string
  released_by: string
  released_time?: Scalars['DateTime']['input']

  room_id?: string
  room_name?: string
  row_number?: number
  start_time?: Scalars['DateTime']['input']

  timezone?: string
  to_block?: string
}

interface BlockReleaseBulkCreateResponse {
  org_id: string
  releases: BlockReleaseBulkCreateOutput[]
  success: boolean
  timezone: string
}

export const BlockManagementPageUploadReleasePage = () => {
  const { getAccessTokenSilentlyOrLogout } = useApellaAuth0()
  const [loading, setLoading] = useState(true)
  const { timezone: localTimezone } = useTimezone()
  const [displayTimezone, setDisplayTimezone] = useState<string>(localTimezone)
  const location: Location<{ csv: string; orgId: string }> = useLocation()
  const navigate = useNavigate()
  const { currentOrganization } = useCurrentUser()

  useEffect(() => {
    if (!location.state?.csv) {
      toast.warn('Invalid CSV file. Please re-upload.')
      navigate(LocationPath.Blocks)
    }
  }, [location.state?.csv, navigate])

  const [bulkCreateResponse, setBulkCreateResponse] = useState<
    BlockReleaseBulkCreateResponse | undefined
  >()

  useEffect(() => {
    // This gymnastics is to prevent re-id-generation on every render, they need to stay the same to match returned status
    if (bulkCreateResponse === undefined && location.state?.csv) {
      getAccessTokenSilentlyOrLogout()
        .then((token) =>
          // Pre-releases endpoint actually add the releases
          fetch(`${settings.api.domain}/v1/blocks/pre-releases`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: token ? `Bearer ${token}` : '',
            },
            body: JSON.stringify({
              fileContent: location.state.csv,
            }),
          })
        )
        .then((response) => response.json())
        .then((data: BlockReleaseBulkCreateResponse) => {
          setDisplayTimezone(data.timezone) // Use org timezone
          const releases = data.releases.map((release) => {
            const rTimezone = release.timezone ?? data.timezone
            return {
              ...release,
              block_date: release.block_date
                ? DateTime.fromRFC2822(release.block_date, {
                    zone: rTimezone,
                    setZone: true,
                  }).toISO()
                : undefined,
              start_time: release.start_time
                ? DateTime.fromRFC2822(release.start_time, {
                    zone: rTimezone,
                    setZone: true,
                  }).toISO()
                : undefined,
              end_time: release.end_time
                ? DateTime.fromRFC2822(release.end_time, {
                    zone: rTimezone,
                    setZone: true,
                  }).toISO()
                : undefined,
              released_time: release.released_time
                ? DateTime.fromRFC2822(release.released_time, {
                    zone: rTimezone,
                    setZone: true,
                  }).toISO()
                : undefined,
            }
          })

          // Set output display
          setBulkCreateResponse({
            success: data.success,
            timezone: data.timezone,
            org_id: data.org_id,
            releases,
          })
        })
        .catch(() => {
          toast.error('Error parsing CSV file. Please contact support.')
        })
        .finally(() => setLoading(false))
    }
  }, [
    getAccessTokenSilentlyOrLogout,
    location.state?.csv,
    location.state?.orgId,
    localTimezone,
    bulkCreateResponse,
    setBulkCreateResponse,
    currentOrganization?.node.id,
  ])

  if (loading) {
    return <LoadingOverlay />
  }

  return (
    <FlexContainer justifyContent="center">
      <FlexContainer
        direction="column"
        css={{ width: flexboxgrid.breakpoints.xl }}
      >
        <FlexItem css={{ alignSelf: 'flex-start' }}>
          <H4 as="h1" style={{ margin: `${remSpacing.large} 0` }}>
            Upload Block Releases
          </H4>
        </FlexItem>

        <FlexItem css={{ alignSelf: 'flex-end' }}>
          <Label htmlFor="timezone-button-group">Timezone</Label>
          <ButtonGroup id={'timezone-button-group'}>
            <FlexContainer direction="row">
              {bulkCreateResponse?.timezone &&
                bulkCreateResponse.timezone !== localTimezone && (
                  <Tooltip
                    body="Timezone at the organization in the CSV file"
                    placement="bottom"
                  >
                    <Button
                      type="button"
                      color={
                        displayTimezone === bulkCreateResponse.timezone
                          ? 'alternate'
                          : 'gray'
                      }
                      appearance={
                        displayTimezone === bulkCreateResponse.timezone
                          ? 'button'
                          : 'secondary'
                      }
                      onClick={() =>
                        setDisplayTimezone(bulkCreateResponse.timezone)
                      }
                    >
                      {bulkCreateResponse.timezone}
                    </Button>
                  </Tooltip>
                )}

              <Tooltip
                body="Timezone at current organization"
                placement="bottom"
              >
                <Button
                  type="button"
                  color={
                    displayTimezone === localTimezone ? 'alternate' : 'gray'
                  }
                  appearance={
                    displayTimezone === localTimezone ? 'button' : 'secondary'
                  }
                  onClick={() => setDisplayTimezone(localTimezone)}
                >
                  {localTimezone}
                </Button>
              </Tooltip>
            </FlexContainer>
          </ButtonGroup>
        </FlexItem>

        <BlockReleaseUploadTable
          displayTimezone={displayTimezone}
          releases={bulkCreateResponse?.releases ?? []}
        />

        <FlexContainer
          justifyContent="space-between"
          gap={remSpacing.small}
          css={{
            margin: `${remSpacing.medium} 0 ${remSpacing.large}`,
          }}
        >
          <Button
            type="button"
            appearance="link"
            onClick={() => navigate(LocationPath.Blocks)}
          >
            Back
          </Button>
        </FlexContainer>
      </FlexContainer>
    </FlexContainer>
  )
}

const BlockReleaseUploadTable = ({
  displayTimezone,
  releases,
}: {
  displayTimezone: string
  releases: BlockReleaseBulkCreateOutput[]
}) => {
  const theme = useTheme()

  const showStartEndTimeColumns = releases.some(
    (release) => release.start_time && release.end_time
  )
  const showToBlockColumn = releases.some((release) => release.to_block)

  return (
    <Section>
      <Tile>
        {releases.length === 0 ? (
          <Span2>
            No records found or invalid format/organization. Please contact
            support.
          </Span2>
        ) : (
          <Table css={{ overflowX: 'auto' }}>
            <THead
              css={{
                position: 'sticky',
                top: 0,
                background: theme.palette.background.primary,
              }}
            >
              <TR>
                <TH>
                  <FlexContainer gap={remSpacing.xsmall}>
                    <BoldedSpan>Row # </BoldedSpan>
                  </FlexContainer>
                </TH>
                <TH>
                  <BoldedSpan>Block Name</BoldedSpan>
                </TH>
                <TH>
                  <BoldedSpan>Room Name</BoldedSpan>
                </TH>
                <TH>
                  <BoldedSpan>Release Date</BoldedSpan>
                </TH>
                <TH>
                  <BoldedSpan>Release Length</BoldedSpan>
                </TH>

                {showStartEndTimeColumns && (
                  <>
                    <TH>
                      <BoldedSpan>Release Start Time</BoldedSpan>
                    </TH>
                    <TH>
                      <BoldedSpan>Release End Time</BoldedSpan>
                    </TH>
                  </>
                )}

                <TH>
                  <BoldedSpan>Release Reason</BoldedSpan>
                </TH>
                <TH>
                  <BoldedSpan>Released Time</BoldedSpan>
                </TH>
                <TH>
                  <BoldedSpan>Released by</BoldedSpan>
                </TH>

                {showToBlockColumn && (
                  <TH>
                    <BoldedSpan>To Block</BoldedSpan>
                  </TH>
                )}

                <TH>
                  <BoldedSpan>Status</BoldedSpan>
                </TH>
              </TR>
            </THead>

            <TBody>
              <BlockReleaseUploadTableRow
                releases={releases}
                timezone={displayTimezone}
                showStartEndTimeColumns={showStartEndTimeColumns}
                showToBlockColumn={showToBlockColumn}
              />
            </TBody>
          </Table>
        )}
      </Tile>
    </Section>
  )
}

export const BlockReleaseUploadTableRow = ({
  releases,
  timezone,
  showStartEndTimeColumns = false,
  showToBlockColumn = false,
}: {
  releases: BlockReleaseBulkCreateOutput[]
  timezone: string
  showStartEndTimeColumns?: boolean
  showToBlockColumn?: boolean
}) => {
  const theme = useTheme()

  return (
    <>
      {releases.map((release, index) => {
        const rowNumber = index + 1
        return (
          <TR
            key={release.id}
            css={{
              backgroundColor:
                index % 2 === 0
                  ? theme.palette.background.primary
                  : theme.palette.background.secondary,
            }}
          >
            <TD>
              <Span2>{release.row_number ?? rowNumber}</Span2>
            </TD>
            <TD>
              <Span2>{release.block_name}</Span2>
            </TD>

            <TD>
              <Span2>{release.room_name}</Span2>
            </TD>

            <TD>
              {release.block_date && (
                <Span2>
                  {DateTime.fromISO(release.block_date)
                    .setZone(timezone)
                    .toFormat('yyyy-MM-dd')}
                </Span2>
              )}
            </TD>

            <TD>
              <Span2>{`${release.release_length}m`}</Span2>
            </TD>

            {showStartEndTimeColumns && (
              <>
                <TD>
                  {release.start_time && (
                    <Span2>
                      {DateTime.fromISO(release.start_time)
                        .setZone(timezone)
                        .toFormat('yyyy-MM-dd HH:mm')}
                    </Span2>
                  )}
                </TD>

                <TD>
                  {release.end_time && (
                    <Span2>
                      {DateTime.fromISO(release.end_time)
                        .setZone(timezone)
                        .toFormat('yyyy-MM-dd HH:mm')}
                    </Span2>
                  )}
                </TD>
              </>
            )}

            <TD>
              <Span2>{release.release_reason}</Span2>
            </TD>

            <TD>
              {release.released_time && (
                <Span2>
                  {DateTime.fromISO(release.released_time)
                    .setZone(timezone)
                    .toFormat('yyyy-MM-dd HH:mm')}
                </Span2>
              )}
            </TD>

            <TD>
              <Span2>{release.released_by}</Span2>
            </TD>

            {showToBlockColumn && (
              <TD>
                <Span2>{release.to_block}</Span2>
              </TD>
            )}

            <TD>
              <FlexContainer gap={remSpacing.xsmall}>
                {!release.rejected_reason ? (
                  <>
                    <CheckCircle size="sm" color={theme.palette.green[50]} />
                    Saved
                  </>
                ) : (
                  <>
                    <Error size="sm" color={theme.palette.red[50]} />
                    {release.rejected_reason}
                  </>
                )}
              </FlexContainer>
            </TD>
          </TR>
        )
      })}
    </>
  )
}
