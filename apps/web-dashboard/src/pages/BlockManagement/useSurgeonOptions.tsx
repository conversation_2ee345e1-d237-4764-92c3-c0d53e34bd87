import { useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { capitalize } from 'lodash'

import { useCurrentUser } from 'src/modules/user/hooks'
import { getFullName } from 'src/utils/getFullName'

import {
  GetPrimarySurgeons,
  GetPrimarySurgeonsVariables,
} from './__generated__'
import { GET_PRIMARY_SURGEONS } from './queries'

export const useSurgeonOptions = () => {
  const { currentOrganization } = useCurrentUser()
  const { data } = useQuery<GetPrimarySurgeons, GetPrimarySurgeonsVariables>(
    GET_PRIMARY_SURGEONS,
    {
      variables: {
        orgId: currentOrganization?.node.id,
      },
      skip: !currentOrganization?.node.id,
    }
  )

  const surgeons = useMemo(() => {
    return (data?.staff?.edges ?? []).map((s) => ({
      ...s.node,
      name: getFullName(
        capitalize(s.node.lastName),
        capitalize(s.node.firstName)
      ),
    }))
  }, [data?.staff])
  return surgeons
}
