import { DateTime } from 'luxon'

import { FilterBy, OrderBy, OrderByValue } from './types'

export const DEFAULT_BLOCK_COLOR = '#808080'

export const START_MONTH_OFFSET = 1
export const END_MONTH_OFFSET = 3

export const DEFAULT_ORDER_BY: OrderBy[] = [
  {
    field: 'startTime',
    value: OrderByValue.ASC,
  },
]

export const DEFAULT_FILTER_BY: FilterBy = {
  dateRange: [
    DateTime.now().minus({ months: START_MONTH_OFFSET }).toJSDate(),
    DateTime.now().plus({ months: END_MONTH_OFFSET }).toJSDate(),
  ],
  daysOfWeek: [],
  siteIds: undefined,
  roomIds: [],
}

export const ZERO_TIME_RANGE_ERROR =
  'Start time and end time cannot be the same'
export const OVER_LAPPING_TIME_RANGE_ERROR = 'Over-lapping time range'

export const DEFAULT_BLOCK_FILTER_LABEL = 'All blocks'
