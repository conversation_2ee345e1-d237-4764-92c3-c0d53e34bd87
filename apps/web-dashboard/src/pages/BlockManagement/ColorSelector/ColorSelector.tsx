import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { capitalize, omit } from 'lodash'

import { ComponentTheme, Input, Option } from '@apella/component-library'

export const ColorBlock = styled.span`
  display: inline-block;
  background: ${(prop) => prop.color};
  border-radius: 2px;
  width: 10px;
  height: 10px;
  margin-right: 8px;
`

export const ColorSelector = ({
  label,
  name,
  initialValue,
  placeholder = 'Pick a color',
}: {
  label?: string
  name: string
  initialValue: string
  placeholder?: string
}) => {
  const theme: ComponentTheme = useTheme()
  const colors = useMemo(
    () => omit(theme.palette, ['background', 'gray', 'text']),
    [theme.palette]
  )

  const options = useMemo(() => {
    const options = []
    for (const [color, colorPalette] of Object.entries(colors)) {
      const hexValue = colorPalette[30].toLowerCase()
      options.push(
        <Option
          key={hexValue}
          value={hexValue}
          label={color}
          selected={hexValue === initialValue}
        >
          <ColorBlock color={hexValue} /> {capitalize(color)}
        </Option>
      )
    }
    return options
  }, [colors, initialValue])

  return (
    <Input.SingleSelect
      label={label}
      placeholder={placeholder}
      name={name}
      required
    >
      {options}
    </Input.SingleSelect>
  )
}
