import { useId, useState } from 'react'

import { useField } from 'formik'
import { DateTime } from 'luxon'

import {
  AdditionalText,
  BaseInputProps,
  FlexContainer,
  Label,
  useInputCSS,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'

interface DateTimePickerProps extends BaseInputProps {
  onChange?: (newReleasedTime: DateTime) => void
  value: string // releasedTime gets default value from formik
}

const formatDateTimeStr = (dateTimeStr: string) => {
  const dateTime = DateTime.fromISO(dateTimeStr)
  return dateTime.toFormat("yyyy-MM-dd'T'HH:mm")
}

export const DateTimePicker = ({
  id: idProp,
  name,
  value,
  label,
  success,
  size = 'md',
  error = '',
  width,
  required = false,
  disabled,
  onChange,
}: DateTimePickerProps) => {
  const [, meta] = useField(name)
  const errorMessage = meta.error && meta.touched ? meta.error : ''

  const { timezone } = useTimezone()
  const [internalVal, setInternalVal] = useState(formatDateTimeStr(value))
  const uniqueId = useId()
  const id = idProp || `${name}-${uniqueId}`

  const inputCss = useInputCSS(size, success, error, width)

  const handleOnChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInternalVal(event.target.value)
    const newReleasedTime = DateTime.fromISO(event.target.value).setZone(
      timezone
    )
    onChange?.(newReleasedTime)
  }

  return (
    <FlexContainer direction="column">
      <Label htmlFor={name} required={required}>
        {label}
      </Label>
      <input
        disabled={disabled}
        type="datetime-local"
        id={id}
        name={name}
        value={internalVal}
        css={inputCss}
        onChange={handleOnChange}
      />
      <AdditionalText error={errorMessage} />
    </FlexContainer>
  )
}
