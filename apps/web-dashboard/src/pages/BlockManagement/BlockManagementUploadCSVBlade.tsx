import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react'
import { FileWithPath, useDropzone } from 'react-dropzone'
import { useNavigate } from 'react-router'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import Papa from 'papaparse'

import {
  AdditionalText,
  Blade,
  Button,
  CloudUpload,
  ComponentTheme,
  Delete,
  FlexContainer,
  P3,
  remSpacing,
  Span3,
} from '@apella/component-library'
import { LocationPath } from 'src/router/types'
import { useRoomOptions } from 'src/utils/useSiteRoomsOptions'

import { CSVContent, CSVContentType } from './types'
import { useBlockNames } from './useBlockNames'

const MAX_NUM_OF_FILES = 1

interface BlockManagementUploadCSVBladeProps {
  contentType?: CSVContentType
  isBladeOpen: boolean
  onBladeClose: () => void
}

export const BlockManagementUploadCSVBlade = ({
  isBladeOpen,
  onBladeClose,
  contentType = CSVContentType.BlockTime,
}: BlockManagementUploadCSVBladeProps) => {
  return (
    <Blade size="xs" side="right" onClose={onBladeClose} isOpen={isBladeOpen}>
      <BlockManagementUploadCSVBladeContent
        onBladeClose={onBladeClose}
        contentType={contentType}
      />
    </Blade>
  )
}

const BlockManagementUploadCSVBladeContent = ({
  onBladeClose: onClose,
  contentType,
}: Omit<BlockManagementUploadCSVBladeProps, 'isBladeOpen'>) => {
  const theme: ComponentTheme = useTheme()
  const navigate = useNavigate()
  const [errorMessage, setErrorMessage] = useState<string>()
  const [csvContent, setCsvContent] = useState<CSVContent>()

  const rooms = useRoomOptions()
  const validRoomIds = useMemo(() => rooms.map((room) => room.node.id), [rooms])
  const { blockNodes } = useBlockNames()

  const description =
    contentType === CSVContentType.BlockTime
      ? 'To upload blocks, please upload a CSV file'
      : 'To upload block releases, please upload a CSV file.'

  const blockTimeValidator = useCallback(
    (csvContent: CSVContent) => {
      if (csvContent.errors.length) {
        setErrorMessage('Invalid CSV file')
        return false
      }
      // check that room ids belong to valid org
      const roomIds = csvContent.data.map((row) => row[2])
      const invalidRoomIds = roomIds
        .slice(1)
        .filter((roomId) => !validRoomIds.includes(roomId))

      const blockNames = csvContent.data.map((row) => row[0])
      const invalidBlockNames = blockNames
        .slice(1)
        .filter(
          (blockName) =>
            !blockNodes.map((bn) => bn.node.name).includes(blockName)
        )

      if (invalidRoomIds.length > 0 || invalidBlockNames.length > 0) {
        let errorMessage =
          'Some records contain invalid block names or room IDs.'

        if (invalidRoomIds.length > 0) {
          errorMessage += ` Invalid Room IDs: ${invalidRoomIds.join(', ')}.`
        }

        if (invalidBlockNames.length > 0) {
          errorMessage += ` Invalid Block Names: ${invalidBlockNames.join(', ')}.`
        }

        setErrorMessage(errorMessage)
        return false
      }

      return true
    },
    [validRoomIds, blockNodes]
  )

  const blockReleaseValidator = useCallback((csvContent: CSVContent) => {
    if (csvContent.errors.length) {
      setErrorMessage('Invalid CSV file')
      return false
    }

    return true
  }, [])

  const onClickHandler = () => {
    if (!csvContent) {
      return
    }

    if (contentType === CSVContentType.BlockTime) {
      navigate(LocationPath.BlockUpload, {
        state: { csv: csvContent },
      })
    } else {
      navigate(LocationPath.BlockUploadReleases, {
        state: {
          csv: Papa.unparse(csvContent.data),
        },
      })
    }
  }

  return (
    <FlexContainer
      direction="column"
      justifyContent="space-between"
      css={{ height: '100%' }}
    >
      <Blade.Header>
        <Blade.Title
          title={`Upload ${contentType === CSVContentType.BlockTime ? 'Blocks' : 'Releases'}`}
        />
        <Blade.CloseButton onClose={onClose} />
      </Blade.Header>

      <FlexContainer
        css={{
          padding: remSpacing.medium,
        }}
        justifyContent="flex-start"
        direction={'column'}
        gap="8px"
      >
        <Dropzone
          description={description}
          errorMessage={errorMessage}
          setErrorMessage={setErrorMessage}
          setCsvContent={setCsvContent}
          validator={
            contentType === CSVContentType.BlockTime
              ? blockTimeValidator
              : blockReleaseValidator
          }
        />
      </FlexContainer>

      <FlexContainer
        justifyContent="flex-end"
        gap={remSpacing.small}
        css={{
          marginTop: 'auto',
          borderTop: `1px solid ${theme.palette.gray[20]}`,
          padding: remSpacing.medium,
        }}
      >
        <Button
          disabled={!!errorMessage || !csvContent}
          onClick={onClickHandler}
        >
          Next
        </Button>
      </FlexContainer>
    </FlexContainer>
  )
}

const onDropHandler =
  (
    setMyFiles: Dispatch<SetStateAction<FileWithPath[]>>,
    setCsvContent: Dispatch<SetStateAction<CSVContent | undefined>>,
    validator?: (csvContent: CSVContent) => void
  ) =>
  (acceptedFiles: FileWithPath[]) => {
    if (acceptedFiles.length !== MAX_NUM_OF_FILES) {
      toast.error(`Please upload exactly ${MAX_NUM_OF_FILES} file`)
      return
    }

    const [acceptedFile] = acceptedFiles
    setMyFiles([acceptedFile])

    const reader = new FileReader()

    reader.onabort = () => toast.warn('Upload was aborted')
    reader.onerror = () => toast.error('Upload failed')
    reader.onload = () => {
      if (reader.result && typeof reader.result === 'string') {
        const csvContent = Papa.parse<string[]>(reader.result)
        if (validator?.(csvContent)) {
          setCsvContent(csvContent)
        }
      }
    }
    reader.readAsText(acceptedFile)
  }

const UploadedCsvItem = ({
  file,
  setMyFiles,
  setCsvContent,
  setErrorMessage,
}: {
  file: FileWithPath
  setMyFiles: Dispatch<SetStateAction<FileWithPath[]>>
  setCsvContent: Dispatch<SetStateAction<CSVContent | undefined>>
  setErrorMessage: Dispatch<SetStateAction<string | undefined>>
}) => {
  const theme = useTheme()

  if (!file) {
    return null
  }

  return (
    <FlexContainer
      key={file.path}
      style={{
        alignItems: 'center',
        color: theme.palette.text.secondary,
        justifyContent: 'space-between',
        width: '100%',
      }}
    >
      <P3>{file.path}</P3>
      <Button
        type="button"
        size="sm"
        appearance="link"
        color="alternate"
        onClick={() => {
          setMyFiles([])
          setCsvContent(undefined)
          setErrorMessage(undefined)
        }}
      >
        <Delete size="xs" color={theme.palette.text.tertiary} />
      </Button>
    </FlexContainer>
  )
}

const Dropzone = ({
  description,
  errorMessage,
  setErrorMessage,
  setCsvContent,
  validator,
}: {
  description: string
  errorMessage?: string
  setErrorMessage: Dispatch<SetStateAction<string | undefined>>
  setCsvContent: Dispatch<SetStateAction<CSVContent | undefined>>
  validator?: (csvContent: CSVContent) => boolean
}) => {
  const theme: ComponentTheme = useTheme()
  const [myFiles, setMyFiles] = useState<FileWithPath[]>([])

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop: onDropHandler(setMyFiles, setCsvContent, validator),
    noClick: true,
    noKeyboard: true,
    accept: { 'text/plain': ['.csv'] },
    maxFiles: MAX_NUM_OF_FILES,
  })

  const style = {
    gap: remSpacing.small,
    backgroundColor: theme.palette.gray[10],
    border: `1px dashed ${theme.palette.gray[30]}`,
    borderRadius: remSpacing.xsmall,
    padding: remSpacing.medium,
    alignItems: 'center',
  }

  return (
    <FlexContainer direction="column" gap={remSpacing.xlarge}>
      <Span3>{description}</Span3>
      {myFiles.length !== 1 ? (
        <FlexContainer
          direction="row"
          gap={remSpacing.small}
          {...getRootProps({ style })}
        >
          <input {...getInputProps()} />
          <Button type="button" onClick={open}>
            Select file
          </Button>
          <FlexContainer gap={remSpacing.xsmall}>
            <CloudUpload size="xs" color={theme.palette.text.tertiary} />
            <P3 color={theme.palette.text.secondary}>Drop files here...</P3>
          </FlexContainer>
        </FlexContainer>
      ) : (
        <FlexContainer direction="column" gap={remSpacing.xsmall} style={style}>
          <UploadedCsvItem
            file={myFiles[0]}
            setMyFiles={setMyFiles}
            setCsvContent={setCsvContent}
            setErrorMessage={setErrorMessage}
          />

          {errorMessage && (
            <FlexContainer
              direction="row"
              justifyContent="flex-start"
              style={{ width: '100%', paddingBottom: remSpacing.small }}
            >
              <AdditionalText error={errorMessage} />
            </FlexContainer>
          )}
        </FlexContainer>
      )}
    </FlexContainer>
  )
}
