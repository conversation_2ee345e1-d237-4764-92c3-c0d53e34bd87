import { generatePath, useNavigate } from 'react-router'
import { toast } from 'react-toastify'

import { ApolloError, useMutation } from '@apollo/client'

import { useTimezone } from 'src/Contexts'
import { useCurrentUser } from 'src/modules/user/hooks'
import { LocationPath } from 'src/router/types'

import { BlockCreate } from './__generated__'
import { BlockForm } from './BlockForm'
import { CREATE_BLOCK } from './queries'
import { BlockFormTitle } from './types'
import { parseBackendErrorMsg } from './utils'

export const BlockNewPage = () => {
  const navigate = useNavigate()
  const { timezone } = useTimezone()
  const { currentOrganization } = useCurrentUser()

  const onCreateBlock = (data: BlockCreate) => {
    if (data.blockCreate?.block?.id) {
      toast.success(`Block '${data.blockCreate.block.name}' created`)
      navigate(generatePath(LocationPath.Blocks))
    }
  }

  const onError = (error: ApolloError) => {
    const errMsg = parseBackendErrorMsg(error.message, timezone)
    toast.error(errMsg)
  }

  const [createBlock] = useMutation<BlockCreate>(CREATE_BLOCK, {
    onCompleted: onCreateBlock,
    onError,
  })

  return (
    <BlockForm
      title={BlockFormTitle.Create}
      initialValues={{
        id: crypto.randomUUID(),
        name: '',
        blockTimes: [],
        surgeonIds: [],
        color: '',
      }}
      onSubmit={async (value) => {
        await createBlock({
          variables: {
            input: {
              ...value,
              orgId: currentOrganization?.node.id,
            },
          },
        })
      }}
    />
  )
}
