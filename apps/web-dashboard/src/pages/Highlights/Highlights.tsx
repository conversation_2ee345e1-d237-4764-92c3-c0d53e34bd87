import { useCallback, useMemo } from 'react'
import { generatePath, Outlet, useLocation, useNavigate } from 'react-router'

import { useTheme } from '@emotion/react'

import { DateTime, Duration } from 'luxon'

import {
  CsvExportButton,
  Direction,
  formatDuration,
  PaginatedTableColumn,
  RemotePaginatedTable,
} from '@apella/component-library'
import { FeedbackStatus } from 'src/__generated__/globalTypes'
import { DateFilter } from 'src/components/Filters/DateFilter'
import {
  MultiFilterWithCount,
  SingleFilterWithCount,
} from 'src/components/Filters/FilterWithCount'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { ResetFilters } from 'src/components/ResetFilters'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { useTimezone } from 'src/Contexts'
import { StatusBadge } from 'src/modules/highlights/components/StatusBadge'
import {
  useHighlightCategories,
  useHighlightSearch,
} from 'src/modules/highlights/hooks'
import {
  GetHighlightSearchData,
  GetHighlightSearchDataVariables,
} from 'src/modules/highlights/queries/__generated__'
import { LocationPath } from 'src/router/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

type GetHighlightSearchData_highlightSearch_edges_node =
  GetHighlightSearchData['highlightSearch']['edges'][number]['node']

const START_TIME = 'startTime'
const DESCRIPTION = 'description'
const DURATION = 'duration'
const CATEGORY = 'category'
type ORDER_BY = 'orderBy'
const SITE = 'siteId'
const ROOM = 'roomId'

const SORTABLE_FIELDS = [
  START_TIME,
  DESCRIPTION,
  DURATION,
  CATEGORY,
  SITE,
  ROOM,
]

const DEFAULT_ORDER_BY_STATE: Pick<GetHighlightSearchDataVariables, ORDER_BY> =
  {
    orderBy: [
      {
        sort: START_TIME,
        direction: Direction.DESC,
      },
    ],
  }

const DEFAULT_STATE = {
  organizationIds: undefined,
  siteIds: undefined,
  roomIds: undefined,
  categories: undefined,
  assignedUserIds: undefined,
  feedbackStatus: undefined,
  after: undefined,
  first: 10,
  ...DEFAULT_ORDER_BY_STATE,
}

export const HighlightsPage = (): React.JSX.Element => {
  const { timezone: locale } = useTimezone()
  const navigate = useNavigate()
  const theme = useTheme()
  const location = useLocation()

  const DEFAULT_START_TIME = DateTime.now()
    .setZone(locale)
    .minus({ weeks: 2 })
    .startOf('day')

  const DEFAULT_END_TIME = DateTime.now().setZone(locale).endOf('day')

  const eventsLogger = useAnalyticsEventLogger()

  const {
    selectedDateTime,
    pageCursors,
    tableData,
    fetchExportRows,
    rowKeySelector,
    resetFilters,
    filterData,
    highlightCategories,
    highlightsLoading,
    onCategoryChange,
    onSiteIdsChange,
    onRoomIdsChange,
    onSortChange,
    onDateChange,
    state,
    filtersLoading,
    updateTableFilterSortState,
    onFeedbackStatusChange,
  } = useHighlightSearch({
    DEFAULT_START_TIME,
    DEFAULT_END_TIME,
    DEFAULT_STATE,
    SORTABLE_FIELDS,
    DEFAULT_ORDER_BY_STATE,
  })

  const categories = useHighlightCategories()

  const columns: PaginatedTableColumn<(typeof tableData)[number]>[] = useMemo(
    () => [
      {
        name: 'Start time',
        selector: START_TIME,
        formatter: (date?: string) =>
          date !== undefined
            ? DateTime.fromISO(date)
                .setZone(locale)
                .toLocaleString(DateTime.DATETIME_SHORT)
            : '',
        sortAttribute: START_TIME,
      },
      {
        name: 'Site',
        selector: (
          rowData: GetHighlightSearchData_highlightSearch_edges_node
        ) => rowData.site.name,
        sortAttribute: SITE,
      },
      {
        name: 'Room',
        selector: (
          rowData: GetHighlightSearchData_highlightSearch_edges_node
        ) => rowData.room.name,
        sortAttribute: ROOM,
      },
      {
        name: 'Category',
        selector: CATEGORY,
        sortAttribute: CATEGORY,
        formatter: (categoryId?: string) =>
          categories.find((c) => c.type === categoryId)?.name ?? '',
      },
      {
        name: 'Message',
        selector: DESCRIPTION,
        sortAttribute: DESCRIPTION,
      },
      {
        name: 'Duration',
        selector: (
          rowData: GetHighlightSearchData_highlightSearch_edges_node
        ) =>
          rowData.startTime !== undefined &&
          rowData.startTime !== null &&
          rowData.endTime !== undefined &&
          rowData.endTime !== null
            ? DateTime.fromISO(rowData.endTime).diff(
                DateTime.fromISO(rowData.startTime)
              )
            : undefined,
        formatter: (duration?: Duration) => formatDuration(duration),
        sortAttribute: DURATION,
      },
      {
        name: 'Feedback',
        selector: (
          rowData: GetHighlightSearchData_highlightSearch_edges_node
        ) =>
          rowData.myFeedback?.id
            ? FeedbackStatus.COMPLETE
            : FeedbackStatus.INCOMPLETE,
        formatter: (status: FeedbackStatus) => <StatusBadge status={status} />,
      },
    ],
    [categories, locale]
  )

  const exportColumns = useMemo(
    <T extends GetHighlightSearchData_highlightSearch_edges_node>() => [
      {
        name: 'Start time',
        exportFormatter: (row: T) =>
          row[START_TIME] !== undefined
            ? DateTime.fromISO(row[START_TIME])
                .setZone(locale)
                .toLocaleString(DateTime.DATETIME_SHORT)
            : '',
      },
      {
        name: 'Site',
        exportFormatter: (
          rowData: GetHighlightSearchData_highlightSearch_edges_node
        ) => rowData.site.name,
      },
      {
        name: 'Room',
        exportFormatter: (
          rowData: GetHighlightSearchData_highlightSearch_edges_node
        ) => rowData.room.name,
      },
      {
        name: 'Category',
        exportFormatter: (row: T) =>
          categories.find((c) => c.type === row[CATEGORY])?.name ?? '',
      },
      {
        name: 'Message',
        exportFormatter: (row: T) => row[DESCRIPTION],
      },
      {
        name: 'Duration (in minutes)',
        exportFormatter: (
          rowData: GetHighlightSearchData_highlightSearch_edges_node
        ) =>
          rowData.startTime !== undefined &&
          rowData.startTime !== null &&
          rowData.endTime !== undefined &&
          rowData.endTime !== null
            ? DateTime.fromISO(rowData.endTime)
                .diff(DateTime.fromISO(rowData.startTime))
                .as('minutes')
                .toString()
            : '',
      },
      {
        name: 'Feedback Status',
        exportFormatter: (row: T) =>
          row.myFeedback?.id
            ? FeedbackStatus.COMPLETE
            : FeedbackStatus.INCOMPLETE,
      },
      {
        name: 'Rating',
        exportFormatter: (row: T) => row.myFeedback?.rating?.toString() ?? '',
      },
      {
        name: 'Feedback Comment',
        exportFormatter: (row: T) => row.myFeedback?.comment ?? '',
      },
    ],
    [categories, locale]
  )

  const onFeedbackChange = useCallback(
    (feedback?: string | string[]) => {
      onFeedbackStatusChange(feedback as FeedbackStatus)
    },
    [onFeedbackStatusChange]
  )

  const feedbackStatues = useMemo(
    () => [
      {
        node: {
          id: FeedbackStatus.COMPLETE,
          name: 'Complete',
        },
      },
      {
        node: {
          id: FeedbackStatus.INCOMPLETE,
          name: 'Incomplete',
        },
      },
    ],
    []
  )

  return (
    <PageContentTemplate
      title="Highlights"
      actions={
        <CsvExportButton
          columns={exportColumns}
          fetchRows={fetchExportRows}
          filename="highlights"
          onClick={() => eventsLogger(EVENTS.EXPORT_HIGHLIGHT_TABLE, state)}
          size="sm"
          iconSize="xs"
          tooltipPlacement="bottom-end"
        />
      }
      filters={
        <>
          <DateFilter selected={selectedDateTime} onChangeDate={onDateChange} />
          <SitesRoomsFilter
            sites={filterData?.sites.edges ?? []}
            selectedSiteIds={state.siteIds || undefined}
            onChangeSites={onSiteIdsChange}
            rooms={
              filterData?.sites?.edges?.flatMap((site) =>
                site.node.rooms.edges.map((room) => ({
                  node: {
                    ...room.node,
                    site: site.node,
                  },
                }))
              ) ?? []
            }
            selectedRoomIds={state.roomIds || undefined}
            onChangeRooms={onRoomIdsChange}
            multipleSites
            bulkSelectSites
            bulkSelectRooms
          />
          <MultiFilterWithCount
            items={highlightCategories.map((category) => ({
              node: { id: category.type, name: category.name },
            }))}
            selectedIds={state.categories || undefined}
            bulkSelect
            onChange={onCategoryChange}
            label="Category"
          />
          <SingleFilterWithCount
            items={feedbackStatues}
            value={state.feedbackStatus || undefined}
            onChange={onFeedbackChange}
            label="Status"
            selectAllLabel="All Feedback"
          />
          <ResetFilters resetActions={resetFilters} disabled={filtersLoading} />
        </>
      }
    >
      <RemotePaginatedTable
        columns={columns}
        data={tableData}
        isLoading={highlightsLoading || filtersLoading}
        paginationType="cursor"
        rowKeySelector={rowKeySelector}
        onChangePage={(cursorItem) => {
          updateTableFilterSortState({ after: cursorItem.cursor })
        }}
        sortOrder={state.orderBy ?? undefined}
        onChangeSort={onSortChange}
        isMultiSort={false}
        conditionalRowStyles={[
          {
            when: (
              rowData: GetHighlightSearchData_highlightSearch_edges_node
            ) => !rowData.hasVideoAvailable,
            style: {
              color: theme.palette.text.tertiary,
            },
          },
        ]}
        onRowClicked={(data) => {
          const path = generatePath(LocationPath.HighlightView, {
            highlightId: data.id,
          })

          navigate(path, { state: { background: location } })
        }}
        {...pageCursors}
      />
      <Outlet />
    </PageContentTemplate>
  )
}
