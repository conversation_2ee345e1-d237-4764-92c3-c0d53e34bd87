import { useEffect } from 'react'
import { generatePath, useNavigate, useParams } from 'react-router'

import { gql, useMutation, useQuery } from '@apollo/client'
import { DateTime } from 'luxon'
import * as Yup from 'yup'

import {
  Blade,
  Button,
  Col,
  FlexContainer,
  FlexItem,
  Form,
  FormActionRow,
  formatDuration,
  H4,
  Input,
  ProgressOverlay,
  remSpacing,
  Table,
  TD,
  Tile,
  TR,
  ApellaDateTimeFormats,
} from '@apella/component-library'
import { VideoBladeContent } from 'src/pages/VideoBlade/VideoBlade'
import { LocationPath } from 'src/router/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { PAGE_CURSORS_FRAGMENT } from 'src/utils/graphqlFragments'
import { useBladeState } from 'src/utils/useBladeState'

import {
  CreateHighlightFeedback,
  CreateHighlightFeedbackVariables,
  GetHighlightPageData,
  GetHighlightPageDataVariables,
  UpdateHighlightFeedback,
  UpdateHighlightFeedbackVariables,
} from './__generated__'

type HighlightViewQueryParams = 'highlightId'

export const GET_HIGHLIGHTS_DATA = gql`
  ${PAGE_CURSORS_FRAGMENT}
  query GetHighlightsData(
    $after: String
    $first: Int
    $minTime: DateTime
    $maxTime: DateTime
    $orderBy: [OrderBy!]
  ) {
    highlights(
      after: $after
      first: $first
      query: { minTime: $minTime, maxTime: $maxTime }
      orderBy: $orderBy
    ) {
      edges {
        node {
          id
          description
          startTime
          endTime
          hasVideoAvailable
          category
          myFeedback {
            id
          }
        }
      }
      pageCursors {
        ...PageCursorsFragment
      }
    }
  }
`

const GET_HIGHLIGHT_PAGE_DATA = gql`
  query GetHighlightPageData($highlightId: String!) {
    highlight(id: $highlightId) {
      id
      description
      startTime
      endTime
      category
      room {
        id
        name
      }
      site {
        id
        timezone
      }
      myFeedback {
        id
        rating
        comment
      }
    }
  }
`

const CREATE_HIGHLIGHT_FEEDBACK = gql`
  mutation CreateHighlightFeedback(
    $highlightFeedback: HighlightFeedbackCreateInput!
  ) {
    highlightFeedbackCreate(input: $highlightFeedback) {
      success
      createdHighlightFeedback {
        id
        comment
        rating
      }
    }
  }
`

const UPDATE_HIGHLIGHT_FEEDBACK = gql`
  mutation UpdateHighlightFeedback(
    $highlightFeedback: HighlightFeedbackUpdateInput!
  ) {
    highlightFeedbackUpdate(input: $highlightFeedback) {
      success
      updatedHighlightFeedback {
        id
        comment
        rating
      }
    }
  }
`

const HIGHLIGHT_VIDEO_BUFFER_SECONDS = 60 * 5 // 5 minutes

const validationSchema = Yup.object({
  rating: Yup.number(),
  comment: Yup.string(),
})

export const HighlightViewPageContent = ({
  onClose,
}: {
  onClose: () => void
}): React.JSX.Element => {
  const navigate = useNavigate()
  const { highlightId } = useParams<HighlightViewQueryParams>()
  const eventsLogger = useAnalyticsEventLogger()

  const mutationVariables = {
    refetchQueries: [
      {
        query: GET_HIGHLIGHT_PAGE_DATA,
        variables: {
          highlightId: highlightId,
        },
      },
      {
        query: GET_HIGHLIGHTS_DATA,
      },
    ],
  }

  const [
    createHighlightFeedback,
    { loading: createLoading, error: createError, data: createdData },
  ] = useMutation<CreateHighlightFeedback, CreateHighlightFeedbackVariables>(
    CREATE_HIGHLIGHT_FEEDBACK,
    mutationVariables
  )
  const [
    updatedHighlightFeedback,
    { loading: updateLoading, error: updateError, data: updatedData },
  ] = useMutation<UpdateHighlightFeedback, UpdateHighlightFeedbackVariables>(
    UPDATE_HIGHLIGHT_FEEDBACK,
    mutationVariables
  )

  const mutationLoading = createLoading || updateLoading
  const mutationError =
    createError?.message ?? updateError?.message ?? undefined
  const mutationSuccess =
    createdData?.highlightFeedbackCreate?.success ??
    updatedData?.highlightFeedbackUpdate?.success ??
    undefined

  const { loading, data } = useQuery<
    GetHighlightPageData,
    GetHighlightPageDataVariables
  >(GET_HIGHLIGHT_PAGE_DATA, {
    variables: {
      highlightId: highlightId || '',
    },
  })

  const highlightData = data?.highlight
  const myFeedback = highlightData?.myFeedback ?? undefined

  const initialValues =
    myFeedback === undefined
      ? {
          comment: '',
          rating: undefined,
        }
      : {
          comment: myFeedback.comment,
          rating: myFeedback.rating,
        }
  const description = highlightData?.description ?? ''
  const category = highlightData?.category ?? ''

  const highlightStartTime =
    highlightData?.startTime !== undefined && highlightData.startTime !== null
      ? DateTime.fromISO(highlightData.startTime).minus({
          seconds: HIGHLIGHT_VIDEO_BUFFER_SECONDS,
        })
      : undefined
  const highlightEndTime =
    highlightData?.endTime !== undefined && highlightData.endTime !== null
      ? DateTime.fromISO(highlightData.endTime).plus({
          seconds: HIGHLIGHT_VIDEO_BUFFER_SECONDS,
        })
      : undefined

  const startTime =
    highlightData?.startTime !== undefined && highlightData.startTime !== null
      ? DateTime.fromISO(highlightData.startTime).setZone(
          highlightData.site.timezone
        )
      : undefined
  const endTime =
    highlightData?.endTime !== undefined && highlightData?.endTime !== null
      ? DateTime.fromISO(highlightData.endTime).setZone(
          highlightData.site.timezone
        )
      : undefined
  const duration =
    endTime !== undefined && startTime !== undefined
      ? formatDuration(endTime.diff(startTime))
      : ''

  useEffect(() => {
    if (mutationLoading === false && mutationSuccess === true) {
      const timeoutId = window.setTimeout(() => {
        const path = generatePath(LocationPath.Highlights)
        navigate(path)
      }, 1 * 1000)

      return () => window.clearTimeout(timeoutId)
    }
  }, [mutationLoading, mutationError, mutationSuccess, navigate])

  useEffect(() => {
    if (description) {
      eventsLogger(EVENTS.VIEW_HIGHLIGHT, {
        highlightId,
        highlightTitle: description,
        highlightCategory: category,
      })
    }
  }, [highlightId, description, eventsLogger, category])

  return loading === true ||
    highlightStartTime === undefined ||
    highlightEndTime === undefined ||
    highlightData === undefined ||
    highlightData === null ? (
    <ProgressOverlay />
  ) : (
    <FlexContainer
      direction="column"
      gap={remSpacing.medium}
      css={{
        height: 'inherit',
        overflowY: 'auto',
        width: '100%',
      }}
    >
      <FlexItem>
        <VideoBladeContent
          isHighlight
          roomId={highlightData.room.id}
          startTime={highlightStartTime.startOf('minute').toISO()}
          endTime={highlightEndTime.startOf('minute').toISO()}
          time={highlightData.startTime}
          onClose={onClose}
        ></VideoBladeContent>
      </FlexItem>
      <FlexItem>
        <FlexContainer
          css={{ padding: remSpacing.gutter }}
          direction="row"
          gap={remSpacing.medium}
        >
          <FlexItem grow>
            <Tile gutter css={{ height: '100%' }}>
              <FlexContainer direction="column">
                <H4>Details</H4>
                <Table>
                  <TR>
                    <TD>Title</TD>
                    <TD>{description}</TD>
                  </TR>
                  <TR>
                    <TD>Date</TD>
                    <TD>
                      {startTime?.toLocaleString(ApellaDateTimeFormats.DATE) ??
                        ''}
                    </TD>
                  </TR>
                  <TR>
                    <TD>Event start</TD>
                    <TD>
                      {startTime?.toLocaleString(ApellaDateTimeFormats.TIME) ??
                        ''}
                    </TD>
                  </TR>
                  <TR>
                    <TD>Duration</TD>
                    <TD>{duration}</TD>
                  </TR>
                  <TR>
                    <TD>OR</TD>
                    <TD>{highlightData.room.name ?? ''}</TD>
                  </TR>
                </Table>
              </FlexContainer>
            </Tile>
          </FlexItem>
          <FlexItem grow>
            <Tile gutter css={{ height: '100%' }}>
              <Form
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={({ comment, rating }) => {
                  const isCreating = myFeedback?.id === undefined

                  const savingFunction = isCreating
                    ? createHighlightFeedback
                    : updatedHighlightFeedback

                  const params = {
                    id: isCreating ? crypto.randomUUID() : myFeedback.id,
                    highlightId: highlightId || '',
                    comment,
                    rating: Number(rating),
                  }

                  eventsLogger(EVENTS.SUBMIT_HIGHLIGHT_FEEDBACK, {
                    ...params,
                    isCreating,
                  })

                  savingFunction({
                    variables: {
                      highlightFeedback: params,
                    },
                  })
                }}
              >
                <FlexContainer direction="column" gap={remSpacing.medium}>
                  <H4>Feedback</H4>
                  <Table>
                    <TR>
                      <TD>Rating</TD>
                      <TD>
                        <Input.Rating name="rating" />
                      </TD>
                    </TR>
                  </Table>
                  <Input.Text name="comment" placeholder="Notes" multiline />
                  <FormActionRow
                    error={mutationError}
                    saving={mutationLoading}
                    success={mutationSuccess}
                  >
                    <Col>
                      <Button
                        size="lg"
                        type="submit"
                        disabled={mutationLoading}
                      >
                        Save
                      </Button>
                    </Col>
                  </FormActionRow>
                </FlexContainer>
              </Form>
            </Tile>
          </FlexItem>
        </FlexContainer>
      </FlexItem>
    </FlexContainer>
  )
}

export const HighlightViewPage = () => {
  const { isBladeOpen, onBladeClose } = useBladeState({
    hardRefreshFallbackPath: LocationPath.Highlights,
  })

  return (
    <Blade
      isOpen={isBladeOpen}
      onClose={onBladeClose}
      size={'lg'}
      side={'right'}
      overlay={true}
    >
      <HighlightViewPageContent onClose={onBladeClose} />
    </Blade>
  )
}
