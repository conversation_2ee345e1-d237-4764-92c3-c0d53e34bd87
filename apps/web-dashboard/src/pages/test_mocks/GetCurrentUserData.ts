import { aUserUiPermissions } from 'src/__generated__/generated-mocks'
import { UserUiPermissions } from 'src/__generated__/globalTypes'
import { GetCurrentUser } from 'src/modules/user/__generated__'

export enum UserType {
  ALL_ACCESS = 'allAccess',
  TURNOVERS_ACCESS_ONLY = 'turnoversAccessOnly',
}

const permissions = [
  'annotation_task:read:any',
  'annotation_task_type:write:any',
  'annotation_task:write:any',
  'big_board:read:any',
  'big_board:write:any',
  'block:read:any',
  'block:write:any',
  'camera:read:any',
  'camera:write:any',
  'case_duration:read:any',
  'case_note_plan:read:any',
  'case_note_plan:write:any',
  'case:read:any',
  'case_staff_plan:read:any',
  'case_staff_plan:write:any',
  'case:write:any',
  'cluster:read:any',
  'cluster:write:any',
  'contact_information:read:any',
  'contact_information:write:any',
  'dashboard:insights:read:any',
  'dashboard:live_from_schedule:read:any',
  'dashboard:turnovers_dashboard:read:any',
  'dashboard:live:read:any',
  'dashboard:optimized:read:any',
  'dashboard:schedule:edit:any',
  'dashboard:schedule:read:any',
  'dashboard:terminal_cleans:read:any',
  'event:read:any',
  'event_type:write:any',
  'event:write:any',
  'feedback:read:any',
  'feedback:write_if_assigned',
  'highlight:read:any',
  'highlight:write:any',
  'live_stream:read:any',
  'mapping:read:any',
  'mapping:write:any',
  'measurement_period:read:any',
  'measurement_period:write:any',
  'media_asset:read:any',
  'media_asset:write:any',
  'notification:read:any',
  'notification:write:any',
  'object:read:any',
  'org:read:any',
  'org:write:any',
  'patient:read:all',
  'room:read:any',
  'room:write:any',
  'site:read:any',
  'site:write:any',
  'staffing_needs:read:any',
  'staffing_needs:write:any',
  'user:read:any',
  'available_times:email:any',
]

const getPermissions = (userType: UserType) => {
  switch (userType) {
    case UserType.ALL_ACCESS:
      return permissions
    case UserType.TURNOVERS_ACCESS_ONLY:
      return ['dashboard:turnovers_dashboard:read:any']
  }
}

const getUiPermissions = (
  access?: boolean,
  userPermissions?: Partial<UserUiPermissions>
) => {
  const defaultPermissions = (accessValue: boolean): UserUiPermissions => {
    const permissions = aUserUiPermissions() as {
      [key: string]: string | boolean
    }

    return Object.keys(permissions).reduce<UserUiPermissions>(
      (accum, curr) => ({
        ...accum,
        [curr]:
          typeof permissions[curr] === 'boolean'
            ? accessValue
            : permissions[curr],
      }),
      permissions as UserUiPermissions
    )
  }

  const permissionsBasedOnAccess = defaultPermissions(
    access !== undefined ? access : true
  )
  return aUserUiPermissions({
    ...permissionsBasedOnAccess,
    ...userPermissions,
  })
}

export const GetCurrentUserData = (
  userType: UserType,
  userPermissions?: Partial<UserUiPermissions>
): {
  data: GetCurrentUser
  userType: UserType
  userPermissions?: Partial<UserUiPermissions>
} => {
  const isAllAccess = userType === UserType.ALL_ACCESS
  return {
    data: {
      me: {
        __typename: 'User',
        id: 'google-apps|<EMAIL>',
        email: '<EMAIL>',
        organizations: {
          edges: [
            {
              node: {
                id: 'home_hospital',
                auth0OrgId: 'org_123456',
                name: 'Home Hospital',
                sites: {
                  edges: [
                    {
                      node: {
                        id: 'site-01',
                        timezone: 'America/Chicago',
                        name: 'Site 01',
                        rooms: {
                          edges: [
                            {
                              node: {
                                id: 'site-01-01',
                                name: 'Site 02 Room 01',
                                __typename: 'Room',
                              },
                              __typename: 'RoomEdge',
                            },
                            {
                              node: {
                                id: 'site-01-02',
                                name: 'Site 02 Room 02',
                                __typename: 'Room',
                              },
                              __typename: 'RoomEdge',
                            },
                            {
                              node: {
                                id: 'site-01-03',
                                name: 'Site 02 Room 03',
                                __typename: 'Room',
                              },
                              __typename: 'RoomEdge',
                            },
                          ],
                          __typename: 'RoomConnection',
                        },
                        __typename: 'Site',
                      },
                      __typename: 'SiteEdge',
                    },
                    {
                      node: {
                        id: 'site-02',
                        timezone: 'America/Chicago',
                        name: 'Site 02',
                        rooms: {
                          edges: [
                            {
                              node: {
                                id: 'site-02-01',
                                name: 'Site 02 Room 01',
                                __typename: 'Room',
                              },
                              __typename: 'RoomEdge',
                            },
                            {
                              node: {
                                id: 'site-02-02',
                                name: 'Site 02 Room 02',
                                __typename: 'Room',
                              },
                              __typename: 'RoomEdge',
                            },
                          ],
                          __typename: 'RoomConnection',
                        },
                        __typename: 'Site',
                      },
                      __typename: 'SiteEdge',
                    },
                  ],
                  __typename: 'SiteConnection',
                },
                __typename: 'Organization',
              },
              __typename: 'OrganizationEdge',
            },
          ],
          __typename: 'OrganizationConnection',
        },
        permissions: getPermissions(userType),
        uiPermissions: getUiPermissions(isAllAccess, userPermissions),
      },
    },
    userType,
    userPermissions,
  }
}
