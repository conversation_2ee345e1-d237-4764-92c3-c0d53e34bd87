import { generatePath, Navigate } from 'react-router'

import { addEventProcessor, Event } from '@sentry/react'

import { LoadingBackdrop } from 'src/components/LoadingBackdrop'
import { useCurrentUser } from 'src/modules/user/hooks'
import { AuthErrorPage } from 'src/pages/AuthErrorPage'
import { LocationPath } from 'src/router/types'
import { useMenuItems } from 'src/router/useMenuItems'

/**
 * Redirect to a page the user is authorized to access. If they are not authorized to access any
 * page, show a helpful error message.
 */
export const HomePage = (): React.JSX.Element => {
  const { permissions, loading: isLoading } = useCurrentUser()
  let locationPath: string | undefined = undefined

  const { menuItems, isLoadingMenuItems } = useMenuItems()
  if (!isLoadingMenuItems && menuItems.length > 1) {
    locationPath = LocationPath.Home
  } else if (permissions?.dashboardScheduleEnabled) {
    locationPath = LocationPath.Schedule
  } else if (permissions?.dashboardInsightsEnabled) {
    locationPath = LocationPath.CaseLengths
  } else if (permissions?.dashboardLiveEnabled) {
    locationPath = LocationPath.Live
  } else if (permissions?.dashboardHighlightsEnabled) {
    locationPath = LocationPath.Highlights
  } else if (permissions?.bigBoardEnabled) {
    locationPath = LocationPath.Boards
  } else if (permissions?.caseDurationEnabled) {
    locationPath = LocationPath.CaseDuration
  } else if (permissions?.dashboardTurnoversEnabled) {
    locationPath = LocationPath.TurnoversDashboard
  }

  if (isLoading || isLoadingMenuItems) {
    return <LoadingBackdrop />
  }

  // When we redirect from the home page the event transaction name
  // grouping is maintained as the home page transaction of `/`. We want
  // to use the redirect url for more representative transaction grouping
  // in Sentry's performance metrics
  if (locationPath) {
    addEventProcessor((event: Event) => {
      event.transaction = locationPath
      return event
    })
  }

  return locationPath ? (
    <Navigate replace to={generatePath(locationPath)} />
  ) : (
    <AuthErrorPage message={'You do not have permission to view any page.'} />
  )
}
