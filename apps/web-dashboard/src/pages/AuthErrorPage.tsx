import { useTheme } from '@emotion/react'

import { Button, ErrorPage, P2 } from '@apella/component-library'
import { useApellaAuth0 } from '@apella/hooks'
import { ACTIVE_ORG_ID_KEY } from 'src/router/constants'
import { logger } from 'src/utils/exceptionLogging'

export interface AuthErrorPageProps {
  message: string
}

export const AuthErrorPage = ({
  message,
}: AuthErrorPageProps): React.JSX.Element => {
  const theme = useTheme()
  const { logout } = useApellaAuth0()

  // Log auth errors to Sentry so we can be alerted for any issues.
  logger.error(`Auth Error: ${message}`)

  return (
    <ErrorPage
      header={'Oh no! Something went wrong.'}
      body={<P2 color={theme.palette.gray[40]}>{message}</P2>}
      actions={
        <Button
          onClick={() => {
            logout({ logoutParams: { returnTo: window.location.origin } })
            // Clear active orgId key to try to resolve auth issues if the user doesn't
            // have access to the active orgId. For example:
            // * A user is removed from the organization and tries to log-in with that orgId
            // * The local storage becomes corrupt in some way.
            localStorage.removeItem(ACTIVE_ORG_ID_KEY)
          }}
        >
          Logout
        </Button>
      }
    />
  )
}
