import { type ReactNode } from 'react'

import { mediaQueries, remSpacing, ZIndex } from '@apella/component-library'

export const MetricsContainer = ({
  graph,
  summaryTile,
  table,
}: {
  graph: ReactNode
  summaryTile?: ReactNode
  table?: ReactNode
}) => {
  return (
    <div
      css={{
        zIndex: ZIndex.DEFAULT,
        display: 'grid',
        gridTemplateColumns: 'minmax(0,1fr)',
        gap: remSpacing.gutter,
      }}
    >
      <div
        css={{
          display: 'grid',
          gap: remSpacing.gutter,
          [mediaQueries.lg]: {
            gridAutoFlow: 'column',
            gridTemplateColumns: '3fr 1fr',
          },
        }}
      >
        {graph}
        {summaryTile}
      </div>
      {table}
    </div>
  )
}
