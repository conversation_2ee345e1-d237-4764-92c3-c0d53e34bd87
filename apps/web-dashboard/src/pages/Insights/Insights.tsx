import { generatePath, Outlet } from 'react-router'

import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { UserViewContext } from 'src/components/UserViews/useUserFilterViews'
import { CaseLength } from 'src/pages/Insights/CaseLength/CaseLength'
import { FirstCaseStart } from 'src/pages/Insights/FirstCaseStart/FirstCaseStart'
import { Turnover } from 'src/pages/Insights/Turnover/Turnover'
import { LocationPath } from 'src/router/types'

import InsightsPageContextProvider, {
  useInsightsPageContext,
} from './InsightsContext'
import { METRICS } from './types'

const Insights = ({ metric }: { metric: METRICS }): React.JSX.Element => {
  return (
    <InsightsPageContextProvider metric={metric}>
      <InsightsPage />
      <Outlet />
    </InsightsPageContextProvider>
  )
}

const DEFAULT_METRICS = [
  {
    id: METRICS.CASE.toString(),
    display: METRICS.CASE.toString(),
    to: generatePath(LocationPath.CaseLengths),
  },
  {
    id: METRICS.FIRST_CASE_START.toString(),
    display: METRICS.FIRST_CASE_START.toString(),
    to: generatePath(LocationPath.FirstCaseStarts),
  },
  {
    id: METRICS.TURNOVER.toString(),
    display: METRICS.TURNOVER.toString(),
    to: generatePath(LocationPath.Turnovers),
  },
]

const InsightsPage = (): React.JSX.Element => {
  const { selectedView, selectedMetric } = useInsightsPageContext()

  return (
    <PageContentTemplate
      title="Insights"
      views={DEFAULT_METRICS}
      selectedViewId={selectedView}
      userViewContexts={[UserViewContext.Insights]}
    >
      {selectedMetric == METRICS.TURNOVER && <Turnover />}
      {selectedMetric == METRICS.FIRST_CASE_START && <FirstCaseStart />}
      {selectedMetric == METRICS.CASE && <CaseLength />}
    </PageContentTemplate>
  )
}

export default Insights
