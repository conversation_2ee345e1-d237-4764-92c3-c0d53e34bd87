import { useCallback, useEffect, useState } from 'react'

import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

import { Paging } from './types'

export const PAGE_SIZE = 10

export const DEFAULT_PAGING_STATE: Paging = {
  limit: PAGE_SIZE,
  offset: 0,
}

export function usePagingState({
  loggingState,
  totalCount,
  pageSize = PAGE_SIZE,
  event,
}: {
  totalCount: number | undefined
  pageSize?: number
  loggingState?: Record<string, unknown>
  event: EVENTS
}) {
  const eventsLogger = useAnalyticsEventLogger()
  const [pagingState, setPagingState] = useState(DEFAULT_PAGING_STATE)

  const { offset } = pagingState

  const onChangePage = useCallback(
    (newOffset: number) => {
      const newState = { ...loggingState, offset: newOffset }
      setPagingState((prev) => ({ ...prev, offset: newOffset }))

      eventsLogger(event, newState)
    },
    [loggingState, eventsLogger, event]
  )

  const onNextPage = useCallback(() => {
    onChangePage(offset + pageSize)
  }, [offset, onChangePage, pageSize])

  const onPreviousPage = useCallback(() => {
    onChangePage(offset - pageSize)
  }, [offset, onChangePage, pageSize])

  useEffect(
    function ensureOffsetWithinBounds() {
      if (totalCount === undefined) return

      const maxOffset = calculateMaxOffset(totalCount, pageSize)
      if (offset > maxOffset) {
        setPagingState((prev) => ({ ...prev, offset: maxOffset }))
      }
    },
    [totalCount, pageSize, offset]
  )

  return {
    pagingState,
    onChangePage,
    hasPreviousPage: offset > 0,
    hasNextPage:
      totalCount !== undefined ? offset + pageSize < totalCount : false,
    onNextPage,
    onPreviousPage,
  }
}

/**
 * Given a number of items and a page size, returns the maximum offset value.
 */
function calculateMaxOffset(totalCount: number, pageSize: number): number {
  if (totalCount < pageSize) {
    return 0
  }
  return Math.floor((totalCount - 1) / pageSize) * pageSize
}
