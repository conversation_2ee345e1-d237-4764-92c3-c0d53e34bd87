import { useCallback, useEffect, useMemo } from 'react'
import { useSearchParams } from 'react-router'

import { isEqual } from 'lodash'
import { DateTime } from 'luxon'

import { DEFAULT_CASE_CLASSIFICATION_TYPES_ID } from 'src/components/CaseClassificationTypesFilter'
import { DEFAULT_DAYS_OF_WEEK } from 'src/components/DayOfWeekFilter'
import { toIds } from 'src/components/Filters/FilterWithCount'
import { FlipRoomValues } from 'src/components/FlipRoomFilter'
import { CaseGraphMode } from 'src/components/InsightsGraph'
import { DEFAULT_PROCEDURES } from 'src/components/ProceduresFilter'
import { DEFAULT_SERVICE_LINES } from 'src/components/ServiceLinesFilter'
import { DEFAULT_STAFF } from 'src/components/StaffFilter'
import { TimePeriod } from 'src/components/TimePeriodFilter'
import { useTimezone } from 'src/Contexts'
import { CASES, TURNOVERS } from 'src/modules/cube/types/dataCubes'
import {
  useAnesthesiaStaffWithCount,
  useCirculatorsWithCount,
  usePrimarySurgeonsWithCount,
  useProceduresWithCount,
  useScrubTechsWithCount,
} from 'src/modules/insights/hooks'
import { useCurrentUser } from 'src/modules/user/hooks'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { determineDimension, Dimension } from 'src/utils/bucketHelpers'
import { getCleanedUrlParam } from 'src/utils/getCleanedUrlParam'
import {
  dateTimeToUrlFriendlyDate,
  urlFriendlyDateToDateTime,
} from 'src/utils/urlFriendlyDate'

import { CaseLengthMeasures } from './CaseLength/caseLengthMeasures'
import { END_DATE, START_DATE } from './constants'
import { FirstCaseMeasures } from './FirstCaseStart/firstCaseMeasures'
import {
  getCasesQueryParams,
  getNewFirstCaseStartQueryParams,
  getTurnoverQueryParams,
} from './queries'
import { TurnoverExclusionOptions } from './Turnover/turnoverExclusionOptions'
import { TurnoverMeasures } from './Turnover/turnoverMeasures'
import { DayOfWeek, METRICS } from './types'

const defaultDurationPeriod = {
  min: 0,
  max: 45,
}

const defaultCaseTimePeriod = {
  min: DateTime.fromObject({ hour: 0, minute: 0 }).toISOTime({
    includeOffset: false,
  }),
  max: DateTime.fromObject({ hour: 23, minute: 59 }).toISOTime({
    includeOffset: false,
  }),
}

const defaultTurnoverTimePeriod = {
  min: DateTime.fromObject({ hour: 7, minute: 30 }).toISOTime({
    includeOffset: false,
  }),
  max: DateTime.fromObject({ hour: 17, minute: 0 }).toISOTime({
    includeOffset: false,
  }),
}

const metricsToDefaultGraphSort = {
  [METRICS.CASE]: CaseLengthMeasures.COUNT,
  [METRICS.FIRST_CASE_START]: FirstCaseMeasures.LATE_STARTS_PERCENT,
  [METRICS.TURNOVER]: TurnoverMeasures.COUNT,
}

const metricsToCountFieldName = {
  [METRICS.CASE]: CASES.COUNT,
  [METRICS.FIRST_CASE_START]: CASES.COUNT,
  [METRICS.TURNOVER]: TURNOVERS.COUNT,
}

const metricsToParamTag = {
  [METRICS.CASE]: 'CaseLength',
  [METRICS.FIRST_CASE_START]: 'FirstCaseStart',
  [METRICS.TURNOVER]: 'Turnover',
}

enum InsightsAnalyticsEvents {
  FILTER_DATE_RANGE = 'FILTER_DATE_RANGE',
  FILTER_SITES = 'FILTER_SITES',
  FILTER_ROOMS = 'FILTER_ROOMS',
  FILTER_STAFF = 'FILTER_STAFF',
  FILTER_ANESTHESIA = 'FILTER_ANESTHESIA',
  FILTER_CIRCULATOR = 'FILTER_CIRCULATOR',
  FILTER_SCRUB_TECH = 'FILTER_SCRUB_TECH',
  FILTER_SERVICE_LINE = 'FILTER_SERVICE_LINE',
  FILTER_PROCEDURE = 'FILTER_PROCEDURE',
  FILTER_DAYS_OF_WEEK = 'FILTER_DAYS_OF_WEEK',

  CHANGE_GRAPH_DIMENSION = 'CHANGE_GRAPH_DIMENSION',
  CHANGE_GRAPH_TOP_COUNT = 'CHANGE_GRAPH_TOP_COUNT',
  CHANGE_GRAPH_SORT = 'CHANGE_GRAPH_SORT',
  CHANGE_GRAPH_STACK_GROUPING = 'CHANGE_GRAPH_STACK_GROUPING',

  PAGE_TABLE = 'PAGE_TABLE',
  SORT_TABLE = 'SORT_TABLE',
}

const metricsToAnalyticsEvent = {
  [CaseGraphMode.Volume]: {
    [InsightsAnalyticsEvents.FILTER_DATE_RANGE]:
      EVENTS.FILTER_CASE_VOLUME_DATE_RANGE,
    [InsightsAnalyticsEvents.FILTER_SITES]: EVENTS.FILTER_CASE_VOLUME_SITES,
    [InsightsAnalyticsEvents.FILTER_ROOMS]: EVENTS.FILTER_CASE_VOLUME_ROOMS,
    [InsightsAnalyticsEvents.FILTER_STAFF]: EVENTS.FILTER_CASE_VOLUME_STAFF,
    [InsightsAnalyticsEvents.FILTER_ANESTHESIA]:
      EVENTS.FILTER_CASE_VOLUME_ANESTHESIA,
    [InsightsAnalyticsEvents.FILTER_CIRCULATOR]:
      EVENTS.FILTER_CASE_VOLUME_CIRCULATOR,
    [InsightsAnalyticsEvents.FILTER_SCRUB_TECH]:
      EVENTS.FILTER_CASE_VOLUME_SCRUB_TECH,
    [InsightsAnalyticsEvents.FILTER_SERVICE_LINE]:
      EVENTS.FILTER_CASE_VOLUME_SERVICE_LINE,
    [InsightsAnalyticsEvents.FILTER_PROCEDURE]:
      EVENTS.FILTER_CASE_VOLUME_PROCEDURE,
    [InsightsAnalyticsEvents.FILTER_DAYS_OF_WEEK]:
      EVENTS.FILTER_CASE_VOLUME_DAYS_OF_WEEK,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_DIMENSION]:
      EVENTS.CHANGE_CASE_VOLUME_GRAPH_DIMENSION,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_TOP_COUNT]:
      EVENTS.CHANGE_CASE_VOLUME_GRAPH_TOP_COUNT,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_SORT]:
      EVENTS.CHANGE_CASE_VOLUME_GRAPH_SORT,
  },
  [METRICS.CASE]: {
    [InsightsAnalyticsEvents.FILTER_DATE_RANGE]:
      EVENTS.FILTER_CASE_LENGTH_DATE_RANGE,
    [InsightsAnalyticsEvents.FILTER_SITES]: EVENTS.FILTER_CASE_LENGTH_SITES,
    [InsightsAnalyticsEvents.FILTER_ROOMS]: EVENTS.FILTER_CASE_LENGTH_ROOMS,
    [InsightsAnalyticsEvents.FILTER_STAFF]: EVENTS.FILTER_CASE_LENGTH_STAFF,
    [InsightsAnalyticsEvents.FILTER_ANESTHESIA]:
      EVENTS.FILTER_CASE_LENGTH_ANESTHESIA,
    [InsightsAnalyticsEvents.FILTER_CIRCULATOR]:
      EVENTS.FILTER_CASE_LENGTH_CIRCULATOR,
    [InsightsAnalyticsEvents.FILTER_SCRUB_TECH]:
      EVENTS.FILTER_CASE_LENGTH_SCRUB_TECH,
    [InsightsAnalyticsEvents.FILTER_PROCEDURE]:
      EVENTS.FILTER_CASE_LENGTH_PROCEDURE,
    [InsightsAnalyticsEvents.FILTER_SERVICE_LINE]:
      EVENTS.FILTER_CASE_LENGTH_SERVICE_LINE,
    [InsightsAnalyticsEvents.FILTER_DAYS_OF_WEEK]:
      EVENTS.FILTER_CASE_LENGTH_DAYS_OF_WEEK,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_DIMENSION]:
      EVENTS.CHANGE_CASE_LENGTH_GRAPH_DIMENSION,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_TOP_COUNT]:
      EVENTS.CHANGE_CASE_LENGTH_GRAPH_TOP_COUNT,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_SORT]:
      EVENTS.CHANGE_CASE_LENGTH_GRAPH_SORT,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_STACK_GROUPING]:
      EVENTS.CHANGE_CASE_LENGTH_GRAPH_STACK_GROUPING,
  },
  [METRICS.FIRST_CASE_START]: {
    [InsightsAnalyticsEvents.FILTER_DATE_RANGE]:
      EVENTS.FILTER_FIRST_CASE_START_DATE_RANGE,
    [InsightsAnalyticsEvents.FILTER_SITES]:
      EVENTS.FILTER_FIRST_CASE_START_SITES,
    [InsightsAnalyticsEvents.FILTER_ROOMS]:
      EVENTS.FILTER_FIRST_CASE_START_ROOMS,
    [InsightsAnalyticsEvents.FILTER_STAFF]:
      EVENTS.FILTER_FIRST_CASE_START_STAFF,
    [InsightsAnalyticsEvents.FILTER_ANESTHESIA]:
      EVENTS.FILTER_FIRST_CASE_START_ANESTHESIA,
    [InsightsAnalyticsEvents.FILTER_CIRCULATOR]:
      EVENTS.FILTER_FIRST_CASE_START_CIRCULATOR,
    [InsightsAnalyticsEvents.FILTER_SCRUB_TECH]:
      EVENTS.FILTER_FIRST_CASE_START_SCRUB_TECH,
    [InsightsAnalyticsEvents.FILTER_SERVICE_LINE]:
      EVENTS.FILTER_FIRST_CASE_START_SERVICE_LINE,
    [InsightsAnalyticsEvents.FILTER_PROCEDURE]:
      EVENTS.FILTER_FIRST_CASE_START_PROCEDURE,
    [InsightsAnalyticsEvents.FILTER_DAYS_OF_WEEK]:
      EVENTS.FILTER_FIRST_CASE_START_DAYS_OF_WEEK,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_DIMENSION]:
      EVENTS.CHANGE_FIRST_CASE_START_GRAPH_DIMENSION,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_TOP_COUNT]:
      EVENTS.CHANGE_FIRST_CASE_START_GRAPH_TOP_COUNT,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_SORT]:
      EVENTS.CHANGE_FIRST_CASE_START_GRAPH_SORT,
  },
  [METRICS.TURNOVER]: {
    [InsightsAnalyticsEvents.FILTER_DATE_RANGE]:
      EVENTS.FILTER_TURNOVER_DATE_RANGE,
    [InsightsAnalyticsEvents.FILTER_SITES]: EVENTS.FILTER_TURNOVER_SITES,
    [InsightsAnalyticsEvents.FILTER_ROOMS]: EVENTS.FILTER_TURNOVER_ROOMS,
    [InsightsAnalyticsEvents.FILTER_STAFF]: EVENTS.FILTER_TURNOVER_STAFF,
    [InsightsAnalyticsEvents.FILTER_ANESTHESIA]:
      EVENTS.FILTER_TURNOVER_ANESTHESIA,
    [InsightsAnalyticsEvents.FILTER_CIRCULATOR]:
      EVENTS.FILTER_TURNOVER_CIRCULATOR,
    [InsightsAnalyticsEvents.FILTER_SCRUB_TECH]:
      EVENTS.FILTER_TURNOVER_SCRUB_TECH,
    [InsightsAnalyticsEvents.FILTER_SERVICE_LINE]:
      EVENTS.FILTER_TURNOVER_SERVICE_LINE,
    [InsightsAnalyticsEvents.FILTER_PROCEDURE]:
      EVENTS.FILTER_TURNOVER_PROCEDURE,
    [InsightsAnalyticsEvents.FILTER_DAYS_OF_WEEK]:
      EVENTS.FILTER_TURNOVER_DAYS_OF_WEEK,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_DIMENSION]:
      EVENTS.CHANGE_TURNOVER_GRAPH_DIMENSION,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_TOP_COUNT]:
      EVENTS.CHANGE_TURNOVER_GRAPH_TOP_COUNT,
    [InsightsAnalyticsEvents.CHANGE_GRAPH_SORT]:
      EVENTS.CHANGE_TURNOVER_GRAPH_SORT,
  },
}

export const useInsightsSearchParams = (metric: METRICS) => {
  const eventsLogger = useAnalyticsEventLogger()
  const { timezone } = useTimezone()
  const [searchParams, setSearchParams] = useSearchParams()

  const defaultMinTime = DateTime.now()
    .minus({ months: 1 })
    .startOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  const defaultMaxTime = DateTime.now()
    .minus({ days: 1 })
    .endOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  const defaultDateRange = useMemo(() => {
    return [new Date(defaultMinTime), new Date(defaultMaxTime)]
  }, [defaultMinTime, defaultMaxTime])

  const defaultTimePeriod = useMemo(
    () =>
      metric === METRICS.CASE
        ? defaultCaseTimePeriod
        : defaultTurnoverTimePeriod,
    [metric]
  )

  // Filter Keys
  const { currentOrganization } = useCurrentUser()
  const orgId = currentOrganization?.node?.id
  const paramPrefix = `${orgId}_Insights_${metricsToParamTag[metric]}`

  const STAFF = `${paramPrefix}_staff`
  const ANESTHESIA = `${paramPrefix}_anesthesia`
  const CIRCULATOR = `${paramPrefix}_circulator`
  const SCRUB_TECH = `${paramPrefix}_scrub_tech`
  const SERVICE_LINES = `${paramPrefix}_service_lines`
  const PROCEDURE = `${paramPrefix}_procedures`
  const SITE_IDS = `${paramPrefix}_sites`
  const ROOM_IDS = `${paramPrefix}_rooms`
  const WEEKDAYS = `${paramPrefix}_weekdays`
  const DIMENSION = `${paramPrefix}_dimension`
  const GRAPH_SORT = `${paramPrefix}_graph_sort`
  const MAX_DURATION = `${paramPrefix}_maxDuration`
  const FLIP_ROOM = `${paramPrefix}_show_flip_room`
  const TIME_PERIOD = `${paramPrefix}_timePeriod`
  const CASE_CLASSIFICATION_TYPES_ID = `${paramPrefix}_caseClassificationTypesId`
  const GRAPH_STACK_GROUPING = `${paramPrefix}_graph_stack_grouping`
  const IS_TIME_PERIOD_INVERTED = `${paramPrefix}_isTimePeriodInverted`
  const TURNOVER_EXCLUSION_OPTION = `${paramPrefix}_turnoverExclusionOption`

  const minTimeFromUrl = getCleanedUrlParam<string | undefined>({
    searchParams,
    key: START_DATE,
    defaultValue: undefined,
  })

  const maxTimeFromUrl = getCleanedUrlParam<string | undefined>({
    searchParams,
    key: END_DATE,
    defaultValue: undefined,
  })

  const minTime = (
    minTimeFromUrl
      ? urlFriendlyDateToDateTime(minTimeFromUrl)
      : DateTime.fromISO(defaultMinTime)
  )
    .startOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  const maxTime = (
    maxTimeFromUrl
      ? urlFriendlyDateToDateTime(maxTimeFromUrl)
      : DateTime.fromISO(defaultMaxTime)
  )
    .endOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  useEffect(() => {
    metric &&
      eventsLogger(EVENTS.CHANGE_INSIGHTS_METRIC_TYPE, {
        minTime,
        maxTime,
        metric,
      })
  }, [minTime, maxTime, eventsLogger, metric])

  const staffIds = searchParams.get(STAFF)
    ? JSON.parse(searchParams.get(STAFF)!)
    : DEFAULT_STAFF

  const anesthesiaIds = searchParams.get(ANESTHESIA)
    ? JSON.parse(searchParams.get(ANESTHESIA)!)
    : DEFAULT_STAFF

  const circulatorStaffIds = searchParams.get(CIRCULATOR)
    ? JSON.parse(searchParams.get(CIRCULATOR)!)
    : DEFAULT_STAFF

  const scrubTechStaffIds = searchParams.get(SCRUB_TECH)
    ? JSON.parse(searchParams.get(SCRUB_TECH)!)
    : DEFAULT_STAFF

  const serviceLineIds = searchParams.get(SERVICE_LINES)
    ? JSON.parse(searchParams.get(SERVICE_LINES)!)
    : DEFAULT_SERVICE_LINES

  const procedureIds = searchParams.get(PROCEDURE)
    ? JSON.parse(searchParams.get(PROCEDURE)!)
    : DEFAULT_PROCEDURES

  const siteIds = searchParams.get(SITE_IDS)
    ? JSON.parse(searchParams.get(SITE_IDS)!)
    : undefined

  const roomIds = searchParams.get(ROOM_IDS)
    ? JSON.parse(searchParams.get(ROOM_IDS)!)
    : undefined

  const daysOfWeek = searchParams.get(WEEKDAYS)
    ? JSON.parse(searchParams.get(WEEKDAYS)!)
    : DEFAULT_DAYS_OF_WEEK

  const dimension = searchParams.get(DIMENSION)
    ? JSON.parse(searchParams.get(DIMENSION)!)
    : determineDimension(DateTime.fromISO(minTime), DateTime.fromISO(maxTime))

  const graphSort = searchParams.get(GRAPH_SORT)
    ? JSON.parse(searchParams.get(GRAPH_SORT)!)
    : metricsToDefaultGraphSort[metric]

  const durationRangeMinutes = searchParams.get(MAX_DURATION)
    ? JSON.parse(searchParams.get(MAX_DURATION)!)
    : defaultDurationPeriod

  const showFlipRooms = searchParams.get(FLIP_ROOM)
    ? JSON.parse(searchParams.get(FLIP_ROOM)!)
    : undefined

  const timePeriod = searchParams.get(TIME_PERIOD)
    ? JSON.parse(searchParams.get(TIME_PERIOD)!)
    : defaultTimePeriod

  const caseClassificationTypesIds = searchParams.get(
    CASE_CLASSIFICATION_TYPES_ID
  )
    ? JSON.parse(searchParams.get(CASE_CLASSIFICATION_TYPES_ID)!)
    : DEFAULT_CASE_CLASSIFICATION_TYPES_ID

  const graphStackGrouping = searchParams.get(GRAPH_STACK_GROUPING)
    ? JSON.parse(searchParams.get(GRAPH_STACK_GROUPING)!)
    : CaseGraphMode.Breakdown

  const isTimePeriodInverted = searchParams.get(IS_TIME_PERIOD_INVERTED)
    ? JSON.parse(searchParams.get(IS_TIME_PERIOD_INVERTED)!)
    : false

  const turnoverExclusionOption = searchParams.get(TURNOVER_EXCLUSION_OPTION)
    ? JSON.parse(searchParams.get(TURNOVER_EXCLUSION_OPTION)!)
    : undefined

  const loggingState = useMemo(
    () => ({
      minTime,
      maxTime,
      staffIds,
      anesthesiaIds,
      circulatorStaffIds,
      scrubTechStaffIds,
      procedureIds,
      siteIds,
      roomIds,
      daysOfWeek,
      dimension,
      graphSort,
      durationRangeMinutes,
      showFlipRooms,
      timePeriod,
      caseClassificationTypesIds,
      graphStackGrouping,
      isTimePeriodInverted,
      turnoverExclusionOption,
    }),
    [
      minTime,
      maxTime,
      staffIds,
      anesthesiaIds,
      circulatorStaffIds,
      scrubTechStaffIds,
      procedureIds,
      siteIds,
      roomIds,
      daysOfWeek,
      dimension,
      graphSort,
      durationRangeMinutes,
      showFlipRooms,
      timePeriod,
      caseClassificationTypesIds,
      graphStackGrouping,
      isTimePeriodInverted,
      turnoverExclusionOption,
    ]
  )

  useEffect(
    () =>
      metric &&
      eventsLogger(EVENTS.LOAD_INSIGHTS_PAGE, {
        ...loggingState,
        startDateQueryString: minTime,
        endDateQueryString: maxTime,
      }),
    [minTime, maxTime, eventsLogger, metric, loggingState]
  )

  const loggingMode = useMemo(
    () =>
      metric === METRICS.CASE && graphStackGrouping === CaseGraphMode.Volume
        ? CaseGraphMode.Volume
        : metric,
    [metric, graphStackGrouping]
  )

  // Handlers
  const onChangeStaff = useCallback(
    (staffIds: string | string[] | undefined) => {
      const newState = {
        ...loggingState,
        staffIds: toIds(staffIds),
      }
      if (staffIds) {
        searchParams.set(STAFF, JSON.stringify(toIds(staffIds)))
      } else {
        searchParams.delete(STAFF)
      }

      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_STAFF
        ],
        newState
      )
    },
    [
      loggingState,
      eventsLogger,
      searchParams,
      setSearchParams,
      STAFF,
      loggingMode,
    ]
  )

  const onChangeAnesthesia = useCallback(
    (anesthesiaIds: string | string[] | undefined) => {
      const newState = {
        ...loggingState,
        anesthesiaIds: toIds(anesthesiaIds),
      }
      if (anesthesiaIds) {
        searchParams.set(ANESTHESIA, JSON.stringify(toIds(anesthesiaIds)))
      } else {
        searchParams.delete(ANESTHESIA)
      }

      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_ANESTHESIA
        ],
        newState
      )
    },
    [
      loggingState,
      eventsLogger,
      searchParams,
      setSearchParams,
      ANESTHESIA,
      loggingMode,
    ]
  )

  const onChangeCirculator = useCallback(
    (circulatorStaffIds: string | string[] | undefined) => {
      const newState = {
        ...loggingState,
        circulatorStaffIds: toIds(circulatorStaffIds),
      }
      if (circulatorStaffIds) {
        searchParams.set(CIRCULATOR, JSON.stringify(toIds(circulatorStaffIds)))
      } else {
        searchParams.delete(CIRCULATOR)
      }

      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_CIRCULATOR
        ],
        newState
      )
    },
    [
      loggingState,
      eventsLogger,
      searchParams,
      setSearchParams,
      CIRCULATOR,
      loggingMode,
    ]
  )

  const onChangeScrubTech = useCallback(
    (scrubTechStaffIds: string | string[] | undefined) => {
      const newState = {
        ...loggingState,
        scrubTechStaffIds: toIds(scrubTechStaffIds),
      }
      if (scrubTechStaffIds) {
        searchParams.set(SCRUB_TECH, JSON.stringify(toIds(scrubTechStaffIds)))
      } else {
        searchParams.delete(SCRUB_TECH)
      }

      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_SCRUB_TECH
        ],
        newState
      )
    },
    [
      loggingState,
      eventsLogger,
      searchParams,
      setSearchParams,
      SCRUB_TECH,
      loggingMode,
    ]
  )

  const onChangeServiceLines = useCallback(
    (serviceLineIds?: string | string[]) => {
      const newState = {
        ...loggingState,
        serviceLineIds: toIds(serviceLineIds),
      }
      if (serviceLineIds) {
        searchParams.set(SERVICE_LINES, JSON.stringify(toIds(serviceLineIds)))
      } else {
        searchParams.delete(SERVICE_LINES)
      }

      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_SERVICE_LINE
        ],
        newState
      )
    },
    [
      loggingState,
      loggingMode,
      eventsLogger,
      searchParams,
      setSearchParams,
      SERVICE_LINES,
    ]
  )

  const onChangeProcedure = useCallback(
    (procedureIds?: string | string[]) => {
      const newState = {
        ...loggingState,
        procedureIds: toIds(procedureIds),
      }
      if (procedureIds) {
        searchParams.set(PROCEDURE, JSON.stringify(toIds(procedureIds)))
      } else {
        searchParams.delete(PROCEDURE)
      }

      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_PROCEDURE
        ],
        newState
      )
    },
    [
      loggingState,
      eventsLogger,
      searchParams,
      setSearchParams,
      PROCEDURE,
      loggingMode,
    ]
  )

  const onChangeSites = useCallback(
    (siteIds?: string | string[]) => {
      const newState = {
        ...loggingState,
        siteIds: toIds(siteIds),
      }
      if (siteIds) {
        searchParams.set(SITE_IDS, JSON.stringify(toIds(siteIds)))
      } else {
        searchParams.delete(SITE_IDS)
      }

      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_SITES
        ],
        newState
      )
    },
    [
      eventsLogger,
      loggingState,
      searchParams,
      setSearchParams,
      SITE_IDS,
      loggingMode,
    ]
  )

  const onChangeRooms = useCallback(
    (roomIds?: string | string[]) => {
      const newState = {
        ...loggingState,
        roomIds: toIds(roomIds),
      }
      if (roomIds) {
        searchParams.set(ROOM_IDS, JSON.stringify(toIds(roomIds)))
      } else {
        searchParams.delete(ROOM_IDS)
      }

      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_ROOMS
        ],
        newState
      )
    },
    [
      eventsLogger,
      loggingState,
      searchParams,
      setSearchParams,
      ROOM_IDS,
      loggingMode,
    ]
  )

  const onChangeDaysOfWeek = useCallback(
    (newDaysOfWeek: DayOfWeek[] | undefined) => {
      const newState = {
        ...loggingState,
        daysOfWeek: newDaysOfWeek,
      }
      if (newDaysOfWeek) {
        searchParams.set(WEEKDAYS, JSON.stringify(newDaysOfWeek))
      } else {
        searchParams.delete(WEEKDAYS)
      }
      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_DAYS_OF_WEEK
        ],
        newState
      )
    },
    [
      eventsLogger,
      loggingState,
      searchParams,
      setSearchParams,
      WEEKDAYS,
      loggingMode,
    ]
  )

  const onChangeDimension = useCallback(
    (newDimension: Dimension) => {
      const newState = {
        ...loggingState,
        dimension: newDimension,
      }
      searchParams.set(DIMENSION, JSON.stringify(newDimension))
      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.CHANGE_GRAPH_DIMENSION
        ],
        newState
      )
    },
    [
      loggingState,
      eventsLogger,
      searchParams,
      setSearchParams,
      DIMENSION,
      loggingMode,
    ]
  )

  const onChangeGraphOrderBy = useCallback(
    (newGraphSort: string) => {
      const newState = {
        ...loggingState,
        graphSort: newGraphSort,
      }
      searchParams.set(GRAPH_SORT, JSON.stringify(newGraphSort))
      setSearchParams(searchParams)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.CHANGE_GRAPH_SORT
        ],
        newState
      )
    },
    [
      loggingState,
      eventsLogger,
      searchParams,
      setSearchParams,
      GRAPH_SORT,
      loggingMode,
    ]
  )

  const onChangeDurationRange = useCallback(
    (newDurationRange: TimePeriod<number>) => {
      searchParams.set(MAX_DURATION, JSON.stringify(newDurationRange))
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams, MAX_DURATION]
  )

  const onChangeFlipRoom = useCallback(
    (newFlipRoom?: FlipRoomValues) => {
      if (newFlipRoom) {
        searchParams.set(FLIP_ROOM, JSON.stringify(newFlipRoom))
      } else {
        searchParams.delete(FLIP_ROOM)
      }
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams, FLIP_ROOM]
  )

  const onChangeTimePeriod = useCallback(
    (newTimePeriod: TimePeriod<string>) => {
      searchParams.set(TIME_PERIOD, JSON.stringify(newTimePeriod))
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams, TIME_PERIOD]
  )

  const onChangeCaseClassificationTypesId = useCallback(
    (caseClassificationTypesIds?: string | string[]) => {
      const newState = {
        ...loggingState,
        caseClassificationTypesIds: toIds(caseClassificationTypesIds),
      }
      if (caseClassificationTypesIds) {
        searchParams.set(
          CASE_CLASSIFICATION_TYPES_ID,
          JSON.stringify(toIds(caseClassificationTypesIds))
        )
      } else {
        searchParams.delete(CASE_CLASSIFICATION_TYPES_ID)
      }

      setSearchParams(searchParams)
      eventsLogger(
        EVENTS.FILTER_TURNOVER_CASE_CLASSIFICATION_TYPES_ID,
        newState
      )
    },
    [
      loggingState,
      eventsLogger,
      searchParams,
      setSearchParams,
      CASE_CLASSIFICATION_TYPES_ID,
    ]
  )

  const onChangeGraphStackGrouping = useCallback(
    (graphStackGrouping?: string) => {
      const newState = {
        ...loggingState,
        graphStackGrouping,
      }
      if (graphStackGrouping) {
        searchParams.set(
          GRAPH_STACK_GROUPING,
          JSON.stringify(graphStackGrouping)
        )
      } else {
        searchParams.delete(GRAPH_STACK_GROUPING)
      }
      setSearchParams(searchParams)
      eventsLogger(EVENTS.CHANGE_CASE_LENGTH_GRAPH_STACK_GROUPING, newState)
    },
    [
      loggingState,
      eventsLogger,
      searchParams,
      setSearchParams,
      GRAPH_STACK_GROUPING,
    ]
  )

  const onChangeTimePeriodInverted = useCallback(
    (isInverted: boolean) => {
      searchParams.set(IS_TIME_PERIOD_INVERTED, JSON.stringify(isInverted))
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams, IS_TIME_PERIOD_INVERTED]
  )

  const onChangeTurnoverExclusionOption = useCallback(
    (newValue?: TurnoverExclusionOptions) => {
      if (newValue) {
        searchParams.set(TURNOVER_EXCLUSION_OPTION, JSON.stringify(newValue))
      } else {
        searchParams.delete(TURNOVER_EXCLUSION_OPTION)
      }
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams, TURNOVER_EXCLUSION_OPTION]
  )

  const onChangeDateRanges = useCallback(
    (minTime: DateTime, maxTime: DateTime) => {
      searchParams.set(
        START_DATE,
        JSON.stringify(dateTimeToUrlFriendlyDate(minTime))
      )
      searchParams.set(
        END_DATE,
        JSON.stringify(dateTimeToUrlFriendlyDate(maxTime))
      )
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams]
  )

  const localOnChangeDateRanges = useCallback(
    (dates: Date[]) => {
      const minTime = DateTime.fromJSDate(dates[0])
        .setZone(timezone)
        .startOf('day')
      const maxTime = DateTime.fromJSDate(dates[1])
        .setZone(timezone)
        .endOf('day')

      const newState = {
        ...loggingState,
        minTime: minTime.toISO(),
        maxTime: maxTime.toISO(),
      }

      const newBucketSize = determineDimension(minTime, maxTime, dimension)
      onChangeDimension(newBucketSize)
      eventsLogger(
        metricsToAnalyticsEvent[loggingMode][
          InsightsAnalyticsEvents.FILTER_DATE_RANGE
        ],
        newState
      )
      onChangeDateRanges(minTime, maxTime)
    },
    [
      eventsLogger,
      loggingState,
      dimension,
      onChangeDateRanges,
      timezone,
      onChangeDimension,
      loggingMode,
    ]
  )

  const showResetFiltersButton = useMemo(() => {
    return (
      !isEqual(minTime, defaultMinTime) ||
      !isEqual(maxTime, defaultMaxTime) ||
      !isEqual(staffIds, DEFAULT_STAFF) ||
      !isEqual(anesthesiaIds, DEFAULT_STAFF) ||
      !isEqual(serviceLineIds, DEFAULT_SERVICE_LINES) ||
      !isEqual(procedureIds, DEFAULT_PROCEDURES) ||
      !isEqual(siteIds, undefined) ||
      !isEqual(roomIds, undefined) ||
      !isEqual(daysOfWeek, DEFAULT_DAYS_OF_WEEK) ||
      !isEqual(
        dimension,
        determineDimension(DateTime.fromISO(minTime), DateTime.fromISO(maxTime))
      ) ||
      !isEqual(graphSort, metricsToDefaultGraphSort[metric]) ||
      !isEqual(caseClassificationTypesIds, undefined) ||
      !isEqual(durationRangeMinutes, defaultDurationPeriod) ||
      !isEqual(showFlipRooms, undefined) ||
      !isEqual(timePeriod, defaultTimePeriod) ||
      !isEqual(graphStackGrouping, CaseGraphMode.Breakdown) ||
      !isEqual(isTimePeriodInverted, false) ||
      !isEqual(turnoverExclusionOption, undefined)
    )
  }, [
    minTime,
    defaultMinTime,
    maxTime,
    defaultMaxTime,
    staffIds,
    anesthesiaIds,
    serviceLineIds,
    procedureIds,
    siteIds,
    roomIds,
    daysOfWeek,
    dimension,
    graphSort,
    metric,
    caseClassificationTypesIds,
    durationRangeMinutes,
    showFlipRooms,
    timePeriod,
    defaultTimePeriod,
    graphStackGrouping,
    isTimePeriodInverted,
    turnoverExclusionOption,
  ])

  const resetActions = useCallback(() => {
    localOnChangeDateRanges(defaultDateRange)
    onChangeStaff(DEFAULT_STAFF)
    onChangeAnesthesia(DEFAULT_STAFF)
    onChangeServiceLines(DEFAULT_SERVICE_LINES)
    onChangeProcedure(DEFAULT_PROCEDURES)
    onChangeSites()
    onChangeRooms()
    onChangeDaysOfWeek(DEFAULT_DAYS_OF_WEEK)
    onChangeDimension(
      determineDimension(DateTime.fromISO(minTime), DateTime.fromISO(maxTime))
    )
    onChangeGraphOrderBy(metricsToDefaultGraphSort[metric])
    onChangeDurationRange(defaultDurationPeriod)
    onChangeFlipRoom(undefined)
    onChangeTimePeriod(defaultTimePeriod)
    onChangeCaseClassificationTypesId(DEFAULT_CASE_CLASSIFICATION_TYPES_ID)
    onChangeGraphStackGrouping(CaseGraphMode.Breakdown)
    onChangeTimePeriodInverted(false)
    onChangeTurnoverExclusionOption(undefined)
  }, [
    localOnChangeDateRanges,
    defaultDateRange,
    onChangeStaff,
    onChangeAnesthesia,
    onChangeServiceLines,
    onChangeProcedure,
    onChangeSites,
    onChangeRooms,
    onChangeDaysOfWeek,
    onChangeDimension,
    minTime,
    maxTime,
    onChangeGraphOrderBy,
    metric,
    onChangeDurationRange,
    onChangeFlipRoom,
    onChangeTimePeriod,
    defaultTimePeriod,
    onChangeCaseClassificationTypesId,
    onChangeGraphStackGrouping,
    onChangeTimePeriodInverted,
    onChangeTurnoverExclusionOption,
  ])

  const cubeParams = useMemo(() => {
    let cubeParams = {}

    switch (metric) {
      case METRICS.CASE:
        cubeParams = getCasesQueryParams({
          siteIds,
          roomIds,
          serviceLineIds,
          procedureIds,
          staffIds,
          anesthesiaIds,
          circulatorStaffIds,
          scrubTechStaffIds,
          minDateTime: minTime,
          maxDateTime: maxTime,
          daysOfWeek,
          timezone,
          timePeriod,
          isTimePeriodInverted,
        })
        break
      case METRICS.FIRST_CASE_START:
        cubeParams = getNewFirstCaseStartQueryParams({
          minDateTime: minTime,
          maxDateTime: maxTime,
          daysOfWeek,
          siteIds,
          roomIds,
          serviceLineIds,
          procedureIds,
          staffIds,
          anesthesiaIds,
          timezone,
        })
        break
      case METRICS.TURNOVER:
        cubeParams = getTurnoverQueryParams({
          caseClassificationTypesIds,
          siteIds,
          roomIds,
          serviceLineIds,
          procedureIds,
          staffIds,
          anesthesiaIds,
          minDateTime: minTime,
          maxDateTime: maxTime,
          daysOfWeek,
          timezone,
          timePeriod,
          durationRangeMinutes,
          showFlipRooms,
          turnoverExclusionOption,
        })
        break
      default:
        break
    }
    return cubeParams
  }, [
    metric,
    siteIds,
    roomIds,
    serviceLineIds,
    procedureIds,
    staffIds,
    anesthesiaIds,
    circulatorStaffIds,
    scrubTechStaffIds,
    minTime,
    maxTime,
    daysOfWeek,
    timezone,
    timePeriod,
    isTimePeriodInverted,
    caseClassificationTypesIds,
    durationRangeMinutes,
    showFlipRooms,
    turnoverExclusionOption,
  ])

  const { staffWithCount } = usePrimarySurgeonsWithCount(
    cubeParams,
    metricsToCountFieldName[metric]
  )

  const { staffWithCount: anesthesiaStaffWithCount } =
    useAnesthesiaStaffWithCount(cubeParams, metricsToCountFieldName[metric])

  const { staffWithCount: circulatorStaffWithCount } = useCirculatorsWithCount(
    cubeParams,
    metricsToCountFieldName[metric]
  )

  const { staffWithCount: scrubTechStaffWithCount } = useScrubTechsWithCount(
    cubeParams,
    metricsToCountFieldName[metric]
  )

  const { proceduresWithCount } = useProceduresWithCount(
    cubeParams,
    metricsToCountFieldName[metric]
  )

  return {
    paramPrefix,
    loggingState,

    minTime,
    maxTime,
    staffIds,
    anesthesiaIds,
    circulatorStaffIds,
    scrubTechStaffIds,
    procedureIds,
    serviceLineIds,
    siteIds,
    roomIds,
    daysOfWeek,
    dimension,
    graphSort,
    durationRangeMinutes,
    showFlipRooms,
    timePeriod,
    caseClassificationTypesIds,
    graphStackGrouping,
    isTimePeriodInverted,
    turnoverExclusionOption,

    showResetFiltersButton,
    cubeParams,
    staffWithCount,
    anesthesiaStaffWithCount,
    circulatorStaffWithCount,
    scrubTechStaffWithCount,
    proceduresWithCount,

    localOnChangeDateRanges,
    onChangeStaff,
    onChangeAnesthesia,
    onChangeCirculator,
    onChangeScrubTech,
    onChangeProcedure,
    onChangeServiceLines,
    onChangeSites,
    onChangeRooms,
    onChangeDaysOfWeek,
    onChangeDimension,
    onChangeGraphOrderBy,
    onChangeDurationRange,
    onChangeFlipRoom,
    onChangeTimePeriod,
    onChangeCaseClassificationTypesId,
    onChangeGraphStackGrouping,
    onChangeTimePeriodInverted,
    onChangeTurnoverExclusionOption,
    resetActions,
  }
}
