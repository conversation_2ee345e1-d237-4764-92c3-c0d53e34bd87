import { Duration } from 'luxon'

import {
  convertDurationToMinutesString,
  convertFromUtcString,
} from 'src/pages/Insights/dateTimeHelpers'

describe('convertDurationToMinutesString', () => {
  test('test duration converts minutes and hours to correct time in minutes', () => {
    const expected = 110
    const durationWithMinAndHour = Duration.fromObject({
      hours: 1,
      minutes: 50,
    })
    expect(convertDurationToMinutesString(durationWithMinAndHour)).toBe(
      expected.toString()
    )
  })

  test('test duration is unchanged when the input is only minutes', () => {
    const durationWithOnlyMin = Duration.fromObject({ hours: 0, minutes: 50 })
    const result = convertDurationToMinutesString(durationWithOnlyMin)
    expect(result).toBe('50')
  })

  test('test duration converts hours correctly to minutes', () => {
    const durationWithOnlyHour = Duration.fromObject({ hours: 3, minutes: 0 })
    const result = convertDurationToMinutesString(durationWithOnlyHour)
    expect(result).toBe('180')
  })

  test('test duration converts milliseconds to minutes', () => {
    const expectedMinutes = 26
    const durationWithOnlyMilliseconds = Duration.fromObject({
      hours: 0,
      minutes: 0,
      milliseconds: expectedMinutes * 60 * 1000 - 1000 * 1.4,
    })
    expect(convertDurationToMinutesString(durationWithOnlyMilliseconds)).toBe(
      expectedMinutes.toString()
    )
  })

  test('test duration converts milliseconds and hours to minutes', () => {
    const durationWithHoursAndMilliseconds = Duration.fromObject({
      hours: 1,
      minutes: 3,
      milliseconds: 1588502,
    })
    const result = convertDurationToMinutesString(
      durationWithHoursAndMilliseconds
    )
    expect(result).toBe('89')
  })

  test('test duration returns an empty string when duration is undefined', () => {
    const result = convertDurationToMinutesString(undefined)
    expect(result).toBe('')
  })

  test('duration truncates to minutes', () => {
    const result = convertDurationToMinutesString(
      Duration.fromObject({
        minutes: 0,
        seconds: 59,
      }),
      'floor'
    )
    expect(result).toBe('0')
  })
})

describe('convertFromUtcString', () => {
  it('string without timezone is properly converted', () => {
    const date = '2020-01-01T05:00:00'
    const result = convertFromUtcString(date, 'America/New_York')
    expect(result.toString()).toEqual('2020-01-01T00:00:00.000-05:00')
  })
})
