import { createContext, useContext, useMemo } from 'react'

import { LoadingBackdrop } from 'src/components/LoadingBackdrop'
import { useCurrentUser } from 'src/modules/user/hooks'

import {
  UserViewContext,
  useUserFilterViews,
} from '../../components/UserViews/useUserFilterViews'
import { METRICS } from './types'

const InsightsPageContext = createContext<{
  selectedMetric?: METRICS
  selectedView?: string
}>({
  selectedView: '',
})

const InsightsPageContextProvider = ({
  children,
  metric,
}: {
  children: React.ReactNode
  metric: METRICS
}) => {
  const { loading: userLoading } = useCurrentUser()

  const {
    selectedView: selectedViewFromParams,
    loading: userFilterViewsLoading,
  } = useUserFilterViews({
    contexts: [UserViewContext.Insights],
  })

  const selectedView = !userFilterViewsLoading
    ? selectedViewFromParams || metric
    : undefined

  const value = useMemo(
    () => ({
      selectedMetric: metric,
      selectedView,
    }),
    [metric, selectedView]
  )

  if (userLoading || userFilterViewsLoading) {
    return <LoadingBackdrop />
  }

  return (
    <InsightsPageContext.Provider value={value}>
      {children}
    </InsightsPageContext.Provider>
  )
}

export default InsightsPageContextProvider

// Create a hook to use the APIContext, this is a Kent C. Dodds pattern
export function useInsightsPageContext() {
  const context = useContext(InsightsPageContext)
  if (context === undefined) {
    throw new Error('Context must be used within a Provider')
  }
  return context
}
