import { DashComponent } from 'src/components/DashComponent'
import { ProcedureShape } from 'src/components/ProceduresFilter'
import { StaffShape } from 'src/components/StaffFilter'
import { GetSiteOptionsFilter } from 'src/modules/site/__generated__'

type GetInsightsSearchFilters_sites_edges =
  GetSiteOptionsFilter['sites']['edges'][number]

export const getStaffNameById = (staff: StaffShape[]): Map<string, string> => {
  return staff.reduce((acc, staff) => {
    if (acc.has(staff.node.id) !== undefined) {
      acc.set(staff.node.id, `${staff.node.lastName}, ${staff.node.firstName}`)
    }
    return acc
  }, new Map<string, string>())
}

export const getStaffIdByName = (staff: StaffShape[]): Map<string, string> => {
  return staff.reduce((acc, staff) => {
    const name = `${staff.node.lastName}, ${staff.node.firstName}`
    if (acc.has(name) !== undefined) {
      acc.set(name, staff.node.id)
    }
    return acc
  }, new Map<string, string>())
}

export const getRoomNameById = (
  sites: GetInsightsSearchFilters_sites_edges[],
  siteIds?: string[]
): Map<string, string> => {
  return sites
    .flatMap((s) => s.node.rooms.edges)
    .reduce((acc, room) => {
      if (acc.has(room.node.id) !== undefined) {
        const name =
          sites.length > 1 && (!siteIds || siteIds?.length > 1)
            ? `${room.node.site.name}-${room.node.name}`
            : room.node.name
        acc.set(room.node.id, name)
      }
      return acc
    }, new Map<string, string>())
}

export const getRoomIdByName = (
  sites: GetInsightsSearchFilters_sites_edges[],
  siteIds?: string[]
): Map<string, string> => {
  return sites
    .flatMap((s) => s.node.rooms.edges)
    .reduce((acc, room) => {
      const name =
        sites.length > 1 && (!siteIds || siteIds?.length > 1)
          ? `${room.node.site.name}-${room.node.name}`
          : room.node.name
      if (acc.has(name) !== undefined) {
        acc.set(name, room.node.id)
      }
      return acc
    }, new Map<string, string>())
}

export const getProcedureNameById = (
  procedures: ProcedureShape[]
): Map<string, string> => {
  return procedures.reduce((acc, procedure) => {
    if (acc.has(procedure.node.id) !== undefined) {
      acc.set(procedure.node.id, procedure.node.name)
    }
    return acc
  }, new Map<string, string>())
}

export const getProcedureIdByName = (
  procedures: ProcedureShape[]
): Map<string, string> => {
  return procedures.reduce((acc, procedure) => {
    if (acc.has(procedure.node.name) !== undefined) {
      acc.set(procedure.node.name, procedure.node.id)
    }
    return acc
  }, new Map<string, string>())
}

export const getServiceLineNameById = (
  serviceLines: { id: string; name: string }[]
): Map<string, string> => {
  return serviceLines.reduce((acc, serviceLine) => {
    if (acc.has(serviceLine.id) !== undefined) {
      acc.set(serviceLine.id, serviceLine.name)
    }
    return acc
  }, new Map<string, string>())
}

export const getServiceLineIdByName = (
  serviceLines: { id: string; name: string }[]
): Map<string, string> => {
  return serviceLines.reduce((acc, serviceLine) => {
    if (acc.has(serviceLine.name) !== undefined) {
      acc.set(serviceLine.name, serviceLine.id)
    }
    return acc
  }, new Map<string, string>())
}

export const generateListUiDisplay = (list?: string[]) =>
  list && list.length > 0 ? list.join('; ') : <DashComponent />
export const displayCustomerCaseId = (customerCaseId?: string) =>
  customerCaseId ? customerCaseId : <DashComponent />

export const displayIsAddOn = (scheduledCaseId?: string, isAddOn?: boolean) => {
  if (!scheduledCaseId) {
    return <DashComponent />
  }

  if (isAddOn === true) {
    return 'Yes'
  } else {
    return 'No'
  }
}

export const displayIsAddOnCsv = (
  scheduledCaseId?: string,
  isAddOn?: boolean
) => {
  if (!scheduledCaseId) {
    return '-'
  }

  if (isAddOn === true) {
    return 'Yes'
  } else {
    return 'No'
  }
}

export const displayCustomerCaseIdCsv = (customerCaseId?: string) =>
  customerCaseId ? customerCaseId : '-'

export const generateListCsvDisplay = (list?: string[]) =>
  list && list.length > 0 ? list.join('; ') : '-'
