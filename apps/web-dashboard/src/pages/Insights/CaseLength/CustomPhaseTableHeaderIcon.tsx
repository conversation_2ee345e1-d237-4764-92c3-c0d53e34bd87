import { useState } from 'react'

import {
  Dropdown<PERSON>rapper,
  Button,
  theme,
  DotsHorizontal,
  Dropdown,
  Edit,
  Delete,
} from '@apella/component-library'

interface Props {
  id: string
  isModalOpen: boolean

  onDelete: (columnName: string) => void
  setEditCustomConfig: (id: string) => void
  setIsOpenModal: (isOpen: boolean) => void
}

export const CustomPhaseTableHeaderIcon = ({
  setIsOpenModal,
  onDelete,
  id,
  setEditCustomConfig,
}: Props) => {
  const [isOpen, setIsOpen] = useState(false)

  const handleEdit = async () => {
    setIsOpenModal(true)
    setEditCustomConfig(id)
  }

  return (
    <DropdownWrapper
      display="block"
      open={isOpen}
      onClose={() => {
        setIsOpen(false)
      }}
      css={{
        alignItems: 'center',
      }}
    >
      <Button
        onClick={(event) => {
          // Prevent the click event from triggering sort order change on the column.
          event.stopPropagation()
          setIsOpen((prev) => !prev)
        }}
        type="button"
        buttonType="icon"
        appearance="link"
        size="md"
        css={{ color: theme.palette.gray[50] }}
      >
        <DotsHorizontal size="sm" css={{ transform: 'rotate(90deg)' }} />
      </Button>
      <Dropdown open={isOpen} left minWidth="fit-content">
        <div
          css={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
          }}
        >
          <Button
            onClick={(event) => {
              // Prevent the click event from triggering sort order change on the column.
              event.stopPropagation()
              setIsOpen(false)
              handleEdit()
            }}
            type="button"
            appearance="link"
            css={{ color: theme.palette.gray[70] }}
          >
            <Edit />
            Modify
          </Button>
          <Button
            onClick={(event) => {
              // Prevent the click event from triggering sort order change on the column.
              event.stopPropagation()
              setIsOpen(false)
              onDelete(id)
            }}
            type="button"
            appearance="link"
            css={{ color: theme.palette.gray[70] }}
          >
            <Delete /> Delete
          </Button>
        </div>
      </Dropdown>
    </DropdownWrapper>
  )
}
