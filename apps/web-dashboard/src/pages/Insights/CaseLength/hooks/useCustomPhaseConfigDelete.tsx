import { useCallback } from 'react'

import { gql, useMutation } from '@apollo/client'

import {
  customPhaseConfigDelete,
  customPhaseConfigDeleteVariables,
} from './__generated__'
import { GET_CUSTOM_PHASE_CONFIG } from './useCustomPhaseConfigQuery'

const CUSTOM_PHASE_CONFIG_DELETE_MUTATION = gql`
  mutation customPhaseConfigDelete($input: CustomPhaseConfigDeleteInput!) {
    customPhaseConfigDelete(input: $input) {
      success
      id
    }
  }
`
export const useCustomPhaseConfigDelete = () => {
  const [upsertCustomPhaseConfig, { called: mutationStarted }] = useMutation<
    customPhaseConfigDelete,
    customPhaseConfigDeleteVariables
  >(CUSTOM_PHASE_CONFIG_DELETE_MUTATION, {
    refetchQueries: [{ query: GET_CUSTOM_PHASE_CONFIG }],
  })
  const doDeleteCustomPhaseConfig = useCallback(
    ({ id }: { id: string }) => {
      return upsertCustomPhaseConfig({
        variables: {
          input: {
            id,
          },
        },
      })
    },
    [upsertCustomPhaseConfig]
  )

  return {
    doDeleteCustomPhaseConfig,
    mutationStarted,
  }
}
