import { useQuery } from '@apollo/client'

import { useCurrentUser } from 'src/modules/user/hooks'
import { useEventTypes } from 'src/utils/hooks'

import { GetObservationTypeNamesForCustomPhases } from '../../__generated__'
import { GET_OBSERVATION_TYPE_NAMES_FOR_CUSTOM_PHASES } from '../../queries'

const useCustomPhaseEventData = () => {
  const { currentOrganization } = useCurrentUser()
  const apellaEventTypes = useEventTypes()

  const supportedApellaEventTypes = new Set<string>([
    'anesthesia_draping',
    'anesthesia_undraping',
    'back_table_cleared',
    'back_table_open',
    'endo_pack_open',
    'intubation',
    'mop_out_turnover',
    'patient_draped',
    'patient_undraped',
    'patient_wheels_in',
    'patient_wheels_out',
    'patient_xfer_to_bed',
    'patient_xfer_to_or_table',
  ])

  const { data: obxDataForCustomPhases } =
    useQuery<GetObservationTypeNamesForCustomPhases>(
      GET_OBSERVATION_TYPE_NAMES_FOR_CUSTOM_PHASES,
      {
        variables: {
          orgId: currentOrganization?.node.id,
        },
      }
    )

  const getObxData =
    obxDataForCustomPhases?.observationTypeNamesForCustomPhases.map((obx) => {
      return {
        id: obx?.typeId,
        name: '[EHR] ' + obx?.name,
      }
    })
  const eventTypes = apellaEventTypes?.eventTypes
    .filter((event) => supportedApellaEventTypes.has(event?.id))
    .map((event) => {
      return {
        id: event?.id,
        name: '[Apella] ' + event?.name,
      }
    })

  const customPhaseEventData = [
    ...(getObxData ? getObxData : []),
    ...eventTypes,
  ]

  return { customPhaseEventData }
}

export default useCustomPhaseEventData
