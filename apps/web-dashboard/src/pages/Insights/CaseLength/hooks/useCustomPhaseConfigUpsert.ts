import { useCallback } from 'react'

import { gql, useMutation } from '@apollo/client'

import {
  CustomPhaseConfigUpsert,
  CustomPhaseConfigUpsertVariables,
} from './__generated__'
import { GET_CUSTOM_PHASE_CONFIG } from './useCustomPhaseConfigQuery'

const UPSERT_CUSTOM_PHASE_CONFIG = gql`
  mutation CustomPhaseConfigUpsert($input: CustomPhaseConfigUpsertInput!) {
    customPhaseConfigUpsert(input: $input) {
      success
      customPhaseConfig {
        id
        startEventType
        endEventType
        name
        description
      }
    }
  }
`

const useCustomPhaseConfigUpsert = (
  onMutationCompleted: (id: string) => void
) => {
  const [upsertCustomPhaseConfig, { called: mutationStarted }] = useMutation<
    CustomPhaseConfigUpsert,
    CustomPhaseConfigUpsertVariables
  >(UPSERT_CUSTOM_PHASE_CONFIG, {
    refetchQueries: [{ query: GET_CUSTOM_PHASE_CONFIG }],
    // Wait for the refetch to complete so that when onCompleted is called, we already
    // have the new list of custom phases to use for rendering the table columns.
    awaitRefetchQueries: true,
    onCompleted: ({ customPhaseConfigUpsert }) =>
      onMutationCompleted(customPhaseConfigUpsert?.customPhaseConfig?.id ?? ''),
  })

  const doUpsertCustomPhaseConfig = useCallback(
    ({
      id,
      startEventType,
      endEventType,
      name,
      description,
    }: {
      id: string
      startEventType: string
      endEventType: string
      name: string
      description: string
    }) => {
      return upsertCustomPhaseConfig({
        variables: {
          input: {
            id,
            startEventType,
            endEventType,
            name,
            description,
          },
        },
      })
    },
    [upsertCustomPhaseConfig]
  )

  return {
    doUpsertCustomPhaseConfig,
    mutationStarted,
  }
}

export default useCustomPhaseConfigUpsert
