import { gql, useQuery } from '@apollo/client'

import { CustomPhaseConfig } from '../../types'
import { CustomPhaseConfig as CustomPhaseConfigData } from './__generated__'

export const GET_CUSTOM_PHASE_CONFIG = gql`
  query CustomPhaseConfig {
    customPhaseConfigs {
      id
      startEventType
      endEventType
      name
      description
    }
  }
`

const useCustomPhaseConfigQuery = () => {
  const { loading, data } = useQuery<CustomPhaseConfigData>(
    GET_CUSTOM_PHASE_CONFIG
  )

  const customPhaseConfigs = new Array<CustomPhaseConfig>()
  const customPhaseConfigData = data?.customPhaseConfigs ?? []
  customPhaseConfigData.forEach((phase) => {
    customPhaseConfigs.push({
      id: phase.id,
      startEventTypeId: phase.startEventType,
      endEventTypeId: phase.endEventType,
      name: phase.name,
      description: phase.description,
    })
  })

  return {
    loading,
    customPhaseConfigs,
  }
}

export default useCustomPhaseConfigQuery
