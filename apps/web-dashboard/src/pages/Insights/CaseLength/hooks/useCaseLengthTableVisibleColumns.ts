import { useUrlCleanParamAndLocalStorageState } from '@apella/hooks'
import { DisplayName } from 'src/pages/Insights/types'

const DEFAULT_VISIBLE_COLUMNS = [
  DisplayName.START_TIME,
  DisplayName.ROOM,
  DisplayName.PRIMARY_SURGEONS,
  DisplayName.PROCEDURES,
  DisplayName.ACTUAL_DURATION,
  DisplayName.SCHEDULED_DURATION,
  DisplayName.DELAY_REASON,
  DisplayName.DELAY_TYPE,
]

const useCaseLengthTableVisibleColumns = (caseLengthPrefix: string) => {
  const queryParamAndLocalStorageKey = `${caseLengthPrefix}_visibleColumns`
  const [urlAndLocalStorageState, setUrlAndLocalStorageState] =
    useUrlCleanParamAndLocalStorageState<string[]>(
      queryParamAndLocalStorageKey,
      DEFAULT_VISIBLE_COLUMNS
    )
  return [urlAndLocalStorageState, setUrlAndLocalStorageState] as const
}

export default useCaseLengthTableVisibleColumns
