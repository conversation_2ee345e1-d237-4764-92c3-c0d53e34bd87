import { QueryOrder } from '@cubejs-client/core'

import { CASES } from 'src/modules/cube/types/dataCubes'

export enum CaseLengthMeasures {
  COUNT = 'Total cases',
  ACTUAL_AVG_DURATION = 'Actual avg',
  SCHEDULED_AVG_DURATION = 'Scheduled avg',
  SCHEDULE_VARIANCE = 'Schedule variance',
}

export const caseLengthMeasuresToOrderBy: {
  [key in CaseLengthMeasures]: [string, QueryOrder]
} = {
  [CaseLengthMeasures.COUNT]: [CASES.COUNT, 'desc'],
  [CaseLengthMeasures.ACTUAL_AVG_DURATION]: [
    CASES.AVG_ACTUAL_DURATION_MINUTES,
    'desc',
  ],
  [CaseLengthMeasures.SCHEDULED_AVG_DURATION]: [
    CASES.AVG_SCHEDULED_DURATION_MINUTES,
    'desc',
  ],
  [CaseLengthMeasures.SCHEDULE_VARIANCE]: [
    CASES.AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES,
    'desc',
  ],
} as const
