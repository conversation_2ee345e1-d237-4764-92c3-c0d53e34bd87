import { useCallback, useMemo } from 'react'

import { ResultSet, TimeDimension } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'
import { capitalize, toSafeInteger } from 'lodash'
import { DateTime, Duration, DurationLikeObject } from 'luxon'

import { ChartData, ChartMeta } from '@apella/component-library'
import { CaseStatusName, PhaseType } from 'src/__generated__/globalTypes'
import { CaseGraphMode, InsightsGraph } from 'src/components/InsightsGraph'
import { DimensionDropdown } from 'src/components/InsightsGraph/DimensionDropdown'
import { useServiceLines } from 'src/components/ServiceLinesFilter'
import { useTimezone } from 'src/Contexts'
import { toTimeDimensionGranularity } from 'src/modules/cube/adapters'
import {
  CASE_PROCEDURES,
  CASE_STAFF_ANESTHESIA,
  CASE_STAFF_CIRCULATOR,
  CASE_STAFF_SCRUB_TECH,
  CASE_STAFF_SURGEONS,
  CASES,
  SERVICE_LINES,
  STAFF,
} from 'src/modules/cube/types/dataCubes'
import {
  CaseLengthMeasures,
  caseLengthMeasuresToOrderBy,
} from 'src/pages/Insights/CaseLength/caseLengthMeasures'
import {
  getProcedureIdByName,
  getProcedureNameById,
  getRoomIdByName,
  getRoomNameById,
  getServiceLineIdByName,
  getServiceLineNameById,
  getStaffIdByName,
  getStaffNameById,
} from 'src/pages/Insights/dataHelpers'
import { getPhaseDetailsFromType } from 'src/pages/Insights/phaseHelpers'
import { formatTimeDimensionDateRange } from 'src/pages/Insights/queries'
import { DayOfWeek, METRICS } from 'src/pages/Insights/types'
import { useInsightsSearchParams } from 'src/pages/Insights/useInsightsSearchParams'
import { usePhaseTypes } from 'src/pages/Insights/usePhaseTypes'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'
import {
  Dimension,
  getBucketEndDate,
  NonTimeDimensions,
  TimeDimensions,
} from 'src/utils/bucketHelpers'
import { useApellaCaseStateColor } from 'src/utils/status'
import { useSiteOptions } from 'src/utils/useSiteRoomsOptions'

/** Converts negative Duration instances to positive values */
export const durationAbs = (duration: Duration) =>
  Duration.fromMillis(Math.abs(duration.valueOf()))

const isCaseLengthMeasure = (measure: string): measure is CaseLengthMeasures =>
  Object.values(CaseLengthMeasures).map(String).includes(measure)

/**
 * Given the current way that the data is being grouped in the graph, return the property name of
 * on the Cube datum object which is acting as the grouping identifier. eg:
 * By day           => InsightsCases.actualStartTimestamp.day
 * By week          => InsightsCases.actualStartTimestamp.week
 * By month         => InsightsCases.actualStartTimestamp.month
 * By day of week   => InsightsCases.actualDayOfWeek
 * By OR            => InsightsCases.roomId
 * By surgeon       => CaseStaffSurgeonsBQ.staffId
 * By anesthesia    => CaseStaffAnesthesiaBQ.staffId
 * By circulator    => CaseStaffCirculator.staffId
 * By scrub tech    => CaseStaffScrubTech.staffId
 * By service line  => ServiceLinesBQ.id
 * By procedure     => CaseProceduresBQ.procedureId
 * @param currentGroupingDimension
 */
const getDataGroupingIdByCurrentGroupingDimension = (
  currentGroupingDimension: Dimension
): string => {
  return currentGroupingDimension in NonTimeDimensions
    ? `${getCubeJsDimensionFromNonTimeDimension(currentGroupingDimension as NonTimeDimensions)}`
    : `${CASES.ACTUAL_START_TIMESTAMP}.${currentGroupingDimension.toLowerCase()}`
}

function mapToCaseDataWithPlaceholdersForEmptyData(
  caseData: ResultSet<CaseData> | null,
  currentDimensionForGrouping: Dimension,
  daysOfWeek: DayOfWeek[] | undefined
): CaseData[] | undefined {
  /**
   * A previous implementation of this used a .rawData() method instead of .tablePivot()
   * .rawData() only returns data which isn't "empty" (eg: only returns data for when there are
   * cases matching these filters), whereas .tablePivot({ fillMissingDates: true }) will return
   * placeholder values for "empty" dates.
   * For instance, if some surgeon performs case every weekday / not weekends, the date range
   * filter is for the entire calendar week (ie: Sunday through Saturday), and the days-of-week
   * filter is for weekdays, this is what each function will return:
   * .rawData(): [
   *     {caseForMonday},
   *     {caseForTuesday},
   *     {caseForWednesday},
   *     {caseForThursday},
   *     {caseForFriday}
   * ]
   * .tablePivot(): [
   *     {emptyCase}, // Sunday
   *     {caseForMonday},
   *     {caseForTuesday},
   *     {caseForWednesday},
   *     {caseForThursday},
   *     {caseForFriday}
   *     {emptyCase}, // Saturday
   * ]
   */

  const caseDataForGraph = caseData?.tablePivot({ fillMissingDates: true })

  if (!caseDataForGraph) {
    return []
  }

  const currentDaysOfWeekFilter = daysOfWeek
    ? new Set(daysOfWeek.map((d) => d.toLowerCase()))
    : null
  const datumKeyForGrouping = getDataGroupingIdByCurrentGroupingDimension(
    currentDimensionForGrouping
  )
  const caseDataWithPlaceholdersForEmptyData: CaseData[] = []

  caseDataForGraph.forEach((caseDatum) => {
    const datumValueForGrouping = caseDatum[datumKeyForGrouping]

    // regarding the falsy-check: a race-condition exists after the grouping is changed, in which
    // the data from Cube is still grouped by the previous dimension and thus will not be able to
    // be grouped until the data from Cube is re-fetched
    if (datumValueForGrouping && typeof datumValueForGrouping === 'string') {
      if (currentDimensionForGrouping === TimeDimensions.DAY) {
        // apply additional filtering for weekdays, to determine if datum should be shown
        const dateForDatumValueForGrouping = DateTime.fromISO(
          datumValueForGrouping
        )
        const dayOfWeekForDatum = dateForDatumValueForGrouping
          .toLocaleString({ weekday: 'long' })
          .toLowerCase()

        // even if "Entire week" option is selected, the daysOfWeek / currentDaysOfWeekFilter will
        // contain a list of all 7 weekdays, so currentDaysOfWeekFilter should never be null,
        // however having currentDaysOfWeekFilter be null would probably be more appropriate and
        // expected, so in case this changes in the future, this won't break
        if (
          !currentDaysOfWeekFilter ||
          currentDaysOfWeekFilter.has(dayOfWeekForDatum)
        ) {
          caseDatum[CASES.ACTUAL_START_TIMESTAMP] =
            `${caseDatum[datumKeyForGrouping]}`
          caseDataWithPlaceholdersForEmptyData.push(caseDatum)
        }
      } else if (
        currentDimensionForGrouping === TimeDimensions.WEEK ||
        currentDimensionForGrouping === TimeDimensions.MONTH
      ) {
        caseDatum[CASES.ACTUAL_START_TIMESTAMP] =
          `${caseDatum[datumKeyForGrouping]}`
        caseDataWithPlaceholdersForEmptyData.push(caseDatum)
      } else {
        // intentionally omitting a case for NonTimeDimensions.DAYOFWEEK, as it would take a
        // different approach for displaying placeholder data.  Similar to grouping by other
        // NonTimeDimensions (eg: .SURGEON), Cube does not return 'empty' data rows for .DAYOFWEEK
        // (even though it's conceptually similar to TimeDimensions.DAY), nor other
        // NonTimeDimensions
        caseDataWithPlaceholdersForEmptyData.push(caseDatum)
      }
    }
  })

  return caseDataWithPlaceholdersForEmptyData
}

type CaseData = Partial<{
  [CASES.AVG_SCHEDULED_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.COUNT]: string
  [CASES.AVG_ACTUAL_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.ACTUAL_START_TIMESTAMP]: string
  [CASES.ACTUAL_DAY_OF_WEEK]: string
  [CASES.AVG_PRE_OP_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.AVG_INTRA_OP_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.AVG_POST_OP_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.ROOM_ID]: string
  [CASE_STAFF_SURGEONS.STAFF_ID]: string
  [CASE_STAFF_ANESTHESIA.STAFF_ID]: string
  [CASE_STAFF_CIRCULATOR.STAFF_ID]: string
  [CASE_STAFF_SCRUB_TECH.STAFF_ID]: string
  [CASE_PROCEDURES.PROCEDURE_ID]: string
  [SERVICE_LINES.ID]: string
  [STAFF.FIRST_NAME]: string
  [STAFF.LAST_NAME]: string
}>

const getCubeJsDimensionFromNonTimeDimension = (
  nonTimeDimension: NonTimeDimensions
): string => {
  switch (nonTimeDimension) {
    case NonTimeDimensions.DAYOFWEEK:
      return CASES.ACTUAL_DAY_OF_WEEK
    case NonTimeDimensions.OR:
      return CASES.ROOM_ID
    case NonTimeDimensions.SURGEON:
      return CASE_STAFF_SURGEONS.STAFF_ID
    case NonTimeDimensions.ANESTHESIA:
      return CASE_STAFF_ANESTHESIA.STAFF_ID
    case NonTimeDimensions.CIRCULATOR:
      return CASE_STAFF_CIRCULATOR.STAFF_ID
    case NonTimeDimensions.SCRUB_TECH:
      return CASE_STAFF_SCRUB_TECH.STAFF_ID
    case NonTimeDimensions.SERVICE_LINE:
      return SERVICE_LINES.ID
    case NonTimeDimensions.PROCEDURE:
      return CASE_PROCEDURES.PROCEDURE_ID
  }
}

const getDimensionFromResult = (
  data: CaseData,
  timezone: string,
  staffNameById: Map<string, string>,
  anesthesiaStaffNameById: Map<string, string>,
  circulatorStaffNameById: Map<string, string>,
  scrubTechStaffNameById: Map<string, string>,
  serviceLineNameById: Map<string, string>,
  procedureNameById: Map<string, string>,
  roomNameById?: Map<string, string>
): string => {
  if (data[CASES.ACTUAL_START_TIMESTAMP]) {
    return DateTime.fromISO(data[CASES.ACTUAL_START_TIMESTAMP], {
      zone: timezone,
    }).toISO()
  }

  return (
    data[CASES.ACTUAL_DAY_OF_WEEK] ||
    (data[CASES.ROOM_ID] && roomNameById?.get(data[CASES.ROOM_ID])) ||
    (data[CASE_STAFF_SURGEONS.STAFF_ID] &&
      staffNameById.get(data[CASE_STAFF_SURGEONS.STAFF_ID] ?? '')) ||
    (data[CASE_STAFF_ANESTHESIA.STAFF_ID] &&
      anesthesiaStaffNameById.get(
        data[CASE_STAFF_ANESTHESIA.STAFF_ID] ?? ''
      )) ||
    (data[CASE_STAFF_CIRCULATOR.STAFF_ID] &&
      circulatorStaffNameById.get(
        data[CASE_STAFF_CIRCULATOR.STAFF_ID] ?? ''
      )) ||
    (data[CASE_STAFF_SCRUB_TECH.STAFF_ID] &&
      scrubTechStaffNameById.get(data[CASE_STAFF_SCRUB_TECH.STAFF_ID] ?? '')) ||
    (data[SERVICE_LINES.ID] &&
      serviceLineNameById.get(data[SERVICE_LINES.ID] ?? '')) ||
    (data[CASE_PROCEDURES.PROCEDURE_ID] &&
      procedureNameById.get(data[CASE_PROCEDURES.PROCEDURE_ID] ?? '')) ||
    ''
  )
}

const CaseLengthGraph = (): React.JSX.Element => {
  const { phaseTypes } = usePhaseTypes()
  const { timezone } = useTimezone()
  const { serviceLines } = useServiceLines()
  const {
    minTime,
    maxTime,
    siteIds,
    daysOfWeek,
    dimension,
    graphSort,
    graphStackGrouping,

    staffWithCount,
    anesthesiaStaffWithCount,
    proceduresWithCount,
    circulatorStaffWithCount,
    scrubTechStaffWithCount,

    localOnChangeDateRanges,
    onChangeStaff,
    onChangeAnesthesia,
    onChangeCirculator,
    onChangeScrubTech,
    onChangeProcedure,
    onChangeServiceLines,
    onChangeRooms,
    onChangeDaysOfWeek,
    onChangeDimension,
    onChangeGraphOrderBy,
    onChangeGraphStackGrouping,
    cubeParams,
  } = useInsightsSearchParams(METRICS.CASE)
  const { sites } = useSiteOptions()

  const staffNameById = useMemo(
    () => getStaffNameById(staffWithCount),
    [staffWithCount]
  )
  const staffIdByName = useMemo(
    () => getStaffIdByName(staffWithCount),
    [staffWithCount]
  )
  const anesthesiaStaffNameById = useMemo(
    () => getStaffNameById(anesthesiaStaffWithCount),
    [anesthesiaStaffWithCount]
  )
  const anesthesiaStaffIdByName = useMemo(
    () => getStaffIdByName(anesthesiaStaffWithCount),
    [anesthesiaStaffWithCount]
  )
  const circulatorStaffNameById = useMemo(
    () => getStaffNameById(circulatorStaffWithCount),
    [circulatorStaffWithCount]
  )
  const circulatorStaffIdByName = useMemo(
    () => getStaffIdByName(circulatorStaffWithCount),
    [circulatorStaffWithCount]
  )
  const scrubTechStaffNameById = useMemo(
    () => getStaffNameById(scrubTechStaffWithCount),
    [scrubTechStaffWithCount]
  )
  const scrubTechStaffIdByName = useMemo(
    () => getStaffIdByName(scrubTechStaffWithCount),
    [scrubTechStaffWithCount]
  )

  const roomNameById = useMemo(
    () => getRoomNameById(sites, siteIds),
    [sites, siteIds]
  )
  const roomIdByName = useMemo(() => {
    return getRoomIdByName(sites, siteIds)
  }, [sites, siteIds])

  const procedureNameById = useMemo(
    () => getProcedureNameById(proceduresWithCount),
    [proceduresWithCount]
  )
  const procedureIdByName = useMemo(
    () => getProcedureIdByName(proceduresWithCount),
    [proceduresWithCount]
  )
  const serviceLineNameById = useMemo(
    () => getServiceLineNameById(serviceLines),
    [serviceLines]
  )
  const serviceLineIdsByName = useMemo(
    () => getServiceLineIdByName(serviceLines),
    [serviceLines]
  )

  const timeDimension = (() => {
    const timeDimension: TimeDimension = {
      dimension: CASES.ACTUAL_START_TIMESTAMP,
      dateRange: formatTimeDimensionDateRange([minTime, maxTime], timezone),
    }
    if (dimension in TimeDimensions) {
      timeDimension.granularity = toTimeDimensionGranularity(
        dimension as TimeDimensions
      )
    }
    return timeDimension
  })()

  const dimensions = (() => {
    const dimensions = []
    if (dimension in NonTimeDimensions) {
      const nonTimeDimension = getCubeJsDimensionFromNonTimeDimension(
        dimension as NonTimeDimensions
      )
      dimensions.push(nonTimeDimension)
    }
    return dimensions
  })()

  const order = useMemo(() => {
    if (graphStackGrouping === CaseGraphMode.Volume)
      return [caseLengthMeasuresToOrderBy[CaseLengthMeasures.COUNT]]

    if (isCaseLengthMeasure(graphSort)) {
      return [caseLengthMeasuresToOrderBy[graphSort]]
    }

    return [caseLengthMeasuresToOrderBy[CaseLengthMeasures.COUNT]]
  }, [graphSort, graphStackGrouping])

  // The overall case measures are calculated as a separate query from the child phase data for
  // performance benefit.
  const { resultSet: caseData, isLoading } = useCubeQuery<CaseData>({
    ...cubeParams,
    measures: [
      CASES.COUNT,
      CASES.AVG_ACTUAL_DURATION_MINUTES,
      CASES.AVG_SCHEDULED_DURATION_MINUTES,
      CASES.AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES,
      CASES.AVG_PRE_OP_DURATION_MINUTES,
      CASES.AVG_INTRA_OP_DURATION_MINUTES,
      CASES.AVG_POST_OP_DURATION_MINUTES,
    ],
    timeDimensions: [timeDimension],
    dimensions: [...dimensions],
    order: order,
  })

  const getChildDurationDisplay = useCallback(
    (phaseType: PhaseType) =>
      `${capitalize(getPhaseDetailsFromType(phaseTypes)(phaseType).title)} avg`,
    [phaseTypes]
  )

  const phaseDetails = getPhaseDetailsFromType(phaseTypes)(PhaseType.CASE)

  const chartData = useMemo(() => {
    // Convert the data to chart data format
    const chartData: ChartData[] = []

    const caseDataForGraph = mapToCaseDataWithPlaceholdersForEmptyData(
      caseData,
      dimension,
      daysOfWeek
    )

    caseDataForGraph?.forEach((data) => {
      const baseData = {
        dimension: getDimensionFromResult(
          data,
          timezone,
          staffNameById,
          anesthesiaStaffNameById,
          circulatorStaffNameById,
          scrubTechStaffNameById,
          serviceLineNameById,
          procedureNameById,
          roomNameById
        ),
      }

      const countData = {
        ...baseData,
        identifier: CaseLengthMeasures.COUNT,
        value: toSafeInteger(data[CASES.COUNT]),
      }

      const caseAverageActualDuration = {
        ...baseData,
        identifier: CaseLengthMeasures.ACTUAL_AVG_DURATION,
        value: data[CASES.AVG_ACTUAL_DURATION_MINUTES]
          ? durationFromMinutes(data[CASES.AVG_ACTUAL_DURATION_MINUTES])
          : 0,
      }

      const caseAverageScheduledDuration = {
        ...baseData,
        identifier: CaseLengthMeasures.SCHEDULED_AVG_DURATION,
        value: data[CASES.AVG_SCHEDULED_DURATION_MINUTES]
          ? durationFromMinutes(data[CASES.AVG_SCHEDULED_DURATION_MINUTES])
          : 0,
      }

      const caseAverageActualMinusScheduledDuration = {
        ...baseData,
        identifier: CaseLengthMeasures.SCHEDULE_VARIANCE,
        value: data[CASES.AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES]
          ? durationAbs(
              durationFromMinutes(
                data[CASES.AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES]
              )
            )
          : 0,
      }

      const childPhases: ChartData[] = [
        {
          ...baseData,
          identifier: getChildDurationDisplay(PhaseType.PRE_OPERATIVE),
          value: data[CASES.AVG_PRE_OP_DURATION_MINUTES]
            ? durationFromMinutes(data[CASES.AVG_PRE_OP_DURATION_MINUTES])
            : 0,
        },
        {
          ...baseData,
          identifier: getChildDurationDisplay(PhaseType.INTRA_OPERATIVE),
          value: data[CASES.AVG_INTRA_OP_DURATION_MINUTES]
            ? durationFromMinutes(data[CASES.AVG_INTRA_OP_DURATION_MINUTES])
            : 0,
        },
        {
          ...baseData,
          identifier: getChildDurationDisplay(PhaseType.POST_OPERATIVE),
          value: data[CASES.AVG_POST_OP_DURATION_MINUTES]
            ? durationFromMinutes(data[CASES.AVG_POST_OP_DURATION_MINUTES])
            : 0,
        },
      ]

      chartData.push(countData)

      if (graphStackGrouping === CaseGraphMode.Breakdown) {
        chartData.push(...childPhases)
      }

      if (graphStackGrouping !== CaseGraphMode.Volume) {
        chartData.push(
          caseAverageActualDuration,
          caseAverageScheduledDuration,
          caseAverageActualMinusScheduledDuration
        )
      }
    })

    return chartData
  }, [
    caseData,
    timezone,
    staffNameById,
    anesthesiaStaffNameById,
    circulatorStaffNameById,
    scrubTechStaffNameById,
    serviceLineNameById,
    procedureNameById,
    roomNameById,
    getChildDurationDisplay,
    graphStackGrouping,
    dimension,
    daysOfWeek,
  ])

  // When clicking on a graph group, change the desired filters.
  // Transform the bar group label that the component passes back to the ID required for filtering.
  const onClickBarGroup = useCallback(
    (dimensionValue: string) => {
      if (dimension in TimeDimensions) {
        const startDate = new Date(dimensionValue)
        const endDate = getBucketEndDate(startDate, dimension as TimeDimensions)
        localOnChangeDateRanges([startDate, endDate])
      } else if (dimension == NonTimeDimensions.DAYOFWEEK) {
        onChangeDaysOfWeek([dimensionValue.toLocaleLowerCase()] as DayOfWeek[])
        localOnChangeDateRanges([new Date(minTime), new Date(maxTime)])
      } else if (dimension === NonTimeDimensions.OR) {
        onChangeRooms(
          roomIdByName.get(dimensionValue)
            ? [roomIdByName.get(dimensionValue)!]
            : []
        )
      } else if (dimension === NonTimeDimensions.SURGEON) {
        const staffName = staffIdByName.get(dimensionValue)
        onChangeStaff(staffName ? [staffName] : undefined)
      } else if (dimension === NonTimeDimensions.ANESTHESIA) {
        const staffName = anesthesiaStaffIdByName.get(dimensionValue)
        onChangeAnesthesia(staffName ? [staffName] : undefined)
      } else if (dimension === NonTimeDimensions.CIRCULATOR) {
        const staffName = circulatorStaffIdByName.get(dimensionValue)
        onChangeCirculator(staffName ? [staffName] : undefined)
      } else if (dimension === NonTimeDimensions.SCRUB_TECH) {
        const staffName = scrubTechStaffIdByName.get(dimensionValue)
        onChangeScrubTech(staffName ? [staffName] : undefined)
      } else if (dimension === NonTimeDimensions.PROCEDURE) {
        onChangeProcedure(procedureIdByName.get(dimensionValue))
      } else if (dimension === NonTimeDimensions.SERVICE_LINE) {
        onChangeServiceLines(serviceLineIdsByName.get(dimensionValue))
      }

      if (dimension in NonTimeDimensions) {
        // For non-time dimensions, filter by dimension then refresh to a by-time graph
        // (triggering onChangeDateRanges will update the bucket accordingly: i.e Day if last month, Week if 2 months, etc.)
        localOnChangeDateRanges([new Date(minTime), new Date(maxTime)])
      }
    },
    [
      dimension,
      localOnChangeDateRanges,
      onChangeDaysOfWeek,
      minTime,
      maxTime,
      onChangeRooms,
      roomIdByName,
      staffIdByName,
      onChangeStaff,
      anesthesiaStaffIdByName,
      circulatorStaffIdByName,
      scrubTechStaffIdByName,
      onChangeAnesthesia,
      onChangeCirculator,
      onChangeScrubTech,
      onChangeProcedure,
      procedureIdByName,
      onChangeServiceLines,
      serviceLineIdsByName,
    ]
  )

  const barColors = useApellaCaseStateColor()

  const chartMetaAndStackGroupingMapping: {
    [key: string]: ChartMeta
  } = {
    [CaseGraphMode.Breakdown]: {
      [getChildDurationDisplay(PhaseType.PRE_OPERATIVE)]: {
        ordinal: 0,
        color: barColors[CaseStatusName.PREP],
      },
      [getChildDurationDisplay(PhaseType.INTRA_OPERATIVE)]: {
        ordinal: 1,
        color: barColors[CaseStatusName.SURGERY],
      },
      [getChildDurationDisplay(PhaseType.POST_OPERATIVE)]: {
        ordinal: 2,
        color: barColors[CaseStatusName.WRAP_UP],
      },
      [CaseLengthMeasures.ACTUAL_AVG_DURATION]: {
        ordinal: 3,
        hide: true,
      },
      [CaseLengthMeasures.SCHEDULED_AVG_DURATION]: {
        ordinal: 4,
        hide: true,
      },
      [CaseLengthMeasures.SCHEDULE_VARIANCE]: {
        ordinal: 5,
        hide: true,
      },
      [CaseLengthMeasures.COUNT]: {
        ordinal: 6,
        hide: true,
      },
    },
    [CaseGraphMode.ScheduleVsActual]: {
      [CaseLengthMeasures.ACTUAL_AVG_DURATION]: {
        stack: '0',
        ordinal: 0,
      },
      [CaseLengthMeasures.SCHEDULED_AVG_DURATION]: {
        ordinal: 1,
      },
      [CaseLengthMeasures.SCHEDULE_VARIANCE]: {
        ordinal: 2,
        hide: true,
      },
      [CaseLengthMeasures.COUNT]: {
        ordinal: 3,
        hide: true,
      },
    },
    [CaseGraphMode.Volume]: {
      [CaseLengthMeasures.COUNT]: {
        ordinal: 4,
      },
    },
  }

  const groupStackSelection = graphStackGrouping ?? CaseGraphMode.Breakdown

  return (
    <InsightsGraph
      dimension={dimension}
      data={chartData}
      meta={
        chartMetaAndStackGroupingMapping[groupStackSelection] ??
        chartMetaAndStackGroupingMapping[CaseGraphMode.Breakdown]
      }
      isLoading={isLoading}
      minTime={minTime}
      maxTime={maxTime}
      title={phaseDetails.title}
      timezone={timezone}
      onClickBarGroup={onClickBarGroup}
      graphSort={graphSort}
      onChangeGraphSort={onChangeGraphOrderBy}
      measures={Object.values(CaseLengthMeasures)}
      graphStackGrouping={groupStackSelection}
      graphStackGroupingOptions={[...Object.values(CaseGraphMode)]}
      onChangeGraphStackGrouping={onChangeGraphStackGrouping}
      valueAxisScale={
        groupStackSelection === `${CaseGraphMode.Volume}` ? 'linear' : undefined
      }
      allowSort={groupStackSelection !== CaseGraphMode.Volume.toString()}
    >
      <DimensionDropdown
        dimension={dimension}
        onChangeDimension={onChangeDimension}
        showCirculatorAndScrubTechOptions={true}
      />
    </InsightsGraph>
  )
}

export default CaseLengthGraph
