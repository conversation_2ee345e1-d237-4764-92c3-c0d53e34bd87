import { ReactElement, useCallback, useMemo, useState } from 'react'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import { Query, QueryOrder } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime, Duration } from 'luxon'

import {
  CsvExportButton,
  Direction,
  formatDuration,
  OrderBy,
  PaginatedTableColumn,
  remSpacing,
  Tile,
  ApellaDateTimeFormats,
  Button,
  PlusCircle,
  Span2,
  ToastMessage,
} from '@apella/component-library'
import { PatientClass, PhaseType } from 'src/__generated__/globalTypes'
import { ColumnsPicker } from 'src/components/ColumnsPicker'
import { DashComponent } from 'src/components/DashComponent'
import { CaseGraphMode } from 'src/components/InsightsGraph'
import { RemotePaginatedTableWithVideoBlade } from 'src/components/RemotePaginatedTableWithVideoBlade'
import { useTimezone } from 'src/Contexts'
import {
  CASE_CLASSIFICATION_TYPES,
  CASES,
  REPORT_CASE,
  ROOMS,
  SERVICE_LINES,
  SITES,
} from 'src/modules/cube/types/dataCubes'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { useCaseIdToClinicalDataMap } from 'src/modules/insights/hooks/useClinicalData'
import { useCaseLengthTableExport } from 'src/pages/Insights/CaseLength/CaseLengthTableExport'
import {
  displayCustomerCaseId,
  displayIsAddOn,
  generateListUiDisplay,
} from 'src/pages/Insights/dataHelpers'
import { roughlyGreaterThanAYearDifference } from 'src/pages/Insights/dateTimeHelpers'
import { getPhaseDetailsFromType } from 'src/pages/Insights/phaseHelpers'
import SiteRoomCell from 'src/pages/Insights/SiteRoomCell'
import {
  CustomPhaseConfig,
  Description,
  DisplayName,
  METRICS,
  VideoBladeTableData,
} from 'src/pages/Insights/types'
import { getPatientClassText } from 'src/pages/Schedule/CaseDetails'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'
import { logger } from 'src/utils/exceptionLogging'
import useSortOrderConversion from 'src/utils/sortOrderHelpers'

import { useInsightsSearchParams } from '../useInsightsSearchParams'
import { usePagingState } from '../usePagingState'
import { usePhaseTypes } from '../usePhaseTypes'
import { CustomPhaseDialog } from './CustomPhaseDialog'
import { CustomPhaseTableHeaderIcon } from './CustomPhaseTableHeaderIcon'
import useCaseLengthTableVisibleColumns from './hooks/useCaseLengthTableVisibleColumns'
import { useCustomPhaseConfigDelete } from './hooks/useCustomPhaseConfigDelete'
import useCustomPhaseConfigQuery from './hooks/useCustomPhaseConfigQuery'
import useCustomPhaseEventData from './hooks/useCustomPhaseEventData'

type RawTableData = Partial<{
  [CASES.ACTUAL_START_TIMESTAMP]: string
  [CASES.ACTUAL_DURATION_MINUTES]: number
  [CASES.PRE_OP_DURATION_MINUTES]: number
  [CASES.INTRA_OP_DURATION_MINUTES]: number
  [CASES.POST_OP_DURATION_MINUTES]: number
  [CASES.CASE_ID]: string
  [CASES.SCHEDULED_DURATION_MINUTES]: number
  [SITES.NAME]: string
  [ROOMS.NAME]: string
  [ROOMS.ID]: string
  [CASES.CUSTOMER_CASE_ID]: string
  [CASES.IS_ADD_ON]: boolean
  [CASES.PATIENT_CLASSIFICATION_ID]: string
  [CASE_CLASSIFICATION_TYPES.NAME]: string
  [SERVICE_LINES.NAME]: string
  [CASES.CASE_PHASE_ID]: string
  [CASES.ANESTHESIA_PREP_DURATION_MINUTES]: number
  [CASES.SURGERY_PREP_DURATION_MINUTES]: number
  [REPORT_CASE.DELAY_COMMENTS]: string
  [REPORT_CASE.DELAY_REASON]: string
  [REPORT_CASE.DELAY_TYPE]: string
  [REPORT_CASE.DELAY_DURATION_SECONDS]: number
}> & {
  // Allow custom phase columns. It's impractical to include the full list here.
  [key: string]: number
}

export interface TableData extends VideoBladeTableData {
  actualDuration: Duration | undefined
  anesthesiaNames: string | ReactElement | React.JSX.Element
  anesthesiaPrepDuration: Duration | undefined
  caseClassificationName: string | ReactElement | React.JSX.Element
  customPhaseDurations: Map<string, number | undefined>
  intraOpDuration: Duration | undefined
  isAddOn: string | ReactElement | React.JSX.Element
  patientClass: string
  postOpDuration: Duration | undefined
  preOpDuration: Duration | undefined
  procedureNames: string | ReactElement | React.JSX.Element
  scheduledDuration: Duration | undefined
  serviceLineName: string
  siteName: string
  surgeonNames: string | ReactElement | React.JSX.Element
  surgeryPrepDuration: Duration | undefined
}

const mandatoryColumnNames = [DisplayName.CUSTOMER_CASE_ID].map((name) =>
  name.toString()
)
const CaseLengthTable = (): React.JSX.Element => {
  const theme = useTheme()
  const { timezone } = useTimezone()

  const { loading: phaseTypesLoading, phaseTypes } = usePhaseTypes()

  const {
    paramPrefix: CaseLengthPrefix,
    loggingState,
    minTime,
    maxTime,
    graphStackGrouping,
    cubeParams,
  } = useInsightsSearchParams(METRICS.CASE)

  const [isOpenModal, setIsOpenModal] = useState(false)
  const [editCustomConfig, setEditCustomConfig] = useState('')

  const { customPhaseModalEnabled, delayDataEnabled } =
    useFlags<WebDashboardFeatureFlagSet>()
  const { customPhaseEventData } = useCustomPhaseEventData()
  const { customPhaseConfigs } = useCustomPhaseConfigQuery()

  const { doDeleteCustomPhaseConfig } = useCustomPhaseConfigDelete()
  const eventsLogger = useAnalyticsEventLogger()

  const ORDER_BY = `${CaseLengthPrefix}_orderBy`
  const DEFAULT_ORDER_BY_STATE: OrderBy[] | undefined = useMemo(
    () => [
      {
        sort: CASES.ACTUAL_START_TIMESTAMP,
        direction: Direction.DESC,
      },
    ],
    []
  )

  // State
  const [orderByState, setOrderByState] = useSortOrderConversion(
    ORDER_BY,
    DEFAULT_ORDER_BY_STATE
  )

  // Handlers
  const onChangeSort = useCallback(
    (sortOrder: OrderBy[] | undefined) => {
      const newState = {
        ...loggingState,
        orderBy: sortOrder,
      }
      setOrderByState(sortOrder)
      graphStackGrouping !== CaseGraphMode.Volume
        ? eventsLogger(EVENTS.SORT_CASE_LENGTH_TABLE, newState)
        : eventsLogger(EVENTS.SORT_CASE_VOLUME_TABLE, newState)
    },
    [setOrderByState, graphStackGrouping, eventsLogger, loggingState]
  )

  const generateCustomPhaseColumn = useMemo(
    () => (customPhase: CustomPhaseConfig) => {
      const key = `CustomPhases.${customPhase.startEventTypeId}_${customPhase.endEventTypeId}`
      const selector = (rowData: TableData) => {
        const customPhaseDuration = rowData.customPhaseDurations.get(key)

        // Custom phase can have duration of undefined or 0, both are valid.
        return customPhaseDuration || customPhaseDuration === 0
          ? durationFromMinutes(customPhaseDuration)
          : undefined
      }

      return {
        key,
        name: customPhase.name,
        description: customPhase.description,
        selector,
        sortAttribute: key,
        formatter: (duration: Duration) => {
          return duration ? formatDuration(duration) : <DashComponent />
        },
      }
    },
    []
  )

  const customPhaseColumns = useMemo(() => {
    const handleDeletedColumn = async (id: string) => {
      try {
        await doDeleteCustomPhaseConfig({
          id: id,
        })
        toast.error(
          <ToastMessage title="Custom phase deleted" message="" theme={theme} />
        )
      } catch (error) {
        logger.warn('Failed to delete custom phases:', { error: String(error) })
        toast.error(
          <ToastMessage
            title="Failed to delete custom phase"
            message="An error occurred. Please try again or contact support for assistance."
            theme={theme}
          />
        )
      }
    }

    return (customPhaseModalEnabled ? customPhaseConfigs : []).map(
      (customPhaseColumn) => ({
        ...generateCustomPhaseColumn(customPhaseColumn),
        customPhase: true,
        description: (
          <CustomPhaseTableHeaderIcon
            isModalOpen={isOpenModal}
            setIsOpenModal={setIsOpenModal}
            onDelete={handleDeletedColumn}
            id={customPhaseColumn.id}
            setEditCustomConfig={setEditCustomConfig}
          />
        ),
      })
    )
  }, [
    customPhaseModalEnabled,
    customPhaseConfigs,
    generateCustomPhaseColumn,
    isOpenModal,
    doDeleteCustomPhaseConfig,
    theme,
  ])

  const generateChildPhaseColumn = useMemo(
    () =>
      (
        fieldName: keyof TableData,
        sortAttribute: CASES,
        phaseType?: PhaseType,
        syntheticPhaseType?: {
          type: string
          title: string
          description: string
        }
      ) => ({
        key: phaseType ?? syntheticPhaseType?.type,
        name: phaseType
          ? getPhaseDetailsFromType(phaseTypes)(phaseType).title
          : (syntheticPhaseType?.title ?? ''),
        description: phaseType
          ? getPhaseDetailsFromType(phaseTypes)(phaseType).description
          : (syntheticPhaseType?.description ?? ''),
        selector: (rowData: TableData) => rowData[fieldName],
        sortAttribute: sortAttribute,
        formatter: (duration: Duration) =>
          duration ? formatDuration(duration) : <DashComponent />,
      }),
    [phaseTypes]
  )

  const childPhaseColumns = useMemo(() => {
    const columns = [
      generateChildPhaseColumn(
        'preOpDuration',
        CASES.PRE_OP_DURATION_MINUTES,
        PhaseType.PRE_OPERATIVE
      ),
      generateChildPhaseColumn(
        'intraOpDuration',
        CASES.INTRA_OP_DURATION_MINUTES,
        PhaseType.INTRA_OPERATIVE
      ),
      generateChildPhaseColumn(
        'postOpDuration',
        CASES.POST_OP_DURATION_MINUTES,
        PhaseType.POST_OPERATIVE
      ),
    ]
    columns.push(
      generateChildPhaseColumn(
        'anesthesiaPrepDuration',
        CASES.ANESTHESIA_PREP_DURATION_MINUTES,
        undefined,
        {
          type: 'ANESTHESIA_PREP',
          title: DisplayName.PREP_ANESTHESIA,
          description:
            'The portion of prep that is driven by the Anesthesia team. It begins with patient wheels in and ends with either intubation or the final anesthesia drape being removed (whichever is later).',
        }
      ),
      generateChildPhaseColumn(
        'surgeryPrepDuration',
        CASES.SURGERY_PREP_DURATION_MINUTES,
        undefined,
        {
          type: 'SURGERY_PREP',
          title: DisplayName.PREP_SURGERY,
          description:
            'The portion of prep that is driven by the Surgical team. It begins at intubation or the final anesthesia drape removal and ends in patient procedural drape.',
        }
      )
    )
    return columns
  }, [generateChildPhaseColumn])

  const columns: PaginatedTableColumn<TableData>[] = useMemo(() => {
    let columnsToShow: PaginatedTableColumn<TableData>[] = [
      {
        name: DisplayName.CUSTOMER_CASE_ID,
        selector: 'customerCaseId',
      },
      {
        name: DisplayName.START_TIME,
        selector: 'actualStartTime',
        sortAttribute: CASES.ACTUAL_START_TIMESTAMP,
        formatter: (startTime: DateTime) =>
          startTime.toLocaleString(ApellaDateTimeFormats.DATETIME_WITH_WEEKDAY),
      },
      {
        name: DisplayName.ROOM,
        selector: (rowData: TableData) =>
          `${rowData.siteName} ${rowData.roomName}`,
        sortAttribute: ROOMS.NAME,
        formatter: (
          rowColumn: TableData,
          rowData: { siteName: string; roomName: string }
        ) => <SiteRoomCell site={rowData.siteName} room={rowData.roomName} />,
      },
      {
        name: DisplayName.PRIMARY_SURGEONS,
        selector: 'surgeonNames',
      },
      {
        name: DisplayName.ANESTHESIA,
        selector: 'anesthesiaNames',
      },
      {
        name: DisplayName.CIRCULATORS,
        selector: 'circulatorNames',
      },
      {
        name: DisplayName.SCRUB_TECHS,
        selector: 'scrubTechNames',
      },
      {
        name: DisplayName.SERVICE_LINE,
        selector: 'serviceLineName',
      },
      {
        name: DisplayName.PROCEDURES,
        selector: 'procedureNames',
      },
      {
        name: DisplayName.CASE_CLASSIFICATION,
        selector: 'caseClassificationName',
      },
      {
        name: DisplayName.PATIENT_CLASS,
        selector: 'patientClass',
        formatter: (patientClass: PatientClass) =>
          getPatientClassText(patientClass) ?? 'Other',
      },
      {
        name: DisplayName.ADD_ON,
        selector: 'isAddOn',
      },
    ]
    delayDataEnabled &&
      columnsToShow.push(
        {
          name: DisplayName.DELAY_COMMENTS,
          selector: 'delayComments',
          formatter: (delayComments: string) =>
            delayComments ? delayComments : <DashComponent />,
        },
        {
          name: DisplayName.DELAY_DURATION,
          selector: 'delayDurationMinutes',
          formatter: (duration: Duration) =>
            duration ? formatDuration(duration) : <DashComponent />,
        }
      )
    columnsToShow = columnsToShow.concat(childPhaseColumns)

    columnsToShow.push(
      {
        name: DisplayName.ACTUAL_DURATION,
        selector: 'actualDuration',
        sortAttribute: CASES.ACTUAL_DURATION_MINUTES,
        formatter: (duration: Duration) =>
          duration ? formatDuration(duration) : <DashComponent />,
        description: Description.ACTUAL_DURATION,
      },
      {
        name: DisplayName.SCHEDULED_DURATION,
        selector: 'scheduledDuration',
        sortAttribute: CASES.SCHEDULED_DURATION_MINUTES,
        formatter: (duration: Duration) =>
          duration ? formatDuration(duration) : <DashComponent />,
        description: Description.SCHEDULED_DURATION,
      }
    )
    delayDataEnabled &&
      columnsToShow.push(
        {
          name: DisplayName.DELAY_TYPE,
          selector: 'delayType',
          formatter: (delayType: string) =>
            delayType ? delayType : <DashComponent />,
        },
        {
          name: DisplayName.DELAY_REASON,
          selector: 'delayReason',
          formatter: (delayReason: string) =>
            delayReason ? delayReason : <DashComponent />,
        }
      )
    columnsToShow = columnsToShow.concat(customPhaseColumns)
    return columnsToShow
  }, [childPhaseColumns, customPhaseColumns, delayDataEnabled])

  const sortKeys = columns.map((column) => column.sortAttribute)

  const mandatoryColumns = useMemo(
    () => columns.filter((col) => mandatoryColumnNames.includes(col.name)),
    [columns]
  )

  const selectableColumns = useMemo(
    () => columns.filter((col) => !mandatoryColumnNames.includes(col.name)),
    [columns]
  )
  const [selectedColumnIds, setSelectedColumnIds] =
    useCaseLengthTableVisibleColumns(CaseLengthPrefix)

  const handleSelectedColumnsChange = (colNames?: string[]) => {
    const validColumns: string[] = []
    const columnNames = new Set(columns.map((col) => col.name))
    colNames?.forEach((col) => {
      if (columnNames.has(col)) {
        validColumns.push(col)
      } else {
        logger.warn('[Insights page > Cases tab > table] invalid column name', {
          col,
        })
      }
    })

    setSelectedColumnIds(validColumns)

    // Log the names of visible columns, so we can track
    // which columns users want to see in addition to defaults.
    eventsLogger(EVENTS.CASE_LENGTH_COLUMN_VISIBILITY_CHANGED, {
      selectedColumns: colNames,
    })
  }

  const selectedColumns = useMemo(
    () =>
      // filters out unknown columns: if the available column names are changed, or if the displayable columns
      //  are added to or removed from in the future
      columns.filter((col) =>
        selectedColumnIds
          ?.map((colEnum) => colEnum.toString())
          .includes(col.name)
      ),
    [columns, selectedColumnIds]
  )

  const { fetchExport, exportColumns } = useCaseLengthTableExport({
    cubeParams,
    orderBy: orderByState,
    sortKeys,
    customPhaseConfigs: customPhaseModalEnabled ? customPhaseConfigs : [],
  })

  const { totalCount, isLoading: isLoadingTotalCountData } =
    useCaseLengthCountData({ cubeParams })

  const {
    hasNextPage,
    hasPreviousPage,
    onNextPage,
    onPreviousPage,
    pagingState: { offset, limit },
  } = usePagingState({
    totalCount,
    loggingState,
    event:
      graphStackGrouping !== CaseGraphMode.Volume
        ? EVENTS.SORT_CASE_LENGTH_TABLE
        : EVENTS.SORT_CASE_VOLUME_TABLE,
  })

  const tableQuery: Query = {
    ...cubeParams,
    dimensions: [
      CASES.CUSTOMER_CASE_ID,
      SITES.NAME,
      ROOMS.ID,
      ROOMS.NAME,
      CASES.CASE_ID,
      CASES.SCHEDULED_DURATION_MINUTES,
      CASES.ACTUAL_START_TIMESTAMP,
      CASES.ACTUAL_DURATION_MINUTES,
      CASES.PRE_OP_DURATION_MINUTES,
      CASES.INTRA_OP_DURATION_MINUTES,
      CASES.POST_OP_DURATION_MINUTES,
      CASES.IS_ADD_ON,
      CASES.PATIENT_CLASSIFICATION_ID,
      CASE_CLASSIFICATION_TYPES.NAME,
      SERVICE_LINES.NAME,
      CASES.CASE_PHASE_ID,
      CASES.ANESTHESIA_PREP_DURATION_MINUTES,
      CASES.SURGERY_PREP_DURATION_MINUTES,
      REPORT_CASE.DELAY_COMMENTS,
      REPORT_CASE.DELAY_REASON,
      REPORT_CASE.DELAY_TYPE,
      REPORT_CASE.DELAY_DURATION_SECONDS,
    ],
    offset: offset,
    limit: limit,
  }

  if (orderByState && orderByState.length == 1) {
    if (sortKeys.includes(orderByState[0].sort)) {
      const queryDirection: QueryOrder =
        orderByState[0].direction == undefined
          ? 'asc'
          : orderByState[0].direction == Direction.ASC
            ? 'asc'
            : 'desc'
      tableQuery.order = {
        [orderByState[0].sort]: queryDirection,
      }
    } else {
      // If the sort passed in is not a known sort, set it to a default sort. This prevents CubeJS
      // from failing when there's an unknown sort order.
      onChangeSort([
        {
          sort: CASES.ACTUAL_START_TIMESTAMP,
          direction: Direction.DESC,
        },
      ])
    }
  }

  // Add dimensions for configured custom phase columns before requesting the data.
  customPhaseColumns.forEach((customPhaseColumn) => {
    tableQuery.dimensions?.push(customPhaseColumn.key)
  })

  const { isLoading: isLoadingRawTableData, resultSet: rawTableData } =
    useCubeQuery<RawTableData>(tableQuery)

  const caseIds = !rawTableData
    ? []
    : rawTableData.rawData().map((edge) => edge[CASES.CASE_ID] ?? '')

  const { caseIdToClinicalData } = useCaseIdToClinicalDataMap(caseIds)

  const tableData: TableData[] = useMemo(() => {
    return (rawTableData?.rawData() ?? []).map((edge) => {
      const clinicalData = caseIdToClinicalData.get(edge[CASES.CASE_ID] ?? '')
      const customerCaseId = edge[CASES.CUSTOMER_CASE_ID]
      const customPhaseDurations = new Map<string, number | undefined>()
      customPhaseColumns.forEach((customPhaseColumn) => {
        customPhaseDurations.set(
          customPhaseColumn.key,
          edge[customPhaseColumn.key]
        )
      })

      return {
        caseId: edge[CASES.CASE_ID] ?? '',
        actualPhaseId: edge[CASES.CASE_PHASE_ID] ?? '',
        customerCaseId: displayCustomerCaseId(customerCaseId),
        surgeonNames: generateListUiDisplay(clinicalData?.surgeonNames),
        anesthesiaNames: generateListUiDisplay(clinicalData?.anesthesiaNames),
        procedureNames: generateListUiDisplay(clinicalData?.procedureNames),
        siteName: edge[SITES.NAME] ?? '',
        roomName: edge[ROOMS.NAME] ?? '',
        roomId: edge[ROOMS.ID] ?? '',
        // Cube automatically adds time zone offset to `timeDimensions`, so they are no longer in UTC
        actualStartTime: DateTime.fromISO(
          edge[CASES.ACTUAL_START_TIMESTAMP] ?? '',
          {
            zone: timezone,
          }
        ),
        actualDuration: edge[CASES.ACTUAL_DURATION_MINUTES]
          ? durationFromMinutes(edge[CASES.ACTUAL_DURATION_MINUTES])
          : undefined,
        preOpDuration: edge[CASES.PRE_OP_DURATION_MINUTES]
          ? durationFromMinutes(edge[CASES.PRE_OP_DURATION_MINUTES])
          : undefined,
        anesthesiaPrepDuration: edge[CASES.ANESTHESIA_PREP_DURATION_MINUTES]
          ? durationFromMinutes(edge[CASES.ANESTHESIA_PREP_DURATION_MINUTES])
          : undefined,
        surgeryPrepDuration: edge[CASES.SURGERY_PREP_DURATION_MINUTES]
          ? durationFromMinutes(edge[CASES.SURGERY_PREP_DURATION_MINUTES])
          : undefined,
        intraOpDuration: edge[CASES.INTRA_OP_DURATION_MINUTES]
          ? durationFromMinutes(edge[CASES.INTRA_OP_DURATION_MINUTES])
          : undefined,
        postOpDuration: edge[CASES.POST_OP_DURATION_MINUTES]
          ? durationFromMinutes(edge[CASES.POST_OP_DURATION_MINUTES])
          : undefined,
        scheduledDuration: edge[CASES.SCHEDULED_DURATION_MINUTES]
          ? durationFromMinutes(edge[CASES.SCHEDULED_DURATION_MINUTES])
          : undefined,
        serviceLineName: edge[SERVICE_LINES.NAME] ?? '',
        caseClassificationName: edge[CASE_CLASSIFICATION_TYPES.NAME] ?? (
          <DashComponent />
        ),
        isAddOn: displayIsAddOn(edge[CASES.CASE_ID], edge[CASES.IS_ADD_ON]),
        patientClass: edge[CASES.PATIENT_CLASSIFICATION_ID] ?? '',
        scrubTechNames: generateListUiDisplay(clinicalData?.scrubTechNames),
        circulatorNames: generateListUiDisplay(clinicalData?.circulatorNames),
        customPhaseDurations,
        delayReason: edge[REPORT_CASE.DELAY_REASON] ?? '',
        delayType: edge[REPORT_CASE.DELAY_TYPE] ?? '',
        delayComments: edge[REPORT_CASE.DELAY_COMMENTS] ?? '',
        delayDuration: edge[REPORT_CASE.DELAY_DURATION_SECONDS]
          ? durationFromMinutes(edge[REPORT_CASE.DELAY_DURATION_SECONDS] / 60)
          : undefined,
      }
    })
  }, [rawTableData, caseIdToClinicalData, customPhaseColumns, timezone])

  const csvExportButtonDisabled = useMemo(
    () =>
      roughlyGreaterThanAYearDifference(new Date(minTime), new Date(maxTime)),
    [maxTime, minTime]
  )

  const currCustomPhaseConfig =
    customPhaseConfigs.filter((id) => id.id === editCustomConfig)[0] ?? ''

  // Ensure newly added custom phase column is immediately added to the table.
  const handleCustomConfigMutationComplete = (customPhaseId: string) => {
    handleSelectedColumnsChange([
      ...selectedColumnIds,
      customPhaseConfigs.find((config) => config.id === customPhaseId)?.name ??
        '',
    ])
  }
  return (
    <Tile gutter={remSpacing.large}>
      <div css={{ display: 'flex', flexDirection: 'column' }}>
        <CustomPhaseDialog
          customPhaseEventData={customPhaseEventData}
          isModalOpen={isOpenModal}
          setIsOpenModal={setIsOpenModal}
          customPhaseConfig={currCustomPhaseConfig}
          handleCustomConfigMutationComplete={
            handleCustomConfigMutationComplete
          }
          setEditCustomConfig={setEditCustomConfig}
          allCustomPhaseConfigs={customPhaseConfigs}
        />
        <div style={{ display: 'flex', marginLeft: 'auto' }}>
          <div style={{ marginRight: remSpacing.medium }}>
            <ColumnsPicker
              label="Columns"
              name="column-picker"
              bulkSelect={true}
              columnNames={selectableColumns.map((col) => col.name)}
              selectedColumnNames={selectedColumnIds}
              onChange={handleSelectedColumnsChange}
            ></ColumnsPicker>
          </div>
          {customPhaseModalEnabled && (
            <div style={{ marginRight: remSpacing.medium }}>
              <Button
                css={{
                  marginBottom: remSpacing.xsmall,
                }}
                onClick={() => {
                  setIsOpenModal(true)
                }}
                type="button"
                color="alternate"
              >
                <PlusCircle
                  css={{
                    color: theme.palette.gray[50],
                  }}
                  size="sm"
                />
                <Span2 color={theme.palette.text.secondary}>Custom phase</Span2>
              </Button>
            </div>
          )}

          <CsvExportButton
            disabled={csvExportButtonDisabled}
            tooltip={
              csvExportButtonDisabled
                ? 'In order to export data, please adjust filters to a time period covering a year or less.'
                : undefined
            }
            fetchRows={fetchExport}
            columns={exportColumns}
            onClick={() =>
              graphStackGrouping !== CaseGraphMode.Volume
                ? eventsLogger(EVENTS.EXPORT_CASE_LENGTH_TABLE, loggingState)
                : eventsLogger(EVENTS.EXPORT_CASE_VOLUME_TABLE, loggingState)
            }
            filename={'case_details'}
          />
        </div>
        <div>
          <RemotePaginatedTableWithVideoBlade
            columns={[...mandatoryColumns, ...selectedColumns]}
            data={tableData}
            isLoading={
              isLoadingRawTableData ||
              isLoadingTotalCountData ||
              phaseTypesLoading
            }
            isMultiSort={false}
            paginationType="minimal"
            sortOrder={orderByState ?? undefined}
            hasNextPage={hasNextPage}
            hasPreviousPage={hasPreviousPage}
            onNextPageClicked={onNextPage}
            onPreviousPageClicked={onPreviousPage}
            onChangeSort={(sortOrder) => onChangeSort(sortOrder)}
            metric={METRICS.CASE}
          />
        </div>
      </div>
    </Tile>
  )
}

export default CaseLengthTable

function useCaseLengthCountData({ cubeParams }: { cubeParams: Query }) {
  const { isLoading, resultSet: totalCountData } = useCubeQuery<{
    [key: string]: number
  }>({
    ...cubeParams,
    measures: [CASES.COUNT],
  })

  return {
    totalCount: totalCountData?.rawData()[0][CASES.COUNT],
    isLoading,
  }
}
