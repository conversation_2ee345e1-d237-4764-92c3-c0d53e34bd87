import { useContext, useMemo } from 'react'

import { Query, ResultSet } from '@cubejs-client/core'
import { CubeContext } from '@cubejs-client/react'
import { DateTime, DurationLikeObject } from 'luxon'

import {
  Direction,
  OrderBy,
  ApellaDateTimeFormats,
} from '@apella/component-library'
import { PatientClass, PhaseType } from 'src/__generated__/globalTypes'
import { useTimezone } from 'src/Contexts'
import {
  CASE_CLASSIFICATION_TYPES,
  CASES,
  REPORT_CASE,
  ROOMS,
  SERVICE_LINES,
  SITES,
} from 'src/modules/cube/types/dataCubes'
import { useClinicalData } from 'src/modules/insights/hooks/useClinicalData'
import {
  displayCustomerCaseIdCsv,
  displayIsAddOnCsv,
  generateListCsvDisplay,
} from 'src/pages/Insights/dataHelpers'
import { convertDurationToMinutesString } from 'src/pages/Insights/dateTimeHelpers'
import { CaseLengthFilterProps, DisplayName } from 'src/pages/Insights/types'
import { getPatientClassText } from 'src/pages/Schedule/CaseDetails'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'

import { getPhaseDetailsFromType } from '../phaseHelpers'
import { CustomPhaseConfig } from '../types'
import { usePhaseTypes } from '../usePhaseTypes'

type RawTableData = Partial<{
  [CASES.ACTUAL_START_TIMESTAMP]: string
  [CASES.ACTUAL_DURATION_MINUTES]: DurationLikeObject
  [CASES.PRE_OP_DURATION_MINUTES]: DurationLikeObject
  [CASES.ANESTHESIA_PREP_DURATION_MINUTES]: DurationLikeObject
  [CASES.SURGERY_PREP_DURATION_MINUTES]: DurationLikeObject
  [CASES.INTRA_OP_DURATION_MINUTES]: DurationLikeObject
  [CASES.POST_OP_DURATION_MINUTES]: DurationLikeObject
  [CASES.CASE_ID]: string
  [CASES.SCHEDULED_DURATION_MINUTES]: DurationLikeObject
  [SITES.NAME]: string
  [ROOMS.NAME]: string
  [ROOMS.ID]: string
  [CASES.CUSTOMER_CASE_ID]: string
  [CASES.IS_ADD_ON]: boolean
  [CASES.PATIENT_CLASSIFICATION_ID]: string
  [CASE_CLASSIFICATION_TYPES.NAME]: string
  [SERVICE_LINES.NAME]: string
}> & {
  // Allow custom phase columns. It's impractical to include the full list here.
  [key: string]: number
}

type CsvResult = {
  [DisplayName.CUSTOMER_CASE_ID]: string
  [DisplayName.DAY]: string
  [DisplayName.START]: string
  [DisplayName.ROOMS]?: string
  [DisplayName.SITE]?: string
  [DisplayName.PRIMARY_SURGEONS]: string
  [DisplayName.ANESTHESIA]: string
  [DisplayName.PROCEDURES]: string
  [DisplayName.ACTUAL_DURATION]?: string
  [DisplayName.SCHEDULED_DURATION]?: string
  [DisplayName.ADD_ON]: string
  [DisplayName.DELAY_REASON]?: string
  [DisplayName.DELAY_TYPE]?: string
} & Partial<{ [key: string]: string }>

export const useCaseLengthTableExport = ({
  orderBy,
  sortKeys,
  cubeParams,
  customPhaseConfigs,
}: Pick<CaseLengthFilterProps, 'cubeParams'> & {
  orderBy?: OrderBy[]
  sortKeys: (string | undefined)[]
  customPhaseConfigs: CustomPhaseConfig[]
}) => {
  const { timezone } = useTimezone()
  const defaultSort = CASES.ACTUAL_START_TIMESTAMP
  const orderByQuery =
    orderBy && orderBy.length == 1 && sortKeys.includes(orderBy[0].sort)
      ? orderBy[0]
      : { sort: defaultSort, direction: Direction.DESC }

  const { phaseTypes } = usePhaseTypes()

  const { fetchClinicalData } = useClinicalData()

  const extendedTableQuery: Query = {
    ...cubeParams,
    dimensions: [
      CASES.CUSTOMER_CASE_ID,
      SITES.NAME,
      ROOMS.ID,
      ROOMS.NAME,
      CASES.CASE_ID,
      CASES.SCHEDULED_DURATION_MINUTES,
      CASES.ACTUAL_START_TIMESTAMP,
      CASES.ACTUAL_DURATION_MINUTES,
      CASES.PRE_OP_DURATION_MINUTES,
      CASES.INTRA_OP_DURATION_MINUTES,
      CASES.POST_OP_DURATION_MINUTES,
      CASES.IS_ADD_ON,
      CASES.PATIENT_CLASSIFICATION_ID,
      CASE_CLASSIFICATION_TYPES.NAME,
      SERVICE_LINES.NAME,
      CASES.ANESTHESIA_PREP_DURATION_MINUTES,
      CASES.SURGERY_PREP_DURATION_MINUTES,
      REPORT_CASE.DELAY_TYPE,
      REPORT_CASE.DELAY_REASON,
      REPORT_CASE.DELAY_DURATION_SECONDS,
      REPORT_CASE.DELAY_COMMENTS,
    ],
    order: {
      [orderByQuery.sort]:
        orderByQuery.direction == undefined
          ? 'asc'
          : orderByQuery.direction !== Direction.ASC
            ? 'desc'
            : 'asc',
    },
  }
  const { cubeApi } = useContext(CubeContext)

  const anesthesiaPrepColumnName = `${DisplayName.PREP_ANESTHESIA} (mins)`
  const surgeonPrepColumnName = `${DisplayName.PREP_SURGERY} (mins)`
  const preOpColumnName = `${
    getPhaseDetailsFromType(phaseTypes)(PhaseType.PRE_OPERATIVE).title
  } (mins)`
  const intraOpColumnName = `${
    getPhaseDetailsFromType(phaseTypes)(PhaseType.INTRA_OPERATIVE).title
  } (mins)`
  const postOpColumnName = `${
    getPhaseDetailsFromType(phaseTypes)(PhaseType.POST_OPERATIVE).title
  } (mins)`

  const customPhaseColumns = useMemo(() => {
    return customPhaseConfigs.map((customPhase) =>
      generateCustomPhaseColumn(customPhase)
    )
  }, [customPhaseConfigs])

  const exportColumns = useMemo(() => {
    const columns: {
      name: string
      exportFormatter: (row: CsvResult) => string | undefined
    }[] = [
      {
        name: DisplayName.CUSTOMER_CASE_ID,
        exportFormatter: (row: CsvResult) => row[DisplayName.CUSTOMER_CASE_ID],
      },
      {
        name: DisplayName.DAY,
        exportFormatter: (row: CsvResult) => row[DisplayName.DAY],
      },
      {
        name: DisplayName.START,
        exportFormatter: (row: CsvResult) => row[DisplayName.START],
      },
      {
        name: DisplayName.ROOMS,
        exportFormatter: (row: CsvResult) => row[DisplayName.ROOMS],
      },
      {
        name: DisplayName.SITE,
        exportFormatter: (row: CsvResult) => row[DisplayName.SITE],
      },
      {
        name: DisplayName.SERVICE_LINE,
        exportFormatter: (row: CsvResult) => row[DisplayName.SERVICE_LINE],
      },
      {
        name: DisplayName.PRIMARY_SURGEONS,
        exportFormatter: (row: CsvResult) => row[DisplayName.PRIMARY_SURGEONS],
      },
      {
        name: DisplayName.ANESTHESIA,
        exportFormatter: (row: CsvResult) => row[DisplayName.ANESTHESIA],
      },
      {
        name: DisplayName.CIRCULATORS,
        exportFormatter: (row: CsvResult) => row[DisplayName.CIRCULATORS],
      },
      {
        name: DisplayName.SCRUB_TECHS,
        exportFormatter: (row: CsvResult) => row[DisplayName.SCRUB_TECHS],
      },
      {
        name: DisplayName.DELAY_TYPE,
        exportFormatter: (row: CsvResult) => row[DisplayName.DELAY_TYPE],
      },
      {
        name: DisplayName.DELAY_REASON,
        exportFormatter: (row: CsvResult) => row[DisplayName.DELAY_REASON],
      },
      {
        name: DisplayName.DELAY_DURATION,
        exportFormatter: (row: CsvResult) => row[DisplayName.DELAY_DURATION],
      },
      {
        name: DisplayName.DELAY_COMMENTS,
        exportFormatter: (row: CsvResult) => row[DisplayName.DELAY_COMMENTS],
      },
    ]

    columns.push(
      {
        name: DisplayName.PROCEDURES,
        exportFormatter: (row: CsvResult) => row[DisplayName.PROCEDURES],
      },
      {
        name: DisplayName.CASE_CLASSIFICATION,
        exportFormatter: (row: CsvResult) =>
          row[DisplayName.CASE_CLASSIFICATION],
      },
      {
        name: DisplayName.PATIENT_CLASS,
        exportFormatter: (row: CsvResult) =>
          getPatientClassText(row[DisplayName.PATIENT_CLASS] as PatientClass) ??
          'Other',
      },
      {
        name: DisplayName.ADD_ON,
        exportFormatter: (row: CsvResult) => row[DisplayName.ADD_ON],
      },
      {
        name: preOpColumnName,
        exportFormatter: (row: CsvResult) => row[preOpColumnName],
      },
      {
        name: intraOpColumnName,
        exportFormatter: (row: CsvResult) => row[intraOpColumnName],
      },
      {
        name: postOpColumnName,
        exportFormatter: (row: CsvResult) => row[postOpColumnName],
      },
      {
        name: DisplayName.ACTUAL_DURATION,
        exportFormatter: (row: CsvResult) => row[DisplayName.ACTUAL_DURATION],
      },
      {
        name: DisplayName.SCHEDULED_DURATION,
        exportFormatter: (row: CsvResult) =>
          row[DisplayName.SCHEDULED_DURATION],
      }
    )
    columns.push(
      {
        name: anesthesiaPrepColumnName,
        exportFormatter: (row: CsvResult) => row[anesthesiaPrepColumnName],
      },
      {
        name: surgeonPrepColumnName,
        exportFormatter: (row: CsvResult) => row[surgeonPrepColumnName],
      }
    )
    if (customPhaseColumns.length > 0) {
      customPhaseColumns.forEach((column) => {
        columns.push({
          name: column.name,
          exportFormatter: column.exportFormatter,
        })
      })
    }
    return columns
  }, [
    preOpColumnName,
    intraOpColumnName,
    postOpColumnName,
    customPhaseColumns,
    anesthesiaPrepColumnName,
    surgeonPrepColumnName,
  ])

  // Add dimensions for configured custom phase columns before requesting the data.
  customPhaseColumns.forEach((column) => {
    extendedTableQuery.dimensions?.push(column.key)
  })

  const fetchExport = async () => {
    const results = (await cubeApi.load(
      extendedTableQuery
    )) as ResultSet<RawTableData> | null

    const data = results ? results.rawData() : []

    const caseIds = data.map((edge) => edge[CASES.CASE_ID] ?? '')

    const caseIdToClinicalData = await fetchClinicalData(caseIds)

    return data.map<CsvResult>((edge) => {
      const caseId = edge[CASES.CASE_ID] ?? ''
      const clinicalData = caseIdToClinicalData.get(caseId)

      const customPhases = new Map<string, string>()

      if (customPhaseColumns.length > 0) {
        customPhaseColumns.forEach((column) => {
          const key = edge[column.key]
          customPhases.set(column.name, key !== null ? String(key) : '')
        })
      }

      const customPhasesObject = Object.fromEntries(customPhases)

      return {
        [DisplayName.CUSTOMER_CASE_ID]: displayCustomerCaseIdCsv(
          edge[CASES.CUSTOMER_CASE_ID]
        ),
        [DisplayName.DAY]: DateTime.fromISO(
          edge[CASES.ACTUAL_START_TIMESTAMP] ?? '',
          {
            zone: timezone,
          }
        ).weekdayLong,
        [DisplayName.START]: DateTime.fromISO(
          edge[CASES.ACTUAL_START_TIMESTAMP] ?? '',
          {
            zone: timezone,
          }
        ).toLocaleString(ApellaDateTimeFormats.DATETIME),
        [DisplayName.ROOMS]: edge[ROOMS.NAME],
        [DisplayName.SITE]: edge[SITES.NAME],
        [DisplayName.PRIMARY_SURGEONS]: generateListCsvDisplay(
          clinicalData?.surgeonNames
        ),
        [DisplayName.ANESTHESIA]: generateListCsvDisplay(
          clinicalData?.anesthesiaNames
        ),
        [DisplayName.PROCEDURES]: generateListCsvDisplay(
          clinicalData?.procedureNames
        ),
        [preOpColumnName]: convertDurationToMinutesString(
          edge[CASES.PRE_OP_DURATION_MINUTES]
            ? durationFromMinutes(edge[CASES.PRE_OP_DURATION_MINUTES])
            : undefined
        ),
        [anesthesiaPrepColumnName]: convertDurationToMinutesString(
          edge[CASES.ANESTHESIA_PREP_DURATION_MINUTES]
            ? durationFromMinutes(edge[CASES.ANESTHESIA_PREP_DURATION_MINUTES])
            : undefined
        ),
        [surgeonPrepColumnName]: convertDurationToMinutesString(
          edge[CASES.SURGERY_PREP_DURATION_MINUTES]
            ? durationFromMinutes(edge[CASES.SURGERY_PREP_DURATION_MINUTES])
            : undefined
        ),
        [intraOpColumnName]: convertDurationToMinutesString(
          edge[CASES.INTRA_OP_DURATION_MINUTES]
            ? durationFromMinutes(edge[CASES.INTRA_OP_DURATION_MINUTES])
            : undefined
        ),
        [postOpColumnName]: convertDurationToMinutesString(
          edge[CASES.POST_OP_DURATION_MINUTES]
            ? durationFromMinutes(edge[CASES.POST_OP_DURATION_MINUTES])
            : undefined
        ),
        [DisplayName.ACTUAL_DURATION]: edge[CASES.ACTUAL_DURATION_MINUTES]
          ? convertDurationToMinutesString(
              durationFromMinutes(edge[CASES.ACTUAL_DURATION_MINUTES])
            )
          : undefined,
        [DisplayName.SCHEDULED_DURATION]: edge[CASES.SCHEDULED_DURATION_MINUTES]
          ? convertDurationToMinutesString(
              durationFromMinutes(edge[CASES.SCHEDULED_DURATION_MINUTES])
            )
          : undefined,
        [DisplayName.SERVICE_LINE]: edge[SERVICE_LINES.NAME] ?? '',
        [DisplayName.CASE_CLASSIFICATION]:
          edge[CASE_CLASSIFICATION_TYPES.NAME] ?? '',
        [DisplayName.ADD_ON]: displayIsAddOnCsv(
          edge[CASES.CASE_ID],
          edge[CASES.IS_ADD_ON]
        ),
        [DisplayName.PATIENT_CLASS]:
          edge[CASES.PATIENT_CLASSIFICATION_ID] ?? '',
        [DisplayName.CIRCULATORS]: generateListCsvDisplay(
          clinicalData?.circulatorNames
        ),
        [DisplayName.SCRUB_TECHS]: generateListCsvDisplay(
          clinicalData?.scrubTechNames
        ),
        ...customPhasesObject,
      }
    })
  }

  return { fetchExport, exportColumns }
}

const generateCustomPhaseColumn = (customPhase: CustomPhaseConfig) => {
  const columnName = `${customPhase.name} (mins)`
  return {
    key: `CustomPhases.${customPhase.startEventTypeId}_${customPhase.endEventTypeId}`,
    name: columnName,
    exportFormatter: (row: CsvResult) => {
      return row[columnName]
    },
  }
}

export default useCaseLengthTableExport
