import { useMemo } from 'react'

import { Query } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'
import { DateTime, DurationLikeObject } from 'luxon'

import { formatDuration } from '@apella/component-library'
import { PhaseType } from 'src/__generated__/globalTypes'
import { CaseGraphMode } from 'src/components/InsightsGraph'
import {
  InsightsSummaryMetric,
  InsightsTile,
  MetricsSection,
} from 'src/components/InsightsTile/InsightsTile'
import { useTimezone } from 'src/Contexts'
import { CASES } from 'src/modules/cube/types/dataCubes'
import { getPhaseDetailsFromType } from 'src/pages/Insights/phaseHelpers'
import { formatTimeDimensionDateRange } from 'src/pages/Insights/queries'
import { METRICS } from 'src/pages/Insights/types'
import { useInsightsSearchParams } from 'src/pages/Insights/useInsightsSearchParams'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'

import { usePhaseTypes } from '../usePhaseTypes'
import { durationAbs } from './CaseLengthGraph'

type SummaryTileData = Partial<{
  [CASES.AVG_ACTUAL_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.COUNT]: string
  [CASES.AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.AVG_SCHEDULED_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.AVG_PRE_OP_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.AVG_INTRA_OP_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.AVG_POST_OP_DURATION_MINUTES]: DurationLikeObject | null
  [CASES.SUM_ACTUAL_DURATION_MINUTES]: DurationLikeObject | null
}>

const CaseLengthSummaryTile = (): React.JSX.Element => {
  const { phaseTypes } = usePhaseTypes()
  const { timezone } = useTimezone()
  const { minTime, maxTime, graphStackGrouping, cubeParams } =
    useInsightsSearchParams(METRICS.CASE)

  const minDateTime = DateTime.fromISO(minTime)
  const maxDateTime = DateTime.fromISO(maxTime)
  const diff = maxDateTime.diff(minDateTime)
  const prevMinTime = minDateTime.minus(diff)

  const caseLengthDataQuery: Query = {
    measures: [
      CASES.COUNT,
      CASES.AVG_ACTUAL_DURATION_MINUTES,
      CASES.AVG_SCHEDULED_DURATION_MINUTES,
      CASES.AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES,
      CASES.AVG_PRE_OP_DURATION_MINUTES,
      CASES.AVG_INTRA_OP_DURATION_MINUTES,
      CASES.AVG_POST_OP_DURATION_MINUTES,
      CASES.SUM_ACTUAL_DURATION_MINUTES,
    ],
    ...cubeParams,
  }

  const {
    resultSet: caseLengthResultSet,
    isLoading: isLoadingCaseLengthResult,
  } = useCubeQuery<SummaryTileData>(caseLengthDataQuery)
  const caseLengthData = caseLengthResultSet?.rawData()

  const {
    isLoading: isLoadingPrevCaseLengthResultSet,
    resultSet: prevCaseLengthResultSet,
  } = useCubeQuery<SummaryTileData>({
    ...caseLengthDataQuery,
    timeDimensions: [
      {
        dimension: CASES.ACTUAL_START_TIMESTAMP,
        dateRange: formatTimeDimensionDateRange(
          [prevMinTime.toISO(), minTime],
          timezone
        ),
      },
    ],
    timezone: timezone,
  })

  const prevCaseLengthData = prevCaseLengthResultSet?.rawData()

  const phaseDetails = getPhaseDetailsFromType(phaseTypes)(PhaseType.CASE)

  const durationMetric = useMemo(() => {
    const averageDurationData = caseLengthData
      ? caseLengthData[0][CASES.AVG_ACTUAL_DURATION_MINUTES]
      : undefined
    const averageDuration = averageDurationData
      ? durationFromMinutes(averageDurationData)
      : undefined
    const previousDurationData = prevCaseLengthData
      ? prevCaseLengthData[0]?.[CASES.AVG_ACTUAL_DURATION_MINUTES]
      : undefined
    const previousDuration = previousDurationData
      ? durationFromMinutes(previousDurationData)
      : undefined

    return {
      label: `${phaseDetails.title} avg`,
      value: averageDuration,
      description: `The average duration of ${phaseDetails.title.toLowerCase()}s for the selected filters. ${
        phaseDetails.description
      }`,
      previousValue: previousDuration,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
    }
  }, [
    caseLengthData,
    prevCaseLengthData,
    minDateTime,
    maxDateTime,
    prevMinTime,
    phaseDetails.description,
    phaseDetails.title,
  ])

  const countMetric = useMemo(() => {
    const count =
      caseLengthData !== undefined
        ? Number(caseLengthData[0]?.[CASES.COUNT])
        : undefined
    const previousCount =
      prevCaseLengthData !== undefined
        ? Number(prevCaseLengthData[0]?.[CASES.COUNT])
        : undefined

    return {
      label: `Total ${phaseDetails.title.toLowerCase()}s`,
      value: count,
      description: `The total count of ${phaseDetails.title.toLowerCase()}s for the selected filters. ${
        phaseDetails.description
      }`,
      previousValue: previousCount,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
    }
  }, [
    caseLengthData,
    prevCaseLengthData,
    minDateTime,
    maxDateTime,
    prevMinTime,
    phaseDetails.description,
    phaseDetails.title,
  ])

  const totalDurationMetric = useMemo(() => {
    const totalDurationData = caseLengthData
      ? caseLengthData[0][CASES.SUM_ACTUAL_DURATION_MINUTES]
      : undefined
    const totalDuration = totalDurationData
      ? durationFromMinutes(totalDurationData)
      : undefined

    const displayedMinutes = formatDuration(totalDuration)

    return {
      label: `Total duration`,
      value: totalDuration,
      description: `A sum of ${displayedMinutes} for ${phaseDetails.title.toLowerCase()}s for the selected filters. ${
        phaseDetails.description
      }`,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
    }
  }, [
    caseLengthData,
    minDateTime,
    maxDateTime,
    prevMinTime,
    phaseDetails.description,
    phaseDetails.title,
  ])

  const generateChildMetric = (phaseType: PhaseType, field: string) => {
    const phaseAverage = caseLengthData
      ? caseLengthData[0]?.[field as keyof SummaryTileData]
      : undefined
    return {
      label: `${getPhaseDetailsFromType(phaseTypes)(phaseType).title} avg`,
      value:
        caseLengthData && phaseAverage && typeof phaseAverage === 'number'
          ? durationFromMinutes(phaseAverage)
          : undefined,
      description: getPhaseDetailsFromType(phaseTypes)(phaseType).description,
    }
  }

  const middleMetrics: InsightsSummaryMetric[] = [
    generateChildMetric(
      PhaseType.PRE_OPERATIVE,
      CASES.AVG_PRE_OP_DURATION_MINUTES
    ),
    generateChildMetric(
      PhaseType.INTRA_OPERATIVE,
      CASES.AVG_INTRA_OP_DURATION_MINUTES
    ),
    generateChildMetric(
      PhaseType.POST_OPERATIVE,
      CASES.AVG_POST_OP_DURATION_MINUTES
    ),
  ]

  const scheduledDurationMetric = useMemo(() => {
    const avgScheduledDurationData = caseLengthData
      ? caseLengthData[0][CASES.AVG_SCHEDULED_DURATION_MINUTES]
      : undefined
    const avgScheduledDuration = avgScheduledDurationData
      ? durationFromMinutes(avgScheduledDurationData)
      : undefined
    const previousAvgScheduledDurationData = prevCaseLengthData
      ? prevCaseLengthData[0]?.[CASES.AVG_SCHEDULED_DURATION_MINUTES]
      : undefined
    const previousAvgScheduledDuration = previousAvgScheduledDurationData
      ? durationFromMinutes(previousAvgScheduledDurationData)
      : undefined

    return {
      label: `Scheduled avg`,
      value: avgScheduledDuration,
      description: `The average scheduled duration of cases for the selected filters.`,
      previousValue: previousAvgScheduledDuration,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
    }
  }, [
    caseLengthData,
    prevCaseLengthData,
    minDateTime,
    maxDateTime,
    prevMinTime,
  ])

  const scheduleVarianceMetric = useMemo(() => {
    const avgScheduleVarianceData = caseLengthData
      ? caseLengthData[0][CASES.AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES]
      : undefined
    const avgActualDuration = avgScheduleVarianceData
      ? durationAbs(durationFromMinutes(avgScheduleVarianceData))
      : undefined
    const previousAvgScheduleVarianceData = prevCaseLengthData
      ? prevCaseLengthData[0]?.[
          CASES.AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES
        ]
      : undefined
    const previousActualDuration = previousAvgScheduleVarianceData
      ? durationAbs(durationFromMinutes(previousAvgScheduleVarianceData))
      : undefined

    return {
      label: `Schedule variance avg`,
      value: avgActualDuration,
      description: `The average schedule variance of cases for the selected filters. Schedule variance is the absolute value of difference between the actual and scheduled duration of cases.`,
      previousValue: previousActualDuration,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
    }
  }, [
    caseLengthData,
    prevCaseLengthData,
    minDateTime,
    maxDateTime,
    prevMinTime,
  ])

  const topMetrics = [countMetric, totalDurationMetric]

  const bottomMetrics = [durationMetric]

  let metricsSections = [topMetrics, middleMetrics, bottomMetrics]

  const isLoading =
    isLoadingCaseLengthResult || isLoadingPrevCaseLengthResultSet

  if (graphStackGrouping === CaseGraphMode.ScheduleVsActual) {
    bottomMetrics.push(scheduledDurationMetric)
    bottomMetrics.push(scheduleVarianceMetric)
    durationMetric.label = 'Actual avg'
    metricsSections = [topMetrics, bottomMetrics]
  }

  return (
    <InsightsTile
      metricsSections={metricsSections.map((metrics, index) => (
        <MetricsSection isLoading={isLoading} key={index} metrics={metrics} />
      ))}
    />
  )
}

export default CaseLengthSummaryTile
