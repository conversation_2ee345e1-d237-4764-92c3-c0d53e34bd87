import {
  CASE_STAFF_SURGEONS,
  CASES,
  TURNOVERS,
} from 'src/modules/cube/types/dataCubes'
import {
  formatTimeDimensionDateRange,
  getNewFirstCaseStartQueryParams,
  getCasesQueryParams,
  getTurnoverQueryParams,
} from 'src/pages/Insights/queries'

const staffIds = ['staffId']
const minDateTime = '2022-11-30T00:00:00.000-07:00'
const maxDateTime = '2022-11-30T23:59:59.999-07:00'
const timezone = 'America/Los_Angeles'

describe('turnover queries', () => {
  it('has timezone', () => {
    const query = getTurnoverQueryParams({
      minDateTime,
      maxDateTime,
      timezone,
    })

    expect(query.timezone).toEqual(timezone)
  })
  it('has time dimensions', () => {
    const query = getTurnoverQueryParams({
      minDateTime,
      maxDateTime,
      timezone,
    })

    expect(query.timeDimensions).toEqual([
      {
        dimension: TURNOVERS.START_TIMESTAMP,
        dateRange: ['2022-11-29T23:00:00.000', '2022-11-30T22:59:59.999'],
      },
    ])
  })

  it('filtering by staffId without dimension has correct filters', () => {
    const query = getTurnoverQueryParams({
      staffIds,
      minDateTime,
      maxDateTime,
      timezone,
    })

    expect(query.filters).toEqual([
      {
        member: CASE_STAFF_SURGEONS.STAFF_ID,
        operator: 'equals',
        values: staffIds,
      },
    ])
  })
})
describe('first case starts queries', () => {
  it('has timezone', () => {
    const query = getNewFirstCaseStartQueryParams({
      minDateTime,
      maxDateTime,
      timezone,
    })

    expect(query.timezone).toEqual(timezone)
  })
  it('has time dimensions', () => {
    const query = getNewFirstCaseStartQueryParams({
      minDateTime,
      maxDateTime,
      timezone,
    })

    expect(query.timeDimensions).toEqual([
      {
        dimension: CASES.SCHEDULED_START_TIMESTAMP,
        dateRange: ['2022-11-29T23:00:00.000', '2022-11-30T22:59:59.999'],
      },
    ])
  })

  it('filtering by staffId without dimension has correct filters', () => {
    const query = getNewFirstCaseStartQueryParams({
      minDateTime,
      maxDateTime,
      staffIds,
      timezone,
    })
    expect(query.filters).toEqual([
      {
        member: CASES.IS_FIRST_CASE,
        operator: 'equals',
        values: ['true'],
      },
      {
        member: CASES.ACTUAL_START_TIMESTAMP,
        operator: 'set',
      },
      {
        member: CASE_STAFF_SURGEONS.STAFF_ID,
        operator: 'equals',
        values: staffIds,
      },
    ])
  })
})

describe('case length queries', () => {
  it('has timezone', () => {
    const query = getCasesQueryParams({
      minDateTime,
      maxDateTime,
      timezone,
    })

    expect(query.timezone).toEqual(timezone)
  })
  it('has time dimensions', () => {
    const query = getCasesQueryParams({
      minDateTime,
      maxDateTime,
      timezone,
    })

    expect(query.timeDimensions).toEqual([
      {
        dimension: CASES.ACTUAL_START_TIMESTAMP,
        dateRange: ['2022-11-29T23:00:00.000', '2022-11-30T22:59:59.999'],
      },
    ])
  })

  it('filtering by staffId without dimension has correct filters', () => {
    const query = getCasesQueryParams({
      staffIds,
      minDateTime,
      maxDateTime,
      timezone,
    })

    expect(query.filters).toEqual([
      {
        member: CASE_STAFF_SURGEONS.STAFF_ID,
        operator: 'equals',
        values: staffIds,
      },
    ])
  })
})

describe('formatTimeDimension', () => {
  it('with the same timezone', () => {
    const times: [string, string] = [
      '2022-11-30T00:00:00.000-08:00',
      '2022-11-30T23:59:59.999-08:00',
    ]
    const timezone = 'America/Los_Angeles'

    const result = formatTimeDimensionDateRange(times, timezone)

    expect(result).toEqual([
      '2022-11-30T00:00:00.000',
      '2022-11-30T23:59:59.999',
    ])
  })
  it('with different timezone', () => {
    const times: [string, string] = [
      '2022-11-30T00:00:00.000-08:00',
      '2022-11-30T23:59:59.999-08:00',
    ]
    const timezone = 'America/New_York'

    const result = formatTimeDimensionDateRange(times, timezone)

    expect(result).toEqual([
      '2022-11-30T03:00:00.000',
      '2022-12-01T02:59:59.999',
    ])
  })
})
