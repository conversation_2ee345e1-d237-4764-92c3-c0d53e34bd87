import { useMemo } from 'react'

import { METRICS } from '../types'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import NumerousMultiSelectDropdown, {
  getSuggestedOptionsDescriptionBasedOnTab,
  mapToOption,
} from './NumerousMultiSelectDropdown'

const ScrubTechFilterDropdown = ({ metric }: { metric: METRICS }) => {
  const { scrubTechStaffIds, onChangeScrubTech, scrubTechStaffWithCount } =
    useInsightsSearchParams(metric)

  const options = useMemo(
    () => scrubTechStaffWithCount.map(mapToOption),
    [scrubTechStaffWithCount]
  )
  return (
    <NumerousMultiSelectDropdown
      noOptionSelectedText={'Scrub Techs'}
      noSearchResultsFoundText={'No scrub techs found'}
      selectedOptionIds={new Set<string>(scrubTechStaffIds)}
      options={options}
      suggestedOptionsDescription={getSuggestedOptionsDescriptionBasedOnTab(
        metric
      )}
      onChangeHandler={(newSelectedScrubTechUserIds) => {
        onChangeScrubTech(
          newSelectedScrubTechUserIds.length === 0
            ? undefined
            : newSelectedScrubTechUserIds
        )
      }}
    />
  )
}

export default ScrubTechFilterDropdown
