import { useMemo } from 'react'

import { METRICS } from '../types'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import NumerousMultiSelectDropdown, {
  getSuggestedOptionsDescriptionBasedOnTab,
  mapToOption,
} from './NumerousMultiSelectDropdown'

const CirculatorFilterDropdown = ({ metric }: { metric: METRICS }) => {
  const { circulatorStaffIds, onChangeCirculator, circulatorStaffWithCount } =
    useInsightsSearchParams(metric)

  const options = useMemo(
    () => circulatorStaffWithCount.map(mapToOption),
    [circulatorStaffWithCount]
  )
  return (
    <NumerousMultiSelectDropdown
      noOptionSelectedText={'Circulators'}
      noSearchResultsFoundText={'No circulators found'}
      selectedOptionIds={new Set<string>(circulatorStaffIds)}
      options={options}
      suggestedOptionsDescription={getSuggestedOptionsDescriptionBasedOnTab(
        metric
      )}
      onChangeHandler={(newSelectedCirculatorUserIds) => {
        onChangeCirculator(
          newSelectedCirculatorUserIds.length === 0
            ? undefined
            : newSelectedCirculatorUserIds
        )
      }}
    />
  )
}

export default CirculatorFilterDropdown
