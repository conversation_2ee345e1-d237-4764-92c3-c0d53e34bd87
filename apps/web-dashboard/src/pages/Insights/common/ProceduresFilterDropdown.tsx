import { useMemo } from 'react'

import { METRICS } from '../types'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import NumerousMultiSelectDropdown, {
  getSuggestedOptionsDescriptionBasedOnTab,
  mapToOption,
} from './NumerousMultiSelectDropdown'

const ProcedureFilterDropdown = ({ metric }: { metric: METRICS }) => {
  const { procedureIds, onChangeProcedure, proceduresWithCount } =
    useInsightsSearchParams(metric)

  const options = useMemo(
    () => proceduresWithCount.map(mapToOption),
    [proceduresWithCount]
  )
  return (
    <NumerousMultiSelectDropdown
      noOptionSelectedText={'Procedures'}
      noSearchResultsFoundText={'No procedures found'}
      selectedOptionIds={new Set<string>(procedureIds)}
      options={options}
      suggestedOptionsDescription={getSuggestedOptionsDescriptionBasedOnTab(
        metric
      )}
      onChangeHandler={(newSelectedProcedureIds) => {
        onChangeProcedure(
          newSelectedProcedureIds.length === 0
            ? undefined
            : newSelectedProcedureIds
        )
      }}
    />
  )
}

export default ProcedureFilterDropdown
