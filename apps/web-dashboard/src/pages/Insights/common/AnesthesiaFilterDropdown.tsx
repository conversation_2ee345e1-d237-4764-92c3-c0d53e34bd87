import { useMemo } from 'react'

import { METRICS } from '../types'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import NumerousMultiSelectDropdown, {
  getSuggestedOptionsDescriptionBasedOnTab,
  mapToOption,
} from './NumerousMultiSelectDropdown'

const AnesthesiaFilterDropdown = ({ metric }: { metric: METRICS }) => {
  const { anesthesiaIds, onChangeAnesthesia, anesthesiaStaffWithCount } =
    useInsightsSearchParams(metric)

  const options = useMemo(
    () => anesthesiaStaffWithCount.map(mapToOption),
    [anesthesiaStaffWithCount]
  )
  return (
    <NumerousMultiSelectDropdown
      noOptionSelectedText={'Anesthesia'}
      noSearchResultsFoundText={'No anesthesiologists found'}
      selectedOptionIds={new Set<string>(anesthesiaIds)}
      options={options}
      suggestedOptionsDescription={getSuggestedOptionsDescriptionBasedOnTab(
        metric
      )}
      onChangeHandler={(newSelectedAnesthesiologistUserIds) => {
        onChangeAnesthesia(
          newSelectedAnesthesiologistUserIds.length === 0
            ? undefined
            : newSelectedAnesthesiologistUserIds
        )
      }}
    />
  )
}

export default AnesthesiaFilterDropdown
