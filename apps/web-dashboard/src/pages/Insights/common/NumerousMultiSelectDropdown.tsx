import { useMemo, useState } from 'react'

import {
  ControlledSearchMultiSelect,
  Option,
  textContainsSearchTerms,
} from '@apella/component-library'

import { ProcedureShape } from '../../../components/ProceduresFilter'
import { StaffShape } from '../../../components/StaffFilter'
import { METRICS } from '../types'

export type DropdownOption = {
  id: string
  description: string
  count: number
}

const getDropdownButtonLabel = (
  selectedOptions: DropdownOption[],
  noneSelectedText: string
): string => {
  if (selectedOptions.length === 1) {
    return selectedOptions[0].description
  } else if (selectedOptions.length > 1) {
    return `${selectedOptions.length} selected`
  } else {
    // if negative / invalid, just return the default, too
    return noneSelectedText
  }
}

export const getSuggestedOptionsDescriptionBasedOnTab = (
  metric: METRICS
): string => {
  switch (metric) {
    case METRICS.FIRST_CASE_START:
      return 'Suggested (by first case count)'
    case METRICS.TURNOVER:
      return 'Suggested (by turnover count)'
    case METRICS.CASE:
    default:
      return 'Suggested (by case count)'
  }
}

const sortDescByCount = (a: DropdownOption, b: DropdownOption) =>
  b.count - a.count

/**
 * Intended to be used for dropdowns with many options (eg: hundreds), which
 * users would not be expected to scroll through, rather only search for options
 * in the dropdown.
 * Out of convenience, this component gives a list of "suggested" options based
 * on the highest count.
 */
const NumerousMultiSelectDropdown = ({
  noOptionSelectedText,
  noSearchResultsFoundText,
  selectedOptionIds,
  options,
  suggestedOptionsDescription,
  onChangeHandler,
}: {
  noOptionSelectedText: string
  noSearchResultsFoundText: string
  selectedOptionIds: Set<string>
  options: DropdownOption[]
  suggestedOptionsDescription: string
  onChangeHandler: (selectedIds: string[]) => void
}) => {
  const [searchTerm, setSearchTerm] = useState('')

  const optionsById = useMemo(() => {
    return new Map(options.map((option) => [option.id, option]))
  }, [options])

  const selectedOptions = useMemo(() => {
    const selectedOptions: DropdownOption[] = []
    selectedOptionIds.forEach((selectedOptionId) => {
      if (optionsById.has(selectedOptionId)) {
        selectedOptions.push(
          optionsById.get(selectedOptionId) as DropdownOption
        )
      }
    })
    return selectedOptions
  }, [selectedOptionIds, optionsById])

  const unselectedOptions = useMemo(() => {
    return options.filter((o) => !selectedOptionIds.has(o.id))
  }, [options, selectedOptionIds])

  const suggestedOptions = useMemo(() => {
    return unselectedOptions
      .filter((o) => o.count > 0)
      .sort(sortDescByCount)
      .slice(0, 10)
  }, [unselectedOptions])

  const optionsMatchingSearch = useMemo(() => {
    return options
      .filter((o) => textContainsSearchTerms(o.description, [searchTerm]))
      .sort(sortDescByCount)
  }, [searchTerm, options])

  return (
    <ControlledSearchMultiSelect
      name={`${noOptionSelectedText}-filter`}
      // displayLabel overrides the existing logic, which wouldn't work with how this is currently
      // implemented (ControlledSearchMultiSelect calculates the label based off the selected options
      // that are rendered; in the case of searching, only search results render, so it will
      // change the label to only the selected search result options)
      displayLabel={getDropdownButtonLabel(
        selectedOptions,
        noOptionSelectedText
      )}
      search
      value={Array.from(selectedOptionIds)}
      onChange={(value?: string[]) => {
        if (searchTerm) {
          const newSelectedOptions = new Set(selectedOptionIds)
          const newlySelectedSearchOptions = new Set(value)

          optionsMatchingSearch
            .map((o) => o.id)
            .forEach((optionMatchingSearch) => {
              const isSearchedOptionAlreadySelected =
                selectedOptionIds.has(optionMatchingSearch)
              const isOptionMatchingSearchSelected =
                newlySelectedSearchOptions.has(optionMatchingSearch)
              if (
                isSearchedOptionAlreadySelected &&
                !isOptionMatchingSearchSelected
              ) {
                newSelectedOptions.delete(optionMatchingSearch)
              } else if (
                !isSearchedOptionAlreadySelected &&
                isOptionMatchingSearchSelected
              ) {
                newSelectedOptions.add(optionMatchingSearch)
              }
            })

          onChangeHandler(Array.from(newSelectedOptions))
        } else {
          onChangeHandler(value || []) // undefined if empty
        }
        setSearchTerm('')
      }}
      noResultsText={noSearchResultsFoundText}
      searchTerm={searchTerm}
      onChangeSearchTerm={(newTerm) => {
        setSearchTerm(newTerm)
      }}
    >
      {!searchTerm &&
        selectedOptions.length &&
        selectedOptions.map((option) => (
          <Option
            key={option.id}
            value={option.id}
            label={option.description}
            count={option.count}
            group={'Selected'}
          />
        ))}
      {!searchTerm &&
        suggestedOptions.map(({ id, description, count }) => (
          <Option
            key={id}
            value={id}
            label={description}
            count={count}
            selected={false}
            group={suggestedOptionsDescription}
          />
        ))}

      {searchTerm &&
        optionsMatchingSearch?.map(({ id, description, count }) => (
          <Option
            key={id}
            value={id}
            label={description}
            count={count}
            group={'Search results'}
          />
        ))}
    </ControlledSearchMultiSelect>
  )
}

export const mapToOption = (
  staff: StaffShape | ProcedureShape
): DropdownOption =>
  ({
    id: staff.node.id,
    description: staff.node.name,
    count: staff.node.count,
  }) as DropdownOption

export default NumerousMultiSelectDropdown
