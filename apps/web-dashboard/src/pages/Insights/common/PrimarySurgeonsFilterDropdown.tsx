import { useMemo } from 'react'

import { METRICS } from '../types'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import NumerousMultiSelectDropdown, {
  getSuggestedOptionsDescriptionBasedOnTab,
  mapToOption,
} from './NumerousMultiSelectDropdown'

const PrimarySurgeonsFilterDropdown = ({ metric }: { metric: METRICS }) => {
  const { staffIds, onChangeStaff, staffWithCount } =
    useInsightsSearchParams(metric)

  const options = useMemo(
    () => staffWithCount.map(mapToOption),
    [staffWithCount]
  )

  return (
    <NumerousMultiSelectDropdown
      noOptionSelectedText={'Primary Surgeons'}
      noSearchResultsFoundText={'No primary surgeons found'}
      selectedOptionIds={new Set<string>(staffIds)}
      options={options}
      suggestedOptionsDescription={getSuggestedOptionsDescriptionBasedOnTab(
        metric
      )}
      onChangeHandler={(newSelectedPrimarySurgeonUserIds) => {
        onChangeStaff(
          newSelectedPrimarySurgeonUserIds.length === 0
            ? undefined
            : newSelectedPrimarySurgeonUserIds
        )
      }}
    />
  )
}

export default PrimarySurgeonsFilterDropdown
