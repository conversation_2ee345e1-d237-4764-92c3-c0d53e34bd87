import { ApellaDateTimeFormats } from '@apella/component-library'

import { PhaseType } from '../../__generated__/globalTypes'
import { GetPhaseTypesData } from './__generated__'
import { CLEANING_V2_CUTOVER_DATE } from './Turnover/cleaningV2CutoverDate'

export const UNKNOWN_PHASE_DETAILS = {
  type: undefined,
  slug: undefined,
  title: 'Unknown',
  description: undefined,
}

type GetPhaseTypesData_phaseTypes_edges_node =
  GetPhaseTypesData['phaseTypes']['edges'][number]['node']

export const getPhaseDetailsFromType =
  (phaseTypeDetails: GetPhaseTypesData_phaseTypes_edges_node[]) =>
  (
    type: PhaseType
  ): { title: string } & Partial<
    Omit<GetPhaseTypesData_phaseTypes_edges_node, '__typename'>
  > => {
    const phaseDetails = phaseTypeDetails.find((pd) => pd.type === type)

    if (phaseDetails === undefined) {
      return UNKNOWN_PHASE_DETAILS
    }

    return phaseDetails
  }

export const getCleaningAndOpeningDetailsFromType = (type: string) => {
  if (type === 'cleaning') {
    return {
      title: 'Cleaning avg',
      description:
        'The cleaning phase is measured as the period between when a patient wheels out and either the mop is taken out of the room or the back table is opened.\n' +
        '\n' +
        `Mop data is available as of ${CLEANING_V2_CUTOVER_DATE.toLocaleString(ApellaDateTimeFormats.DATE)}.`,
    }
  } else {
    return {
      title: 'Opening avg',
      description:
        'The opening phase is measured as the time between the back table opening and the patient being wheeled into the operating room.',
    }
  }
}
