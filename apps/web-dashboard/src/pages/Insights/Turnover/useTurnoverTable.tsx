import { ReactElement } from 'react'

import { Query } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'
import { DateTime, Duration, DurationLikeObject } from 'luxon'

import {
  Direction,
  formatDuration,
  OrderBy,
  PaginatedTableColumn,
  ApellaDateTimeFormats,
} from '@apella/component-library'
import { PatientClass } from 'src/__generated__/globalTypes'
import { useCaseClassificationTypes } from 'src/components/CaseClassificationTypesFilter'
import { DashComponent } from 'src/components/DashComponent'
import { useTimezone } from 'src/Contexts'
import {
  CASES,
  ROOMS,
  SERVICE_LINES,
  SITES,
  TURNOVER_GOAL,
  TURNOVER_NOTES,
  TURNOVERS,
} from 'src/modules/cube/types/dataCubes'
import { useCaseIdToFollowingCaseDataMap } from 'src/modules/insights/hooks/useTurnoverFollowingCaseData'
import {
  displayCustomerCaseId,
  displayIsAddOn,
  generateListUiDisplay,
} from 'src/pages/Insights/dataHelpers'
import { convertFromUtcString } from 'src/pages/Insights/dateTimeHelpers'
import SiteRoomCell from 'src/pages/Insights/SiteRoomCell'
import { DisplayName, VideoBladeTableData } from 'src/pages/Insights/types'
import { getPatientClassText } from 'src/pages/Schedule/CaseDetails'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'

export interface TurnoverTableData {
  [CASES.CASE_CLASSIFICATION_ID]: string[]
  [CASES.CUSTOMER_CASE_ID]: string
  [ROOMS.NAME]: string
  [SERVICE_LINES.NAME]: string
  [SITES.NAME]: string
  [TURNOVER_GOAL.GOAL]: number | null
  [TURNOVER_GOAL.MAX_MINUTES]: number
  [TURNOVER_NOTES.NOTE]: string
  [TURNOVERS.CLEAN_DURATION_MINUTES]: DurationLikeObject
  [TURNOVERS.DAY_OF_WEEK]: string
  [TURNOVERS.DELAY_LABELS]: string
  [TURNOVERS.FOLLOWING_CASE_ID]: string
  [TURNOVERS.OPEN_DURATION_MINUTES]: DurationLikeObject
  [TURNOVERS.PHASE_ID]: string
  [TURNOVERS.PRECEDING_CASE_ID]: string
  [TURNOVERS.ROOM_ID]: string
  [TURNOVERS.START_TIMESTAMP]: string
  [TURNOVERS.TOTAL_DURATION_MINUTES]: DurationLikeObject
}

export interface TableData extends VideoBladeTableData {
  customerCaseId: string | ReactElement | React.JSX.Element | string[]
  delayLabels: string
  followingCaseActualStartTime?: DateTime
  followingCaseAnesthesiaNames:
    | string
    | ReactElement
    | React.JSX.Element
    | string[]
  followingCaseClassification: string | ReactElement | React.JSX.Element
  // Following Case Data
  followingCaseId: string | undefined
  followingCaseIsAddOn?: string | ReactElement | React.JSX.Element
  followingCaseIsFlipRoom?: boolean
  followingCaseIsFollowing?: boolean
  followingCasePatientClass?: string
  followingCaseProcedureNames:
    | string
    | ReactElement
    | React.JSX.Element
    | string[]
  followingCaseScheduledStartTime?: DateTime
  followingCaseServiceLineName: string | undefined
  followingCaseSurgeonNames:
    | string
    | ReactElement
    | React.JSX.Element
    | string[]
  id: string
  note: string
  site: string
}

const turnoverTableColumns: PaginatedTableColumn<TableData>[] = [
  {
    name: DisplayName.START_TIME,
    selector: 'actualStartTime',
    sortAttribute: TURNOVERS.START_TIMESTAMP,
    formatter: (startTime: DateTime) =>
      startTime.toLocaleString(ApellaDateTimeFormats.DATETIME_WITH_WEEKDAY),
  },
  {
    name: DisplayName.ROOM,
    sortAttribute: ROOMS.NAME,
    contents: (rowData: { site: string; roomName: string }) => (
      <SiteRoomCell site={rowData.site} room={rowData.roomName} />
    ),
  },
  {
    name: DisplayName.ACTUAL_DURATION,
    selector: 'actualDuration',
    sortAttribute: TURNOVERS.TOTAL_DURATION_MINUTES,
    formatter: (duration?: Duration) =>
      duration ? formatDuration(duration) : <DashComponent />,
  },
  {
    name: DisplayName.CLEANING,
    sortAttribute: TURNOVERS.CLEAN_DURATION_MINUTES,
    selector: 'Cleaning',
    formatter: (duration?: Duration) =>
      duration ? formatDuration(duration) : <DashComponent />,
  },
  {
    name: DisplayName.OPENING,
    sortAttribute: TURNOVERS.OPEN_DURATION_MINUTES,
    selector: 'Opening',
    formatter: (duration?: Duration) =>
      duration ? formatDuration(duration) : <DashComponent />,
  },
  {
    name: DisplayName.FOLLOWING_CASE_ID,
    selector: 'followingCaseId',
  },
  {
    name: DisplayName.FOLLOWING_CASE_SURGEONS,
    selector: 'followingCaseSurgeonNames',
  },
  {
    name: DisplayName.FOLLOWING_CASE_PROCEDURES,
    selector: 'followingCaseProcedureNames',
  },
  {
    name: DisplayName.FOLLOWING_CASE_SERVICE_LINE,
    selector: 'followingCaseServiceLineName',
  },
  {
    name: DisplayName.FOLLOWING_CASE_ADD_ON,
    selector: 'followingCaseIsAddOn',
  },
  {
    name: DisplayName.FOLLOWING_CASE_PATIENT_CLASS,
    selector: 'followingCasePatientClass',
    formatter: (patientClass: PatientClass) =>
      getPatientClassText(patientClass) ?? 'Other',
  },
  {
    name: DisplayName.FOLLOWING_CASE_SCHEDULED_START,
    selector: 'followingCaseScheduledStartTime',
    formatter: (followingCaseScheduledStartTime?: DateTime) =>
      followingCaseScheduledStartTime ? (
        followingCaseScheduledStartTime.toLocaleString(
          ApellaDateTimeFormats.DATETIME_WITH_WEEKDAY
        )
      ) : (
        <DashComponent />
      ),
  },
  {
    name: DisplayName.FOLLOWING_CASE_ACTUAL_START,
    selector: 'followingCaseActualStartTime',
    formatter: (followingCaseActualStartTime?: DateTime) =>
      followingCaseActualStartTime ? (
        followingCaseActualStartTime.toLocaleString(
          ApellaDateTimeFormats.DATETIME_WITH_WEEKDAY
        )
      ) : (
        <DashComponent />
      ),
  },
  {
    name: DisplayName.FOLLOWING_CASE_IS_FOLLOWING,
    selector: 'followingCaseIsFollowing',
    formatter: (isFollowing: boolean) => (isFollowing ? 'Yes' : 'No'),
  },
  {
    name: DisplayName.FOLLOWING_CASE_IS_IN_FLIP_ROOM,
    selector: 'followingCaseIsFlipRoom',
    formatter: (isFlipRoom: boolean) => (isFlipRoom ? 'Yes' : 'No'),
  },
  {
    name: DisplayName.FOLLOWING_CASE_ANESTHESIA,
    selector: 'followingCaseAnesthesiaNames',
  },
  {
    name: DisplayName.FOLLOWING_CASE_CLASSIFICATION,
    selector: 'followingCaseClassification',
  },
  {
    name: DisplayName.NOTE,
    selector: 'note',
  },
  {
    name: DisplayName.DELAY_LABELS,
    selector: 'delayLabels',
  },
]

interface HookParams {
  cubeParams: Query
  limit: number
  occludeCleaningData: boolean
  offset: number
  orderBy: OrderBy[] | undefined
}

const useTurnoverDataPipelineData = (
  cubeParams: Query,
  { orderBy, offset, limit }: Pick<HookParams, 'orderBy' | 'offset' | 'limit'>,
  occludeCleaningData: boolean
) => {
  const { timezone } = useTimezone()

  const { caseClassificationTypes } = useCaseClassificationTypes()

  const defaultSort = TURNOVERS.TOTAL_DURATION_MINUTES

  const sortKeys = turnoverTableColumns.map((column) => column.sortAttribute)

  const orderByQuery =
    orderBy && orderBy.length == 1 && sortKeys.includes(orderBy[0].sort)
      ? orderBy[0]
      : { sort: defaultSort, direction: Direction.DESC }

  const dimensions = [
    CASES.CUSTOMER_CASE_ID,
    TURNOVERS.PHASE_ID,
    TURNOVERS.START_TIMESTAMP,
    TURNOVERS.ROOM_ID,
    SERVICE_LINES.NAME,
    ROOMS.NAME,
    SITES.NAME,
    TURNOVERS.TOTAL_DURATION_MINUTES,
    TURNOVERS.CLEAN_DURATION_MINUTES,
    TURNOVERS.OPEN_DURATION_MINUTES,
    TURNOVERS.PRECEDING_CASE_ID,
    TURNOVERS.FOLLOWING_CASE_ID,
    TURNOVER_NOTES.NOTE,
    TURNOVERS.DELAY_LABELS,
    TURNOVER_GOAL.GOAL,
    TURNOVER_GOAL.MAX_MINUTES,
  ]

  const tableQuery: Query = {
    ...cubeParams,
    dimensions,
    offset: offset,
    limit: limit,
    order: {
      [orderByQuery.sort]:
        orderByQuery.direction && orderByQuery.direction !== Direction.ASC
          ? 'desc'
          : 'asc',
    },
  }
  const { isLoading: isLoadingTableData, resultSet: tableData } =
    useCubeQuery<TurnoverTableData>(tableQuery)

  const rawTableData = tableData?.rawData() ?? []

  const followingCaseIds = rawTableData.map((edge) =>
    (edge[TURNOVERS.FOLLOWING_CASE_ID] ?? '').toString()
  )

  const { isLoading: isLoadingClinicalData, caseIdToClinicalData } =
    useCaseIdToFollowingCaseDataMap(isLoadingTableData ? [] : followingCaseIds)

  const data: TableData[] = rawTableData.map((edge) => {
    const cleanDuration = durationFromMinutes(
      edge[TURNOVERS.CLEAN_DURATION_MINUTES] || {}
    )

    const cleaning = {
      Cleaning:
        !occludeCleaningData && cleanDuration.valueOf()
          ? cleanDuration
          : undefined,
    }

    const openDuration = durationFromMinutes(
      edge[TURNOVERS.OPEN_DURATION_MINUTES] || {}
    )
    const opening = {
      Opening: openDuration.valueOf() ? openDuration : undefined,
    }

    const followingCaseClinicalData = caseIdToClinicalData.get(
      edge[TURNOVERS.FOLLOWING_CASE_ID]
    )

    let caseClassificationId = null
    const caseClassificationTypesId =
      followingCaseClinicalData?.caseClassificationTypeId
    if (caseClassificationTypesId) {
      caseClassificationId = caseClassificationTypes?.find(
        (val) => val.id == caseClassificationTypesId.toString()
      )
    }

    return {
      id: (edge[TURNOVERS.PHASE_ID] ?? '').toString(),
      precedingCaseId: (edge[TURNOVERS.PRECEDING_CASE_ID] ?? '').toString(),
      followingCaseId: (edge[TURNOVERS.FOLLOWING_CASE_ID] ?? '').toString(),
      customerCaseId: displayCustomerCaseId(edge[CASES.CUSTOMER_CASE_ID] || ''),
      site: edge[SITES.NAME],
      roomId: edge[TURNOVERS.ROOM_ID],
      roomName: edge[ROOMS.NAME],
      // Cube automatically adds time zone offset to `timeDimensions`, so they are no longer in UTC
      actualStartTime: DateTime.fromISO(edge[TURNOVERS.START_TIMESTAMP], {
        zone: timezone,
      }),
      actualDuration: durationFromMinutes(
        edge[TURNOVERS.TOTAL_DURATION_MINUTES] ?? {}
      ),
      ...cleaning,
      ...opening,
      followingCaseActualStartTime: followingCaseClinicalData?.actualStartTime
        ? convertFromUtcString(
            followingCaseClinicalData.actualStartTime,
            timezone
          )
        : undefined,
      followingCaseScheduledStartTime:
        followingCaseClinicalData?.scheduledStartTime
          ? convertFromUtcString(
              followingCaseClinicalData.scheduledStartTime,
              timezone
            )
          : undefined,
      followingCaseAnesthesiaNames: generateListUiDisplay(
        followingCaseClinicalData?.anesthesiaNames
      ),
      followingCaseProcedureNames: generateListUiDisplay(
        followingCaseClinicalData?.procedureNames
      ),
      followingCaseServiceLineName: followingCaseClinicalData?.serviceLineName,
      followingCaseSurgeonNames: generateListUiDisplay(
        followingCaseClinicalData?.surgeonNames
      ),

      followingCaseClassification: caseClassificationId ? (
        caseClassificationId.name
      ) : (
        <DashComponent />
      ),
      followingCaseIsAddOn: displayIsAddOn(
        followingCaseClinicalData?.caseId,
        followingCaseClinicalData?.isAddOn
      ),
      followingCaseIsFlipRoom: followingCaseClinicalData?.isInFlipRoom,
      followingCaseIsFollowing: followingCaseClinicalData?.isFollowing,
      followingCasePatientClass: followingCaseClinicalData?.patientClass,
      note: edge[TURNOVER_NOTES.NOTE] ?? '',
      delayLabels: edge[TURNOVERS.DELAY_LABELS] ?? '',
      goal: edge[TURNOVER_GOAL.GOAL] ?? null,
      maxMinutes: edge[TURNOVER_GOAL.MAX_MINUTES] ?? null,
    }
  })

  return {
    columns: turnoverTableColumns,
    tableData: data,
    isLoading: isLoadingTableData || isLoadingClinicalData,
  }
}

export const useTurnoverTableData = ({
  limit,
  offset,
  orderBy,
  cubeParams,
  occludeCleaningData,
}: HookParams) => {
  const dataPipelineResults = useTurnoverDataPipelineData(
    cubeParams,
    {
      limit,
      offset,
      orderBy,
    },
    occludeCleaningData
  )

  const noResults = {
    columns: [],
    tableData: [],
    isLoading: false,
  }

  return dataPipelineResults ?? noResults
}

export const useTurnoverCountData = ({ cubeParams }: { cubeParams: Query }) => {
  const { isLoading, resultSet: totalCountData } = useCubeQuery<{
    [key: string]: number
  }>({
    ...cubeParams,
    measures: [TURNOVERS.COUNT],
  })

  return {
    totalCount: totalCountData?.rawData()[0][TURNOVERS.COUNT],
    isLoading,
  }
}
