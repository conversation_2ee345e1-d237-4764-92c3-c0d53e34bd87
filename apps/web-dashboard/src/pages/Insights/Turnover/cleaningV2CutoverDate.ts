import { DateTime } from 'luxon'

import { useTimezone } from '../../../Contexts'

export const CLEANING_V2_CUTOVER_DATE = DateTime.fromObject({
  year: 2025,
  month: 4,
  day: 1,
})

export enum CleaningDataVersion {
  V1,
  V2,
  MIXED,
}

export const useCleaningDataVersion = (
  minTime: DateTime,
  maxTime: DateTime
) => {
  const { timezone } = useTimezone()

  const cleaningV2CutoverDateTime = CLEANING_V2_CUTOVER_DATE.setZone(timezone, {
    keepLocalTime: true,
  })

  return minTime < cleaningV2CutoverDateTime &&
    maxTime < cleaningV2CutoverDateTime
    ? CleaningDataVersion.V1
    : minTime < cleaningV2CutoverDateTime && maxTime > cleaningV2CutoverDateTime
      ? CleaningDataVersion.MIXED
      : CleaningDataVersion.V2
}
