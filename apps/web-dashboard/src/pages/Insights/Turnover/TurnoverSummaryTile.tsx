import { Fragment, useMemo } from 'react'

import { Query } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'
import { DateTime, DurationLikeObject } from 'luxon'

import { ApellaDateTimeFormats } from '@apella/component-library'
import { PhaseType } from 'src/__generated__/globalTypes'
import {
  InsightsSummaryMetric,
  InsightsSummaryMetricInfo,
  InsightsTile,
  MetricsSection,
  MetricInfo,
} from 'src/components/InsightsTile/InsightsTile'
import { METRIC_DIRECTION } from 'src/components/InsightsTile/PercentChangeIndicator'
import { useTimezone } from 'src/Contexts'
import { TURNOVERS } from 'src/modules/cube/types/dataCubes'
import {
  getCleaningAndOpeningDetailsFromType,
  getPhaseDetailsFromType,
} from 'src/pages/Insights/phaseHelpers'
import { formatTimeDimensionDateRange } from 'src/pages/Insights/queries'
import { METRICS } from 'src/pages/Insights/types'
import { useInsightsSearchParams } from 'src/pages/Insights/useInsightsSearchParams'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'

import { usePhaseTypes } from '../usePhaseTypes'
import {
  CLEANING_V2_CUTOVER_DATE,
  CleaningDataVersion,
  useCleaningDataVersion,
} from './cleaningV2CutoverDate'

interface SummaryTileDataAnalytics {
  [TURNOVERS.AVG_DURATION]: DurationLikeObject
  [TURNOVERS.CLEANING_AVG_MINUTES]: DurationLikeObject | null
  [TURNOVERS.CLEANING_COUNT]: string | null
  [TURNOVERS.COUNT]: string
  [TURNOVERS.OPENING_AVG_MINUTES]: DurationLikeObject | null
  [TURNOVERS.OPENING_COUNT]: string | null
}

export interface CommonTileData {
  averageDuration: DurationLikeObject
  cleaningAverage?: DurationLikeObject
  cleaningCount?: string
  count: string
  openingAverage?: DurationLikeObject
  openingCount?: string
}

const TurnoverSummaryTile = (): React.JSX.Element => {
  const { phaseTypes } = usePhaseTypes()
  const { timezone } = useTimezone()

  const { minTime, maxTime, cubeParams } = useInsightsSearchParams(
    METRICS.TURNOVER
  )

  const minDateTime = DateTime.fromISO(minTime)
  const maxDateTime = DateTime.fromISO(maxTime)
  const cleaningDataVersion = useCleaningDataVersion(minDateTime, maxDateTime)
  const occludeCleaningData = cleaningDataVersion === CleaningDataVersion.MIXED
  const diff = maxDateTime.diff(minDateTime)
  const prevMinTime = minDateTime.minus(diff)

  const cleaningV2CutoverDate = CLEANING_V2_CUTOVER_DATE.setZone(timezone, {
    keepLocalTime: true,
  })

  const getAnalyticsData = (
    analyticTurnoverData: SummaryTileDataAnalytics[]
  ): CommonTileData[] => {
    return analyticTurnoverData.map((edge) => ({
      averageDuration: edge[TURNOVERS.AVG_DURATION],
      count: edge[TURNOVERS.COUNT],
      openingAverage: edge[TURNOVERS.OPENING_AVG_MINUTES] ?? undefined,
      cleaningAverage:
        edge[TURNOVERS.CLEANING_AVG_MINUTES] && !occludeCleaningData
          ? edge[TURNOVERS.CLEANING_AVG_MINUTES]
          : undefined,
      openingCount: edge[TURNOVERS.OPENING_COUNT] ?? undefined,
      cleaningCount: edge[TURNOVERS.CLEANING_COUNT] ?? undefined,
    }))
  }

  const turnoverAnalyticsDataQuery: Query = {
    measures: [
      TURNOVERS.AVG_DURATION,
      TURNOVERS.COUNT,
      TURNOVERS.OPENING_AVG_MINUTES,
      TURNOVERS.CLEANING_AVG_MINUTES,
      TURNOVERS.OPENING_COUNT,
      TURNOVERS.CLEANING_COUNT,
    ],
    ...cubeParams,
  }
  const {
    isLoading: isLoadingTurnoverResultSet,
    resultSet: turnoverResultSet,
  } = useCubeQuery<SummaryTileDataAnalytics>(turnoverAnalyticsDataQuery)
  const turnoverData = turnoverResultSet?.rawData()
  const turnoverQuery = turnoverAnalyticsDataQuery
  const {
    isLoading: isLoadingPrevTurnoverResultSet,
    resultSet: prevTurnoverResultSet,
  } = useCubeQuery<SummaryTileDataAnalytics>({
    ...turnoverQuery,
    timeDimensions: [
      {
        dimension: TURNOVERS.START_TIMESTAMP,
        dateRange: formatTimeDimensionDateRange(
          [prevMinTime.toISO(), minTime],
          timezone
        ),
      },
    ],
    timezone: timezone,
  })
  const prevTurnoverData = prevTurnoverResultSet?.rawData()
  const analyticsDataPrev = getAnalyticsData(prevTurnoverData ?? [])

  const phaseDetails = getPhaseDetailsFromType(phaseTypes)(PhaseType.TURNOVER)
  const analyticsData = getAnalyticsData(turnoverData ?? [])

  const summaryTileData = analyticsData[0]

  const previousSummaryTileData = analyticsDataPrev[0]
  const durationMetric = (() => {
    const averageDurationData = summaryTileData
      ? summaryTileData.averageDuration
      : undefined

    const averageDuration = averageDurationData
      ? durationFromMinutes(averageDurationData)
      : undefined
    const previousDurationData = previousSummaryTileData
      ? previousSummaryTileData.averageDuration
      : undefined
    const previousDuration = previousDurationData
      ? durationFromMinutes(previousDurationData)
      : undefined

    return {
      label: `${phaseDetails.title} avg`,
      value: averageDuration,
      description: `The average duration of ${phaseDetails.title.toLowerCase()}s for the selected filters. ${
        phaseDetails.description
      }`,
      previousValue: previousDuration,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
      desiredChangeDirection: METRIC_DIRECTION.NEGATIVE,
    }
  })()

  const count =
    summaryTileData !== undefined ? Number(summaryTileData.count) : undefined

  const countMetric = (() => {
    const previousCount =
      previousSummaryTileData !== undefined
        ? Number(previousSummaryTileData.count)
        : undefined

    return {
      label: `Total ${phaseDetails.title.toLowerCase()}s`,
      value: count,
      description: `The total count of ${phaseDetails.title.toLowerCase()}s for the selected filters. ${
        phaseDetails.description
      }`,
      previousValue: previousCount,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
    }
  })()

  const childPhaseOrdering = [PhaseType.TURNOVER_CLEAN, PhaseType.TURNOVER_OPEN]
  const childCount =
    summaryTileData !== undefined
      ? Math.max(
          Number(summaryTileData.cleaningCount),
          Number(summaryTileData.openingCount)
        )
      : undefined

  const diffMetric = count && childCount ? count - childCount : undefined

  const diffMetricInfo: InsightsSummaryMetricInfo = (() => {
    const cleaning = getPhaseDetailsFromType(phaseTypes)(
      childPhaseOrdering[0]
    ).title.toLowerCase()
    const opening = getPhaseDetailsFromType(phaseTypes)(
      childPhaseOrdering[1]
    ).title.toLowerCase()
    const turnover = phaseDetails.title.toLowerCase()
    return {
      preLabel: `There ${diffMetric !== 1 ? 'were' : 'was'} `,
      value: diffMetric,
      postLabel:
        `${turnover}${diffMetric !== 1 ? 's' : ''} where ${cleaning} and ${opening} phases were not detected. As a ` +
        `result, the ${cleaning} and ${opening} averages may not be reflective of the total ` +
        `${turnover}${diffMetric !== 1 ? 's' : ''} accounted for above.`,
    }
  })()

  const mixedCleanVersionMetricInfo: InsightsSummaryMetricInfo = useMemo(
    () => ({
      preLabel:
        'You are viewing data where some Cleaning phases use mop data and some do not. As a result, the Cleaning data is not shown.' +
        `\n\nMop data is available as of ${cleaningV2CutoverDate.toLocaleString(ApellaDateTimeFormats.DATE)}.`,
      postLabel: '',
    }),
    [cleaningV2CutoverDate]
  )

  const oldCleanVersionMetricInfo: InsightsSummaryMetricInfo = useMemo(
    () => ({
      preLabel:
        'You are viewing data that does not use mop data to calculate Cleaning phases.' +
        `\n\nMop data is available as of ${cleaningV2CutoverDate.toLocaleString(ApellaDateTimeFormats.DATE)}.`,
      postLabel: '',
    }),
    [cleaningV2CutoverDate]
  )

  const info = useMemo(() => {
    if (occludeCleaningData) {
      return [mixedCleanVersionMetricInfo]
    }

    const infoSections = []
    if (diffMetricInfo.value && diffMetricInfo.value > 0) {
      infoSections.push(diffMetricInfo)
    }
    if (cleaningDataVersion === CleaningDataVersion.V1) {
      infoSections.push(oldCleanVersionMetricInfo)
    }

    return infoSections
  }, [
    cleaningDataVersion,
    oldCleanVersionMetricInfo,
    occludeCleaningData,
    mixedCleanVersionMetricInfo,
    diffMetricInfo,
  ])

  const middleMetrics: InsightsSummaryMetric[] = []
  // Generate child phases items from childPhaseDisplaySort
  let avgDurationOpening = undefined
  let avgDurationCleaning = undefined
  if (summaryTileData) {
    avgDurationOpening = summaryTileData.openingAverage
    avgDurationCleaning = summaryTileData.cleaningAverage
    const cleaningDetails = getCleaningAndOpeningDetailsFromType('cleaning')
    const openingDetails = getCleaningAndOpeningDetailsFromType('opening')

    middleMetrics.push({
      label: `${cleaningDetails.title}`,
      value:
        avgDurationCleaning !== undefined
          ? durationFromMinutes(avgDurationCleaning)
          : undefined,
      description: `${cleaningDetails.description}`,
    })

    middleMetrics.push({
      label: `${openingDetails.title}`,
      value:
        avgDurationOpening !== undefined
          ? durationFromMinutes(avgDurationOpening)
          : undefined,
      description: `${openingDetails.description}`,
    })
  }

  const topMetrics = [countMetric]

  const bottomMetrics = [durationMetric]

  const isLoading = isLoadingTurnoverResultSet || isLoadingPrevTurnoverResultSet

  return (
    <InsightsTile
      metricsSections={[
        <MetricsSection key="top" isLoading={isLoading} metrics={topMetrics} />,
        <Fragment key="middle">
          <MetricsSection isLoading={isLoading} metrics={middleMetrics} />
          {info.map((info, idx) => (
            <MetricInfo key={idx} {...info} />
          ))}
        </Fragment>,
        <MetricsSection
          key="bottom"
          isLoading={isLoading}
          metrics={bottomMetrics}
        />,
      ]}
    />
  )
}

export default TurnoverSummaryTile
