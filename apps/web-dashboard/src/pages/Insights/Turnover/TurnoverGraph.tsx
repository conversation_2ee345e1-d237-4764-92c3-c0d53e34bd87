import { useCallback, useMemo } from 'react'

import { TimeDimension } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'
import { minBy, toSafeInteger } from 'lodash'
import { DateTime, Duration, DurationLikeObject } from 'luxon'
import { Label, ReferenceLineProps } from 'recharts'

import {
  ApellaDateTimeFormats,
  ChartData,
  ChartMeta,
} from '@apella/component-library'
import { PhaseType } from 'src/__generated__/globalTypes'
import { InsightsGraph } from 'src/components/InsightsGraph'
import { DimensionDropdown } from 'src/components/InsightsGraph/DimensionDropdown'
import { useServiceLines } from 'src/components/ServiceLinesFilter'
import { useTimezone } from 'src/Contexts'
import { toTimeDimensionGranularity } from 'src/modules/cube/adapters'
import {
  CASE_STAFF_ANESTHESIA,
  CASE_STAFF_SURGEONS,
  PROCEDURES,
  SERVICE_LINES,
  TURNOVERS,
} from 'src/modules/cube/types/dataCubes'
import {
  getProcedureIdByName,
  getProcedureNameById,
  getRoomIdByName,
  getRoomNameById,
  getServiceLineIdByName,
  getServiceLineNameById,
  getStaffIdByName,
  getStaffNameById,
} from 'src/pages/Insights/dataHelpers'
import { getPhaseDetailsFromType } from 'src/pages/Insights/phaseHelpers'
import { formatTimeDimensionDateRange } from 'src/pages/Insights/queries'
import {
  TurnoverMeasures,
  TurnoverMeasuresToOrderBy,
} from 'src/pages/Insights/Turnover/turnoverMeasures'
import { DayOfWeek, METRICS } from 'src/pages/Insights/types'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'
import {
  getBucketEndDate,
  NonTimeDimensions,
  TimeDimensions,
} from 'src/utils/bucketHelpers'

import { useSiteOptions } from '../../../utils/useSiteRoomsOptions'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import { usePhaseTypes } from '../usePhaseTypes'
import {
  CLEANING_V2_CUTOVER_DATE,
  CleaningDataVersion,
  useCleaningDataVersion,
} from './cleaningV2CutoverDate'

interface TurnoverDataAnalytics {
  [CASE_STAFF_ANESTHESIA.STAFF_ID]?: string
  [CASE_STAFF_SURGEONS.STAFF_ID]?: string
  [PROCEDURES.ID]?: string
  [SERVICE_LINES.ID]?: string
  [TURNOVERS.AVG_DURATION]: DurationLikeObject
  [TURNOVERS.COUNT]?: string
  [TURNOVERS.DAY_OF_WEEK]?: string
  [TURNOVERS.ROOM_ID]?: string
  [TURNOVERS.START_TIMESTAMP]?: string
}

export interface CommonGraphData {
  avgDuration: DurationLikeObject
  count?: string
  dayOfWeek?: string
  followingCaseAnesthesiaId?: string
  followingCaseProcedureId?: string
  followingCaseServiceLineId?: string
  followingCaseStaffId?: string
  proceduresId?: string
  roomId?: string
  staffId?: string
  startTime?: string
}

const getDimensionFromResult = (
  data: CommonGraphData,
  timezone: string,
  staffNameById: Map<string, string>,
  anesthesiaStaffNameById: Map<string, string>,
  serviceLineNameById: Map<string, string>,
  procedureNameById: Map<string, string>,
  roomNameById?: Map<string, string>
): string => {
  if (data.startTime) {
    return DateTime.fromISO(data.startTime, {
      zone: timezone,
    }).toISO()
  }

  return (
    data.dayOfWeek ||
    (data.roomId && roomNameById?.get(data.roomId)) ||
    (data.followingCaseStaffId &&
      staffNameById.get(data.followingCaseStaffId)) ||
    (data.followingCaseAnesthesiaId &&
      anesthesiaStaffNameById.get(data.followingCaseAnesthesiaId)) ||
    (data.followingCaseServiceLineId &&
      serviceLineNameById.get(data.followingCaseServiceLineId)) ||
    (data.followingCaseProcedureId &&
      procedureNameById.get(data.followingCaseProcedureId)) ||
    ''
  )
}

const getValueFromResult = (
  measure: TurnoverMeasures,
  data: CommonGraphData
): number | Duration => {
  switch (measure) {
    case TurnoverMeasures.DURATION:
      return data.avgDuration ? durationFromMinutes(data.avgDuration) : 0
    case TurnoverMeasures.COUNT:
    default:
      return toSafeInteger(data.count)
  }
}

export const TurnoverGraph = (): React.JSX.Element => {
  const { phaseTypes } = usePhaseTypes()
  const { timezone } = useTimezone()
  const { serviceLines } = useServiceLines()
  const { sites } = useSiteOptions()

  const {
    minTime,
    maxTime,
    siteIds,
    dimension,
    graphSort,

    cubeParams,
    staffWithCount,
    proceduresWithCount,
    anesthesiaStaffWithCount,

    localOnChangeDateRanges,
    onChangeStaff,
    onChangeAnesthesia,
    onChangeProcedure,
    onChangeRooms,
    onChangeDaysOfWeek,
    onChangeDimension,
    onChangeGraphOrderBy,
    onChangeServiceLines,
  } = useInsightsSearchParams(METRICS.TURNOVER)

  const cleaningV2CutoverDate = CLEANING_V2_CUTOVER_DATE.setZone(timezone, {
    keepLocalTime: true,
  })
  const cleaningDataVersion = useCleaningDataVersion(
    DateTime.fromISO(minTime),
    DateTime.fromISO(maxTime)
  )

  const staffNameById = useMemo(
    () => getStaffNameById(staffWithCount),
    [staffWithCount]
  )
  const staffIdByName = useMemo(
    () => getStaffIdByName(staffWithCount),
    [staffWithCount]
  )

  const anesthesiaStaffNameById = useMemo(
    () => getStaffNameById(anesthesiaStaffWithCount),
    [anesthesiaStaffWithCount]
  )
  const anesthesiaStaffIdByName = useMemo(
    () => getStaffIdByName(anesthesiaStaffWithCount),
    [anesthesiaStaffWithCount]
  )
  const roomNameById = useMemo(
    () => getRoomNameById(sites, siteIds),
    [sites, siteIds]
  )
  const roomIdByName = useMemo(
    () => getRoomIdByName(sites, siteIds),
    [sites, siteIds]
  )
  const procedureNameById = useMemo(
    () => getProcedureNameById(proceduresWithCount),
    [proceduresWithCount]
  )
  const procedureIdsByName = useMemo(
    () => getProcedureIdByName(proceduresWithCount),
    [proceduresWithCount]
  )
  const serviceLineNameById = useMemo(
    () => getServiceLineNameById(serviceLines),
    [serviceLines]
  )
  const serviceLineIdsByName = useMemo(
    () => getServiceLineIdByName(serviceLines),
    [serviceLines]
  )
  const timeDimension = (() => {
    const timeDimension: TimeDimension = {
      dimension: TURNOVERS.START_TIMESTAMP,
      dateRange: formatTimeDimensionDateRange([minTime, maxTime], timezone),
    }

    if (isTimeDimension(dimension)) {
      timeDimension.granularity = toTimeDimensionGranularity(dimension)
    }
    return timeDimension
  })()

  const dimensions = isNonTimeDimension(dimension)
    ? [getCubeJsDimensionFromNonTimeDimension(dimension)]
    : []

  const { resultSet: turnoverData, isLoading } =
    useCubeQuery<TurnoverDataAnalytics>({
      ...cubeParams,
      measures: [TURNOVERS.AVG_DURATION, TURNOVERS.COUNT],
      timeDimensions: [timeDimension],
      dimensions: dimensions,
      order: [TurnoverMeasuresToOrderBy[graphSort as TurnoverMeasures]],
    })
  const phaseDetails = getPhaseDetailsFromType(phaseTypes)(PhaseType.TURNOVER)
  const durationIdentifier = TurnoverMeasures.DURATION
  const countIdentifier = TurnoverMeasures.COUNT

  const chartData = useMemo(() => {
    const finalData = (turnoverData?.rawData() ?? []).map((edge) => ({
      count: edge[TURNOVERS.COUNT],
      dayOfWeek: edge[TURNOVERS.DAY_OF_WEEK] ?? undefined,
      avgDuration: edge[TURNOVERS.AVG_DURATION],
      startTime: edge[TURNOVERS.START_TIMESTAMP] ?? undefined,
      followingCaseStaffId: edge[CASE_STAFF_SURGEONS.STAFF_ID] ?? undefined,
      followingCaseAnesthesiaId:
        edge[CASE_STAFF_ANESTHESIA.STAFF_ID] ?? undefined,
      followingCaseServiceLineId: edge[SERVICE_LINES.ID] ?? undefined,
      followingCaseProcedureId: edge[PROCEDURES.ID] ?? undefined,
      roomId: edge[TURNOVERS.ROOM_ID] ?? undefined,
    }))

    // Convert the data to chart data format
    const chartData: ChartData[] = []
    finalData.forEach((data) => {
      const baseData = {
        dimension: getDimensionFromResult(
          data,
          timezone,
          staffNameById,
          anesthesiaStaffNameById,
          serviceLineNameById,
          procedureNameById,
          roomNameById
        ),
      }
      const durationData = {
        ...baseData,
        identifier: durationIdentifier,
        value: getValueFromResult(TurnoverMeasures.DURATION, data),
      }
      const countData = {
        ...baseData,
        identifier: countIdentifier,
        value: getValueFromResult(TurnoverMeasures.COUNT, data),
      }
      chartData.push(durationData, countData)
    })
    return chartData
  }, [
    turnoverData,
    timezone,
    staffNameById,
    anesthesiaStaffNameById,
    serviceLineNameById,
    procedureNameById,
    roomNameById,
    durationIdentifier,
    countIdentifier,
  ])

  const referenceLines: ReferenceLineProps[] | undefined = useMemo(() => {
    if (cleaningDataVersion !== CleaningDataVersion.MIXED) {
      return undefined
    }

    if (dimension === TimeDimensions.WEEK) {
      const firstTickAfterCutover = minBy(
        chartData.map((datum) => datum.dimension),
        (dateStr) =>
          DateTime.fromISO(dateStr) < cleaningV2CutoverDate
            ? Number.MAX_SAFE_INTEGER
            : DateTime.fromISO(dateStr).toMillis() -
              cleaningV2CutoverDate.toMillis()
      )
      return [
        {
          x: firstTickAfterCutover,
          label: (
            <Label
              value={`Mop data available as of ${cleaningV2CutoverDate.toLocaleString(ApellaDateTimeFormats.DATE_WITHOUT_YEAR)}`}
              position={'insideTop'}
            />
          ),
          position: 'middle',
        },
      ]
    } else if (
      dimension === TimeDimensions.DAY ||
      dimension === TimeDimensions.MONTH
    ) {
      return [
        {
          x: cleaningV2CutoverDate.toISO(),
          label: (
            <Label value={'Mop data available'} position={'insideTopLeft'} />
          ),
          position: 'start',
        },
      ]
    }

    return []
  }, [chartData, cleaningV2CutoverDate, dimension, cleaningDataVersion])

  // When clicking on a graph group, change the desired filters.
  // Transform the bar group label that the component passes back to the ID required for filtering.
  const onClickBarGroup = useCallback(
    (dimensionValue: string) => {
      if (dimension in TimeDimensions) {
        const startDate = new Date(dimensionValue)
        const endDate = getBucketEndDate(startDate, dimension as TimeDimensions)
        localOnChangeDateRanges([startDate, endDate])
      } else if (dimension == NonTimeDimensions.DAYOFWEEK) {
        onChangeDaysOfWeek([dimensionValue.toLocaleLowerCase()] as DayOfWeek[])
        localOnChangeDateRanges([new Date(minTime), new Date(maxTime)])
      } else if (dimension === NonTimeDimensions.OR) {
        onChangeRooms(
          roomIdByName.get(dimensionValue)
            ? [roomIdByName.get(dimensionValue)!]
            : []
        )
      } else if (dimension === NonTimeDimensions.SURGEON) {
        onChangeStaff(staffIdByName.get(dimensionValue))
      } else if (dimension === NonTimeDimensions.ANESTHESIA) {
        onChangeAnesthesia(anesthesiaStaffIdByName.get(dimensionValue))
      } else if (dimension === NonTimeDimensions.PROCEDURE) {
        onChangeProcedure(procedureIdsByName.get(dimensionValue))
      } else if (dimension === NonTimeDimensions.SERVICE_LINE) {
        onChangeServiceLines(serviceLineIdsByName.get(dimensionValue))
      }

      if (dimension in NonTimeDimensions) {
        // For non-time dimensions, filter by dimension then refresh to a by-time graph
        // (triggering localOnChangeDateRanges will update the bucket accordingly: i.e Day if last month, Week if 2 months, etc.)
        localOnChangeDateRanges([new Date(minTime), new Date(maxTime)])
      }
    },
    [
      dimension,
      localOnChangeDateRanges,
      onChangeDaysOfWeek,
      minTime,
      maxTime,
      onChangeRooms,
      roomIdByName,
      onChangeStaff,
      staffIdByName,
      onChangeAnesthesia,
      anesthesiaStaffIdByName,
      onChangeProcedure,
      procedureIdsByName,
      serviceLineIdsByName,
      onChangeServiceLines,
    ]
  )

  const chartMetaData: ChartMeta = {
    [countIdentifier]: {
      hide: true,
    },
    [durationIdentifier]: {
      hide: false,
    },
  }

  return (
    <InsightsGraph
      dimension={dimension}
      data={chartData}
      meta={chartMetaData}
      isLoading={isLoading}
      minTime={minTime}
      maxTime={maxTime}
      title={phaseDetails.title}
      timezone={timezone}
      onClickBarGroup={onClickBarGroup}
      graphSort={graphSort}
      onChangeGraphSort={onChangeGraphOrderBy}
      measures={Object.values(TurnoverMeasures)}
      referenceLines={referenceLines}
    >
      <DimensionDropdown
        dimension={dimension}
        onChangeDimension={onChangeDimension}
      />
    </InsightsGraph>
  )
}

const getCubeJsDimensionFromNonTimeDimension = (
  nonTimeDimension: NonTimeDimensions
): keyof TurnoverDataAnalytics => {
  switch (nonTimeDimension) {
    case NonTimeDimensions.DAYOFWEEK:
      return TURNOVERS.DAY_OF_WEEK
    case NonTimeDimensions.OR:
      return TURNOVERS.ROOM_ID
    case NonTimeDimensions.SURGEON:
      return CASE_STAFF_SURGEONS.STAFF_ID
    case NonTimeDimensions.ANESTHESIA:
      return CASE_STAFF_ANESTHESIA.STAFF_ID
    case NonTimeDimensions.PROCEDURE:
      return PROCEDURES.ID
    case NonTimeDimensions.SERVICE_LINE:
      return SERVICE_LINES.ID
    default:
      throw new Error(`Unsupported non-time dimension of: ${nonTimeDimension}`)
  }
}

const isTimeDimension = (dimension: string): dimension is TimeDimensions =>
  dimension in TimeDimensions

const isNonTimeDimension = (
  dimension: string
): dimension is NonTimeDimensions => dimension in NonTimeDimensions
