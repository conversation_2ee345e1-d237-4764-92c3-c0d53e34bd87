import { useCallback, useMemo, useState } from 'react'

import { DateTime } from 'luxon'

import {
  CsvExportButton,
  FlexContainer,
  FlexItem,
  OrderBy,
  PaginatedTableColumn,
  remSpacing,
  Tile,
} from '@apella/component-library'
import { ColumnsPicker } from 'src/components/ColumnsPicker'
import { RemotePaginatedTableWithVideoBlade } from 'src/components/RemotePaginatedTableWithVideoBlade'
import {
  useTurnoverTableData,
  TableData,
  useTurnoverCountData,
} from 'src/pages/Insights/Turnover/useTurnoverTable'
import { useTurnoverTableExport } from 'src/pages/Insights/Turnover/useTurnoverTableExport'
import { DisplayName, METRICS } from 'src/pages/Insights/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

import { Direction } from '../../../__generated__/globalTypes'
import { TURNOVERS } from '../../../modules/cube/types/dataCubes'
import useSortOrderConversion from '../../../utils/sortOrderHelpers'
import { roughlyGreaterThanAYearDifference } from '../dateTimeHelpers'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import { usePagingState } from '../usePagingState'
import {
  CleaningDataVersion,
  useCleaningDataVersion,
} from './cleaningV2CutoverDate'

const mandatoryColumnNames = [
  DisplayName.START_TIME,
  DisplayName.ROOM,
  DisplayName.ACTUAL_DURATION,
].map((name) => name.toString())

const selectedOnLoadColumnNames = [
  DisplayName.CLEANING,
  DisplayName.OPENING,
  DisplayName.FOLLOWING_CASE_SURGEONS,
  DisplayName.FOLLOWING_CASE_PROCEDURES,
  DisplayName.FOLLOWING_CASE_SERVICE_LINE,
  DisplayName.FOLLOWING_CASE_ADD_ON,
  DisplayName.FOLLOWING_CASE_PATIENT_CLASS,
].map((name) => name.toString())

const DEFAULT_ORDER_BY_STATE: OrderBy[] | undefined = [
  {
    sort: TURNOVERS.TOTAL_DURATION_MINUTES,
    direction: Direction.DESC,
  },
]

const TurnoverTable = (): React.JSX.Element => {
  const {
    paramPrefix: TurnoverPrefix,
    loggingState,
    minTime,
    maxTime,
    cubeParams,
  } = useInsightsSearchParams(METRICS.TURNOVER)
  const eventsLogger = useAnalyticsEventLogger()

  const ORDER_BY = `${TurnoverPrefix}_orderBy`

  const [orderByState, setOrderByState] = useSortOrderConversion(
    ORDER_BY,
    DEFAULT_ORDER_BY_STATE
  )

  const onChangeSort = useCallback(
    (sortOrder: OrderBy[] | undefined) => {
      const newState = {
        ...loggingState,
        orderBy: sortOrder,
      }
      setOrderByState(sortOrder)
      eventsLogger(EVENTS.SORT_TURNOVER_TABLE, newState)
    },
    [setOrderByState, eventsLogger, loggingState]
  )

  const { isLoading: isLoadingTotalCountData, totalCount } =
    useTurnoverCountData({ cubeParams })
  const occludeCleaningData =
    useCleaningDataVersion(
      DateTime.fromISO(minTime),
      DateTime.fromISO(maxTime)
    ) === CleaningDataVersion.MIXED

  const {
    hasNextPage,
    hasPreviousPage,
    onNextPage,
    onPreviousPage,
    pagingState: { offset, limit },
  } = usePagingState({
    totalCount,
    loggingState,
    event: EVENTS.PAGE_TURNOVER_TABLE,
  })

  const { columns, tableData, isLoading } = useTurnoverTableData({
    orderBy: orderByState,
    offset,
    limit,
    cubeParams,
    occludeCleaningData,
  })

  const sortKeys = columns.map((column) => column.sortAttribute)

  const mandatoryColumns = useMemo(
    () => columns.filter((col) => mandatoryColumnNames.includes(col.name)),
    [columns]
  )

  const selectableColumns = useMemo(
    () => columns.filter((col) => !mandatoryColumnNames.includes(col.name)),
    [columns]
  )

  const [selectedColumns, setSelectedColumns] = useState<
    PaginatedTableColumn<TableData>[]
  >(columns.filter((col) => selectedOnLoadColumnNames.includes(col.name)))

  const handleSelectedColumnsChange = (colNames?: string[]) => {
    setSelectedColumns(
      columns.filter((col) => colNames?.includes(col.name)) ?? []
    )
    eventsLogger(EVENTS.CHANGE_TURNOVER_TABLE_COLUMNS, {
      columns: colNames,
    })
  }
  const exportData = useTurnoverTableExport({
    orderBy: orderByState,
    sortKeys,
    cubeParams,
  })

  const csvExportButtonDisabled = useMemo(
    () =>
      roughlyGreaterThanAYearDifference(new Date(minTime), new Date(maxTime)),
    [maxTime, minTime]
  )

  return (
    <Tile gutter={remSpacing.large}>
      <FlexContainer direction="column">
        <FlexItem style={{ display: 'flex', marginLeft: 'auto' }}>
          <div style={{ marginRight: remSpacing.medium }}>
            <ColumnsPicker
              label="Columns"
              name="column-picker"
              bulkSelect={true}
              columnNames={selectableColumns.map((col) => col.name)}
              selectedColumnNames={selectedColumns.map((col) => col.name)}
              onChange={handleSelectedColumnsChange}
            ></ColumnsPicker>
          </div>
          <CsvExportButton
            disabled={csvExportButtonDisabled}
            tooltip={
              csvExportButtonDisabled
                ? 'In order to export data, please adjust filters to a time period covering a year or less.'
                : undefined
            }
            data={exportData}
            onClick={() =>
              eventsLogger(EVENTS.EXPORT_TURNOVER_TABLE, loggingState)
            }
            filename="turnovers.csv"
          />
        </FlexItem>
        <FlexItem>
          <RemotePaginatedTableWithVideoBlade
            columns={[...mandatoryColumns, ...selectedColumns]}
            data={tableData}
            isLoading={isLoading || isLoadingTotalCountData}
            isMultiSort={false}
            paginationType="minimal"
            sortOrder={orderByState ?? undefined}
            hasNextPage={hasNextPage}
            hasPreviousPage={hasPreviousPage}
            onNextPageClicked={onNextPage}
            onPreviousPageClicked={onPreviousPage}
            onChangeSort={(sortOrder) => onChangeSort(sortOrder)}
            metric={METRICS.TURNOVER}
          />
        </FlexItem>
      </FlexContainer>
    </Tile>
  )
}

export default TurnoverTable
