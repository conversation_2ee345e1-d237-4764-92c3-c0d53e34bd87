import { Query } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'
import { DateTime } from 'luxon'

import {
  Direction,
  OrderBy,
  ApellaDateTimeFormats,
} from '@apella/component-library'
import { PatientClass } from 'src/__generated__/globalTypes'
import { useCaseClassificationTypes } from 'src/components/CaseClassificationTypesFilter'
import { useTimezone } from 'src/Contexts'
import {
  CASES,
  ROOMS,
  SERVICE_LINES,
  SITES,
  TURNOVERS,
} from 'src/modules/cube/types/dataCubes'
import { useCaseIdToFollowingCaseDataMap } from 'src/modules/insights/hooks/useTurnoverFollowingCaseData'
import {
  displayCustomerCaseIdCsv,
  displayIsAddOn,
  generateListCsvDisplay,
} from 'src/pages/Insights/dataHelpers'
import {
  convertDurationToMinutesString,
  convertFromUtcString,
} from 'src/pages/Insights/dateTimeHelpers'
import { TurnoverTableData } from 'src/pages/Insights/Turnover/useTurnoverTable'
import { DisplayName } from 'src/pages/Insights/types'
import { getPatientClassText } from 'src/pages/Schedule/CaseDetails'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'

const useTurnoverDataPipelineData = ({
  orderBy,
  sortKeys,
  cubeParams,
}: {
  cubeParams: Query
  orderBy?: OrderBy[]
  sortKeys: (string | undefined)[]
}) => {
  const { timezone } = useTimezone()

  const defaultSort = TURNOVERS.TOTAL_DURATION_MINUTES
  const orderByQuery =
    orderBy && orderBy.length == 1 && sortKeys.includes(orderBy[0].sort)
      ? orderBy[0]
      : { sort: defaultSort, direction: Direction.DESC }

  const tableQuery: Query = {
    ...cubeParams,
    dimensions: [
      CASES.CUSTOMER_CASE_ID,
      TURNOVERS.PHASE_ID,
      TURNOVERS.START_TIMESTAMP,
      TURNOVERS.DAY_OF_WEEK,
      TURNOVERS.ROOM_ID,
      SERVICE_LINES.NAME,
      ROOMS.NAME,
      SITES.NAME,
      TURNOVERS.TOTAL_DURATION_MINUTES,
      TURNOVERS.CLEAN_DURATION_MINUTES,
      TURNOVERS.OPEN_DURATION_MINUTES,
      TURNOVERS.FOLLOWING_CASE_ID,
    ],
    order: {
      [orderByQuery.sort]:
        orderByQuery.direction && orderByQuery.direction !== Direction.ASC
          ? 'desc'
          : 'asc',
    },
  }

  const { resultSet: tableData, isLoading: isLoadingTableData } =
    useCubeQuery<TurnoverTableData>(tableQuery)
  const { caseClassificationTypes } = useCaseClassificationTypes()

  const rawTableData = tableData?.rawData() ?? []

  const followingCaseIds = rawTableData.map((edge) =>
    (edge[TURNOVERS.FOLLOWING_CASE_ID] ?? '').toString()
  )

  const { caseIdToClinicalData } = useCaseIdToFollowingCaseDataMap(
    isLoadingTableData ? [] : followingCaseIds
  )

  const data = rawTableData.map((edge) => {
    const cleanDuration = edge[TURNOVERS.CLEAN_DURATION_MINUTES] || {}

    const openDuration = edge[TURNOVERS.OPEN_DURATION_MINUTES] || {}

    const clinicalData = caseIdToClinicalData.get(
      edge[TURNOVERS.FOLLOWING_CASE_ID]
    )

    let caseClassificationId = null
    const caseClassificationTypesId = clinicalData?.caseClassificationTypeId
    if (caseClassificationTypesId) {
      caseClassificationId = caseClassificationTypes?.find(
        (val) => val.id == caseClassificationTypesId.toString()
      )
    }

    return {
      [DisplayName.CUSTOMER_CASE_ID]: displayCustomerCaseIdCsv(
        edge[CASES.CUSTOMER_CASE_ID]
      ),
      [DisplayName.DAY]: edge[TURNOVERS.DAY_OF_WEEK],
      // Cube automatically adds time zone offset to `timeDimensions`, so they are no longer in UTC
      [DisplayName.START]: DateTime.fromISO(edge[TURNOVERS.START_TIMESTAMP], {
        zone: timezone,
      }).toLocaleString(ApellaDateTimeFormats.DATETIME),
      [DisplayName.ROOMS]: edge[ROOMS.NAME],
      [DisplayName.ACTUAL_DURATION]: convertDurationToMinutesString(
        durationFromMinutes(edge[TURNOVERS.TOTAL_DURATION_MINUTES] ?? {})
      ),
      'Cleaning (mins)': convertDurationToMinutesString(
        durationFromMinutes(cleanDuration)
      ),
      'Opening (mins)': convertDurationToMinutesString(
        durationFromMinutes(openDuration)
      ),
      [DisplayName.FOLLOWING_CASE_SURGEONS]: generateListCsvDisplay(
        clinicalData?.surgeonNames
      ),
      [DisplayName.FOLLOWING_CASE_PROCEDURES]: generateListCsvDisplay(
        clinicalData?.procedureNames
      ),
      [DisplayName.FOLLOWING_CASE_SERVICE_LINE]: edge[SERVICE_LINES.NAME],
      [DisplayName.FOLLOWING_CASE_ADD_ON]: displayIsAddOn(
        clinicalData?.caseId,
        clinicalData?.isAddOn
      ),
      [DisplayName.FOLLOWING_CASE_PATIENT_CLASS]:
        getPatientClassText(clinicalData?.patientClass as PatientClass) ??
        'Other',
      [DisplayName.FOLLOWING_CASE_ACTUAL_START]: clinicalData?.actualStartTime
        ? convertFromUtcString(clinicalData.actualStartTime, timezone)
        : '',
      [DisplayName.FOLLOWING_CASE_SCHEDULED_START]:
        clinicalData?.scheduledStartTime
          ? convertFromUtcString(clinicalData.scheduledStartTime, timezone)
          : '',
      [DisplayName.FOLLOWING_CASE_IS_FOLLOWING]: clinicalData?.isFollowing
        ? 'Yes'
        : 'No',
      [DisplayName.FOLLOWING_CASE_IS_IN_FLIP_ROOM]: clinicalData?.isInFlipRoom
        ? 'Yes'
        : 'No',
      [DisplayName.FOLLOWING_CASE_ANESTHESIA]: generateListCsvDisplay(
        clinicalData?.anesthesiaNames
      ),
      [DisplayName.FOLLOWING_CASE_CLASSIFICATION]: caseClassificationId
        ? caseClassificationId.name
        : '-',
    }
  })

  return data
}

export const useTurnoverTableExport = (
  params: Parameters<typeof useTurnoverDataPipelineData>[0]
) => {
  const pipelineExportData = useTurnoverDataPipelineData(params)

  return pipelineExportData ?? []
}
