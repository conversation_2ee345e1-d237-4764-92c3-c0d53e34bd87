import { useFlags } from 'launchdarkly-react-client-sdk'

import { Option, SingleSelect } from '@apella/component-library'
import { CaseClassificationTypesFilter } from 'src/components/CaseClassificationTypesFilter'
import { DayOfWeekFilter } from 'src/components/DayOfWeekFilter'
import { DurationFilter } from 'src/components/DurationFilter'
import { FlipRoomFilter } from 'src/components/FlipRoomFilter'
import { InsightsDatePicker } from 'src/components/InsightsDatePicker'
import { FilterBarContainer } from 'src/components/PageContentTemplate'
import { ProceduresFilter } from 'src/components/ProceduresFilter'
import { ResetFilters } from 'src/components/ResetFilters'
import { ServiceLinesFilterWithCount } from 'src/components/ServiceLinesFilter'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { StaffFilter } from 'src/components/StaffFilter'
import { TimePeriodFilter } from 'src/components/TimePeriodFilter'
import { TURNOVERS } from 'src/modules/cube/types/dataCubes'
import AnesthesiaFilterDropdown from 'src/pages/Insights/common/AnesthesiaFilterDropdown'
import PrimarySurgeonsFilterDropdown from 'src/pages/Insights/common/PrimarySurgeonsFilterDropdown'
import ProcedureFilterDropdown from 'src/pages/Insights/common/ProceduresFilterDropdown'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { WebDashboardFeatureFlagSet } from '../../../modules/feature/types'
import { MetricsContainer } from '../MetricsContainer'
import { METRICS } from '../types'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import { TurnoverExclusionOptions } from './turnoverExclusionOptions'
import { TurnoverGraph } from './TurnoverGraph'
import TurnoverSummaryTile from './TurnoverSummaryTile'
import TurnoverTable from './TurnoverTable'

export const Turnover = () => {
  const { enablePerformantFilterDropdownsOnInsightsPageTabs } =
    useFlags<WebDashboardFeatureFlagSet>()

  const {
    minTime,
    maxTime,
    staffIds,
    anesthesiaIds,
    serviceLineIds,
    procedureIds,
    siteIds,
    roomIds,
    daysOfWeek,
    durationRangeMinutes,
    showFlipRooms,
    timePeriod,
    caseClassificationTypesIds,
    turnoverExclusionOption,

    showResetFiltersButton,
    cubeParams: turnoverQueryParams,
    staffWithCount,
    anesthesiaStaffWithCount,
    proceduresWithCount,

    localOnChangeDateRanges,
    onChangeStaff,
    onChangeAnesthesia,
    onChangeServiceLines,
    onChangeProcedure,
    onChangeSites,
    onChangeRooms,
    onChangeDaysOfWeek,
    onChangeDurationRange,
    onChangeFlipRoom,
    onChangeTimePeriod,
    onChangeCaseClassificationTypesId,
    onChangeTurnoverExclusionOption,
    resetActions,
  } = useInsightsSearchParams(METRICS.TURNOVER)

  const { sites } = useSiteOptions()
  const rooms = useRoomOptions()

  return (
    <>
      <FilterBarContainer>
        <InsightsDatePicker
          minTime={minTime}
          maxTime={maxTime}
          onChangeDateRanges={localOnChangeDateRanges}
        />
        <DayOfWeekFilter selected={daysOfWeek} onChange={onChangeDaysOfWeek} />
        <TimePeriodFilter
          timePeriod={timePeriod}
          onChangeTimePeriod={onChangeTimePeriod}
        />
        <FlipRoomFilter selected={showFlipRooms} onChange={onChangeFlipRoom} />
        <SitesRoomsFilter
          sites={sites}
          selectedSiteIds={siteIds}
          onChangeSites={onChangeSites}
          rooms={rooms}
          selectedRoomIds={roomIds}
          onChangeRooms={onChangeRooms}
          multipleSites
          bulkSelectSites
          bulkSelectRooms
        />
        {!enablePerformantFilterDropdownsOnInsightsPageTabs && (
          <>
            <StaffFilter
              items={staffWithCount}
              selectedIds={staffIds}
              onChange={onChangeStaff}
              bulkSelect
            />
            <StaffFilter
              label="Anesthesia"
              items={anesthesiaStaffWithCount}
              selectedIds={anesthesiaIds}
              onChange={onChangeAnesthesia}
              bulkSelect
            />
            <CaseClassificationTypesFilter
              value={caseClassificationTypesIds}
              onChange={onChangeCaseClassificationTypesId}
            />
            <ServiceLinesFilterWithCount
              measure={TURNOVERS.COUNT}
              value={serviceLineIds}
              onChange={onChangeServiceLines}
              cubeParams={turnoverQueryParams}
            />
            <ProceduresFilter
              items={proceduresWithCount}
              selectedIds={procedureIds}
              onChange={onChangeProcedure}
              bulkSelect
            />
          </>
        )}
        {enablePerformantFilterDropdownsOnInsightsPageTabs && (
          <>
            <PrimarySurgeonsFilterDropdown metric={METRICS.TURNOVER} />
            <AnesthesiaFilterDropdown metric={METRICS.TURNOVER} />
            <CaseClassificationTypesFilter
              value={caseClassificationTypesIds}
              onChange={onChangeCaseClassificationTypesId}
            />
            <ServiceLinesFilterWithCount
              measure={TURNOVERS.COUNT}
              value={serviceLineIds}
              onChange={onChangeServiceLines}
              cubeParams={turnoverQueryParams}
            />
            <ProcedureFilterDropdown metric={METRICS.TURNOVER} />
          </>
        )}

        <DurationFilter
          value={durationRangeMinutes}
          onChange={onChangeDurationRange}
        />
        <TurnoverExclusionFilter
          selected={turnoverExclusionOption}
          onChange={onChangeTurnoverExclusionOption}
        />
        {showResetFiltersButton && <ResetFilters resetActions={resetActions} />}
      </FilterBarContainer>
      <MetricsContainer
        graph={<TurnoverGraph />}
        summaryTile={<TurnoverSummaryTile />}
        table={<TurnoverTable />}
      />
    </>
  )
}

const TurnoverExclusionFilter = ({
  selected,
  onChange,
}: {
  selected: TurnoverExclusionOptions
  onChange: () => void
}) => {
  return (
    <SingleSelect
      name={'turnoverDelay'}
      label={'Exclusions'}
      value={selected}
      onChange={onChange}
    >
      <Option label={TurnoverExclusionOptions.ALL} value={undefined} />
      <Option
        label={TurnoverExclusionOptions.EXCLUDED}
        value={TurnoverExclusionOptions.EXCLUDED}
      />
      <Option
        label={TurnoverExclusionOptions.NOT_EXCLUDED}
        value={TurnoverExclusionOptions.NOT_EXCLUDED}
      />
    </SingleSelect>
  )
}
