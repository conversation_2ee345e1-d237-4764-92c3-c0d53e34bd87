import { ReactElement, useCallback, useMemo, useState } from 'react'

import { DateTime, Duration } from 'luxon'

import {
  CsvExportButton,
  Direction,
  FlexContainer,
  FlexItem,
  OrderBy,
  PaginatedTableColumn,
  remSpacing,
  Tile,
  ApellaDateTimeFormats,
} from '@apella/component-library'
import { ColumnsPicker } from 'src/components/ColumnsPicker'
import { RemotePaginatedTableWithVideoBlade } from 'src/components/RemotePaginatedTableWithVideoBlade'
import { useCaseIdToClinicalDataMap } from 'src/modules/insights/hooks/useClinicalData'
import {
  displayCustomerCaseId,
  generateListUiDisplay,
} from 'src/pages/Insights/dataHelpers'
import { roughlyGreaterThanAYearDifference } from 'src/pages/Insights/dateTimeHelpers'
import { FirstCaseSortDimensions } from 'src/pages/Insights/FirstCaseStart/FirstCaseSortDimensions'
import { formatMinutesLateDuration } from 'src/pages/Insights/FirstCaseStart/formatMinutesLateDuration'
import { useFirstCaseCountData } from 'src/pages/Insights/FirstCaseStart/hooks/useFirstCaseCountData'
import useFirstCaseStartTableExport from 'src/pages/Insights/FirstCaseStart/hooks/useFirstCaseStartTableExport'
import useFirstCaseTableData from 'src/pages/Insights/FirstCaseStart/hooks/useFirstCaseTableData'
import SiteRoomCell from 'src/pages/Insights/SiteRoomCell'
import {
  DisplayName,
  METRICS,
  VideoBladeTableData,
} from 'src/pages/Insights/types'
import { useInsightsSearchParams } from 'src/pages/Insights/useInsightsSearchParams'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import useSortOrderConversion from 'src/utils/sortOrderHelpers'

import { usePagingState } from '../usePagingState'

const mandatoryColumnNames = [DisplayName.CUSTOMER_CASE_ID].map((name) =>
  name.toString()
)

const selectedOnLoadColumnNames = [
  DisplayName.ROOM,
  DisplayName.PRIMARY_SURGEONS,
  DisplayName.PROCEDURES,
  DisplayName.SCHEDULED_START,
  DisplayName.ACTUAL_START_TIME,
  DisplayName.MINUTES_LATE,
].map((name) => name.toString())

const columns: PaginatedTableColumn<TableData>[] = [
  {
    name: DisplayName.CUSTOMER_CASE_ID,
    selector: 'customerCaseId',
  },
  {
    name: DisplayName.ROOM,
    sortAttribute: FirstCaseSortDimensions.ROOM_NAME,
    contents: (rowData: { siteName: string; roomName: string }) => (
      <SiteRoomCell site={rowData.siteName} room={rowData.roomName} />
    ),
  },
  {
    name: DisplayName.PRIMARY_SURGEONS,
    selector: 'surgeonNames',
  },
  {
    name: DisplayName.ANESTHESIA,
    selector: 'anesthesiaNames',
  },
  {
    name: DisplayName.SERVICE_LINE,
    selector: 'serviceLineName',
  },
  {
    name: DisplayName.PROCEDURES,
    selector: 'procedureNames',
  },

  {
    name: DisplayName.SCHEDULED_START,
    selector: 'scheduledStartTime',
    sortAttribute: FirstCaseSortDimensions.SCHEDULED_START_TIME,
    formatter: (startTime: DateTime) =>
      startTime.toLocaleString(ApellaDateTimeFormats.DATETIME_WITH_WEEKDAY),
  },
  {
    name: DisplayName.ACTUAL_START_TIME,
    selector: 'actualStartTime',
    sortAttribute: FirstCaseSortDimensions.ACTUAL_START_TIME,
    formatter: (startTime: DateTime) =>
      startTime.toLocaleString(
        ApellaDateTimeFormats.DATETIME_SECONDS_WITH_WEEKDAY
      ),
  },
  {
    name: DisplayName.MINUTES_LATE,
    selector: 'lateMinutes',
    sortAttribute: FirstCaseSortDimensions.LATE_MINUTES,
    formatter: (duration: Duration) => formatMinutesLateDuration(duration),
  },
]

export interface TableData extends VideoBladeTableData {
  actualStartTime: DateTime
  anesthesiaNames: string | ReactElement | React.JSX.Element
  lateMinutes: Duration
  procedureNames: string | ReactElement | React.JSX.Element
  scheduledStartTime: DateTime
  siteName: string
  surgeonNames: string | ReactElement | React.JSX.Element
}

const FirstCaseStartTable = (): React.JSX.Element => {
  const {
    paramPrefix: FirstCaseStartPrefix,
    loggingState,
    minTime,
    maxTime,
    cubeParams,
  } = useInsightsSearchParams(METRICS.FIRST_CASE_START)

  const eventsLogger = useAnalyticsEventLogger()
  const ORDER_BY = `${FirstCaseStartPrefix}_orderBy`

  const [orderByState, setOrderByState] = useSortOrderConversion(
    ORDER_BY,
    DEFAULT_ORDER_BY_STATE
  )

  // Handlers
  const onChangeSort = useCallback(
    (sortOrder: OrderBy[] | undefined) => {
      const newState = {
        ...loggingState,
        orderBy: sortOrder,
      }
      setOrderByState(sortOrder)
      eventsLogger(EVENTS.SORT_FIRST_CASE_START_TABLE, newState)
    },
    [setOrderByState, eventsLogger, loggingState]
  )

  const sortKeys = columns.map((column) => column.sortAttribute)
  if (
    orderByState &&
    orderByState.length == 1 &&
    !sortKeys.includes(orderByState[0].sort)
  ) {
    // If the sort passed in is not a known sort, set it to a default sort. This prevents CubeJS
    // from failing when there's an unknown sort order.
    onChangeSort([
      {
        sort: FirstCaseSortDimensions.LATE_MINUTES,
        direction: Direction.DESC,
      },
    ])
  }

  const mandatoryColumns = useMemo(
    () => columns.filter((col) => mandatoryColumnNames.includes(col.name)),
    []
  )

  const selectableColumns = useMemo(
    () => columns.filter((col) => !mandatoryColumnNames.includes(col.name)),
    []
  )

  const [selectedColumns, setSelectedColumns] = useState<
    PaginatedTableColumn<TableData>[]
  >(columns.filter((col) => selectedOnLoadColumnNames.includes(col.name)))

  const handleSelectedColumnsChange = (colNames?: string[]) => {
    setSelectedColumns(
      columns.filter((col) => colNames?.includes(col.name)) ?? []
    )
  }
  const { isLoading: isLoadingTotalCount, totalCount } =
    useFirstCaseCountData(cubeParams)

  const {
    hasNextPage,
    hasPreviousPage,
    onNextPage,
    onPreviousPage,
    pagingState: { offset, limit },
  } = usePagingState({
    totalCount,
    loggingState,
    event: EVENTS.SORT_FIRST_CASE_START_TABLE,
  })

  const { isLoading: isLoadingRawTableData, tableData: rawTableData } =
    useFirstCaseTableData({
      offset,
      limit,
      orderBy: orderByState,
      sortKeys,
      cubeParams,
    })

  const { isLoading: isLoadingRawClinicalData, caseIdToClinicalData } =
    useCaseIdToClinicalDataMap(rawTableData.map((edge) => edge.caseId))

  const tableData: TableData[] = useMemo(() => {
    return rawTableData.map((firstCase) => {
      const clinicalData = caseIdToClinicalData.get(firstCase.caseId)

      return {
        customerCaseId: displayCustomerCaseId(firstCase.customerCaseId),
        caseId: firstCase.caseId,
        surgeonNames: generateListUiDisplay(clinicalData?.surgeonNames),
        anesthesiaNames: generateListUiDisplay(clinicalData?.anesthesiaNames),
        procedureNames: generateListUiDisplay(clinicalData?.procedureNames),
        serviceLineName: firstCase.serviceLineName,
        siteName: firstCase.siteName,
        roomName: firstCase.roomName,
        roomId: firstCase.roomId,
        scheduledStartTime: firstCase.scheduledStartTime,
        actualStartTime: firstCase.actualStartTime,
        videoSeekTime: firstCase.actualStartTime,
        lateMinutes: firstCase.lateMinutes,
        actualDuration: firstCase.actualDuration,
      }
    })
  }, [caseIdToClinicalData, rawTableData])

  const csvData = useFirstCaseStartTableExport({
    cubeParams,
    orderBy: orderByState,
    sortKeys,
  })

  const csvExportButtonDisabled = useMemo(
    () =>
      roughlyGreaterThanAYearDifference(new Date(minTime), new Date(maxTime)),
    [maxTime, minTime]
  )

  return (
    <Tile gutter={remSpacing.large}>
      <FlexContainer direction="column">
        <FlexItem style={{ display: 'flex', marginLeft: 'auto' }}>
          <div style={{ marginRight: remSpacing.medium }}>
            <ColumnsPicker
              label="Columns"
              name="column-picker"
              bulkSelect={true}
              columnNames={selectableColumns.map((col) => col.name)}
              selectedColumnNames={selectedColumns.map((col) => col.name)}
              onChange={handleSelectedColumnsChange}
            ></ColumnsPicker>
          </div>
          <CsvExportButton
            disabled={csvExportButtonDisabled}
            tooltip={
              csvExportButtonDisabled
                ? 'In order to export data, please adjust filters to a time period covering a year or less.'
                : undefined
            }
            data={csvData}
            onClick={() =>
              eventsLogger(EVENTS.EXPORT_FIRST_CASE_START_TABLE, loggingState)
            }
            filename="first_case_start.csv"
          />
        </FlexItem>
        <FlexItem>
          <RemotePaginatedTableWithVideoBlade
            columns={[...mandatoryColumns, ...selectedColumns]}
            data={tableData}
            isLoading={
              isLoadingRawClinicalData ||
              isLoadingRawTableData ||
              isLoadingTotalCount
            }
            isMultiSort={false}
            paginationType="minimal"
            sortOrder={orderByState ?? undefined}
            hasNextPage={hasNextPage}
            hasPreviousPage={hasPreviousPage}
            onNextPageClicked={onNextPage}
            onPreviousPageClicked={onPreviousPage}
            onChangeSort={(sortOrder) => onChangeSort(sortOrder)}
            metric={METRICS.FIRST_CASE_START}
          />
        </FlexItem>
      </FlexContainer>
    </Tile>
  )
}

export default FirstCaseStartTable
export const DEFAULT_ORDER_BY_STATE: OrderBy[] | undefined = [
  {
    sort: FirstCaseSortDimensions.LATE_MINUTES,
    direction: Direction.DESC,
  },
]
