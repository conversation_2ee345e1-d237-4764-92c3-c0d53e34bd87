import { useFlags } from 'launchdarkly-react-client-sdk'

import { DayOfWeekFilter } from 'src/components/DayOfWeekFilter'
import { InsightsDatePicker } from 'src/components/InsightsDatePicker'
import { FilterBarContainer } from 'src/components/PageContentTemplate'
import { ProceduresFilter } from 'src/components/ProceduresFilter'
import { ResetFilters } from 'src/components/ResetFilters'
import { ServiceLinesFilterWithCount } from 'src/components/ServiceLinesFilter'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { StaffFilter } from 'src/components/StaffFilter'
import { CASES } from 'src/modules/cube/types/dataCubes'
import AnesthesiaFilterDropdown from 'src/pages/Insights/common/AnesthesiaFilterDropdown'
import PrimarySurgeonsFilterDropdown from 'src/pages/Insights/common/PrimarySurgeonsFilterDropdown'
import ProcedureFilterDropdown from 'src/pages/Insights/common/ProceduresFilterDropdown'
import FirstCaseStartTable from 'src/pages/Insights/FirstCaseStart/FirstCaseStartTable'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { WebDashboardFeatureFlagSet } from '../../../modules/feature/types'
import { MetricsContainer } from '../MetricsContainer'
import { METRICS } from '../types'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import FirstCaseStartGraph from './FirstCaseStartGraph'
import FirstCaseStartSummaryTile from './FirstCaseStartSummaryTile'

export const FirstCaseStart = () => {
  const {
    minTime,
    maxTime,
    daysOfWeek,
    siteIds,
    roomIds,
    staffIds,
    anesthesiaIds,
    serviceLineIds,
    procedureIds,

    showResetFiltersButton,
    staffWithCount,
    anesthesiaStaffWithCount,
    proceduresWithCount,

    onChangeSites,
    onChangeRooms,
    onChangeStaff,
    onChangeAnesthesia,
    onChangeServiceLines,
    onChangeProcedure,
    onChangeDaysOfWeek,
    localOnChangeDateRanges,
    resetActions,
    cubeParams,
  } = useInsightsSearchParams(METRICS.FIRST_CASE_START)

  const { enablePerformantFilterDropdownsOnInsightsPageTabs } =
    useFlags<WebDashboardFeatureFlagSet>()

  const { sites } = useSiteOptions()
  const rooms = useRoomOptions()

  return (
    <>
      <FilterBarContainer>
        <InsightsDatePicker
          minTime={minTime}
          maxTime={maxTime}
          onChangeDateRanges={localOnChangeDateRanges}
        />
        <DayOfWeekFilter selected={daysOfWeek} onChange={onChangeDaysOfWeek} />
        <SitesRoomsFilter
          sites={sites}
          selectedSiteIds={siteIds}
          onChangeSites={onChangeSites}
          rooms={rooms}
          selectedRoomIds={roomIds}
          onChangeRooms={onChangeRooms}
          multipleSites
          bulkSelectSites
          bulkSelectRooms
        />
        {!enablePerformantFilterDropdownsOnInsightsPageTabs && (
          <>
            <StaffFilter
              items={staffWithCount}
              selectedIds={staffIds}
              onChange={onChangeStaff}
              bulkSelect
            />
            <StaffFilter
              label="Anesthesia"
              items={anesthesiaStaffWithCount}
              selectedIds={anesthesiaIds}
              onChange={onChangeAnesthesia}
              bulkSelect
            />
            <ServiceLinesFilterWithCount
              cubeParams={cubeParams}
              measure={CASES.COUNT}
              value={serviceLineIds}
              onChange={onChangeServiceLines}
            />
            <ProceduresFilter
              items={proceduresWithCount}
              selectedIds={procedureIds}
              onChange={onChangeProcedure}
              bulkSelect
            />
          </>
        )}
        {enablePerformantFilterDropdownsOnInsightsPageTabs && (
          <>
            <PrimarySurgeonsFilterDropdown metric={METRICS.FIRST_CASE_START} />
            <AnesthesiaFilterDropdown metric={METRICS.FIRST_CASE_START} />
            <ServiceLinesFilterWithCount
              cubeParams={cubeParams}
              measure={CASES.COUNT}
              value={serviceLineIds}
              onChange={onChangeServiceLines}
            />
            <ProcedureFilterDropdown metric={METRICS.FIRST_CASE_START} />
          </>
        )}
        {showResetFiltersButton && <ResetFilters resetActions={resetActions} />}
      </FilterBarContainer>
      <MetricsContainer
        graph={<FirstCaseStartGraph />}
        summaryTile={<FirstCaseStartSummaryTile />}
        table={<FirstCaseStartTable />}
      />
    </>
  )
}
