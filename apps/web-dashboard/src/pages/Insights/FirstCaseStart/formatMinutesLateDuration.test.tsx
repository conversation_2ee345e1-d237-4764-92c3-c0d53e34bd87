import { Duration } from 'luxon'

import { formatMinutesLateDuration } from 'src/pages/Insights/FirstCaseStart/formatMinutesLateDuration'

describe('formatMinutesLateDuration', () => {
  it('all fields - should truncate to minutes', () => {
    const duration = Duration.fromObject({
      days: 1,
      hours: 1,
      minutes: 1,
      seconds: 59,
    })
    expect(formatMinutesLateDuration(duration)).toEqual('1d 1h 1m')
  })
  it('only minutes - should truncate to minutes', () => {
    const duration = Duration.fromObject({
      minutes: 0,
      seconds: 59,
    })
    expect(formatMinutesLateDuration(duration)).toEqual('0m')
  })
})
