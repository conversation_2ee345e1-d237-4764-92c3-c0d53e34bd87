import { Query } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'

import { CASES } from 'src/modules/cube/types/dataCubes'

interface FirstCaseCountData {
  isLoading: boolean
  totalCount: number | undefined
}

export const useFirstCaseCountData = (
  cubeParams: Query
): FirstCaseCountData => {
  const { isLoading, resultSet } = useCubeQuery<{
    [CASES.COUNT]: number
  }>({
    ...cubeParams,
    measures: [CASES.COUNT],
  })

  const totalCount = resultSet?.rawData()[0][CASES.COUNT]

  return { isLoading, totalCount }
}
