import { renderHook } from '@testing-library/react'
import { DateTime, Duration } from 'luxon'

import { defaultTimezone } from 'src/Contexts'
import { useCaseIdToClinicalDataMap } from 'src/modules/insights/hooks/useClinicalData'
import useFirstCaseStartTableExport from 'src/pages/Insights/FirstCaseStart/hooks/useFirstCaseStartTableExport'
import useFirstCaseTableData from 'src/pages/Insights/FirstCaseStart/hooks/useFirstCaseTableData'
import { MockAppProvider } from 'src/test/MockAppProvider'

import { getNewFirstCaseStartQueryParams } from '../../queries'

vi.mock('src/pages/Insights/FirstCaseStart/hooks/useFirstCaseTableData')
const mockedUseFirstCaseTableData = vi.mocked(useFirstCaseTableData)

vi.mock('src/modules/insights/hooks/useClinicalData')
const mockedUseCaseIdToClinicalDataMap = vi.mocked(useCaseIdToClinicalDataMap)

describe('First Case Start Table Export', () => {
  it('when no data exists, returns empty result', () => {
    mockedUseFirstCaseTableData.mockReturnValue({
      isLoading: false,
      tableData: [],
    })

    mockedUseCaseIdToClinicalDataMap.mockReturnValue({
      isLoading: false,
      caseIdToClinicalData: new Map(),
    })

    const cubeParams = getNewFirstCaseStartQueryParams({
      minDateTime: '',
      maxDateTime: '',
      siteIds: [],
      roomIds: [],
      staffIds: [],
      anesthesiaIds: [],
      serviceLineIds: [],
      procedureIds: [],
      daysOfWeek: [],
      timezone: defaultTimezone,
    })

    const { result } = renderHook(
      () => {
        return useFirstCaseStartTableExport({
          cubeParams,
          orderBy: [],
          sortKeys: [],
        })
      },
      { wrapper: MockAppProvider }
    )

    expect(result.current).toEqual([])
  })

  it('when data exists, data export object has right values', () => {
    mockedUseFirstCaseTableData.mockReturnValue({
      isLoading: false,
      tableData: [
        {
          serviceLineName: 'General Surgery',
          caseId: 'caseId1',
          siteName: 'Site 1',
          roomName: 'Room 1',
          roomId: 'roomId1',
          customerCaseId: 'customerCaseId1',
          scheduledStartTime: DateTime.fromISO(
            '2021-01-01T12:08:00.000-05:00',
            {
              setZone: true,
            }
          ),
          actualStartTime: DateTime.fromISO('2021-01-01T15:09:08.000-08:00', {
            setZone: true,
          }),
          lateMinutes: Duration.fromObject({
            hours: 1,
            minutes: 5,
            seconds: 59,
          }),
        },
        {
          serviceLineName: 'General Surgery',
          caseId: 'caseId2',
          siteName: 'Site 2',
          roomName: 'Room 2',
          roomId: 'roomId2',
          customerCaseId: 'customerCaseId2',
          scheduledStartTime: DateTime.fromISO(
            '2021-01-02T00:13:00.000-08:00',
            { setZone: true }
          ),
          actualStartTime: DateTime.fromISO('2021-01-02T01:00:00.000-05:00', {
            setZone: true,
          }),
          lateMinutes: Duration.fromObject({
            hours: 3,
            minutes: 10,
            seconds: 30,
          }),
        },
      ],
    })

    mockedUseCaseIdToClinicalDataMap.mockReturnValue({
      isLoading: false,
      caseIdToClinicalData: new Map([
        [
          'caseId1',
          {
            caseId: 'caseId1',
            surgeonNames: ['Smith, John', 'Jones, Jane'],
            anesthesiaNames: [],
            procedureNames: ['Procedure 1', 'Procedure 2'],
          },
        ],
      ]),
    })

    const cubeParams = getNewFirstCaseStartQueryParams({
      minDateTime: '',
      maxDateTime: '',
      siteIds: [],
      roomIds: [],
      staffIds: [],
      anesthesiaIds: [],
      serviceLineIds: [],
      procedureIds: [],
      daysOfWeek: [],
      timezone: defaultTimezone,
    })

    const { result } = renderHook(
      () => {
        return useFirstCaseStartTableExport({
          cubeParams,
          orderBy: [],
          sortKeys: [],
        })
      },
      { wrapper: MockAppProvider }
    )
    expect(result.current).toEqual([
      {
        Anesthesia: '-',
        'Primary Surgeons': 'Smith, John; Jones, Jane',
        'Service Line': 'General Surgery',
        Procedures: 'Procedure 1; Procedure 2',
        Rooms: 'Room 1',
        'Day of Week': 'Friday',
        'Scheduled Start Time': '01/01/21, 12:08',
        'Actual Start Time': '01/01/21, 15:09',
        'Case ID': 'customerCaseId1',
        'Minutes Late': '65',
      },
      {
        Anesthesia: '-',
        'Primary Surgeons': '-',
        'Service Line': 'General Surgery',
        Procedures: '-',
        Rooms: 'Room 2',
        'Day of Week': 'Saturday',
        'Scheduled Start Time': '01/02/21, 00:13',
        'Actual Start Time': '01/02/21, 01:00',
        'Case ID': 'customerCaseId2',
        'Minutes Late': '190',
      },
    ])
  })
})
