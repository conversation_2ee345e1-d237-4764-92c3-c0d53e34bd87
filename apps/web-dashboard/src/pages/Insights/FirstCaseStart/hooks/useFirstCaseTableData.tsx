import { Query, QueryOrder } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'
import { DateTime, Duration, DurationLikeObject } from 'luxon'

import { Direction, OrderBy } from '@apella/component-library'
import { useTimezone } from 'src/Contexts'
import {
  CASES,
  ROOMS,
  SERVICE_LINES,
  SITES,
} from 'src/modules/cube/types/dataCubes'
import { FirstCaseSortDimensions } from 'src/pages/Insights/FirstCaseStart/FirstCaseSortDimensions'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'

interface NewRawTableData {
  [CASES.ACTUAL_DURATION_MINUTES]: DurationLikeObject
  [CASES.ACTUAL_START_TIMESTAMP]: string
  [CASES.CASE_ID]: string
  [CASES.CASE_PHASE_ID]: string
  [CASES.CUSTOMER_CASE_ID]: string
  [CASES.LATE_MINUTES]: DurationLikeObject
  [CASES.SCHEDULED_START_TIMESTAMP]: string
  [ROOMS.ID]: string
  [ROOMS.NAME]: string
  [SERVICE_LINES.NAME]: string
  [SITES.NAME]: string
}

export interface CommonTableData {
  actualDuration?: Duration
  actualStartTime: DateTime
  caseId: string
  customerCaseId?: string
  lateMinutes: Duration
  roomId: string
  roomName: string
  scheduledStartTime: DateTime
  serviceLineName: string
  siteName: string
}

interface FirstCaseTableData {
  isLoading: boolean
  tableData: CommonTableData[]
}

const useFirstCaseTableData = ({
  cubeParams,
  offset,
  limit,
  orderBy,
  sortKeys,
}: {
  cubeParams: Query
  limit: number | undefined
  offset: number
  orderBy: OrderBy[] | undefined
  sortKeys: (string | undefined)[]
}): FirstCaseTableData => {
  const { timezone } = useTimezone()
  const tableQuery: Query = {
    ...cubeParams,
    dimensions: [
      CASES.CASE_ID,
      SERVICE_LINES.NAME,
      SITES.NAME,
      ROOMS.ID,
      ROOMS.NAME,
      CASES.SCHEDULED_START_TIMESTAMP,
      CASES.ACTUAL_START_TIMESTAMP,
      CASES.LATE_MINUTES,
      CASES.CUSTOMER_CASE_ID,
      CASES.ACTUAL_DURATION_MINUTES,
      CASES.CASE_PHASE_ID,
    ],
    offset: offset,
    limit: limit,
  }

  if (orderBy && orderBy.length == 1 && sortKeys.includes(orderBy[0].sort)) {
    const queryDirection: QueryOrder =
      orderBy[0].direction == undefined
        ? 'asc'
        : orderBy[0].direction == Direction.ASC
          ? 'asc'
          : 'desc'

    const sortDimensionToCubeDimension: Record<string, string> = {
      [FirstCaseSortDimensions.ROOM_NAME]: ROOMS.NAME,
      [FirstCaseSortDimensions.ACTUAL_START_TIME]: CASES.ACTUAL_START_TIMESTAMP,
      [FirstCaseSortDimensions.SCHEDULED_START_TIME]:
        CASES.SCHEDULED_START_TIMESTAMP,
      [FirstCaseSortDimensions.LATE_MINUTES]: CASES.LATE_MINUTES,
    }

    const cubeDimension = sortDimensionToCubeDimension[orderBy[0].sort]
    tableQuery.order = {
      [cubeDimension]: queryDirection,
    }
  }

  const { isLoading, resultSet } = useCubeQuery<NewRawTableData>(tableQuery)

  const results = resultSet?.rawData() ?? []

  const tableData: CommonTableData[] = results.map((edge) => ({
    customerCaseId: edge[CASES.CUSTOMER_CASE_ID],
    caseId: edge[CASES.CASE_ID],
    casePhaseId: edge[CASES.CASE_PHASE_ID],
    serviceLineName: edge[SERVICE_LINES.NAME],
    siteName: edge[SITES.NAME],
    roomName: edge[ROOMS.NAME],
    roomId: edge[ROOMS.ID],
    // Cube automatically adds time zone offset to `timeDimensions`, so they are no longer in UTC
    scheduledStartTime: DateTime.fromISO(
      edge[CASES.SCHEDULED_START_TIMESTAMP],
      {
        zone: timezone,
      }
    ),
    // Cube automatically adds time zone offset to `timeDimensions`, so they are no longer in UTC
    actualStartTime: DateTime.fromISO(edge[CASES.ACTUAL_START_TIMESTAMP], {
      zone: timezone,
    }),
    lateMinutes: durationFromMinutes(edge[CASES.LATE_MINUTES]),
    actualDuration: edge[CASES.ACTUAL_DURATION_MINUTES]
      ? durationFromMinutes(edge[CASES.ACTUAL_DURATION_MINUTES])
      : undefined,
  }))
  return { tableData, isLoading }
}

export default useFirstCaseTableData
