import { Duration } from 'luxon'

/**
 * For First Case Starts, format the minutes late duration by truncating to a whole minute.
 * @param duration
 */
export const formatMinutesLateDuration = (duration?: Duration): string => {
  if (duration === undefined) {
    return ''
  }

  if (duration.valueOf() == 0) {
    return '0m'
  }

  const shiftedDuration = duration
    .shiftTo('days', 'hours', 'minutes')
    .toObject()

  return `${
    shiftedDuration.days !== undefined && shiftedDuration.days > 0
      ? `${shiftedDuration.days}d`
      : ''
  } ${
    shiftedDuration.hours !== undefined && shiftedDuration.hours > 0
      ? `${shiftedDuration.hours}h`
      : ''
  } ${
    shiftedDuration.minutes !== undefined && shiftedDuration.minutes > 0
      ? `${Math.floor(shiftedDuration.minutes)}m`
      : ''
  }`.trim()
}
