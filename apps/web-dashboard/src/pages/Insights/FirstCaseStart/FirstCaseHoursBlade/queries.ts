import { gql } from '@apollo/client'

export const ROOM_FIRST_CASE_CONFIG_FRAGMENT = gql`
  fragment RoomFirstCaseConfigFragment on RoomFirstCaseConfig {
    source
    sunday {
      startTime
      endTime
    }
    monday {
      startTime
      endTime
    }
    tuesday {
      startTime
      endTime
    }
    wednesday {
      startTime
      endTime
    }
    thursday {
      startTime
      endTime
    }
    friday {
      startTime
      endTime
    }
    saturday {
      startTime
      endTime
    }
  }
`

export const SITE_FIRST_CASE_CONFIG_FRAGMENT = gql`
  fragment SiteFirstCaseConfigFragment on SiteFirstCaseConfig {
    sunday {
      startTime
      endTime
    }
    monday {
      startTime
      endTime
    }
    tuesday {
      startTime
      endTime
    }
    wednesday {
      startTime
      endTime
    }
    thursday {
      startTime
      endTime
    }
    friday {
      startTime
      endTime
    }
    saturday {
      startTime
      endTime
    }
  }
`

export const GET_FIRST_CASE_HOURS_QUERY = gql`
  ${ROOM_FIRST_CASE_CONFIG_FRAGMENT}
  ${SITE_FIRST_CASE_CONFIG_FRAGMENT}
  query GetFirstCaseHoursQuery($siteIds: [String!]) {
    sites(siteIds: $siteIds) {
      edges {
        node {
          id
          name

          firstCaseConfig {
            siteId
            ...SiteFirstCaseConfigFragment
          }

          rooms {
            edges {
              node {
                id
                name
                firstCaseConfig {
                  roomId
                  ...RoomFirstCaseConfigFragment
                }
              }
            }
          }
        }
      }
    }
  }
`
