import { useCallback, useMemo, useState } from 'react'

import { useTheme } from '@emotion/react'

import { useQuery } from '@apollo/client'
import { capitalize } from 'lodash'

import {
  AvTimer,
  Blade,
  Button,
  H4,
  H5,
  H6,
  MeetingRoom,
  P2,
  Progress,
  remSpacing,
} from '@apella/component-library'
import { FirstCaseConfigSource } from 'src/__generated__/globalTypes'
import {
  DayOfWeek,
  DAYS_OF_WEEK,
  WeekConfig,
} from 'src/modules/dayOfWeekConfig/types'
import { convertToWeekConfig } from 'src/modules/dayOfWeekConfig/utils'

import {
  GetFirstCaseHoursQuery,
  GetFirstCaseHoursQueryVariables,
} from './__generated__'
import { GET_FIRST_CASE_HOURS_QUERY } from './queries'

type RoomData = {
  id: string
  name: string
  firstCaseConfig: WeekConfig & { source: FirstCaseConfigSource }
  customDays: DayOfWeek[]
}

const SiteFirstCaseHoursCard = ({
  name,
  firstCaseConfig,
  rooms,
}: {
  name: string
  firstCaseConfig: WeekConfig
  rooms: RoomData[]
}) => {
  const theme = useTheme()
  const customRooms = useMemo(
    () =>
      rooms.filter(
        (room) => room.firstCaseConfig.source === FirstCaseConfigSource.ROOM
      ),
    [rooms]
  )

  return (
    <div
      css={{
        display: 'flex',
        flexDirection: 'column',
        gap: remSpacing.medium,
        padding: `${remSpacing.gutter} ${remSpacing.medium} ${remSpacing.medium} ${remSpacing.medium}`,
        background: theme.palette.background.secondary,
        border: `1px solid ${theme.palette.gray[30]}`,
        borderRadius: remSpacing.xsmall,
      }}
    >
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
          padding: `0 ${remSpacing.xsmall}`,
        }}
      >
        <H4 as="h3">{name}</H4>

        <div
          css={{
            display: 'flex',
            alignItems: 'center',
            gap: remSpacing.xxsmall,
            color: theme.palette.gray[50],
          }}
        >
          <MeetingRoom size="xs" />
          <H6>{rooms.length} Rooms</H6>
        </div>
      </div>

      <div
        css={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <P2 as="span" color={theme.palette.gray[70]}>
          Site Defaults
        </P2>
      </div>

      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
          gap: remSpacing.xsmall,
          padding: `0 ${remSpacing.medium}`,
        }}
      >
        {DAYS_OF_WEEK.map((day) => {
          const timeRange = firstCaseConfig[day]

          return (
            <div
              key={day}
              css={{
                display: 'flex',
                justifyContent: 'space-between',
                gap: remSpacing.small,
              }}
            >
              <H5 as="span" color={theme.palette.gray[80]}>
                {capitalize(day)}
              </H5>

              {timeRange ? (
                <P2 as="span" color={theme.palette.gray[80]}>
                  {timeRange.startTime} - {timeRange.endTime}
                </P2>
              ) : (
                <P2
                  as="span"
                  css={{ fontStyle: 'italic' }}
                  color={theme.palette.gray[80]}
                >
                  Excluded
                </P2>
              )}
            </div>
          )
        })}
      </div>

      {customRooms.length > 0 && <CustomRoomsCard rooms={customRooms} />}
    </div>
  )
}

const CustomRoomsCard = ({ rooms }: { rooms: RoomData[] }) => {
  const theme = useTheme()

  return (
    <div
      css={{
        display: 'flex',
        padding: `${remSpacing.medium}`,
        flexDirection: 'column',
        justifyContent: 'center',
        gap: remSpacing.medium,
        borderRadius: remSpacing.xxsmall,
        background: theme.palette.blue.background,
      }}
    >
      <div
        css={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <P2 as="h4" color={theme.palette.gray[70]}>
          Custom Rooms
        </P2>
      </div>

      {rooms.map((room) => {
        return <RoomCustomHours key={room.id} {...room} />
      })}
    </div>
  )
}

const RoomCustomHours = ({ firstCaseConfig, name, customDays }: RoomData) => {
  const theme = useTheme()
  return (
    <div
      css={{ display: 'flex', flexDirection: 'column', gap: remSpacing.small }}
    >
      <H5>{name}</H5>

      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
          gap: remSpacing.xsmall,
        }}
      >
        {customDays.map((day) => {
          const timeRange = firstCaseConfig[day]

          return (
            <div
              key={day}
              css={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <H5 as="span" color={theme.palette.gray[70]}>
                {capitalize(day)}
              </H5>

              {timeRange ? (
                <P2 as="span" color={theme.palette.gray[70]}>
                  {timeRange.startTime} - {timeRange.endTime}
                </P2>
              ) : (
                <P2
                  as="span"
                  css={{ fontStyle: 'italic' }}
                  color={theme.palette.gray[70]}
                >
                  Excluded
                </P2>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}

/**
 * Returns first case hours data for the given site IDs.
 *
 * Rooms are filtered to only include those that differ from the site.
 */
const useFirstCaseHoursData = (siteIds?: string[]) => {
  const { data, loading } = useQuery<
    GetFirstCaseHoursQuery,
    GetFirstCaseHoursQueryVariables
  >(GET_FIRST_CASE_HOURS_QUERY, { variables: { siteIds } })

  const sites = useMemo(() => {
    return (
      data?.sites?.edges.map(({ node: siteData }) => {
        const siteFirstCaseConfig = convertToWeekConfig(
          siteData.firstCaseConfig
        )
        return {
          ...siteData,
          firstCaseConfig: siteFirstCaseConfig,
          rooms: siteData.rooms.edges.map(({ node: roomData }) => {
            const roomFirstCaseConfig = convertToWeekConfig(
              roomData.firstCaseConfig
            )
            return {
              ...roomData,
              firstCaseConfig: {
                ...roomFirstCaseConfig,
                source: roomData.firstCaseConfig.source,
              },
              customDays: DAYS_OF_WEEK.filter((day) => {
                return (
                  siteFirstCaseConfig[day]?.startTime !==
                    roomFirstCaseConfig[day]?.startTime ||
                  siteFirstCaseConfig[day]?.endTime !==
                    roomFirstCaseConfig[day]?.endTime
                )
              }),
            }
          }),
        }
      }) ?? []
    )
  }, [data?.sites?.edges])

  return { loading, sites }
}

export const FirstCaseHoursBladeContent = ({
  siteIds,
}: {
  siteIds?: string[]
}) => {
  const { loading, sites } = useFirstCaseHoursData(siteIds)
  const theme = useTheme()

  return (
    <div
      css={{
        overflow: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: remSpacing.gutter,
        padding: remSpacing.gutter,
      }}
    >
      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
          gap: remSpacing.small,
          padding: `0 ${remSpacing.gutter}`,
        }}
      >
        <P2 color={theme.palette.gray[60]}>
          First case hours are time windows that define which cases may be
          counted as a &quot;First Case&quot;. A case will meet the criteria for
          &quot;First Case&quot; if it is the first case within first case
          hours, <span css={{ fontStyle: 'italic' }}>and</span> the first case
          of the day for a surgeon.
        </P2>
        <P2 color={theme.palette.gray[60]}>
          Note: In flip room scenarios, the second case for a surgeon is
          excluded even if it is within first case hours.
        </P2>
        <P2 color={theme.palette.gray[60]}>
          First case hours are customizable for each site and room. Please
          contact <a href="mailto:<EMAIL>"><EMAIL></a> to
          export or make changes.
        </P2>
      </div>
      {loading ? (
        <Progress />
      ) : (
        sites.map((site) => {
          return (
            <SiteFirstCaseHoursCard
              key={site.id}
              name={site.name}
              firstCaseConfig={site.firstCaseConfig}
              rooms={site.rooms}
            />
          )
        })
      )}
    </div>
  )
}

export const FirstCaseHoursBladeButton = ({
  siteIds,
}: {
  siteIds?: string[]
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const onClose = useCallback(() => setIsOpen(false), [])
  const theme = useTheme()

  return (
    <>
      <div css={{ display: 'flex', paddingTop: remSpacing.medium }}>
        <Button
          size="sm"
          type="button"
          appearance="link"
          onClick={() => setIsOpen(true)}
        >
          <AvTimer />
          First Case Hours
        </Button>
      </div>

      <Blade side="right" onClose={onClose} isOpen={isOpen} size="sm">
        <Blade.Header>
          <div
            css={{
              display: 'flex',
              alignItems: 'center',
              gap: remSpacing.xxsmall,
              color: theme.palette.gray[70],
            }}
          >
            <AvTimer />
            <H5 as="h2">First Case Hours</H5>
          </div>
          <Blade.CloseButton onClose={onClose} />
        </Blade.Header>

        <FirstCaseHoursBladeContent siteIds={siteIds} />
      </Blade>
    </>
  )
}
