import { Navigate, useLocation } from 'react-router'

import { CaseGraphMode } from 'src/components/InsightsGraph'
import { useCurrentUser } from 'src/modules/user/hooks'
import { LocationPath } from 'src/router/types'

export const CaseVolumePage = (): React.JSX.Element => {
  const { currentOrganization } = useCurrentUser()
  const orgId = currentOrganization?.node?.id
  const CaseLengthPrefix = `${orgId}_Insights_CaseLength`
  const GRAPH_STACK_GROUPING = `${CaseLengthPrefix}_graph_stack_grouping`

  const location = useLocation()
  const params = new URLSearchParams(location.search)
  const newParams = new URLSearchParams()

  newParams.set(GRAPH_STACK_GROUPING, `"${CaseGraphMode.Volume}"`)
  for (const [key, value] of params) {
    newParams.set(key.replace('CaseVolume', 'CaseLength'), value)
  }

  const to = `${LocationPath.CaseLengths}?${newParams.toString()}`
  return <Navigate to={to} replace={true} />
}
