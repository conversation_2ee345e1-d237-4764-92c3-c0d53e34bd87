import { useMemo } from 'react'

import { useQuery } from '@apollo/client'

import { GET_PHASE_TYPES } from 'src/pages/Insights/queries'

import { GetPhaseTypesData } from './__generated__'

export function usePhaseTypes() {
  const { loading, data: phaseTypesData } =
    useQuery<GetPhaseTypesData>(GET_PHASE_TYPES)

  const phaseTypes = useMemo(
    () => phaseTypesData?.phaseTypes.edges.map((e) => e.node) ?? [],
    [phaseTypesData?.phaseTypes.edges]
  )

  return { loading, phaseTypes }
}
