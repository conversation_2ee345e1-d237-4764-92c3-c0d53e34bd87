import { DateTime, Duration } from 'luxon'

export const convertFromUtcString = (
  utcDateTime: string,
  timezone: string
): DateTime =>
  DateTime.fromISO(utcDateTime, {
    zone: 'UTC',
  }).setZone(timezone)

export const convertDurationToMinutesString = (
  duration?: Duration,
  rounding?: keyof Pick<Math, 'floor' | 'round'>
): string => {
  return duration
    ? Math[rounding ?? 'round'](duration.shiftTo('minutes').minutes).toString()
    : ''
}

/** Given two dates, return true if the dates are roughly greater than a year apart */
export const roughlyGreaterThanAYearDifference = (
  date1: Date,
  date2: Date
): boolean =>
  Math.abs((date1.valueOf() - date2.valueOf()) / 1000 / 60 / 60 / 24) > 368
