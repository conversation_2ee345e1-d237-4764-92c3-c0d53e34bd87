import { DateTime, Duration, Interval } from 'luxon'

import { CaseFlag } from 'src/modules/planning/types'
import {
  TurnoverLengthWithGoal,
  TurnoverLengthWithoutGoal,
} from 'src/utils/status'

import {
  CaseMatchingStatus,
  CaseStatusName,
  CaseType,
  PatientClass,
  Scalars,
  TurnoverGoals,
  TurnoverLabel,
  TurnoverType,
} from '../__generated__/globalTypes'
import { Block, BlockTime, BlockTimeRelease } from './BlockManagement/types'

export const Uncategorized = 'uncategorized'
export const Unlabeled = 'Unlabeled'
export const CaseTooltipHoverInterConfiguration = {
  cursorRestMs: 200,
}

export enum EventSourceType {
  Prediction = 'prediction',
  Human = 'human_gt',
  Forecasting = 'forecasting',
  Unified = 'unified',
}

export interface ApellaRoom {
  id: string
  name: string
}

export interface ApellaSite {
  id: string
  name: string
}

export interface CaseStatus {
  name: CaseStatusName
  since?: DateTime
}

export interface ApellaPhase {
  endTime?: DateTime
  id: string
  room: ApellaRoom
  startTime: DateTime
}

export interface Turnover {
  currentLengthStatus: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
  duration: Duration
  endTime: DateTime
  followingCaseId: string
  goals: TurnoverGoals
  id: string
  labels: TurnoverLabel[] | null
  note: string | null
  overallLengthStatus: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
  precedingCaseId: string
  roomId: string
  scheduledEndTime?: DateTime
  startTime: DateTime
  type: TurnoverType
}

export interface ApellaCase {
  actualPhase?: ApellaPhase
  case?: Case
  endTime?: DateTime
  id: string
  room: ApellaRoom
  site: ApellaSite
  startTime: DateTime
  status: CaseStatus
  type: CaseType
}

export interface EventNotificationRecipients {
  eventColor: string
  eventName: string
  notificationRecipients: Set<string>
  notificationSentTime: string
}

export interface StaffName {
  name: string | null
}

export interface EventNotification {
  event?: {
    id: string
    name: string
    color: string | null
  }
  notificationEvent: {
    name: string
    color: string | null
    id: string
  }
  observation?: {
    id: string
  }
  sentTime?: DateTime
  staffEventContactInformation: {
    contactInformation: {
      isApellaEmployee: boolean
      name: string
    }
  }
}

export interface CaseLabel {
  abbreviation: string
  color: string
  fieldId: string
  id: string
  optionId: string
  value: string
}

export interface Case {
  caseClassificationType?: {
    id: string
    name: string
  }
  caseFlags: CaseFlag[]
  caseLabels?: CaseLabel[]
  caseMatchingStatus?: CaseMatchingStatus
  eventNotifications?: EventNotification[]
  externalCaseId?: string
  id: string
  isAddOn?: boolean
  isFirstCase?: boolean
  isInFlipRoom?: boolean
  notePlan?: {
    id: string
    note: string
  }
  patient?: {
    id: string
    age?: number
    abbreviatedName: string
    administrativeSex: string
  }
  patientClass?: PatientClass
  precedingCaseId?: string
  procedures: { name: string; anesthesiaType?: string }[]
  room: ApellaRoom
  scheduledEndTime: DateTime
  scheduledStartTime: DateTime
  serviceLine?: {
    id: string
    name?: string
  }
  staff: Staff[]
  staffPlan?: StaffPlan[]
}

export interface Observation {
  color: string
  id: string
  observedTime: DateTime
  observedType: string
  observedTypeName: string
}

export interface Staff {
  displayName: string
  firstName: string
  id: string
  lastName: string
  role: string
}

export interface StaffPlan extends Staff {
  rowId: string
}

export interface LiveEventAttrs {
  color: string
  id: string
  name: string | null
}

export interface LiveEvent {
  attrs: LiveEventAttrs | null
  id: string
  name: string
  startTime: Scalars['DateTime']['output']
}

export interface ApellaBlockTimeRelease extends BlockTimeRelease {
  releasedTime: Scalars['DateTime']['output']
  source: string
  sourceType: string
}

export interface ApellaBlockTime extends BlockTime {
  block: {
    surgeonIds: string[]
    id: string
    name: string
    color: string
    archivedTime?: Scalars['DateTime']['input'] | null
  }
  blockId: string
  createdTime?: Scalars['DateTime']['output'] | null
  endTime: Scalars['DateTime']['output']
  id: string
  intervals: Interval[]
  releasedFrom?: string | null
  releases: ApellaBlockTimeRelease[]
  room: {
    id: string
    name: string
  }
  roomId: string
  startTime: Scalars['DateTime']['output']
}

export interface ApellaBlock extends Block {
  blockTimes: ApellaBlockTime[]
  caseIds: string[]
  utilizationPercentage?: number
}

export type StartAndEndDates =
  | Date
  | [Date | null, Date | null]
  | null
  | undefined
