import { useCallback } from 'react'

import { useTheme } from '@emotion/react'

import { FieldArrayRenderProps } from 'formik'

import { Button, Close, Plus, theme } from '@apella/component-library'

const SubscriptionActionButton = ({
  onClick,
  children,
}: {
  onClick: () => void
  children: React.ReactNode
}) => {
  return (
    <Button
      type="button"
      size="sm"
      appearance="link"
      buttonType="icon"
      onClick={onClick}
    >
      {children}
    </Button>
  )
}

export const ArchiveSubscriptionButton = ({
  setFieldValue,
  index,
}: {
  setFieldValue: FieldArrayRenderProps['form']['setFieldValue']
  index: number
}) => {
  const onClick = useCallback(() => {
    setFieldValue(`staffEventSubscriptions.${index}.archive`, true)
  }, [setFieldValue, index])
  return (
    <SubscriptionActionButton onClick={onClick}>
      <Close size="sm" color={theme.palette.red[50]} />
    </SubscriptionActionButton>
  )
}

export const DeleteSubscriptionButton = ({
  remove,
  index,
}: {
  remove: FieldArrayRenderProps['remove']
  index: number
}) => {
  const theme = useTheme()
  const onClick = useCallback(() => {
    remove(index)
  }, [remove, index])
  return (
    <SubscriptionActionButton onClick={onClick}>
      <Close size="sm" color={theme.palette.red[50]} />
    </SubscriptionActionButton>
  )
}

export const AddSubscriptionButton = ({
  push,
}: {
  push: FieldArrayRenderProps['push']
}) => {
  const onClick = useCallback(() => {
    push({
      archive: false,
      eventTypes: [],
      staffId: '',
      isNewSubscription: true,
    })
  }, [push])
  return (
    <SubscriptionActionButton onClick={onClick}>
      <Plus size="sm" /> Add subscription
    </SubscriptionActionButton>
  )
}
