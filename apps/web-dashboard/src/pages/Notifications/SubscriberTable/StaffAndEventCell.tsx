import { H6, P3, theme, Tooltip, TooltipProps } from '@apella/component-library'

export const StaffAndEventCell = ({
  staffName,
  events,
  tooltipPlacement,
  text,
}: {
  staffName: string
  events: string[]
  tooltipPlacement: TooltipProps['placement']
  text: string[]
}) => {
  return (
    <>
      <div style={{ display: 'inline-block', marginRight: '1%' }}>
        <Tooltip
          body={
            <div css={{ whiteSpace: 'normal', margin: '4px' }}>
              {text.map((subs, pos) => (
                <div
                  key={pos}
                  css={{
                    marginBottom: '10px',
                  }}
                >
                  {subs}
                  {pos != text.length - 1 && (
                    <hr
                      key={pos}
                      css={{
                        border: '1px solid #F6F6F6',
                      }}
                    ></hr>
                  )}
                </div>
              ))}
            </div>
          }
          placement={tooltipPlacement}
          strategy="fixed"
        >
          <H6>{staffName}</H6>

          <P3 css={{ color: theme.palette.text.tertiary }}>
            {events.length} EVENTS
          </P3>
        </Tooltip>
      </div>
    </>
  )
}
