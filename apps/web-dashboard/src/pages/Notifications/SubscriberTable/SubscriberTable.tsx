import { useEffect, useState } from 'react'
import { generatePath } from 'react-router'

import {
  ButtonLink,
  Edit,
  Table,
  TBody,
  TD,
  TH,
  THead,
  TR,
} from '@apella/component-library'
import { LocationPath } from 'src/router/types'

import { FinalSubscriberContent } from '../types'
import { useNotificationsSearchParams } from '../useNotificationsSearchParams'
import { StaffAndEventCell } from './StaffAndEventCell'
import { SubscriberCell } from './SubscriberCell'

export const SubscriberTable = (props: {
  subscriberInfo: FinalSubscriberContent[]
}) => {
  const [filteredSubscriberInfo, setFilteredSubscriberInfo] = useState<
    FinalSubscriberContent[]
  >(props.subscriberInfo)

  const { selectedPhoneNumber, selectedSubscriber, selectedSubscriberStaff } =
    useNotificationsSearchParams()
  useEffect(() => {
    const updatedSubscriberInfo = props.subscriberInfo.filter(
      (subscriberInfo: FinalSubscriberContent) => {
        const validPhoneNumber = selectedPhoneNumber
          ? subscriberInfo.phoneNumber === selectedPhoneNumber
          : true
        const validSubscriber = selectedSubscriber
          ? subscriberInfo.recipientName === selectedSubscriber
          : true
        const validStaff = selectedSubscriberStaff
          ? subscriberInfo.staffNameAndEvent.some(
              (staffNameEvent) =>
                staffNameEvent.staff === selectedSubscriberStaff
            )
          : true
        return validPhoneNumber && validSubscriber && validStaff
      }
    )
    setFilteredSubscriberInfo(updatedSubscriberInfo)
  }, [
    selectedPhoneNumber,
    selectedSubscriber,
    selectedSubscriberStaff,
    props.subscriberInfo,
  ])

  return (
    <Table>
      <THead>
        <TR>
          <TH>Subscriber</TH>
          <TH>Staff & Event</TH>
          <TH>Actions</TH>
        </TR>
      </THead>
      <TBody>
        {filteredSubscriberInfo.map((subscriber) => (
          <TR key={subscriber.id}>
            <TD>
              <SubscriberCell
                name={subscriber.recipientName}
                phoneNumber={subscriber.phoneNumber}
              />
            </TD>

            <TD>
              {subscriber.staffNameAndEvent.map((sub) => {
                return (
                  <StaffAndEventCell
                    key={`${subscriber.id}-${sub.staff}`}
                    staffName={sub.staff}
                    events={sub.eventType}
                    tooltipPlacement={'bottom'}
                    text={sub.eventType}
                  />
                )
              })}
            </TD>
            <TD>
              <ButtonLink
                color="alternate"
                appearance="link"
                to={generatePath(LocationPath.EditSubscriber, {
                  id: subscriber.id,
                })}
              >
                <Edit size="sm" color="gray" />
              </ButtonLink>
            </TD>
          </TR>
        ))}
      </TBody>
    </Table>
  )
}
