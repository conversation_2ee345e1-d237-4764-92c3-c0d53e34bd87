import { SingleSelect, Option } from '@apella/component-library'

import { FinalSubscriberContent } from '../types'
import { useNotificationsSearchParams } from '../useNotificationsSearchParams'

export const SubscriberTableFilters = ({
  subscriberInfo,
}: {
  subscriberInfo: FinalSubscriberContent[]
}) => {
  const {
    selectedPhoneNumber,
    selectedSubscriber,
    selectedSubscriberStaff,
    onChangePhoneNumber,
    onChangeSubscriber,
    onChangeSubscriberStaff,
  } = useNotificationsSearchParams()

  const { phoneNumbers, subscribers, subscriberStaffs } =
    subscriberInfo.reduce<{
      phoneNumbers: string[]
      subscribers: string[]
      subscriberStaffs: string[]
    }>(
      (acc, info: FinalSubscriberContent) => {
        if (!acc.phoneNumbers.includes(info.phoneNumber)) {
          acc.phoneNumbers.push(info.phoneNumber)
        }
        if (!acc.subscribers.includes(info.recipientName)) {
          acc.subscribers.push(info.recipientName)
        }

        info.staffNameAndEvent.map((staffNameEvent) => {
          if (!acc.subscriberStaffs.includes(staffNameEvent.staff)) {
            acc.subscriberStaffs.push(staffNameEvent.staff)
          }
        })
        return acc
      },
      {
        phoneNumbers: [],
        subscribers: [],
        subscriberStaffs: [],
      }
    )

  const getOptions = (options: string[]) => {
    return options.map((option) => (
      <Option key={option} label={option} value={option} />
    ))
  }

  return (
    <>
      <SingleSelect
        name="phone-number-filter"
        label="Select phone number"
        value={selectedPhoneNumber}
        search
        onChange={onChangePhoneNumber}
        noResultsText={'No phone number found'}
      >
        <Option label="All phone numbers" value="" />
        {getOptions(phoneNumbers)}
      </SingleSelect>
      <SingleSelect
        name="subscriber-filter"
        label="Select subscriber"
        value={selectedSubscriber}
        search
        onChange={onChangeSubscriber}
        noResultsText={'No subscriber found'}
      >
        <Option label="All subscribers" value="" />
        {getOptions(subscribers)}
      </SingleSelect>
      <SingleSelect
        name="subscriber-staff-filter"
        label="Select staff"
        value={selectedSubscriberStaff}
        search
        onChange={onChangeSubscriberStaff}
        noResultsText={'No staff found'}
      >
        <Option label="All staff" value="" />
        {getOptions(subscriberStaffs)}
      </SingleSelect>
    </>
  )
}
