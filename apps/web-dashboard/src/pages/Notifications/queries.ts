import { gql } from '@apollo/client'

export const GET_NOTIFICATION_INFORMATION = gql`
  query GetStaffEventNotificationContactInformation($orgId: ID!) {
    staffEventNotificationContactInformation(query: { orgIds: [$orgId] }) {
      edges {
        node {
          id
          staff {
            id
            name
          }
          eventType {
            name
          }
          observationType {
            name
          }
          contactInformation {
            id
            firstName
            lastName
            contactInformationValue
          }
        }
      }
    }
  }
`
export const GET_STAFF = gql`
  query GetStaff($orgId: String!) {
    staff(
      query: { orgId: $orgId }
      orderBy: [
        { sort: "lastName", direction: ASC }
        { sort: "firstName", direction: ASC }
      ]
    ) {
      edges {
        node {
          name
          id
        }
      }
    }
  }
`

export const SET_SUBSCRIBERS = gql`
  mutation SetSubscribers($subscriberUpsertInput: [SubscriberUpsertInput!]!) {
    subscribersUpsert(input: $subscriberUpsertInput) {
      contactInformation {
        firstName
        lastName
        contactInformationValue
        isApellaEmployee
      }
      success
    }
  }
`

export const GET_NOTIFICATION_EVENT_TYPES = gql`
  query GetNotificationEventTypes($eventTypeIds: [String!]) {
    eventTypes(ids: $eventTypeIds) {
      edges {
        node {
          id
          name
        }
      }
    }
  }
`

export const GET_OBX_NOTIFICATION_TYPES = gql`
  query GetObxNotificationTypes($query: ObservationTypeNamesInput!) {
    observationTypeNames(query: $query) {
      typeId
      name
    }
  }
`

export const GET_SUBSCRIBER_INFORMATION = gql`
  query GetSubscriberInformation($contactInformationId: ID!) {
    contactInformation(query: { ids: [$contactInformationId] }) {
      edges {
        node {
          staffEventContactInformation {
            id
            staff {
              id
              firstName
              lastName
            }
            eventType {
              id
              name
            }
            observationType {
              id
              name
            }
          }
          id
          firstName
          lastName
          contactInformationValue
          isApellaEmployee
        }
      }
    }
  }
`
