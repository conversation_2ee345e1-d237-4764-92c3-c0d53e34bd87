import { useMemo } from 'react'
import { generatePath, useNavigate } from 'react-router'
import { toast } from 'react-toastify'

import { ApolloError, useMutation, useQuery } from '@apollo/client'
import { FieldArray, Form, Formik, FormikHelpers } from 'formik'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { chain } from 'lodash'
import {
  array as Yup<PERSON>rray,
  object as YupObject,
  string as YupString,
} from 'yup'

import {
  Button,
  FlexContainer,
  Input,
  Option,
  Tile,
  remSpacing,
  ProgressOverlay,
  H4,
  THead,
  Table,
  TH,
  TR,
  TBody,
  TD,
  ButtonLink,
} from '@apella/component-library'
import {
  ContactInformationType,
  SubscriberEventNotificationUpsertInput,
} from 'src/__generated__/globalTypes'
import { MultiFilterWithCount } from 'src/components/Filters/FilterWithCount'
import { Section } from 'src/components/FormLayout/FormLayout'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import {
  GetNotificationEventTypes,
  GetNotificationEventTypesVariables,
  GetObxNotificationTypes,
  GetObxNotificationTypesVariables,
  GetStaff,
  GetSubscriberInformation,
  SetSubscribers,
  SetSubscribersVariables,
} from 'src/pages/Notifications/__generated__'
import {
  GET_NOTIFICATION_EVENT_TYPES,
  GET_NOTIFICATION_INFORMATION,
  GET_OBX_NOTIFICATION_TYPES,
  GET_STAFF,
  GET_SUBSCRIBER_INFORMATION,
  SET_SUBSCRIBERS,
} from 'src/pages/Notifications/queries'
import {
  AddSubscriptionButton,
  ArchiveSubscriptionButton,
  DeleteSubscriptionButton,
} from 'src/pages/Notifications/SubscriptionActions'
import { LocationPath } from 'src/router/types'

const subscriberSchema = YupObject({
  firstName: YupString().required('First name is required').min(1),
  lastName: YupString().required('Last name is required'),
  contactInformationValue: YupString()
    .required('Phone number is required')
    .matches(
      /^\+?\d{0,3}\d{10}$/,
      'Phone number must contain only 10 digits, and optionally contain a + and a three digit country code.'
    ),
  staffEventSubscriptions: YupArray().of(
    YupObject({
      staffId: YupString().required('Staff is required'),
      eventTypes: YupArray()
        .required('Events are required')
        .of(YupString())
        .min(1, 'At least one event type is required'),
    })
  ),
})

interface StaffAndEvent {
  archive: boolean
  eventTypes: string[]
  isNewSubscription: boolean
  staffId: string
}
interface SubscriberContent {
  contactInformationValue: string
  firstName: string
  id: string
  isApellaEmployee: boolean
  lastName: string
  staffEventSubscriptions: StaffAndEvent[]
  type: ContactInformationType
}

export enum SUBSCRIBER_FORM_TYPE {
  ADD = 'Add',
  EDIT = 'Edit',
}

interface SubscriberFormProps {
  formType: SUBSCRIBER_FORM_TYPE
  subscriberId?: string
}

export const SubscriberForm = ({
  subscriberId,
  formType,
}: SubscriberFormProps) => {
  const navigate = useNavigate()
  const { currentOrganization } = useCurrentUser()
  const orgId = currentOrganization?.node?.id
  const [setSubscribers] = useMutation<SetSubscribers, SetSubscribersVariables>(
    SET_SUBSCRIBERS,
    {
      onCompleted: () => {
        navigate(generatePath(LocationPath.Notifications))
        toast.success(
          `Successfully ${formType.toLocaleLowerCase()}ed a subscription`
        )
      },
      onError: (error: ApolloError) => {
        toast.error(
          `Error ${error.message}. Please try again. If error persists, contact #team-realtime`
        )
      },
      refetchQueries: [
        {
          query: GET_NOTIFICATION_INFORMATION,
          variables: { orgId },
        },
      ],
      awaitRefetchQueries: true,
    }
  )
  const { notificationEventTypes } = useFlags<WebDashboardFeatureFlagSet>()
  const { data: eventTypesData } = useQuery<
    GetNotificationEventTypes,
    GetNotificationEventTypesVariables
  >(GET_NOTIFICATION_EVENT_TYPES, {
    variables: {
      eventTypeIds: notificationEventTypes,
    },
  })
  const { obxNotificationTypes } = useFlags<WebDashboardFeatureFlagSet>()
  const { data: obxEventTypesData } = useQuery<
    GetObxNotificationTypes,
    GetObxNotificationTypesVariables
  >(GET_OBX_NOTIFICATION_TYPES, {
    variables: {
      query: {
        orgId: orgId,
        typeIds: obxNotificationTypes,
      },
    },
    skip: obxNotificationTypes.length === 0,
  })
  const { data: subscriberInfo, loading } = useQuery<GetSubscriberInformation>(
    GET_SUBSCRIBER_INFORMATION,
    {
      variables: {
        contactInformationId: subscriberId,
      },
      skip: !subscriberId,
      fetchPolicy: 'no-cache',
    }
  )

  const allEventTypes = [...notificationEventTypes, ...obxNotificationTypes]

  const eventTypeOptions = useMemo(() => {
    const orEvents =
      eventTypesData?.eventTypes?.edges.map(({ node }) => {
        const optionNode = {
          id: node.id,
          name: node.name ?? node.id,
          group: 'OR Events',
        }

        return {
          node: optionNode,
        }
      }) ?? []

    const ehrEvents =
      obxEventTypesData?.observationTypeNames
        ?.map((item) => {
          if (!item) return null
          const optionNode = {
            id: item.typeId,
            name: item.name,
            group: 'EHR Events',
          }

          return {
            node: optionNode,
          }
        })
        .filter(Boolean) ?? []

    return [...orEvents, ...ehrEvents]
  }, [eventTypesData, obxEventTypesData])

  const currSubscriber = useMemo(
    () =>
      subscriberInfo?.contactInformation.edges.find(
        (edge) => edge.node.id === subscriberId
      )?.node,
    [subscriberInfo, subscriberId]
  )
  const initialSubscriptions: StaffAndEvent[] = useMemo(() => {
    const subscriptionsByStaffId = currSubscriber
      ? chain(currSubscriber.staffEventContactInformation)
          .groupBy('staff.id')
          .value()
      : {}
    return Object.entries(subscriptionsByStaffId).map(
      ([staffId, subscriptions]) => {
        return {
          staffId: staffId,
          eventTypes: subscriptions.map((subscription) =>
            subscription.eventType.id?.trim()
              ? subscription.eventType.id
              : (subscription.observationType?.id ?? '')
          ),
          archive: false,
          isNewSubscription: false,
        }
      }
    )
  }, [currSubscriber])

  const { data: staffData } = useQuery<GetStaff>(GET_STAFF, {
    variables: { orgId },
  })
  const onSubmit = async (
    {
      firstName,
      lastName,
      isApellaEmployee,
      contactInformationValue,
      staffEventSubscriptions,
    }: Omit<SubscriberContent, 'type'>,
    { setSubmitting }: FormikHelpers<SubscriberContent>
  ) => {
    const type = ContactInformationType.PHONE_NUMBER
    const subscriptions: SubscriberEventNotificationUpsertInput[] =
      staffEventSubscriptions.flatMap((staff) => {
        const selectedEvents = staff.eventTypes.map((event) => {
          return {
            staffId: staff.staffId,
            eventTypeId: event,
            archive: staff.archive,
          }
        })

        const eventsToArchive = allEventTypes
          .filter(
            (eventTypeId) =>
              !staff.eventTypes.some((event) => event === eventTypeId)
          )
          .map((event) => {
            return {
              staffId: staff.staffId,
              eventTypeId: event,
              archive: true,
            }
          })
        return [...selectedEvents, ...eventsToArchive]
      })

    // archive the subscriptions that are no longer selected but were a part of the initial subscriptions
    const currStaffIds = subscriptions.map(
      (subscription) => subscription.staffId
    )
    initialSubscriptions.map(({ staffId }) => {
      if (!currStaffIds.includes(staffId)) {
        // add to archive staffId
        const eventsToArchive = allEventTypes.map((event) => {
          return {
            staffId: staffId,
            eventTypeId: event,
            archive: true,
          }
        })
        subscriptions.push(...eventsToArchive)
      }
    })

    await setSubscribers({
      variables: {
        subscriberUpsertInput: [
          {
            subscriptions,
            contactInformationValue,
            contactInformationId: subscriberId,
            type,
            firstName,
            lastName,
            isApellaEmployee,
          },
        ],
      },
    })
    setSubmitting(false)
  }

  const staffOptions = useMemo(
    () =>
      staffData?.staff.edges.map(({ node: { id, name } }) => (
        <Option key={id} value={id} label={name} />
      )),
    [staffData]
  )
  const initialFormValues: SubscriberContent = useMemo(
    () => ({
      id: currSubscriber?.id ?? '',
      contactInformationValue: currSubscriber?.contactInformationValue ?? '',
      type: ContactInformationType.PHONE_NUMBER,
      staffEventSubscriptions: initialSubscriptions,
      firstName: currSubscriber?.firstName ?? '',
      lastName: currSubscriber?.lastName ?? '',
      isApellaEmployee: currSubscriber?.isApellaEmployee ?? false,
    }),
    [currSubscriber, initialSubscriptions]
  )

  if (loading) {
    return <ProgressOverlay />
  }

  return (
    <PageContentTemplate
      maxContainerSize="largeForm"
      title={`${formType} Subscribed Staff & Events`}
      breadcrumbs={[
        {
          to: LocationPath.Notifications,
          id: 'notifications',
          display: 'Notifications',
        },
      ]}
    >
      <Formik
        initialValues={initialFormValues}
        enableReinitialize
        validationSchema={subscriberSchema}
        onSubmit={onSubmit}
      >
        {({ values, setFieldValue, isValid, dirty, isSubmitting }) => {
          return (
            <Form>
              <Section>
                <FlexContainer
                  gap={remSpacing.small}
                  css={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}
                >
                  <Input.Text label="First Name" name={'firstName'} required />
                  <Input.Text label="Last Name" name={'lastName'} required />
                  <Input.Text
                    label="Phone Number"
                    name={'contactInformationValue'}
                    required
                  />
                  <Input.Checkbox
                    label="Check this box if you're an Apella employee"
                    ariaLabel="Is Apella Employee Checkbox"
                    name="isApellaEmployee"
                    value={values.isApellaEmployee}
                    as={FlexContainer}
                    asProps={{ alignItems: 'center', gap: remSpacing.xsmall }}
                  />
                </FlexContainer>
              </Section>

              <Section>
                <H4>Subscriptions</H4>
                <FieldArray
                  name="staffEventSubscriptions"
                  render={({ push, remove }) => (
                    <Tile
                      css={{
                        padding: remSpacing.medium,
                      }}
                    >
                      <Table>
                        <THead>
                          <TR>
                            <TH>Staff</TH>
                            <TH>Events</TH>
                            <TH>Actions</TH>
                          </TR>
                        </THead>
                        <TBody>
                          {values.staffEventSubscriptions.map(
                            (staffAndEvent, index) => {
                              const selectedStaffIds =
                                values.staffEventSubscriptions
                                  .filter(
                                    (s: StaffAndEvent, i) =>
                                      i !== index && s.staffId
                                  )
                                  .map((s: StaffAndEvent) => s.staffId)

                              return (
                                !staffAndEvent.archive && (
                                  <TR key={staffAndEvent.staffId}>
                                    <TD>
                                      <Input.SingleSelect
                                        name={`staffEventSubscriptions.${index}.staffId`}
                                        placeholder="Staff"
                                        search
                                      >
                                        {staffOptions?.filter(
                                          (option) =>
                                            !selectedStaffIds.includes(
                                              option.key || ''
                                            )
                                        )}
                                      </Input.SingleSelect>
                                    </TD>
                                    <TD>
                                      <MultiFilterWithCount
                                        label="Events"
                                        items={eventTypeOptions}
                                        selectedIds={staffAndEvent.eventTypes}
                                        onChange={(selected) => {
                                          setFieldValue(
                                            `staffEventSubscriptions.${index}.eventTypes`,
                                            selected
                                          )
                                        }}
                                      />
                                    </TD>
                                    <TD>
                                      {staffAndEvent.isNewSubscription ? (
                                        <DeleteSubscriptionButton
                                          remove={remove}
                                          index={index}
                                        />
                                      ) : (
                                        <ArchiveSubscriptionButton
                                          index={index}
                                          setFieldValue={setFieldValue}
                                        />
                                      )}
                                    </TD>
                                  </TR>
                                )
                              )
                            }
                          )}
                        </TBody>
                      </Table>
                      <FlexContainer
                        justifyContent="end"
                        css={{ marginTop: remSpacing.medium }}
                      >
                        <AddSubscriptionButton push={push} />
                      </FlexContainer>
                    </Tile>
                  )}
                />
              </Section>

              <FlexContainer justifyContent="end" gap={remSpacing.small}>
                <ButtonLink
                  appearance="link"
                  to={{ pathname: LocationPath.Notifications }}
                >
                  Cancel
                </ButtonLink>
                <Button
                  type="submit"
                  disabled={!isValid || !dirty || isSubmitting}
                >
                  Save
                </Button>
              </FlexContainer>
            </Form>
          )
        }}
      </Formik>
    </PageContentTemplate>
  )
}
