import {
  aStaffEventNotificationContactInformationConnection,
  aContactInformation,
  aStaff,
  anEventType,
  aStaffEventNotificationContactInformationEdge,
  aStaffEventNotificationContactInformation,
} from 'src/__generated__/generated-mocks'

export const GetStaffEventNotificationContactInformationData = {
  data: {
    staffEventNotificationContactInformation:
      aStaffEventNotificationContactInformationConnection({
        edges: [
          aStaffEventNotificationContactInformationEdge({
            node: aStaffEventNotificationContactInformation({
              id: 'notification-id-1',
              staff: aStaff({
                id: 'staff-id-1',
                name: 'Staff Name',
              }),
              eventType: anEventType({
                name: 'Pt wheeled out',
              }),
              contactInformation: aContactInformation({
                id: 'contact-info-id-1',
                firstName: 'Fake',
                lastName: 'Name',
                contactInformationValue: '555-123-1234',
              }),
            }),
          }),
          aStaffEventNotificationContactInformationEdge({
            node: aStaffEventNotificationContactInformation({
              id: 'notification-id-2',
              staff: aStaff({
                id: 'staff-id-1',
                name: 'Staff Name',
              }),
              eventType: anEventType({
                name: 'Pt wheeled in',
              }),
              contactInformation: aContactInformation({
                id: 'contact-info-id-1',
                firstName: 'Fake',
                lastName: 'Name',
                contactInformationValue: '555-123-1234',
              }),
            }),
          }),
          aStaffEventNotificationContactInformationEdge({
            node: aStaffEventNotificationContactInformation({
              id: 'notification-id-3',
              staff: aStaff({
                id: 'staff-id-1',
                name: 'Staff Name',
              }),
              eventType: anEventType({
                name: 'Pt wheeled in',
              }),
              contactInformation: aContactInformation({
                id: 'contact-info-id-2',
                firstName: 'Fake2',
                lastName: 'Name2',
                contactInformationValue: '555-789-7890',
              }),
            }),
          }),
        ],
      }),
  },
}
