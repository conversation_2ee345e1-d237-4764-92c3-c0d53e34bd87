import { generatePath } from 'react-router'

import { useQuery } from '@apollo/client'
import { chain } from 'lodash'

import { ButtonLink, Plus } from '@apella/component-library'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { useCurrentUser } from 'src/modules/user/hooks'
import { LocationPath } from 'src/router/types'

import { GetStaffEventNotificationContactInformation } from './__generated__/'
import { GET_NOTIFICATION_INFORMATION } from './queries'
import { SubscriberTable } from './SubscriberTable/SubscriberTable'
import { SubscriberTableFilters } from './SubscriberTable/SubscriberTableFilters'
import { FinalSubscriberContent, StaffAndEvent } from './types'

export const Notifications = (): React.JSX.Element => {
  const { currentOrganization } = useCurrentUser()
  const orgId = currentOrganization?.node?.id

  const { data, loading: isLoadingNotificationData } =
    useQuery<GetStaffEventNotificationContactInformation>(
      GET_NOTIFICATION_INFORMATION,
      { variables: { orgId } }
    )

  const subscriberInformation: FinalSubscriberContent[] = chain(
    data?.staffEventNotificationContactInformation.edges
  )
    .groupBy('node.contactInformation.contactInformationValue')
    .mapValues((contactInfos) => {
      const firstInfo = contactInfos[0].node.contactInformation

      const staffNameAndEvent: StaffAndEvent[] = chain(contactInfos)
        .groupBy('node.staff.name')
        .mapValues((staffGroup) => {
          const eventTypeNames = staffGroup
            .filter(
              ({ node }) => node.eventType.name || node.observationType?.name
            )
            .map(({ node }) =>
              node.eventType.name && node.eventType.name !== ''
                ? node.eventType.name
                : (node.observationType?.name ?? '')
            )

          return {
            staff: staffGroup[0].node.staff.name,
            eventType: eventTypeNames,
          }
        })
        .values()
        .value()

      return {
        id: firstInfo.id,
        recipientName: `${firstInfo.firstName} ${firstInfo.lastName}`,
        phoneNumber: firstInfo.contactInformationValue ?? '',
        staffNameAndEvent,
      }
    })
    .values()
    .value()

  return (
    <PageContentTemplate
      title="Staff Event Notifications"
      actions={
        <ButtonLink size="sm" to={generatePath(LocationPath.AddSubscriber)}>
          <Plus size="xs" />
          Add subscriber
        </ButtonLink>
      }
      filters={
        <SubscriberTableFilters subscriberInfo={subscriberInformation} />
      }
    >
      {isLoadingNotificationData && <LoadingOverlay />}
      <SubscriberTable subscriberInfo={subscriberInformation} />
    </PageContentTemplate>
  )
}
