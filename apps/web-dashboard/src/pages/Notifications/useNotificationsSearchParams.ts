import { useCallback } from 'react'
import { useSearchParams } from 'react-router'

enum NotificationFilterParams {
  FILTER_PHONE_NUMBER = 'phoneNumber',
  FILTER_SUBSCRIBER = 'subscriber',
  FILTER_SUBSCRIBER_STAFF = 'subscriberStaff',
}

const DEFAULT_SEARCH_PARAM = ''
export const useNotificationsSearchParams = () => {
  const [searchParams, setSearchParams] = useSearchParams()

  const selectedPhoneNumber =
    searchParams.get(NotificationFilterParams.FILTER_PHONE_NUMBER) ?? ''

  const selectedSubscriber =
    searchParams.get(NotificationFilterParams.FILTER_SUBSCRIBER) ?? ''

  const selectedSubscriberStaff =
    searchParams.get(NotificationFilterParams.FILTER_SUBSCRIBER_STAFF) ?? ''

  const onChangePhoneNumber = useCallback(
    (phoneNumber?: string) => {
      searchParams.set(
        NotificationFilterParams.FILTER_PHONE_NUMBER,
        phoneNumber || DEFAULT_SEARCH_PARAM
      )
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams]
  )

  const onChangeSubscriber = useCallback(
    (subscriber?: string) => {
      searchParams.set(
        NotificationFilterParams.FILTER_SUBSCRIBER,
        subscriber || DEFAULT_SEARCH_PARAM
      )
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams]
  )

  const onChangeSubscriberStaff = useCallback(
    (subscriberStaff?: string) => {
      searchParams.set(
        NotificationFilterParams.FILTER_SUBSCRIBER_STAFF,
        subscriberStaff || DEFAULT_SEARCH_PARAM
      )
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams]
  )
  const resetActions = useCallback(() => {
    onChangePhoneNumber('')
    onChangeSubscriber('')
    onChangeSubscriberStaff('')
  }, [onChangePhoneNumber, onChangeSubscriber, onChangeSubscriberStaff])
  return {
    selectedPhoneNumber,
    selectedSubscriber,
    selectedSubscriberStaff,

    onChangePhoneNumber,
    onChangeSubscriber,
    onChangeSubscriberStaff,
    resetActions,
  }
}
