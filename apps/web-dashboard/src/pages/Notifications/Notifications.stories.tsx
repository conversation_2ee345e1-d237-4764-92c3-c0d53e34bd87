import { Apollo<PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import { Meta, StoryObj } from '@storybook/react'
import { delay, graphql, http, HttpResponse } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'

import { GetStaffEventNotificationContactInformationDocument } from './__generated__'
import { GetStaffEventNotificationContactInformationData } from './__mocks__/GetStaffEventNotificationContactInformationData'
import { Notifications } from './StaffEventNotifications'

const mockedClient = new ApolloClient({
  uri: 'https://your-graphql-endpoint',
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
    query: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
  },
})

const meta: Meta<typeof Notifications> = {
  component: Notifications,
  decorators: [
    (Story) => (
      <TimezoneProvider>
        <ApolloProvider client={mockedClient}>
          <Story />
        </ApolloProvider>
      </TimezoneProvider>
    ),
    withRouter,
  ],
}

export default meta
type Story = StoryObj<typeof Notifications>
const routing = {
  path: '/notifications',
  handle: 'Notifications',
}

const msw = (
  wait?: number,
  {
    getStaffEventNotificationContactInformationData = GetStaffEventNotificationContactInformationData,
  } = {}
) => ({
  handlers: [
    http.get('https://events.launchdarkly.com/events/diagnostic/', () => {
      return HttpResponse.json({})
    }),
    http.get('https://app.launchdarkly.com/sdk/goals/', () => {
      return HttpResponse.json({})
    }),
    graphql.query(
      GetStaffEventNotificationContactInformationDocument,
      async () => {
        await delay(wait ?? 300)
        return HttpResponse.json(
          getStaffEventNotificationContactInformationData
        )
      }
    ),
  ],
})

export const StaffEventNotificationsLoadingState: Story = {
  parameters: {
    date: new Date(2024, 8, 12, 12),
    msw: msw(60 * 60 * 1000),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/notifications',
      },
      routing,
    },
  },
}

// TODO (RT-2091): Fix flakey test
// export const StaffEventNotifications: Story = {
//   parameters: {
//     date: new Date(2024, 8, 12, 12),
//     msw: msw(0),
//     decorators: [withRouter],
//     reactRouter: {
//       location: {
//         path: '/notifications',
//       },
//       routing,
//     },
//   },
// }
