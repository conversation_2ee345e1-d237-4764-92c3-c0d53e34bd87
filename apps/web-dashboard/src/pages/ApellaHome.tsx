import { Link } from 'react-router'

import { rem } from 'polished'

import {
  ApellaLogo,
  H4,
  mediaQueries,
  MenuItemProps,
  P3,
  remSpacing,
  theme,
} from '@apella/component-library'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { useMenuItems } from 'src/router/useMenuItems'

export const ApellaHome = ({
  menuItems,
}: {
  menuItems?: MenuItemProps[]
}): React.JSX.Element => {
  const subTextLookUp: { [key: string]: string | undefined } = {
    Schedule: 'Keep today on track',
    Live: 'View live OR status',
    'Schedule Assistant': 'Optimize OR planning',
    Insights: 'Create custom reports',
    Boards: 'Use big screen views',
    'Terminal Cleans': 'Audit room cleans',
    Highlights: 'Save case video clips',
    'Block Utilization': 'Track block time usage',
  }
  const { menuItems: defaultMenuItems } = useMenuItems()
  const menuItemsToUse = menuItems ?? defaultMenuItems

  return (
    <PageContentTemplate>
      <div
        css={{
          padding: `${remSpacing.large} 0`,
          [mediaQueries.lg]: {
            padding: `${remSpacing.xxlarge} ${remSpacing.xxxxlarge}`,
          },
        }}
      >
        <div css={{ marginLeft: '0.3rem' }}>
          <ApellaLogo type="full" multiplier={1.5} />
        </div>
        <div
          css={{
            paddingLeft: '24px',
            display: 'grid',
            gridTemplateColumns: 'repeat(2, 1fr)',
            gap: remSpacing.gutter,
            paddingTop: remSpacing.gutter,
            [mediaQueries.lg]: {
              justifyContent: 'flex-start',
              paddingTop: rem('180px'),
              display: 'flex',
              alignItems: 'flex-start',
              rowGap: remSpacing.xxlarge,

              flexWrap: 'wrap',

              columnGap: remSpacing.gutter,
            },
          }}
        >
          {menuItemsToUse.map((route) => (
            <Link
              key={route.title}
              css={{
                ':hover': {
                  backgroundColor: theme.palette.blue.background,
                  cursor: 'pointer',
                },
                display: 'flex',
                gap: remSpacing.gutter,
                flexWrap: 'wrap',
                alignItems: 'flex-start',
                flexDirection: 'column',
                textDecoration: 'none',
                [mediaQueries.lg]: {
                  width: '200px',
                  padding: remSpacing.medium,
                  borderRadius: remSpacing.gutter,
                },
              }}
              to={route.to ?? ''}
            >
              <div
                css={{
                  padding: '24px',
                  backgroundColor: route.backgroundColor,
                  borderRadius: remSpacing.small,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  gap: remSpacing.gutter,
                }}
              >
                <route.Icon size="xxl" color={route.iconColor} />
              </div>

              <div
                css={{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  flexDirection: 'column',
                  gap: remSpacing.xxsmall,
                }}
              >
                <H4 css={{ color: theme.palette.text.primary }}>
                  {route.title}
                </H4>
                {route.title && subTextLookUp[route.title] && (
                  <P3 css={{ color: theme.palette.text.tertiary }}>
                    {subTextLookUp[route.title]}
                  </P3>
                )}
              </div>
            </Link>
          ))}
        </div>
      </div>
    </PageContentTemplate>
  )
}
