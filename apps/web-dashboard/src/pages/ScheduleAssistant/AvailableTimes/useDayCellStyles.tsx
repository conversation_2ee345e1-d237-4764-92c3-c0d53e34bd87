import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

export const useDayCellStyles = () => {
  const theme = useTheme()
  return useMemo(
    () => ({
      PlaceHolder: {
        backgroundColor: 'inherit',
        border: 'inherit',
        color: 'inherit',
      },
      DateInThePast: {
        backgroundColor: 'inherit',
        border: 'inherit',
        color: theme.palette.gray[50],
      },
      Today: {
        backgroundColor: theme.palette.blue[50],
        border: `2px solid ${theme.palette.blue[50]}`,
        color: theme.palette.background.primary,
      },
      Weekend: {
        backgroundColor: theme.palette.gray[10],
        border: `2px solid ${theme.palette.gray[10]}`,
      },
      DateWithSlots: {
        backgroundColor: theme.palette.green[10],
        border: `2px solid ${theme.palette.green[10]}`,
      },
      SelectedDateWithSlots: {
        backgroundColor: theme.palette.green[10],
        border: `2px solid ${theme.palette.green[50]}`,
      },
      DateWithNoSlots: {
        backgroundColor: 'inherit',
        border: `2px solid ${theme.palette.background.primary}`,
      },
    }),
    [theme]
  )
}
