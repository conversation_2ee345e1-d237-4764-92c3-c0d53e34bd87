import { useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { isUndefined } from 'lodash'
import { DateTime, Duration } from 'luxon'

import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import {
  GetAvailableTimeSlots,
  GetAvailableTimeSlotsVariables,
  GetPrimarySurgeonInformation,
  GetPrimarySurgeonInformationVariables,
} from '../__generated__'
import { BlockAttributes } from '../interfaces'
import {
  GET_AVAILABLE_TIME_SLOTS,
  GET_PRIMARY_SURGEON_INFORMATION,
} from '../queries'
import { ScheduleAssistantSurgeonProcedureFilters } from '../useScheduleAssistantSurgeonProcedureFilters'
import { removeDuplicateAvailableSlots } from '../utils'
import { CASE_DURATION_PAGE_POLL_INTERVAL, SLOT_DATE_FMT } from './constants'
import { IAvailableSlot, IRoomInfo } from './interfaces'
import { useTimeFinderCaseData } from './useApellaCaseData'
import { AvailableTimesFilters } from './useAvailableTimesFilters'
import { useBlockData } from './useBlockData'
import { getAvailableSlotsFlat, getDayOfWeek } from './utils'

export const useAvailableSlotsData = ({
  selectedSurgeonId,
  selectedDaysOfWeek,
  selectedRoomIds,
  selectedSiteIds,
  selectedBlockIds,
  minAvailableDuration,
  timeRange,
  timezone,
}: {
  selectedSurgeonId: ScheduleAssistantSurgeonProcedureFilters['surgeon']
  selectedDaysOfWeek: AvailableTimesFilters['dayOfWeek']
  selectedRoomIds: AvailableTimesFilters['rooms']
  selectedSiteIds: AvailableTimesFilters['sites']
  selectedBlockIds: AvailableTimesFilters['blocks']
  minAvailableDuration: Duration
  timeRange: string[]
  timezone: string
}) => {
  const { sites, isLoading: sitesLoading } = useSiteOptions()
  const rooms = useRoomOptions()
  const roomInfoMap = useMemo(
    () =>
      rooms.reduce((acc, room) => {
        acc.set(room?.node?.id, {
          siteId: room?.node?.site.id,
          siteName: room?.node?.site.name,
          roomId: room?.node?.id,
          roomName: room?.node?.name,
        })
        return acc
      }, new Map<string, IRoomInfo>()),
    [rooms]
  )
  const { data: surgeonData, loading: surgeonDataLoading } = useQuery<
    GetPrimarySurgeonInformation,
    GetPrimarySurgeonInformationVariables
  >(GET_PRIMARY_SURGEON_INFORMATION, {
    variables: { id: selectedSurgeonId },
    pollInterval: CASE_DURATION_PAGE_POLL_INTERVAL,
  })
  const {
    siteId: primarySiteId,
    siteTimezone: primarySiteTimezone,
    surgeonBlockIds,
  } = useMemo(() => {
    if (!surgeonData?.staff || surgeonData.staff.edges.length !== 1) {
      return {}
    }
    const surgeon = surgeonData.staff.edges[0].node

    return {
      surgeonBlockIds: surgeon.blockIds?.filter(
        (blockId): blockId is string => blockId !== null
      ),
      siteId: surgeon.mostFrequentSite?.id,
      siteTimezone: surgeon.mostFrequentSite?.timezone,
    }
  }, [surgeonData])

  const { isTimeFinderCaseDataLoading, timeFinderCaseData } =
    useTimeFinderCaseData({
      minEndTime: DateTime.fromFormat(timeRange[0], SLOT_DATE_FMT, {
        zone: timezone,
      }).startOf('day'),
      maxStartTime: DateTime.fromFormat(timeRange[1], SLOT_DATE_FMT, {
        zone: timezone,
      }).endOf('day'),
      siteIds: selectedSiteIds || [],
      skip: !selectedSurgeonId || !selectedSiteIds || !selectedSiteIds.length,
    })

  const { data: availableTimeSlots, loading: isAvailableTimeSlotDataLoading } =
    useQuery<GetAvailableTimeSlots, GetAvailableTimeSlotsVariables>(
      GET_AVAILABLE_TIME_SLOTS,
      {
        skip: !selectedSurgeonId || !selectedSiteIds || !selectedSiteIds.length,
        variables: {
          minAvailableDuration: minAvailableDuration.toISO(),
          startDate: timeRange[0],
          endDate: timeRange[1],
          surgeonId: selectedSurgeonId,
          siteIds: selectedSiteIds || [],
        },
        pollInterval: CASE_DURATION_PAGE_POLL_INTERVAL,
      }
    )

  const flatSlots = useMemo(
    () =>
      getAvailableSlotsFlat({
        availableTimeSlots: availableTimeSlots?.availableTimeSlots ?? [],
        minAvailableDuration,
        timezone,
        roomInfoMap,
      }),
    [availableTimeSlots, minAvailableDuration, timezone, roomInfoMap]
  )

  const {
    blockTimeIdMap,
    blocks,
    loading: isBlockDataLoading,
  } = useBlockData({
    slots: flatSlots,
  })
  const availableFlatSlots = useMemo(() => {
    if (
      !isUndefined(selectedRoomIds) &&
      !isUndefined(selectedSiteIds) &&
      !isUndefined(selectedDaysOfWeek)
    ) {
      return flatSlots.reduce<IAvailableSlot[]>((acc, slot) => {
        if (!selectedDaysOfWeek.includes(getDayOfWeek(slot.date))) {
          return acc
        }
        if (slot.startTime < DateTime.now()) {
          return acc
        }
        if (selectedRoomIds?.length && !selectedRoomIds.includes(slot.roomId)) {
          return acc
        }
        if (selectedSiteIds?.length && !selectedSiteIds.includes(slot.siteId)) {
          return acc
        }
        acc.push(slot)
        return acc
      }, [])
    }
    return undefined
  }, [flatSlots, selectedRoomIds, selectedSiteIds, selectedDaysOfWeek])

  const filteredFlatSlots = useMemo(() => {
    if (!isUndefined(availableFlatSlots) && !isUndefined(selectedBlockIds)) {
      const availableFilteredSlots = availableFlatSlots.reduce<
        IAvailableSlot[]
      >((acc, slot) => {
        const blocksWithoutUnblockedIds = slot.blockTimeIds
          .map((id) => blockTimeIdMap.get(id)?.block)
          .filter((block): block is BlockAttributes => block !== undefined)

        const hasSelectedBlock = blocksWithoutUnblockedIds.every((block) =>
          selectedBlockIds?.includes(block.id)
        )

        if (hasSelectedBlock) {
          acc.push(slot)
        }
        return acc
      }, [])

      return removeDuplicateAvailableSlots(availableFilteredSlots)
    }
    return undefined
  }, [availableFlatSlots, selectedBlockIds, blockTimeIdMap])

  const surgeonFilteredFlatSlots = useMemo(() => {
    if (!isUndefined(availableFlatSlots)) {
      return availableFlatSlots.reduce<IAvailableSlot[]>((acc, slot) => {
        const hasSurgeonBlock =
          slot.blockTimeIds.length > 0 &&
          slot.blockTimeIds
            .map((id) => blockTimeIdMap.get(id)?.block)
            .filter((block): block is BlockAttributes => block !== undefined)
            .some((block) => {
              return surgeonBlockIds?.includes(block.id)
            })

        if (hasSurgeonBlock) {
          acc.push(slot)
        }
        return acc
      }, [])
    }
    return undefined
  }, [availableFlatSlots, surgeonBlockIds, blockTimeIdMap])

  return {
    surgeonFilteredFlatSlots,
    filteredFlatSlots,
    timeFinderCaseData,
    primarySiteId,
    primarySiteTimezone,
    blockTimeIdMap,
    surgeonBlockIds,
    blocks,
    availabilityDataLoading:
      sitesLoading || isAvailableTimeSlotDataLoading || surgeonDataLoading,
    surgeonDataLoading,
    isTimeFinderCaseDataLoading,
    isBlockDataLoading,
    sites,
    rooms,
  }
}
