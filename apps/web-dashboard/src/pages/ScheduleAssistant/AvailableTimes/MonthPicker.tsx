import {
  CSSProperties,
  Dispatch,
  forwardRef,
  ReactElement,
  ReactNode,
  SetStateAction,
  useCallback,
  useState,
} from 'react'

import { useTheme } from '@emotion/react'

import { useFloating, offset, flip, shift } from '@floating-ui/react'
import { DateTime, Info } from 'luxon'
import { rem } from 'polished'

import {
  Button,
  CalendarIcon,
  ChevronLeft,
  ChevronRight,
  DropdownWrapper,
  FlexContainer,
  H5,
  Span2,
  Tile,
  remSpacing,
} from '@apella/component-library'

import { FILTER_PADDING } from './constants'
import { useAvailableTimesContext } from './contexts'

export interface MonthPickerProps {
  children?: ReactElement
  firstDayOfMonth: DateTime
  onClick?: () => void
  setMonth: (date: DateTime) => void
}

export const MonthPicker = ({
  firstDayOfMonth,
  setMonth,
  onClick: onClickProp,
}: MonthPickerProps) => {
  const theme = useTheme()
  const [selectedYear, setSelectedYear] = useState(firstDayOfMonth.year)
  const [isOpen, setIsOpen] = useState(false)
  const { refs, floatingStyles } = useFloating({
    placement: 'bottom',
    middleware: [offset({ mainAxis: 5, crossAxis: 10 }), flip(), shift()],
  })

  const onClose = useCallback(() => {
    setIsOpen(false)
  }, [setIsOpen])

  const onClick = useCallback(() => {
    onClickProp?.()
    setIsOpen((isOpen) => !isOpen)
  }, [onClickProp])

  return (
    <DropdownWrapper
      display="block"
      onClose={onClose}
      open={isOpen}
      css={{
        justifySelf: 'center',
        paddingBottom: `${FILTER_PADDING}px`,
      }}
    >
      <div ref={refs.setReference}>
        <Button color="alternate" onClick={onClick}>
          <CalendarIcon size="sm" color={theme.palette.text.tertiary} />
          <H5 as="h4">
            {firstDayOfMonth.monthLong} {firstDayOfMonth.year}
          </H5>
        </Button>
      </div>

      <MonthPickerBody
        open={isOpen}
        ref={refs.setFloating}
        style={floatingStyles}
      >
        <MonthPickerNavHeader year={selectedYear} setYear={setSelectedYear} />

        <MonthPickerGrid
          selectedFirstDayOfMonth={firstDayOfMonth}
          selectedYear={selectedYear}
          setMonth={setMonth}
        />
      </MonthPickerBody>
    </DropdownWrapper>
  )
}

export interface MonthPickerBodyProps {
  children: ReactNode
  className?: string
  gutter?: boolean | string
  inline?: boolean
  open: boolean
  style?: CSSProperties
}

export const MonthPickerBody = forwardRef<HTMLDivElement, MonthPickerBodyProps>(
  (
    { children, gutter, open, className, style },
    ref
  ): React.JSX.Element | null => {
    const theme = useTheme()

    if (!open) {
      return null
    }

    return (
      <div
        css={{
          backgroundColor: theme.palette.background.primary,
          minWidth: rem('268px'),
          minHeight: rem('212px'),
          display: 'flex',
          flexDirection: 'column',
          padding: remSpacing.xsmall,
        }}
        className={className}
        ref={ref}
        style={style}
      >
        <Tile gutter={gutter}>{children}</Tile>
      </div>
    )
  }
)
MonthPickerBody.displayName = 'MonthPickerBody'

const MonthPickerNavHeader = ({
  year,
  setYear,
}: {
  year: number
  setYear: Dispatch<SetStateAction<number>>
}) => {
  const { maxDate } = useAvailableTimesContext()

  return (
    <FlexContainer
      css={{ justifyContent: 'space-between', alignItems: 'center' }}
    >
      <Button
        buttonType="icon"
        appearance="link"
        size="sm"
        onClick={() => setYear((prev) => prev - 1)}
        disabled={year === DateTime.now().year}
      >
        <ChevronLeft />
      </Button>

      <H5 as="h5">{year}</H5>

      <Button
        buttonType="icon"
        appearance="link"
        size="sm"
        onClick={() => setYear((prev) => prev + 1)}
        disabled={year === maxDate.year}
      >
        <ChevronRight />
      </Button>
    </FlexContainer>
  )
}

const MonthPickerGrid = ({
  selectedFirstDayOfMonth,
  selectedYear,
  setMonth,
}: {
  selectedFirstDayOfMonth: DateTime
  selectedYear: number
  setMonth: (date: DateTime) => void
}) => {
  const theme = useTheme()
  const { maxDate } = useAvailableTimesContext()

  return (
    <div
      css={{
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: remSpacing.xxsmall,
      }}
    >
      {Info.months('short').map((monthShort, index) => {
        const month = index + 1
        const firstDayOfMonth = DateTime.fromObject({
          year: selectedYear,
          month,
          day: 1,
        })
        const isOutOfRange =
          DateTime.now().diff(firstDayOfMonth, 'months').months > 1 ||
          firstDayOfMonth > maxDate

        return (
          <Button
            size="sm"
            buttonType="icon"
            css={{ boxShadow: 'none' }}
            color={
              selectedFirstDayOfMonth.hasSame(firstDayOfMonth, 'month')
                ? 'primary'
                : 'alternate'
            }
            key={`month-label-${selectedYear}-${monthShort}`}
            disabled={isOutOfRange}
            onClick={() => setMonth(firstDayOfMonth)}
          >
            <Span2
              style={{
                color: isOutOfRange ? theme.palette.text.tertiary : undefined,
              }}
            >
              {monthShort}
            </Span2>
          </Button>
        )
      })}
    </div>
  )
}
