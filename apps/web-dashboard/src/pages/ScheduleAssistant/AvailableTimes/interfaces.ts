import { DateTime, Duration } from 'luxon'

export type DateString = string

export type DateOffsetPair = [DateString, number | undefined]

export interface IRoomTagInfo {
  id: string
  name: string
}

export interface IRoomInfo {
  roomId: string
  roomName: string
  roomTags?: IRoomTagInfo[]
  siteId: string
  siteName: string
}

export interface IAvailableSlot extends IRoomInfo {
  blockTimeIds: string[]
  date: DateString
  endTime: DateTime
  id: string
  maxAvailableDuration: Duration
  startTime: DateTime
}
