import {
  memo,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useState,
} from 'react'

import { DateTime } from 'luxon'

import { Span3 } from '@apella/component-library'

import { CELL_BASE_STYLE } from './constants'
import { useAvailableTimesContext } from './contexts'
import { useDayCellStyles } from './useDayCellStyles'

interface DayCellProps {
  day?: DateTime
  hasSlots?: boolean
  onClick?: (date: DateTime) => void
}

export const DayCell = memo(function ({
  day,
  hasSlots,
  onClick,
}: DayCellProps) {
  const DayCellStyles = useDayCellStyles()
  const { firstDayInScrollView } = useAvailableTimesContext()
  const [today, setToday] = useState(DateTime.now())
  const [attrs, setAttrs] = useState<{
    backgroundColor: string
    border: string
    color?: string
  }>(() => {
    // Initial state to prevent flashing DayCells
    if (!day) {
      return DayCellStyles.PlaceHolder
    }

    if (DateTime.now().startOf('day') > day.startOf('day')) {
      return DayCellStyles.DateInThePast
    }

    if (DateTime.now().hasSame(day, 'day')) {
      return DayCellStyles.Today
    }

    if (hasSlots) {
      return DayCellStyles.DateWithSlots
    }

    if (day.weekday >= 6) {
      return DayCellStyles.Weekend
    }

    return DayCellStyles.DateWithNoSlots
  })

  const dayLabel = useMemo(
    () => day && <Span3>{day.toFormat('dd')}</Span3>,
    [day]
  )

  useLayoutEffect(() => {
    const updateCellAttrs = () => {
      if (!day) {
        return setAttrs(DayCellStyles.PlaceHolder)
      }

      // Blue for today
      if (DateTime.now().hasSame(day, 'day')) {
        setToday(day)
        return setAttrs(DayCellStyles.Today)
      }

      if (DateTime.now().startOf('day') > day.startOf('day')) {
        return setAttrs(DayCellStyles.DateInThePast)
      }

      // Highlight the day cell if the calendar is scrolled to the day
      if (
        firstDayInScrollView &&
        DateTime.fromISO(firstDayInScrollView).hasSame(day, 'day')
      ) {
        return setAttrs(DayCellStyles.SelectedDateWithSlots)
      }

      // Green for days with slots
      if (hasSlots) {
        return setAttrs(DayCellStyles.DateWithSlots)
      }

      // Gray for weekends
      if (day.weekday >= 6) {
        return setAttrs(DayCellStyles.Weekend)
      }

      // Default
      return setAttrs(DayCellStyles.DateWithNoSlots)
    }

    updateCellAttrs()

    const [mainElement] = document.body.getElementsByTagName('main')
    mainElement?.addEventListener('scroll', updateCellAttrs)

    return () => {
      mainElement?.removeEventListener('scroll', updateCellAttrs)
    }
  }, [
    day,
    hasSlots,
    DayCellStyles.PlaceHolder,
    DayCellStyles.DateInThePast,
    DayCellStyles.DateWithNoSlots,
    DayCellStyles.DateWithSlots,
    DayCellStyles.SelectedDateWithSlots,
    DayCellStyles.Today,
    DayCellStyles.Weekend,
    firstDayInScrollView,
  ])

  useEffect(() => {
    const interval = setInterval(() => {
      if (!DateTime.now().hasSame(today, 'day')) {
        window.location.reload()
      }
    }, 60000 * 10) // checks every 10 minutes

    return () => clearInterval(interval)
  }, [today])

  const onClickHandler = useCallback(() => {
    if (day && hasSlots) {
      onClick?.(day)
    }
  }, [day, hasSlots, onClick])

  return (
    <div
      css={{
        ...CELL_BASE_STYLE,
        cursor: hasSlots ? 'pointer' : undefined,
      }}
      style={attrs}
      onClick={onClickHandler}
    >
      {dayLabel}
    </div>
  )
})

DayCell.displayName = 'DayCell'
