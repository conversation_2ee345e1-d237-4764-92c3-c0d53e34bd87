import Skeleton from 'react-loading-skeleton'

import {
  Button,
  Close,
  H5,
  P2,
  remSpacing,
  theme,
} from '@apella/component-library'
import { WarningMessage } from 'src/components/WarningMessage'

export const SurgeonWithBlockNoAvailableTimes = ({
  isLoa<PERSON>,
  surgeon,
  onClose,
}: {
  isLoading: boolean
  surgeon: string
  onClose: () => void
}) => {
  const CloseButton = ({ onClose }: { onClose: () => void }) => (
    <Button
      color="black"
      appearance="link"
      onClick={onClose}
      buttonType={'icon'}
      css={{ padding: 0 }}
    >
      <Close css={{ color: theme.palette.gray[60] }} size={'sm'} />
    </Button>
  )
  return isLoading ? (
    <Skeleton height={50} width={'full-width'} />
  ) : (
    <WarningMessage
      css={{
        width: '100%',
        paddingRight: remSpacing.small,
      }}
    >
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
          alignSelf: 'stretch',
        }}
      >
        <H5 css={{ color: theme.palette.text.primary }}>
          Currently viewing unblocked available times
        </H5>
        <CloseButton onClose={onClose} />
      </div>
      <P2
        css={{
          color: theme.palette.text.secondary,
        }}
      >
        No block availability was found for {surgeon}
      </P2>
    </WarningMessage>
  )
}
