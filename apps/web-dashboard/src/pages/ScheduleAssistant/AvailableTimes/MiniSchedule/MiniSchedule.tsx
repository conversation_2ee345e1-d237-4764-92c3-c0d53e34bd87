import { memo, useCallback, useMemo, useState } from 'react'
import Skeleton from 'react-loading-skeleton'

import { useTheme } from '@emotion/react'

import { styled } from '@storybook/theming'
import { DateTime } from 'luxon'
import { rem } from 'polished'

import {
  Caps1Bold,
  Clock,
  FlexContainer,
  H6,
  P3,
  Tooltip,
  TooltipProps,
  ZIndex,
  remSpacing,
  ApellaDateTimeFormats,
} from '@apella/component-library'
import { useSize } from '@apella/hooks'
import { useTimezone } from 'src/Contexts'
import { PillHolder } from 'src/pages/Schedule/TimelinePill/TimelinePill'
import { MouseHoverState, MouseMovementHandler } from 'src/pages/Schedule/types'
import { CaseTooltipHoverInterConfiguration } from 'src/pages/types'
import { EM_DASH } from 'src/utils/htmlCodes'
import { isPrimarySurgeon, toStaffFullName } from 'src/utils/roles'
import { CaseStatusNameStyleConfig } from 'src/utils/status'
import {
  calculatePillPositionAndSizeHelper,
  PillPositionAndSize,
} from 'src/utils/timelineHelpers'

import { TimeFinderCase } from '../useApellaCaseData'
import { getSlotTimeRange } from '../utils'

export const MINI_SCHEDULE_HEIGHT = 20
export const MINI_SCHEDULE_WIDTH = 300

const PRIME_TIME_START = 7
const PRIME_TIME_END = 17

const PILL_HEIGHT = 12
const PILL_OFFSET = (MINI_SCHEDULE_HEIGHT - PILL_HEIGHT) * 1.5

export interface MiniDateRoomScheduleProps {
  durationEstInMins: number
  endTime: DateTime
  id: string
  loading: boolean
  startTime: DateTime
  timeFinderCases: TimeFinderCase[]
}

export const MiniDateRoomSchedule = ({
  loading,
  timeFinderCases,
  id,
  startTime,
  endTime,
  durationEstInMins,
}: MiniDateRoomScheduleProps) => {
  const theme = useTheme()
  const [miniTimelineRef, setMiniTimelineRef] = useState<HTMLDivElement | null>(
    null
  )
  const primeTimeStart = startTime
    .startOf('day')
    .plus({ hours: PRIME_TIME_START })
  const primeTimeEnd = startTime.startOf('day').plus({ hours: PRIME_TIME_END })
  const timelineSize = useSize(miniTimelineRef)

  const [hoverState, setHoverState] = useState<MouseHoverState>({})

  const onMouseEnter: MouseMovementHandler = (id?: string) =>
    id &&
    setHoverState((prevHoverState) => ({
      ...prevHoverState,
      [id]: true,
    }))

  const onMouseLeave: MouseMovementHandler = (id?: string) =>
    id &&
    setHoverState((prevHoverState) => ({
      ...prevHoverState,
      [id]: false,
    }))

  const calculatePillPositionAndSize = useCallback(
    (
      startTime: DateTime,
      endTime?: DateTime,
      minWidth: number = 6
    ): PillPositionAndSize => {
      if (!timelineSize?.width)
        return {
          left: '0',
          widthPct: '0%',
          widthPx: 0,
          forecastedWidthPx: 0,
          doesOverflowMinTime: false,
          doesOverflowMaxTime: false,
          gradientWidthPx: 0,
        }
      return calculatePillPositionAndSizeHelper({
        startTime,
        timelineWidth: timelineSize.width,
        minHour: primeTimeStart,
        maxHour: primeTimeEnd,
        minWidth,
        endTime,
      })
    },
    [timelineSize?.width, primeTimeStart, primeTimeEnd]
  )

  return (
    <div
      ref={setMiniTimelineRef}
      css={{
        zIndex: ZIndex.DEFAULT,
        width: MINI_SCHEDULE_WIDTH,
        height: MINI_SCHEDULE_HEIGHT,
        marginTop: MINI_SCHEDULE_HEIGHT * -0.5,
        position: 'relative',
        alignSelf: 'center',
        ...(!loading && {
          alignSelf: undefined,
          '&:after': {
            content: '""',
            display: 'block',
            height: '100%',
            borderBottom: `4px solid ${theme.palette.gray[20]}`,
          },
        }),
      }}
    >
      {loading ? (
        <Skeleton />
      ) : (
        <>
          {timeFinderCases.map((timeFinderCase) => {
            const caseStartTime = timeFinderCase.case?.scheduledStartTime
              ? DateTime.fromISO(timeFinderCase.case.scheduledStartTime)
              : DateTime.fromISO(timeFinderCase.startTime)
            const caseEndTime = timeFinderCase.case?.scheduledEndTime
              ? DateTime.fromISO(timeFinderCase.case.scheduledEndTime)
              : timeFinderCase.endTime
                ? DateTime.fromISO(timeFinderCase.endTime)
                : DateTime.now()

            return (
              <TimeFinderCasePill
                key={timeFinderCase.id}
                tooltipBody={
                  <TimeFinderCaseTooltipBody timeFinderCase={timeFinderCase} />
                }
                tooltipMiddleware={[
                  Tooltip.middleware.autoPlacement(),
                  Tooltip.middleware.shift(),
                  Tooltip.middleware.offset(8),
                ]}
                tooltipCss={{
                  padding: remSpacing.xsmall,
                  maxWidth: rem(300),
                }}
                timeFinderCase={timeFinderCase}
                pillPositionAndSize={calculatePillPositionAndSize(
                  caseStartTime,
                  caseEndTime
                )}
                color={theme.palette.gray[30]}
                hoveredColor={theme.palette.blue[10]}
                isHovered={hoverState[timeFinderCase.id]}
                onMouseEnter={() => onMouseEnter(timeFinderCase.id)}
                onMouseLeave={() => onMouseLeave(timeFinderCase.id)}
              />
            )
          })}
          <TimeFinderCasePill
            key={id}
            pillPositionAndSize={calculatePillPositionAndSize(
              startTime,
              endTime
            )}
            color={theme.palette.blue[10]}
          />
          {durationEstInMins ? (
            <TimeFinderCasePill
              key={`${id}-estimate`}
              tooltipBody={
                <AvailableSlotTooltipBody
                  startTime={startTime}
                  endTime={startTime.plus({
                    minutes: durationEstInMins,
                  })}
                />
              }
              tooltipMiddleware={[
                Tooltip.middleware.shift(),
                Tooltip.middleware.offset(8),
              ]}
              tooltipPlacement="top-start"
              tooltipCss={{
                padding: `4px 6px`,
                borderRadius: 0,
                boxShadow: 'none',
                background: theme.palette.blue.background,
                borderLeft: `2px solid ${theme.palette.blue[50]}`,
              }}
              pillPositionAndSize={calculatePillPositionAndSize(
                startTime,
                startTime.plus({
                  minutes: durationEstInMins,
                })
              )}
              color={theme.palette.blue[30]}
              hoveredColor={theme.palette.blue[50]}
              isHovered={hoverState[id]}
              onMouseEnter={() => onMouseEnter(id)}
              onMouseLeave={() => onMouseLeave(id)}
            />
          ) : null}
        </>
      )}
    </div>
  )
}

const TimeFinderCaseStatusPill = styled.div<CaseStatusNameStyleConfig>`
      border-radius: ${(props) => props.borderRadius};
      background-color: ${(props) => props.backgroundColor};
      border: ${(props) => props.border ?? 'none'}};
    `

interface TimeFinderCasePillProps {
  additionalCss?: React.CSSProperties
  color?: string
  hoveredColor?: string
  isHovered?: boolean
  onMouseEnter?: () => void
  onMouseLeave?: () => void
  pillId?: string
  pillPositionAndSize: PillPositionAndSize
  timeFinderCase?: TimeFinderCase
  tooltipBody?: React.JSX.Element
  tooltipCss?: React.CSSProperties
  tooltipMiddleware?: TooltipProps['middleware']
  tooltipPlacement?: TooltipProps['placement']
}

const TimeFinderCasePill = memo(function TimeFinderCasePill({
  pillPositionAndSize,
  additionalCss,
  tooltipCss,
  color,
  hoveredColor,
  isHovered,
  tooltipBody,
  tooltipPlacement,
  tooltipMiddleware,
  onMouseEnter,
  onMouseLeave,
}: TimeFinderCasePillProps) {
  const { left, containedWidth } = useMemo(
    () => ({
      left: pillPositionAndSize.left,
      containedWidth: (pillPositionAndSize?.widthPx ?? 2) - 2,
    }),
    [pillPositionAndSize]
  )

  return (
    <PillHolder
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      style={{
        left,
        width: containedWidth,
        height: PILL_HEIGHT,
        top: PILL_OFFSET,
        display: 'flex',
        cursor: 'pointer',
      }}
    >
      {tooltipBody ? (
        <Tooltip
          body={tooltipBody}
          middleware={tooltipMiddleware}
          maxWidth={undefined}
          isHoverable={true}
          isTouchEnabled={false}
          hoverIntent={CaseTooltipHoverInterConfiguration}
          placement={tooltipPlacement}
          additionalCss={{ ...tooltipCss }}
        >
          <TimeFinderCaseStatusPill
            borderRadius={rem(2)}
            backgroundColor={(isHovered ? hoveredColor : color) ?? 'gray'}
            css={{
              width: containedWidth,
              height: PILL_HEIGHT,
              ...additionalCss,
            }}
          />
        </Tooltip>
      ) : (
        <TimeFinderCaseStatusPill
          borderRadius={rem(2)}
          backgroundColor={(isHovered ? hoveredColor : color) ?? 'gray'}
          css={{
            width: containedWidth,
            height: PILL_HEIGHT,
            ...additionalCss,
          }}
        />
      )}
    </PillHolder>
  )
})

interface TimeFinderCaseTooltipBodyProps {
  timeFinderCase: TimeFinderCase
}

const TimeFinderCaseTooltipBody = memo(function TimeFinderCaseTooltipBody({
  timeFinderCase,
}: TimeFinderCaseTooltipBodyProps) {
  const caseObj = timeFinderCase.case
  const theme = useTheme()

  const primarySurgeon = useMemo(
    () =>
      timeFinderCase.case?.caseStaff
        .map((cs) => ({
          firstName: cs.staff.firstName,
          lastName: cs.staff.lastName,
          displayName: toStaffFullName({
            staff: cs.staff,
            displayLastNameFirst: true,
          }),
          id: cs.staff.id,
          role: cs.role ?? '',
        }))
        .filter(isPrimarySurgeon)
        .map((e) => e.displayName)
        .join('; '),
    [timeFinderCase.case]
  )

  const procedures = useMemo(
    () =>
      caseObj?.primaryCaseProcedures
        .map((e) => ({
          name: e.procedure.name,
        }))
        .filter(Boolean),
    [caseObj]
  )

  return (
    <div css={{ minWidth: rem(250), userSelect: 'text' }}>
      <FlexContainer alignItems={'center'}>
        <Caps1Bold
          css={{
            color: theme.palette.text.secondary,
          }}
        >
          {primarySurgeon}
        </Caps1Bold>
      </FlexContainer>

      <P3
        css={{
          color: theme.palette.text.tertiary,
          marginTop: 2,
          marginBottom: remSpacing.small,
        }}
      >
        {procedures
          ? procedures.map((p) => p.name).join('; ')
          : 'Scheduled Case'}
      </P3>

      <ScheduledTimingNode caseObj={caseObj} />
    </div>
  )
})

const AvailableSlotTooltipBody = memo(function AvailableSlotTooltipBody({
  startTime,
  endTime,
}: {
  startTime: DateTime
  endTime: DateTime
}) {
  const theme = useTheme()

  return (
    <div css={{ userSelect: 'text' }}>
      <P3 css={{ color: theme.palette.gray[60] }}>
        {getSlotTimeRange(startTime, endTime)}
      </P3>
    </div>
  )
})

export const ScheduledTimingNode = ({
  caseObj,
}: {
  caseObj: TimeFinderCase['case']
}) => {
  const theme = useTheme()
  const { timezone } = useTimezone()
  return (
    <FlexContainer
      gap={remSpacing.xxsmall}
      alignItems="center"
      css={{ color: theme.palette.text.tertiary }}
    >
      <P3 css={{ color: theme.palette.text.secondary }}>SCHEDULED</P3>

      <Clock size={'xs'} color={theme.palette.gray[50]} />
      <H6 color={theme.palette.gray[50]}>
        {caseObj ? (
          <>
            {DateTime.fromISO(caseObj.scheduledStartTime)
              .setZone(timezone)
              .toLocaleString(ApellaDateTimeFormats.TIME)}{' '}
            -{' '}
            {DateTime.fromISO(caseObj.scheduledEndTime)
              .setZone(timezone)
              .toLocaleString(ApellaDateTimeFormats.TIME)}
          </>
        ) : (
          EM_DASH
        )}
      </H6>
    </FlexContainer>
  )
}
