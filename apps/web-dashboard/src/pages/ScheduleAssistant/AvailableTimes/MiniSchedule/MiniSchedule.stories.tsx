import { DateTime, Duration } from 'luxon'

import { FlexContainer } from '@apella/component-library'

import { IAvailableSlot } from '../interfaces'
import { TimeFinderCase } from '../useApellaCaseData'
import { MiniDateRoomSchedule } from './MiniSchedule'

export default {
  title: 'Components/MiniSchedule',
  component: MiniDateRoomSchedule,
}

export const Showcase = (): React.JSX.Element => {
  const cases: TimeFinderCase[] = [
    {
      case: {
        caseStaff: [
          {
            role: 'Primary',
            staff: {
              firstName: 'Alex',
              id: 'd5ccbf90-5533-4d84-b80e-972f1054a63c',
              lastName: 'Nguyen',
              __typename: 'Staff',
            },

            __typename: 'CaseStaff',
          },
        ],
        id: '2cda0a94-38e5-4b89-b5ad-69b59daa5152',
        primaryCaseProcedures: [
          {
            procedure: {
              id: '6611666b-f03b-49d9-adcd-01a190e9ac8e',
              name: 'SINUS SURGERY, ENDOSCOPIC',
              __typename: 'Procedure',
            },
            __typename: 'CaseProcedure',
          },
        ],
        scheduledEndTime: '2024-08-21T15:30:00+00:00',
        scheduledStartTime: '2024-08-21T13:00:00+00:00',
        __typename: 'ScheduledCase',
      },
      endTime: '2024-08-21T15:02:56+00:00',
      id: 'case:2cda0a94-38e5-4b89-b5ad-69b59daa5152',
      roomId: 'HMH-OPC18-OR06',
      startTime: '2024-08-21T12:57:30+00:00',
      __typename: 'ApellaCase',
    },

    {
      case: {
        caseStaff: [
          {
            role: 'Primary',
            staff: {
              firstName: 'Alex',
              id: 'd5ccbf90-5533-4d84-b80e-972f1054a63c',
              lastName: 'Nguyen',
              __typename: 'Staff',
            },
            __typename: 'CaseStaff',
          },
        ],
        id: '2cda0a94-38e5-4b89-b5ad-69b59daa5152',
        primaryCaseProcedures: [
          {
            procedure: {
              id: '360a3bff-5113-437b-a363-e0c8f61c5859',
              name: 'SEPTOPLASTY, NASAL SOME REALLY REALLY REALLY, WAIT FOR IT, REALLY LONG NAME',
              __typename: 'Procedure',
            },
            __typename: 'CaseProcedure',
          },
        ],
        scheduledEndTime: '2024-08-21T15:30:00+00:00',
        scheduledStartTime: '2024-08-21T13:00:00+00:00',
        __typename: 'ScheduledCase',
      },
      endTime: '2024-08-21T18:22:28.585510+00:00',
      id: 'case:692c2ab4-ea8b-4009-b311-367568cdafbd',
      roomId: 'HMH-OPC18-OR06',
      startTime: '2024-08-21T15:37:30+00:00',
      __typename: 'ApellaCase',
    },
  ]

  const slot: IAvailableSlot = {
    blockTimeIds: [],
    date: '2024-08-21',
    maxAvailableDuration: Duration.fromObject({ minutes: 120 }),
    id: 'HMH-OPC18-OR062024-08-21T19:00:00Z2024-08-21T22:00:00Z180',
    roomId: 'HMH-OPC18-OR06',
    roomName: 'OR 06',
    siteId: 'HMH-OPC18',
    siteName: 'OPC18',
    startTime: DateTime.fromISO('2024-08-21T18:22:28.585510+00:00'),
    endTime: DateTime.fromISO('2024-08-21T21:22:28.585510+00:00'),
  }

  return (
    <>
      <FlexContainer
        css={{
          height: '100vh',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <MiniDateRoomSchedule
          loading={false}
          timeFinderCases={cases}
          id={slot.id}
          startTime={slot.startTime}
          endTime={slot.endTime}
          durationEstInMins={112}
        />
      </FlexContainer>
    </>
  )
}
