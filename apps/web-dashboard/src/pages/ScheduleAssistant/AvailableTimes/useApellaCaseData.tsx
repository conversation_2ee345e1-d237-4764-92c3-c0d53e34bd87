import { useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { useTimezone } from 'src/Contexts'

import {
  GetApellaCasesInSite,
  GetApellaCasesInSiteVariables,
} from '../__generated__'
import { GET_APELLA_CASES_IN_SITE } from '../queries'
import { SLOT_DATE_FMT } from './constants'

export type TimeFinderCase =
  GetApellaCasesInSite['apellaCases']['edges'][number]['node']
export type RoomIdToCasesMap = Map<string, TimeFinderCase[]>
export type DateToRoomIdToCaseMap = Map<string, RoomIdToCasesMap>

export const useTimeFinderCaseData = ({
  minEndTime,
  maxStartTime,
  siteIds,
  skip,
}: {
  minEndTime: DateTime
  maxStartTime: DateTime
  siteIds: string[]
  skip: boolean
}) => {
  const { timezone } = useTimezone()
  const { loading: isTimeFinderCaseDataLoading, data: casesData } = useQuery<
    GetApellaCasesInSite,
    GetApellaCasesInSiteVariables
  >(GET_APELLA_CASES_IN_SITE, {
    variables: {
      minEndTime: minEndTime.toISO(),
      maxStartTime: maxStartTime.toISO(),
      siteIds,
    },
    skip,
  })

  const timeFinderCaseData = useMemo(() => {
    const cases = (casesData?.apellaCases.edges ?? []).map(({ node }) => node)
    const dateRoomIdMap: DateToRoomIdToCaseMap = new Map()

    return cases.reduce((acc, c) => {
      const dateStr = DateTime.fromISO(c.startTime, {
        zone: timezone,
      }).toFormat(SLOT_DATE_FMT)

      if (!acc.has(dateStr)) {
        const roomIdToCasesMap: RoomIdToCasesMap = new Map()
        roomIdToCasesMap.set(c.roomId, [c])
        acc.set(dateStr, roomIdToCasesMap)
      } else {
        const roomIdToCasesMap = acc.get(dateStr)!

        if (!roomIdToCasesMap.has(c.roomId)) {
          roomIdToCasesMap.set(c.roomId, [c])
        } else {
          roomIdToCasesMap.get(c.roomId)!.push(c)
        }
      }

      return acc
    }, dateRoomIdMap)
  }, [casesData, timezone])

  return { isTimeFinderCaseDataLoading, timeFinderCaseData }
}
