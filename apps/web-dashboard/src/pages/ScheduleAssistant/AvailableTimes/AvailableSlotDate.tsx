import { useTheme } from '@emotion/react'

import { uniq } from 'lodash'
import { DateTime } from 'luxon'

import {
  ApellaDateTimeFormats,
  ButtonLink,
  FlexContainer,
  H4,
  remSpacing,
  ScheduleGantt,
  typography,
} from '@apella/component-library'
import { useCurrentUser } from 'src/modules/user/hooks'
import { LocationPath } from 'src/router/types'

import { SLOT_DATE_FMT } from './constants'
import { IAvailableSlot } from './interfaces'
import { getDayOfWeek } from './utils'

export interface AvailableSlotDateProps {
  dateString: string
  groupHeightRef?: any
  slots: IAvailableSlot[]
}

export const AvailableSlotDate = ({
  dateString,
  slots,
  groupHeightRef,
}: AvailableSlotDateProps) => {
  const theme = useTheme()
  const constructPath = new URLSearchParams({
    sites: JSON.stringify(uniq(slots.map((slot) => slot.siteId))),
    date: JSON.stringify(dateString),
  })
  const { permissions } = useCurrentUser()
  return (
    <div
      ref={groupHeightRef}
      css={{
        paddingBottom: remSpacing.medium,
      }}
    >
      <FlexContainer
        justifyContent="space-between"
        css={{
          backgroundColor: theme.palette.background.primary,
          paddingTop: remSpacing.medium,
          alignItems: 'center',
        }}
      >
        <FlexContainer gap={remSpacing.medium}>
          <H4>
            {DateTime.fromFormat(dateString, SLOT_DATE_FMT).toLocaleString(
              ApellaDateTimeFormats.DATETIME_WITH_MONTH_LONG
            )}
          </H4>

          <span
            css={{
              ...typography.p1,
              color: theme.palette.text.tertiary,
              textTransform: 'capitalize',
            }}
          >
            {getDayOfWeek(dateString)}
          </span>
        </FlexContainer>
        {permissions?.dashboardScheduleEnabled && (
          <ButtonLink
            appearance="link"
            to={`${LocationPath.Schedule}?${constructPath.toString()}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <ScheduleGantt size="sm" />
            View schedule
          </ButtonLink>
        )}
      </FlexContainer>
    </div>
  )
}
