import { useMemo } from 'react'

import { DateTime, Info } from 'luxon'

import { FlexContainer, H5, Span3, remSpacing } from '@apella/component-library'

import { CELL_BASE_STYLE, SLOT_DATE_FMT } from './constants'
import { DayCell } from './DayCell'
import { DateString, IAvailableSlot } from './interfaces'
import { getDaysWithPaddingForMonth } from './utils'

export interface CalendarViewProps {
  availableDays: string[]
  onDateClick: (date: DateTime) => void
  slotsByMonth: Record<DateString, IAvailableSlot[]>
  startDate: DateTime
}

export const CalendarView = ({
  availableDays,
  slotsByMonth,
  startDate,
  onDateClick,
}: CalendarViewProps) => {
  const month1 = startDate.startOf('month')
  const month2 = startDate.plus({ months: 1 }).startOf('month')

  return (
    <FlexContainer
      direction="column"
      css={{
        gap: remSpacing.large,
      }}
    >
      <MonthView
        key={month1.toFormat(SLOT_DATE_FMT)}
        availableDays={availableDays}
        firstDayOfMonth={month1}
        availableSlots={slotsByMonth[month1.toFormat(SLOT_DATE_FMT)]}
        showWeekdayLabels
        showMonthLabel={false}
        onDateClick={onDateClick}
      />
      <MonthView
        key={month2.toFormat(SLOT_DATE_FMT)}
        availableDays={availableDays}
        firstDayOfMonth={month2}
        availableSlots={slotsByMonth[month2.toFormat(SLOT_DATE_FMT)]}
        showWeekdayLabels={false}
        showMonthLabel
        onDateClick={onDateClick}
      />
    </FlexContainer>
  )
}

const weekdaysStartingSunday = [
  Info.weekdays('short')[6],
  ...Info.weekdays('short').slice(0, 6),
] as const

const WeekdaysLabels = () => {
  return weekdaysStartingSunday.map((day) => (
    <div key={day} css={CELL_BASE_STYLE}>
      <Span3>{day[0]}</Span3>
    </div>
  ))
}

interface MonthViewProps {
  availableDays: string[]
  availableSlots: IAvailableSlot[]
  firstDayOfMonth: DateTime
  onDateClick: (date: DateTime) => void
  showMonthLabel?: boolean
  showWeekdayLabels?: boolean
}

const MonthView = ({
  firstDayOfMonth,
  showWeekdayLabels = false,
  showMonthLabel = false,
  availableDays,
  onDateClick,
}: MonthViewProps) => {
  const days = useMemo(
    () => getDaysWithPaddingForMonth(firstDayOfMonth),
    [firstDayOfMonth]
  )

  return (
    <FlexContainer direction="column" gap={remSpacing.medium}>
      {showMonthLabel && (
        <H5 as="h4">
          {firstDayOfMonth.monthLong} {firstDayOfMonth.year}
        </H5>
      )}
      <div
        css={{
          display: 'grid',
          gridTemplateColumns: 'repeat(7, 1fr)', // 7 columns for each day of the week
          gap: 2,
        }}
      >
        {showWeekdayLabels && <WeekdaysLabels />}
        {days.map((day) =>
          day.month === firstDayOfMonth.month ? (
            <DayCell
              key={`case-duration-${day.toFormat(SLOT_DATE_FMT)}`}
              day={day}
              hasSlots={availableDays.some(
                (availDay) => availDay === day.toFormat(SLOT_DATE_FMT)
              )}
              onClick={onDateClick}
            />
          ) : (
            <DayCell key={`case-duration-${day.toFormat(SLOT_DATE_FMT)}`} />
          )
        )}
      </div>
    </FlexContainer>
  )
}
