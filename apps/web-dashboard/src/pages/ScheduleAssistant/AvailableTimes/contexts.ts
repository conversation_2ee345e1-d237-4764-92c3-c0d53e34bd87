import { createContext, useContext } from 'react'

import { DateTime } from 'luxon'

import { BlockTimeAttributes } from '../interfaces'
import { MAX_DATE } from './constants'
import { DateToRoomIdToCaseMap } from './useApellaCaseData'

export const AvailableTimesContext = createContext<{
  isLoading: boolean
  blockTimeIdMap: Map<string, BlockTimeAttributes>
  maxDate: DateTime
  selectedSiteIds?: string[]
  firstDayInScrollView?: string
  timeFinderCaseData: DateToRoomIdToCaseMap
  isTimeFinderCaseDataLoading: boolean
  procedureEstInMins: number | null
  turnoverEstInMins: number | null
}>({
  isLoading: true,
  blockTimeIdMap: new Map(),
  maxDate: MAX_DATE,
  selectedSiteIds: undefined,
  timeFinderCaseData: new Map(),
  isTimeFinderCaseDataLoading: true,
  procedureEstInMins: null,
  turnoverEstInMins: null,
})

export const useAvailableTimesContext = () => useContext(AvailableTimesContext)
