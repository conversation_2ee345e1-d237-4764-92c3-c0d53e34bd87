import React, {
  createContext,
  ForwardedRef,
  forwardRef,
  useContext,
  useLayoutEffect,
  useRef,
} from 'react'
import { GroupedVirtuoso, VirtuosoHandle } from 'react-virtuoso'

import { isUndefined } from 'lodash'
import { Duration } from 'luxon'

import { ZIndex, Duration as DurationIcon } from '@apella/component-library'
import { EmptyState } from 'src/components/EmptyState'

import { AvailableSlot } from '../AvailableSlot'
import { BlockTimeAttributes } from '../interfaces'
import { AvailableSlotDate } from './AvailableSlotDate'
import { AvailableSlotsByDateSkeleton } from './AvailableSlotsByDateSkeleton'
import { useAvailableTimesContext } from './contexts'
import { DateString, IAvailableSlot } from './interfaces'
import { RoomIdToCasesMap, TimeFinderCase } from './useApellaCaseData'

const FooterContext = createContext(0)

const VirtualScrollBufferFooter = () => {
  const footerSize = useContext(FooterContext)
  return <div css={{ paddingTop: `${footerSize}px` }} />
}

export interface AvailableSlotsByDateViewProps {
  byDayCounts: number[]
  dates: DateString[]
  setFirstDayInScrollView: (date: DateString) => void
  setShowScrollToTopButton: (show: boolean) => void
  slots: IAvailableSlot[] | undefined
  slotsByDate: Record<DateString, IAvailableSlot[]>
  virtuosoRef: React.RefObject<VirtuosoHandle | null>
}

export const AvailableSlotsByDateView = ({
  setFirstDayInScrollView,
  setShowScrollToTopButton,
  slots,
  dates,
  byDayCounts,
  slotsByDate,
  virtuosoRef,
}: AvailableSlotsByDateViewProps) => {
  const {
    isLoading,
    timeFinderCaseData: apellaCaseData,
    selectedSiteIds,
  } = useAvailableTimesContext()
  const {
    groupHeightRef,
    scrollContainerRef,
    setSlotHeight,
    slotHeight,
    footerHeight,
  } = useVirtualScrollFooterCalc({
    numLastGroupSlots: byDayCounts[byDayCounts.length - 1],
  })

  return (
    <div
      ref={scrollContainerRef}
      style={{
        display: 'flex',
        flexDirection: 'column',
        zIndex: ZIndex.DEFAULT,
        position: 'relative',
        width: '100%',
      }}
    >
      <FooterContext.Provider value={footerHeight}>
        {isLoading || isUndefined(slots) ? (
          <>
            <AvailableSlotsByDateSkeleton />
            <AvailableSlotsByDateSkeleton />
          </>
        ) : selectedSiteIds?.length ? (
          slots.length > 0 ? (
            <GroupedVirtuoso
              style={{ height: '100vh', width: '100%' }}
              components={{
                Footer: VirtualScrollBufferFooter,
              }}
              ref={virtuosoRef}
              rangeChanged={({ startIndex }) => {
                setShowScrollToTopButton(startIndex > 10)
                setFirstDayInScrollView(slots[startIndex]?.date)
              }}
              groupCounts={byDayCounts}
              groupContent={(index) => {
                const dateString = dates[index]
                const slots = slotsByDate[dateString]
                return (
                  <AvailableSlotDate
                    dateString={dateString}
                    slots={slots}
                    groupHeightRef={groupHeightRef}
                  />
                )
              }}
              itemContent={(index) => {
                const slot = slots[index]
                const casesByDate = apellaCaseData?.get(slot.date) ?? new Map()
                return (
                  <AvailableTimesAvailableSlot
                    ref={(el) => {
                      if (el && !slotHeight) {
                        setSlotHeight(el.clientHeight)
                      }
                    }}
                    key={slot.id}
                    slot={slot}
                    timeFinderCases={casesByDate?.get(slot.roomId) ?? []}
                  />
                )
              }}
            />
          ) : (
            <EmptyState
              message="No available times"
              subtext="Try adjusting your filters to find available times"
              Icon={DurationIcon}
            />
          )
        ) : (
          <EmptyState
            message="Select a site"
            subtext="to view available times to schedule"
            Icon={DurationIcon}
          />
        )}
      </FooterContext.Provider>
    </div>
  )
}

const useVirtualScrollFooterCalc = ({
  numLastGroupSlots,
}: {
  numLastGroupSlots: number
}) => {
  const groupHeightRef = useRef<HTMLDivElement>(null)
  const [slotHeight, setSlotHeight] = React.useState(0)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [footerHeight, setFooterHeight] = React.useState(0)
  const scrollContainerHeight = scrollContainerRef.current?.clientHeight || 0
  const groupHeight = groupHeightRef.current?.clientHeight || 0

  useLayoutEffect(() => {
    setFooterHeight(
      scrollContainerHeight - numLastGroupSlots * slotHeight - groupHeight
    )
  }, [numLastGroupSlots, slotHeight, scrollContainerHeight, groupHeight])

  return {
    groupHeightRef,
    scrollContainerRef,
    slotHeight,
    setSlotHeight,
    footerHeight,
  }
}

export interface AvailableSlotsByDateProps {
  casesByDate?: RoomIdToCasesMap
  dateString: DateString
  slotsByDate: IAvailableSlot[]
}

export interface AvailableSlotProps {
  slot: IAvailableSlot
  timeFinderCases: TimeFinderCase[]
}

const AvailableTimesAvailableSlot = forwardRef(
  function AvailableTimesAvailableSlot(
    { slot, timeFinderCases }: AvailableSlotProps,
    ref: ForwardedRef<HTMLDivElement>
  ) {
    const {
      isTimeFinderCaseDataLoading,
      blockTimeIdMap,
      procedureEstInMins,
      turnoverEstInMins,
    } = useAvailableTimesContext()

    const blockTimes = slot.blockTimeIds.reduce<BlockTimeAttributes[]>(
      (acc, blockTimeId) => {
        const blockTime = blockTimeIdMap.get(blockTimeId)

        if (blockTime) {
          acc.push(blockTime)
        }
        return acc
      },
      []
    )

    return (
      <AvailableSlot
        ref={ref}
        timeFinderCases={timeFinderCases}
        isTimeFinderCaseDataLoading={isTimeFinderCaseDataLoading}
        blockTimes={blockTimes}
        slot={slot}
        maxAvailableDuration={slot.maxAvailableDuration}
        durationUsedInSlot={Duration.fromObject({
          minutes: (procedureEstInMins ?? 0) + (turnoverEstInMins ?? 0),
        })}
        showTooltip
      />
    )
  }
)
