import { useMemo } from 'react'

import { useQuery } from '@apollo/client'

import { GetBlockTimes, GetBlockTimesVariables } from '../__generated__'
import { UNBLOCKED_BLOCK, THE_UNBLOCKED_BLOCK_ID } from '../constants'
import { BlockNode, BlockTimeAttributes } from '../interfaces'
import { GET_BLOCK_TIMES } from '../queries'
import { CASE_DURATION_PAGE_POLL_INTERVAL } from './constants'
import { IAvailableSlot } from './interfaces'

export const useBlockData = ({ slots }: { slots: IAvailableSlot[] }) => {
  const blockTimeIds = useMemo(() => {
    return slots.flatMap((slot: IAvailableSlot) => slot.blockTimeIds)
  }, [slots])

  const { data: blockTimeData, loading } = useQuery<
    GetBlockTimes,
    GetBlockTimesVariables
  >(GET_BLOCK_TIMES, {
    skip: blockTimeIds.length === 0,
    variables: { ids: blockTimeIds },
    pollInterval: CASE_DURATION_PAGE_POLL_INTERVAL,
  })

  const [blockTimeIdMap, blocks] = useMemo(() => {
    const blocksMap = new Map<string, BlockNode>()
    const blockTimeIdMap = (blockTimeData?.blockTimesBulk ?? []).reduce(
      (acc, blockTime) => {
        acc.set(blockTime.id, blockTime)
        blocksMap.set(blockTime.block.id, { node: blockTime.block })
        return acc
      },
      new Map<string, BlockTimeAttributes>()
    )

    // We want to return the unblocked block first, so either we find it and remove it from the map to use later or we use the FCFS block
    const unblockedBlock =
      blocksMap.get(THE_UNBLOCKED_BLOCK_ID) ?? UNBLOCKED_BLOCK
    if (blocksMap.has(THE_UNBLOCKED_BLOCK_ID)) {
      blocksMap.delete(THE_UNBLOCKED_BLOCK_ID)
    }

    // Sort the blocks by name, putting the unblocked block first
    const blocks = [
      unblockedBlock,
      ...Array.from(blocksMap.values()).sort((a, b) =>
        a.node.name.localeCompare(b.node.name)
      ),
    ]

    return [blockTimeIdMap, blocks]
  }, [blockTimeData])

  return {
    blockTimeIdMap,
    blocks,
    loading,
  }
}
