import { useMemo, useState, useEffect, useRef, Suspense } from 'react'
import Skeleton from 'react-loading-skeleton'
import { Await, useAsyncValue, useRouteLoaderData } from 'react-router'
import { VirtuosoHandle } from 'react-virtuoso'

import { useTheme } from '@emotion/react'

import { difference, groupBy, isUndefined, uniq } from 'lodash'
import { DateTime, Duration } from 'luxon'

import {
  ArrowUp,
  Button,
  ExpandLess,
  ExpandMore,
  FlexContainer,
  mediaQueries,
  remSpacing,
  ZIndex,
} from '@apella/component-library'
import { DayOfWeekFilter } from 'src/components/DayOfWeekFilter'
import { MultiFilterWithCount } from 'src/components/Filters/FilterWithCount'
import { FloatingActionIconButton } from 'src/components/FloatingAction'
import { ResetFilters } from 'src/components/ResetFilters'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { useTimezone } from 'src/Contexts'
import { DayOfWeek, daysOfWeekInOrder } from 'src/pages/Insights/types'
import { LocationPath } from 'src/router/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import orgLocalStorage from 'src/utils/orgLocalStorage'

import { FilterLabel } from '../../../components/FilterLabel'
import { BLOCK_FILTER_LABEL, THE_UNBLOCKED_BLOCK_ID } from '../constants'
import { loader as estimateLoader } from '../EstimateLayout'
import { useScheduleAssistantSurgeonProcedureFilters } from '../useScheduleAssistantSurgeonProcedureFilters'
import { AvailableSlotsByDateSkeleton } from './AvailableSlotsByDateSkeleton'
import { AvailableSlotsByDateView } from './AvailableSlotsByDateView'
import { CalendarView } from './CalendarView'
import {
  FILTER_PADDING,
  MAX_DATE,
  MONTH_VIEWS_CONTAINER_ID,
  SLOT_DATE_FMT,
} from './constants'
import { AvailableTimesContext } from './contexts'
import { IRoomInfo } from './interfaces'
import { MonthPicker } from './MonthPicker'
import { SurgeonWithBlockNoAvailableTimes } from './SurgeonWithBlockNoAvailableTimes'
import { useAvailableSlotsData } from './useAvailableSlotsData'
import {
  useAvailableTimesFilters,
  SITES_FILTER,
  BLOCKS_FILTER,
  START_DATE_FILTER,
  DAY_OF_WEEK_FILTER,
  ROOMS_FILTER,
} from './useAvailableTimesFilters'

export interface AvailableTimesProps {
  blockIds: string[]
  daysOfWeek: DayOfWeek[]
  onChangeBlocks: (blockIds: string[] | undefined) => void
  onChangeMonth: (dateTime: DateTime) => void
  onChangeNextMonth: () => void
  onChangePrevMonth: () => void
  onChangeSitesAndRooms: (siteIds?: string[], roomIds?: string[]) => void
  onDayOfWeekChange: (daysOfWeek: DayOfWeek[]) => void
  primarySiteId?: string
  primarySiteTimezone?: string
  procedure: string
  procedureEstInMins?: number
  resetFilters: () => void
  roomIds: string[]
  roomInfoMap: Map<string, IRoomInfo>
  showResetFiltersButton: boolean
  siteIds: string[]
  startDate: string
  surgeon: string
  surgeonBlockIds?: string[]
  surgeonId: string
  turnoverEstInMins?: number
}

const getDateRange = (startDate: DateTime, zone: string) => {
  const tomorrow = DateTime.now().plus({ day: 1 }).setZone(zone)
  const start = DateTime.max(
    startDate.hasSame(DateTime.now().setZone(zone), 'month')
      ? startDate
      : startDate.startOf('month'),
    tomorrow
  )
  return [
    start.toFormat(SLOT_DATE_FMT),
    start.plus({ month: 1 }).endOf('month').toFormat(SLOT_DATE_FMT),
  ]
}

export const loader = () => {
  orgLocalStorage.setItem(
    'scheduleAssistantLastTab',
    LocationPath.AvailableTimes
  )
  return null
}

export const AvailableTimesRoute = () => {
  const { predictionPromise } = useRouteLoaderData(
    'caseDurationEstimate'
  ) as Awaited<ReturnType<typeof estimateLoader>>

  return (
    <Suspense fallback={<AvailableTimesLoadingFallback />}>
      <Await resolve={predictionPromise}>
        <AvailableTimes />
      </Await>
    </Suspense>
  )
}

const AvailableTimesLoadingFallback = () => {
  const { selectedStartDate } = useAvailableTimesFilters()
  const { timezone } = useTimezone()
  const startDate = DateTime.fromISO(selectedStartDate, { zone: timezone })

  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.gutter,
      }}
    >
      <div
        css={{
          paddingRight: remSpacing.xlarge,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
        }}
      >
        <Skeleton
          css={{
            marginBottom: remSpacing.medium,
          }}
          height={40}
          width={100}
        />
        <CalendarView
          availableDays={[]}
          slotsByMonth={{}}
          startDate={startDate}
          onDateClick={() => {}}
        />
      </div>
      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
          alignContent: 'stretch',
          flex: '1 0 0',
        }}
      >
        <Skeleton height={40} width={500} />
        <div
          css={{
            marginTop: remSpacing.medium,
          }}
        >
          <AvailableSlotsByDateSkeleton />
          <AvailableSlotsByDateSkeleton />
        </div>
      </div>
    </div>
  )
}

const AvailableTimes = () => {
  const {
    surgeonName: selectedSurgeon,
    procedureName: selectedProcedure,
    procedureDuration: procedureEstInMins,
    turnoverDuration: turnoverEstInMins,
  } = useAsyncValue() as Awaited<
    Awaited<ReturnType<typeof estimateLoader>>['predictionPromise']
  >

  const { selectedSurgeonId } = useScheduleAssistantSurgeonProcedureFilters()
  const {
    selectedDaysOfWeek,
    selectedBlockIds,
    selectedRoomIds,
    selectedSiteIds,
    selectedStartDate,
    onSearch,
    resetable,
    resetParamsToDefaults,
    setDefaults,
    clearParams,
  } = useAvailableTimesFilters()

  const { timezone } = useTimezone()
  const startDate = DateTime.fromISO(selectedStartDate, { zone: timezone })
  const timeRange = useMemo(
    () => getDateRange(startDate, timezone),
    [startDate, timezone]
  )
  const eventLogger = useAnalyticsEventLogger()
  const virtuosoRef = useRef<VirtuosoHandle | null>(null)
  const [firstDayInScrollView, setFirstDayInScrollView] = useState<string>()
  const [showScrollToTopButton, setShowScrollToTopButton] = useState(false)
  const minAvailableDuration = useMemo(
    () =>
      Duration.fromObject({
        minutes: (procedureEstInMins ?? 0) + (turnoverEstInMins ?? 0),
      }),
    [procedureEstInMins, turnoverEstInMins]
  )

  const {
    surgeonFilteredFlatSlots,
    filteredFlatSlots,
    timeFinderCaseData,
    primarySiteId,
    primarySiteTimezone,
    surgeonBlockIds,
    blocks,
    blockTimeIdMap,
    isTimeFinderCaseDataLoading,
    isBlockDataLoading,
    availabilityDataLoading,
    rooms,
    sites,
  } = useAvailableSlotsData({
    selectedSurgeonId,
    selectedDaysOfWeek,
    selectedRoomIds,
    selectedSiteIds,
    selectedBlockIds,
    minAvailableDuration,
    timeRange,
    timezone,
  })

  const {
    dateGroups,
    slotsByDate,
    slotsByMonth,
    byDayCounts,
    firstSlotInGroupIndexes,
  } = useMemo(() => {
    const dateGroups = uniq(filteredFlatSlots?.map((slot) => slot.date))
    const slotsByDate = groupBy(filteredFlatSlots, 'date')
    const slotsByMonth = groupBy(
      filteredFlatSlots,
      (slot) => DateTime.fromFormat(slot.date, SLOT_DATE_FMT).month
    )
    const firstSlotInGroupIndexes = dateGroups.map(
      (date) => filteredFlatSlots?.findIndex((slot) => slot.date === date) ?? 0
    )
    const byDayCounts = dateGroups.map((date) => slotsByDate[date].length)

    return {
      dateGroups,
      slotsByMonth,
      slotsByDate,
      byDayCounts,
      firstSlotInGroupIndexes,
    }
  }, [filteredFlatSlots])
  const readyToCheckParams = !isBlockDataLoading && !availabilityDataLoading

  /**
   * This use effect is needed because we cannot set defaults for params until all data is loaded
   * In a future where data loading does not happen inside of the render lifecycle, we can replace this
   * with async/await + "replace" from react-router
   */
  const allBlockIds = blocks.map((b) => b.node.id)
  const hasSetDefaultSiteParam = useRef(false)
  const hasSetDefaultBlockParam = useRef(false)
  const isLoading = availabilityDataLoading

  const readyToSetSiteDefaultParams =
    !hasSetDefaultSiteParam.current &&
    readyToCheckParams &&
    !selectedSiteIds?.length

  const readyToSetBlockDefaultParam =
    !hasSetDefaultBlockParam.current &&
    !isUndefined(surgeonFilteredFlatSlots) &&
    readyToCheckParams

  useEffect(() => {
    if (readyToSetSiteDefaultParams) {
      setDefaults((defaults) => ({
        ...defaults,
        [SITES_FILTER]: primarySiteId ? [primarySiteId] : [],
      }))
      hasSetDefaultSiteParam.current = true
      resetParamsToDefaults()
    }
  }, [
    primarySiteId,
    setDefaults,
    resetParamsToDefaults,
    readyToSetSiteDefaultParams,
  ])

  useEffect(() => {
    if (readyToSetBlockDefaultParam) {
      setDefaults((defaults) => ({
        ...defaults,
        // If the surgeon's blocks are outside the currently selected time window
        // then those blocks will be ignored.
        [BLOCKS_FILTER]:
          surgeonFilteredFlatSlots?.length > 0 &&
          surgeonBlockIds?.length &&
          surgeonBlockIds?.length > 0
            ? surgeonBlockIds
            : [THE_UNBLOCKED_BLOCK_ID],
      }))
      resetParamsToDefaults()
      hasSetDefaultBlockParam.current = true
    }
  }, [
    surgeonBlockIds,
    setDefaults,
    resetParamsToDefaults,
    allBlockIds,
    surgeonFilteredFlatSlots,
    readyToSetBlockDefaultParam,
  ])

  // if the surgeon or procedure duration changes we need to reset the defaults
  useEffect(() => {
    if (selectedSurgeonId && minAvailableDuration) {
      hasSetDefaultBlockParam.current = false
      hasSetDefaultSiteParam.current = false
    }
  }, [selectedSurgeonId, minAvailableDuration, selectedProcedure])

  /**
   * This series of useEffects are needed because we cannot validate params until all data is loaded
   * In a future where data loading does not happen inside of the render lifecycle, we can replace these
   * with async/await + replace from react-router
   */
  // Clear params if selected days of week are not valid
  useEffect(() => {
    if (
      selectedDaysOfWeek.length &&
      difference(selectedDaysOfWeek, daysOfWeekInOrder).length
    ) {
      clearParams()
    }
  }, [selectedDaysOfWeek, clearParams])

  // Clear params if selected site is not valid
  useEffect(() => {
    if (
      selectedSiteIds?.length &&
      readyToCheckParams &&
      difference(
        selectedSiteIds,
        sites.map((s) => s.node.id)
      ).length
    ) {
      clearParams()
    }
  }, [selectedSiteIds, clearParams, sites, readyToCheckParams])

  // Clear params if selected room is not valid
  useEffect(() => {
    if (
      selectedRoomIds?.length &&
      readyToCheckParams &&
      difference(
        selectedRoomIds,
        rooms.map((r) => r.node.id)
      ).length
    ) {
      clearParams()
    }
  }, [selectedRoomIds, clearParams, rooms, readyToCheckParams])

  const analyticState = useMemo(
    () => ({
      primarySurgeonName: selectedSurgeon,
      primaryProcedureName: selectedProcedure,
      primarySiteId,
      primarySiteTimezone,
      procedureEstimateMinutes: procedureEstInMins,
      turnoverEstimateMinutes: turnoverEstInMins,
      startDate: timeRange[0],
      endDate: timeRange[1],
    }),
    [
      selectedSurgeon,
      selectedProcedure,
      primarySiteId,
      primarySiteTimezone,
      procedureEstInMins,
      turnoverEstInMins,
      timeRange,
    ]
  )

  const dateSelectHandler = (dateTime: DateTime) => {
    const index =
      firstSlotInGroupIndexes[
        dateGroups.indexOf(dateTime.toFormat(SLOT_DATE_FMT))
      ]
    virtuosoRef.current?.scrollToIndex({ index, behavior: 'smooth' })
    eventLogger(EVENTS.TIME_FINDER_SELECT_DATE, {
      ...analyticState,
      selectedDate: dateTime.toFormat(SLOT_DATE_FMT),
    })
  }

  const monthPickerHandler = () => {
    eventLogger(EVENTS.TIME_FINDER_OPEN_MONTH_PICKER, analyticState)
  }

  // If date groups change the slots changed. Scroll to the top and ensure the first date is set as "selected"
  useEffect(() => {
    setFirstDayInScrollView(dateGroups[0])
    virtuosoRef.current?.scrollTo({ top: 0 })
  }, [dateGroups])

  const [showNoSurgeonAvailableTimes, setShowNoSurgeonAvailableTimes] =
    useState<boolean>(true)
  const onClose = () => {
    setShowNoSurgeonAvailableTimes(false)
  }

  const showUnblockedTimeInfoPanel =
    (surgeonBlockIds || []).length > 0 &&
    surgeonFilteredFlatSlots?.length === 0 &&
    selectedBlockIds?.length === 1 &&
    selectedBlockIds.includes(THE_UNBLOCKED_BLOCK_ID) &&
    showNoSurgeonAvailableTimes

  return (
    <AvailableTimesContext.Provider
      value={{
        isLoading,
        blockTimeIdMap,
        maxDate: MAX_DATE,
        selectedSiteIds,
        firstDayInScrollView,
        isTimeFinderCaseDataLoading,
        timeFinderCaseData,
        procedureEstInMins,
        turnoverEstInMins,
      }}
    >
      <div
        css={{
          display: 'flex',
          gap: remSpacing.gutter,
        }}
      >
        <div
          css={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            paddingRight: remSpacing.xlarge,
          }}
        >
          <MonthPicker
            firstDayOfMonth={startDate.startOf('month')}
            setMonth={(dateTime) => {
              eventLogger(EVENTS.TIME_FINDER_SELECT_MONTH, {
                ...analyticState,
                selectedMonth: dateTime.toFormat(SLOT_DATE_FMT),
              })
              onSearch({ [START_DATE_FILTER]: dateTime.toISODate() })
            }}
            onClick={monthPickerHandler}
          />
          <FlexContainer
            css={{
              position: 'relative',
              zIndex: ZIndex.DEFAULT,
              backgroundColor: 'white',
              height: 'fit-content',
              width: 'fit-content',
              flexDirection: 'column',
              alignItems: 'center',
              textAlign: 'center',
            }}
            id={MONTH_VIEWS_CONTAINER_ID}
            gap={remSpacing.medium}
          >
            <CalendarView
              availableDays={dateGroups}
              slotsByMonth={slotsByMonth}
              startDate={startDate}
              onDateClick={dateSelectHandler}
            />
            <div css={{ gap: remSpacing.medium }}>
              <Button
                color="alternate"
                buttonType="icon"
                disabled={startDate.hasSame(
                  DateTime.now().setZone(timezone),
                  'month'
                )}
                onClick={() => {
                  eventLogger(EVENTS.TIME_FINDER_PREV_MONTH, analyticState)
                  onSearch({
                    [START_DATE_FILTER]: DateTime.fromISO(selectedStartDate)
                      .minus({ month: 1 })
                      .toISODate(),
                  })
                }}
              >
                <ExpandLess />
              </Button>
              <Button
                color="alternate"
                buttonType="icon"
                disabled={startDate >= MAX_DATE}
                onClick={() => {
                  eventLogger(EVENTS.TIME_FINDER_NEXT_MONTH, analyticState)
                  onSearch({
                    [START_DATE_FILTER]: DateTime.fromISO(selectedStartDate)
                      .plus({ month: 1 })
                      .toISODate(),
                  })
                }}
              >
                <ExpandMore />
              </Button>
            </div>
          </FlexContainer>
        </div>
        <div
          css={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            alignContent: 'stretch',
            flex: '1 0 0',
          }}
        >
          <FlexContainer
            css={{
              textAlign: 'initial',
              zIndex: ZIndex.ABOVE,
              backgroundColor: 'white',
              position: 'relative',
              width: '100%',
              [mediaQueries.lg]: {
                justifyContent: 'start',
              },
              paddingBottom: `${FILTER_PADDING}px`,
            }}
            wrap="wrap"
            gap={remSpacing.gutter}
          >
            {isLoading || isUndefined(filteredFlatSlots) ? (
              <Skeleton height={40} width={500} />
            ) : (
              <>
                <FilterLabel label="Day of week">
                  <DayOfWeekFilter
                    selected={selectedDaysOfWeek}
                    onChange={(daysOfWeek) => {
                      eventLogger(EVENTS.TIME_FINDER_FILTER_DOWS, {
                        surgeon: selectedSurgeon,
                        procedure: selectedProcedure,
                        dayOfWeek: daysOfWeek?.join(','),
                      })
                      onSearch({ [DAY_OF_WEEK_FILTER]: daysOfWeek })
                    }}
                  />
                </FilterLabel>

                <SitesRoomsFilter
                  sitesLabel="Select a site"
                  sites={sites}
                  selectedRoomIds={selectedRoomIds}
                  selectedSiteIds={selectedSiteIds}
                  rooms={rooms}
                  onChangeSitesAndRooms={(siteIds, roomIds) => {
                    eventLogger(EVENTS.TIME_FINDER_FILTER_SITES, {
                      surgeon: selectedSurgeon,
                      procedure: selectedProcedure,
                      sites: siteIds?.join(','),
                    })
                    eventLogger(EVENTS.TIME_FINDER_FILTER_ROOMS, {
                      surgeon: selectedSurgeon,
                      procedure: selectedProcedure,
                      rooms: roomIds?.join(','),
                    })
                    onSearch({
                      [SITES_FILTER]: siteIds ?? [],
                      [ROOMS_FILTER]: roomIds ?? [],
                    })
                  }}
                  bulkSelectSites
                  bulkSelectRooms
                  multipleSites
                  siteSelectProps={{
                    selectedOnTop: true,
                  }}
                  roomSelectProps={{
                    selectedOnTop: true,
                  }}
                  showLabels
                />

                <FilterLabel label="Block">
                  <MultiFilterWithCount
                    items={blocks}
                    selectedIds={selectedBlockIds}
                    onChange={(blockIds) => {
                      eventLogger(EVENTS.TIME_FINDER_FILTER_BLOCKS, {
                        surgeon: selectedSurgeon,
                        procedure: selectedProcedure,
                        blocks: blockIds?.join(','),
                      })
                      onSearch({ [BLOCKS_FILTER]: blockIds ?? [] })
                    }}
                    label={BLOCK_FILTER_LABEL}
                    bulkSelect={true}
                    selectedOnTop
                  />
                </FilterLabel>

                {resetable && (
                  <ResetFilters
                    css={{ alignSelf: 'flex-end' }}
                    resetActions={() => {
                      eventLogger(EVENTS.TIME_FINDER_FILTERS_RESET)
                      resetParamsToDefaults()
                    }}
                  />
                )}
              </>
            )}
          </FlexContainer>
          <div
            css={{
              display: 'flex',
              width: '100%',
              flexWrap: 'wrap',
              alignSelf: 'stretch',
              alignItems: 'flex-start',
            }}
          >
            {showUnblockedTimeInfoPanel && selectedSurgeon && (
              <SurgeonWithBlockNoAvailableTimes
                surgeon={selectedSurgeon}
                onClose={onClose}
                isLoading={isLoading}
              />
            )}
          </div>
          <AvailableSlotsByDateView
            setShowScrollToTopButton={setShowScrollToTopButton}
            setFirstDayInScrollView={setFirstDayInScrollView}
            virtuosoRef={virtuosoRef}
            slots={filteredFlatSlots}
            dates={dateGroups}
            byDayCounts={byDayCounts}
            slotsByDate={slotsByDate}
          />
        </div>
        <ScrollToTopButton
          onClick={() => {
            eventLogger(EVENTS.TIME_FINDER_SCROLL_TO_TOP, analyticState)
            virtuosoRef.current?.scrollToIndex({ index: 0, behavior: 'smooth' })
          }}
          isVisible={showScrollToTopButton}
        />
      </div>
    </AvailableTimesContext.Provider>
  )
}

const ScrollToTopButton = ({
  onClick,
  isVisible,
}: {
  onClick: () => void
  isVisible: boolean
}) => {
  const theme = useTheme()

  return (
    <>
      {isVisible && (
        <div
          css={{
            zIndex: ZIndex.ABOVE,
            position: 'fixed',
            bottom: remSpacing.large,
            right: remSpacing.xlarge,
          }}
        >
          <FloatingActionIconButton
            tooltipContent="Scroll to top"
            Icon={ArrowUp}
            onClick={onClick}
            color={theme.palette.blue[50]}
          />
        </div>
      )}
    </>
  )
}
