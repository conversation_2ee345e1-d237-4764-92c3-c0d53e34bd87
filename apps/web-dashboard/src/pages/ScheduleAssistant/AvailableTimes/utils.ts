import { sortBy, uniq } from 'lodash'
import { DateTime, Duration } from 'luxon'

import { ApellaDateTimeFormats } from '@apella/component-library'
import { DayOfWeek } from 'src/pages/Insights/types'

import { SLOT_DATE_FMT } from './constants'
import { IAvailableSlot, IRoomInfo } from './interfaces'

export const createSlotId = (
  roomId: string,
  startTime: string,
  endTime: string,
  maxAvailableDuration: string
) => roomId.concat(startTime).concat(endTime).concat(maxAvailableDuration)

export const mapGroupBy = <T, K extends string | number>(
  array: T[],
  iteratee: (value: T) => K
): Map<K, T[]> =>
  array.reduce((acc, value) => {
    const key = iteratee(value)
    const group = acc.get(key) ?? []
    group.push(value)
    acc.set(key, group)
    return acc
  }, new Map<K, T[]>())

export const slotMapper =
  (timezone: string, roomInfoMap: Map<string, IRoomInfo>) =>
  (slot: {
    __typename: 'AvailableTimeSlot'
    startTime: string
    endTime: string
    roomId: string
    maxAvailableDuration: string
    blockTimeIds: Array<string>
  }): IAvailableSlot => {
    const startTime = DateTime.fromISO(slot.startTime, {
      zone: timezone,
    })
    const endTime = DateTime.fromISO(slot.endTime, {
      zone: timezone,
    })
    const roomInfo = roomInfoMap.get(slot.roomId)

    return {
      id: createSlotId(
        slot.roomId,
        slot.startTime,
        slot.endTime,
        slot.maxAvailableDuration
      ),
      date: startTime.toFormat(SLOT_DATE_FMT),
      siteId: roomInfo?.siteId ?? '',
      siteName: roomInfo?.siteName ?? '',
      roomId: slot.roomId,
      roomName: roomInfo?.roomName ?? '',
      roomTags: roomInfo?.roomTags ?? [],
      startTime,
      endTime,
      maxAvailableDuration: Duration.fromISO(slot.maxAvailableDuration),
      blockTimeIds: uniq(slot.blockTimeIds),
    }
  }

export const getAvailableSlotsFlat = ({
  availableTimeSlots,
  minAvailableDuration,
  timezone,
  roomInfoMap,
  sortByConfig: sortByIteratees = ['startTime', 'roomName', 'duration'],
}: {
  availableTimeSlots: {
    __typename: 'AvailableTimeSlot'
    startTime: string
    endTime: string
    roomId: string
    maxAvailableDuration: string
    blockTimeIds: Array<string>
  }[]
  minAvailableDuration: Duration
  timezone: string
  roomInfoMap: Map<string, IRoomInfo>
  sortByConfig?: string[]
}): IAvailableSlot[] => {
  const slots: IAvailableSlot[] = sortBy(
    availableTimeSlots
      ?.filter(
        (slot) =>
          Duration.fromISO(slot.maxAvailableDuration) >= minAvailableDuration
      )
      .map(slotMapper(timezone, roomInfoMap)) ?? [],
    sortByIteratees
  )
  return slots
}

export const getDaysWithPaddingForMonth = (firstDayOfMonth: DateTime) => {
  const lastDayOfMonth = firstDayOfMonth.endOf('month')

  const days = []

  // Adjusted for Sunday as the start of the week
  const startPadding =
    firstDayOfMonth.weekday === 7 ? 0 : firstDayOfMonth.weekday

  const startDate = firstDayOfMonth.minus({ days: startPadding })

  for (let day = startDate; day <= lastDayOfMonth; day = day.plus({ day: 1 })) {
    days.push(day)
  }

  return days
}

export const getDayOfWeek = (date: string) =>
  DateTime.fromFormat(
    date,
    SLOT_DATE_FMT
  ).weekdayLong.toLocaleLowerCase() as DayOfWeek

export const getSlotTimeRange = (startTime: DateTime, endTime: DateTime) => {
  return `${startTime.toLocaleString(ApellaDateTimeFormats.TIME)} - ${endTime.toLocaleString(ApellaDateTimeFormats.TIME)}`
}

export const getDateTimeFromSlotTime = (slotTime: string, date: DateTime) => {
  const time = DateTime.fromFormat(slotTime, 'HH:mm')
  return DateTime.fromObject(
    {
      day: date.day,
      month: date.month,
      year: date.year,
      hour: time.hour,
      minute: time.minute,
    },
    { zone: date.zone }
  )
}
