import { DateTime } from 'luxon'
import { rem } from 'polished'

import { pxSpacing, remSpacing } from '@apella/component-library'

export const MONTH_VIEWS_CONTAINER_ID = 'month-views-container'
export const DATE_SLOT_PREFIX = 'available-slot-for'
export const SLOT_DATE_FMT = 'yyyy-MM-dd'
export const DAY_CELL_DIM_IN_PX = 40

export const CELL_BASE_STYLE = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  borderRadius: remSpacing.xsmall,
  width: rem(DAY_CELL_DIM_IN_PX),
  height: rem(DAY_CELL_DIM_IN_PX),
}

export const MAX_DATE = DateTime.now().plus({ months: 6 }).startOf('month')

export const CASE_DURATION_PAGE_POLL_INTERVAL = 60 * 60 * 1000

export const FILTER_PADDING = pxSpacing.medium
export const AVA<PERSON><PERSON><PERSON>_SLOTS_TOP_PADDING = pxSpacing.medium
