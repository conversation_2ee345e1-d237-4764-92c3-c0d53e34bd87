import Skeleton from 'react-loading-skeleton'

import { rem } from 'polished'

import { FlexContainer, FlexItem, remSpacing } from '@apella/component-library'
import { MetricTile } from 'src/components/MetricTile'

import {
  MINI_SCHEDULE_HEIGHT,
  MINI_SCHEDULE_WIDTH,
} from './MiniSchedule/MiniSchedule'

export const AvailableSlotsByDateSkeleton = ({
  showCheckBoxSkeleton = false,
}: {
  showCheckBoxSkeleton?: boolean
}) => {
  return (
    <FlexContainer
      direction="column"
      css={{
        width: '100%',
        gap: remSpacing.small,
        marginBottom: remSpacing.xlarge,
        marginTop: remSpacing.medium,
      }}
    >
      <FlexContainer
        justifyContent="space-between"
        css={{
          width: '100%',
          marginBottom: remSpacing.medium,
        }}
      >
        <FlexItem css={{ width: rem(300) }}>
          <Skeleton css={{ height: rem(20) }} />
        </FlexItem>

        <FlexItem css={{ width: rem(175) }}>
          <Skeleton />
        </FlexItem>
      </FlexContainer>
      <AvailableSlotSkeleton showCheckBoxSkeleton={showCheckBoxSkeleton} />
      <AvailableSlotSkeleton showCheckBoxSkeleton={showCheckBoxSkeleton} />
      <AvailableSlotSkeleton showCheckBoxSkeleton={showCheckBoxSkeleton} />
    </FlexContainer>
  )
}
const OneLineWrappedSkelly = ({
  width,
  height = MINI_SCHEDULE_HEIGHT,
  marginTop = remSpacing.small,
}: {
  width: number | string
  height?: number | string
  marginTop?: number | string
}) => (
  <div
    css={{
      width,
      marginTop,
    }}
  >
    <Skeleton height={height} />
  </div>
)

const TwoLineSkelly = () => (
  <>
    <Skeleton />
    <Skeleton />
  </>
)

const AvailableSlotSkeleton = ({
  showCheckBoxSkeleton = false,
}: {
  showCheckBoxSkeleton?: boolean
}) => (
  <MetricTile>
    <FlexContainer
      gap={remSpacing.gutter}
      justifyContent="space-between"
      css={{
        padding: `${remSpacing.medium} ${remSpacing.gutter}`,
        height: 100,
        alignItems: 'center',
      }}
    >
      <FlexContainer gap={remSpacing.medium}>
        {showCheckBoxSkeleton && (
          <OneLineWrappedSkelly width={rem(20)} height={rem(20)} />
        )}
        <FlexContainer
          css={{
            flexDirection: 'column',
            gap: remSpacing.small,
            width: remSpacing.xxxlarge,
          }}
        >
          <TwoLineSkelly />
        </FlexContainer>

        <FlexContainer
          css={{
            flexDirection: 'column',
            gap: remSpacing.small,
            width: rem(150),
          }}
        >
          <TwoLineSkelly />
        </FlexContainer>

        <OneLineWrappedSkelly width={rem(125)} />
      </FlexContainer>

      <Skeleton height={MINI_SCHEDULE_HEIGHT} width={MINI_SCHEDULE_WIDTH} />
    </FlexContainer>
  </MetricTile>
)
