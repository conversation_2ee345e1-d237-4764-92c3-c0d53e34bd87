import { useState } from 'react'

import { DateTime } from 'luxon'

import { useParsedSearchParams } from '@apella/hooks'
import {
  DEFAULT_DAYS_OF_WEEK,
  VALID_DAYS_OF_WEEK,
} from 'src/components/DayOfWeekFilter'
import orgLocalStorage from 'src/utils/orgLocalStorage'

import { DayOfWeek } from '../../Insights/types'
import { PAGE_FILTER_LOCAL_STORAGE_KEY } from '../constants'

export const DAY_OF_WEEK_FILTER = 'dayOfWeek'
export const SITES_FILTER = 'sites'
export const ROOMS_FILTER = 'rooms'
export const BLOCKS_FILTER = 'blocks'
export const START_DATE_FILTER = 'startDate'

export type AvailableTimesFilters = {
  [DAY_OF_WEEK_FILTER]: DayOfWeek[]
  [START_DATE_FILTER]: string
  [SITES_FILTER]?: string[]
  [ROOMS_FILTER]: string[]
  [BLOCKS_FILTER]?: string[]
}

export const useAvailableTimesFilters = () => {
  // The site/blocks filters are not defined in the defaults as they are set after the surgeon info is fetched
  // We want the default assignment logic below to fallback to those defaults after their state has changed so that
  // the selectedSiteIds and selectedBlockIds change and populate their respective filters
  const [defaults, setDefaults] = useState<AvailableTimesFilters>({
    [DAY_OF_WEEK_FILTER]: DEFAULT_DAYS_OF_WEEK,
    [START_DATE_FILTER]: DateTime.now().plus({ day: 1 }).toISODate(),
    [ROOMS_FILTER]: [],
  })
  const onClear = () =>
    orgLocalStorage.setItem(PAGE_FILTER_LOCAL_STORAGE_KEY, '')
  const { params, onSearch, ...rest } =
    useParsedSearchParams<AvailableTimesFilters>({ defaults, onClear })

  const {
    [DAY_OF_WEEK_FILTER]: selectedDaysOfWeekParam,
    [START_DATE_FILTER]: selectedStartDateParam,
    [SITES_FILTER]: selectedSiteIdsParam,
    [ROOMS_FILTER]: selectedRoomIdsParam,
    [BLOCKS_FILTER]: selectedBlockIdsParam,
  } = params

  const selectedDaysOfWeek =
    Array.isArray(selectedDaysOfWeekParam) &&
    (selectedDaysOfWeekParam.length === 0 ||
      selectedDaysOfWeekParam.every((day) =>
        typeof day === 'string'
          ? VALID_DAYS_OF_WEEK.includes(
              day as (typeof VALID_DAYS_OF_WEEK)[number]
            )
          : false
      ))
      ? (selectedDaysOfWeekParam as DayOfWeek[])
      : defaults[DAY_OF_WEEK_FILTER]
  const selectedStartDate =
    typeof selectedStartDateParam === 'string' &&
    DateTime.fromISO(selectedStartDateParam).isValid
      ? selectedStartDateParam
      : defaults[START_DATE_FILTER]
  const selectedSiteIds =
    Array.isArray(selectedSiteIdsParam) &&
    selectedSiteIdsParam.every((siteId) => typeof siteId === 'string')
      ? selectedSiteIdsParam
      : defaults[SITES_FILTER]
  const selectedRoomIds =
    Array.isArray(selectedRoomIdsParam) &&
    selectedRoomIdsParam.every((roomId) => typeof roomId === 'string')
      ? selectedRoomIdsParam
      : defaults[ROOMS_FILTER]
  const selectedBlockIds =
    Array.isArray(selectedBlockIdsParam) &&
    selectedBlockIdsParam.every((blockId) => typeof blockId === 'string')
      ? selectedBlockIdsParam
      : defaults[BLOCKS_FILTER]

  return {
    selectedDaysOfWeek,
    selectedStartDate,
    selectedSiteIds,
    selectedRoomIds,
    selectedBlockIds,
    onSearch,
    setDefaults,
    ...rest,
  }
}
