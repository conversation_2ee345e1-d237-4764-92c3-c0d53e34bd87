import { useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { Duration } from 'luxon'

import { DayOfWeek } from 'src/pages/Insights/types'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import {
  GetAvailableTimeSlots,
  GetAvailableTimeSlotsVariables,
} from '../__generated__'
import { IAvailableSlot, IRoomInfo } from '../AvailableTimes/interfaces'
import { useBlockData } from '../AvailableTimes/useBlockData'
import { getAvailableSlotsFlat } from '../AvailableTimes/utils'
import { BlockAttributes } from '../interfaces'
import { GET_AVAILABLE_TIME_SLOTS } from '../queries'
import { removeDuplicateAvailableSlots } from '../utils'
import { TimeSelectionFiltersType } from './useTimeSelectionFilters'

export const useAvailableTimeSlotsForTimeSelection = ({
  selectedBlockIds,
  selectedDateRangeStart,
  selectedDateRangeEnd,
  selectedDaysOfWeek,
  selectedRoomIds,
  selectedSiteIds,
  selectedDuration,
  selectedRoomTypes,
  timezone,
}: {
  selectedBlockIds: TimeSelectionFiltersType['blocks']
  selectedDateRangeStart: TimeSelectionFiltersType['dateRangeStart']
  selectedDateRangeEnd: TimeSelectionFiltersType['dateRangeEnd']
  selectedDaysOfWeek: TimeSelectionFiltersType['daysOfWeek']
  selectedRoomIds: TimeSelectionFiltersType['rooms']
  selectedSiteIds: TimeSelectionFiltersType['sites']
  selectedDuration: TimeSelectionFiltersType['duration']
  selectedRoomTypes: TimeSelectionFiltersType['roomType']
  timezone: string
}) => {
  const minAvailableDuration = useMemo(
    () =>
      Duration.fromObject({
        minutes: selectedDuration,
      }),
    [selectedDuration]
  )

  const { sites, isLoading: siteOptionsLoading } = useSiteOptions()

  const { data: availableSlotsData, loading: isLoading } = useQuery<
    GetAvailableTimeSlots,
    GetAvailableTimeSlotsVariables
  >(GET_AVAILABLE_TIME_SLOTS, {
    skip:
      (selectedSiteIds.length === 0 && sites.length > 1) || !selectedDuration,
    variables: {
      siteIds:
        sites.length === 1
          ? sites.map((site) => site.node.id)
          : selectedSiteIds,
      minAvailableDuration: minAvailableDuration.toISO(),
      startDate: selectedDateRangeStart,
      endDate: selectedDateRangeEnd,
    },
  })

  const rooms = useRoomOptions()
  const roomInfoMap = useMemo(
    () =>
      rooms.reduce((acc, room) => {
        acc.set(room?.node?.id, {
          siteId: room?.node?.site.id,
          siteName: room?.node?.site.name,
          roomId: room?.node?.id,
          roomName: room?.node?.name,
          roomTags: room?.node?.tags,
        })
        return acc
      }, new Map<string, IRoomInfo>()),
    [rooms]
  )

  const flatSlots = useMemo(
    () =>
      getAvailableSlotsFlat({
        availableTimeSlots: availableSlotsData?.availableTimeSlots ?? [],
        minAvailableDuration,
        timezone,
        roomInfoMap,
      }),
    [availableSlotsData, minAvailableDuration, timezone, roomInfoMap]
  )

  const {
    blockTimeIdMap,
    blocks,
    loading: isBlockDataLoading,
  } = useBlockData({
    slots: flatSlots,
  })

  const availableSlots = useMemo(() => {
    const allAvailableSlots = flatSlots.filter(
      ({ roomId, startTime, blockTimeIds, roomTags }: IAvailableSlot) => {
        const isValidRoom =
          selectedRoomIds.length > 0 ? selectedRoomIds.includes(roomId) : true
        const roomTagIds = roomTags?.map((tag) => tag.id) ?? []

        let isValidRoomType = true

        if (selectedRoomTypes.length > 0) {
          isValidRoomType =
            roomTags !== undefined && roomTags.length > 0
              ? roomTagIds?.some((tagId) => selectedRoomTypes.includes(tagId))
              : false
        } else {
          isValidRoomType = true
        }
        const dayOfWeek = startTime.weekdayLong.toLocaleLowerCase() as DayOfWeek
        const isValidDayOfWeek =
          selectedDaysOfWeek.length > 0
            ? selectedDaysOfWeek.includes(dayOfWeek)
            : true

        const blocksWithoutUnblockedIds = blockTimeIds
          .map((id) => blockTimeIdMap.get(id)?.block)
          .filter((block): block is BlockAttributes => block !== undefined)

        const isValidBlock = blocksWithoutUnblockedIds.every((block) =>
          selectedBlockIds.includes(block.id)
        )

        return (
          isValidRoom && isValidDayOfWeek && isValidBlock && isValidRoomType
        )
      }
    )
    // remove duplicates is there are slots with more than 1 blocktime
    return removeDuplicateAvailableSlots(allAvailableSlots)
  }, [
    flatSlots,
    selectedRoomIds,
    selectedRoomTypes,
    selectedDaysOfWeek,
    selectedBlockIds,
    blockTimeIdMap,
  ])

  return {
    rooms,
    sites,
    availableSlots,
    availabilityDataLoading: isLoading || siteOptionsLoading,
    isBlockDataLoading,
    blocks,
    blockTimeIdMap,
  }
}
