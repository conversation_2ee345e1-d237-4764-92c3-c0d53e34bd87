import { loader } from './TimeSelection'

describe('ScheduleAssistantRoute', () => {
  const params = {}

  describe('loader', () => {
    it("redirects if carol's email is disabled", () => {
      const request = new Request(
        'https://example.com/schedule-assistant/time-selection'
      )
      const context = { flags: { enableCarolsEmail: false } }
      try {
        loader({ request, params, context }, context)
      } catch (thrown) {
        if (!(thrown instanceof Response)) {
          throw new Error('Expected loader to throw a response')
        }
        expect(thrown).toBeInstanceOf(Response)
        expect(thrown.status).toBe(302)
        expect(thrown.headers.get('Location')).toBe('../case-duration')
      }
    })

    it("does not redirect if carol's email is enabled", () => {
      const request = new Request(
        'https://example.com/schedule-assistant/time-selection'
      )
      const context = { flags: { enableCarolsEmail: true } }
      expect(loader({ request, params, context }, context)).toEqual(null)
    })
  })
})
