import { useState } from 'react'

import { DateTime } from 'luxon'

import { useParsedSearchParams } from '@apella/hooks'
import {
  DEFAULT_DAYS_OF_WEEK,
  VALID_DAYS_OF_WEEK,
} from 'src/components/DayOfWeekFilter'
import { useTimezone } from 'src/Contexts'
import { DayOfWeek } from 'src/pages/Insights/types'
import orgLocalStorage from 'src/utils/orgLocalStorage'

import {
  PAGE_FILTER_LOCAL_STORAGE_KEY_TIME_SELECTION,
  THE_UNBLOCKED_BLOCK_ID,
} from '../constants'

export const DATE_RANGE_START_FILTER = 'dateRangeStart'
export const DATE_RANGE_END_FILTER = 'dateRangeEnd'
export const DAYS_OF_WEEK_FILTER = 'daysOfWeek'
export const SITES_FILTER = 'sites'
export const ROOMS_FILTER = 'rooms'
export const DURATION_FILTER = 'duration'
export const BLOCKS_FILTER = 'blocks'
export const ROOM_TYPE_FILTER = 'roomType'

export const VALID_TIME_SELECTION_FILTERS = [
  DATE_RANGE_START_FILTER,
  DATE_RANGE_END_FILTER,
  DAYS_OF_WEEK_FILTER,
  SITES_FILTER,
  BLOCKS_FILTER,
  ROOMS_FILTER,
  DURATION_FILTER,
  ROOM_TYPE_FILTER,
]

const END_DAYS_OFFSET = 14

// defaults
const DURATION_DEFAULT = 90
export const BLOCKS_DEFAULT = [THE_UNBLOCKED_BLOCK_ID]

export type TimeSelectionFiltersType = {
  [DATE_RANGE_START_FILTER]: string
  [DATE_RANGE_END_FILTER]: string
  [DAYS_OF_WEEK_FILTER]: DayOfWeek[]
  [SITES_FILTER]: string[]
  [ROOMS_FILTER]: string[]
  [DURATION_FILTER]: number
  [BLOCKS_FILTER]: string[]
  [ROOM_TYPE_FILTER]: string[]
}

export const useTimeSelectionFilters = () => {
  const { timezone } = useTimezone()
  const tomorrow = DateTime.now().plus({ day: 1 }).setZone(timezone)
  const [defaults, setDefaults] = useState<TimeSelectionFiltersType>({
    [DATE_RANGE_START_FILTER]: tomorrow.toISODate(),
    [DATE_RANGE_END_FILTER]: tomorrow
      .plus({ days: END_DAYS_OFFSET })
      .toISODate(),
    [DAYS_OF_WEEK_FILTER]: DEFAULT_DAYS_OF_WEEK,
    [SITES_FILTER]: [],
    [ROOMS_FILTER]: [],
    [DURATION_FILTER]: DURATION_DEFAULT,
    [BLOCKS_FILTER]: BLOCKS_DEFAULT,
    [ROOM_TYPE_FILTER]: [],
  })
  const onClear = () =>
    orgLocalStorage.setItem(PAGE_FILTER_LOCAL_STORAGE_KEY_TIME_SELECTION, '')
  const { params, onSearch, ...rest } =
    useParsedSearchParams<TimeSelectionFiltersType>({ defaults, onClear })

  const {
    [DATE_RANGE_START_FILTER]: selectedDateRangeStartParam,
    [DATE_RANGE_END_FILTER]: selectedDateRangeEndParam,
    [DAYS_OF_WEEK_FILTER]: selectedDaysOfWeekParam,
    [SITES_FILTER]: selectedSiteIdsParam,
    [ROOMS_FILTER]: selectedRoomIdsParam,
    [DURATION_FILTER]: selectedDurationParam,
    [BLOCKS_FILTER]: selectedBlockIdsParam,
    [ROOM_TYPE_FILTER]: selectedRoomTypeParam,
  } = params

  const selectedDateRangeStart =
    typeof selectedDateRangeStartParam === 'string' &&
    DateTime.fromISO(selectedDateRangeStartParam).isValid
      ? selectedDateRangeStartParam
      : defaults[DATE_RANGE_START_FILTER]
  const selectedDateRangeEnd =
    typeof selectedDateRangeEndParam === 'string' &&
    DateTime.fromISO(selectedDateRangeEndParam).isValid
      ? selectedDateRangeEndParam
      : defaults[DATE_RANGE_END_FILTER]
  const selectedDaysOfWeek =
    Array.isArray(selectedDaysOfWeekParam) &&
    (selectedDaysOfWeekParam.length === 0 ||
      selectedDaysOfWeekParam.every((day) =>
        typeof day === 'string'
          ? VALID_DAYS_OF_WEEK.includes(
              day as (typeof VALID_DAYS_OF_WEEK)[number]
            )
          : false
      ))
      ? (selectedDaysOfWeekParam as DayOfWeek[])
      : defaults[DAYS_OF_WEEK_FILTER]
  const selectedSiteIds =
    Array.isArray(selectedSiteIdsParam) &&
    selectedSiteIdsParam.every((siteId) => typeof siteId === 'string')
      ? selectedSiteIdsParam
      : defaults[SITES_FILTER]
  const selectedRoomIds =
    Array.isArray(selectedRoomIdsParam) &&
    selectedRoomIdsParam.every((roomId) => typeof roomId === 'string')
      ? selectedRoomIdsParam
      : defaults[ROOMS_FILTER]
  const selectedDurationParamParsed =
    typeof selectedDurationParam === 'string'
      ? parseInt(selectedDurationParam)
      : NaN
  const selectedDuration =
    !isNaN(selectedDurationParamParsed) && selectedDurationParamParsed > 0
      ? selectedDurationParamParsed
      : defaults[DURATION_FILTER]
  const selectedBlockIds =
    Array.isArray(selectedBlockIdsParam) &&
    selectedBlockIdsParam.every((blockId) => typeof blockId === 'string')
      ? selectedBlockIdsParam
      : defaults[BLOCKS_FILTER]

  const selectedRoomTypes =
    Array.isArray(selectedRoomTypeParam) &&
    selectedRoomTypeParam.every((roomType) => typeof roomType === 'string')
      ? selectedRoomTypeParam
      : defaults[ROOM_TYPE_FILTER]

  // Ensure that the selected start date is at least tomorrow and that the end date is equal to or greater than the start date
  const selectedStartDateTime = DateTime.fromISO(selectedDateRangeStart, {
    zone: timezone,
  })
  const selectedEndDateTime = DateTime.fromISO(selectedDateRangeEnd, {
    zone: timezone,
  })
  const validStartDate = DateTime.max(selectedStartDateTime, tomorrow)

  const validDateRangeString =
    selectedEndDateTime >= validStartDate
      ? [validStartDate.toISODate(), selectedEndDateTime.toISODate()]
      : [defaults[DATE_RANGE_START_FILTER], defaults[DATE_RANGE_END_FILTER]]

  return {
    selectedDateRangeStart: validDateRangeString[0],
    selectedDateRangeEnd: validDateRangeString[1],
    selectedDaysOfWeek,
    selectedSiteIds,
    selectedRoomIds,
    selectedDuration,
    selectedBlockIds,
    selectedRoomTypes,
    onSearch,
    setDefaults,
    ...rest,
  }
}
