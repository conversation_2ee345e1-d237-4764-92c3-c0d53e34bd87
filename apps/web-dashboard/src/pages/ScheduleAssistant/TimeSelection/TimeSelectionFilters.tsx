import { useMemo } from 'react'

import { DateTime } from 'luxon'

import { remSpacing, DatePicker, ZIndex } from '@apella/component-library'
import { DayOfWeekFilter } from 'src/components/DayOfWeekFilter'
import { MultiFilterWithCount } from 'src/components/Filters/FilterWithCount'
import { MinimumDurationFilter } from 'src/components/Filters/MinimumDurationFilter'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { useTimezone } from 'src/Contexts'
import { StartAndEndDates } from 'src/pages/types'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { FilterLabel } from '../../../components/FilterLabel'
import { IRoomTagInfo } from '../AvailableTimes/interfaces'
import { BLOCK_FILTER_LABEL } from '../constants'
import { useAvailableTimeSlotsForTimeSelection } from './useAvailableTimeSlotsForTimeSelection'
import {
  BLOCKS_FILTER,
  ROOMS_FILTER,
  SITES_FILTER,
  DAYS_OF_WEEK_FILTER,
  useTimeSelectionFilters,
  DATE_RANGE_START_FILTER,
  DATE_RANGE_END_FILTER,
  DURATION_FILTER,
  BLOCKS_DEFAULT,
  TimeSelectionFiltersType,
  ROOM_TYPE_FILTER,
} from './useTimeSelectionFilters'

export const TimeSelectionFilters = () => {
  const { sites } = useSiteOptions()
  const rooms = useRoomOptions()
  const { timezone } = useTimezone()

  const {
    onSearch,
    selectedBlockIds,
    selectedDateRangeStart,
    selectedDateRangeEnd,
    selectedDaysOfWeek,
    selectedRoomIds,
    selectedSiteIds: originalSelectedSiteIds,
    selectedDuration,
    selectedRoomTypes,
  } = useTimeSelectionFilters()

  const selectedSiteIds = useMemo(() => {
    if (!sites.length || sites.length > 1) {
      return originalSelectedSiteIds
    }
    return [sites[0].node.id]
  }, [sites, originalSelectedSiteIds])

  const selectedSites = useMemo(() => {
    const selectedSites = sites.flatMap((site) => {
      return selectedSiteIds.includes(site.node.id) ? site.node || [] : []
    })
    return selectedSites
  }, [sites, selectedSiteIds])

  const includedRoomTags: IRoomTagInfo[] = useMemo(() => {
    if (selectedRoomIds.length > 0) {
      const roomTags: IRoomTagInfo[] = rooms?.flatMap((room) => {
        return selectedRoomIds.includes(room.node.id) ? room.node.tags : []
      })
      return roomTags
    } else {
      const roomTags: IRoomTagInfo[] = selectedSites?.flatMap((site) => {
        const roomTags: IRoomTagInfo[] = site.rooms.edges.flatMap((room) => {
          return room.node.tags || []
        })
        return roomTags
      })
      return roomTags || []
    }
  }, [rooms, selectedRoomIds, selectedSites])

  const roomTags: IRoomTagInfo[] = useMemo<IRoomTagInfo[]>(() => {
    const uniqueTagsMap = new Map()
    includedRoomTags.forEach((tag) => {
      if (!uniqueTagsMap.has(tag.id)) {
        uniqueTagsMap.set(tag.id, tag)
      }
    })
    return Array.from(uniqueTagsMap.values()) as IRoomTagInfo[]
  }, [includedRoomTags])

  const { blocks, isBlockDataLoading } = useAvailableTimeSlotsForTimeSelection({
    selectedBlockIds,
    selectedDateRangeStart,
    selectedDateRangeEnd,
    selectedDaysOfWeek,
    selectedSiteIds,
    selectedRoomIds,
    selectedDuration,
    selectedRoomTypes,
    timezone,
  })

  return (
    <div
      css={{
        position: 'relative',
        display: 'flex',
        gap: remSpacing.medium,
        zIndex: ZIndex.MORE_ABOVE,
      }}
    >
      <FilterLabel label="Date range">
        <DatePicker
          value={[
            DateTime.fromISO(selectedDateRangeStart, {
              zone: timezone,
            }).toJSDate(),
            DateTime.fromISO(selectedDateRangeEnd, {
              zone: timezone,
            }).toJSDate(),
          ]}
          selectRange
          showPresets
          inTheFuture
          timezone={timezone}
          setValue={(dates: StartAndEndDates) => {
            Array.isArray(dates) &&
              dates[0] &&
              dates[1] &&
              onSearch({
                [DATE_RANGE_START_FILTER]: DateTime.fromJSDate(dates[0])
                  .setZone(timezone)
                  .toISODate(),
                [DATE_RANGE_END_FILTER]: DateTime.fromJSDate(dates[1])
                  .setZone(timezone)
                  .toISODate(),
              })
          }}
        />
      </FilterLabel>
      <FilterLabel label="Days of week">
        <DayOfWeekFilter
          selected={selectedDaysOfWeek}
          onChange={(daysOfWeek) => {
            onSearch({
              [DAYS_OF_WEEK_FILTER]: daysOfWeek,
            })
          }}
        />
      </FilterLabel>
      <FilterLabel label="Minimum duration">
        <MinimumDurationFilter
          label="Minimum Duration"
          value={`${selectedDuration}`}
          onChange={(duration: number) => {
            onSearch({
              [DURATION_FILTER]: duration,
            })
          }}
        />
      </FilterLabel>
      <SitesRoomsFilter
        sitesLabel="Select a site"
        sites={sites}
        rooms={rooms}
        selectedSiteIds={selectedSiteIds}
        selectedRoomIds={selectedRoomIds}
        onChangeSitesAndRooms={(siteIds, roomIds) => {
          const updatedFilters: Partial<TimeSelectionFiltersType> = {
            [SITES_FILTER]: siteIds ?? [],
            [ROOMS_FILTER]: roomIds ?? [],
          }
          // no sites selected
          if (!siteIds) {
            updatedFilters[BLOCKS_FILTER] = BLOCKS_DEFAULT
          }
          onSearch(updatedFilters)
        }}
        multipleSites
        bulkSelectSites
        bulkSelectRooms
        showLabels
      />
      {selectedSiteIds.length ? (
        <FilterLabel label="Block">
          <MultiFilterWithCount
            disabled={isBlockDataLoading}
            items={blocks.map((block) => ({
              node: block.node,
            }))}
            selectedIds={selectedBlockIds}
            onChange={(blockIds) => {
              onSearch({ [BLOCKS_FILTER]: blockIds ?? [] })
            }}
            label={BLOCK_FILTER_LABEL}
            bulkSelect
            selectedOnTop
          />
        </FilterLabel>
      ) : null}
      {selectedSiteIds.length && roomTags.length > 0 ? (
        <FilterLabel label="Room Type">
          <MultiFilterWithCount
            label="All Types"
            items={roomTags.map((tag) => ({
              node: tag,
            }))}
            selectedIds={selectedRoomTypes}
            onChange={(roomTagIds) => {
              onSearch({ [ROOM_TYPE_FILTER]: roomTagIds ?? [] })
            }}
            bulkSelect
            selectedOnTop
          />
        </FilterLabel>
      ) : null}
    </div>
  )
}
