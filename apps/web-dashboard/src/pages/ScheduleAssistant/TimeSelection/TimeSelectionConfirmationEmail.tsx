import { Duration, H5, P2, remSpacing, theme } from '@apella/component-library'
import { EmptyState } from 'src/components/EmptyState'

export const TimeSelectionConfirmationEmail = ({
  emailHtmlString,
  subjectString,
}: {
  emailHtmlString: string
  subjectString: string
}) => {
  const isPreviewEmpty =
    emailHtmlString.length === 0 && subjectString.length === 0
  return (
    <>
      <H5 css={{ marginBlock: remSpacing.xsmall }}>Message Preview</H5>
      <div
        css={{
          display: 'flex',
          padding: remSpacing.medium,
          flexDirection: 'column',
          alignItems: 'flex-start',
          gap: remSpacing.small,
          alignSelf: 'stretch',
          background: isPreviewEmpty ? undefined : theme.palette.gray[10],
          border: `1px solid ${theme.palette.gray[30]}`,
          height: '100%',
        }}
      >
        {isPreviewEmpty ? (
          <EmptyState
            message="Unable to create preview"
            subtext="We encountered an issue while trying to create your email. Please check your connection and try again. If the problem persists, try again later."
            Icon={Duration}
          />
        ) : (
          <>
            <div css={{ display: 'flex', gap: remSpacing.xsmall }}>
              <P2 color={theme.palette.gray[60]}>Subject:</P2>{' '}
              <P2>{subjectString}</P2>
            </div>
            <iframe
              css={{
                width: '100%',
                height: '100%',
                border: 'none',
              }}
              srcDoc={emailHtmlString}
            ></iframe>
          </>
        )}
      </div>
    </>
  )
}
