import { useCallback, useReducer } from 'react'

import { DateTime } from 'luxon'

export enum EditStates {
  Idle = 'idle',
  Editing = 'editing',
}

export enum EditActions {
  Edit = 'edit',
  UpdateTime = 'updateTime',
  Save = 'save',
  Cancel = 'cancel',
  Error = 'error',
  InitializeData = 'initializeData',
}

type StateMachine = {
  initial: EditStates.Idle
  states: {
    [key in EditStates]: {
      on: Partial<{
        [key in EditActions]: EditStates
      }>
    }
  }
}

const STATE_GRAPH: StateMachine = {
  initial: EditStates.Idle,
  states: {
    [EditStates.Idle]: {
      on: {
        [EditActions.Edit]: EditStates.Editing,
      },
    },
    [EditStates.Editing]: {
      on: {
        [EditActions.Save]: EditStates.Idle,
        [EditActions.Cancel]: EditStates.Idle,
        [EditActions.UpdateTime]: EditStates.Editing,
        [EditActions.Error]: EditStates.Editing,
      },
    },
  },
}

export type EditState = {
  startTime?: DateTime
  endTime?: DateTime
  startTimeDraft?: DateTime
  endTimeDraft?: DateTime
  error?: { error: string; field: string } | null
}

export type EditAction = {
  id: string
  type: EditActions
  data?: EditState
}

export type InitializeAction = {
  type: EditActions.InitializeData
  data: {
    timeSlotsById: TimesBySlotId
  }
}

export type EditMachine = Record<
  string,
  {
    state: EditStates
    data: EditState
  }
>

export type TimesBySlotId = Record<string, EditState>

const createInitialEditMachine = (
  initialTimesBySlotId: TimesBySlotId
): EditMachine =>
  Object.entries(initialTimesBySlotId).reduce(
    (acc, [slotId, initialData]) => ({
      ...acc,
      [slotId]: {
        state: STATE_GRAPH.initial,
        data: initialData,
        error: null,
      },
    }),
    {}
  )

type UseEditOptions = {
  callbacks?: {
    [key in EditActions]?: (action: EditAction, state: EditMachine) => void
  }
}

const isInitializeAction = (
  action: EditAction | InitializeAction
): action is InitializeAction => action.type === EditActions.InitializeData

/**
 * Store edit state and related data for available times
 */
export const useEditAvailableTimes = ({
  callbacks = {},
}: UseEditOptions = {}) => {
  const [state, dispatch] = useReducer(
    (
      machine: EditMachine,
      action: EditAction | InitializeAction
    ): EditMachine => {
      if (isInitializeAction(action)) {
        return createInitialEditMachine(action.data.timeSlotsById)
      }
      const { state, data } = machine[action.id]
      const nextState = STATE_GRAPH.states[state]?.on[action.type] || state
      const nextData = { ...data, ...(action.data || {}) }
      return {
        ...machine,
        [action.id]: {
          state: nextState,
          data: nextData,
        },
      }
    },
    {}
  )

  const initializeData = useCallback((data: InitializeAction['data']) => {
    dispatch({ type: EditActions.InitializeData, data })
  }, [])

  const dispatchWithCallback = useCallback(
    (action: EditAction) => {
      dispatch(action)
      if (callbacks[action.type]) {
        callbacks[action.type]!(action, state)
      }
    },
    [state, callbacks]
  )

  const anyTimesInEditMode = Object.values(state).some(
    ({ state }) => state === EditStates.Editing
  )

  return [
    state,
    dispatchWithCallback,
    { initializeData, anyTimesInEditMode },
  ] as const
}
