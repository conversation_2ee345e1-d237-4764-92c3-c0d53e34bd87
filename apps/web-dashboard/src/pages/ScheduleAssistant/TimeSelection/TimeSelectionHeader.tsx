import { Counter, P1, P2, remSpacing } from '@apella/component-library'

export const TimeSelectionHeader = ({
  numToReviewContent,
}: {
  numToReviewContent: React.ReactNode
}) => {
  return (
    <div>
      <div
        css={{
          display: 'flex',
          gap: remSpacing.xsmall,
          alignItems: 'center',
        }}
      >
        <P1>Times to review</P1>
        {typeof numToReviewContent === 'number' ? (
          <Counter count={numToReviewContent} size="sm" color="blue" />
        ) : (
          numToReviewContent
        )}
      </div>
      <P2>Select times to include in the open time email</P2>
    </div>
  )
}
