import { useEffect, useMemo, useRef, useState } from 'react'
import Skeleton from 'react-loading-skeleton'
import { LoaderFunctionArgs, replace } from 'react-router'
import { toast } from 'react-toastify'
import { GroupedVirtuoso } from 'react-virtuoso'

import { useTheme } from '@emotion/react'

import { useMutation } from '@apollo/client'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { difference, groupBy, uniq } from 'lodash'
import { DateTime } from 'luxon'

import {
  Button,
  Dialog,
  H5,
  P2,
  P3,
  pxSpacing,
  remSpacing,
  ToastMessage,
  ZIndex,
  Duration,
} from '@apella/component-library'
import { useApellaAuth0 } from '@apella/hooks'
import { AvailableTimeSlotInput } from 'src/__generated__/globalTypes'
import { EmailListInput } from 'src/components/EmailListTextArea/EmailListInput'
import { EmptyState } from 'src/components/EmptyState'
import { useTimezone } from 'src/Contexts'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { daysOfWeekInOrder } from 'src/pages/Insights/types'
import { getRouteContext, LocationPath } from 'src/router/types'
import { useAnalyticsEventLogger, EVENTS } from 'src/utils/analyticsEvents'
import { logger } from 'src/utils/exceptionLogging'
import orgLocalStorage from 'src/utils/orgLocalStorage'

import {
  EmailAvailableTimes,
  EmailAvailableTimesHtml,
  EmailAvailableTimesHtmlVariables,
  EmailAvailableTimesVariables,
} from '../__generated__'
import { AvailableSlot } from '../AvailableSlot'
import { AvailableSlotDate } from '../AvailableTimes/AvailableSlotDate'
import { AvailableSlotsByDateSkeleton } from '../AvailableTimes/AvailableSlotsByDateSkeleton'
import { SLOT_DATE_FMT } from '../AvailableTimes/constants'
import { IAvailableSlot } from '../AvailableTimes/interfaces'
import { useTimeFinderCaseData } from '../AvailableTimes/useApellaCaseData'
import { getDateTimeFromSlotTime } from '../AvailableTimes/utils'
import {
  PAGE_FILTER_LOCAL_STORAGE_KEY,
  PAGE_FILTER_LOCAL_STORAGE_KEY_TIME_SELECTION,
  THE_UNBLOCKED_BLOCK_ID,
} from '../constants'
import { BlockTimeAttributes } from '../interfaces'
import { EMAIL_AVAILABLE_TIMES, EMAIL_AVAILABLE_TIMES_HTML } from '../queries'
import { TimeSelectionConfirmationEmail } from './TimeSelectionConfirmationEmail'
import { TimeSelectionFilters } from './TimeSelectionFilters'
import { TimeSelectionHeader } from './TimeSelectionHeader'
import { useAvailableTimeSlotsForTimeSelection } from './useAvailableTimeSlotsForTimeSelection'
import {
  EditActions,
  EditState,
  TimesBySlotId,
  useEditAvailableTimes,
} from './useEditAvailableTimes'
import {
  BLOCKS_FILTER,
  useTimeSelectionFilters,
} from './useTimeSelectionFilters'

export const loader = ({ request }: LoaderFunctionArgs, context: unknown) => {
  const { flags } = getRouteContext(context)
  orgLocalStorage.setItem(
    'scheduleAssistantLastTab',
    LocationPath.TimeSelection
  )
  const storedSearchParams =
    orgLocalStorage.getItem(PAGE_FILTER_LOCAL_STORAGE_KEY_TIME_SELECTION) ?? ''
  const requestURL = new URL(request.url)

  const caseDurationStoredFilters =
    orgLocalStorage.getItem(PAGE_FILTER_LOCAL_STORAGE_KEY) ?? ''
  if (!flags.enableCarolsEmail) {
    orgLocalStorage.removeItem(PAGE_FILTER_LOCAL_STORAGE_KEY_TIME_SELECTION)
    throw replace(`../${LocationPath.CaseDuration + caseDurationStoredFilters}`)
  }

  if (requestURL.search === '' && requestURL.search !== storedSearchParams) {
    return replace(requestURL.pathname + storedSearchParams)
  }

  orgLocalStorage.setItem(
    PAGE_FILTER_LOCAL_STORAGE_KEY_TIME_SELECTION,
    requestURL.search
  )

  return null
}

export const TimeSelection = () => {
  const { timezone } = useTimezone()
  const theme = useTheme()
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false)
  const [confirmationModalLoading, setConfirmationModalLoading] =
    useState<boolean>(false)
  const [emailHtmlString, setEmailHtmlString] = useState('')
  const [subjectString, setSubjectString] = useState('')
  const [recipients, setRecipients] = useState<string[]>([])
  const [selectedSlots, setSelectedSlots] = useState<string[]>([])
  const seenSlots = useRef<Set<string>>(new Set())
  const timePlurality = `time${selectedSlots.length === 1 ? '' : 's'}`
  const wasPlurality = selectedSlots.length > 1 ? 'were' : 'was'

  const { user } = useApellaAuth0()
  const { enableCarolsEmailV1 } = useFlags<WebDashboardFeatureFlagSet>()

  const [emailAvailableTimes] = useMutation<
    EmailAvailableTimes,
    EmailAvailableTimesVariables
  >(EMAIL_AVAILABLE_TIMES)
  const [emailAvailableTimesHtml] = useMutation<
    EmailAvailableTimesHtml,
    EmailAvailableTimesHtmlVariables
  >(EMAIL_AVAILABLE_TIMES_HTML)

  const {
    selectedBlockIds,
    selectedDateRangeStart,
    selectedDateRangeEnd,
    selectedDaysOfWeek,
    selectedSiteIds,
    selectedRoomIds,
    selectedRoomTypes,
    selectedDuration,
    clearParams,
    onSearch,
  } = useTimeSelectionFilters()

  const {
    availableSlots,
    availabilityDataLoading,
    blockTimeIdMap,
    blocks,
    isBlockDataLoading,
    sites,
    rooms,
  } = useAvailableTimeSlotsForTimeSelection({
    selectedBlockIds,
    selectedDateRangeStart,
    selectedDateRangeEnd,
    selectedDaysOfWeek,
    selectedSiteIds,
    selectedRoomIds,
    selectedDuration,
    selectedRoomTypes,
    timezone,
  })

  const { site: primarySite } = useMemo(() => {
    if (!sites.length || sites.length > 1) {
      return {}
    }
    return {
      site: sites[0].node,
    }
  }, [sites])

  const allBlockIds = blocks.map((b) => b.node.id)
  const { isTimeFinderCaseDataLoading, timeFinderCaseData } =
    useTimeFinderCaseData({
      minEndTime: DateTime.fromFormat(selectedDateRangeStart, SLOT_DATE_FMT, {
        zone: timezone,
      }).startOf('day'),
      maxStartTime: DateTime.fromFormat(selectedDateRangeEnd, SLOT_DATE_FMT, {
        zone: timezone,
      }).endOf('day'),
      siteIds: primarySite ? [primarySite.id] : selectedSiteIds,
      skip:
        (!selectedSiteIds && !primarySite) ||
        !selectedDateRangeStart ||
        !selectedDateRangeEnd,
    })

  const timeSlotsById = useMemo(() => {
    return availableSlots.reduce<TimesBySlotId>((acc, slot) => {
      acc[slot.id] = {
        startTime: slot.startTime,
        endTime: slot.endTime,
        startTimeDraft: slot.startTime,
        endTimeDraft: slot.endTime,
        error: null,
      }
      return acc
    }, {})
  }, [availableSlots])

  const selectedSites = selectedSiteIds.length
    ? selectedSiteIds
    : [primarySite?.id]

  const selectedBlockNames = selectedBlockIds
    .map(
      (blockId) => blocks.find((block) => block.node.id === blockId)?.node.name
    )
    .join(', ')

  const analyticsData = {
    pageSourceName: LocationPath.TimeSelection,
    selectedDuration,
    selectedDateRangeStart,
    selectedDateRangeEnd,
    selectedDaysOfWeek,
    selectedBlockNames,
    selectedBlockIds,
    selectedSiteIds: selectedSites,
    selectedRoomIds,
  }

  const [
    editState,
    dispatch,
    { initializeData: initializeEditData, anyTimesInEditMode },
  ] = useEditAvailableTimes({
    callbacks: {
      [EditActions.Save]: (action, state) =>
        eventsLogger(EVENTS.TIME_SELECTION_SAVE, {
          action,
          newStart: state[action.id]?.data?.startTime,
          newEnd: state[action.id]?.data?.endTime,
          initialStart: timeSlotsById[action.id]?.startTime,
          initialEnd: timeSlotsById[action.id]?.endTime,
          ...analyticsData,
        }),
      [EditActions.Cancel]: (action) =>
        eventsLogger(EVENTS.TIME_SELECTION_CANCEL, {
          action,
          ...analyticsData,
        }),
      [EditActions.Edit]: (action) =>
        eventsLogger(EVENTS.TIME_SELECTION_EDIT, {
          action,
          ...analyticsData,
        }),
      [EditActions.Error]: (action) =>
        eventsLogger(EVENTS.TIME_SELECTION_ERROR, {
          action,
          ...analyticsData,
        }),
      [EditActions.UpdateTime]: (action) =>
        eventsLogger(EVENTS.TIME_SELECTION_UPDATE_TIME, {
          action,
          ...analyticsData,
        }),
    },
  })

  useEffect(() => {
    initializeEditData({ timeSlotsById })
  }, [initializeEditData, timeSlotsById])

  const eventsLogger = useAnalyticsEventLogger()

  const { slotsByDate, byDayCounts, dateGroups } = useMemo(() => {
    const dateGroups = uniq(availableSlots.map((slot) => slot.date))
    const slotsByDate = groupBy(availableSlots, 'date')
    const byDayCounts = dateGroups.map((date) => slotsByDate[date].length)

    return {
      slotsByDate,
      byDayCounts,
      dateGroups,
    }
  }, [availableSlots])

  const readyToCheckParams = !availabilityDataLoading && !isBlockDataLoading

  /**
   * This series of useEffects are needed because we cannot validate params until all data is loaded
   * In a future where data loading does not happen inside of the render lifecycle, we can replace these
   * with async/await + replace from react-router
   */
  // Clear params if selected days of week are not valid
  useEffect(() => {
    if (
      selectedDaysOfWeek.length &&
      difference(selectedDaysOfWeek, daysOfWeekInOrder).length
    ) {
      clearParams()
    }
  }, [selectedDaysOfWeek, clearParams])

  // Clear params if selected site is not valid
  useEffect(() => {
    if (
      selectedSiteIds.length &&
      readyToCheckParams &&
      difference(
        selectedSiteIds,
        sites.map((s) => s.node.id)
      ).length
    ) {
      clearParams()
    }
  }, [selectedSiteIds, clearParams, sites, readyToCheckParams])

  // Clear params if selected room is not valid
  useEffect(() => {
    if (
      selectedRoomIds.length &&
      readyToCheckParams &&
      difference(
        selectedRoomIds,
        rooms.map((r) => r.node.id)
      ).length
    ) {
      clearParams()
    }
  }, [selectedRoomIds, clearParams, rooms, readyToCheckParams])

  // Set default block if selected block is not valid.
  // "Not valid" could mean the selected block is no longer in the time range of slot data we fetched
  useEffect(() => {
    if (
      selectedBlockIds.length &&
      readyToCheckParams &&
      difference(selectedBlockIds, allBlockIds).length
    ) {
      onSearch({ [BLOCKS_FILTER]: [THE_UNBLOCKED_BLOCK_ID] })
    }
  }, [selectedBlockIds, allBlockIds, readyToCheckParams, onSearch])

  const availableSlotsInput: AvailableTimeSlotInput[] = selectedSlots.reduce<
    AvailableTimeSlotInput[]
  >((acc, slotId) => {
    const slot = availableSlots.find((slot) => slot.id === slotId)
    if (slot) {
      const editData = editState[slotId]?.data || {}
      const edited =
        editData.startTime !== slot?.startTime ||
        editData.endTime !== slot?.endTime
      const maxAvailableDuration =
        edited && editData?.endTime && editData?.startTime
          ? editData?.endTime?.diff(editData?.startTime)
          : slot.maxAvailableDuration
      const availableTimeSlotInput: AvailableTimeSlotInput = {
        startTime: editData?.startTime?.toISO() ?? slot.startTime.toISO(),
        endTime: editData?.endTime?.toISO() ?? slot.endTime.toISO(),
        maxAvailableDuration: maxAvailableDuration.toString(),
        roomId: slot.roomId,
      }
      acc.push(availableTimeSlotInput)
    }
    return acc
  }, [])

  const handleSendAvailableTimes = (v1Enabled: boolean) => async () => {
    const emailId = window.crypto.randomUUID()
    try {
      if (!user?.email) {
        throw new Error('No email available')
      }
      if (!recipients.length && v1Enabled) {
        throw new Error('Please enter recipients')
      }

      const { data } = await emailAvailableTimes({
        variables: {
          input: {
            startDate: selectedDateRangeStart,
            endDate: selectedDateRangeEnd,
            recipients: enableCarolsEmailV1 ? recipients : [user?.email],
            slots: availableSlotsInput,
            senderEmail: enableCarolsEmailV1 ? user?.email : null,
            senderName: enableCarolsEmailV1 ? user?.name : null,
          },
        },
      })

      if (!data?.emailAvailableTimes?.success) {
        throw new Error('There was an error sending the email')
      }

      selectedSlots.forEach((slotId) => {
        const slot = availableSlots.find((slot) => slot.id === slotId)
        const editData = editState[slotId]?.data || {}
        if (slot) {
          eventsLogger(EVENTS.TIME_SELECTION_SEND_TIME, {
            emailId,
            ...getEventLogFromSlot(slot, editData),
            ...analyticsData,
          })
        }
      })

      seenSlots.current.forEach((slotId) => {
        const slot = availableSlots
          // slots that are seen but not selected
          .filter((slot) => !selectedSlots.includes(slot.id))
          .find((slot) => slot.id === slotId)
        const editData = editState[slotId]?.data || {}
        if (slot) {
          eventsLogger(EVENTS.TIME_SELECTION_EVAL_TIME, {
            emailId,
            ...getEventLogFromSlot(slot, editData),
            ...analyticsData,
          })
        }
      })

      eventsLogger(EVENTS.TIME_SELECTION_SEND_EMAIL, {
        emailId,
        ...analyticsData,
        numSelected: selectedSlots.length,
        numSeen: seenSlots.current.size,
        numTotalSlots: availableSlots.length,
        recipients,
      })

      toast.success(
        <ToastMessage
          theme={theme}
          title="Email Sent"
          message={`${selectedSlots.length} available ${timePlurality} ${wasPlurality} sent via email`}
        />
      )
    } catch (e) {
      toast.error(
        <ToastMessage
          theme={theme}
          title="Email Not Sent"
          message="There was an error sending the email"
        />
      )
      logger.error('Failed to send the email', { errorMessage: e })
    }
    setSelectedSlots([])
    seenSlots.current = new Set()
    setIsConfirmationModalOpen(false)
  }

  const openConfirmationModal = async () => {
    eventsLogger(EVENTS.TIME_SELECTION_CONFIRM_SELECTION, {
      ...analyticsData,
      numSelected: selectedSlots.length,
    })
    setConfirmationModalLoading(true)
    try {
      const { data } = await emailAvailableTimesHtml({
        variables: {
          input: {
            startDate: selectedDateRangeStart,
            endDate: selectedDateRangeEnd,
            slots: availableSlotsInput,
          },
        },
      })

      if (!data?.emailAvailableTimesHtml?.success) {
        throw new Error('There was an error getting the email template data')
      }
      setEmailHtmlString(data.emailAvailableTimesHtml?.email || '')
      setSubjectString(data.emailAvailableTimesHtml?.subject || '')
    } catch (e) {
      logger.error('Failed to get email template data', { errorMessage: e })
    } finally {
      setConfirmationModalLoading(false)
      setIsConfirmationModalOpen(true)
    }
  }
  const isPreviewEmpty =
    emailHtmlString.length === 0 && subjectString.length === 0

  return (
    <>
      <TimeSelectionFilters />
      {selectedSiteIds.length ? (
        <TimeSelectionHeader
          numToReviewContent={
            availabilityDataLoading ? (
              <Skeleton borderRadius={10} width={25} height={20} />
            ) : (
              availableSlots.length - selectedSlots.length
            )
          }
        />
      ) : null}
      {availabilityDataLoading || isBlockDataLoading ? (
        <section>
          <AvailableSlotsByDateSkeleton showCheckBoxSkeleton />
          <AvailableSlotsByDateSkeleton showCheckBoxSkeleton />
        </section>
      ) : (selectedSiteIds.length > 0 || primarySite) &&
        availableSlots?.length ? (
        <GroupedVirtuoso
          style={{ height: '100vh' }}
          groupCounts={byDayCounts}
          groupContent={(index) => {
            const dateString = dateGroups[index]
            const slots = slotsByDate[dateString]
            return <AvailableSlotDate dateString={dateString} slots={slots} />
          }}
          itemContent={(index) => {
            const slot = availableSlots[index]
            // Because we are using a virtualized list, slots are only rendered when they are scrolled to
            // we can use that to roughly track with times were "seen"
            seenSlots.current.add(slot.id)
            const { state: uiState, data: slotEditState } =
              editState[slot.id] || {}
            const isSelected = selectedSlots.includes(slot.id)
            const casesByDate = timeFinderCaseData?.get(slot.date) ?? new Map()
            const slotDateTime = DateTime.fromFormat(slot.date, SLOT_DATE_FMT, {
              zone: timezone,
            })
            // draftChanged tracks if the draft value has been changed across edits
            const draftChanged =
              slotEditState?.startTimeDraft &&
              slotEditState?.endTimeDraft &&
              (slot.startTime
                .diff(slotEditState.startTimeDraft)
                .as('minutes') !== 0 ||
                slot.endTime.diff(slotEditState.endTimeDraft).as('minutes') !==
                  0)

            const blockTimes = slot.blockTimeIds.reduce<BlockTimeAttributes[]>(
              (acc, blockTimeId) => {
                const block = blockTimeIdMap.get(blockTimeId)

                if (block) {
                  acc.push(block)
                }
                return acc
              },
              []
            )

            return (
              <AvailableSlot
                key={slot.id}
                slot={slot}
                blockTimes={blockTimes}
                timeFinderCases={casesByDate?.get(slot.roomId) ?? []}
                isTimeFinderCaseDataLoading={isTimeFinderCaseDataLoading}
                selectable
                isSelected={isSelected}
                editable
                onSelected={(selected) => {
                  eventsLogger(
                    selected
                      ? EVENTS.TIME_SELECTION_SELECT_TIME
                      : EVENTS.TIME_SELECTION_UNSELECT_TIME,
                    {
                      ...getEventLogFromSlot(slot, slotEditState),
                      ...analyticsData,
                    }
                  )
                  setSelectedSlots((prev) =>
                    selected
                      ? [...prev, slot.id]
                      : prev.filter((id) => id !== slot.id)
                  )
                }}
                editState={uiState}
                editData={slotEditState}
                onChangeStartTime={(e) => {
                  const startTime = getDateTimeFromSlotTime(
                    e.target.value,
                    slotDateTime
                  )
                  dispatch({
                    type: EditActions.UpdateTime,
                    id: slot.id,
                    data: { startTime },
                  })
                  const { error, field } = validateSlot(
                    {
                      ...slotEditState,
                      startTime,
                    },
                    slot
                  )
                  if (error) {
                    dispatch({
                      type: EditActions.Error,
                      id: slot.id,
                      data: { error: { error, field } },
                    })
                  } else if (slotEditState.error) {
                    dispatch({
                      type: EditActions.Error,
                      id: slot.id,
                      data: { error: null },
                    })
                  }
                }}
                onChangeEndTime={(e) => {
                  const endTime = getDateTimeFromSlotTime(
                    e.target.value,
                    slotDateTime
                  )
                  dispatch({
                    type: EditActions.UpdateTime,
                    id: slot.id,
                    data: {
                      endTime,
                    },
                  })
                  const { error, field } = validateSlot(
                    {
                      ...slotEditState,
                      endTime,
                    },
                    slot
                  )
                  if (error) {
                    dispatch({
                      type: EditActions.Error,
                      id: slot.id,
                      data: { error: { error, field } },
                    })
                  } else if (slotEditState.error) {
                    dispatch({
                      type: EditActions.Error,
                      id: slot.id,
                      data: { error: null },
                    })
                  }
                }}
                onSave={() => {
                  const { error, field } = validateSlot(slotEditState, slot)
                  if (error) {
                    dispatch({
                      type: EditActions.Error,
                      id: slot.id,
                      data: { error: { error, field } },
                    })
                  } else {
                    dispatch({ type: EditActions.Save, id: slot.id })
                    if (draftChanged) {
                      setSelectedSlots((prev) =>
                        prev.filter((id) => id !== slot.id)
                      )
                    }
                  }
                }}
                onCancel={() =>
                  dispatch({
                    type: EditActions.Cancel,
                    id: slot.id,
                    data: {
                      startTime: slotEditState.startTimeDraft,
                      endTime: slotEditState.endTimeDraft,
                      error: null,
                    },
                  })
                }
                onClickEdit={() =>
                  dispatch({
                    type: EditActions.Edit,
                    id: slot.id,
                    data: {
                      startTimeDraft: slotEditState.startTime,
                      endTimeDraft: slotEditState.endTime,
                    },
                  })
                }
              />
            )
          }}
        />
      ) : selectedSiteIds.length === 0 && sites.length > 1 ? (
        <EmptyState
          message="Select a site"
          subtext="to review available times"
          Icon={Duration}
        />
      ) : (
        <EmptyState
          message="No times to review"
          subtext="No times are available to review for the selected filters"
        />
      )}
      {selectedSlots.length > 0 ? (
        <footer
          css={{
            padding: remSpacing.large,
            display: 'flex',
            gap: remSpacing.small,
            alignItems: 'center',
            justifyContent: 'flex-end',
            backgroundColor: theme.palette.background.primary,
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            zIndex: ZIndex.ABOVE,
            boxShadow: '0px 1px 18px 0px rgba(0, 0, 0, 0.12)',
          }}
        >
          <p>{`${selectedSlots.length} ${timePlurality} selected`}</p>
          <Button
            disabled={anyTimesInEditMode || confirmationModalLoading}
            onClick={openConfirmationModal}
          >{`Confirm selected ${timePlurality}`}</Button>
          <Button
            onClick={() => {
              setSelectedSlots([])
              eventsLogger(EVENTS.TIME_SELECTION_CLEAR_SELECTION, {
                ...analyticsData,
              })
            }}
            color="alternate"
          >
            Clear selection
          </Button>
        </footer>
      ) : null}
      {enableCarolsEmailV1 ? (
        <Dialog
          css={{
            height: '100%',
          }}
          isOpen={isConfirmationModalOpen}
          title={'Send time availability email'}
          onClose={() => {
            setIsConfirmationModalOpen(false)
            eventsLogger(EVENTS.TIME_SELECTION_CLOSE_CONFIRMATION_MODAL, {
              ...analyticsData,
            })
          }}
          isFullScreen
        >
          <div
            css={{
              margin: '0 auto',
              maxWidth: '1000px',
              height: '95%',
              marginTop: remSpacing.medium,
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <div>
              <label htmlFor="recipients">
                <H5 css={{ marginBottom: remSpacing.xsmall }}>Recipients</H5>
              </label>
              <EmailListInput
                autoFocus
                inputName="recipients"
                onChange={(emails) => setRecipients(emails)}
              />
            </div>
            <P3
              css={{
                color: theme.palette.gray[60],
                marginTop: remSpacing.xsmall,
                marginBottom: remSpacing.medium,
              }}
            >
              Your email will be <NAME_EMAIL>. Replies will be
              forwarded to {user?.email}
            </P3>
            <TimeSelectionConfirmationEmail
              emailHtmlString={emailHtmlString}
              subjectString={subjectString}
            />
            <div
              css={{
                marginTop: remSpacing.medium,
                display: 'flex',
                gap: pxSpacing.small,
                justifyContent: 'flex-end',
              }}
            >
              {!isPreviewEmpty && (
                <Button
                  disabled={recipients.length === 0}
                  onClick={handleSendAvailableTimes(enableCarolsEmailV1)}
                >
                  Send
                </Button>
              )}
              <Button
                color={isPreviewEmpty ? 'primary' : 'alternate'}
                onClick={() => {
                  setIsConfirmationModalOpen(false)
                  eventsLogger(EVENTS.TIME_SELECTION_CLOSE_CONFIRMATION_MODAL, {
                    ...analyticsData,
                  })
                }}
              >
                {isPreviewEmpty ? 'Close' : 'Cancel'}
              </Button>
            </div>
          </div>
        </Dialog>
      ) : (
        <Dialog
          isOpen={isConfirmationModalOpen}
          title="Send available times email?"
          onClose={() => {
            setIsConfirmationModalOpen(false)
            eventsLogger(EVENTS.TIME_SELECTION_CLOSE_CONFIRMATION_MODAL, {
              ...analyticsData,
            })
          }}
        >
          <P2
            css={{ marginBottom: pxSpacing.gutter }}
            color={theme.palette.gray[70]}
          >
            {`${selectedSlots.length} available ${timePlurality} will be sent to your email address. Once received, please forward to your surgeons and schedulers.`}
          </P2>
          <div css={{ display: 'flex', gap: pxSpacing.small }}>
            <Button onClick={handleSendAvailableTimes(enableCarolsEmailV1)}>
              Send
            </Button>
            <Button
              color="alternate"
              onClick={() => {
                setIsConfirmationModalOpen(false)
                eventsLogger(EVENTS.TIME_SELECTION_CLOSE_CONFIRMATION_MODAL, {
                  ...analyticsData,
                })
              }}
            >
              Cancel
            </Button>
          </div>
        </Dialog>
      )}
    </>
  )
}

const getEventLogFromSlot = (slot: IAvailableSlot, editData: EditState) => {
  return {
    edited:
      editData?.startTime !== slot.startTime ||
      editData?.endTime !== slot.endTime,
    slotStartTime: editData?.startTime?.toISO(),
    slotEndTime: editData?.endTime?.toISO(),
    initialStartTime: slot?.startTime.toISO(),
    initialEndTime: slot?.endTime.toISO(),
    slotMaxAvailableDuration: slot?.maxAvailableDuration.toHuman(),
    slotMaxAvailableDurationMinutes: slot?.maxAvailableDuration.as('minutes'),
    slotRoomId: slot?.roomId,
    slotRoomName: slot?.roomName,
    slotSiteId: slot?.siteId,
    slotSiteName: slot?.siteName,
    slotDate: slot?.date,
  }
}

const validateSlot = (editData: EditState, originalSlot: IAvailableSlot) => {
  if (!editData.startTime) {
    return { error: 'Both fields must be complete', field: 'startTime' }
  }
  if (!editData.endTime) {
    return { error: 'Both fields must be complete', field: 'startTime' }
  }
  if (editData.startTime >= editData.endTime) {
    return { error: 'Start time must be before end time', field: 'startTime' }
  }
  if (originalSlot.startTime > editData.startTime) {
    return {
      error: 'Time must be within original time range',
      field: 'startTime',
    }
  }
  if (originalSlot.endTime < editData.endTime) {
    return {
      error: 'Time must be within original time range',
      field: 'endTime',
    }
  }

  return { error: null, field: null }
}
