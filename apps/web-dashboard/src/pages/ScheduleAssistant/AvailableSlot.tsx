import { ForwardedRef, forwardRef, useState } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime, Duration, Interval } from 'luxon'
import { rem } from 'polished'

import {
  Button,
  remSpacing,
  SelectToggleIcon,
  Edit,
  InputTime,
  ApellaDateTimeFormats,
  AdditionalText,
} from '@apella/component-library'
import { MetricTile } from 'src/components/MetricTile'

import { IAvailableSlot } from './AvailableTimes/interfaces'
import { MiniDateRoomSchedule } from './AvailableTimes/MiniSchedule/MiniSchedule'
import { TimeFinderCase } from './AvailableTimes/useApellaCaseData'
import { getSlotTimeRange } from './AvailableTimes/utils'
import { BlockChips } from './BlockChips'
import { BlockTimeAttributes } from './interfaces'
import { RoomTagChips } from './RoomTagChips'
import { EditStates, EditState } from './TimeSelection/useEditAvailableTimes'

interface EditProps {
  editable: true
  editData: EditState
  editState: EditStates
  onCancel: () => void
  onChangeEndTime: (e: React.ChangeEvent<HTMLInputElement>) => void
  onChangeStartTime: (e: React.ChangeEvent<HTMLInputElement>) => void
  onClickEdit: () => void
  onSave: () => void
}

interface NonEditProps {
  editable?: false
  editData?: never
  editState?: never
  onCancel?: never
  onChangeEndTime?: never
  onChangeStartTime?: never
  onClickEdit?: never
  onSave?: never
}

interface SelectProps {
  isSelected: boolean
  onSelected: (selection: boolean) => void
  selectable: true
}

interface NonSelectProps {
  isSelected?: never
  onSelected?: never
  selectable?: false
}

// Combine using discriminating union
export type AvailableSlotProps = {
  blockTimes: BlockTimeAttributes[]
  durationUsedInSlot?: Duration
  isTimeFinderCaseDataLoading: boolean
  slot: IAvailableSlot
  timeFinderCases: TimeFinderCase[]
  maxAvailableDuration?: Duration
  showTooltip?: boolean
} & (EditProps | NonEditProps) &
  (SelectProps | NonSelectProps)

export const AvailableSlot = forwardRef(function AvailableSlot(
  {
    timeFinderCases,
    isTimeFinderCaseDataLoading,
    blockTimes,
    isSelected = false,
    selectable = false,
    editable = false,
    onSelected,
    durationUsedInSlot,
    maxAvailableDuration,
    slot: {
      roomName,
      siteName,
      startTime: initialStart,
      endTime: initialEnd,
      id: slotId,
      roomId,
    },
    editData,
    editState = EditStates.Idle,
    onChangeStartTime,
    onChangeEndTime,
    onSave,
    onCancel,
    onClickEdit,
    showTooltip = false,
  }: AvailableSlotProps,
  ref: ForwardedRef<HTMLDivElement>
) {
  const theme = useTheme()
  const [showEdit, setShowEdit] = useState(false)

  const showEditMode = editable && editState === EditStates.Idle && showEdit
  // Dynamic version of each time/duration prop so that the UI shows updates
  const startTime = editData?.startTime ?? initialStart
  const endTime = editData?.endTime ?? initialEnd
  const availableDuration = maxAvailableDuration ?? endTime.diff(startTime)
  // edited tracks if the initial value from the server has been changed
  const edited =
    startTime.diff(initialStart).as('minutes') !== 0 ||
    endTime.diff(initialEnd).as('minutes') !== 0

  // Filter out block times that don't overlap the slot times
  // this needs to happen here, despite slots having blockTimeIds, because
  // when a slot is edited, the blockTimeIds are not updated
  const filteredBlockTimes = blockTimes.filter((bt) => {
    const btInterval = Interval.fromDateTimes(
      DateTime.fromISO(bt.startTime, { zone: 'utc' }),
      DateTime.fromISO(bt.endTime, { zone: 'utc' })
    )
    const slotInterval = Interval.fromDateTimes(startTime, endTime)

    return btInterval.overlaps(slotInterval)
  })

  return (
    <div
      data-testid="available-slot"
      ref={ref}
      css={{
        paddingBottom: remSpacing.medium,
      }}
    >
      <MetricTile
        borderColor={
          isSelected ? theme.palette.green[50] : theme.palette.gray[30]
        }
      >
        <div
          onMouseOver={() => editable && setShowEdit(true)}
          onMouseLeave={() => editable && setShowEdit(false)}
          css={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: `${remSpacing.medium} ${remSpacing.gutter}`,
            height: rem(100),
            alignSelf: 'stretch',
            alignContent: 'center',
          }}
        >
          <div
            css={{
              display: 'flex',
              alignItems: 'center',
              gap: remSpacing.small,
            }}
          >
            {selectable && (
              <Button
                disabled={editState === EditStates.Editing}
                onClick={() => onSelected && onSelected(!isSelected)}
                appearance="link"
                css={{
                  width: rem(20),
                  height: rem(20),
                }}
                role="checkbox"
                aria-checked={isSelected}
                aria-label={`Select ${siteName} ${roomName} from ${startTime.toLocaleString(
                  ApellaDateTimeFormats.TIME
                )} to ${endTime.toLocaleString(ApellaDateTimeFormats.TIME)}`}
              >
                <SelectToggleIcon
                  type={isSelected ? 'selected' : undefined}
                  size="md"
                />
              </Button>
            )}
            <div
              css={{
                display: 'flex',
                flexDirection: 'column',
                minWidth: rem(112),
                fontWeight: 'bold',
                textAlign: 'left',
                gap: remSpacing.xsmall,
              }}
            >
              <div
                css={{
                  color: theme.palette.gray[50],
                }}
              >
                {siteName}
              </div>
              <div>{roomName}</div>
            </div>
            {editState === EditStates.Editing ? (
              <div>
                <div
                  css={{
                    display: 'flex',
                    alignItems: 'center',
                    textAlign: 'left',
                    gap: remSpacing.medium,
                    width: rem(350),
                  }}
                >
                  <InputTime
                    css={{ margin: 0 }}
                    error={
                      editData?.error?.field === 'startTime'
                        ? 'Invalid time'
                        : undefined
                    }
                    highlightErrorOnly
                    required
                    size="sm"
                    label="Start"
                    name="startTime"
                    onChange={onChangeStartTime}
                    value={editData?.startTime?.toLocaleString(
                      ApellaDateTimeFormats.TIME
                    )}
                  />
                  <InputTime
                    css={{ margin: 0 }}
                    error={
                      editData?.error?.field === 'endTime'
                        ? 'Invalid time'
                        : undefined
                    }
                    highlightErrorOnly
                    required
                    size="sm"
                    label="End"
                    name="endTime"
                    onChange={onChangeEndTime}
                    value={editData?.endTime?.toLocaleString(
                      ApellaDateTimeFormats.TIME
                    )}
                  />
                  <Button
                    css={{ alignSelf: 'flex-end' }}
                    color="primary"
                    onClick={onSave}
                    size="sm"
                  >
                    Save
                  </Button>
                  <Button
                    css={{ alignSelf: 'flex-end' }}
                    color="alternate"
                    onClick={onCancel}
                    size="sm"
                  >
                    Cancel
                  </Button>
                </div>
                <AdditionalText
                  css={{ marginTop: remSpacing.xxsmall }}
                  error={editData?.error?.error}
                />
              </div>
            ) : (
              <div
                css={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: remSpacing.xsmall,
                  width: rem(150),
                  textAlign: 'left',
                }}
              >
                <div css={{ fontWeight: 'bold' }}>
                  {getSlotTimeRange(startTime, endTime)}
                </div>
                {edited && (
                  <span
                    css={{
                      fontSize: rem('10px'),
                      color: theme.palette.gray[70],
                      textTransform: 'uppercase',
                    }}
                  >
                    edited
                  </span>
                )}
                {editState === EditStates.Idle && (
                  <div css={{ color: theme.palette.gray[50] }}>
                    {`${availableDuration.as('minutes').toFixed(0)}m available`}
                  </div>
                )}
              </div>
            )}
            {showEditMode && (
              <Button
                appearance="link"
                css={{ color: theme.palette.blue[60] }}
                onClick={onClickEdit}
                size="sm"
              >
                <Edit size="sm" />
                Edit
              </Button>
            )}
            {editState === EditStates.Idle && !showEdit && (
              <div
                css={{
                  display: 'flex',
                  gap: remSpacing.small,
                  alignItems: 'center',
                  alignSelf: 'center',
                }}
              >
                <BlockChips
                  blockTimes={filteredBlockTimes}
                  showTooltip={showTooltip}
                />
                <RoomTagChips roomId={roomId} showTooltip={showTooltip} />
              </div>
            )}
          </div>

          <div
            css={{
              alignSelf: 'center',
            }}
          >
            <MiniDateRoomSchedule
              loading={isTimeFinderCaseDataLoading}
              timeFinderCases={timeFinderCases}
              id={slotId}
              startTime={startTime}
              endTime={endTime}
              durationEstInMins={
                durationUsedInSlot ? durationUsedInSlot.as('minutes') : 0
              }
            />
          </div>
        </div>
      </MetricTile>
    </div>
  )
})
