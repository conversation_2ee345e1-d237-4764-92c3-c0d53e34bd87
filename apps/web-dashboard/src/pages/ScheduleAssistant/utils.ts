import { groupBy } from 'lodash'
import { Interval } from 'luxon'

import { IAvailableSlot } from './AvailableTimes/interfaces'

export const isValidTurnoverDuration = (turnoverDuration: number | null) => {
  return typeof turnoverDuration === 'number' && turnoverDuration >= 0
}

export const removeDuplicateAvailableSlots = (
  availableSlots: IAvailableSlot[]
) => {
  const slotsGroupedBySiteRoomDate = groupBy(
    availableSlots,
    (slot) => `${slot.siteId}-${slot.roomId}-${slot.date}`
  )
  const slotsToRemove: string[] = []

  for (const siteRoomDate in slotsGroupedBySiteRoomDate) {
    const slots = slotsGroupedBySiteRoomDate[siteRoomDate]
    const sortedSlots = slots.sort(
      (a, b) => b.blockTimeIds.length - a.blockTimeIds.length
    )
    for (let i = 0; i < sortedSlots.length; i++) {
      const maxSlot = sortedSlots[i]
      if (
        !slotsToRemove.includes(maxSlot.id) &&
        maxSlot.blockTimeIds.length > 1
      ) {
        const maxSlotInterval = Interval.fromDateTimes(
          maxSlot.startTime,
          maxSlot.endTime
        )
        for (let j = i; j < sortedSlots.length; j++) {
          const slot = sortedSlots[j]
          if (
            maxSlot.id !== slot.id &&
            slot.blockTimeIds.every((id) => maxSlot.blockTimeIds.includes(id))
          ) {
            const slotInterval = Interval.fromDateTimes(
              slot.startTime,
              slot.endTime
            )
            const validOverlap = maxSlotInterval.overlaps(slotInterval)
            if (validOverlap) {
              slotsToRemove.push(slot.id)
            }
          }
        }
      }
    }
  }
  return availableSlots.filter((slot) => !slotsToRemove.includes(slot.id))
}

export const percentile = (arr: number[], p: number): number => {
  const index = (p / 100) * (arr.length - 1)
  const lower = Math.floor(index)
  const upper = Math.ceil(index)
  if (lower === upper) return arr[lower]
  return arr[lower] + (arr[upper] - arr[lower]) * (index - lower)
}

/**
 * https://en.wikipedia.org/wiki/Interquartile_range
 * The interquartile range (IQR) is a measure of statistical dispersion, or variability, and is the difference between the third quartile (Q3) and the first quartile (Q1).
 * The ratio of the IQR to the median is a measure of relative variability.
 *
 * @param samples - The sampled data used to calculate the IQR to median ratio
 * @returns number - The IQR to median ratio
 */
export const calcIQRtoMedianRatio = (
  samples: number[]
): {
  ratio: number
  iqr: number
  median: number
  q1: number
  q3: number
} | null => {
  // Need at least 3 points for Q1, Q3, and median
  if (samples.length < 3) return null

  const sorted = [...samples].sort((a, b) => a - b)

  const q1 = percentile(sorted, 25)
  const q3 = percentile(sorted, 75)
  const median = percentile(sorted, 50)

  if (median === 0) {
    return null
  }

  const iqr = q3 - q1
  return { ratio: iqr / median, iqr, median, q1, q3 }
}
