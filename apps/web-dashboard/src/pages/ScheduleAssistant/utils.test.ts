import { DateTime, Duration } from 'luxon'

import { IAvailableSlot } from './AvailableTimes/interfaces'
import {
  calcIQRtoMedianRatio,
  isValidTurnoverDuration,
  percentile,
  removeDuplicateAvailableSlots,
} from './utils'

describe('isValidTurnoverDuration', () => {
  it('should return true for numbers greater than or equal to 0', () => {
    expect(isValidTurnoverDuration(5)).toBe(true) // 5 is valid
    expect(isValidTurnoverDuration(0)).toBe(true) // 0 is valid
  })

  it('should return false for undefined', () => {
    expect(isValidTurnoverDuration(null)).toBe(false) // null is invalid
  })
})

describe('removeDuplicateAvailableSlots', () => {
  const siteDateConfig = {
    date: '2025-03-12',
    siteId: 'site1',
    siteName: 'site 1',
  }
  const siteRoom1DateConfig = {
    ...siteDateConfig,
    roomId: 'room1',
    roomName: 'room 1',
  }

  const sortAndCleanAvailableSlots = (availableSlots: IAvailableSlot[]) => {
    return availableSlots
      .map((slot) => {
        return {
          ...slot,
          startTime: slot.startTime.toString(),
          endTime: slot.endTime.toString(),
          maxAvailableDuration: slot.maxAvailableDuration.toString(),
        }
      })
      .sort((a, b) =>
        a.startTime === b.startTime
          ? a.endTime.localeCompare(b.endTime)
          : a.startTime.localeCompare(b.startTime)
      )
  }
  it('should return 1 slot after removing duplicates (overlapping times)', () => {
    // room 1 --- unblocked1 --- block --- unblocked2 ----
    // expected: 1 slot [unblocked1, block, unblocked2]
    const allAvailableSlots: IAvailableSlot[] = [
      {
        ...siteRoom1DateConfig,
        id: '1',
        startTime: DateTime.local(2025, 3, 12, 10, 30),
        endTime: DateTime.local(2025, 3, 12, 11, 30),
        maxAvailableDuration: Duration.fromISO('PT1H'),
        blockTimeIds: ['unblocked1'],
      },
      {
        ...siteRoom1DateConfig,
        id: '2',
        startTime: DateTime.local(2025, 3, 12, 11, 30),
        endTime: DateTime.local(2025, 3, 12, 13, 0),
        maxAvailableDuration: Duration.fromISO('PT1H30M'),
        blockTimeIds: ['block'],
      },
      {
        ...siteRoom1DateConfig,
        id: '3',
        startTime: DateTime.local(2025, 3, 12, 13, 0),
        endTime: DateTime.local(2025, 3, 12, 19, 30),
        maxAvailableDuration: Duration.fromISO('PT5H30M'),
        blockTimeIds: ['unblocked2'],
      },
      {
        ...siteRoom1DateConfig,
        id: '4',
        startTime: DateTime.local(2025, 3, 12, 10, 30),
        endTime: DateTime.local(2025, 3, 12, 13, 0),
        maxAvailableDuration: Duration.fromISO('PT2H30M'),
        blockTimeIds: ['unblocked1', 'block'],
      },
      {
        ...siteRoom1DateConfig,
        id: '5',
        startTime: DateTime.local(2025, 3, 12, 13, 0),
        endTime: DateTime.local(2025, 3, 12, 19, 30),
        maxAvailableDuration: Duration.fromISO('PT5H30M'),
        blockTimeIds: ['block', 'unblocked2'],
      },
      {
        ...siteRoom1DateConfig,
        id: '6',
        startTime: DateTime.local(2025, 3, 12, 10, 30),
        endTime: DateTime.local(2025, 3, 12, 19, 30),
        maxAvailableDuration: Duration.fromISO('PT9H'),
        blockTimeIds: ['unblocked1', 'block', 'unblocked2'],
      },
    ]

    const expected = sortAndCleanAvailableSlots([
      {
        ...siteRoom1DateConfig,
        id: '6',
        startTime: DateTime.local(2025, 3, 12, 10, 30),
        endTime: DateTime.local(2025, 3, 12, 19, 30),
        maxAvailableDuration: Duration.fromISO('PT9H'),
        blockTimeIds: ['unblocked1', 'block', 'unblocked2'],
      },
    ])

    const actual = removeDuplicateAvailableSlots(allAvailableSlots)
    expect(sortAndCleanAvailableSlots(actual)).toEqual(expected)
  })
  it('should return 2 slots after removing duplicates (overlapping times)', () => {
    // room 1 --- unblocked1 --- block1 ---
    // room 1 --- block2 ---
    // expected: 2 slots [unblocked1, block1] [block2]
    const allAvailableSlots: IAvailableSlot[] = [
      {
        ...siteRoom1DateConfig,
        id: '1',
        startTime: DateTime.local(2025, 3, 12, 10, 30),
        endTime: DateTime.local(2025, 3, 12, 11, 30),
        maxAvailableDuration: Duration.fromISO('PT1H'),
        blockTimeIds: ['unblocked1'],
      },
      {
        ...siteRoom1DateConfig,
        id: '2',
        startTime: DateTime.local(2025, 3, 12, 11, 30),
        endTime: DateTime.local(2025, 3, 12, 13, 0),
        maxAvailableDuration: Duration.fromISO('PT1H30M'),
        blockTimeIds: ['block'],
      },
      {
        ...siteRoom1DateConfig,
        id: '3',
        startTime: DateTime.local(2025, 3, 12, 10, 30),
        endTime: DateTime.local(2025, 3, 12, 13, 0),
        maxAvailableDuration: Duration.fromISO('PT2H30M'),
        blockTimeIds: ['unblocked1', 'block'],
      },
      {
        ...siteRoom1DateConfig,
        id: '4',
        startTime: DateTime.local(2025, 3, 12, 14, 0),
        endTime: DateTime.local(2025, 3, 12, 19, 30),
        maxAvailableDuration: Duration.fromISO('PT4H30M'),
        blockTimeIds: ['block2'],
      },
    ]
    const expected = sortAndCleanAvailableSlots([
      {
        ...siteRoom1DateConfig,
        id: '3',
        startTime: DateTime.local(2025, 3, 12, 10, 30),
        endTime: DateTime.local(2025, 3, 12, 13, 0),
        maxAvailableDuration: Duration.fromISO('PT2H30M'),
        blockTimeIds: ['unblocked1', 'block'],
      },
      {
        ...siteRoom1DateConfig,
        id: '4',
        startTime: DateTime.local(2025, 3, 12, 14, 0),
        endTime: DateTime.local(2025, 3, 12, 19, 30),
        maxAvailableDuration: Duration.fromISO('PT4H30M'),
        blockTimeIds: ['block2'],
      },
    ])

    const actual = removeDuplicateAvailableSlots(allAvailableSlots)
    expect(sortAndCleanAvailableSlots(actual)).toEqual(expected)
  })
  it('should return all inputed slots since each slot has a different room', () => {
    // room 1 --- block1 ---
    // room 2 --- block2 ---
    // expected: 2 slots [block1] [block2]
    const allAvailableSlots: IAvailableSlot[] = [
      {
        ...siteRoom1DateConfig,
        id: '1',
        startTime: DateTime.local(2025, 3, 12, 10, 30),
        endTime: DateTime.local(2025, 3, 12, 11, 30),
        maxAvailableDuration: Duration.fromISO('PT1H'),
        blockTimeIds: ['block1'],
      },
      {
        ...siteDateConfig,
        roomId: 'room2',
        roomName: 'room 2',
        id: '2',
        startTime: DateTime.local(2025, 3, 12, 11, 30),
        endTime: DateTime.local(2025, 3, 12, 13, 0),
        maxAvailableDuration: Duration.fromISO('PT1H30M'),
        blockTimeIds: ['block2'],
      },
    ]
    const expected = sortAndCleanAvailableSlots([
      {
        ...siteRoom1DateConfig,
        id: '1',
        startTime: DateTime.local(2025, 3, 12, 10, 30),
        endTime: DateTime.local(2025, 3, 12, 11, 30),
        maxAvailableDuration: Duration.fromISO('PT1H'),
        blockTimeIds: ['block1'],
      },
      {
        ...siteDateConfig,
        roomId: 'room2',
        roomName: 'room 2',
        id: '2',
        startTime: DateTime.local(2025, 3, 12, 11, 30),
        endTime: DateTime.local(2025, 3, 12, 13, 0),
        maxAvailableDuration: Duration.fromISO('PT1H30M'),
        blockTimeIds: ['block2'],
      },
    ])

    const actual = removeDuplicateAvailableSlots(allAvailableSlots)
    expect(sortAndCleanAvailableSlots(actual)).toEqual(expected)
  })
})

describe('percentile', () => {
  it('should return the correct percentile value', () => {
    const arr = [1, 2, 3, 4, 5]
    expect(percentile(arr, 50)).toBe(3)
    expect(percentile(arr, 0)).toBe(1)
    expect(percentile(arr, 100)).toBe(5)
    expect(percentile(arr, 25)).toBe(2)
    expect(percentile(arr, 75)).toBe(4)
  })
})

describe('calcIQRtoMedianRatio', () => {
  it('should return the correct IQR to median ratio', () => {
    const data = [7, 10, 12]
    const result = calcIQRtoMedianRatio(data)
    expect(result).toEqual({
      ratio: 0.25,
      iqr: 2.5,
      median: 10,
      q1: 8.5,
      q3: 11,
    })
  })

  it('should return the correct IQR to median ratio for high variance', () => {
    const data = [0, 10, 20]
    const result = calcIQRtoMedianRatio(data)
    expect(result).toEqual({
      ratio: 1,
      iqr: 10,
      median: 10,
      q1: 5,
      q3: 15,
    })
  })

  it('should return null for an array with less than 3 elements', () => {
    const data: number[] = [1, 10]
    const result = calcIQRtoMedianRatio(data)
    expect(result).toBeNull()
  })

  it('should return null when the median is 0', () => {
    const data = [0, 0, 0]
    const result = calcIQRtoMedianRatio(data)
    expect(result).toBeNull()
  })
})
