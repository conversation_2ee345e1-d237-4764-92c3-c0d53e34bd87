import { ApolloClient, NormalizedCacheObject } from '@apollo/client'
import { keyBy, uniqBy } from 'lodash'

import {
  GetCaseDurationSurgeonsAndProcedures,
  GetCaseDurationSurgeonsAndProceduresVariables,
} from './__generated__'
import { GET_CASE_DURATION_SURGEONS_AND_PROCEDURES } from './queries'

export const fetchSurgeonProcedures = async (
  client: ApolloClient<NormalizedCacheObject>,
  { selectedSurgeonId }: { selectedSurgeonId: string }
) => {
  const { data } = await client.query<
    GetCaseDurationSurgeonsAndProcedures,
    GetCaseDurationSurgeonsAndProceduresVariables
  >({ query: GET_CASE_DURATION_SURGEONS_AND_PROCEDURES })
  const caseDurations =
    data?.caseDurationSurgeonsAndProcedures.edges?.map(({ node }) => ({
      surgeonId: node.surgeonId,
      surgeon: node.surgeon,
      procedure: node.procedure,
    })) || []

  const allSurgeons = uniqBy(
    caseDurations.map((entry) => ({
      id: entry.surgeonId,
      fullName: entry.surgeon,
    })),
    'id'
  ).sort((a, b) => a.fullName.localeCompare(b.fullName))

  const surgeonIdToSurgeonRecordMap = new Map(
    Object.entries(keyBy(allSurgeons, 'id'))
  )
  const allProcedures = caseDurations.sort((a, b) =>
    a.procedure.localeCompare(b.procedure)
  )

  const selectedSurgeon =
    surgeonIdToSurgeonRecordMap.get(selectedSurgeonId)?.fullName ?? ''

  return {
    selectedSurgeon,
    allSurgeons,
    allProcedures,
    surgeonIdToSurgeonRecordMap,
  }
}
