import React, { PropsWithChildren } from 'react'
import { useMatch } from 'react-router'

import { useTheme } from '@emotion/react'

import {
  remSpacing,
  shape,
  Caps3Bold,
  H4,
  H2,
  Info,
  Tooltip,
  H5,
  Caps2Bold,
  H3,
  ButtonLink,
  mediaQueries,
  P3,
} from '@apella/component-library'
import { WarningMessage } from 'src/components/WarningMessage'
import { LocationPath } from 'src/router/types'

const TOTAL_DURATION_DESCRIPTION =
  'Case duration including turnover time, generated by Apella’s predictive model'
const PROCEDURE_DURATION_DESCRIPTION =
  'Wheels-in to wheels-out duration, generated by Apella’s predictive model'
const TURNOVER_DURATION_DESCRIPTION =
  'Turnover time generated by Apella’s predictive model'

const MAX_FULL_ESTIMATE_BOX_WIDTH = '900px'

export const FullEstimate = ({
  procedureEstimate = 0,
  turnoverEstimate = 0,
  procedure,
  additionalProcedures,
  surgeon,
  highVariability,
}: {
  procedureEstimate?: number | null
  turnoverEstimate?: number | null
  procedure?: string
  additionalProcedures?: string[]
  surgeon?: string
  highVariability: boolean
}) => {
  const theme = useTheme()

  return (
    <div
      css={{
        maxWidth: MAX_FULL_ESTIMATE_BOX_WIDTH,
        margin: '0 auto',
      }}
    >
      <div
        css={{
          alignItems: 'flex-start',
          flexDirection: 'column',
          display: 'flex',
          backgroundColor: theme.palette.blue.background,
          padding: `${remSpacing.medium} ${remSpacing.gutter}`,
          borderRadius: shape.borderRadius.xsmall,
          textAlign: 'left',
        }}
      >
        <H5
          css={{
            color: theme.palette.blue[70],
            marginBottom: remSpacing.xsmall,
          }}
        >
          {surgeon}
        </H5>
        {!!procedure && (
          <H4
            css={{
              color: theme.palette.blue[70],
              marginBottom: remSpacing.xsmall,
            }}
          >
            <ProcedureText
              procedure={procedure}
              additionalProcedures={additionalProcedures}
            />
          </H4>
        )}
        <hr
          css={{ width: '100%', border: `1px solid ${theme.palette.blue[20]}` }}
        />
        <div
          css={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-end',
            marginTop: remSpacing.small,
          }}
        >
          <FullEstimateTile
            estimate={(procedureEstimate ?? 0) + (turnoverEstimate ?? 0)}
            tooltipText={TOTAL_DURATION_DESCRIPTION}
          >
            Total Duration
          </FullEstimateTile>
          <FullEstimateTile
            estimate={procedureEstimate ?? 0}
            tooltipText={PROCEDURE_DURATION_DESCRIPTION}
            estimateTextSize="small"
          >
            {additionalProcedures?.length ? 'Procedures' : 'Procedure'}
          </FullEstimateTile>
          <FullEstimateTile
            estimate={turnoverEstimate ?? 0}
            tooltipText={TURNOVER_DURATION_DESCRIPTION}
            estimateTextSize="small"
          >
            Turnover
          </FullEstimateTile>
        </div>
      </div>
      {highVariability && <HighVariabilityWarning />}
    </div>
  )
}

export const CompactEstimate = ({
  procedureEstimate,
  turnoverEstimate,
  procedure,
  additionalProcedures,
  surgeon,
  recentCasesTo,
  highVariability,
}: {
  procedureEstimate?: number | null
  turnoverEstimate?: number | null
  procedure?: string
  additionalProcedures?: string[]
  surgeon?: string
  recentCasesTo: string
  highVariability: boolean
}) => {
  const theme = useTheme()

  return (
    <>
      <div
        css={{
          padding: `${remSpacing.small} ${remSpacing.gutter}`,
          display: 'flex',
          alignItems: 'center',
          backgroundColor: theme.palette.blue.background,
          borderRadius: shape.borderRadius.xsmall,
          width: '100%',
        }}
      >
        <div
          css={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <H4
            css={{
              color: theme.palette.blue[70],
            }}
          >
            {surgeon}
          </H4>
          {!!procedure && (
            <H5
              css={{
                color: theme.palette.blue[70],
                textAlign: 'left',
              }}
            >
              <ProcedureText
                procedure={procedure}
                additionalProcedures={additionalProcedures}
              />
            </H5>
          )}
        </div>
        <VerticleDivider />
        <div
          css={{
            display: 'flex',
            alignItems: 'flex-start',
            flex: 1,
          }}
        >
          <CompactEstimateTile
            estimate={(procedureEstimate ?? 0) + (turnoverEstimate ?? 0)}
            tooltipText={TOTAL_DURATION_DESCRIPTION}
          >
            Total Duration
          </CompactEstimateTile>
          <CompactEstimateTile
            estimate={procedureEstimate ?? 0}
            tooltipText={PROCEDURE_DURATION_DESCRIPTION}
            estimateTextSize="small"
          >
            {additionalProcedures?.length ? 'Procedures' : 'Procedure'}
          </CompactEstimateTile>
          <CompactEstimateTile
            estimate={turnoverEstimate ?? 0}
            tooltipText={TURNOVER_DURATION_DESCRIPTION}
            estimateTextSize="small"
          >
            Turnover
          </CompactEstimateTile>
        </div>
        <ButtonLink
          css={{ whiteSpace: 'nowrap' }}
          appearance="link"
          to={recentCasesTo}
        >
          View related cases
        </ButtonLink>
      </div>
      {highVariability && <HighVariabilityWarning />}
    </>
  )
}

const VerticleDivider = () => {
  const theme = useTheme()

  return (
    <div
      css={{
        borderLeft: `1px solid ${theme.palette.blue[20]}`,
        alignSelf: 'stretch',
        margin: `0 ${remSpacing.large}`,
      }}
    />
  )
}

const InfoTooltip = ({
  tooltipText,
  infoColor = 'inherit',
}: {
  tooltipText: string
  infoColor?: string
}) => {
  return (
    <Tooltip
      body={<P3>{tooltipText}</P3>}
      placement="bottom-start"
      css={{ padding: remSpacing.xsmall }}
    >
      <Info
        css={{
          color: infoColor,
        }}
        size="xs"
      />
    </Tooltip>
  )
}

type EstimateSize = 'large' | 'small'

const FULL_ESTIMATE_TEXT_SIZE: {
  [Property in EstimateSize]: React.ElementType
} = {
  large: H2,
  small: H3,
}

const FULL_ESTIMATE_HEADER_MARGIN_SIZE: {
  [Property in EstimateSize]: string
} = {
  large: '0',
  small: remSpacing.xxsmall,
}
const FullEstimateTile = ({
  estimate,
  children,
  tooltipText,
  estimateTextSize = 'large',
}: PropsWithChildren<{
  estimate: number
  tooltipText: string
  estimateTextSize?: EstimateSize
}>) => {
  const theme = useTheme()

  const EstimateHeader = FULL_ESTIMATE_TEXT_SIZE[estimateTextSize]

  return (
    <div
      css={{
        marginRight: remSpacing.xlarge,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
      }}
    >
      <EstimateHeader
        css={{
          color: theme.palette.blue[70],
          marginBottom: FULL_ESTIMATE_HEADER_MARGIN_SIZE[estimateTextSize],
        }}
      >
        {estimate}m
      </EstimateHeader>
      <div
        css={{
          display: 'flex',
          alignItems: 'flex-start',
        }}
      >
        <Caps3Bold
          css={{
            marginRight: remSpacing.xxsmall,
            display: 'flex',
            color: theme.palette.blue[70],
          }}
        >
          {children}
        </Caps3Bold>
        <InfoTooltip
          infoColor={theme.palette.blue[70]}
          tooltipText={tooltipText}
        />
      </div>
    </div>
  )
}

const COMPACT_ESTIMATE_TEXT_SIZE: {
  [Property in EstimateSize]: React.ElementType
} = {
  large: H3,
  small: H4,
}

const COMPACT_ESTIMATE_HEADER_MARGIN_SIZE: {
  [Property in EstimateSize]: string
} = {
  large: '0',
  small: remSpacing.xxsmall,
}
const CompactEstimateTile = ({
  estimate,
  children,
  tooltipText,
  estimateTextSize = 'large',
}: PropsWithChildren<{
  estimate: number
  tooltipText: string
  estimateTextSize?: EstimateSize
}>) => {
  const theme = useTheme()

  const EstimateHeader = COMPACT_ESTIMATE_TEXT_SIZE[estimateTextSize]

  return (
    <div
      css={{
        display: 'flex',
        flexDirection: 'column',
        marginRight: remSpacing.large,
        textAlign: 'left',
        [mediaQueries.xl]: {
          marginRight: remSpacing.xxxlarge,
        },
      }}
    >
      <EstimateHeader
        css={{
          color: theme.palette.blue[70],
          marginBottom: COMPACT_ESTIMATE_HEADER_MARGIN_SIZE[estimateTextSize],
        }}
      >
        {estimate}m
      </EstimateHeader>
      <div
        css={{
          display: 'flex',
          flex: '0 0 0',
          alignItems: 'flex-end',
        }}
      >
        <Caps2Bold
          css={{
            marginRight: remSpacing.xxsmall,
            marginBottom: '.1rem',
            display: 'flex',
            alignItems: 'flex-end',
            color: theme.palette.blue[70],
            whiteSpace: 'nowrap',
          }}
        >
          {children}
        </Caps2Bold>
        <InfoTooltip
          infoColor={theme.palette.blue[70]}
          tooltipText={tooltipText}
        />
      </div>
    </div>
  )
}

type ProcedureTextProps = {
  procedure: string
  additionalProcedures?: string[]
}

const ProcedureText = ({
  procedure,
  additionalProcedures,
}: ProcedureTextProps) => {
  return (
    <>
      {procedure}
      {additionalProcedures &&
        additionalProcedures.length > 0 &&
        additionalProcedures.map((additionalProcedure, index) => (
          <React.Fragment key={index}>
            &nbsp;+
            <br />
            {additionalProcedure}
          </React.Fragment>
        ))}
    </>
  )
}

const HighVariabilityWarning = () => {
  const match = useMatch('/schedule-assistant/:viewId/*')
  const viewId = match?.params?.viewId

  return (
    <WarningMessage
      css={{
        marginTop: remSpacing.medium,
        flexDirection:
          viewId === LocationPath.AvailableTimes ? 'row' : 'column',
      }}
    >
      <P3 css={{ fontWeight: 'bold' }}>
        Prediction is highly variable for this procedure.
      </P3>
      <P3>
        Please review case details before scheduling.
        {viewId === LocationPath.AvailableTimes &&
          ' Use the custom duration option above if you need to make an update.'}
      </P3>
    </WarningMessage>
  )
}
