import { stringifyParams } from '@apella/hooks'

import {
  DURATION_SOURCE_FILTER,
  DurationSource,
  PROCEDURE_FILTER,
  SURGEON_FILTER,
  SURGEON_NAME_FILTER,
} from '../useScheduleAssistantSurgeonProcedureFilters'
import { loader } from './RecentCases'

describe('RecentCases', () => {
  const params = {}
  const mockCubeClient = {
    load: vi
      .fn()
      .mockResolvedValue({ data: { caseDurationSurgeonsAndProcedures: {} } }),
  }

  describe('loader', () => {
    it('redirects to prediction duration source if navigation to case-duration with a custom duration source selected', async () => {
      const extraParams = {
        [SURGEON_NAME_FILTER]: 'Surgeon Name',
        [SURGEON_FILTER]: '123',
      }
      const initialUrl =
        'https://example.com/schedule-assistant/case-duration?' +
        stringifyParams({
          ...extraParams,
          [DURATION_SOURCE_FILTER]: DurationSource.Custom,
        })
      const request = new Request(initialUrl)

      try {
        const context = { cube: mockCubeClient }
        await loader({ request, params, context }, context)
      } catch (error) {
        const thrown = error as Response
        expect(thrown).toBeInstanceOf(Response)
        expect(thrown.status).toBe(302)
        const expectedPathName =
          '/schedule-assistant/case-duration?' +
          stringifyParams({
            ...extraParams,
            [DURATION_SOURCE_FILTER]: DurationSource.Prediction,
          })
        expect(thrown.headers.get('Location')).toBe(expectedPathName)
      }
    })

    it('returns null recentCases if no surgeon is selected', async () => {
      const request = new Request(
        `https://example.com/schedule-assistant/case-duration?${PROCEDURE_FILTER}=123`
      )
      const context = { cube: mockCubeClient }
      const { recentCases } = await loader(
        { request, params, context },
        context
      )
      expect(recentCases).toBeNull()
    })

    it('returns null recentCases if no procedure is selected', async () => {
      const request = new Request(
        `https://example.com/schedule-assistant/case-duration?${SURGEON_FILTER}=123&${SURGEON_NAME_FILTER}=LAST, FIRST`
      )
      const context = { cube: mockCubeClient }
      const { recentCases } = await loader(
        { request, params, context },
        context
      )
      expect(recentCases).toBeNull()
    })
  })
})
