import { PropsWithChildren } from 'react'
import {
  LoaderFunctionArgs,
  Outlet,
  replace,
  useLoaderData,
} from 'react-router'

import { useTheme } from '@emotion/react'

import { Property } from 'csstype'
import { isNumber, isString } from 'lodash'
import { DateTime } from 'luxon'

import {
  H6,
  HTMLTH,
  TBody,
  TD,
  THead,
  TR,
  Table,
  ApellaDateTimeFormats,
  remSpacing,
  Tooltip,
  Info,
  P3,
  H5,
  Duration,
} from '@apella/component-library'
import { parseParams } from '@apella/hooks'
import { CaseType } from 'src/__generated__/globalTypes'
import { DashComponent } from 'src/components/DashComponent'
import { EmptyState } from 'src/components/EmptyState'
import { useTimezone } from 'src/Contexts'
import { FORECASTING_REPORT_DATA_USED_IN_TRAINING } from 'src/modules/cube/types/dataCubes'
import { Description, DisplayName } from 'src/pages/Insights/types'
import { getRoute<PERSON>ontext, LocationPath } from 'src/router/types'
import { EVENTS } from 'src/utils/analyticsEvents'
import orgLocalStorage from 'src/utils/orgLocalStorage'
import { useOpenVideoBlade } from 'src/utils/useOpenVideoBlade'

import { CASE_DURATION_PAGE_POLL_INTERVAL } from '../AvailableTimes/constants'
import { createRecentCasesCacheKey } from '../EstimateLayout'
import { CubeQueryResultType, RecentCasesCacheEntry } from '../types'
import {
  castParams,
  DURATION_SOURCE_FILTER,
  DurationSource,
} from '../useScheduleAssistantSurgeonProcedureFilters'

export const clearRecentCasesCache = () => {
  RECENT_CASES_CACHE.clear()
}

const RECENT_CASES_CACHE = new Map<string, RecentCasesCacheEntry>()

export const loader = async (
  { request }: LoaderFunctionArgs,
  context: unknown
) => {
  const { cube } = getRouteContext(context)
  const requestURL = new URL(request.url)
  const params = parseParams(requestURL.search)
  const {
    selectedSurgeonName,
    selectedProcedure,
    selectedAdditionalProcedures,
    durationSource,
  } = castParams(params)

  orgLocalStorage.setItem('scheduleAssistantLastTab', LocationPath.CaseDuration)

  // Reset duration source to predicted if navigating to case duration view in custom duration mode
  if (durationSource === DurationSource.Custom) {
    throw replace(
      requestURL.pathname +
        requestURL.search.replaceAll(
          `${DURATION_SOURCE_FILTER}=${DurationSource.Custom}`,
          `${DURATION_SOURCE_FILTER}=${DurationSource.Prediction}`
        )
    )
  }

  if (!selectedProcedure || !selectedSurgeonName) {
    return { recentCases: null }
  }

  const allProcedures = [selectedProcedure, ...selectedAdditionalProcedures]
  const cacheKey = createRecentCasesCacheKey({
    surgeon: selectedSurgeonName,
    procedure: allProcedures.join('+'),
  })
  const cacheEntry = RECENT_CASES_CACHE.get(cacheKey)
  if (cacheEntry && cacheEntry?.expire > Date.now()) {
    return { recentCases: cacheEntry.data }
  }

  const resultSet = await cube?.load({
    dimensions: [
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM_ID,
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.PROCEDURE_LIST,
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.FIRST_PRIMARY_SURGEON,
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.SURGEON_LIST,
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.CASE_ID,
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.APELLA_CASE_ID,
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.SITE,
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM,
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.START_TIME_LOCAL,
      FORECASTING_REPORT_DATA_USED_IN_TRAINING.DURATION,
    ],
    limit: 20,
    order: [
      [FORECASTING_REPORT_DATA_USED_IN_TRAINING.START_TIME_LOCAL, 'desc'],
    ],
    filters: [
      {
        member: FORECASTING_REPORT_DATA_USED_IN_TRAINING.PROCEDURE_LIST,
        operator: 'contains',
        values: allProcedures,
      },
      {
        member: FORECASTING_REPORT_DATA_USED_IN_TRAINING.FIRST_PRIMARY_SURGEON,
        operator: 'equals',
        values: [selectedSurgeonName],
      },
    ],
  })

  const recentCases = resultSet?.rawData()?.map(narrowRow) || null

  if (!recentCases) {
    return { recentCases: null }
  }

  RECENT_CASES_CACHE.set(cacheKey, {
    data: recentCases,
    expire: Date.now() + CASE_DURATION_PAGE_POLL_INTERVAL,
  })

  return { recentCases }
}

export const RecentCases = () => {
  const { recentCases } = useLoaderData<Awaited<ReturnType<typeof loader>>>()

  return <RecentCasesTable recentCases={recentCases} />
}

const RecentCasesTable = ({
  recentCases,
}: {
  recentCases: CubeQueryResultType[] | null
}) => {
  const handleOpenVideoBlade = useOpenVideoBlade({ appendParams: true })
  const { timezone } = useTimezone()

  if (recentCases === null) {
    return null
  }

  return (
    <div css={{ marginTop: remSpacing.gutter }}>
      {recentCases?.length ? (
        <div
          css={{
            display: 'flex',
            flexDirection: 'column',
            flexWrap: 'wrap',
            width: '100%',
          }}
        >
          <TableLayout>
            {recentCases?.map((row) => {
              const actualDuration =
                row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.DURATION]

              const procedures: string[] = JSON.parse(
                row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.PROCEDURE_LIST] ??
                  '[]'
              )

              const primarySurgeon: string = (
                row[
                  FORECASTING_REPORT_DATA_USED_IN_TRAINING.FIRST_PRIMARY_SURGEON
                ] ?? ''
              ).toUpperCase()

              const surgeons: { first_name: string; last_name: string }[] =
                JSON.parse(
                  row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.SURGEON_LIST] ??
                    '[]'
                )

              const surgeonsNamesCombined: string[] = surgeons.map(
                (s) =>
                  s.last_name.toUpperCase() + ', ' + s.first_name.toUpperCase()
              )

              const primarySurgeonIndex =
                surgeonsNamesCombined.indexOf(primarySurgeon)

              // If the primary surgeon is not first in the list, move them to the front.
              if (primarySurgeonIndex > 0) {
                surgeonsNamesCombined.splice(primarySurgeonIndex, 1)
                surgeonsNamesCombined.unshift(primarySurgeon)
              }
              const apellaCaseId =
                row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.APELLA_CASE_ID]

              return (
                <TR
                  css={{ cursor: apellaCaseId ? 'pointer' : 'default' }}
                  onClick={() => {
                    if (
                      !row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM_ID] ||
                      !row[
                        FORECASTING_REPORT_DATA_USED_IN_TRAINING
                          .START_TIME_LOCAL
                      ] ||
                      !apellaCaseId
                    ) {
                      return
                    }
                    handleOpenVideoBlade(
                      row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM_ID],
                      {
                        apellaCase: {
                          id: `case:${apellaCaseId}`,
                          startTime: DateTime.fromISO(
                            row[
                              FORECASTING_REPORT_DATA_USED_IN_TRAINING
                                .START_TIME_LOCAL
                            ],
                            { zone: timezone }
                          ),
                          endTime: DateTime.fromISO(
                            row[
                              FORECASTING_REPORT_DATA_USED_IN_TRAINING
                                .START_TIME_LOCAL
                            ],
                            { zone: timezone }
                          ).plus({ minutes: actualDuration ?? 0 }),
                          type: CaseType.COMPLETE,
                        },
                        analyticsEvent:
                          EVENTS.OPEN_CASE_DURATION_RECENT_CASES_VIDEO_BLADE,
                      }
                    )
                  }}
                  key={`${row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.CASE_ID]}`}
                >
                  <TD>
                    {row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.CASE_ID]}
                  </TD>
                  <TD css={{ textTransform: 'uppercase' }}>
                    {procedures.sort().map((procedure, index) => (
                      <span key={procedure}>
                        {procedure}
                        {index < procedures.length - 1 && ' + '}
                      </span>
                    ))}
                  </TD>
                  <TD css={{ textTransform: 'uppercase' }}>
                    {surgeonsNamesCombined.join('; ')}
                  </TD>
                  <TD>{row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.SITE]}</TD>
                  <TD>{row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM]}</TD>
                  <TD>
                    {row[
                      FORECASTING_REPORT_DATA_USED_IN_TRAINING.START_TIME_LOCAL
                    ] ? (
                      DateTime.fromISO(
                        row[
                          FORECASTING_REPORT_DATA_USED_IN_TRAINING
                            .START_TIME_LOCAL
                        ]
                      ).toLocaleString(
                        ApellaDateTimeFormats.DATETIME_WITH_WEEKDAY
                      )
                    ) : (
                      <DashComponent />
                    )}
                  </TD>
                  <TD css={{ textAlign: 'center' }}>
                    {actualDuration ? `${actualDuration}m` : <DashComponent />}
                  </TD>
                </TR>
              )
            })}
          </TableLayout>
          <Outlet />
        </div>
      ) : (
        <EmptyState
          message="No related cases"
          subtext="There are no cases available to view or you may not have permission"
          Icon={Duration}
        />
      )}
    </div>
  )
}

const TableLayout = ({ children }: PropsWithChildren) => {
  return (
    <>
      <H5 css={{ textAlign: 'left' }}>Related Cases</H5>
      <Table css={{ textAlign: 'left' }}>
        <THead>
          <TR>
            <ColumnHeader>Case ID</ColumnHeader>
            <ColumnHeader>Procedure(s)</ColumnHeader>
            <ColumnHeader>Surgeon(s)</ColumnHeader>
            <ColumnHeader>Site</ColumnHeader>
            <ColumnHeader>Room</ColumnHeader>
            <ColumnHeader>Start Time</ColumnHeader>
            <ColumnHeader
              justifyContent="center"
              description={Description.ACTUAL_DURATION}
            >
              {DisplayName.ACTUAL_DURATION}
            </ColumnHeader>
          </TR>
        </THead>
        <TBody>{children}</TBody>
      </Table>
    </>
  )
}

const ColumnHeader = ({
  justifyContent = 'inherit',
  children,
  description,
}: {
  justifyContent?: Property.TextAlign
  children: React.ReactNode
  description?: string
}) => {
  const theme = useTheme()
  return (
    <HTMLTH>
      <div
        css={{ display: 'flex', gap: remSpacing.small }}
        style={{ justifyContent: justifyContent }}
      >
        <H6 css={{}}>{children}</H6>
        {description && (
          <Tooltip body={<P3>{description}</P3>}>
            <Info
              css={{
                color: theme.palette.gray[50],
              }}
              size="sm"
            />
          </Tooltip>
        )}
      </div>
    </HTMLTH>
  )
}

const getValueOrDefault = <T,>(
  value: unknown,
  defaultVal: T,
  typeGuard: (value: unknown) => value is T
): T => {
  return typeGuard(value) ? value : defaultVal
}

const narrowRow = (row: Record<string, unknown>): CubeQueryResultType => ({
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM_ID]: getValueOrDefault(
    row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM_ID],
    '',
    isString
  ),
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.PROCEDURE_LIST]: getValueOrDefault(
    row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.PROCEDURE_LIST],
    '[]',
    isString
  ),
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.FIRST_PRIMARY_SURGEON]:
    getValueOrDefault(
      row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.FIRST_PRIMARY_SURGEON],
      '',
      isString
    ),
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.SURGEON_LIST]: getValueOrDefault(
    row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.SURGEON_LIST],
    '[]',
    isString
  ),
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.CASE_ID]: getValueOrDefault(
    row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.CASE_ID],
    '',
    isString
  ),
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.APELLA_CASE_ID]: getValueOrDefault(
    row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.APELLA_CASE_ID],
    '',
    isString
  ),
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.SITE]: getValueOrDefault(
    row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.SITE],
    '',
    isString
  ),
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM]: getValueOrDefault(
    row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM],
    '',
    isString
  ),
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.START_TIME_LOCAL]:
    getValueOrDefault(
      row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.START_TIME_LOCAL],
      '',
      isString
    ),
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.DURATION]: getValueOrDefault(
    row[FORECASTING_REPORT_DATA_USED_IN_TRAINING.DURATION],
    0,
    isNumber
  ),
})
