import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'
import { rem } from 'polished'

import {
  Caps2,
  Chip,
  P3,
  remSpacing,
  shape,
  Tooltip,
  Union,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'

import { useBlockColors } from '../BlockManagement/useBlockColors'
import { getSlotTimeRange } from './AvailableTimes/utils'
import { BlockAttributes, BlockTimeAttributes } from './interfaces'

export const BlockChips = ({
  blockTimes,
  showTooltip = false,
}: {
  blockTimes: BlockTimeAttributes[]
  showTooltip?: boolean
}) => {
  const theme = useTheme()
  const { timezone } = useTimezone()

  const multipleBlockTimesFound = blockTimes.length > 1
  const blockTooltipMiddleware = [Tooltip.middleware.offset(5)]
  const blockTooltipBody = (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.xxsmall,
        flexDirection: 'column',
      }}
    >
      <div
        css={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: remSpacing.xxsmall,
        }}
      >
        <Union size="xs" color={theme.palette.gray[60]} />
        <P3>Block</P3>
      </div>
      {...blockTimes.map(({ startTime, endTime, block }) => (
        <BlockChipTooltipRow
          key={block.id}
          startTime={DateTime.fromISO(startTime, { zone: timezone })}
          endTime={DateTime.fromISO(endTime, { zone: timezone })}
          color={block.color}
          name={block.name}
          multipleRowsExist={multipleBlockTimesFound}
        />
      ))}
    </div>
  )
  const uniqueBlocks = blockTimes.reduce((unique, blockTime) => {
    const block = blockTime.block
    if (!unique.find((b) => b.id === block.id)) {
      unique.push(block)
    }
    return unique
  }, [] as BlockAttributes[])

  const baseBlockChips = (
    <>
      {...uniqueBlocks.map((block) => (
        <BlockChip key={block.id} name={block.name} color={block.color} />
      ))}
    </>
  )

  return showTooltip ? (
    <Tooltip
      body={blockTooltipBody}
      middleware={blockTooltipMiddleware}
      additionalCss={{
        padding: `${remSpacing.xxsmall} ${remSpacing.xsmall}`,
      }}
    >
      <div
        css={{
          display: 'flex',
          gap: remSpacing.small,
          alignItems: 'center',
        }}
      >
        {baseBlockChips}
      </div>
    </Tooltip>
  ) : (
    baseBlockChips
  )
}

const BlockChip = ({
  name,
  color,
}: Pick<BlockAttributes, 'name' | 'color'>) => {
  const theme = useTheme()
  const blockColor = useBlockColors(color)

  return (
    <Chip
      color={{
        textColor: theme.palette.text.primary,
        backgroundColor: `${blockColor.borderColor}70`,
      }}
      label={name}
      size="sm"
    />
  )
}

const BlockChipTooltipRow = ({
  startTime,
  endTime,
  color,
  name,
  multipleRowsExist = false,
}: {
  startTime: DateTime
  endTime: DateTime
  color: string
  name: string
  multipleRowsExist?: boolean
}) => {
  const theme = useTheme()
  const colors = useBlockColors(color)
  return (
    <div
      css={{
        background: theme.palette.gray[10],
        padding: remSpacing.xsmall,
        alignItems: 'center',
        justifyContent: 'space-between',
        display: 'flex',
        width: multipleRowsExist ? rem('227px') : undefined,
      }}
    >
      {multipleRowsExist && (
        <div
          css={{
            display: 'flex',
            alignItems: 'center',
            gap: remSpacing.xxsmall,
          }}
        >
          <div
            css={{
              width: 16,
              height: 16,
              border: `1px solid ${colors.borderColor}`,
              borderRadius: shape.borderRadius.xxsmall,
              background: colors.backgroundColor,
            }}
          />
          <Caps2>{name}</Caps2>
        </div>
      )}
      <P3>{getSlotTimeRange(startTime, endTime)}</P3>
    </div>
  )
}
