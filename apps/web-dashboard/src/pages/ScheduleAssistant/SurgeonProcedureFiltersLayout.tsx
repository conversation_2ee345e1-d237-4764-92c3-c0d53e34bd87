import { ComponentProps, Suspense } from 'react'
import Skeleton from 'react-loading-skeleton'
import {
  Await,
  Outlet,
  useAsyncValue,
  useMatch,
  useRouteLoaderData,
} from 'react-router'

import { useTheme } from '@emotion/react'

import { Formik, FieldArray } from 'formik'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { noop } from 'lodash'
import { rem } from 'polished'

import {
  Button,
  remSpacing,
  Tooltip,
  Close,
  Info,
  SelectToggleIcon,
  useInputCSS,
  P3,
  ZIndex,
  ButtonGroup,
  Plus,
} from '@apella/component-library'
import { LocationPath } from 'src/router/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import orgLocalStorage from 'src/utils/orgLocalStorage'

import { FilterLabel } from '../../components/FilterLabel'
import { PAGE_FILTER_LOCAL_STORAGE_KEY } from './constants'
import { loader as estimateLoader } from './EstimateLayout'
import { ProcedureDropdown } from './ProcedureDropdown'
import { SurgeonDropdown } from './SurgeonDropdown'
import { Complexity } from './types'
import {
  COMPLEXITY_FILTER,
  CUSTOM_PROCEDURE_DURATION_FILTER,
  CUSTOM_TURNOVER_DURATION_FILTER,
  DURATION_SOURCE_FILTER,
  DurationSource,
  PROCEDURE_FILTER,
  ADDITIONAL_PROCEDURES_FILTER,
  SURGEON_FILTER,
  SURGEON_NAME_FILTER,
  useScheduleAssistantSurgeonProcedureFilters,
} from './useScheduleAssistantSurgeonProcedureFilters'

const SurgeonProcedureFiltersErrorElement = () => {
  return null
}

const STANDARD_CASES_DESCRIPTION = <P3>Recommended duration for most cases</P3>
const COMPLEX_CASES_DESCRIPTION = (
  <P3>
    Longer duration for more complex cases
    <br />
    <br />
    We recommend choosing standard if you don’t know the complexity of the case
  </P3>
)

export const SurgeonProcedureFiltersLayout = () => {
  const { predictionPromise } = useRouteLoaderData('caseDurationEstimate')
  const { durationSource } = useScheduleAssistantSurgeonProcedureFilters()
  const { enableMultiProcedureCaseDuration } = useFlags()
  const match = useMatch('/schedule-assistant/:viewId')
  const viewId = match?.params?.viewId

  return (
    <>
      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
          gap: remSpacing.medium,
        }}
      >
        <div
          css={{
            display: 'flex',
            gap: remSpacing.medium,
          }}
        >
          {enableMultiProcedureCaseDuration ? (
            <SurgeonAndProcedureFilters />
          ) : (
            <SingleProcedureSurgeonAndProcedureFilters />
          )}
          {durationSource === DurationSource.Custom &&
            viewId &&
            viewId === (LocationPath.AvailableTimes as string) && (
              <CustomDurationFilters />
            )}
        </div>
        <div
          css={{
            display: 'flex',
            gap: remSpacing.medium,
            alignItems: 'center',
          }}
        >
          {viewId && viewId === (LocationPath.AvailableTimes as string) && (
            <Suspense fallback={<Skeleton height={20} width={175} />}>
              <Await
                resolve={predictionPromise}
                errorElement={<SurgeonProcedureFiltersErrorElement />}
              >
                <ToggleDurationFilter />
              </Await>
            </Suspense>
          )}
        </div>
      </div>
      <Outlet />
    </>
  )
}

const SingleProcedureSurgeonAndProcedureFilters = () => {
  const eventLogger = useAnalyticsEventLogger()
  const {
    onSearch,
    resetParamsTo,
    clearParams,
    selectedSurgeonId,
    selectedSurgeonName,
    selectedProcedure,
    durationSource,
    selectedComplexity,
  } = useScheduleAssistantSurgeonProcedureFilters()

  return (
    <div
      css={{
        position: 'relative',
        zIndex: ZIndex.MORE_ABOVE,
        display: 'flex',
        gap: remSpacing.medium,
      }}
    >
      <FilterLabel label="Surgeon">
        <SurgeonDropdown
          selectedSurgeonId={selectedSurgeonId}
          selectedSurgeonName={selectedSurgeonName}
          onChangeHandler={(surgeonId, surgeonName) => {
            if (!surgeonId) {
              return clearParams()
            }
            eventLogger(EVENTS.SELECT_CASE_DURATION_SURGEON, {
              surgeon: surgeonName,
            })
            resetParamsTo({
              [SURGEON_NAME_FILTER]: surgeonName,
              [SURGEON_FILTER]: surgeonId,
              [DURATION_SOURCE_FILTER]: durationSource,
            })
          }}
        />
      </FilterLabel>
      {durationSource === DurationSource.Prediction && (
        <>
          <FilterLabel label="Procedure">
            <ProcedureDropdown
              name="procedure"
              selectedProcedure={selectedProcedure}
              selectedSurgeon={selectedSurgeonId}
              onChangeHandler={(procedureName) => {
                eventLogger(EVENTS.SELECT_CASE_DURATION_PROCEDURE, {
                  surgeon: selectedSurgeonName,
                  procedure: procedureName,
                })
                onSearch({
                  [PROCEDURE_FILTER]: procedureName,
                })
              }}
            />
          </FilterLabel>
          <ButtonGroup
            css={{ alignSelf: 'flex-end', paddingBottom: remSpacing.xsmall }}
          >
            <ButtonGroupSelectorOption
              key={Complexity.Standard}
              value={Complexity.Standard}
              selectedValue={selectedComplexity}
              onClick={(value) => {
                eventLogger(EVENTS.SELECT_CASE_DURATION_COMPLEXITY, {
                  surgeon: selectedSurgeonName,
                  procedure: selectedProcedure,
                  complexity: selectedComplexity,
                })
                onSearch({ [COMPLEXITY_FILTER]: value })
              }}
              tooltipText={STANDARD_CASES_DESCRIPTION}
              size={'sm'}
            >
              Standard
            </ButtonGroupSelectorOption>
            <ButtonGroupSelectorOption
              key={Complexity.Complex}
              value={Complexity.Complex}
              selectedValue={selectedComplexity}
              onClick={(value) => {
                eventLogger(EVENTS.SELECT_CASE_DURATION_COMPLEXITY, {
                  surgeon: selectedSurgeonName,
                  procedure: selectedProcedure,
                  complexity: selectedComplexity,
                })
                onSearch({ [COMPLEXITY_FILTER]: value })
              }}
              tooltipText={COMPLEX_CASES_DESCRIPTION}
              size={'sm'}
            >
              Complex
            </ButtonGroupSelectorOption>
          </ButtonGroup>
          <ResetButton />
        </>
      )}
    </div>
  )
}

const MAX_MULT_PROCEDURE_LIMIT = 2
// An empty procedure name is used to show the dropdown with no value
const DEFAULT_PROCEDURES_FIELD_ARRAY_VALUE = ['']

const SurgeonAndProcedureFilters = () => {
  const eventLogger = useAnalyticsEventLogger()
  const {
    onSearch,
    resetParamsTo,
    clearParams,
    selectedSurgeonId,
    selectedSurgeonName,
    selectedProcedure,
    selectedAdditionalProcedures,
    durationSource,
    selectedComplexity,
  } = useScheduleAssistantSurgeonProcedureFilters()
  const { enableMultiProcedureCaseDuration } = useFlags()

  const allSelectedProcedures = [
    selectedProcedure,
    ...selectedAdditionalProcedures,
  ]

  return (
    <div
      css={{
        position: 'relative',
        zIndex: ZIndex.MORE_ABOVE,
        display: 'flex',
        gap: remSpacing.medium,
        alignItems: 'flex-end',
      }}
    >
      <Formik
        onSubmit={noop}
        initialValues={{
          procedures: allSelectedProcedures.length
            ? allSelectedProcedures
            : DEFAULT_PROCEDURES_FIELD_ARRAY_VALUE,
        }}
      >
        {({ values, setFieldValue }) => (
          <>
            <FilterLabel label="Surgeon">
              <SurgeonDropdown
                selectedSurgeonId={selectedSurgeonId}
                selectedSurgeonName={selectedSurgeonName}
                onChangeHandler={(surgeonId, surgeonName) => {
                  if (!surgeonId) {
                    return clearParams()
                  }
                  eventLogger(EVENTS.SELECT_CASE_DURATION_SURGEON, {
                    surgeon: surgeonName,
                  })
                  setFieldValue(
                    'procedures',
                    DEFAULT_PROCEDURES_FIELD_ARRAY_VALUE
                  )
                  resetParamsTo({
                    [SURGEON_NAME_FILTER]: surgeonName,
                    [SURGEON_FILTER]: surgeonId,
                    [DURATION_SOURCE_FILTER]: durationSource,
                    [COMPLEXITY_FILTER]: selectedComplexity,
                  })
                }}
              />
            </FilterLabel>
            {durationSource === DurationSource.Prediction && (
              <>
                <FieldArray name="procedures">
                  {(fieldArrayHelpers) => (
                    <>
                      {values.procedures.map((additionalProcedure, index) => (
                        <FilterLabel
                          key={additionalProcedure}
                          label={
                            index == 0 ? 'Procedure' : 'Additional Procedure'
                          }
                        >
                          <div
                            css={{
                              display: 'flex',
                              gap: remSpacing.xxsmall,
                            }}
                          >
                            <ProcedureDropdown
                              name={`procedures.${index}`}
                              selectedProcedure={allSelectedProcedures[index]}
                              selectedSurgeon={selectedSurgeonId}
                              excludedProcedures={allSelectedProcedures.filter(
                                (procedure) =>
                                  procedure !== allSelectedProcedures[index]
                              )}
                              onChangeHandler={(procedureName) => {
                                if (procedureName) {
                                  eventLogger(
                                    EVENTS.SELECT_CASE_DURATION_PROCEDURE,
                                    {
                                      surgeon: selectedSurgeonName,
                                      procedure: procedureName,
                                    }
                                  )
                                  fieldArrayHelpers.replace(
                                    index,
                                    procedureName
                                  )
                                  if (index === 0) {
                                    onSearch({
                                      [PROCEDURE_FILTER]: procedureName,
                                    })
                                  } else {
                                    const nextAdditionalProcedures = [
                                      ...selectedAdditionalProcedures,
                                    ]
                                    nextAdditionalProcedures[index - 1] =
                                      procedureName
                                    onSearch({
                                      [ADDITIONAL_PROCEDURES_FILTER]:
                                        nextAdditionalProcedures,
                                    })
                                  }
                                }
                              }}
                            />
                            {index > 0 && (
                              <Button
                                size="sm"
                                appearance="link"
                                color="black"
                                onClick={() => {
                                  const procedureToRemove =
                                    values.procedures[index]
                                  const updatedAdditionalProcedures =
                                    values.procedures
                                      .slice(1)
                                      .filter(
                                        (val) => val !== procedureToRemove
                                      )

                                  fieldArrayHelpers.remove(index)
                                  onSearch({
                                    [ADDITIONAL_PROCEDURES_FILTER]:
                                      updatedAdditionalProcedures,
                                  })
                                }}
                              >
                                <Close size="sm" />
                              </Button>
                            )}
                          </div>
                        </FilterLabel>
                      ))}
                      {values.procedures.length < MAX_MULT_PROCEDURE_LIMIT && (
                        <Tooltip
                          placement="bottom"
                          body={
                            selectedProcedure ? (
                              <P3>Add a procedure</P3>
                            ) : (
                              <P3>
                                Select a procedure before adding a second
                                procedure
                              </P3>
                            )
                          }
                        >
                          <Button
                            css={{
                              height: rem(40),
                            }}
                            disabled={!selectedProcedure}
                            size="md"
                            onClick={() => fieldArrayHelpers.push('')}
                            color="alternate"
                          >
                            <Plus size="sm" />
                          </Button>
                        </Tooltip>
                      )}
                    </>
                  )}
                </FieldArray>
                {durationSource === DurationSource.Prediction &&
                  enableMultiProcedureCaseDuration && (
                    <ComplexityToggle css={{ marginBottom: rem('6px') }} />
                  )}
                <ResetButton
                  onClick={() => {
                    setFieldValue(
                      'procedures',
                      DEFAULT_PROCEDURES_FIELD_ARRAY_VALUE
                    )
                  }}
                />
              </>
            )}
          </>
        )}
      </Formik>
    </div>
  )
}

const CustomDurationFilters = () => {
  const inputCss = useInputCSS('md', undefined, undefined, rem(232))

  const { onSearch, customProcedureDuration, customTurnoverDuration } =
    useScheduleAssistantSurgeonProcedureFilters()
  return (
    <>
      <div
        css={{
          display: 'flex',
          gap: remSpacing.medium,
        }}
      >
        <FilterLabel label="Procedure duration (min)">
          <input
            type="number"
            value={customProcedureDuration}
            placeholder="Enter duration"
            css={[inputCss, { margin: 0 }]}
            onChange={(e) => {
              const valueNum = parseInt(e.target.value)
              const value = isNaN(valueNum) ? undefined : valueNum
              onSearch({
                [CUSTOM_PROCEDURE_DURATION_FILTER]: value,
              })
            }}
            step={15}
          />
        </FilterLabel>
        <FilterLabel label="Turnover duration (min)">
          <input
            type="number"
            value={customTurnoverDuration}
            placeholder="Enter duration"
            css={[inputCss, { margin: 0 }]}
            onChange={(e) => {
              const valueNum = parseInt(e.target.value)
              const value = isNaN(valueNum) ? undefined : valueNum
              onSearch({
                [CUSTOM_TURNOVER_DURATION_FILTER]: value,
              })
            }}
            step={15}
          />
        </FilterLabel>
      </div>
      <ResetButton />
    </>
  )
}

const ToggleDurationFilter = () => {
  const { procedureDuration, turnoverDuration } = useAsyncValue() as Awaited<
    Awaited<ReturnType<typeof estimateLoader>>['predictionPromise']
  >
  const eventLogger = useAnalyticsEventLogger()
  const match = useMatch('/schedule-assistant/:viewId')
  const viewId = match?.params?.viewId
  const theme = useTheme()
  const customDurationTooltipText =
    "Custom duration entry will replace Apella's duration prediction"

  const { onSearch, durationSource } =
    useScheduleAssistantSurgeonProcedureFilters()

  const onToggleDurationSource = () => {
    eventLogger(EVENTS.TIME_FINDER_FILTERS_TOGGLE_CUSTOM_DURATION)
    onSearch({
      durationSource:
        durationSource === DurationSource.Custom
          ? DurationSource.Prediction
          : DurationSource.Custom,
      // When switching to custom duration, we want to default to the prediction duration if it exists
      customProcedureDuration:
        durationSource === DurationSource.Prediction && procedureDuration
          ? procedureDuration
          : undefined,
      customTurnoverDuration:
        durationSource === DurationSource.Prediction && turnoverDuration
          ? turnoverDuration
          : undefined,
    })
  }
  return (
    !!viewId &&
    viewId === (LocationPath.AvailableTimes as string) && (
      <div
        css={{
          display: 'flex',
          alignItems: 'center',
          gap: remSpacing.xsmall,
          // This is to prevent extra space from being added to the icons
          lineHeight: 0,
        }}
      >
        <div
          css={{
            display: 'flex',
            gap: remSpacing.xsmall,
            userSelect: 'none',
            cursor: 'pointer',
          }}
          onClick={onToggleDurationSource}
        >
          <div
            aria-label="Use custom duration"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                onToggleDurationSource()
                e.preventDefault()
              }
            }}
            role="checkbox"
            aria-checked={durationSource === DurationSource.Custom}
            aria-labelledby="use-custom-duration-label"
          >
            <SelectToggleIcon
              type={
                durationSource === DurationSource.Custom
                  ? 'selected'
                  : undefined
              }
              size="sm"
            />
          </div>
          <P3 as="label" id="use-custom-duration-label">
            Use custom duration
          </P3>
        </div>
        <Tooltip
          body={<P3>{customDurationTooltipText}</P3>}
          placement="bottom"
          css={{ padding: remSpacing.xsmall }}
        >
          <Info size="xs" color={theme.palette.gray[50]} />
        </Tooltip>
      </div>
    )
  )
}

const ResetButton = (props: { onClick?: () => void }) => {
  const eventLogger = useAnalyticsEventLogger()
  const { resetable, clearParams } =
    useScheduleAssistantSurgeonProcedureFilters()
  return (
    resetable && (
      <Button
        css={{ alignSelf: 'flex-end' }}
        appearance="link"
        onClick={() => {
          orgLocalStorage.setItem(PAGE_FILTER_LOCAL_STORAGE_KEY, '')
          eventLogger(EVENTS.CLEAR_CASE_DURATION_LOOKUP)
          clearParams()
          props.onClick && props.onClick()
        }}
      >
        <Close size="sm" />
        Clear
      </Button>
    )
  )
}

const ButtonGroupSelectorOption = <T extends Complexity>({
  value,
  selectedValue,
  onClick,
  tooltipText,
  children,
  ...props
}: {
  value: T
  selectedValue: T
  onClick: (value: T) => void
  tooltipText: React.ReactNode
} & Omit<
  ComponentProps<typeof Button>,
  'onClick' | 'color' | 'appearance'
>) => {
  const isSelected = value === selectedValue
  return (
    <Button
      color={isSelected ? 'alternate' : 'gray'}
      appearance={isSelected ? 'button' : 'secondary'}
      onClick={() => onClick(value)}
      {...props}
    >
      <Tooltip body={tooltipText} placement={'bottom-start'}>
        {children}
      </Tooltip>
    </Button>
  )
}

const ComplexityToggle = ({ className }: { className?: string }) => {
  const eventLogger = useAnalyticsEventLogger()
  const {
    selectedComplexity,
    selectedSurgeonName,
    selectedProcedure,
    onSearch,
  } = useScheduleAssistantSurgeonProcedureFilters()

  return (
    <ButtonGroup className={className}>
      <ButtonGroupSelectorOption
        key={Complexity.Standard}
        value={Complexity.Standard}
        selectedValue={selectedComplexity}
        onClick={(value) => {
          eventLogger(EVENTS.SELECT_CASE_DURATION_COMPLEXITY, {
            surgeon: selectedSurgeonName,
            procedure: selectedProcedure,
            complexity: selectedComplexity,
          })
          onSearch({ [COMPLEXITY_FILTER]: value })
        }}
        tooltipText={STANDARD_CASES_DESCRIPTION}
        size={'sm'}
      >
        Standard
      </ButtonGroupSelectorOption>
      <ButtonGroupSelectorOption
        key={Complexity.Complex}
        value={Complexity.Complex}
        selectedValue={selectedComplexity}
        onClick={(value) => {
          eventLogger(EVENTS.SELECT_CASE_DURATION_COMPLEXITY, {
            surgeon: selectedSurgeonName,
            procedure: selectedProcedure,
            complexity: selectedComplexity,
          })
          onSearch({ [COMPLEXITY_FILTER]: value })
        }}
        tooltipText={COMPLEX_CASES_DESCRIPTION}
        size={'sm'}
      >
        Complex
      </ButtonGroupSelectorOption>
    </ButtonGroup>
  )
}
