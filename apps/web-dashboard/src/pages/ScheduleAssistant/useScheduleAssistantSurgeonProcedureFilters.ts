import { ParsedQs } from 'qs'

import { useParsedSearchParams } from '@apella/hooks'
import orgLocalStorage from 'src/utils/orgLocalStorage'

import { PAGE_FILTER_LOCAL_STORAGE_KEY } from './constants'
import { Complexity, isComplexity } from './types'
import { isValidTurnoverDuration } from './utils'

export enum DurationSource {
  Prediction = 'prediction',
  Custom = 'custom',
}

export const SURGEON_NAME_FILTER = 'surgeonName'
export const SURGEON_FILTER = 'surgeon'
export const PROCEDURE_FILTER = 'procedure'
export const ADDITIONAL_PROCEDURES_FILTER = 'additionalProcedures'
export const DURATION_SOURCE_FILTER = 'durationSource'
export const CUSTOM_PROCEDURE_DURATION_FILTER = 'customProcedureDuration'
export const CUSTOM_TURNOVER_DURATION_FILTER = 'customTurnoverDuration'
export const COMPLEXITY_FILTER = 'complexity'

export type ScheduleAssistantSurgeonProcedureFilters = {
  [SURGEON_NAME_FILTER]: string
  [SURGEON_FILTER]: string
  [PROCEDURE_FILTER]: string
  [ADDITIONAL_PROCEDURES_FILTER]: string[]
  [DURATION_SOURCE_FILTER]: DurationSource
  [CUSTOM_PROCEDURE_DURATION_FILTER]?: number
  [CUSTOM_TURNOVER_DURATION_FILTER]?: number
  [COMPLEXITY_FILTER]?: Complexity
}

export const defaults = {
  [COMPLEXITY_FILTER]: Complexity.Standard,
  [SURGEON_NAME_FILTER]: '',
  [SURGEON_FILTER]: '',
  [PROCEDURE_FILTER]: '',
  [ADDITIONAL_PROCEDURES_FILTER]: [],
  [DURATION_SOURCE_FILTER]: DurationSource.Prediction,
  [CUSTOM_PROCEDURE_DURATION_FILTER]: NaN,
  [CUSTOM_TURNOVER_DURATION_FILTER]: NaN,
}

export const castParams = (params: ParsedQs) => {
  const {
    [SURGEON_NAME_FILTER]: selectedSurgeonNameParam,
    [SURGEON_FILTER]: selectedSurgeonIdParam,
    [PROCEDURE_FILTER]: selectedProcedureParam,
    [ADDITIONAL_PROCEDURES_FILTER]: selectedAdditionalProceduresParam,
    [DURATION_SOURCE_FILTER]: durationSourceParam,
    [CUSTOM_PROCEDURE_DURATION_FILTER]: customProcedureDurationParam,
    [CUSTOM_TURNOVER_DURATION_FILTER]: customTurnoverDurationParam,
    [COMPLEXITY_FILTER]: selectedComplexitParam,
  } = params

  const selectedSurgeonName =
    typeof selectedSurgeonNameParam === 'string'
      ? selectedSurgeonNameParam
      : defaults[SURGEON_FILTER]
  const selectedSurgeonId =
    typeof selectedSurgeonIdParam === 'string'
      ? selectedSurgeonIdParam
      : defaults[SURGEON_FILTER]
  const selectedProcedure =
    typeof selectedProcedureParam === 'string'
      ? selectedProcedureParam
      : defaults[PROCEDURE_FILTER]
  const selectedAdditionalProcedures =
    Array.isArray(selectedAdditionalProceduresParam) &&
    selectedAdditionalProceduresParam.every((p) => typeof p === 'string')
      ? selectedAdditionalProceduresParam.map(String)
      : defaults[ADDITIONAL_PROCEDURES_FILTER]
  const durationSource =
    typeof durationSourceParam === 'string' &&
    Object.values(DurationSource).includes(
      durationSourceParam as DurationSource
    )
      ? (durationSourceParam as DurationSource)
      : defaults[DURATION_SOURCE_FILTER]

  const customProcedureDurationParamParsed =
    typeof customProcedureDurationParam === 'string'
      ? parseInt(customProcedureDurationParam)
      : NaN
  const customProcedureDuration =
    !isNaN(customProcedureDurationParamParsed) &&
    customProcedureDurationParamParsed > 0
      ? customProcedureDurationParamParsed
      : defaults[CUSTOM_PROCEDURE_DURATION_FILTER]
  const customTurnoverDurationParamParsed =
    typeof customTurnoverDurationParam === 'string'
      ? parseInt(customTurnoverDurationParam)
      : NaN
  const customTurnoverDuration =
    !isNaN(customTurnoverDurationParamParsed) &&
    isValidTurnoverDuration(customTurnoverDurationParamParsed)
      ? customTurnoverDurationParamParsed
      : defaults[CUSTOM_TURNOVER_DURATION_FILTER]
  const selectedComplexity =
    typeof selectedComplexitParam === 'string' &&
    isComplexity(selectedComplexitParam)
      ? selectedComplexitParam
      : defaults[COMPLEXITY_FILTER]

  return {
    selectedSurgeonName,
    selectedSurgeonId,
    selectedProcedure,
    selectedAdditionalProcedures,
    durationSource,
    customProcedureDuration,
    customTurnoverDuration,
    selectedComplexity,
  }
}

export const useScheduleAssistantSurgeonProcedureFilters = () => {
  const onClear = () =>
    orgLocalStorage.setItem(PAGE_FILTER_LOCAL_STORAGE_KEY, '')
  const { params, onSearch, ...rest } =
    useParsedSearchParams<ScheduleAssistantSurgeonProcedureFilters>({
      defaults,
      onClear,
    })

  const typedParams = castParams(params)

  return {
    params,
    onSearch,
    ...typedParams,
    ...rest,
  }
}
