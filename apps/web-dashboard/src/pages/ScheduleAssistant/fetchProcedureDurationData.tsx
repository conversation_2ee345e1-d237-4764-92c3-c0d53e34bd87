import { ApolloClient, NormalizedCacheObject } from '@apollo/client'

import {
  GetCaseDurationTurnoverPrediction,
  GetCaseDurationTurnoverPredictionVariables,
  GetCaseDurationPredictions,
  GetCaseDurationPredictionsVariables,
} from './__generated__'
import {
  GET_CASE_DURATION_TURNOVER_PREDICTION,
  GET_CASE_DURATION_PREDICTIONS,
} from './queries'
import { Complexity } from './types'
import { calcIQRtoMedianRatio } from './utils'

export const fetchProcedureDurationData = async (
  client: ApolloClient<NormalizedCacheObject>,
  {
    selectedComplexity,
    selectedSurgeonId,
    selectedProcedure,
    selectedAdditionalProcedures,
  }: {
    selectedComplexity: Complexity
    selectedSurgeonId: string
    selectedProcedure: string
    selectedAdditionalProcedures?: string[]
  }
) => {
  if (!selectedProcedure) {
    return {
      surgeonName: undefined,
      procedureName: undefined,
      additionalProcedures: [],
      procedureDuration: null,
      turnoverDuration: null,
      variabilityRatio: 0,
      iqr: 0,
    }
  }

  return Promise.all([
    client.query<
      GetCaseDurationTurnoverPrediction,
      GetCaseDurationTurnoverPredictionVariables
    >({
      query: GET_CASE_DURATION_TURNOVER_PREDICTION,
      variables: { surgeonId: selectedSurgeonId, procedure: selectedProcedure },
    }),
    client.query<
      GetCaseDurationPredictions,
      GetCaseDurationPredictionsVariables
    >({
      query: GET_CASE_DURATION_PREDICTIONS,
      variables: {
        surgeonId: selectedSurgeonId,
        procedure: selectedProcedure,
        additionalProcedures: selectedAdditionalProcedures,
      },
    }),
  ]).then(
    ([
      { data: turnoverDurationPrediction },
      { data: caseDurationPredictions },
    ]) => {
      const procedureDuration =
        caseDurationPredictions?.caseDurationPredictions?.standard || null
      const procedureDurationComplex =
        caseDurationPredictions?.caseDurationPredictions?.complex || null
      const turnoverDuration =
        turnoverDurationPrediction?.caseDurationTurnoverPrediction
          ?.cleanAfterCase || null
      const additionalProcedures =
        caseDurationPredictions.caseDurationPredictions?.meta
          ?.additionalProcedures || []
      const samples =
        caseDurationPredictions.caseDurationPredictions?.samples || []
      const result = calcIQRtoMedianRatio(
        samples.filter((sample): sample is number => typeof sample === 'number')
      )
      const variabilityRatio = result?.ratio ?? 0
      const iqr = result?.iqr ?? 0

      return {
        surgeonName:
          caseDurationPredictions.caseDurationPredictions?.meta?.surgeonName,
        procedureName:
          caseDurationPredictions.caseDurationPredictions?.meta?.procedureName,
        additionalProcedures: additionalProcedures.filter(
          (procedure): procedure is string => typeof procedure === 'string'
        ),
        procedureDuration:
          selectedComplexity === Complexity.Standard
            ? procedureDuration
            : procedureDurationComplex,
        turnoverDuration,
        variabilityRatio,
        iqr,
      }
    }
  )
}
