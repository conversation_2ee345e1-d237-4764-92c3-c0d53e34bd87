import { Outlet, useMatch } from 'react-router'

import { useFlags } from 'launchdarkly-react-client-sdk'

import { Button, Flag } from '@apella/component-library'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import { LocationPath, MenuItemTitle } from 'src/router/types'
import { onClickContactSupport } from 'src/utils/toggleBeacon'

export const Component = () => {
  const { permissions } = useCurrentUser()
  const { enableTimeFinder } = useFlags<WebDashboardFeatureFlagSet>()
  const match = useMatch('/schedule-assistant/:viewId/*')
  const viewId = match?.params?.viewId

  const caseDurationLink = {
    id: LocationPath.CaseDuration,
    to: {
      pathname: LocationPath.CaseDuration,
    },
    display: 'Case duration',
  }
  const availableTimesLink = {
    id: LocationPath.AvailableTimes,
    to: {
      pathname: LocationPath.AvailableTimes,
    },
    display: 'Available times',
  }
  const timeSelectionLink = {
    id: LocationPath.TimeSelection,
    to: {
      pathname: LocationPath.TimeSelection,
    },
    display: 'Time boost',
  }
  const { enableCarolsEmail } = useFlags<WebDashboardFeatureFlagSet>()
  const links = []
  if (enableCarolsEmail && permissions?.availableTimesEmailEnabled) {
    links.push(timeSelectionLink)
  }
  if (enableTimeFinder && permissions?.caseDurationEnabled) {
    links.push(availableTimesLink)
  }
  if (permissions?.caseDurationEnabled && links.length > 0) {
    links.unshift(caseDurationLink)
  }

  const feedBackActionButton = (
    <Button
      size="sm"
      appearance="link"
      color="primary"
      onClick={(e) =>
        onClickContactSupport(e, { subject: 'Schedule Assistant Request' })
      }
    >
      <Flag size="xs" />
      Feedback
    </Button>
  )

  return (
    <PageContentTemplate
      selectedViewId={viewId}
      title={
        enableTimeFinder
          ? MenuItemTitle.ScheduleAssistant
          : 'Case Duration Lookup'
      }
      views={links}
      actions={feedBackActionButton}
    >
      <Outlet />
    </PageContentTemplate>
  )
}
