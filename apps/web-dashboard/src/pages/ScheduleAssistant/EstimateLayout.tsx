import { Suspense, useEffect } from 'react'
import Skeleton from 'react-loading-skeleton'
import {
  Await,
  LoaderFunctionArgs,
  Outlet,
  replace,
  useAsyncError,
  useAsyncValue,
  useLoaderData,
  useMatch,
  useNavigate,
  useRouteError,
} from 'react-router'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import { useFlags } from 'launchdarkly-react-client-sdk'

import {
  Duration,
  Error as ErrorElement,
  FlexContainer,
  H3,
  P2,
  remSpacing,
} from '@apella/component-library'
import { parseParams } from '@apella/hooks'
import { EmptyState } from 'src/components/EmptyState'
import { useIsCurrentPageLoading } from 'src/router/hooks/useIsCurrentPageLoading'
import { getRouteContext, LocationPath } from 'src/router/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { logger } from 'src/utils/exceptionLogging'
import orgLocalStorage from 'src/utils/orgLocalStorage'

import { PAGE_FILTER_LOCAL_STORAGE_KEY } from './constants'
import { CompactEstimate, FullEstimate } from './Estimate'
import { fetchProcedureDurationData } from './fetchProcedureDurationData'
import {
  castParams,
  DurationSource,
  useScheduleAssistantSurgeonProcedureFilters,
} from './useScheduleAssistantSurgeonProcedureFilters'

// / If a procedure is really short but highly varies by only 25 minutes, we should not warn because that 25 minutes is likely inconsequential to scheduling decisions.
const IQR_THRESHOLD_MINUTES = 30

export const createRecentCasesCacheKey = ({
  surgeon,
  procedure,
}: {
  surgeon: string
  procedure: string
}) => `surgeon:${surgeon}-procedure:${procedure}`

export const loader = async (
  { request }: LoaderFunctionArgs,
  context: unknown
) => {
  const { apolloClient } = getRouteContext(context)
  const storedSearchParams =
    orgLocalStorage.getItem(PAGE_FILTER_LOCAL_STORAGE_KEY) ?? ''
  const requestURL = new URL(request.url)
  const params = parseParams(requestURL.search)
  const {
    selectedSurgeonId,
    selectedProcedure,
    selectedAdditionalProcedures,
    customProcedureDuration,
    customTurnoverDuration,
    selectedComplexity,
    durationSource,
  } = castParams(params)

  if (requestURL.search === '' && requestURL.search !== storedSearchParams) {
    throw replace(requestURL.pathname + storedSearchParams)
  }

  orgLocalStorage.setItem(PAGE_FILTER_LOCAL_STORAGE_KEY, requestURL.search)

  if (durationSource === DurationSource.Prediction) {
    return {
      predictionPromise: fetchProcedureDurationData(apolloClient, {
        selectedComplexity,
        selectedSurgeonId,
        selectedProcedure,
        selectedAdditionalProcedures,
      }),
    }
  } else {
    return {
      predictionPromise: Promise.resolve({
        procedureDuration: !isNaN(customProcedureDuration)
          ? customProcedureDuration
          : 0,
        turnoverDuration: !isNaN(customTurnoverDuration)
          ? customTurnoverDuration
          : 0,
        surgeonName: undefined,
        procedureName: undefined,
        additionalProcedures: [],
        variabilityRatio: 0,
        iqr: 0,
      }),
    }
  }
}

export const ErrorBoundary = () => {
  const theme = useTheme()
  const routeError = useRouteError()
  const asyncError = useAsyncError()
  const error = routeError || asyncError
  const navigate = useNavigate()

  useEffect(() => {
    if (
      error &&
      error instanceof Error &&
      error.message.includes('Surgeon not found')
    ) {
      orgLocalStorage.removeItem(PAGE_FILTER_LOCAL_STORAGE_KEY)
      toast.warning(
        'The selected surgeon was not found. Please select a different surgeon.'
      )
      navigate(LocationPath.ScheduleAssistant, { replace: true })
      return
    }

    if (error) {
      logger.error('Error predicting duration', { errorMessage: error })

      // eslint-disable-next-line no-console
      console.error(error)
    }
  }, [error, navigate])

  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.gutter,
        flexDirection: 'column',
      }}
    >
      <FlexContainer
        css={{
          padding: remSpacing.xlarge,
          margin: remSpacing.gutter,
          textAlign: 'center',
        }}
        direction="column"
        gap={remSpacing.xsmall}
      >
        <ErrorElement
          css={{ alignSelf: 'center', color: theme.palette.gray[20] }}
          size="xl"
        />
        <H3 as="h2">Unable to predict duration</H3>
        <P2 css={{ color: theme.palette.text.secondary }}>
          We encountered an issue while generating your prediction. Please try
          again later.
        </P2>
      </FlexContainer>
    </div>
  )
}

export const EstimateLayout = () => {
  const match = useMatch('/schedule-assistant/:viewId/*')
  const viewId = match?.params?.viewId
  const {
    durationSource,
    selectedSurgeonName,
    selectedProcedure,
    customProcedureDuration,
    customTurnoverDuration,
  } = useScheduleAssistantSurgeonProcedureFilters()
  const { predictionPromise } =
    useLoaderData<Awaited<ReturnType<typeof loader>>>()
  const reloading = useIsCurrentPageLoading()

  const fallback =
    viewId === LocationPath.AvailableTimes ? (
      <CompactEstimateSkeleton />
    ) : (
      <FullEstimateSkeleton />
    )

  if (
    !selectedSurgeonName ||
    (!selectedProcedure && durationSource === DurationSource.Prediction) ||
    (isNaN(customProcedureDuration) &&
      isNaN(customTurnoverDuration) &&
      durationSource === DurationSource.Custom)
  ) {
    return <NoSelectionEmptyState />
  }

  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.gutter,
        flexDirection: 'column',
      }}
    >
      {reloading ? (
        fallback
      ) : (
        <Suspense fallback={fallback}>
          <div
            id="estimate"
            css={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
            }}
          >
            <Await errorElement={<ErrorBoundary />} resolve={predictionPromise}>
              <EstimateDisplay durationSource={durationSource} />
            </Await>
          </div>
        </Suspense>
      )}

      <div
        css={{
          width: '100%',
          height: '100%',
        }}
      >
        <Outlet />
      </div>
    </div>
  )
}

const EstimateDisplay = ({
  durationSource,
}: {
  durationSource: DurationSource
}) => {
  const {
    procedureDuration,
    turnoverDuration,
    procedureName,
    additionalProcedures,
    surgeonName,
    variabilityRatio,
    iqr,
  } = useAsyncValue() as Awaited<
    Awaited<ReturnType<typeof loader>>['predictionPromise']
  >
  const { caseDurationVariabilityHeuristic } = useFlags()
  const { selectedSurgeonName, searchString } =
    useScheduleAssistantSurgeonProcedureFilters()
  const eventLogger = useAnalyticsEventLogger()
  const match = useMatch('/schedule-assistant/:viewId/*')
  const viewId = match?.params?.viewId

  useEffect(() => {
    eventLogger(EVENTS.RECEIVE_CASE_DURATION_PREDICTIONS, {
      surgeon: surgeonName,
      procedure: procedureName,
      additionalProcedures,
      procedureEstimateMinutes: procedureDuration,
      turnoverEstimateMinutes: turnoverDuration,
    })
  }, [
    eventLogger,
    procedureDuration,
    additionalProcedures,
    procedureName,
    surgeonName,
    turnoverDuration,
  ])

  return viewId === LocationPath.AvailableTimes ? (
    <CompactEstimate
      highVariability={
        caseDurationVariabilityHeuristic !== 0 &&
        variabilityRatio > caseDurationVariabilityHeuristic &&
        iqr > IQR_THRESHOLD_MINUTES
      }
      procedureEstimate={procedureDuration}
      turnoverEstimate={turnoverDuration}
      procedure={
        durationSource === DurationSource.Prediction ? procedureName : undefined
      }
      additionalProcedures={
        durationSource === DurationSource.Prediction &&
        additionalProcedures?.length
          ? additionalProcedures
          : []
      }
      surgeon={selectedSurgeonName}
      recentCasesTo={LocationPath.CaseDuration + '?' + searchString}
    />
  ) : (
    <FullEstimate
      highVariability={
        caseDurationVariabilityHeuristic !== 0 &&
        variabilityRatio > caseDurationVariabilityHeuristic &&
        iqr > IQR_THRESHOLD_MINUTES
      }
      procedureEstimate={procedureDuration}
      turnoverEstimate={turnoverDuration}
      procedure={
        durationSource === DurationSource.Prediction ? procedureName : undefined
      }
      additionalProcedures={
        durationSource === DurationSource.Prediction ? additionalProcedures : []
      }
      surgeon={selectedSurgeonName}
    />
  )
}

const SUBTEXT_MAP: { [x: string]: string } = {
  [LocationPath.CaseDuration]: 'to predict case duration',
  [LocationPath.AvailableTimes]: 'to view available times to schedule',
  default: '',
}

const NoSelectionEmptyState = () => {
  const match = useMatch('/schedule-assistant/:viewId')
  const { durationSource } = useScheduleAssistantSurgeonProcedureFilters()

  return (
    <EmptyState
      message={
        'Select a surgeon and ' +
        (durationSource === DurationSource.Prediction
          ? 'procedure'
          : 'enter durations')
      }
      subtext={SUBTEXT_MAP[match?.params?.viewId || 'default']}
      Icon={Duration}
    />
  )
}

const FullEstimateSkeleton = () => {
  return (
    <div
      css={{
        margin: '0 auto',
        textAlign: 'center',
      }}
    >
      <Skeleton height={197} width={700} />
    </div>
  )
}

const CompactEstimateSkeleton = () => {
  return (
    <div css={{ width: '100%' }}>
      <Skeleton height={76} width="100%" />
    </div>
  )
}
