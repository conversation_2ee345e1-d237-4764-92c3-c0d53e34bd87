import { useEffect, useMemo, useState } from 'react'

import { gql, useLazyQuery } from '@apollo/client'
import { debounce, differenceBy, uniqBy } from 'lodash'

import {
  P3,
  Option,
  Tooltip,
  remSpacing,
  Progress,
  ControlledSearchSingleSelect,
} from '@apella/component-library'

import {
  GetCaseDurationProcedures,
  GetCaseDurationProceduresVariables,
} from './__generated__'

const SingleSelectWithDisabledTooltip = ({
  ...props
}: Omit<
  React.ComponentProps<typeof ControlledSearchSingleSelect>,
  'disabled'
> & {
  disabledTooltip?: React.ReactElement
}) => {
  if (props.disabledTooltip) {
    return (
      <Tooltip body={props.disabledTooltip} placement={'bottom-start'}>
        <ControlledSearchSingleSelect disabled {...props} />
      </Tooltip>
    )
  }
  return <ControlledSearchSingleSelect {...props} />
}

export const GET_PROCEDURE_OPTIONS = gql`
  query GetCaseDurationProcedures($procedureTerm: String, $surgeonId: ID) {
    caseDurationProcedures(
      query: { procedureTerm: $procedureTerm, surgeonId: $surgeonId }
    ) {
      edges {
        node {
          procedureName
        }
      }
    }
  }
`

export const ProcedureDropdown = ({
  name,
  selectedProcedure,
  selectedSurgeon,
  onChangeHandler,
  excludedProcedures,
}: {
  name: string
  selectedProcedure: string
  selectedSurgeon: string
  onChangeHandler: (value?: string) => void
  excludedProcedures?: string[]
}) => {
  const [term, setTerm] = useState('')
  // Because the query is debounced, we cannot rely on "loading" to determine if we are searching
  const [searching, setSearching] = useState(false)
  const [selectionGroup, setSelectionGroup] = useState('')
  const [searchAll, { data: allProceduresData, loading: loadingAll }] =
    useLazyQuery<GetCaseDurationProcedures, GetCaseDurationProceduresVariables>(
      GET_PROCEDURE_OPTIONS
    )
  const [
    searchRecentProcedures,
    { data: recentProceduresData, loading: loadingSurgeonProcedures },
  ] = useLazyQuery<
    GetCaseDurationProcedures,
    GetCaseDurationProceduresVariables
  >(GET_PROCEDURE_OPTIONS)

  // Sync the "searching" state with the queries' loading states
  useEffect(() => {
    setSearching(loadingAll || loadingSurgeonProcedures)
  }, [loadingAll, loadingSurgeonProcedures])

  useEffect(() => {
    if (selectedSurgeon) {
      searchRecentProcedures({
        variables: { surgeonId: selectedSurgeon },
      })
    }
  }, [selectedSurgeon, searchRecentProcedures])

  const debounceSearch = useMemo(() => debounce(searchAll, 200), [searchAll])
  const debounceSurgeonSearch = useMemo(
    () => debounce(searchRecentProcedures, 200),
    [searchRecentProcedures]
  )

  const recentProcedures = uniqBy(
    recentProceduresData?.caseDurationProcedures?.edges || [],
    'node.procedureName'
    // Currently selected is added manually
  )
    .filter((procedure) => procedure.node.procedureName !== selectedProcedure)
    .filter(
      (procedure) => !excludedProcedures?.includes(procedure.node.procedureName)
    )

  const otherProcedures = uniqBy(
    differenceBy(
      allProceduresData?.caseDurationProcedures?.edges || [],
      recentProcedures,
      'node.procedureName'
    ),
    'node.procedureName'
    // Currently selected is added manually
  )
    .filter((procedure) => procedure.node.procedureName !== selectedProcedure)
    .filter(
      (procedure) => !excludedProcedures?.includes(procedure.node.procedureName)
    )

  const noResults =
    term &&
    !searching &&
    otherProcedures.length === 0 &&
    recentProcedures.length === 0

  const selectedOption = (
    <Option
      key={selectedProcedure}
      value={selectedProcedure}
      label={selectedProcedure}
      group={selectionGroup}
    >
      {selectedProcedure}
    </Option>
  )

  return (
    <SingleSelectWithDisabledTooltip
      name={name}
      label={'Procedure'}
      search={true}
      css={{ dropdownWidth: '35rem' }}
      value={selectedProcedure}
      onChange={(value) => {
        setSelectionGroup(
          recentProcedures.some(
            (procedure) => procedure.node.procedureName === value
          )
            ? 'Recent Procedures'
            : 'Other Procedures'
        )
        setTerm('')
        // Reload all recent procedures after selection
        searchRecentProcedures({
          variables: {
            surgeonId: selectedSurgeon,
          },
        })
        onChangeHandler(value)
      }}
      searchPlaceholder={'Search for "Other Procedures"...'}
      disabledTooltip={
        !selectedSurgeon ? <P3>Please select a surgeon first.</P3> : undefined
      }
      noResultsText={noResults ? 'No procedure found' : ''}
      searchTerm={term}
      onChangeSearchTerm={(newTerm) => {
        setTerm(newTerm)
        if (newTerm) {
          debounceSearch({
            variables: { procedureTerm: newTerm.toUpperCase() },
          })
        }
        if (newTerm !== term) {
          debounceSurgeonSearch({
            variables: {
              surgeonId: selectedSurgeon,
              procedureTerm: newTerm.toUpperCase(),
            },
          })
        }
      }}
      loadingOptionsElement={
        (loadingAll || loadingSurgeonProcedures) && (
          <div
            css={{
              textAlign: 'center',
              marginTop: remSpacing.small,
              marginBottom: remSpacing.small,
            }}
          >
            <Progress size="sm" />
          </div>
        )
      }
    >
      {selectedProcedure &&
        selectionGroup === 'Recent Procedures' &&
        selectedOption}
      {recentProcedures?.map((procedure) => (
        <Option
          key={procedure.node.procedureName}
          value={procedure.node.procedureName}
          label={procedure.node.procedureName}
          group={'Recent Procedures'}
        />
      ))}
      {selectedProcedure &&
        selectionGroup === 'Other Procedures' &&
        selectedOption}
      {term &&
        otherProcedures?.map((procedure) => (
          <Option
            key={procedure.node.procedureName}
            value={procedure.node.procedureName}
            label={procedure.node.procedureName}
            group={'Other Procedures'}
          />
        ))}
      {selectedProcedure && !selectionGroup && selectedOption}
    </SingleSelectWithDisabledTooltip>
  )
}
