import { useEffect, useMemo, useState } from 'react'

import { gql, useLazyQuery } from '@apollo/client'
import { debounce, uniqBy } from 'lodash'

import {
  Option,
  Progress,
  remSpacing,
  ControlledSearchSingleSelect,
} from '@apella/component-library'
import { useLocalStorageState } from '@apella/hooks'
import { useCurrentUser } from 'src/modules/user/hooks'

import {
  GetCaseDurationSurgeons,
  GetCaseDurationSurgeonsVariables,
} from './__generated__'
import { RECENTLY_SELECTED_SURGEONS_KEY } from './constants'

const RECENTLY_SELECTED_SURGEON_GROUP_NAME = 'Recently Selected Surgeons'

export const GET_SURGEON_OPTIONS = gql`
  query GetCaseDurationSurgeons($surgeonTerm: String, $surgeonId: ID) {
    caseDurationSurgeons(
      query: { surgeonTerm: $surgeonTerm, surgeonId: $surgeonId }
    ) {
      edges {
        node {
          surgeonId
          surgeonName
        }
      }
    }
  }
`

export const SurgeonDropdown = ({
  selectedSurgeonId,
  selectedSurgeonName,
  onChangeHandler,
}: {
  selectedSurgeonName?: string
  selectedSurgeonId?: string
  onChangeHandler: (value?: string, label?: string) => void
}) => {
  const [term, setTerm] = useState('')
  // Because the query is debounced, we cannot rely on "loading" to determine if we are searching
  const [searching, setSearching] = useState(false)
  const user = useCurrentUser()
  const [recentSurgeonsData, setRecentSurgeons] = useLocalStorageState<
    Array<{
      surgeonId: string
      surgeonName: string
    }>
  >(
    `${user.currentOrganization?.node.id}-${RECENTLY_SELECTED_SURGEONS_KEY}`,
    []
  )

  const addRecentSurgeon = (surgeonId: string, surgeonName: string) => {
    const existingSurgeon = recentSurgeonsData.find(
      (surgeon) => surgeon.surgeonId === surgeonId
    )

    if (!existingSurgeon) {
      setRecentSurgeons((prev) => [...prev, { surgeonId, surgeonName }])
    }
  }
  const removeRecentSurgeon = (surgeonId: string) => {
    const updatedSurgeons = recentSurgeonsData.filter(
      (surgeon) => surgeon.surgeonId !== surgeonId
    )
    setRecentSurgeons(updatedSurgeons)
  }

  const [search, { data, loading }] = useLazyQuery<
    GetCaseDurationSurgeons,
    GetCaseDurationSurgeonsVariables
  >(GET_SURGEON_OPTIONS)

  // Sync the "searching" state with the query's "loading" state
  useEffect(() => {
    setSearching(loading)
  }, [loading])

  const filteredSurgeons = uniqBy(
    data?.caseDurationSurgeons?.edges || [],
    'node.surgeonId'
    // Currently selected is added manually
  ).filter((surgeon) => surgeon.node.surgeonId !== selectedSurgeonId)

  const recentlySelectedSurgeons = recentSurgeonsData.filter(
    (surgeon) => surgeon.surgeonId !== selectedSurgeonId
  )

  const debounceSearch = useMemo(() => debounce(search, 200), [search])

  // Fetch the initial surgeon by ID if selectedSurgeonId is provided
  useEffect(() => {
    if (selectedSurgeonId) {
      search({ variables: { surgeonId: selectedSurgeonId } })
    }
  }, [selectedSurgeonId, search])

  const noResults = term && !searching && filteredSurgeons.length === 0

  return (
    <ControlledSearchSingleSelect
      name={'surgeon-filter'}
      label={'Surgeon'}
      search
      value={selectedSurgeonId}
      onChange={(value?: string, label?: string) => {
        setTerm('')
        onChangeHandler(value, label)
        if (value && label) {
          addRecentSurgeon(value, label)
        }
      }}
      searchPlaceholder={'Search for a surgeon...'}
      noResultsText={noResults ? 'No surgeons found' : ''}
      searchTerm={term}
      onChangeSearchTerm={(newTerm) => {
        setSearching(true)
        setTerm(newTerm)
        if (newTerm && newTerm.length > 1) {
          debounceSearch({ variables: { surgeonTerm: newTerm.toUpperCase() } })
        }
      }}
      loadingOptionsElement={
        loading && (
          <div
            css={{
              textAlign: 'center',
              marginTop: remSpacing.small,
              marginBottom: remSpacing.small,
            }}
          >
            <Progress size="sm" />
          </div>
        )
      }
    >
      {selectedSurgeonId && (
        <Option
          key={selectedSurgeonId}
          value={selectedSurgeonId}
          label={selectedSurgeonName || ''}
          group={RECENTLY_SELECTED_SURGEON_GROUP_NAME}
        />
      )}
      {recentlySelectedSurgeons.map(({ surgeonId, surgeonName }) => (
        <Option
          removable
          onRemove={(event) => {
            event.stopPropagation()
            removeRecentSurgeon(surgeonId)
          }}
          key={surgeonId}
          value={surgeonId}
          label={surgeonName}
          group={RECENTLY_SELECTED_SURGEON_GROUP_NAME}
        />
      ))}
      {term &&
        filteredSurgeons?.map(({ node: surgeon }) => (
          <Option
            key={surgeon.surgeonId}
            value={surgeon.surgeonId}
            label={surgeon.surgeonName}
            group={recentSurgeonsData.length ? 'Other Surgeons' : undefined}
          />
        ))}
    </ControlledSearchSingleSelect>
  )
}
