import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { useQuery } from '@apollo/client'

import {
  Chip,
  P3,
  Progress,
  remSpacing,
  Span3,
  Tooltip,
} from '@apella/component-library'

import { GetRoomTagsQuery, GetRoomTagsQueryVariables } from './__generated__'
import { GET_ROOM_TAGS } from './queries'

export const RoomTagChips = ({
  roomId,
  showTooltip = false,
}: {
  roomId: string
  showTooltip?: boolean
}) => {
  const { loading, data } = useQuery<
    GetRoomTagsQuery,
    GetRoomTagsQueryVariables
  >(GET_ROOM_TAGS, { variables: { roomId } })

  const tags = useMemo(() => data?.room?.tags ?? [], [data])

  // Show only the first two tags and hide the rest.
  // This should be replaced with a more robust solution
  // based on screen width
  const displayedTags = tags.slice(0, 2)
  const hiddenTags = tags.slice(2)

  const roomTooltipMiddleware = [Tooltip.middleware.offset(5)]
  const roomTooltipBody = <P3>Room type</P3>

  return loading && !data ? (
    <Progress size="sm" />
  ) : (
    <div css={{ display: 'flex', gap: remSpacing.xsmall }}>
      {displayedTags.map((tag: any) => {
        const tagChipComponent = (
          <TagChip key={tag.id} name={tag.name} color={tag.color} />
        )
        return showTooltip ? (
          <Tooltip
            key={tag.id}
            body={roomTooltipBody}
            middleware={roomTooltipMiddleware}
            additionalCss={{
              padding: `${remSpacing.xxsmall} ${remSpacing.xsmall}`,
            }}
          >
            {tagChipComponent}
          </Tooltip>
        ) : (
          tagChipComponent
        )
      })}
      {hiddenTags.length > 0 && <OverflowChip tags={hiddenTags} />}
    </div>
  )
}

const OverflowChip = ({
  tags,
}: {
  tags: { id: string; name: string; color: string }[]
}) => {
  const theme = useTheme()

  return (
    <Tooltip
      placement="bottom"
      body={
        <div css={{ display: 'flex', gap: remSpacing.xsmall }}>
          {tags.map((tag) => {
            return <TagChip key={tag.id} name={tag.name} color={tag.color} />
          })}
        </div>
      }
    >
      <Chip
        size="sm"
        css={{ border: `1px solid ${theme.palette.gray[30]}` }}
        color={{
          backgroundColor: theme.palette.background.primary,
          textColor: theme.palette.text.primary,
        }}
      >
        <Span3>+{tags.length}</Span3>
      </Chip>
    </Tooltip>
  )
}

const TagChip = ({ name, color }: { name: string; color: string }) => {
  const theme = useTheme()
  return (
    <Chip
      size="sm"
      css={{ border: `1px solid ${theme.palette.gray[30]}` }}
      color={{
        backgroundColor: theme.palette.background.primary,
        textColor: theme.palette.text.primary,
      }}
    >
      <span
        css={{
          height: remSpacing.xsmall,
          width: remSpacing.xsmall,
          background: color,
          borderRadius: '50%',
        }}
      />
      <Span3>{name}</Span3>
    </Chip>
  )
}
