import { gql } from '@apollo/client'

export const GET_CASE_DURATION_SURGEONS_AND_PROCEDURES = gql`
  query GetCaseDurationSurgeonsAndProcedures {
    caseDurationSurgeonsAndProcedures {
      edges {
        node {
          surgeonId
          surgeon
          procedure
        }
      }
    }
  }
`

export const GET_CASE_DURATION_TURNOVER_PREDICTION = gql`
  query GetCaseDurationTurnoverPrediction(
    $surgeonId: ID!
    $procedure: String!
  ) {
    caseDurationTurnoverPrediction(
      query: { surgeonId: $surgeonId, procedure: $procedure }
    ) {
      meta {
        surgeonName
        procedureName
      }
      cleanAfterCase
    }
  }
`

export const GET_CASE_DURATION_PREDICTIONS = gql`
  query GetCaseDurationPredictions(
    $surgeonId: ID!
    $procedure: String!
    $additionalProcedures: [String!] = []
  ) {
    caseDurationPredictions(
      query: {
        surgeonId: $surgeonId
        procedure: $procedure
        additionalProcedures: $additionalProcedures
      }
    ) {
      meta {
        surgeonName
        procedureName
        additionalProcedures
      }
      standard
      complex
      samples
    }
  }
`

export const GET_AVAILABLE_TIME_SLOTS = gql`
  query GetAvailableTimeSlots(
    $minAvailableDuration: Duration!
    $startDate: Date!
    $endDate: Date!
    $siteIds: [String!]!
    $surgeonId: String
  ) {
    availableTimeSlots(
      query: {
        minAvailableDuration: $minAvailableDuration
        startDate: $startDate
        endDate: $endDate
        siteIds: $siteIds
        surgeonId: $surgeonId
      }
    ) {
      roomId
      startTime
      endTime
      maxAvailableDuration
      blockTimeIds
    }
  }
`

export const GET_BLOCK_TIMES = gql`
  query GetBlockTimes($ids: [ID!]!) {
    blockTimesBulk(query: { ids: $ids }) {
      id
      startTime
      endTime
      block {
        id
        name
        color
        surgeonIds
      }
    }
  }
`

export const GET_PRIMARY_SURGEON_INFORMATION = gql`
  query GetPrimarySurgeonInformation($id: String!) {
    staff(query: { ids: [$id] }) {
      edges {
        node {
          id
          blockIds
          mostFrequentSite {
            id
            timezone
          }
        }
      }
    }
  }
`

export const GET_APELLA_CASES_IN_SITE = gql`
  query GetApellaCasesInSite(
    $minEndTime: DateTime!
    $maxStartTime: DateTime!
    $siteIds: [ID!]!
  ) {
    apellaCases(
      query: {
        minEndTime: $minEndTime
        maxStartTime: $maxStartTime
        siteIds: $siteIds
      }
    ) {
      edges {
        node {
          id
          startTime
          endTime
          roomId
          case {
            id
            scheduledStartTime
            scheduledEndTime
            caseStaff {
              role
              staff {
                id
                firstName
                lastName
              }
            }
            primaryCaseProcedures {
              procedure {
                id
                name
              }
            }
          }
        }
      }
    }
  }
`

export const GET_ROOM_TAGS = gql`
  query GetRoomTagsQuery($roomId: String!) {
    room(id: $roomId) {
      id
      tags {
        id
        name
        color
      }
    }
  }
`

export const EMAIL_AVAILABLE_TIMES = gql`
  mutation EmailAvailableTimes($input: EmailAvailableTimesInput!) {
    emailAvailableTimes(input: $input) {
      success
    }
  }
`

export const EMAIL_AVAILABLE_TIMES_HTML = gql`
  mutation EmailAvailableTimesHtml($input: EmailAvailableTimesHtmlInput!) {
    emailAvailableTimesHtml(input: $input) {
      success
      email
      subject
    }
  }
`
