import { GetPrimarySurgeonInformation } from '../__generated__'

export const GetPrimarySurgeonInformationNoBlocksData: {
  data: GetPrimarySurgeonInformation
} = {
  data: {
    staff: {
      edges: [
        {
          node: {
            id: '485e9ced-cb7e-4c3e-93bf-5d1485d122ae',
            blockIds: [],
            mostFrequentSite: {
              id: 'site-01',
              timezone: 'America/Chicago',
              __typename: 'Site',
            },
            __typename: 'Staff',
          },
          __typename: 'StaffEdge',
        },
      ],
      __typename: 'StaffConnection',
    },
  },
}

export const GetPrimarySurgeonInformationThanosData: {
  data: GetPrimarySurgeonInformation
} = {
  data: {
    staff: {
      edges: [
        {
          node: {
            id: '485e9ced-cb7e-4c3e-93bf-5d1485d122ae',
            blockIds: ['block-01'],
            mostFrequentSite: {
              id: 'site-01',
              timezone: 'America/Chicago',
              __typename: 'Site',
            },
            __typename: 'Staff',
          },
          __typename: 'StaffEdge',
        },
      ],
      __typename: 'StaffConnection',
    },
  },
}
