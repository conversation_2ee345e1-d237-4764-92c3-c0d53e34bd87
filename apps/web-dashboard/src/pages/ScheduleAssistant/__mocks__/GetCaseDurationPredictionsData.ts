import { GetCaseDurationPredictions } from '../__generated__'
import {
  GetCaseDurationProceduresData,
  GetCaseDurationSurgeonsData,
} from './GetCaseDurationSurgeonsAndProceduresData'

export const GetCaseDurationPredictionsData: {
  data: GetCaseDurationPredictions
} = {
  data: {
    caseDurationPredictions: {
      meta: {
        __typename: 'PredictionMetadata',
        surgeonName:
          GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
            .surgeonName,
        procedureName:
          GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
            .node.procedureName,
        additionalProcedures: [],
      },
      standard: 48,
      complex: 60,
      samples: [40, 45, 47, 48, 49, 50, 55, 58, 60, 62],
      __typename: 'CaseDurationPredictions',
    },
  },
}

export const GetCaseDurationPredictionsMultiProcedureData: {
  data: GetCaseDurationPredictions
} = {
  data: {
    caseDurationPredictions: {
      meta: {
        __typename: 'PredictionMetadata',
        surgeonName:
          GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
            .surgeonName,
        procedureName:
          GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
            .node.procedureName,
        additionalProcedures: [
          GetCaseDurationProceduresData.data.caseDurationProcedures.edges[1]
            .node.procedureName,
        ],
      },
      standard: 148,
      complex: 160,
      samples: [140, 145, 147, 148, 149, 150, 155, 158, 160, 162],
      __typename: 'CaseDurationPredictions',
    },
  },
}

export const GetCaseDurationPredictionsMultiProcedureDataHighVariance: {
  data: GetCaseDurationPredictions
} = {
  data: {
    caseDurationPredictions: {
      meta: {
        __typename: 'PredictionMetadata',
        surgeonName:
          GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
            .surgeonName,
        procedureName:
          GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
            .node.procedureName,
        additionalProcedures: [
          GetCaseDurationProceduresData.data.caseDurationProcedures.edges[1]
            .node.procedureName,
        ],
      },
      standard: 148,
      complex: 250,
      samples: [0, 10, 80],
      __typename: 'CaseDurationPredictions',
    },
  },
}
