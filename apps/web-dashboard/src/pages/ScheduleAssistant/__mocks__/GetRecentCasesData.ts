export const GetRecentCasesData = {
  queryType: 'regularQuery',
  results: [
    {
      query: {
        dimensions: [
          'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure',
          'ForecastingReportDataUsedInTrainingBQ.procedureList',
          'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon',
          'ForecastingReportDataUsedInTrainingBQ.surgeonList',
          'ForecastingReportDataUsedInTrainingBQ.caseId',
          'ForecastingReportDataUsedInTrainingBQ.site',
          'ForecastingReportDataUsedInTrainingBQ.room',
          'ForecastingReportDataUsedInTrainingBQ.roomId',
          'ForecastingReportDataUsedInTrainingBQ.startTimeLocal',
          'ForecastingReportDataUsedInTrainingBQ.duration',
        ],
        limit: 20,
        order: [
          {
            id: 'ForecastingReportDataUsedInTrainingBQ.startTimeLocal',
            desc: true,
          },
        ],
        filters: [
          {
            member:
              'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure',
            operator: 'equals',
            values: ['Remove pink gem'],
          },
          {
            member: 'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon',
            operator: 'equals',
            values: ['Thanos'],
          },
          {
            member: 'OrganizationsBQ.id',
            operator: 'equals',
            values: ['houston_methodist'],
          },
          {
            member: 'OrganizationsBQ.id',
            operator: 'set',
          },
        ],
        timezone: 'UTC',
        timeDimensions: [],
        rowLimit: 20,
        queryType: 'regularQuery',
      },
      data: [
        {
          'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure':
            'Remove pink gem',
          'ForecastingReportDataUsedInTrainingBQ.procedureList':
            '["Remove pink gem"]',
          'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon': 'Thanos',
          'ForecastingReportDataUsedInTrainingBQ.surgeonList':
            '[{"first_name":"Tha","last_name":"nos"}]',
          'ForecastingReportDataUsedInTrainingBQ.caseId': 'case-001',
          'ForecastingReportDataUsedInTrainingBQ.site': 'Site 01',
          'ForecastingReportDataUsedInTrainingBQ.room': 'OR 01',
          'ForecastingReportDataUsedInTrainingBQ.roomID': 'HMH-Site01-OR01',
          'ForecastingReportDataUsedInTrainingBQ.startTimeLocal':
            '2024-08-13T13:22:07.354',
          'ForecastingReportDataUsedInTrainingBQ.duration': 81,
        },
        {
          'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure':
            'Remove pink gem',
          'ForecastingReportDataUsedInTrainingBQ.procedureList':
            '["Remove pink gem"]',
          'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon': 'Thanos',
          'ForecastingReportDataUsedInTrainingBQ.surgeonList':
            '[{"first_name":"Tha","last_name":"nos"}]',
          'ForecastingReportDataUsedInTrainingBQ.caseId': 'case-002',
          'ForecastingReportDataUsedInTrainingBQ.site': 'Site 01',
          'ForecastingReportDataUsedInTrainingBQ.room': 'OR 01',
          'ForecastingReportDataUsedInTrainingBQ.roomID': 'HMH-Site01-OR01',
          'ForecastingReportDataUsedInTrainingBQ.startTimeLocal':
            '2022-05-31T13:54:00.000',
          'ForecastingReportDataUsedInTrainingBQ.duration': 31,
        },
        {
          'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure':
            'Remove pink gem',
          'ForecastingReportDataUsedInTrainingBQ.procedureList':
            '["Remove pink gem"]',
          'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon': 'Thanos',
          'ForecastingReportDataUsedInTrainingBQ.surgeonList':
            '[{"first_name":"Tha","last_name":"nos"}]',
          'ForecastingReportDataUsedInTrainingBQ.caseId': 'case-003',
          'ForecastingReportDataUsedInTrainingBQ.site': 'Site 01',
          'ForecastingReportDataUsedInTrainingBQ.room': 'OR 01',
          'ForecastingReportDataUsedInTrainingBQ.roomID': 'HMH-Site01-OR01',
          'ForecastingReportDataUsedInTrainingBQ.startTimeLocal':
            '2022-01-25T11:15:00.000',
          'ForecastingReportDataUsedInTrainingBQ.duration': 71,
        },
      ],
      lastRefreshTime: '2024-09-12T20:41:46.322Z',
      annotation: {
        measures: {},
        dimensions: {
          'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure': {
            title:
              'Forecasting Report Data Used in Training Bq First Primary Procedure',
            shortTitle: 'First Primary Procedure',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.procedureList': {
            title: 'Forecasting Report Data Used in Training Bq Procedure List',
            shortTitle: 'Procedure List',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon': {
            title:
              'Forecasting Report Data Used in Training Bq First Primary Surgeon',
            shortTitle: 'First Primary Surgeon',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.surgeonList': {
            title: 'Forecasting Report Data Used in Training Bq Surgeon List',
            shortTitle: 'Surgeon List',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.caseId': {
            title: 'Forecasting Report Data Used in Training Bq Case Id',
            shortTitle: 'Case Id',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.site': {
            title: 'Forecasting Report Data Used in Training Bq Site',
            shortTitle: 'Site',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.room': {
            title: 'Forecasting Report Data Used in Training Bq Room',
            shortTitle: 'Room',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.roomId': {
            title: 'Forecasting Report Data Used in Training Bq Room Id',
            shortTitle: 'Room Id',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.startTimeLocal': {
            title:
              'Forecasting Report Data Used in Training Bq Start Time Local',
            shortTitle: 'Start Time Local',
            type: 'time',
          },
          'ForecastingReportDataUsedInTrainingBQ.duration': {
            title: 'Forecasting Report Data Used in Training Bq Duration',
            shortTitle: 'Duration',
            type: 'number',
          },
        },
        segments: {},
        timeDimensions: {},
      },
      dataSource: 'bigquery',
      dbType: 'bigquery',
      external: false,
      slowQuery: false,
      total: null,
    },
  ],
  pivotQuery: {
    dimensions: [
      'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure',
      'ForecastingReportDataUsedInTrainingBQ.procedureList',
      'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon',
      'ForecastingReportDataUsedInTrainingBQ.surgeonList',
      'ForecastingReportDataUsedInTrainingBQ.caseId',
      'ForecastingReportDataUsedInTrainingBQ.site',
      'ForecastingReportDataUsedInTrainingBQ.room',
      'ForecastingReportDataUsedInTrainingBQ.roomId',
      'ForecastingReportDataUsedInTrainingBQ.startTimeLocal',
      'ForecastingReportDataUsedInTrainingBQ.duration',
    ],
    limit: 20,
    order: [
      {
        id: 'ForecastingReportDataUsedInTrainingBQ.startTimeLocal',
        desc: true,
      },
    ],
    filters: [
      {
        member: 'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure',
        operator: 'equals',
        values: ['Remove pink gem'],
      },
      {
        member: 'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon',
        operator: 'equals',
        values: ['Thanos'],
      },
      {
        member: 'OrganizationsBQ.id',
        operator: 'equals',
        values: ['houston_methodist'],
      },
      {
        member: 'OrganizationsBQ.id',
        operator: 'set',
      },
    ],
    timezone: 'UTC',
    timeDimensions: [],
    rowLimit: 20,
    queryType: 'regularQuery',
  },
  slowQuery: false,
}

export const GetRecentCasesDataNoResults = {
  queryType: 'regularQuery',
  results: [
    {
      query: {
        dimensions: [
          'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure',
          'ForecastingReportDataUsedInTrainingBQ.procedureList',
          'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon',
          'ForecastingReportDataUsedInTrainingBQ.surgeonList',
          'ForecastingReportDataUsedInTrainingBQ.caseId',
          'ForecastingReportDataUsedInTrainingBQ.site',
          'ForecastingReportDataUsedInTrainingBQ.room',
          'ForecastingReportDataUsedInTrainingBQ.roomId',
          'ForecastingReportDataUsedInTrainingBQ.startTimeLocal',
          'ForecastingReportDataUsedInTrainingBQ.duration',
        ],
        limit: 20,
        order: [
          {
            id: 'ForecastingReportDataUsedInTrainingBQ.startTimeLocal',
            desc: true,
          },
        ],
        filters: [
          {
            member:
              'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure',
            operator: 'equals',
            values: ['Remove pink gem'],
          },
          {
            member: 'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon',
            operator: 'equals',
            values: ['Thanos'],
          },
          {
            member: 'OrganizationsBQ.id',
            operator: 'equals',
            values: ['houston_methodist'],
          },
          {
            member: 'OrganizationsBQ.id',
            operator: 'set',
          },
        ],
        timezone: 'UTC',
        timeDimensions: [],
        rowLimit: 20,
        queryType: 'regularQuery',
      },
      data: [],
      lastRefreshTime: '2024-09-12T20:41:46.322Z',
      annotation: {
        measures: {},
        dimensions: {
          'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure': {
            title:
              'Forecasting Report Data Used in Training Bq First Primary Procedure',
            shortTitle: 'First Primary Procedure',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.procedureList': {
            title: 'Forecasting Report Data Used in Training Bq Procedure List',
            shortTitle: 'Procedure List',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon': {
            title:
              'Forecasting Report Data Used in Training Bq First Primary Surgeon',
            shortTitle: 'First Primary Surgeon',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.surgeonList': {
            title: 'Forecasting Report Data Used in Training Bq Surgeon List',
            shortTitle: 'Surgeon List',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.caseId': {
            title: 'Forecasting Report Data Used in Training Bq Case Id',
            shortTitle: 'Case Id',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.site': {
            title: 'Forecasting Report Data Used in Training Bq Site',
            shortTitle: 'Site',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.room': {
            title: 'Forecasting Report Data Used in Training Bq Room',
            shortTitle: 'Room',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.roomId': {
            title: 'Forecasting Report Data Used in Training Bq Room Id',
            shortTitle: 'Room Id',
            type: 'string',
          },
          'ForecastingReportDataUsedInTrainingBQ.startTimeLocal': {
            title:
              'Forecasting Report Data Used in Training Bq Start Time Local',
            shortTitle: 'Start Time Local',
            type: 'time',
          },
          'ForecastingReportDataUsedInTrainingBQ.duration': {
            title: 'Forecasting Report Data Used in Training Bq Duration',
            shortTitle: 'Duration',
            type: 'number',
          },
        },
        segments: {},
        timeDimensions: {},
      },
      dataSource: 'bigquery',
      dbType: 'bigquery',
      external: false,
      slowQuery: false,
      total: null,
    },
  ],
  pivotQuery: {
    dimensions: [
      'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure',
      'ForecastingReportDataUsedInTrainingBQ.procedureList',
      'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon',
      'ForecastingReportDataUsedInTrainingBQ.surgeonList',
      'ForecastingReportDataUsedInTrainingBQ.caseId',
      'ForecastingReportDataUsedInTrainingBQ.site',
      'ForecastingReportDataUsedInTrainingBQ.room',
      'ForecastingReportDataUsedInTrainingBQ.roomId',
      'ForecastingReportDataUsedInTrainingBQ.startTimeLocal',
      'ForecastingReportDataUsedInTrainingBQ.duration',
    ],
    limit: 20,
    order: [
      {
        id: 'ForecastingReportDataUsedInTrainingBQ.startTimeLocal',
        desc: true,
      },
    ],
    filters: [
      {
        member: 'ForecastingReportDataUsedInTrainingBQ.firstPrimaryProcedure',
        operator: 'equals',
        values: ['Remove pink gem'],
      },
      {
        member: 'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon',
        operator: 'equals',
        values: ['Thanos'],
      },
      {
        member: 'OrganizationsBQ.id',
        operator: 'equals',
        values: ['houston_methodist'],
      },
      {
        member: 'OrganizationsBQ.id',
        operator: 'set',
      },
    ],
    timezone: 'UTC',
    timeDimensions: [],
    rowLimit: 20,
    queryType: 'regularQuery',
  },
  slowQuery: false,
}
