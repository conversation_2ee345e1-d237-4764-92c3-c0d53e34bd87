import { GetBlockTimes } from '../__generated__'
import { THE_UNBLOCKED_BLOCK_ID } from '../constants'

export const GetBlockTimesData: { data: GetBlockTimes } = {
  data: {
    blockTimesBulk: [
      {
        startTime: '2024-09-13T17:30:00+00:00',
        endTime: '2024-09-13T22:00:00+00:00',
        block: {
          name: 'thanos',
          color: '#f36f66',
          surgeonIds: ['9c4e8601-6674-4cd3-aecc-4184165ae28f'],
          __typename: 'Block',
          id: 'block-01',
        },
        __typename: 'BlockTime',
        id: 'blocktime-01',
      },
      {
        startTime: '2024-09-13T14:30:00+00:00',
        endTime: '2024-09-13T17:30:00+00:00',
        block: {
          id: THE_UNBLOCKED_BLOCK_ID,
          name: 'Unblocked',
          color: '#555',
          surgeonIds: [],
          __typename: 'Block',
        },
        __typename: 'BlockTime',
        id: 'blocktime-02',
      },
      {
        startTime: '2024-09-14T00:30:00+00:00',
        endTime: '2024-09-23T22:00:00+00:00',
        block: {
          id: THE_UNBLOCKED_BLOCK_ID,
          name: 'Unblocked',
          color: '#555',
          surgeonIds: [],
          __typename: 'Block',
        },
        __typename: 'BlockTime',
        id: 'blocktime-03',
      },
      {
        startTime: '2024-09-13T22:00:00+00:00',
        endTime: '2024-09-13T23:00:00+00:00',
        block: {
          id: THE_UNBLOCKED_BLOCK_ID,
          name: 'Unblocked',
          color: '#555',
          surgeonIds: [],
          __typename: 'Block',
        },
        __typename: 'BlockTime',
        id: 'blocktime-04',
      },
    ],
  },
}
