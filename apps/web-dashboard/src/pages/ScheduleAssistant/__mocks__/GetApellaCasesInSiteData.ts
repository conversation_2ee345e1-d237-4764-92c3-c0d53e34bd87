import { GetSiteOptionsFilterData } from 'src/modules/site/__mocks__/GetSiteOptionsFilterData'

import { GetApellaCasesInSite } from './../__generated__'

export const GetApellaCasesInSiteData: { data: GetApellaCasesInSite } = {
  data: {
    apellaCases: {
      edges: [
        {
          node: {
            id: 'case:d13254dc-7bf3-4016-9999-d9da7fae03e2',
            startTime: '2024-09-13T11:57:31+00:00',
            endTime: '2024-09-13T12:33:00+00:00',
            roomId:
              GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0]
                .node.id,
            case: {
              id: 'd13254dc-7bf3-4016-9999-d9da7fae03e2',
              scheduledStartTime: '2024-09-12T12:00:00+00:00',
              scheduledEndTime: '2024-09-12T12:40:00+00:00',
              caseStaff: [
                {
                  role: 'Primary',
                  staff: {
                    id: 'f6f68ee1-acaf-4882-aa2b-300e32cb2cf9',
                    firstName: 'John',
                    lastName: 'Doe',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Scrub Person',
                  staff: {
                    id: 'ea54a2fd-a403-44e5-9293-530185037cd3',
                    firstName: 'Jane',
                    lastName: 'Person',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Circulator',
                  staff: {
                    id: '577da30b-cba4-4861-9b9d-dd03529f4e38',
                    firstName: 'Peter',
                    lastName: 'Parker',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Anesthesiologist',
                  staff: {
                    id: '0b3abbbc-5309-4865-bea7-9ca6fb35bc73',
                    firstName: 'Bruce',
                    lastName: 'Banner',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
              ],
              primaryCaseProcedures: [
                {
                  procedure: {
                    id: 'ca22f888-841a-4206-a8a8-456110bd6e88',
                    name: 'PHACOEMULSIFICATION, CATARACT, WITH IOL IMPLANTATION',
                    __typename: 'Procedure',
                  },
                  __typename: 'CaseProcedure',
                },
              ],
              __typename: 'ScheduledCase',
            },
            __typename: 'ApellaCase',
          },
          __typename: 'ApellaCaseEdge',
        },
        {
          node: {
            id: 'case:ec41dec1-8a45-43a5-aa3b-c40e5cac76ae',
            startTime: '2024-09-13T19:28:46+00:00',
            endTime: '2024-09-13T20:20:30+00:00',
            roomId:
              GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[2]
                .node.id,
            case: {
              id: 'ec41dec1-8a45-43a5-aa3b-c40e5cac76ae',
              scheduledStartTime: '2024-09-13T22:30:00+00:00',
              scheduledEndTime: '2024-09-13T23:55:00+00:00',
              caseStaff: [
                {
                  role: 'Primary',
                  staff: {
                    id: 'aecb74cc-de8d-4838-af44-6a53abb66de5',
                    firstName: 'Happy',
                    lastName: 'Hogan',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Scrub Person',
                  staff: {
                    id: '2e30d1d4-f1b7-440c-90a0-cd45558e0117',
                    firstName: 'Phil',
                    lastName: 'Coulson',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Circulator',
                  staff: {
                    id: '52d1eb53-ca82-45e8-a482-0d4ec1a3519e',
                    firstName: 'Captain',
                    lastName: 'America',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Anesthesiologist',
                  staff: {
                    id: 'be8de483-0bda-4278-a81a-a4752d078aa8',
                    firstName: 'Pepper',
                    lastName: 'Potts',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
              ],
              primaryCaseProcedures: [
                {
                  procedure: {
                    id: 'd4cf5854-a5bf-4bf5-b4c7-0e5c9ad308ef',
                    name: 'MENISCECTOMY, KNEE, MEDIAL, ARTHROSCOPIC',
                    __typename: 'Procedure',
                  },
                  __typename: 'CaseProcedure',
                },
              ],
              __typename: 'ScheduledCase',
            },
            __typename: 'ApellaCase',
          },
          __typename: 'ApellaCaseEdge',
        },
        {
          node: {
            id: 'case:aaca19f1-4a35-4cda-9bf5-5c279cb93231',
            startTime: '2024-09-13T12:30:25+00:00',
            endTime: '2024-09-13T14:40:00+00:00',
            roomId:
              GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1]
                .node.id,
            case: {
              id: 'aaca19f1-4a35-4cda-9bf5-5c279cb93231',
              scheduledStartTime: '2024-09-13T12:30:00+00:00',
              scheduledEndTime: '2024-09-13T15:25:00+00:00',
              caseStaff: [
                {
                  role: 'Primary',
                  staff: {
                    id: '4a077815-e393-4969-9945-7df5773c9463',
                    firstName: 'Wonder',
                    lastName: 'Woman',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Circulator',
                  staff: {
                    id: 'e6bb7ed9-92fd-49de-83dc-f320d7661c20',
                    firstName: 'Bat',
                    lastName: 'Man',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Scrub Person',
                  staff: {
                    id: '26bad2e7-9f06-4511-ba18-478797c212ac',
                    firstName: 'Super',
                    lastName: 'Man',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Anesthesiologist',
                  staff: {
                    id: '9c4e8601-6674-4cd3-aecc-4184165ae28f',
                    firstName: 'Thanos',
                    lastName: 'Villain',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
              ],
              primaryCaseProcedures: [
                {
                  procedure: {
                    id: '6611666b-f03b-49d9-adcd-01a190e9ac8e',
                    name: 'SINUS SURGERY, ENDOSCOPIC',
                    __typename: 'Procedure',
                  },
                  __typename: 'CaseProcedure',
                },
              ],
              __typename: 'ScheduledCase',
            },
            __typename: 'ApellaCase',
          },
          __typename: 'ApellaCaseEdge',
        },
        {
          node: {
            id: 'case:66404f66-d94d-4635-a5b7-cf7a07f76e8c',
            startTime: '2024-09-16T12:44:58+00:00',
            endTime: '2024-09-16T13:24:00+00:00',
            roomId:
              GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1]
                .node.id,
            case: {
              id: '66404f66-d94d-4635-a5b7-cf7a07f76e8c',
              scheduledStartTime: '2024-09-16T15:40:00+00:00',
              scheduledEndTime: '2024-09-16T22:25:00+00:00',
              caseStaff: [
                {
                  role: 'Primary',
                  staff: {
                    id: 'f6f68ee1-acaf-4882-aa2b-300e32cb2cf9',
                    firstName: 'John',
                    lastName: 'Doe',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Scrub Person',
                  staff: {
                    id: 'ea54a2fd-a403-44e5-9293-530185037cd3',
                    firstName: 'Jane',
                    lastName: 'Person',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Circulator',
                  staff: {
                    id: '577da30b-cba4-4861-9b9d-dd03529f4e38',
                    firstName: 'Peter',
                    lastName: 'Parker',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
                {
                  role: 'Anesthesiologist',
                  staff: {
                    id: '0b3abbbc-5309-4865-bea7-9ca6fb35bc73',
                    firstName: 'Bruce',
                    lastName: 'Banner',
                    __typename: 'Staff',
                  },
                  __typename: 'CaseStaff',
                },
              ],
              primaryCaseProcedures: [
                {
                  procedure: {
                    id: 'ca22f888-841a-4206-a8a8-456110bd6e88',
                    name: 'PHACOEMULSIFICATION, CATARACT, WITH IOL IMPLANTATION',
                    __typename: 'Procedure',
                  },
                  __typename: 'CaseProcedure',
                },
              ],
              __typename: 'ScheduledCase',
            },
            __typename: 'ApellaCase',
          },
          __typename: 'ApellaCaseEdge',
        },
      ],
      __typename: 'ApellaCaseConnection',
    },
  },
}
