import { anAvailableTimeSlot } from 'src/__generated__/generated-mocks'
import { GetSiteOptionsFilterData } from 'src/modules/site/__mocks__/GetSiteOptionsFilterData'

import { GetAvailableTimeSlots } from '../__generated__'
import { GetBlockTimesData } from './GetBlockTimesData'

export const GetAvailableTimeSlotsData: { data: GetAvailableTimeSlots } = {
  data: {
    availableTimeSlots: [
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0].node
            .id,
        startTime: '2024-09-13T15:10:00+00:00',
        endTime: '2024-09-13T23:00:00+00:00',
        maxAvailableDuration: 'PT6H50M',
        blockTimeIds: [
          GetBlockTimesData.data.blockTimesBulk[1].id,
          GetBlockTimesData.data.blockTimesBulk[0].id,
          GetBlockTimesData.data.blockTimesBulk[3].id,
        ],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0].node
            .id,
        startTime: '2024-09-13T15:10:00+00:00',
        endTime: '2024-09-13T17:30:00+00:00',
        maxAvailableDuration: 'PT2H20M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[1].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0].node
            .id,
        startTime: '2024-09-13T17:30:00+00:00',
        endTime: '2024-09-13T22:00:00+00:00',
        maxAvailableDuration: 'PT4H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[0].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0].node
            .id,
        startTime: '2024-09-14T14:30:00+00:00',
        endTime: '2024-09-14T22:00:00+00:00',
        maxAvailableDuration: 'PT6H27M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1].node
            .id,
        startTime: '2024-09-14T14:30:00+00:00',
        endTime: '2024-09-14T22:00:00+00:00',
        maxAvailableDuration: 'PT6H23M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[2].node
            .id,
        startTime: '2024-09-14T14:30:00+00:00',
        endTime: '2024-09-14T22:00:00+00:00',
        maxAvailableDuration: 'PT6H',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0].node
            .id,
        startTime: '2024-09-15T14:30:00+00:00',
        endTime: '2024-09-15T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1].node
            .id,
        startTime: '2024-09-15T14:30:00+00:00',
        endTime: '2024-09-15T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[2].node
            .id,
        startTime: '2024-09-15T14:30:00+00:00',
        endTime: '2024-09-15T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1].node
            .id,
        startTime: '2024-09-16T14:30:00+00:00',
        endTime: '2024-09-16T16:05:00+00:00',
        maxAvailableDuration: 'PT2H15M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0].node
            .id,
        startTime: '2024-09-16T18:35:00+00:00',
        endTime: '2024-09-16T22:00:00+00:00',
        maxAvailableDuration: 'PT3H25M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[2].node
            .id,
        startTime: '2024-09-16T17:58:00+00:00',
        endTime: '2024-09-16T22:00:00+00:00',
        maxAvailableDuration: 'PT4H',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0].node
            .id,
        startTime: '2024-09-18T14:30:00+00:00',
        endTime: '2024-09-18T16:20:00+00:00',
        maxAvailableDuration: 'PT1H40M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[2].node
            .id,
        startTime: '2024-09-18T20:05:00+00:00',
        endTime: '2024-09-18T22:00:00+00:00',
        maxAvailableDuration: 'PT1H52M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1].node
            .id,
        startTime: '2024-09-18T17:25:00+00:00',
        endTime: '2024-09-18T22:00:00+00:00',
        maxAvailableDuration: 'PT4H',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0].node
            .id,
        startTime: '2024-09-18T16:10:00+00:00',
        endTime: '2024-09-18T22:00:00+00:00',
        maxAvailableDuration: 'PT5H20M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[2].node
            .id,
        startTime: '2024-09-19T19:25:00+00:00',
        endTime: '2024-09-19T22:00:00+00:00',
        maxAvailableDuration: 'PT2H',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1].node
            .id,
        startTime: '2024-09-19T18:50:00+00:00',
        endTime: '2024-09-19T22:00:00+00:00',
        maxAvailableDuration: 'PT2H10M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1].node
            .id,
        startTime: '2024-09-19T15:30:00+00:00',
        endTime: '2024-09-19T17:30:00+00:00',
        maxAvailableDuration: 'PT2H',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1].node
            .id,
        startTime: '2024-09-20T17:55:00+00:00',
        endTime: '2024-09-20T22:00:00+00:00',
        maxAvailableDuration: 'PT4H',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[2].node
            .id,
        startTime: '2024-09-20T17:00:00+00:00',
        endTime: '2024-09-20T22:00:00+00:00',
        maxAvailableDuration: 'PT4H',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0].node
            .id,
        startTime: '2024-09-21T14:30:00+00:00',
        endTime: '2024-09-21T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1].node
            .id,
        startTime: '2024-09-21T14:30:00+00:00',
        endTime: '2024-09-21T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[2].node
            .id,
        startTime: '2024-09-21T14:30:00+00:00',
        endTime: '2024-09-21T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId: 'site-02-01',
        startTime: '2024-09-22T14:30:00+00:00',
        endTime: '2024-09-22T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1].node
            .id,
        startTime: '2024-09-22T14:30:00+00:00',
        endTime: '2024-09-22T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId:
          GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[2].node
            .id,
        startTime: '2024-09-22T14:30:00+00:00',
        endTime: '2024-09-22T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
      anAvailableTimeSlot({
        roomId: 'site-02-01',
        startTime: '2024-09-23T14:30:00+00:00',
        endTime: '2024-09-23T22:00:00+00:00',
        maxAvailableDuration: 'PT7H30M',
        blockTimeIds: [GetBlockTimesData.data.blockTimesBulk[2].id],
      }),
    ],
  },
}

export const GetAvailableTimeSlotsDataNoResults: {
  data: GetAvailableTimeSlots
} = {
  data: {
    availableTimeSlots: [],
  },
}
