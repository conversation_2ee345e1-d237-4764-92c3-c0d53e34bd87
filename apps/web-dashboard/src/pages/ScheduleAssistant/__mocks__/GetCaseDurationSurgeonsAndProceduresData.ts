import {
  GetCaseDurationProcedures,
  GetCaseDurationSurgeons,
} from '../__generated__'

export const GetCaseDurationSurgeonsData: {
  data: GetCaseDurationSurgeons
} = {
  data: {
    caseDurationSurgeons: {
      edges: [
        {
          node: {
            surgeonId: '9c4e8601-6674-4cd3-aecc-4184165ae28f',
            surgeonName: 'Thanos',
            __typename: 'CaseDurationSurgeonOption',
          },
          __typename: 'CaseDurationSurgeonOptionEdge',
        },
        {
          node: {
            surgeonId: '9c4e8601-6674-4cd3-aecc-4184165ae28e',
            surgeonName: 'Venom',
            __typename: 'CaseDurationSurgeonOption',
          },
          __typename: 'CaseDurationSurgeonOptionEdge',
        },
      ],
      __typename: 'CaseDurationSurgeonOptionConnection',
    },
  },
}

export const GetCaseDurationProceduresData: {
  data: GetCaseDurationProcedures
} = {
  data: {
    caseDurationProcedures: {
      edges: [
        {
          node: {
            procedureName: 'Remove pink gem',
            __typename: 'CaseDurationProcedureOption',
          },
          __typename: 'CaseDurationProcedureOptionEdge',
        },
        {
          node: {
            procedureName: 'Remove blue gem',
            __typename: 'CaseDurationProcedureOption',
          },
          __typename: 'CaseDurationProcedureOptionEdge',
        },
        {
          node: {
            procedureName: 'Venom Punch',
            __typename: 'CaseDurationProcedureOption',
          },
          __typename: 'CaseDurationProcedureOptionEdge',
        },
        {
          node: {
            procedureName: 'Venom Heavy Punch',
            __typename: 'CaseDurationProcedureOption',
          },
          __typename: 'CaseDurationProcedureOptionEdge',
        },
        {
          node: {
            procedureName: 'Venom Kick',
            __typename: 'CaseDurationProcedureOption',
          },
          __typename: 'CaseDurationProcedureOptionEdge',
        },
      ],
      __typename: 'CaseDurationProcedureOptionConnection',
    },
  },
}
