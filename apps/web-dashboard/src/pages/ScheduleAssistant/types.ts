import { FORECASTING_REPORT_DATA_USED_IN_TRAINING } from 'src/modules/cube/types/dataCubes'

export type CubeQueryResultType = {
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM_ID]?: string
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.PROCEDURE_LIST]?: string
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.FIRST_PRIMARY_SURGEON]?: string
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.SURGEON_LIST]?: string
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.CASE_ID]?: string
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.APELLA_CASE_ID]?: string
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.SITE]?: string
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.ROOM]?: string
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.START_TIME_LOCAL]?: string
  [FORECASTING_REPORT_DATA_USED_IN_TRAINING.DURATION]?: number
}

export type RecentCasesCacheEntry = {
  data: CubeQueryResultType[]
  expire: number
}
export type RecentCasesCache = Record<string, RecentCasesCacheEntry>

export enum Complexity {
  Standard = 'standard',
  Complex = 'complex',
}

export function isComplexity(value: unknown): value is Complexity {
  return value === Complexity.Standard || value === Complexity.Complex
}
