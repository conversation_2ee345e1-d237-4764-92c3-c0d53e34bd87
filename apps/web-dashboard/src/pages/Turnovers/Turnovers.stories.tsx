import 'react-loading-skeleton/dist/skeleton.css'

import { ApolloC<PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import { Meta, StoryObj } from '@storybook/react'
import { graphql, http, HttpResponse, delay } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'
import { GetSiteOptionsFilterData } from 'src/modules/site/__mocks__/GetSiteOptionsFilterData'
import MockLDProvider from 'src/test/MockLDProvider'
import { modes } from 'src/test/storybookHelpers'

import { GetCurrentUserData, UserType } from '../test_mocks/GetCurrentUserData'
import {
  MockedTurnoverDataNone,
  MockTurnoverData,
} from './mock/GetTurnoverData'
import { Turnovers } from './Turnovers'

const ldParams = {}

const mockedClient = new ApolloClient({
  uri: 'https://your-graphql-endpoint',
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
    query: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
  },
})

const meta: Meta<typeof Turnovers> = {
  title: 'Pages/Turnovers',
  component: Turnovers,
  decorators: [
    (Story) => (
      <TimezoneProvider>
        <MockLDProvider flags={ldParams}>
          <ApolloProvider client={mockedClient}>
            <Story />
          </ApolloProvider>
        </MockLDProvider>
      </TimezoneProvider>
    ),
    withRouter,
  ],
  parameters: {
    chromatic: {
      modes: {
        mobile: modes.mobile,
        xLarge: modes.xLarge,
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof Turnovers>
const routing = {
  path: '/schedule/turnovers',
  handle: 'Turnovers',
}

const msw = (
  wait?: number,
  { getTurnoverData = MockTurnoverData, userType = UserType.ALL_ACCESS } = {}
) => ({
  handlers: [
    http.get('https://events.launchdarkly.com/events/diagnostic/', () => {
      return HttpResponse.json({})
    }),
    http.get('https://app.launchdarkly.com/sdk/goals/', () => {
      return HttpResponse.json({})
    }),
    graphql.query('GetCurrentUser', () => {
      const userData = GetCurrentUserData(userType)
      return HttpResponse.json(userData)
    }),
    graphql.query('GetTurnoverData', async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(getTurnoverData)
    }),
    graphql.query('GetSiteOptionsFilter', async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
  ],
})

export const TurnoversDashboard: Story = {
  parameters: {
    date: new Date('2024-10-23T08:35:58.518-07:00'),
    msw: msw(0, { userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule/turnovers',
      },
      routing,
    },
  },
}

export const TurnoversDashboardNoData: Story = {
  parameters: {
    date: new Date(),
    msw: msw(0, {
      getTurnoverData: MockedTurnoverDataNone,
      userType: UserType.ALL_ACCESS,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule/turnovers',
      },
      routing,
    },
  },
}

export const TurnoversDashboardOnly: Story = {
  parameters: {
    date: new Date('2024-10-23T08:35:58.518-07:00'),
    msw: msw(0, { userType: UserType.TURNOVERS_ACCESS_ONLY }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule/turnovers',
      },
      routing,
    },
  },
}
