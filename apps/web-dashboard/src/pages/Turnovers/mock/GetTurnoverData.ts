import {
  CaseStatusName,
  CaseType,
  Room<PERSON>tatus<PERSON>ame,
  TurnoverStatusName,
  TurnoverType,
} from 'src/__generated__/globalTypes'

import { GetTurnoverData } from '../__generated__'

interface MockedData {
  data: GetTurnoverData
}

export const MockTurnoverData: MockedData = {
  data: {
    sites: {
      __typename: 'SiteConnection',
      edges: [
        {
          __typename: 'SiteEdge',
          node: {
            __typename: 'Site',
            name: 'Site 1',
            turnoverGoals: {
              __typename: 'TurnoverGoals',
              goalMinutes: 30,
              maxMinutes: 60,
            },
            rooms: {
              __typename: 'RoomConnection',
              edges: [
                {
                  node: {
                    name: 'Room 1',
                    turnovers: [
                      {
                        __typename: 'Turnover',
                        id: '1',
                        startTime: '2024-10-23T08:13:58.518-07:00',
                        endTime: '2024-10-23T08:54:06.454941-07:00',
                        type: TurnoverType.LIVE,
                        status: {
                          __typename: 'Turnover<PERSON>tatusGraphene',
                          name: TurnoverStatus<PERSON>ame.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:1',
                          type: CaseType.LIVE,
                          startTime: '2024-10-23T08:54:58.518006-07:00',
                          endTime: '2024-10-23T09:54:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SCHEDULED,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '1',
                            scheduledStartTime:
                              '2024-10-23T08:54:56.234750-07:00',
                            scheduledEndTime:
                              '2024-10-23T09:54:06.454941-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'JAMES',
                                  lastName: 'SMITH',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'ALLY',
                                  lastName: 'JONES',
                                },
                              },
                            ],
                            isAddOn: false,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                    ],
                    status: {
                      name: RoomStatusName.TURNOVER,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus',
                    },
                    __typename: 'Room',
                    id: '1',
                  },

                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    name: 'Room 2',
                    turnovers: [
                      {
                        __typename: 'Turnover',
                        id: '2',
                        startTime: '2024-10-23T08:13:58.518-07:00',
                        endTime: '2024-10-23T08:54:06.454941-07:00',
                        type: TurnoverType.LIVE,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:1',
                          type: CaseType.LIVE,
                          startTime: '2024-10-23T09:00:58.518006-07:00',
                          endTime: '2024-10-23T09:54:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.PRE_PROCEDURE,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '1',
                            scheduledStartTime:
                              '2024-10-23T08:54:56.234750-07:00',
                            scheduledEndTime:
                              '2024-10-23T09:54:06.454941-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'MARK',
                                  lastName: 'JACOBS',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'ANN',
                                  lastName: 'BAKER',
                                },
                              },
                            ],
                            isAddOn: false,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                    ],
                    status: {
                      name: RoomStatusName.TURNOVER,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus',
                    },
                    __typename: 'Room',
                    id: '2',
                  },

                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    name: 'Room 3',
                    turnovers: [
                      {
                        __typename: 'Turnover',
                        id: '3',
                        startTime: '2024-10-23T08:00:58.518-07:00',
                        endTime: '2024-10-23T08:15:06.454941-07:00',
                        type: TurnoverType.LIVE,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:1',
                          type: CaseType.LIVE,
                          startTime: '2024-10-23T08:30:58.518006-07:00',
                          endTime: '2024-10-23T09:00:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.PREP,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '1',
                            scheduledStartTime:
                              '2024-10-23T08:15:58.518006-07:00',
                            scheduledEndTime:
                              '2024-10-23T09:00:58.518006-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'ANGELA',
                                  lastName: 'DAVIS',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'JAMES',
                                  lastName: 'JOHNSON',
                                },
                              },
                            ],
                            isAddOn: true,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                    ],
                    status: {
                      name: RoomStatusName.TURNOVER,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus',
                    },
                    __typename: 'Room',
                    id: '3',
                  },

                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    name: 'Room 4',
                    turnovers: [
                      {
                        __typename: 'Turnover',
                        id: '4',
                        startTime: '2024-10-23T08:00:58.518-07:00',
                        endTime: '2024-10-23T08:15:06.454941-07:00',
                        type: TurnoverType.LIVE,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:1',
                          type: CaseType.LIVE,
                          startTime: '2024-10-23T08:30:58.518006-07:00',
                          endTime: '2024-10-23T09:00:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.PRE_PROCEDURE,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '1',
                            scheduledStartTime:
                              '2024-10-23T08:15:58.518006-07:00',
                            scheduledEndTime:
                              '2024-10-23T09:00:58.518006-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'JENNIFER',
                                  lastName: 'MILLER',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'IAN',
                                  lastName: 'CLARK',
                                },
                              },
                            ],
                            isAddOn: true,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                    ],
                    status: {
                      name: RoomStatusName.TURNOVER,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus',
                    },
                    __typename: 'Room',
                    id: '4',
                  },

                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    name: 'Room 5',
                    turnovers: [
                      {
                        __typename: 'Turnover',
                        id: '5',
                        startTime: '2024-10-23T08:15:06.454941-07:00',
                        endTime: '2024-10-23T08:15:06.454941-07:00',
                        type: TurnoverType.LIVE,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:1',
                          type: CaseType.LIVE,
                          startTime: '2024-10-23T09:15:56.234750-07:00',
                          endTime: '2024-10-23T10:00:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SCHEDULED,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '1',
                            scheduledStartTime:
                              '2024-10-23T08:15:58.518006-07:00',
                            scheduledEndTime:
                              '2024-10-23T09:00:58.518006-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'SUSAN',
                                  lastName: 'CONNER',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'SALLY',
                                  lastName: 'MAE',
                                },
                              },
                            ],
                            isAddOn: false,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'THIS IS A REALLY LONG PROCEDURE NAME',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                    ],
                    status: {
                      name: RoomStatusName.TURNOVER,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus',
                    },
                    __typename: 'Room',
                    id: '5',
                  },

                  __typename: 'RoomEdge',
                },
              ],
            },
          },
        },
        {
          __typename: 'SiteEdge',
          node: {
            __typename: 'Site',
            name: 'Site 2',
            turnoverGoals: {
              __typename: 'TurnoverGoals',
              goalMinutes: null,
              maxMinutes: 60,
            },
            rooms: {
              __typename: 'RoomConnection',
              edges: [
                {
                  node: {
                    name: 'Room 1',
                    turnovers: [
                      {
                        __typename: 'Turnover',
                        id: '1',
                        startTime: '2024-10-23T08:13:58.518-07:00',
                        endTime: '2024-10-23T08:54:06.454941-07:00',
                        type: TurnoverType.LIVE,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:1',
                          type: CaseType.LIVE,
                          startTime: '2024-10-23T08:54:58.518006-07:00',
                          endTime: '2024-10-23T09:54:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SCHEDULED,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '1',
                            scheduledStartTime:
                              '2024-10-23T08:54:56.234750-07:00',
                            scheduledEndTime:
                              '2024-10-23T09:54:06.454941-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'JOHN',
                                  lastName: 'SMITH',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'JOHN',
                                  lastName: 'JONES',
                                },
                              },
                            ],
                            isAddOn: false,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                      {
                        __typename: 'Turnover',
                        id: '2',
                        startTime: '2024-10-23T09:54:58.518-07:00',
                        endTime: '2024-10-23T10:30:06.454941-07:00',
                        type: TurnoverType.FORECAST,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:2',
                          type: CaseType.FORECAST,
                          startTime: '2024-10-23T10:30:58.518006-07:00',
                          endTime: '2024-10-23T11:30:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SCHEDULED,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '2',
                            scheduledStartTime:
                              '2024-10-23T10:30:58.518006-07:00',
                            scheduledEndTime:
                              '2024-10-23T11:30:06.454941-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'MARY',
                                  lastName: 'SMITH',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'DAVID',
                                  lastName: 'BROWN',
                                },
                              },
                            ],
                            isAddOn: false,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'A REALLY LONG PROCEDURE WITH AN ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                      {
                        __typename: 'Turnover',
                        id: '3',
                        startTime: '2024-10-23T11:30:58.518-07:00',
                        endTime: '2024-10-23T12:00:06.454941-07:00',
                        type: TurnoverType.FORECAST,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:3',
                          type: CaseType.FORECAST,
                          startTime: '2024-10-23T12:00:58.518006-07:00',
                          endTime: '2024-10-23T01:00:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SCHEDULED,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '2',
                            scheduledStartTime:
                              '2024-10-23T12:00:58.518006-07:00',
                            scheduledEndTime:
                              '2024-10-23T01:00:06.454941-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'MARIA',
                                  lastName: 'MARTINEZ',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'ROBERT',
                                  lastName: 'MILLER',
                                },
                              },
                            ],
                            isAddOn: false,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                      {
                        __typename: 'Turnover',
                        id: '4',
                        startTime: '2024-10-23T01:00:58.518-07:00',
                        endTime: '2024-10-23T01:30:06.454941-07:00',
                        type: TurnoverType.FORECAST,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:4',
                          type: CaseType.FORECAST,
                          startTime: '2024-10-23T01:30:06.454941-07:00',
                          endTime: '2024-10-23T02:30:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SCHEDULED,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '4',
                            scheduledStartTime:
                              '2024-10-23T01:30:06.454941-07:00',
                            scheduledEndTime:
                              '2024-10-23T02:30:06.454941-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'WILL',
                                  lastName: 'SMITH',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'MARY',
                                  lastName: 'DAVIS',
                                },
                              },
                            ],
                            isAddOn: false,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                    ],
                    status: {
                      name: RoomStatusName.TURNOVER,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus',
                    },
                    __typename: 'Room',
                    id: '6',
                  },

                  __typename: 'RoomEdge',
                },

                {
                  node: {
                    name: 'Room 3',
                    turnovers: [
                      {
                        __typename: 'Turnover',
                        id: '3',
                        startTime: '2024-10-23T08:00:58.518-07:00',
                        endTime: '2024-10-23T08:15:06.454941-07:00',
                        type: TurnoverType.LIVE,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:1',
                          type: CaseType.LIVE,
                          startTime: '2024-10-23T08:30:58.518006-07:00',
                          endTime: '2024-10-23T09:00:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.PREP,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '1',
                            scheduledStartTime:
                              '2024-10-23T08:15:58.518006-07:00',
                            scheduledEndTime:
                              '2024-10-23T09:00:58.518006-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'ANGELA',
                                  lastName: 'DAVIS',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'JAMES',
                                  lastName: 'JOHNSON',
                                },
                              },
                            ],
                            isAddOn: true,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                    ],
                    status: {
                      name: RoomStatusName.TURNOVER,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus',
                    },
                    __typename: 'Room',
                    id: '7',
                  },

                  __typename: 'RoomEdge',
                },

                {
                  node: {
                    name: 'Room 5',
                    turnovers: [
                      {
                        __typename: 'Turnover',
                        id: '5',
                        startTime: '2024-10-23T08:15:06.454941-07:00',
                        endTime: '2024-10-23T08:15:06.454941-07:00',
                        type: TurnoverType.LIVE,
                        status: {
                          __typename: 'TurnoverStatusGraphene',
                          name: TurnoverStatusName.CLEANING,
                        },
                        followingCase: {
                          __typename: 'ApellaCase',
                          id: 'case:1',
                          type: CaseType.LIVE,
                          startTime: '2024-10-23T09:15:56.234750-07:00',
                          endTime: '2024-10-23T10:00:06.454941-07:00',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SCHEDULED,
                          },
                          case: {
                            __typename: 'ScheduledCase',
                            id: '1',
                            scheduledStartTime:
                              '2024-10-23T08:15:58.518006-07:00',
                            scheduledEndTime:
                              '2024-10-23T09:00:58.518006-07:00',
                            caseStaff: [
                              {
                                __typename: 'CaseStaff',
                                role: 'Primary Surgeon',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'SUSAN',
                                  lastName: 'CONNER',
                                },
                              },
                              {
                                __typename: 'CaseStaff',
                                role: 'ANESTHESIA',
                                staff: {
                                  __typename: 'Staff',
                                  id: '1',
                                  firstName: 'SALLY',
                                  lastName: 'MAE',
                                },
                              },
                            ],
                            isAddOn: false,
                            primaryCaseProcedures: [
                              {
                                __typename: 'CaseProcedure',
                                anesthesia: {
                                  __typename: 'Anesthesia',
                                  name: 'General',
                                },
                                procedure: {
                                  __typename: 'Procedure',
                                  id: '1',
                                  name: 'ANGIOGRAM',
                                },
                              },
                            ],
                            isInFlipRoom: null,
                            precedingCase: null,
                          },
                        },
                        precedingCase: {
                          __typename: 'ApellaCase',
                          status: {
                            __typename: 'ApellaCaseStatus',
                            name: CaseStatusName.SURGERY,
                          },
                        },
                      },
                    ],
                    status: {
                      name: RoomStatusName.TURNOVER,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus',
                    },
                    __typename: 'Room',
                    id: '8',
                  },

                  __typename: 'RoomEdge',
                },
              ],
            },
          },
        },
      ],
    },
  },
}

export const MockedTurnoverDataNone: MockedData = {
  data: {
    sites: {
      __typename: 'SiteConnection',
      edges: [
        {
          __typename: 'SiteEdge',
          node: {
            __typename: 'Site',
            name: 'Site 1',
            turnoverGoals: {
              __typename: 'TurnoverGoals',
              goalMinutes: 30,
              maxMinutes: 60,
            },
            rooms: {
              __typename: 'RoomConnection',
              edges: [
                {
                  node: {
                    name: 'Room 1',
                    turnovers: [],
                    status: {
                      __typename: 'RoomStatus',
                      name: RoomStatusName.CLOSED,
                      inProgressApellaCase: null,
                    },
                    __typename: 'Room',
                    id: '9',
                  },
                  __typename: 'RoomEdge',
                },
              ],
            },
          },
        },
      ],
    },
  },
}
