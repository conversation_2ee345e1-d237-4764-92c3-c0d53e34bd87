import { useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { ApellaDateTimeFormats } from '@apella/component-library'
import {
  TurnoverType,
  CaseStatusName,
  RoomStatusName,
  TurnoverStatusName,
} from 'src/__generated__/globalTypes'
import { useTimezone } from 'src/Contexts'
import { PRIMARY_SURGEON_ROLES } from 'src/utils/roles'
import { usePolling } from 'src/utils/usePolling'

import { SLOW_POLL_INTERVAL_MS } from '../Live/consts'
import { GetTurnoverData, GetTurnoverDataVariables } from './__generated__'
import { GET_TURNOVER_DATA } from './queries'

export interface Turnover {
  endTime: string
  followingCase: {
    id: string
    case: {
      scheduledStartTime: string
      isAddOn: boolean | null
    } | null
  }
  id: string
  startTime: string
  type: TurnoverType
}
export interface TurnoverDashboardInterface {
  actualStartTime: string
  anesthesiaStaff: string[]
  anesthesiaType: string[]
  followingCaseStart: string | undefined
  isAddOn: boolean | null
  isInFlipRoom: boolean | null
  patientStatus: CaseStatusName
  precedingCaseId: string | undefined
  precedingCaseStatus: CaseStatusName
  procedure: (string | undefined)[]
  roomId: string
  roomName: string
  roomStatusName: RoomStatusName
  siteName: string
  surgeonName: string[]
  turnoverEndTime: string
  turnoverGoal: number | null
  turnoverId: string
  turnoverMax: number
  turnoverStartTime: string
  turnoverStatus?: TurnoverStatusName
  type: TurnoverType
}

export interface Staff {
  role: string | null
  staff: {
    id: string
    firstName: string
    lastName: string
  }
}

export interface ApellaCase {
  node: {
    case: {
      caseStaff: Array<{
        role: string | null
        staff: {
          __typename: 'Staff'
          firstName: string
          lastName: string
        }
      }>
      primaryCaseProcedures: Array<{
        anesthesia: {
          name: string
        } | null
        procedure: {
          name: string
        }
      }>
    } | null
    status: {
      name: CaseStatusName
    }
    room: {
      status: {
        name: RoomStatusName
      }
    }
    startTime: string
  }
}

export const useTurnoverState = ({
  minEndTime,
  maxStartTime,
  siteIds,
  roomIds,
  skip,
  showClosedRooms,
}: {
  minEndTime: string
  maxStartTime: string
  siteIds?: string[]
  roomIds?: string[]
  skip?: boolean
  showClosedRooms?: boolean
}): {
  turnoverDashboardInfo: TurnoverDashboardInterface[]
  turnoverLoading: boolean
} => {
  const roomStatusFilter =
    showClosedRooms === false
      ? Object.values(RoomStatusName).filter(
          (status) => status !== RoomStatusName.CLOSED
        )
      : undefined

  const {
    data: turnoverData,
    startPolling,
    stopPolling,
    refetch,
    loading,
  } = useQuery<GetTurnoverData, GetTurnoverDataVariables>(GET_TURNOVER_DATA, {
    variables: {
      siteIds: siteIds ?? [],
      roomIds,
      minEndTime,
      maxStartTime,
      statusFilter: roomStatusFilter,
    },
    fetchPolicy: 'no-cache',
  })
  usePolling({
    refetch,
    startPolling,
    stopPolling,
    interval: SLOW_POLL_INTERVAL_MS,
    skip,
  })

  const { timezone } = useTimezone()
  const turnoverDashboardInfo: TurnoverDashboardInterface[] = useMemo(() => {
    return (
      turnoverData?.sites?.edges.flatMap((site) =>
        site.node.rooms.edges.flatMap((room) => {
          // This is room scope
          const roomStatusName = room.node.status.name
          return room.node.turnovers.map((turnover) => {
            const caseStaff = turnover.followingCase.case?.caseStaff || []

            const patientStatus = turnover.followingCase.status.name
            const actualStartTime = DateTime.fromISO(
              turnover.followingCase.startTime
            )
              .setZone(timezone)
              .toLocaleString(ApellaDateTimeFormats.TIME)

            const anesthesiaStaff = caseStaff
              ?.filter((cs) => cs.role === 'ANESTHESIA')
              .map((cs) => `${cs.staff.lastName}, ${cs.staff.firstName}`)
            const surgeonName = caseStaff
              ?.filter(
                (cs) => !!cs.role && PRIMARY_SURGEON_ROLES.includes(cs.role)
              )
              .map((cs) => `${cs.staff.lastName}, ${cs.staff.firstName}`)

            const anesthesiaType =
              turnover.followingCase.case?.primaryCaseProcedures
                .map((p) => p.anesthesia?.name)
                .filter(Boolean) ?? []
            const procedure =
              turnover.followingCase.case?.primaryCaseProcedures.map(
                (p) => p.procedure.name
              ) ?? []

            return {
              turnoverId: turnover.id,
              turnoverStartTime: turnover.startTime,
              turnoverEndTime: turnover.endTime,
              type: turnover.type,
              followingCaseStart: turnover.followingCase.case
                ?.scheduledStartTime
                ? DateTime.fromISO(
                    turnover.followingCase.case.scheduledStartTime
                  )
                    .setZone(timezone)
                    .toLocaleString(ApellaDateTimeFormats.TIME)
                : undefined,
              precedingCaseStatus: turnover.precedingCase?.status.name,
              precedingCaseId: turnover.followingCase.case?.precedingCase?.id,
              isAddOn: turnover.followingCase.case?.isAddOn ?? null,
              isInFlipRoom: turnover.followingCase.case?.isInFlipRoom ?? null,
              turnoverGoal: site.node.turnoverGoals.goalMinutes,
              turnoverMax: site.node.turnoverGoals.maxMinutes,
              siteName: site.node.name,
              roomName: room.node.name,
              actualStartTime,
              anesthesiaStaff,
              surgeonName,
              patientStatus,
              anesthesiaType,
              procedure,
              roomStatusName,
              turnoverStatus: turnover.status?.name ?? undefined,
              roomId: room.node.id,
            }
          })
        })
      ) ?? []
    )
  }, [turnoverData, timezone])
  return {
    turnoverDashboardInfo,
    turnoverLoading: loading,
  }
}
