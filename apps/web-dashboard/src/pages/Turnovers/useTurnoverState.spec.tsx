import { useQuery } from '@apollo/client'
import { renderHook } from '@testing-library/react'

import {
  CaseStatusName,
  RoomStatusName,
  TurnoverStatusName,
  TurnoverType,
} from 'src/__generated__/globalTypes'
import { MockAppProvider } from 'src/test/MockAppProvider'
import { mockUseQueryResult } from 'src/test/useQueryMock'

import { GetTurnoverData, GetTurnoverDataVariables } from './__generated__'
import {
  MockedTurnoverDataNone,
  MockTurnoverData,
} from './mock/GetTurnoverData'
import { useTurnoverState } from './useTurnoverState'

vi.mock('@apollo/client')
const mockedUseQuery =
  vi.mocked<typeof useQuery<GetTurnoverData, GetTurnoverDataVariables>>(
    useQuery
  )

describe('useTurnoverState', () => {
  it('should retrieve turnover dashboard data', () => {
    mockedUseQuery.mockReturnValue(
      mockUseQueryResult({ data: MockTurnoverData.data })
    )

    const { result } = renderHook(
      () =>
        useTurnoverState({
          minEndTime: '2023-09-1T00:00:00.000-05:00',
          maxStartTime: '2024-10-18T23:59:59.999-05:00',
          siteIds: ['Site 1'],
          roomIds: ['Room 1'],
          skip: true,
        }),
      { wrapper: MockAppProvider }
    )

    expect(result.current.turnoverDashboardInfo).toEqual([
      {
        turnoverId: '1',
        turnoverGoal: 30,
        turnoverMax: 60,
        siteName: 'Site 1',
        roomName: 'Room 1',
        roomId: '1',
        actualStartTime: '08:54',
        turnoverStartTime: '2024-10-23T08:13:58.518-07:00',
        turnoverEndTime: '2024-10-23T08:54:06.454941-07:00',
        type: TurnoverType.LIVE,
        followingCaseStart: '08:54',
        anesthesiaType: ['General'],
        anesthesiaStaff: ['JONES, ALLY'],
        surgeonName: ['SMITH, JAMES'],
        procedure: ['ANGIOGRAM'],
        patientStatus: CaseStatusName.SCHEDULED,
        isAddOn: false,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        roomStatusName: RoomStatusName.TURNOVER,
        turnoverStatus: TurnoverStatusName.CLEANING,
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        turnoverId: '2',
        turnoverGoal: 30,
        turnoverMax: 60,
        siteName: 'Site 1',
        roomName: 'Room 2',
        roomId: '2',
        actualStartTime: '09:00',
        turnoverStartTime: '2024-10-23T08:13:58.518-07:00',
        turnoverEndTime: '2024-10-23T08:54:06.454941-07:00',
        type: TurnoverType.LIVE,
        followingCaseStart: '08:54',
        anesthesiaType: ['General'],
        anesthesiaStaff: ['BAKER, ANN'],
        surgeonName: ['JACOBS, MARK'],
        procedure: ['ANGIOGRAM'],
        patientStatus: CaseStatusName.PRE_PROCEDURE,
        isAddOn: false,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        roomStatusName: RoomStatusName.TURNOVER,
        turnoverStatus: TurnoverStatusName.CLEANING,
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        turnoverId: '3',
        turnoverGoal: 30,
        turnoverMax: 60,
        siteName: 'Site 1',
        roomName: 'Room 3',
        roomId: '3',
        actualStartTime: '08:30',
        turnoverStartTime: '2024-10-23T08:00:58.518-07:00',
        turnoverEndTime: '2024-10-23T08:15:06.454941-07:00',
        type: TurnoverType.LIVE,
        followingCaseStart: '08:15',
        anesthesiaType: ['General'],
        anesthesiaStaff: ['JOHNSON, JAMES'],
        surgeonName: ['DAVIS, ANGELA'],
        procedure: ['ANGIOGRAM'],
        patientStatus: CaseStatusName.PREP,
        isAddOn: true,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        roomStatusName: RoomStatusName.TURNOVER,
        turnoverStatus: TurnoverStatusName.CLEANING,
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        turnoverId: '4',
        turnoverGoal: 30,
        turnoverMax: 60,
        siteName: 'Site 1',
        roomName: 'Room 4',
        roomId: '4',
        actualStartTime: '08:30',
        turnoverStartTime: '2024-10-23T08:00:58.518-07:00',
        turnoverEndTime: '2024-10-23T08:15:06.454941-07:00',
        type: TurnoverType.LIVE,
        followingCaseStart: '08:15',
        anesthesiaType: ['General'],
        anesthesiaStaff: ['CLARK, IAN'],
        surgeonName: ['MILLER, JENNIFER'],
        procedure: ['ANGIOGRAM'],
        patientStatus: CaseStatusName.PRE_PROCEDURE,
        isAddOn: true,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        roomStatusName: RoomStatusName.TURNOVER,
        turnoverStatus: TurnoverStatusName.CLEANING,
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        turnoverId: '5',
        turnoverGoal: 30,
        turnoverMax: 60,
        siteName: 'Site 1',
        roomName: 'Room 5',
        roomId: '5',
        actualStartTime: '09:15',
        turnoverStartTime: '2024-10-23T08:15:06.454941-07:00',
        turnoverEndTime: '2024-10-23T08:15:06.454941-07:00',
        type: TurnoverType.LIVE,
        followingCaseStart: '08:15',
        anesthesiaType: ['General'],
        anesthesiaStaff: ['MAE, SALLY'],
        surgeonName: ['CONNER, SUSAN'],
        procedure: ['THIS IS A REALLY LONG PROCEDURE NAME'],
        patientStatus: CaseStatusName.SCHEDULED,
        isAddOn: false,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        roomStatusName: RoomStatusName.TURNOVER,
        turnoverStatus: TurnoverStatusName.CLEANING,
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        turnoverId: '1',
        turnoverGoal: null,
        turnoverMax: 60,
        siteName: 'Site 2',
        roomName: 'Room 1',
        roomId: '6',
        actualStartTime: '08:54',
        turnoverStartTime: '2024-10-23T08:13:58.518-07:00',
        turnoverEndTime: '2024-10-23T08:54:06.454941-07:00',
        type: TurnoverType.LIVE,
        followingCaseStart: '08:54',
        anesthesiaType: ['General'],
        anesthesiaStaff: ['JONES, JOHN'],
        surgeonName: ['SMITH, JOHN'],
        procedure: ['ANGIOGRAM'],
        patientStatus: CaseStatusName.SCHEDULED,
        isAddOn: false,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        roomStatusName: RoomStatusName.TURNOVER,
        turnoverStatus: TurnoverStatusName.CLEANING,
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        actualStartTime: '10:30',
        anesthesiaStaff: ['BROWN, DAVID'],
        anesthesiaType: ['General'],
        followingCaseStart: '10:30',
        isAddOn: false,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        patientStatus: 'SCHEDULED',
        procedure: ['A REALLY LONG PROCEDURE WITH AN ANGIOGRAM'],
        roomName: 'Room 1',
        roomId: '6',
        roomStatusName: 'TURNOVER',
        turnoverStatus: TurnoverStatusName.CLEANING,
        siteName: 'Site 2',
        surgeonName: ['SMITH, MARY'],
        turnoverGoal: null,
        turnoverId: '2',
        turnoverMax: 60,
        turnoverStartTime: '2024-10-23T09:54:58.518-07:00',
        turnoverEndTime: '2024-10-23T10:30:06.454941-07:00',
        type: 'FORECAST',
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        actualStartTime: '12:00',
        anesthesiaStaff: ['MILLER, ROBERT'],
        anesthesiaType: ['General'],
        followingCaseStart: '12:00',
        isAddOn: false,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        patientStatus: 'SCHEDULED',
        procedure: ['ANGIOGRAM'],
        roomName: 'Room 1',
        roomId: '6',
        roomStatusName: 'TURNOVER',
        turnoverStatus: TurnoverStatusName.CLEANING,
        siteName: 'Site 2',
        surgeonName: ['MARTINEZ, MARIA'],
        turnoverGoal: null,
        turnoverId: '3',
        turnoverMax: 60,
        turnoverStartTime: '2024-10-23T11:30:58.518-07:00',
        turnoverEndTime: '2024-10-23T12:00:06.454941-07:00',
        type: 'FORECAST',
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        actualStartTime: '01:30',
        anesthesiaStaff: ['DAVIS, MARY'],
        anesthesiaType: ['General'],
        followingCaseStart: '01:30',
        isAddOn: false,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        patientStatus: 'SCHEDULED',
        procedure: ['ANGIOGRAM'],
        roomName: 'Room 1',
        roomId: '6',
        roomStatusName: 'TURNOVER',
        turnoverStatus: TurnoverStatusName.CLEANING,
        siteName: 'Site 2',
        surgeonName: ['SMITH, WILL'],
        turnoverGoal: null,
        turnoverId: '4',
        turnoverMax: 60,
        turnoverStartTime: '2024-10-23T01:00:58.518-07:00',
        turnoverEndTime: '2024-10-23T01:30:06.454941-07:00',
        type: 'FORECAST',
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        actualStartTime: '08:30',
        anesthesiaStaff: ['JOHNSON, JAMES'],
        anesthesiaType: ['General'],
        followingCaseStart: '08:15',
        patientStatus: 'PREP',
        procedure: ['ANGIOGRAM'],
        roomName: 'Room 3',
        roomId: '7',
        siteName: 'Site 2',
        surgeonName: ['DAVIS, ANGELA'],
        turnoverGoal: null,
        turnoverId: '3',
        turnoverMax: 60,
        turnoverStartTime: '2024-10-23T08:00:58.518-07:00',
        turnoverEndTime: '2024-10-23T08:15:06.454941-07:00',
        type: TurnoverType.LIVE,
        isAddOn: true,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        roomStatusName: RoomStatusName.TURNOVER,
        turnoverStatus: TurnoverStatusName.CLEANING,
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
      {
        actualStartTime: '09:15',
        anesthesiaStaff: ['MAE, SALLY'],
        anesthesiaType: ['General'],
        followingCaseStart: '08:15',
        patientStatus: CaseStatusName.SCHEDULED,
        procedure: ['ANGIOGRAM'],
        roomName: 'Room 5',
        roomId: '8',
        siteName: 'Site 2',
        surgeonName: ['CONNER, SUSAN'],
        turnoverGoal: null,
        turnoverId: '5',
        turnoverMax: 60,
        turnoverStartTime: '2024-10-23T08:15:06.454941-07:00',
        turnoverEndTime: '2024-10-23T08:15:06.454941-07:00',
        type: TurnoverType.LIVE,
        isAddOn: false,
        isInFlipRoom: null,
        precedingCaseId: undefined,
        roomStatusName: RoomStatusName.TURNOVER,
        turnoverStatus: TurnoverStatusName.CLEANING,
        precedingCaseStatus: CaseStatusName.SURGERY,
      },
    ])
  })

  it('should return empty array if no turnovers are found', () => {
    mockedUseQuery.mockReturnValue(
      mockUseQueryResult({
        data: MockedTurnoverDataNone.data,
      })
    )

    const { result } = renderHook(
      () =>
        useTurnoverState({
          minEndTime: '2023-09-1T00:00:00.000-05:00',
          maxStartTime: '2024-10-18T23:59:59.999-05:00',
          siteIds: ['Site 1'],
          roomIds: ['Room 1'],
          skip: true,
        }),
      { wrapper: MockAppProvider }
    )

    expect(result.current.turnoverDashboardInfo).toEqual([])
  })
})

export {}
