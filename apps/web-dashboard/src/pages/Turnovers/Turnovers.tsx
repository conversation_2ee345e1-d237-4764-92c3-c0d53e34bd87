import { Outlet } from 'react-router'

import styled from '@emotion/styled'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { sortBy } from 'lodash'
import { DateTime } from 'luxon'

import {
  ApellaDateTimeFormats,
  formatTimerWithSeconds,
  H3,
  H5,
  mediaQueries,
  P2,
  P3,
  remSpacing,
  Table,
  TBody,
  TD,
  TH,
  THead,
  theme,
  TR,
  Turnover as TurnoverIcon,
} from '@apella/component-library'
import { RoomStatusName, TurnoverType } from 'src/__generated__/globalTypes'
import { ToggleableCaseKey } from 'src/components/Keys'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { useTimezone } from 'src/Contexts'
import { useCurrentUser } from 'src/modules/user/hooks'
import { useCurrentTime } from 'src/utils/useCurrentTime'
import { useOpenVideoBlade } from 'src/utils/useOpenVideoBlade'

import { WebDashboardFeatureFlagSet } from '../../modules/feature/types'
import { AddOnChecker } from '../AddOnChecker'
import { LiveStatusBanner } from '../Live/LiveStatusBanner'
import { CaseStatusPill } from '../Schedule/CaseDetails'
import {
  ScheduleFilterProviderContext,
  useScheduleFilterContext,
} from '../Schedule/ScheduleFilterContext'
import { ScheduleFilterProvider } from '../Schedule/ScheduleFilterProvider'
import { ScheduleFilters } from '../Schedule/ScheduleFilters'
import SchedulePageHeader from '../Schedule/SchedulePageHeader'
import {
  TurnoverDashboardInterface,
  useTurnoverState,
} from './useTurnoverState'

const TurnoversPage = () => {
  const { siteIds, roomIds, minTime, maxTime, showClosedRooms } =
    useScheduleFilterContext()
  const turnoverTypes = [
    { type: TurnoverType.LIVE, label: 'In Progress' },
    { type: TurnoverType.FORECAST, label: 'Upcoming' },
  ]

  const allTurnovers = useTurnoverState({
    minEndTime: minTime,
    maxStartTime: maxTime,
    siteIds,
    roomIds,
    showClosedRooms,
  })

  const inProgressData = allTurnovers.turnoverDashboardInfo.filter(
    (t) => t.type === TurnoverType.LIVE
  )
  const forecastedData = allTurnovers.turnoverDashboardInfo.filter(
    (t) => t.type === TurnoverType.FORECAST
  )

  return (
    <PageContentTemplate maxContainerSize="full">
      <SchedulePageHeader selectedSubNavId={'turnovers'} />
      <ScheduleFilters
        showDateChangeFilter={false}
        showToggleScheduledCases={false}
        showSurgeonFilter={false}
      />

      {turnoverTypes.map(({ type, label }, index) => {
        const data =
          type === TurnoverType.LIVE ? inProgressData : forecastedData
        return (
          <div key={index}>
            <H5>{label}</H5>
            <TurnoverTable
              results={data}
              isLoading={allTurnovers.turnoverLoading}
              type={type}
            />
          </div>
        )
      })}
      <ToggleableCaseKey />
    </PageContentTemplate>
  )
}

export const Turnovers = () => {
  return (
    <ScheduleFilterProvider context={ScheduleFilterProviderContext.TURNOVERS}>
      <TurnoversPage />
      <Outlet />
    </ScheduleFilterProvider>
  )
}

const RoomCol = styled.col({
  width: '14%',
  [mediaQueries.xl]: {
    width: '12%',
  },
})

const ElapsedAndTurnoverStartCol = styled.col({
  width: '14%',
  [mediaQueries.xl]: {
    width: '14%',
  },
})

const FollowStartCol = styled.col({
  width: '14%',
  [mediaQueries.lg]: {
    width: '10%',
  },
})

const FollowCaseCol = styled.col({
  width: '20%',
  [mediaQueries.lg]: {
    width: '22%',
  },
  [mediaQueries.xl]: {
    width: '20%',
  },
})

const FollowAnesthesiaCol = styled.col({
  width: '12%',
})

const PatientStatusCol = styled.col({
  width: '12%',
})

const StatusCol = styled.col({
  width: '10%',
})

const TurnoverTable = ({
  results,
  isLoading,
  type,
}: {
  results: TurnoverDashboardInterface[]
  isLoading: boolean
  type: TurnoverType
}): React.JSX.Element => {
  const currentTime = useCurrentTime()
  const { timezone } = useTimezone()
  const { turnoverStatusesEnabled } = useFlags<WebDashboardFeatureFlagSet>()

  const turnoverWithElapsedTimes = results.map((turnover) => {
    const timeElapsed = currentTime.diff(
      DateTime.fromISO(turnover.turnoverStartTime)
    )
    const formattedTime = formatTimerWithSeconds(timeElapsed)
    return {
      ...turnover,
      elapsedTimes: formattedTime,
    }
  })

  const { permissions: uiPermissions } = useCurrentUser()
  const hasLiveFromSchedulePermission =
    type === TurnoverType.LIVE &&
    uiPermissions?.dashboardLiveFromScheduleEnabled

  const handleOpenVideoBlade = useOpenVideoBlade({ appendParams: true })
  const open = (
    roomId: string,
    turnoverId: string,
    startTime: string,
    endTime: string
  ) => {
    if (hasLiveFromSchedulePermission) {
      handleOpenVideoBlade(roomId, {
        apellaCase: undefined,
        turnover: {
          id: turnoverId,
          startTime: DateTime.fromISO(startTime),
          endTime: DateTime.fromISO(endTime),
        },
      })
    }
  }

  return (
    <div css={{ overflowX: 'auto' }}>
      {isLoading && <LoadingOverlay />}
      <Table>
        <colgroup>
          <RoomCol />
          <StatusCol />
          <ElapsedAndTurnoverStartCol />
          <FollowStartCol />
          <FollowCaseCol />
          <FollowAnesthesiaCol />
          <PatientStatusCol />
        </colgroup>
        <THead>
          <TR>
            <TH>Room</TH>
            {type === TurnoverType.LIVE ? (
              <TH>Room Status</TH>
            ) : (
              <TH>Preceding Case Status</TH>
            )}
            {type === TurnoverType.LIVE ? (
              <TH>Elapsed</TH>
            ) : (
              <TH>
                Turnover Start <br /> (forecasted wheels out)
              </TH>
            )}
            <TH>Follow Start</TH>
            <TH>Follow Case</TH>
            <TH>Follow Anesthesia</TH>
            <TH>Patient Status</TH>
            <TH>Flip/SSSR</TH>
          </TR>
        </THead>
        <TBody>
          {!isLoading && results.length === 0 && (
            <TR>
              <td
                colSpan={100}
                css={{ padding: remSpacing.xlarge, textAlign: 'center' }}
              >
                <TurnoverIcon size="lg" color={theme.palette.gray[40]} />
                {type === TurnoverType.LIVE ? (
                  <H3>No turnovers currently in progress</H3>
                ) : (
                  <H3>No upcoming turnovers</H3>
                )}
              </td>
            </TR>
          )}
          {sortBy(turnoverWithElapsedTimes, [
            'actualStartTime',
            'siteName',
            'roomName',
          ]).map((turnover) => {
            return (
              <TR
                key={turnover.turnoverId}
                onClick={() => {
                  open(
                    turnover.roomId,
                    turnover.turnoverId,
                    turnover.turnoverStartTime,
                    turnover.turnoverEndTime
                  )
                }}
                css={{
                  cursor: hasLiveFromSchedulePermission ? 'pointer' : 'default',
                  '&:hover': {
                    backgroundColor: hasLiveFromSchedulePermission
                      ? theme.palette.gray[10]
                      : undefined,
                  },
                }}
              >
                <TD>
                  <P2 css={{ fontWeight: 'bold' }}>{turnover.roomName}</P2>
                  <P3>{turnover.siteName.toUpperCase()}</P3>
                </TD>
                {type === TurnoverType.LIVE ? (
                  <TD>
                    <div css={{ display: 'flex' }}>
                      <CaseStatusPill
                        status={
                          turnoverStatusesEnabled && turnover.turnoverStatus
                            ? turnover.turnoverStatus
                            : turnover.roomStatusName
                        }
                      />
                    </div>
                  </TD>
                ) : (
                  <TD>
                    <div css={{ display: 'flex' }}>
                      <CaseStatusPill status={turnover.precedingCaseStatus} />
                    </div>
                  </TD>
                )}

                <TD>
                  {type === TurnoverType.LIVE ? (
                    <LiveStatusBanner
                      liveStatus={RoomStatusName.TURNOVER}
                      since={DateTime.fromISO(turnover.turnoverStartTime)}
                      turnoverGoal={
                        turnover.roomStatusName === RoomStatusName.TURNOVER
                          ? turnover.turnoverGoal
                          : undefined
                      }
                      isOnLive={false}
                      emphasizeText
                      isIdleOnTurnoverDash={
                        turnover.roomStatusName !== RoomStatusName.TURNOVER
                      }
                    />
                  ) : (
                    <P2>
                      {DateTime.fromISO(turnover.turnoverStartTime)
                        .setZone(timezone)
                        .toLocaleString(ApellaDateTimeFormats.TIME)}
                    </P2>
                  )}
                </TD>
                <TD>
                  <P3 css={{ fontWeight: 'bold' }}>
                    {turnover.actualStartTime}
                  </P3>
                  <P3>SCH: {turnover.followingCaseStart}</P3>
                </TD>
                <TD>
                  <P3
                    css={{
                      fontWeight: 'bold',
                    }}
                  >
                    {turnover.surgeonName.join('; ')}
                  </P3>
                  <P3>
                    {AddOnChecker(
                      turnover.procedure
                        ? turnover.procedure.filter(Boolean)
                        : [],
                      turnover.isAddOn ?? false,
                      true
                    )}
                  </P3>
                </TD>
                <TD>
                  <P3 css={{ fontWeight: 'bold' }}>
                    {turnover.anesthesiaStaff.join('; ')}
                  </P3>
                  {turnover.anesthesiaType.map((anesthesiaType, i) => (
                    <P3 key={`${anesthesiaType}${i}`}>{anesthesiaType}</P3>
                  ))}
                </TD>
                <TD>
                  <div css={{ display: 'flex' }}>
                    <CaseStatusPill status={turnover.patientStatus} />
                  </div>
                </TD>
                <TD>
                  <P3>
                    {turnover.isInFlipRoom
                      ? 'Flip'
                      : turnover.precedingCaseId !== undefined
                        ? 'SSSR'
                        : null}
                  </P3>
                </TD>
              </TR>
            )
          })}
        </TBody>
      </Table>
    </div>
  )
}
