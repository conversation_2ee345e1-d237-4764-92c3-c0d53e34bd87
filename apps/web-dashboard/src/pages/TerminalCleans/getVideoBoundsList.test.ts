import { DateTime } from 'luxon'

import { getVideoBoundsList } from './TerminalCleansVideoBlade'

describe('getVideoBoundsList', () => {
  test('no cases', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), nextDay.set({ hour: 7, minute: 30 })],
    ])
  })

  test('case ending before current day cutoff within buffer', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [[today.set({ hour: 15 }), today.set({ hour: 16, minute: 58 })]],
    })

    expect(actual).toEqual([
      [
        today.set({ hour: 17, minute: 3 }),
        nextDay.set({ hour: 7, minute: 30 }),
      ],
    ])
  })

  test('case overlapping with current day cutoff', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [[today.set({ hour: 15 }), today.set({ hour: 18 })]],
    })

    expect(actual).toEqual([
      [
        today.set({ hour: 18, minute: 5 }),
        nextDay.set({ hour: 7, minute: 30 }),
      ],
    ])
  })

  test('case starting before current day cutoff with null end', () => {
    const today = getToday()

    const actual = getList({
      cases: [[today.set({ hour: 15 }), null]],
    })

    expect(actual).toEqual([])
  })

  test('case starting soon after current day cutoff', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [today.set({ hour: 7 }), today.set({ hour: 8 })], // irrelevant case
        [today.set({ hour: 17, minute: 5 }), today.set({ hour: 20 })],
      ],
    })

    expect(actual).toEqual([
      [
        today.set({ hour: 20, minute: 5 }),
        nextDay.set({ hour: 7, minute: 30 }),
      ],
    ])
  })

  test('case starting soon after current day cutoff with null end', () => {
    const today = getToday()

    const actual = getList({
      cases: [
        [today.set({ hour: 7 }), today.set({ hour: 8 })], // irrelevant case
        [today.set({ hour: 17, minute: 5 }), null],
      ],
    })

    expect(actual).toEqual([])
  })

  test('case starting after current day cutoff', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [today.set({ hour: 7 }), today.set({ hour: 8 })], // irrelevant case
        [today.set({ hour: 18 }), today.set({ hour: 20 })],
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), today.set({ hour: 17, minute: 55 })],
      [
        today.set({ hour: 20, minute: 5 }),
        nextDay.set({ hour: 7, minute: 30 }),
      ],
    ])
  })

  test('case starting after current day cutoff with nonzero minutes', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [today.set({ hour: 7 }), today.set({ hour: 8 })], // irrelevant case
        [
          today.set({ hour: 18, second: 33 }),
          today.set({ hour: 20, second: 12 }),
        ],
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), today.set({ hour: 17, minute: 55 })],
      [
        today.set({ hour: 20, minute: 6 }),
        nextDay.set({ hour: 7, minute: 30 }),
      ],
    ])
  })

  test('case starting after current day cutoff with null end', () => {
    const today = getToday()

    const actual = getList({
      cases: [
        [today.set({ hour: 7 }), today.set({ hour: 8 })], // irrelevant case
        [today.set({ hour: 21 }), null],
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), today.set({ hour: 20, minute: 55 })],
    ])
  })

  test('case starting after another case with null end', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [today.set({ hour: 7 }), today.set({ hour: 8 })], // irrelevant case
        [today.set({ hour: 18 }), today.set({ hour: 20 })],
        [nextDay.set({ hour: 6 }), null],
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), today.set({ hour: 17, minute: 55 })],
      [
        today.set({ hour: 20, minute: 5 }),
        nextDay.set({ hour: 5, minute: 55 }),
      ],
    ])
  })

  test('case overlapping with next day cutoff', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [nextDay.set({ hour: 6 }), nextDay.set({ hour: 9 })],
        [nextDay.set({ hour: 10 }), nextDay.set({ hour: 12 })], // irrelevant case
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), nextDay.set({ hour: 5, minute: 55 })],
    ])
  })

  test('case ending before next day cutoff within buffer', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [nextDay.set({ hour: 6 }), nextDay.set({ hour: 7, minute: 28 })],
        [nextDay.set({ hour: 10 }), nextDay.set({ hour: 12 })], // irrelevant case
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), nextDay.set({ hour: 5, minute: 55 })],
    ])
  })

  test('case starting after next day cutoff within buffer', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [nextDay.set({ hour: 7, minute: 32 }), nextDay.set({ hour: 9 })],
        [nextDay.set({ hour: 10 }), nextDay.set({ hour: 12 })], // irrelevant case
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), nextDay.set({ hour: 7, minute: 27 })],
    ])
  })

  test('consecutive cases within buffer', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [today.set({ hour: 19 }), today.set({ hour: 21 })],
        [today.set({ hour: 21, minute: 9 }), today.set({ hour: 23 })],
        [today.set({ hour: 23, minute: 9 }), nextDay.set({ hour: 1 })],
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), today.set({ hour: 18, minute: 55 })],
      [
        nextDay.set({ hour: 1, minute: 5 }),
        nextDay.set({ hour: 7, minute: 30 }),
      ],
    ])
  })

  test('consecutive cases within buffer with last case null end', () => {
    const today = getToday()

    const actual = getList({
      cases: [
        [today.set({ hour: 19 }), today.set({ hour: 21 })],
        [today.set({ hour: 21, minute: 9 }), today.set({ hour: 23 })],
        [today.set({ hour: 23, minute: 9 }), null],
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), today.set({ hour: 18, minute: 55 })],
    ])
  })

  test('consecutive cases within buffer around current day cutoff', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [today.set({ hour: 15 }), today.set({ hour: 16, minute: 56 })],
        [today.set({ hour: 17, minute: 3 }), today.set({ hour: 18 })],
      ],
    })

    expect(actual).toEqual([
      [
        today.set({ hour: 18, minute: 5 }),
        nextDay.set({ hour: 7, minute: 30 }),
      ],
    ])
  })

  test('consecutive cases within buffer around next day cutoff', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [nextDay.set({ hour: 6 }), nextDay.set({ hour: 7, minute: 23 })],
        [nextDay.set({ hour: 7, minute: 32 }), nextDay.set({ hour: 9 })],
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 17 }), nextDay.set({ hour: 5, minute: 55 })],
    ])
  })

  test('kitchen sink', () => {
    const today = getToday()
    const nextDay = today.plus({ days: 1 })

    const actual = getList({
      cases: [
        [today.set({ hour: 13 }), today.set({ hour: 14 })],
        [today.set({ hour: 15 }), today.set({ hour: 16, minute: 56 })],
        [today.set({ hour: 17, minute: 3 }), today.set({ hour: 18 })],
        // Video 1
        [today.set({ hour: 19 }), today.set({ hour: 21 })],
        [today.set({ hour: 21, minute: 9 }), today.set({ hour: 23 })],
        [today.set({ hour: 23, minute: 9 }), nextDay.set({ hour: 1 })],
        // Video 2
        [nextDay.set({ hour: 3 }), nextDay.set({ hour: 4 })],
        // Video 3
        [nextDay.set({ hour: 6 }), nextDay.set({ hour: 7, minute: 23 })],
        [nextDay.set({ hour: 7, minute: 32 }), nextDay.set({ hour: 9 })],
        [nextDay.set({ hour: 9 }), nextDay.set({ hour: 11 })],
        [nextDay.set({ hour: 9 }), null],
      ],
    })

    expect(actual).toEqual([
      [today.set({ hour: 18, minute: 5 }), today.set({ hour: 18, minute: 55 })],
      [
        nextDay.set({ hour: 1, minute: 5 }),
        nextDay.set({ hour: 2, minute: 55 }),
      ],
      [
        nextDay.set({ hour: 4, minute: 5 }),
        nextDay.set({ hour: 5, minute: 55 }),
      ],
    ])
  })
})

function getToday() {
  return DateTime.now().startOf('day')
}

function getList({
  cases,
  currentDayCutoff = getToday().set({ hour: 17 }),
  nextDayCutoff = getToday().plus({ days: 1 }).set({ hour: 7, minute: 30 }),
}: {
  cases: [DateTime, DateTime | null][]
  currentDayCutoff?: DateTime
  nextDayCutoff?: DateTime
}) {
  cases.forEach(([caseStart, caseEnd]) => {
    if (caseEnd && caseEnd < caseStart) {
      throw new Error('Case end time must be after than case start time')
    }
  })
  return getVideoBoundsList(
    cases.map(([startDateTime, endDateTime]) => {
      return { startDateTime, endDateTime }
    }),
    currentDayCutoff,
    nextDayCutoff
  )
}
