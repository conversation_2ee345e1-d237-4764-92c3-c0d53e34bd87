import { useCallback, useEffect, useMemo } from 'react'
import { Outlet } from 'react-router'

import { DateTime } from 'luxon'

import { DatePicker } from '@apella/component-library'
import { DayOfWeekFilter } from 'src/components/DayOfWeekFilter'
import { dateTimeToUrlFriendlyDate } from 'src/components/Filters/urls'
import { LoadingBackdrop } from 'src/components/LoadingBackdrop'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { useTimezone } from 'src/Contexts'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { useRoomOptions } from 'src/utils/useSiteRoomsOptions'

import { useTerminalCleansFilterData } from './hooks'
import { TerminalCleansV0Table } from './TerminalCleansV0Table'
import {
  DAY_OF_WEEK_FILTER,
  useTerminalCleansFilters,
} from './useTerminalCleansFilters'

const TerminalCleans = (): React.JSX.Element => {
  const { timezone } = useTimezone()
  const maxDate = DateTime.now().setZone(timezone).minus({ days: 1 })

  const {
    selectedSiteId,
    selectedRoomIds,
    selectedStartDate,
    selectedEndDate,
    selectedDaysOfWeek,
    onSearch,
  } = useTerminalCleansFilters({ maxDate })

  const eventsLogger = useAnalyticsEventLogger()

  useEffect(() => {
    selectedSiteId &&
      eventsLogger(EVENTS.LOAD_TERMINAL_CLEANS_PAGE, {
        siteId: selectedSiteId,
      })
  }, [selectedSiteId, eventsLogger])

  const { activeOrganization, filtersLoading, rooms, userOrgData } =
    useTerminalCleansFilterData(selectedSiteId)

  const sites = useMemo(() => {
    const sites = userOrgData?.sites.edges ?? []

    return sites
  }, [userOrgData])

  useEffect(() => {
    // we always select the first site, so the page is not empty.
    if (sites.length > 0 && !selectedSiteId) {
      onSearch({ site: sites[0].node.id })
    }
  }, [onSearch, selectedSiteId, sites])

  // SitesRoomsFilter requires a different format for rooms.
  const allRooms = useRoomOptions()

  const onSiteIdChange = useCallback(
    (newSiteId?: string) => {
      if (!filtersLoading) {
        onSearch({ site: newSiteId })
      }
    },
    [filtersLoading, onSearch]
  )

  if (filtersLoading) {
    return <LoadingBackdrop />
  }

  return (
    <PageContentTemplate
      title="Terminal Cleans"
      filters={
        <>
          <SitesRoomsFilter
            sites={sites}
            selectedSiteIds={selectedSiteId}
            onChangeSites={onSiteIdChange}
            rooms={allRooms}
            selectedRoomIds={selectedRoomIds}
            onChangeSitesAndRooms={(siteIds, roomIds) => {
              onSearch({
                site: siteIds ? siteIds[0] : undefined,
                rooms: roomIds,
              })
            }}
            disableSelectAllOption={true}
            bulkSelectRooms
          />

          {selectedSiteId && (
            <>
              <DatePicker
                excludeToday
                calendarProps={{
                  maxDate: maxDate.toJSDate(),
                }}
                showIcon={true}
                showArrow={true}
                timezone={timezone}
                selectRange={true}
                showPresets={true}
                value={[
                  selectedStartDate.toJSDate(),
                  selectedEndDate.toJSDate(),
                ]}
                setValue={(
                  dates: Date | [Date | null, Date | null] | null | undefined
                ) => {
                  if (Array.isArray(dates) && dates[0] && dates[1]) {
                    onSearch({
                      startDate: dateTimeToUrlFriendlyDate(
                        DateTime.fromJSDate(dates[0]).setZone(timezone)
                      ),
                      endDate: dateTimeToUrlFriendlyDate(
                        DateTime.fromJSDate(dates[1]).setZone(timezone)
                      ),
                    })
                  }
                }}
              />

              <DayOfWeekFilter
                selected={selectedDaysOfWeek}
                onChange={(daysOfWeek) => {
                  onSearch({ [DAY_OF_WEEK_FILTER]: daysOfWeek })
                }}
              />
            </>
          )}
        </>
      }
    >
      {activeOrganization?.node.id && selectedSiteId && (
        <TerminalCleansV0Table
          selectedDaysOfWeek={selectedDaysOfWeek}
          siteId={selectedSiteId}
          rooms={
            selectedRoomIds.length === 0
              ? rooms
              : rooms.filter((room) => selectedRoomIds.includes(room.id))
          }
          selectedDates={[selectedStartDate, selectedEndDate.plus({ day: 1 })]}
        />
      )}
      <Outlet />
    </PageContentTemplate>
  )
}

export default TerminalCleans
