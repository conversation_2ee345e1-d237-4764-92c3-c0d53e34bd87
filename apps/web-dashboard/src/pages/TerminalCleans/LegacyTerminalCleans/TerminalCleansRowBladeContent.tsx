import { ComponentProps, Fragment, useEffect, useMemo } from 'react'
import { useParams } from 'react-router'

import { useTheme } from '@emotion/react'

import { useQuery } from '@apollo/client'

import {
  Blade,
  Button,
  Caps2,
  Check,
  Clean,
  Close,
  ComponentTheme,
  FlexContainer,
  H4,
  P2,
  P3,
  ProgressOverlay,
  remSpacing,
  Span3,
} from '@apella/component-library'
import { HR } from 'src/components/HR'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { onClickContactSupport } from 'src/utils/toggleBeacon'

import { ISOWeekDate } from '../../Insights/types'
import { VideoBladeContent } from '../../VideoBlade/VideoBlade'
import {
  GetMeasurementPeriod,
  GetMeasurementPeriodVariables,
} from '../__generated__'
import {
  CLEAN_SCORE_LABEL,
  TERMINAL_CLEAN_EVENT_NAMES,
  TERMINAL_CLEAN_SCORE_EVENT_CATEGORIES,
  TERMINAL_CLEAN_VIDEO_BUFFER_MINUTES,
} from '../constants'
import {
  ScoringType,
  useMeasurementPeriodData,
  useTerminalCleansFilterData,
  useTerminalCleansTimeOffset,
} from '../hooks'
import { GET_MEASUREMENT_PERIOD } from '../queries'

const CleanScoreCard = ({ score }: { score: number }) => {
  const theme: ComponentTheme = useTheme()

  return (
    <FlexContainer
      justifyContent="center"
      css={{
        border: `1px solid ${theme.palette.gray[20]}`,
        borderRadius: remSpacing.small,
      }}
    >
      <FlexContainer
        css={{ padding: remSpacing.large }}
        gap={remSpacing.xsmall}
        direction="column"
        alignItems="center"
        justifyContent="center"
      >
        <div>
          <H4 color={theme.palette.text.secondary} as="span">
            {score}
          </H4>
          <P2 color={theme.palette.text.tertiary} as="span">
            &nbsp;/ 6
          </P2>
        </div>
        <Caps2 color={theme.palette.text.secondary} as="span">
          {CLEAN_SCORE_LABEL}
        </Caps2>
      </FlexContainer>
    </FlexContainer>
  )
}

const SubscoreCategories = ({
  categories,
}: {
  categories: { category: string; present: boolean }[]
}) => {
  const theme: ComponentTheme = useTheme()

  return (
    <div
      css={{
        borderRadius: remSpacing.small,
        backgroundColor: theme.palette.gray.background,
      }}
    >
      <div css={{ padding: remSpacing.xsmall }}>
        {categories.map(({ category, present }, idx) => {
          return (
            <Fragment key={category}>
              {idx !== 0 && <HR />}
              <FlexContainer
                gap={remSpacing.xsmall}
                css={{ padding: remSpacing.xsmall }}
              >
                {present ? (
                  <Check size="md" css={{ color: theme.palette.green[50] }} />
                ) : (
                  <Close size="md" css={{ color: theme.palette.red[50] }} />
                )}
                <P2 color={theme.palette.text.secondary} as="span">
                  {category}
                </P2>
              </FlexContainer>
            </Fragment>
          )
        })}
      </div>
    </div>
  )
}

export const TerminalCleansRowBladeContent = ({
  onClose,
}: Pick<ComponentProps<typeof Blade>, 'onClose'>) => {
  const { measurementPeriodId, rowId } = useParams<{
    measurementPeriodId: string
    rowId: string
  }>()
  const theme: ComponentTheme = useTheme()

  const { data, loading } = useQuery<
    GetMeasurementPeriod,
    GetMeasurementPeriodVariables
  >(GET_MEASUREMENT_PERIOD, {
    variables: { id: measurementPeriodId ?? '' },
    skip: !measurementPeriodId,
  })

  const { filtersLoading, rooms } = useTerminalCleansFilterData(
    data?.measurementPeriod?.site.id
  )

  const filteredRooms = useMemo(() => {
    if (!data?.measurementPeriod?.roomIds) return []

    return rooms
      .filter((r) => data.measurementPeriod!.roomIds.includes(r.id))
      .sort((a, b) =>
        (a.site.name + a.name).localeCompare(b.site.name + b.name)
      )
  }, [rooms, data])

  const { startWithOffset, endWithOffset, todayWithOffset } =
    useTerminalCleansTimeOffset(data?.measurementPeriod)

  const { isLoading, terminalCleans, scoringType } = useMeasurementPeriodData({
    skip: loading || !data,
    measurementPeriodId: data?.measurementPeriod?.id,
    organizationId: data?.measurementPeriod?.site.organizationId,
    siteId: data?.measurementPeriod?.site.id,
    annotationTaskTypeId: data?.measurementPeriod?.annotationTaskType.id,
    isoDaysOfWeek: data?.measurementPeriod?.isoDaysOfWeek as ISOWeekDate[],
    rooms: filteredRooms,
    today: todayWithOffset,
    measurementPeriodStart: startWithOffset,
    measurementPeriodEnd: endWithOffset,
  })

  const terminalCleanDataRow = useMemo(
    () => terminalCleans.find((tc) => tc.key === rowId),
    [terminalCleans, rowId]
  )

  const [startTime, endTime] = useMemo(() => {
    // By default show 6am -> 6am video
    const startTime = terminalCleanDataRow?.cleaningStartTime
      ? terminalCleanDataRow.cleaningStartTime
          .minus({ minutes: TERMINAL_CLEAN_VIDEO_BUFFER_MINUTES })
          .startOf('minute')
          .toISO()
      : undefined
    const endTime = terminalCleanDataRow?.cleaningEndTime
      ? terminalCleanDataRow.cleaningEndTime
          .plus({ minutes: TERMINAL_CLEAN_VIDEO_BUFFER_MINUTES })
          .endOf('minute')
          .toISO()
      : undefined
    return [startTime, endTime]
  }, [
    terminalCleanDataRow?.cleaningStartTime,
    terminalCleanDataRow?.cleaningEndTime,
  ])

  const eventsLogger = useAnalyticsEventLogger()
  useEffect(() => {
    if (!terminalCleanDataRow) return
    eventsLogger(EVENTS.VIEW_TERMINAL_CLEAN_BLADE, {
      siteId: terminalCleanDataRow.siteId,
      roomId: terminalCleanDataRow.roomId,
      date: terminalCleanDataRow.date,
      terminalCleanScore: terminalCleanDataRow.cleaningScore,
      terminalCleanObserved: terminalCleanDataRow.cleaningObserved,
      terminalCleanDuration: terminalCleanDataRow.cleaningDuration,
      terminalCleanTotalCases: terminalCleanDataRow.totalCases,
    })
  }, [eventsLogger, terminalCleanDataRow])

  if (loading || filtersLoading || isLoading) {
    return <ProgressOverlay />
  }

  if (!terminalCleanDataRow) {
    return null
  }

  return (
    <>
      <Blade.Header>
        <Blade.Title icon={<Clean size="sm" />}>
          <>
            <P3>{terminalCleanDataRow.roomName}</P3>
            <P3 css={{ color: theme.palette.text.secondary }}>
              <Caps2>{terminalCleanDataRow.siteName}</Caps2>
              <Span3
                css={{
                  marginLeft: remSpacing.xsmall,
                  marginRight: remSpacing.xsmall,
                }}
              >
                &#8226;
              </Span3>
              {terminalCleanDataRow.date}
            </P3>
          </>
        </Blade.Title>
        <FlexContainer>
          <Button appearance="link" onClick={onClickContactSupport} size="sm">
            Submit feedback
          </Button>
          <Blade.CloseButton onClose={onClose} />
        </FlexContainer>
      </Blade.Header>
      <FlexContainer
        css={{
          padding: remSpacing.medium,
          overflowY: 'auto',
          height: 'calc(100vh - 65px)',
        }}
        gap={remSpacing.medium}
        direction="column"
      >
        {scoringType === ScoringType.FULL && (
          <CleanScoreCard score={terminalCleanDataRow.cleaningScore} />
        )}

        {startTime && endTime && (
          <VideoBladeContent
            roomId={terminalCleanDataRow.roomId}
            startTime={startTime}
            endTime={endTime}
            time={startTime}
            onClose={onClose}
            showHeader={false}
            eventNames={Object.values(TERMINAL_CLEAN_EVENT_NAMES)}
          />
        )}

        {scoringType === ScoringType.FULL && (
          <SubscoreCategories
            categories={Object.entries(
              TERMINAL_CLEAN_SCORE_EVENT_CATEGORIES
            ).map(([category, eventNames]) => {
              return {
                category,
                present: eventNames.some(
                  (eventName) => terminalCleanDataRow.eventCounts?.[eventName]
                ),
              }
            })}
          />
        )}
      </FlexContainer>
    </>
  )
}
