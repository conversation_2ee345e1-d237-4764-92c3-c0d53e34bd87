import { Component<PERSON><PERSON>, MouseEvent, useCallback, useState } from 'react'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import { useMutation } from '@apollo/client'
import { DateTime } from 'luxon'

import {
  AdditionalText,
  Blade,
  Button,
  CARD_HEADER_DATE_FORMAT,
  Equalizer,
  FlexContainer,
  FlexItem,
  H5,
  Option,
  remSpacing,
  SingleSelect,
  Span3,
} from '@apella/component-library'
import {
  ISO_DAYS_OF_WEEK,
  ISODayOfWeekFilter,
} from 'src/components/DayOfWeekFilter'
import { DateFilter } from 'src/components/Filters/DateFilter'
import { MultiFilterWithCount } from 'src/components/Filters/FilterWithCount'
import { urlFriendlyDateToJSDate, yyyyMMdd } from 'src/components/Filters/urls'
import { useTimezone } from 'src/Contexts'
import { ISOWeekDate } from 'src/pages/Insights/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

import {
  DeleteMeasurementPeriod,
  DeleteMeasurementPeriodVariables,
  GetMeasurementPeriods,
  UpsertMeasurementPeriod,
  UpsertMeasurementPeriodVariables,
} from '../__generated__'
import {
  TERMINAL_CLEAN_BASIC_SCORING_ID,
  TERMINAL_CLEAN_FULL_SCORING_ID,
} from '../constants'
import { IUseMeasurementPeriodLocationData } from '../hooks'
import {
  DELETE_MEASUREMENT_PERIOD,
  UPSERT_MEASUREMENT_PERIOD,
} from '../queries'

export const MeasurementPeriodBlade = ({
  onClose,
  isOpen,
  siteId,
  rooms,
  measurementPeriod,
}: Pick<ComponentProps<typeof Blade>, 'isOpen' | 'onClose'> &
  Pick<IUseMeasurementPeriodLocationData, 'siteId' | 'rooms'> & {
    measurementPeriod?: GetMeasurementPeriods['measurementPeriods']['edges'][number]['node']
  }) => {
  const theme = useTheme()
  const { timezone } = useTimezone()
  const eventsLogger = useAnalyticsEventLogger()

  const [annotationTaskTypeId, setAnnotationTaskTypeId] = useState<
    string | undefined
  >(measurementPeriod?.annotationTaskType.id ?? TERMINAL_CLEAN_BASIC_SCORING_ID)
  const [roomIds, setRoomIds] = useState<string[] | undefined>(
    measurementPeriod?.roomIds ?? rooms.map((r) => r.id)
  )
  const [roomErrorMsg, setRoomErrorMsg] = useState<string | undefined>(
    undefined
  )
  const [isoDaysOfWeek, setIsoDaysOfWeek] = useState<ISOWeekDate[]>(
    (measurementPeriod?.isoDaysOfWeek as ISOWeekDate[]) ?? ISO_DAYS_OF_WEEK
  )

  const [dateRange, setDateRange] = useState<[Date, Date]>(
    measurementPeriod
      ? [
          DateTime.fromISO(
            urlFriendlyDateToJSDate(
              measurementPeriod.measurementPeriodStart,
              timezone
            ),
            {
              setZone: true,
            }
          ).toJSDate(),
          DateTime.fromISO(
            urlFriendlyDateToJSDate(
              measurementPeriod.measurementPeriodEnd,
              timezone
            ),
            {
              setZone: true,
            }
          )
            .minus({ day: 1 }) // subtract a day to make it inclusive
            .toJSDate(),
        ]
      : [
          DateTime.now()
            .setZone(timezone)
            .minus({ day: 7 })
            .startOf('day')
            .toJSDate(),
          DateTime.now().setZone(timezone).startOf('day').toJSDate(),
        ]
  )

  const [deleteMeasurementPeriod, { loading: deleteLoading }] = useMutation<
    DeleteMeasurementPeriod,
    DeleteMeasurementPeriodVariables
  >(DELETE_MEASUREMENT_PERIOD)

  const onRemoveHandler = useCallback(() => {
    if (
      measurementPeriod &&
      confirm(
        `Are you sure that you would like to delete the ${measurementPeriod.name} measurement period?`
      )
    ) {
      eventsLogger(EVENTS.DELETE_MEASUREMENT_PERIOD, {
        id: measurementPeriod.id,
      })
      deleteMeasurementPeriod({
        variables: {
          id: measurementPeriod.id,
        },
      })
        .then(({ data: deletedData }) => {
          deletedData?.measurementPeriodDelete?.success === true && onClose()
          toast.success(`Measurement period ${measurementPeriod.name} deleted`)
        })
        .catch((e) => {
          toast.error(e.message)
        })
    }
  }, [deleteMeasurementPeriod, eventsLogger, measurementPeriod, onClose])

  const [upsertMeasurementPeriod, { loading: upsertLoading }] = useMutation<
    UpsertMeasurementPeriod,
    UpsertMeasurementPeriodVariables
  >(UPSERT_MEASUREMENT_PERIOD)

  const onSaveHandler = useCallback(
    (e: MouseEvent<HTMLButtonElement>) => {
      e.preventDefault()

      if (!roomIds || roomIds.length === 0) {
        setRoomErrorMsg('Please select at least one room')
        return
      }

      const measurementPeriodStart = DateTime.fromJSDate(dateRange[0])
        .setZone(timezone)
        .startOf('day')
      const measurementPeriodEnd = DateTime.fromJSDate(dateRange[1])
        .setZone(timezone)
        .startOf('day')
      const name = `${measurementPeriodStart.toFormat(
        CARD_HEADER_DATE_FORMAT
      )} - ${measurementPeriodEnd.toFormat(CARD_HEADER_DATE_FORMAT)}`

      const measurementPeriodStartFormatted =
        measurementPeriodStart.toFormat(yyyyMMdd)
      const measurementPeriodEndFormatted = measurementPeriodEnd
        .plus({ day: 1 }) // exclude the last day in data store
        .toFormat(yyyyMMdd)

      const measurementPeriodData = {
        id: measurementPeriod?.id,
        name,
        siteId,
        roomIds,
        isoDaysOfWeek,
        measurementPeriodStart: measurementPeriodStartFormatted,
        measurementPeriodEnd: measurementPeriodEndFormatted,
        annotationTaskTypeId: annotationTaskTypeId!,
      }

      eventsLogger(EVENTS.UPSERT_MEASUREMENT_PERIOD, measurementPeriodData)

      upsertMeasurementPeriod({
        variables: measurementPeriodData,
      })
        .then(({ data: upsertedData }) => {
          upsertedData?.measurementPeriodUpsert?.success === true && onClose()
          toast.success(`Measurement period ${name} saved`)
        })
        .catch((e) => {
          toast.error(e.message)
        })
    },
    [
      dateRange,
      eventsLogger,
      onClose,
      siteId,
      timezone,
      upsertMeasurementPeriod,
      annotationTaskTypeId,
      roomIds,
      isoDaysOfWeek,
      measurementPeriod?.id,
    ]
  )

  return (
    <Blade size="xs" side="right" onClose={onClose} isOpen={isOpen}>
      <Blade.Header>
        <Blade.Title title="Add measurement" icon={<Equalizer size="sm" />} />
        <Blade.CloseButton onClose={onClose} />
      </Blade.Header>
      <FlexContainer
        justifyContent="space-between"
        css={{ padding: remSpacing.medium }}
        gap={remSpacing.medium}
        direction="column"
      >
        <H5>Select measurement period</H5>
        <Span3 color={theme.palette.text.secondary}>
          Please select a date range that will serve as a measurement period for
          this project and help you track progress.
        </Span3>

        <DateFilter
          showPresets={false}
          selected={dateRange}
          onChangeDate={(dates) => {
            setDateRange(dates)
          }}
          showIcon={false}
          showArrow={true}
          buttonAlignment="space-between"
          buttonFullWidth={true}
        />

        <ISODayOfWeekFilter
          selected={isoDaysOfWeek}
          onChange={setIsoDaysOfWeek}
        />

        <MultiFilterWithCount
          items={rooms.map((r) => ({
            node: {
              id: r.id,
              name: r.name,
            },
          }))}
          selectedIds={roomIds}
          onChange={(val) => {
            setRoomIds(val)
            val && setRoomErrorMsg(undefined)
          }}
          label={'Select room(s)'}
          bulkSelect={true}
        />

        <SingleSelect
          label={'Scoring type'}
          name={`annotation-task-type-filter`}
          value={annotationTaskTypeId}
          onChange={setAnnotationTaskTypeId}
        >
          <Option
            key={TERMINAL_CLEAN_FULL_SCORING_ID}
            value={TERMINAL_CLEAN_FULL_SCORING_ID}
            label="Full scoring"
          />
          <Option
            key={TERMINAL_CLEAN_BASIC_SCORING_ID}
            value={TERMINAL_CLEAN_BASIC_SCORING_ID}
            label="Basic Observation only"
          />
        </SingleSelect>

        <AdditionalText error={roomErrorMsg} />

        <FlexItem css={{ display: 'flex', alignSelf: 'flex-end', gap: 8 }}>
          {measurementPeriod && (
            <Button
              appearance="link"
              type="button"
              disabled={deleteLoading}
              style={{ color: theme.palette.red[40] }}
              onClick={onRemoveHandler}
            >
              Remove
            </Button>
          )}

          <Button
            type="button"
            disabled={upsertLoading}
            onClick={onSaveHandler}
          >
            Save
          </Button>
        </FlexItem>
      </FlexContainer>
    </Blade>
  )
}
