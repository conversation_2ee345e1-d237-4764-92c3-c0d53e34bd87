import { Blade } from '@apella/component-library'
import { LocationPath } from 'src/router/types'
import { useBladeState } from 'src/utils/useBladeState'

import { TerminalCleansRowBladeContent } from './TerminalCleansRowBladeContent'

export const TerminalCleansRowView = () => {
  const { isBladeOpen, onBladeClose } = useBladeState({
    hardRefreshFallbackPath: LocationPath.TerminalCleans,
  })

  return (
    <Blade size="sm" side="right" onClose={onBladeClose} isOpen={isBladeOpen}>
      <TerminalCleansRowBladeContent onClose={onBladeClose} />
    </Blade>
  )
}
