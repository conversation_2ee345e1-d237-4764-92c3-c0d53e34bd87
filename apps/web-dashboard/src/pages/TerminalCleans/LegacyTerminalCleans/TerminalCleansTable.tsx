import { ReactNode, useMemo } from 'react'
import { generatePath, useLocation, useNavigate } from 'react-router'

import { useTheme } from '@emotion/react'

import {
  Caps2,
  Check,
  Clock,
  Close,
  ComponentTheme,
  Table,
  TBody,
  TD,
  TH,
  THead,
  Tooltip,
  TR,
} from '@apella/component-library'
import { DashComponent } from 'src/components/DashComponent'
import { LocationPath } from 'src/router/types'

import { CLEAN_SCORE_LABEL } from '../constants'
import { CleaningObserved, ITerminalCleansData, ScoringType } from '../hooks'

const columns: {
  name: string
  selector: keyof ITerminalCleansData
  formatter?:
    | ((row: ITerminalCleansData) => ReactNode)
    | ((row: ITerminalCleansData, theme: ComponentTheme) => ReactNode)
}[] = [
  {
    name: 'Room',
    selector: 'roomName',
  },
  {
    name: 'Date',
    selector: 'date',
  },
  {
    name: CLEAN_SCORE_LABEL,
    selector: 'cleaningScore',
    formatter: (row: ITerminalCleansData) => (
      <span>
        {row['cleaningScore'] !== 0 ? row['cleaningScore'] : <DashComponent />}
      </span>
    ),
  },
  {
    name: 'Total cases',
    selector: 'totalCases',
    formatter: (row: ITerminalCleansData) => (
      <span>
        {row['totalCases'] !== 0 ? row['totalCases'] : <DashComponent />}
      </span>
    ),
  },
  {
    name: 'Cleaning duration',
    selector: 'cleaningDuration',
    formatter: (row: ITerminalCleansData) => {
      const cleaningDuration = row['cleaningDuration']
      return (
        <span>
          {cleaningDuration ? (
            `${cleaningDuration.as('minutes').toFixed(0)}m`
          ) : (
            <DashComponent />
          )}
        </span>
      )
    },
  },
  {
    name: 'Cleaning observed',
    selector: 'cleaningObserved',
    formatter: (row: ITerminalCleansData, theme: ComponentTheme) => {
      const cleaningObserved = row['cleaningObserved']
      return cleaningObserved === CleaningObserved.YES ? (
        <Check size="md" style={{ color: theme.palette.green[30] }} />
      ) : cleaningObserved === CleaningObserved.NO ? (
        <Close size="md" style={{ color: theme.palette.red[40] }} />
      ) : (
        <Tooltip
          body={'Pending'}
          placement="bottom-start"
          middleware={[Tooltip.middleware.shift()]}
        >
          <Clock size="md" style={{ color: theme.palette.orange[50] }} />
        </Tooltip>
      )
    },
  },
]

const TerminalCleansRow = ({
  row,
  children,
}: {
  children: React.ReactNode
  scoringType: ScoringType
  row: ITerminalCleansData
}) => {
  const navigate = useNavigate()
  const location = useLocation()

  return (
    <TR
      css={{ cursor: 'pointer' }}
      onClick={() => {
        const path = generatePath(LocationPath.TerminalCleansView, {
          measurementPeriodId: row.measurementPeriodId,
          rowId: row.key,
        })

        navigate(path, { state: { background: location } })
      }}
    >
      {children}
    </TR>
  )
}

export const TerminalCleansTable = ({
  data,
  scoringType,
}: {
  data: ITerminalCleansData[]
  scoringType: ScoringType
}): React.JSX.Element => {
  const theme: ComponentTheme = useTheme()

  const tableColumns = useMemo(() => {
    if (scoringType === ScoringType.FULL) {
      return columns
    }

    return columns.filter((column) => column.name !== CLEAN_SCORE_LABEL)
  }, [scoringType])

  const dateBackgroundColors = useMemo(() => {
    const colors = data.reduce(
      (acc, row) => {
        if (row.date in acc) {
          return acc
        }

        acc[row.date] =
          Object.keys(acc).length % 2 === 0
            ? 'white'
            : theme.palette.background.secondary

        return acc
      },
      {} as Record<string, string>
    )

    return colors
  }, [data, theme.palette.background.secondary])

  return (
    <div css={{ overflowY: 'auto', maxHeight: 400 }}>
      <Table>
        <THead
          css={{
            position: 'sticky',
            top: 0,
            background: theme.palette.background.primary,
          }}
        >
          <TR>
            {tableColumns.map((column) => (
              <TH key={column.name}>
                <Caps2>{column.name}</Caps2>
              </TH>
            ))}
          </TR>
        </THead>
        <TBody>
          {data.map((row) => {
            return (
              <TerminalCleansRow
                row={row}
                scoringType={scoringType}
                key={row.key}
              >
                {tableColumns.map((column) => (
                  <TD
                    key={column.name}
                    css={{
                      backgroundColor: dateBackgroundColors[row.date],
                    }}
                  >
                    {'formatter' in column ? (
                      column.formatter!(row, theme)
                    ) : (
                      <>{row[column.selector]}</>
                    )}
                  </TD>
                ))}
              </TerminalCleansRow>
            )
          })}
        </TBody>
      </Table>
    </div>
  )
}
