import { useCallback, useEffect, useMemo, useState } from 'react'
import { Outlet } from 'react-router'

import { useTheme } from '@emotion/react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import {
  ArrowCounterClockwise,
  Button,
  ComponentTheme,
  DatePicker,
  FlexContainer,
  H5,
  Plus,
  remSpacing,
  Span3,
  Tooltip,
} from '@apella/component-library'
import { yyyyMMdd } from 'src/components/Filters/urls'
import { LoadingBackdrop } from 'src/components/LoadingBackdrop'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { SitesFilter } from 'src/components/SitesFilter'
import { useTimezone } from 'src/Contexts'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

import {
  GetMeasurementPeriods,
  GetMeasurementPeriodsVariables,
} from '../__generated__'
import { useTerminalCleansFilterData } from '../hooks'
import { GET_MEASUREMENT_PERIODS } from '../queries'
import { MeasurementPeriodBlade } from './MeasurementPeriodBlade'
import TerminalCleansCard from './TerminalCleansCard'

const LegacyTerminalCleans = (): React.JSX.Element => {
  const theme: ComponentTheme = useTheme()
  const { timezone } = useTimezone()

  const [siteId, setSiteId] = useState<string | undefined>(undefined)

  const [selectedDates, setSelectedDates] = useState<[DateTime, DateTime]>([
    DateTime.now().minus({ months: 9 }),
    DateTime.now().plus({ months: 3 }),
  ])
  const [isBladeOpen, setIsBladeOpen] = useState(false)

  const eventsLogger = useAnalyticsEventLogger()

  useEffect(() => {
    siteId &&
      eventsLogger(EVENTS.LOAD_TERMINAL_CLEANS_PAGE, {
        siteId: siteId,
      })
  }, [siteId, eventsLogger])

  const {
    activeOrganization,
    filtersLoading,
    rooms,
    uiPermissions,
    userOrgData,
  } = useTerminalCleansFilterData(siteId)

  const sites = useMemo(() => {
    const sites = userOrgData?.sites.edges ?? []

    if (sites.length === 1) {
      setSiteId(sites[0].node.id)
    }

    return sites
  }, [userOrgData])

  const {
    loading: measurementPeriodsLoading,
    data: measurementPeriodsData,
    refetch: refetchMeasurementPeriodsData,
  } = useQuery<GetMeasurementPeriods, GetMeasurementPeriodsVariables>(
    GET_MEASUREMENT_PERIODS,
    {
      variables: {
        siteId: siteId!,
        measurementPeriodStart: selectedDates[0].toFormat(yyyyMMdd),
        measurementPeriodEnd: selectedDates[1].toFormat(yyyyMMdd),
      },
      skip: !activeOrganization?.node.id || !siteId,
    }
  )

  const onSiteIdChange = useCallback(
    (newSiteId?: string) => {
      if (!filtersLoading) {
        setSiteId(newSiteId)
      }
    },
    [filtersLoading, setSiteId]
  )

  const measurementPeriods = useMemo(
    () =>
      (measurementPeriodsData?.measurementPeriods.edges ?? []).map((edge) => ({
        ...edge.node,
      })),
    [measurementPeriodsData]
  )

  if (filtersLoading || measurementPeriodsLoading) {
    return <LoadingBackdrop />
  }

  return (
    <PageContentTemplate
      title="Terminal Cleans"
      filters={
        <>
          <SitesFilter
            sites={sites}
            selectedSiteIds={siteId}
            onChangeSites={onSiteIdChange}
            disableSelectAllOption={true}
          />

          {siteId && (
            <DatePicker
              showIcon={true}
              showArrow={true}
              timezone={timezone}
              selectRange={true}
              showPresets={true}
              value={[selectedDates[0].toJSDate(), selectedDates[1].toJSDate()]}
              setValue={(
                dates: Date | [Date | null, Date | null] | null | undefined
              ) => {
                if (Array.isArray(dates) && dates[0] && dates[1]) {
                  setSelectedDates([
                    DateTime.fromJSDate(dates[0]).setZone(timezone),
                    DateTime.fromJSDate(dates[1]).setZone(timezone),
                  ])
                }
              }}
            />
          )}
        </>
      }
    >
      {activeOrganization?.node.id && siteId && (
        <>
          <div
            css={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            <H5 as="h2">Measurements</H5>

            <FlexContainer alignItems="center" gap={remSpacing.small}>
              <Tooltip body="Refresh data" placement="bottom">
                <Button
                  onClick={() => refetchMeasurementPeriodsData()}
                  disabled={measurementPeriodsLoading}
                  appearance="link"
                >
                  <ArrowCounterClockwise size="sm" />
                </Button>
              </Tooltip>
              {uiPermissions?.dashboardCreateMeasurementPeriodsEnabled &&
                measurementPeriods.length > 0 && (
                  <Button onClick={() => setIsBladeOpen(true)}>
                    <Plus size="sm" />
                    Add measurement
                  </Button>
                )}
            </FlexContainer>
          </div>

          {isBladeOpen && (
            <MeasurementPeriodBlade
              siteId={siteId}
              rooms={rooms}
              isOpen={isBladeOpen}
              onClose={() => {
                setIsBladeOpen(false)
                refetchMeasurementPeriodsData()
              }}
            />
          )}

          {measurementPeriods.length === 0 ? (
            <div
              style={{
                width: '100%',
                height: 298,
                backgroundColor: theme.palette.background.secondary,
                borderRadius: 8,
              }}
              css={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <div
                css={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
              >
                <H5 as="h3">No measurement periods</H5>
                {uiPermissions?.dashboardCreateMeasurementPeriodsEnabled ? (
                  <>
                    <Span3
                      color={theme.palette.text.secondary}
                      style={{ marginBottom: remSpacing.small }}
                    >
                      Add a measurement period to start tracking progress
                    </Span3>

                    <Button onClick={() => setIsBladeOpen(true)}>
                      <Plus size="xs" />
                      Add measurement
                    </Button>
                  </>
                ) : (
                  <Span3 color={theme.palette.text.secondary}>
                    Contact your Customer Success Manager to add measurement
                    periods and start tracking progress
                  </Span3>
                )}
              </div>
            </div>
          ) : (
            <div
              css={{
                display: 'flex',
                flexDirection: 'column',
                gap: remSpacing.large,
              }}
            >
              {measurementPeriods.map((mp) => (
                <TerminalCleansCard
                  key={mp.id}
                  organizationId={activeOrganization.node.id}
                  siteId={siteId}
                  rooms={rooms}
                  measurementPeriod={mp}
                  refetchMeasurementPeriodsData={refetchMeasurementPeriodsData}
                />
              ))}
            </div>
          )}
        </>
      )}
      <Outlet />
    </PageContentTemplate>
  )
}

export default LegacyTerminalCleans
