import { useMemo, useState } from 'react'

import { useTheme } from '@emotion/react'

import { ApolloQueryResult } from '@apollo/client'
import styled from '@emotion/styled'
import { mean, round } from 'lodash'

import {
  BackdropProps,
  Badge,
  Button,
  CalendarIcon,
  CARD_HEADER_DATE_FORMAT,
  Edit,
  ExpandLess,
  ExpandMore,
  FlexContainer,
  FlexItem,
  H3,
  H4,
  MeetingRoom,
  Progress,
  remSpacing,
  Span2,
  Span3,
  Span4,
  Tooltip,
  ZIndex,
} from '@apella/component-library'
import { Card } from 'src/components/Card'
import { DashComponent } from 'src/components/DashComponent'
import { MetricHeader } from 'src/components/MetricHeader'
import { useCurrentUser } from 'src/modules/user/hooks'

import { ISOWeekDate } from '../../Insights/types'
import {
  GetMeasurementPeriods,
  GetMeasurementPeriodsVariables,
} from '../__generated__'
import { CLEAN_SCORE_LABEL } from '../constants'
import {
  CleaningObserved,
  IUseMeasurementPeriodLocationData,
  ScoringType,
  useMeasurementPeriodData,
  useTerminalCleansTimeOffset,
} from '../hooks'
import { RoomAtSite } from '../types'
import { MeasurementPeriodBlade } from './MeasurementPeriodBlade'
import { TerminalCleansTable } from './TerminalCleansTable'

interface TerminalCleansCardProps extends IUseMeasurementPeriodLocationData {
  measurementPeriod: GetMeasurementPeriods['measurementPeriods']['edges'][number]['node']
  refetchMeasurementPeriodsData: (
    variables?: Partial<GetMeasurementPeriodsVariables>
  ) => Promise<ApolloQueryResult<GetMeasurementPeriods>>
}

const TerminalCleansCardBackdrop = styled.div<BackdropProps>(
  ({ theme, variant }) => ({
    alignItems: 'center',
    backgroundColor: theme.palette.background.primary,
    bottom: 0,
    display: 'flex',
    justifyContent: 'center',
    left: 0,
    ...(variant === 'blurred' && { opacity: 0.5 }),
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: ZIndex.MAX,
  })
)

const TerminalCleansMessage = ({
  title,
  description,
}: {
  title: string
  description: string
}): React.JSX.Element => {
  const theme = useTheme()
  return (
    <div
      css={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: 298,
      }}
    >
      <H4>{title}</H4>
      <Span2 color={theme.palette.text.secondary}>{description}</Span2>
    </div>
  )
}

const TerminalCleansCardWeekdayAndRoomHeader = ({
  isoDaysOfWeek: isoDaysOfWeek,
  rooms,
}: {
  isoDaysOfWeek: ISOWeekDate[]
  rooms: RoomAtSite[]
}) => {
  const theme = useTheme()
  const ISOWeekdateToInitials: Record<ISOWeekDate, string> = {
    1: 'M',
    2: 'T',
    3: 'W',
    4: 'Th',
    5: 'F',
    6: 'S',
    7: 'Su',
  }

  return (
    <div
      css={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderRight: `1px solid ${theme.palette.gray[30]}`,
        gap: remSpacing.large,
        paddingRight: remSpacing.large,
      }}
    >
      <div
        style={{
          display: 'flex',
          gap: remSpacing.xxsmall,
          color: theme.palette.gray[50],
        }}
      >
        <CalendarIcon size="xs" />
        <Span4>
          {isoDaysOfWeek.map((d) => ISOWeekdateToInitials[d]).join(', ')}
        </Span4>
      </div>

      <Tooltip
        placement="bottom"
        body={
          <Span3>
            <ul>
              {rooms.map(({ id, name }) => (
                <li key={id}>{name}</li>
              ))}
            </ul>
          </Span3>
        }
      >
        <div
          style={{
            display: 'flex',
            gap: remSpacing.xxsmall,
            color: theme.palette.gray[50],
          }}
        >
          <MeetingRoom size="xs" />
          <Span4>
            {rooms.length}&nbsp;ROOM{rooms.length > 1 && 'S'}
          </Span4>
        </div>
      </Tooltip>
    </div>
  )
}

const TerminalCleansCard = ({
  organizationId,
  siteId,
  rooms,
  measurementPeriod,
  refetchMeasurementPeriodsData,
}: TerminalCleansCardProps): React.JSX.Element => {
  const { permissions: uiPermissions } = useCurrentUser()

  const filteredRooms = useMemo(
    () =>
      rooms
        .filter((r) => measurementPeriod.roomIds.includes(r.id))
        .sort((a, b) =>
          (a.site.name + a.name).localeCompare(b.site.name + b.name)
        ),
    [rooms, measurementPeriod.roomIds]
  )

  const { startWithOffset, endWithOffset, todayWithOffset } =
    useTerminalCleansTimeOffset(measurementPeriod)

  const [isCollectingData, isInProgress, isComplete] = useMemo(
    () => [
      startWithOffset > todayWithOffset,
      startWithOffset <= todayWithOffset && todayWithOffset <= endWithOffset,
      todayWithOffset > endWithOffset,
    ],
    [startWithOffset, endWithOffset, todayWithOffset]
  )

  const [isExpanded, setIsExpanded] = useState(!isComplete)
  const [isBladeOpen, setIsBladeOpen] = useState(false)

  const { isLoading, terminalCleans, scoringType } = useMeasurementPeriodData({
    skip: !isExpanded,
    measurementPeriodId: measurementPeriod.id,
    organizationId,
    siteId,
    rooms: filteredRooms,
    today: todayWithOffset,
    measurementPeriodStart: startWithOffset,
    measurementPeriodEnd: endWithOffset,
    annotationTaskTypeId: measurementPeriod.annotationTaskType.id,
    isoDaysOfWeek: measurementPeriod.isoDaysOfWeek as ISOWeekDate[],
  })

  const dateHeader = useMemo(
    () =>
      `${startWithOffset.toFormat(CARD_HEADER_DATE_FORMAT)}${
        !startWithOffset.equals(endWithOffset.minus({ day: 1 }))
          ? ` - ${endWithOffset
              .minus({ day: 1 }) // minus one day to make it inclusive in the header
              .toFormat(CARD_HEADER_DATE_FORMAT)}`
          : ''
      }`,
    [startWithOffset, endWithOffset]
  )

  const metrics = useMemo(() => {
    const roomsCleanedPct = {
      label: 'Rooms Cleaned',
      value:
        isCollectingData || terminalCleans.length === 0 ? (
          <DashComponent />
        ) : (
          `${round(
            (terminalCleans.filter(
              (r) => r.cleaningObserved === CleaningObserved.YES
            ).length /
              terminalCleans.length) *
              100,
            1
          )}%`
        ),
    }
    if (scoringType === ScoringType.BASIC) {
      return [roomsCleanedPct]
    }

    return [
      roomsCleanedPct,
      {
        label: `${CLEAN_SCORE_LABEL} Avg`,
        value:
          isCollectingData || terminalCleans.length === 0 ? (
            <DashComponent />
          ) : (
            round(mean(terminalCleans.map((r) => r.cleaningScore)), 2)
          ),
      },
    ]
  }, [scoringType, terminalCleans, isCollectingData])

  return (
    <Card>
      {isBladeOpen && (
        <MeasurementPeriodBlade
          siteId={siteId}
          rooms={rooms}
          isOpen={isBladeOpen}
          measurementPeriod={measurementPeriod}
          onClose={() => {
            setIsBladeOpen(false)
            refetchMeasurementPeriodsData()
          }}
        />
      )}

      <div
        css={{
          display: 'grid',
          gridTemplateColumns: 'minmax(0, 1fr)',
          gap: remSpacing.gutter,
          paddingTop: remSpacing.medium,
        }}
        style={{
          paddingBottom: isExpanded && !isLoading ? '0' : remSpacing.medium,
        }}
      >
        <div
          css={{
            display: 'grid',
            justifyContent: 'space-between',
            gridAutoFlow: 'column',
            padding: `0 ${remSpacing.large}`,
          }}
        >
          <div
            css={{
              display: 'grid',
              gridAutoFlow: 'column',
              gap: remSpacing.small,
              alignItems: 'center',
            }}
          >
            <H3>{dateHeader}</H3>
            {isComplete ? (
              <Badge variant="secondary" color="gray">
                COMPLETE
              </Badge>
            ) : isInProgress ? (
              <Badge variant="secondary" color="blue">
                IN PROGRESS
              </Badge>
            ) : (
              <Badge variant="secondary" color="orange">
                NOT STARTED
              </Badge>
            )}
          </div>

          <FlexContainer
            css={{
              cursor: 'pointer',
              margin: 0,
              gap: remSpacing.small,
            }}
          >
            <TerminalCleansCardWeekdayAndRoomHeader
              isoDaysOfWeek={measurementPeriod.isoDaysOfWeek as ISOWeekDate[]}
              rooms={filteredRooms}
            />

            <FlexItem>
              {uiPermissions?.dashboardCreateMeasurementPeriodsEnabled && (
                <Button
                  appearance="link"
                  color="primary"
                  type="button"
                  size="sm"
                  onClick={() => setIsBladeOpen(true)}
                >
                  <Edit size="sm" />
                </Button>
              )}

              <Button
                appearance="link"
                color="alternate"
                type="button"
                size="sm"
              >
                {isExpanded ? (
                  <ExpandLess size="sm" onClick={() => setIsExpanded(false)} />
                ) : (
                  <ExpandMore size="sm" onClick={() => setIsExpanded(true)} />
                )}
              </Button>
            </FlexItem>
          </FlexContainer>
        </div>
        {isExpanded ? (
          isLoading ? (
            <div
              css={{
                position: 'relative',
                width: '100%',
                height: 150,
              }}
            >
              <TerminalCleansCardBackdrop variant="solid">
                <Progress size="md" />
              </TerminalCleansCardBackdrop>
            </div>
          ) : (
            <>
              <div
                css={{
                  display: 'grid',
                  gridTemplateColumns: 'mixmax(0,1fr)',
                  padding: `0 ${remSpacing.gutter}`,
                }}
              >
                <MetricHeader metrics={metrics} />
              </div>
              <div
                css={{
                  display: 'grid',
                  gridTemplateColumns: 'mixmax(0,1fr)',
                }}
              >
                {isCollectingData ? (
                  <TerminalCleansMessage
                    title="Data collection in progress"
                    description="We're still collecting initial data for this
                  measurement period, check back later."
                  />
                ) : terminalCleans.length === 0 ? (
                  <TerminalCleansMessage
                    title="No data to display"
                    description="Please check weekday/weekend filters and try again."
                  />
                ) : (
                  <TerminalCleansTable
                    data={terminalCleans}
                    scoringType={scoringType}
                  />
                )}
              </div>
            </>
          )
        ) : null}
      </div>
    </Card>
  )
}

export default TerminalCleansCard
