import { ReactNode, useMemo, useState } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import {
  Caps2,
  ComponentTheme,
  Direction,
  Table,
  TBody,
  TD,
  TH,
  THead,
  TR,
} from '@apella/component-library'
import { DashComponent } from 'src/components/DashComponent'
import { dayOfWeekToISOWeekDate } from 'src/components/DayOfWeekFilter'
import { LoadingBackdrop } from 'src/components/LoadingBackdrop'
import { type DayOfWeek } from 'src/pages/Insights/types'

import { IBaseTerminalCleansData, useCasesData } from './hooks'
import TerminalCleansVideoBlade from './TerminalCleansVideoBlade'
import { RoomAtSite } from './types'

const columns: {
  name: string
  selector: keyof IBaseTerminalCleansData
  formatter?:
    | ((row: IBaseTerminalCleansData) => ReactNode)
    | ((row: IBaseTerminalCleansData, theme: ComponentTheme) => ReactNode)
}[] = [
  {
    name: 'Date',
    selector: 'date',
  },
  {
    name: 'Room',
    selector: 'roomName',
  },
  {
    name: 'Total cases',
    selector: 'totalCases',
    formatter: (row: IBaseTerminalCleansData) => (
      <span>
        {row['totalCases'] !== 0 ? row['totalCases'] : <DashComponent />}
      </span>
    ),
  },
]

export const TerminalCleansV0Table = ({
  siteId,
  rooms,
  selectedDates,
  selectedDaysOfWeek,
}: {
  siteId: string
  rooms: RoomAtSite[]
  selectedDates: DateTime[]
  selectedDaysOfWeek?: DayOfWeek[]
}): React.JSX.Element => {
  const theme: ComponentTheme = useTheme()

  const { isLoading, casesData } = useCasesData({
    minEndTime: selectedDates[0],
    maxStartTime: selectedDates[1],
    siteId,
    rooms,
    today: DateTime.now(),
    isoDaysOfWeek: selectedDaysOfWeek?.map(
      (day) => dayOfWeekToISOWeekDate[day]
    ),
  })

  const dateBackgroundColors = useMemo(() => {
    const colors = casesData.reduce(
      (acc, row) => {
        if (row.date in acc) {
          return acc
        }

        acc[row.date] =
          Object.keys(acc).length % 2 === 0
            ? 'white'
            : theme.palette.background.secondary

        return acc
      },
      {} as Record<string, string>
    )

    return colors
  }, [casesData, theme.palette.background.secondary])

  const [sortOrder, setSortOrder] = useState<
    { sort: keyof IBaseTerminalCleansData; direction: Direction } | undefined
  >()

  const rowData = sortOrder
    ? casesData.toSorted((row1, row2) => {
        if (sortOrder.direction === Direction.ASC)
          return compareFn(row1[sortOrder.sort], row2[sortOrder.sort])
        else {
          return compareFn(row2[sortOrder.sort], row1[sortOrder.sort])
        }
      })
    : casesData

  const [selectedORDayKey, setSelectedORDayKey] = useState<string | undefined>()
  const selectedORDay = useMemo(
    () => rowData.find((row) => row.key === selectedORDayKey),
    [rowData, selectedORDayKey]
  )

  if (isLoading) {
    return <LoadingBackdrop />
  }

  return (
    <>
      <Table>
        <THead
          css={{
            position: 'sticky',
            top: 0,
            background: theme.palette.background.primary,
          }}
        >
          <TR>
            {columns.map((column) => (
              <TH
                key={column.name}
                sortDirection={
                  sortOrder?.sort === column.selector
                    ? sortOrder?.direction
                    : undefined
                }
                onClick={(direction) => {
                  if (!direction) {
                    setSortOrder(undefined)
                  } else {
                    setSortOrder({
                      sort: column.selector,
                      direction: direction,
                    })
                  }
                }}
              >
                <Caps2>{column.name}</Caps2>
              </TH>
            ))}
          </TR>
        </THead>
        <TBody>
          {rowData.map((row) => {
            return (
              <TR
                css={{ cursor: 'pointer' }}
                key={row.key}
                onClick={() => {
                  setSelectedORDayKey(row.key)
                }}
              >
                {columns.map((column) => (
                  <TD
                    key={column.name}
                    css={{
                      backgroundColor:
                        sortOrder === undefined || sortOrder.sort === 'date'
                          ? dateBackgroundColors[row.date]
                          : undefined,
                    }}
                  >
                    {'formatter' in column ? (
                      column.formatter!(row, theme)
                    ) : (
                      <>{row[column.selector]}</>
                    )}
                  </TD>
                ))}
              </TR>
            )
          })}
        </TBody>
      </Table>
      {selectedORDay && (
        <TerminalCleansVideoBlade
          date={selectedORDay.date}
          roomId={selectedORDay.roomId}
          roomName={selectedORDay.roomName}
          siteName={selectedORDay.siteName}
          onClose={() => setSelectedORDayKey(undefined)}
        />
      )}
    </>
  )
}

function compareFn<T>(a: T, b: T) {
  if (a < b) return -1
  else if (a > b) return 1

  return 0
}
