import { useMemo, useState } from 'react'

import { DateTime } from 'luxon'
import { type ParsedQs } from 'qs'

import { useParsedSearchParams } from '@apella/hooks'
import {
  DEFAULT_DAYS_OF_WEEK,
  isDayOfWeek,
} from 'src/components/DayOfWeekFilter'
import { dateTimeToUrlFriendlyDate } from 'src/components/Filters/urls'
import { useTimezone } from 'src/Contexts'
import { type DayOfWeek } from 'src/pages/Insights/types'

export const DAY_OF_WEEK_FILTER = 'dayOfWeek'
export const SITE_FILTER = 'site'
export const ROOMS_FILTER = 'rooms'
export const START_DATE_FILTER = 'startDate'
export const END_DATE_FILTER = 'endDate'

const TERMINAL_CLEANS_FILTER_LOCAL_STORAGE_KEY = 'terminalCleansFilters'

type TerminalCleansQueryFilters = {
  [DAY_OF_WEEK_FILTER]: DayOfWeek[]
  [END_DATE_FILTER]: string
  [ROOMS_FILTER]: string[]
  [SITE_FILTER]?: string
  [START_DATE_FILTER]: string
}

type TerminalCleansFilters = Omit<
  TerminalCleansQueryFilters,
  typeof START_DATE_FILTER | typeof END_DATE_FILTER
> & {
  [END_DATE_FILTER]: DateTime
  [START_DATE_FILTER]: DateTime
}

export const useTerminalCleansFilters = ({
  maxDate,
}: {
  maxDate: DateTime
}) => {
  const { timezone } = useTimezone()

  const [defaults] = useState<TerminalCleansFilters>({
    [SITE_FILTER]: undefined,
    [ROOMS_FILTER]: [],
    [START_DATE_FILTER]: maxDate.minus({ days: 7 }),
    [END_DATE_FILTER]: maxDate,
    [DAY_OF_WEEK_FILTER]: DEFAULT_DAYS_OF_WEEK,
  })

  const queryDefaults: TerminalCleansQueryFilters = useMemo(() => {
    return {
      ...defaults,
      [START_DATE_FILTER]: dateTimeToUrlFriendlyDate(
        defaults[START_DATE_FILTER]
      ),
      [END_DATE_FILTER]: dateTimeToUrlFriendlyDate(defaults[END_DATE_FILTER]),
    }
  }, [defaults])

  const { params, onSearch, ...rest } =
    useParsedSearchParams<TerminalCleansQueryFilters>({
      defaults: queryDefaults,
      onClear,
    })

  const {
    [SITE_FILTER]: selectedSiteIdParam,
    [ROOMS_FILTER]: selectedRoomIdsParam,
    [START_DATE_FILTER]: selectedStartDateParam,
    [END_DATE_FILTER]: selectedEndDateParam,
    [DAY_OF_WEEK_FILTER]: selectedDaysOfWeekParam,
  } = params

  const selectedDaysOfWeek = isDayOfWeekArray(selectedDaysOfWeekParam)
    ? selectedDaysOfWeekParam
    : defaults[DAY_OF_WEEK_FILTER]
  const selectedSiteId =
    typeof selectedSiteIdParam === 'string'
      ? selectedSiteIdParam
      : defaults[SITE_FILTER]
  const selectedRoomIds =
    Array.isArray(selectedRoomIdsParam) &&
    selectedRoomIdsParam.every((roomId) => typeof roomId === 'string')
      ? selectedRoomIdsParam
      : defaults[ROOMS_FILTER]

  const parseDate = (
    possibleDate: ParsedQs['string'],
    defaultDate: DateTime
  ) =>
    typeof possibleDate === 'string'
      ? DateTime.fromISO(possibleDate, { zone: timezone })
      : defaultDate

  const parsedStartDate = parseDate(
    selectedStartDateParam,
    defaults[START_DATE_FILTER]
  )
  const parsedEndDate = parseDate(
    selectedEndDateParam,
    defaults[END_DATE_FILTER]
  )

  const selectedEndDate =
    parsedEndDate <= defaults[END_DATE_FILTER]
      ? parsedEndDate
      : defaults[END_DATE_FILTER]

  const selectedStartDate =
    parsedStartDate < selectedEndDate
      ? parsedStartDate
      : selectedEndDate.minus({ days: 7 })

  return {
    selectedSiteId,
    selectedRoomIds,
    selectedStartDate,
    selectedEndDate,
    selectedDaysOfWeek,
    onSearch,
    ...rest,
  }
}

const isDayOfWeekArray = (val: ParsedQs[string]): val is DayOfWeek[] => {
  return (
    Array.isArray(val) &&
    val.every(
      (day): day is DayOfWeek => typeof day === 'string' && isDayOfWeek(day)
    )
  )
}

const onClear = () =>
  window.localStorage.setItem(TERMINAL_CLEANS_FILTER_LOCAL_STORAGE_KEY, '')
