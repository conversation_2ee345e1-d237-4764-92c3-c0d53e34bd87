import { useState, useMemo } from 'react'

import { gql, useQuery } from '@apollo/client'
import { DateTime, Duration } from 'luxon'

import {
  Blade,
  Clean,
  P3,
  theme,
  Caps2,
  Span3,
  remSpacing,
  FlexContainer,
  MenuItem,
  But<PERSON>,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'
import {
  createWeekRecord,
  getDayOfWeek,
} from 'src/modules/dayOfWeekConfig/utils'
import { onClickContactSupport } from 'src/utils/toggleBeacon'

import { EventSourceType } from '../types'
import { VideoBladeContent } from '../VideoBlade/VideoBlade'
import {
  GetRoomPrimeTimeData,
  GetRoomPrimeTimeDataVariables,
  GetTerminalCleansCases,
  GetTerminalCleansCasesVariables,
} from './__generated__'
import { TERMINAL_CLEAN_EVENT_NAMES } from './constants'

export default function TerminalCleansVideoBlade({
  roomId,
  roomName,
  siteName,
  date: dateStr,
  onClose,
}: {
  roomId: string
  roomName: string
  siteName: string
  /** ISO date string YYYY-MM-DD */
  date: string
  onClose: () => void
}) {
  const { timezone } = useTimezone()
  const date = DateTime.fromISO(dateStr, { zone: timezone })
  const videoBoundsList = useTCVideoBounds({ date, roomId })
  const [selectedVideoIdx, setSelectedVideoIdx] = useState(0)
  const videoBounds = videoBoundsList[selectedVideoIdx]

  return (
    <Blade size="lg" side="right" onClose={onClose} isOpen>
      <Blade.Header>
        <Blade.Title icon={<Clean size="sm" />}>
          <>
            <P3>{roomName}</P3>
            <P3 css={{ color: theme.palette.text.secondary }}>
              <Caps2>{siteName}</Caps2>
              <Span3
                css={{
                  marginLeft: remSpacing.xsmall,
                  marginRight: remSpacing.xsmall,
                }}
              >
                &#8226;
              </Span3>
            </P3>
          </>
        </Blade.Title>
        <FlexContainer>
          <Button appearance="link" onClick={onClickContactSupport} size="sm">
            Submit feedback
          </Button>
          <Blade.CloseButton onClose={onClose} />
        </FlexContainer>
      </Blade.Header>
      <FlexContainer
        css={{
          padding: remSpacing.medium,
          overflowY: 'auto',
          height: 'calc(100vh - 65px)',
        }}
        gap={remSpacing.medium}
        direction="column"
      >
        {videoBounds === undefined ? (
          'No cases on this date.'
        ) : (
          <VideoBladeContent
            key={videoBounds[0].toISO()}
            roomId={roomId}
            startTime={videoBounds[0].toISO()}
            endTime={videoBounds[1].toISO()}
            time={videoBounds[0].toISO()}
            onClose={onClose}
            showHeader={false}
            eventNames={[...TERMINAL_CLEAN_EVENT_NAMES]}
          />
        )}
      </FlexContainer>
      {videoBoundsList.length > 1 && (
        <FlexContainer
          css={{ width: '100%', padding: remSpacing.small }}
          justifyContent="center"
        >
          {videoBoundsList.map(([startTime, endTime], idx) => (
            <MenuItem
              key={idx}
              active={selectedVideoIdx == idx}
              onClick={() => setSelectedVideoIdx(idx)}
            >
              <MenuItem.Title>
                {startTime.toFormat('HH:mm')} - {endTime.toFormat('HH:mm')}
              </MenuItem.Title>
            </MenuItem>
          ))}
        </FlexContainer>
      )}
    </Blade>
  )
}

export const GET_TERMINAL_CLEANS_CASES = gql`
  query GetTerminalCleansCases(
    $currentDayStart: DateTime!
    $nextDayEnd: DateTime!
    $roomId: ID!
  ) {
    casePhases: phases(
      query: {
        minEndTime: $currentDayStart
        maxStartTime: $nextDayEnd
        roomIds: [$roomId]
        sourceType: "${EventSourceType.Unified}"
        type: CASE
        statuses: [VALID]
      }
      orderBy: [
        { sort: "startTime", direction: ASC }
      ]
    ) {
      edges {
        node {
          id
          startTime
          endTime
          duration
        }
      }
    }

  }
`

/**
 * Gets the start / end time for the Terminal Cleans video blade on that date.
 * The start time is the last wheels out event on that date + a buffer.
 * If there are no wheels out events on that day (no cases), returns undefined.
 * The end time is the first wheels in event on the next day - a buffer.
 * If there are no wheels in events the next day, the end time is 6am the next day.
 *
 *
 * Gets the list of video bounds for the Terminal Cleans video blade, given a single date + room.
 *
 * Videos are from wheels-out of one case until wheels-in of the next case, minus a 5 min buffer.
 *
 * The bounds for the videos are defined as follows:
 *
 * The first video starts:
 * * At the end of prime time of the current day OR
 * * At the end of the first case that overlaps with prime time
 *
 * The last video ends:
 * * at the start of the first case that ends after prime time starts the next day
 *
 */
const useTCVideoBounds = ({
  date: currentDay,
  roomId,
}: {
  date: DateTime
  roomId: string
}): [DateTime, DateTime][] => {
  const { timezone } = useTimezone()
  const nextDay = currentDay.plus({ days: 1 })
  const { currentDayCutoff, nextDayCutoff } = useCutoffTimes(roomId, currentDay)

  const { data } = useQuery<
    GetTerminalCleansCases,
    GetTerminalCleansCasesVariables
  >(GET_TERMINAL_CLEANS_CASES, {
    variables: {
      currentDayStart: currentDay.startOf('day').toISO(),
      nextDayEnd: nextDay.endOf('day').toISO(),
      roomId,
    },
  })

  const casePhases = (
    data?.casePhases.edges.map((edge) => edge.node) ?? []
  ).map((phase) => {
    return {
      ...phase,
      startDateTime: DateTime.fromISO(phase.startTime, { zone: timezone }),
      endDateTime: phase.endTime
        ? DateTime.fromISO(phase.endTime, { zone: timezone })
        : null,
    }
  })

  return getVideoBoundsList(casePhases, currentDayCutoff, nextDayCutoff)
}

/**
 * Rounds up a DateTime to the nearest minute.
 *
 * Examples:
 * ```
 * roundUpToMinute(DateTime.fromISO('2023-10-01T12:34:56.789Z')) // 2023-10-01T12:35:00.000Z
 * roundUpToMinute(DateTime.fromISO('2023-10-01T12:35:00.000Z')) // 2023-10-01T12:35:00.000Z
 * ```
 *
 */
function roundUpToMinute(dt: DateTime) {
  if (dt.startOf('minute').valueOf() === dt.valueOf()) return dt

  return dt.plus({ minutes: 1 }).startOf('minute')
}

const TC_VIDEO_BUFFER = Duration.fromObject({ minutes: 5 })

/**
 * Gets the list of video bounds for the Terminal Cleans video blade.
 * Assume case phases with null end times are ongoing cases.
 *
 * @param casePhases - a list of phase start / end times.
 * @param currentDayCutoff - the earliest time the videos can start
 * @param nextDayCutoff - the latest time the videos can end
 * @param buffer - videos will not start or end within the buffer from case start / end times.
 * @returns A list of video bounds, where each bound is a tuple of start and end times.
 */
export function getVideoBoundsList(
  casePhases: {
    startDateTime: DateTime
    endDateTime: DateTime | null
  }[],
  currentDayCutoff: DateTime,
  nextDayCutoff: DateTime,
  buffer: Duration = TC_VIDEO_BUFFER
): [DateTime, DateTime][] {
  const casesWithBuffer: [DateTime, DateTime | undefined][] = casePhases.map(
    ({ startDateTime, endDateTime }) => {
      return [
        startDateTime.startOf('minute').minus(buffer),
        endDateTime === null
          ? undefined
          : roundUpToMinute(endDateTime).plus(buffer),
      ]
    }
  )

  // Filter cases outside of cutoff times
  const casesWithinCutoff = casesWithBuffer.filter(
    ([caseBufferedStart, caseBufferedEnd]) =>
      !(
        (caseBufferedEnd && caseBufferedEnd < currentDayCutoff) ||
        caseBufferedStart > nextDayCutoff
      )
  )

  const boundsList: DateTime[] = [currentDayCutoff]

  for (const [caseBufferedStart, caseBufferedEnd] of mergeTimeRanges(
    casesWithinCutoff
  )) {
    // Process case start (possible video end)
    if (caseBufferedStart > boundsList[boundsList.length - 1]) {
      // Case start is after the most recent video start
      // End most recent video at case start minus buffer
      boundsList.push(caseBufferedStart)
    }

    // Process case end (possible video start)
    if (boundsList.length % 2 === 0) {
      // last element is a video end, so check if we should start a new video

      // Don't push any videos if case end is after the next day cutoff
      if (!caseBufferedEnd || nextDayCutoff <= caseBufferedEnd) break

      // start video at case end
      boundsList.push(caseBufferedEnd)
    } else {
      // Last element is video start
      if (caseBufferedEnd && caseBufferedEnd < nextDayCutoff) {
        // Update most recently started video if case ends before cutoff
        boundsList[boundsList.length - 1] = caseBufferedEnd
      } else {
        // Otherwise, delete most recently started video
        boundsList.pop()
        break
      }
    }
  }

  // Ensure list is even in length b/c it represents start / end times
  if (boundsList.length % 2 === 1) {
    boundsList.push(nextDayCutoff)
  }

  return boundsList.reduce<[DateTime, DateTime][]>((acc, el, idx) => {
    if (idx % 2 === 1) {
      return [...acc, [boundsList[idx - 1], el]]
    }
    return acc
  }, [])
}

/**
 * Merge a list of time ranges.
 *
 * If the end time of a time range is undefined it is considered "ongoing", and
 * any time range that starts after the ongoing time range will be merged into it.
 */
function mergeTimeRanges(
  timeRanges: [DateTime, DateTime | undefined][]
): [DateTime, DateTime | undefined][] {
  if (timeRanges.length === 0) return []

  const timeRangesSortedByStartTime = timeRanges.toSorted(
    ([a], [b]) => a.valueOf() - b.valueOf()
  )

  const [firstStart, firstEnd] = timeRangesSortedByStartTime[0]

  // Ongoing case, so return
  if (firstEnd === undefined) return [[firstStart, firstEnd]]

  const mergedRanges: [DateTime, DateTime][] = [[firstStart, firstEnd]]

  for (const [start, end] of timeRangesSortedByStartTime.slice(1)) {
    const [lastStart, lastEnd] = mergedRanges[mergedRanges.length - 1]

    if (start <= lastEnd) {
      // Overlapping ranges, merge them
      if (end === undefined) {
        return [...mergedRanges.slice(0, -1), [lastStart, undefined]]
      }

      mergedRanges[mergedRanges.length - 1][1] = DateTime.max(end, lastEnd)
    } else {
      // Non-overlapping range, add it to the list
      if (end === undefined) {
        return [...mergedRanges, [start, undefined]]
      }
      mergedRanges.push([start, end])
    }
  }

  return mergedRanges
}

/**
 * Get the default terminal cleans start / end times for each day of the week.
 *
 * * `currentDayCutoff` is the earliest time the video(s) can start.
 *   * If prime time is defined for that day, then this is the end of prime time.
 *   * If prime time is not defined, then this is the start of the day.
 *
 * * `nextDayCutoff` is the latest time the video(s) can end.
 *   * If prime time is defined for that day, then this is the start of prime time.
 *   * If prime time is not defined, then this the end of the day (Note that this is
 *     actually 00:00 of the following day).
 */
function useCutoffTimes(
  roomId: string,
  currentDay: DateTime
): {
  loading: boolean
  currentDayCutoff: DateTime
  nextDayCutoff: DateTime
} {
  const { loading, data: rawData } = useQuery<
    GetRoomPrimeTimeData,
    GetRoomPrimeTimeDataVariables
  >(GET_ROOM_PRIME_TIME_DATA, {
    variables: { roomId },
  })

  const { currentDayCutoff, nextDayCutoff } = useMemo(() => {
    const nextDay = currentDay.plus({ days: 1 })

    const primeTimeConfig =
      rawData?.room?.primeTimeConfig ?? createWeekRecord(() => null)

    const primeTimeForDayOfWeek = primeTimeConfig[getDayOfWeek(currentDay)]

    if (!primeTimeForDayOfWeek) {
      return {
        currentDayCutoff: currentDay.startOf('day'),
        nextDayCutoff: nextDay.plus({ days: 1 }).startOf('day'),
      }
    }

    return {
      currentDayCutoff: currentDay
        .set(parseTime(primeTimeForDayOfWeek.endTime))
        .startOf('minute'),
      nextDayCutoff: nextDay
        .set(parseTime(primeTimeForDayOfWeek.startTime))
        .startOf('minute'),
    }
  }, [currentDay, rawData?.room?.primeTimeConfig])

  return { loading, currentDayCutoff, nextDayCutoff }
}

const GET_ROOM_PRIME_TIME_DATA = gql`
  query GetRoomPrimeTimeData($roomId: String!) {
    room(id: $roomId) {
      id
      primeTimeConfig {
        id
        sunday {
          startTime
          endTime
        }
        monday {
          startTime
          endTime
        }
        tuesday {
          startTime
          endTime
        }
        wednesday {
          startTime
          endTime
        }
        thursday {
          startTime
          endTime
        }
        friday {
          startTime
          endTime
        }
        saturday {
          startTime
          endTime
        }
      }
    }
  }
`

const parseTime = (time: string) => {
  const [hour, minute] = time.split(':').map(Number)
  return { hour, minute }
}
