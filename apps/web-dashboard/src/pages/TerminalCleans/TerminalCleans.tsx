import { useFlags } from 'launchdarkly-react-client-sdk'

import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'

import LegacyTerminalCleans from './LegacyTerminalCleans'
import TerminalCleansV0 from './TerminalCleansV0'

const TerminalCleans = (): React.JSX.Element => {
  const { terminalCleansV0 } = useFlags<WebDashboardFeatureFlagSet>()
  if (terminalCleansV0) {
    return <TerminalCleansV0 />
  }
  return <LegacyTerminalCleans />
}

export default TerminalCleans
