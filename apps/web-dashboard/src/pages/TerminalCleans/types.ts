import { GetCurrentUser } from 'src/modules/user/__generated__'

type GetCurrentUser_me_organizations_edges_node_sites_edges_node = NonNullable<
  GetCurrentUser['me']
>['organizations']['edges'][number]['node']['sites']['edges'][number]['node']

type GetCurrentUser_me_organizations_edges_node_sites_edges_node_rooms_edges_node =
  GetCurrentUser_me_organizations_edges_node_sites_edges_node['rooms']['edges'][number]['node']

export interface RoomAtSite
  extends GetCurrentUser_me_organizations_edges_node_sites_edges_node_rooms_edges_node {
  site: GetCurrentUser_me_organizations_edges_node_sites_edges_node
}
