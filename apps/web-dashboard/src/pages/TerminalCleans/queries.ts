import { gql } from '@apollo/client'

import { EventSourceType } from '../types'

export const MEASUREMENT_PERIOD_FRAGMENT = gql`
  fragment MeasurementPeriodFragment on MeasurementPeriod {
    id
    name
    site {
      organizationId
      id
      name
    }
    measurementPeriodStart
    measurementPeriodEnd
    annotationTaskType {
      id
      name
    }
    roomIds
    isoDaysOfWeek
  }
`

export const GET_MEASUREMENT_PERIOD = gql`
  ${MEASUREMENT_PERIOD_FRAGMENT}
  query GetMeasurementPeriod($id: String!) {
    measurementPeriod(id: $id) {
      ...MeasurementPeriodFragment
    }
  }
`

export const GET_MEASUREMENT_PERIODS = gql`
  ${MEASUREMENT_PERIOD_FRAGMENT}
  query GetMeasurementPeriods(
    $siteId: String!
    $measurementPeriodStart: Date!
    $measurementPeriodEnd: Date!
  ) {
    measurementPeriods(
      query: {
        siteId: $siteId
        measurementPeriodStart: $measurementPeriodStart
        measurementPeriodEnd: $measurementPeriodEnd
      }
      orderBy: [{ sort: "startDate", direction: DESC }]
    ) {
      totalRecords
      edges {
        node {
          ...MeasurementPeriodFragment
        }
      }
    }
  }
`

export const UPSERT_MEASUREMENT_PERIOD = gql`
  mutation UpsertMeasurementPeriod(
    $id: ID
    $name: String!
    $siteId: ID!
    $roomIds: [ID!]!
    $isoDaysOfWeek: [Int!]!
    $annotationTaskTypeId: ID!
    $measurementPeriodStart: Date!
    $measurementPeriodEnd: Date!
  ) {
    measurementPeriodUpsert(
      measurementPeriod: {
        id: $id
        name: $name
        siteId: $siteId
        roomIds: $roomIds
        isoDaysOfWeek: $isoDaysOfWeek
        annotationTaskTypeId: $annotationTaskTypeId
        measurementPeriodStart: $measurementPeriodStart
        measurementPeriodEnd: $measurementPeriodEnd
      }
    ) {
      success
    }
  }
`

export const DELETE_MEASUREMENT_PERIOD = gql`
  mutation DeleteMeasurementPeriod($id: ID!) {
    measurementPeriodDelete(input: { id: $id }) {
      success
    }
  }
`

export const EVENT_FRAGMENT = gql`
  fragment EventFragment on Event {
    id
    name # code name/id (e.g. "patient_wheels_in")
    label # friendly name (e.g. "Patient Wheels In")
    startTime
    source
    sourceType
    site {
      id
      name
    }
    room {
      id
      name
    }
  }
`

export const GET_APELLA_CASE_DATA = gql`
  query GetApellaCaseData(
    $siteIds: [String!]
    $roomIds: [String!]
    $minEndTime: DateTime!
    $maxStartTime: DateTime!
  ) {
    sites(siteIds: $siteIds) {
      edges {
        node {
          id
          name
          rooms(roomIds: $roomIds) {
            edges {
              node {
                id
                name
                apellaCases(
                  query: {
                    minEndTime: $minEndTime
                    maxStartTime: $maxStartTime
                  }
                ) {
                  edges {
                    node {
                      id
                      type
                      startTime
                      endTime
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`

export const GET_TERMINAL_CLEANS_DATA = gql`
  ${EVENT_FRAGMENT}
  query GetTerminalCleansData(
    $siteIds: [ID!]
    $roomIds: [ID!]
    $minTime: DateTime!
    $maxTime: DateTime!
    $eventNames: [String!]
    $organizationId: ID
    $siteId: ID
    $typeIds: [ID!]
  ){
    terminalCleanEvents: eventSearch(
      query: {
        minStartTime: $minTime
        maxStartTime: $maxTime
        siteIds: $siteIds
        roomIds: $roomIds
        sourceType: "human_gt"
        includeDeleted: false
        eventNames: $eventNames
      }
      orderBy: [
        { sort: "startTime", direction: DESC }
        { sort: "siteId", direction: ASC }
        { sort: "roomId", direction: ASC }
      ]
    ) {
      edges {
        node {
          ...EventFragment
        }
      }
    }

    annotationTasks(
      query: {
        startTime: $minTime
        endTime: $maxTime
        organizationId: $organizationId
        siteId: $siteId
        typeIds: $typeIds
      }
      orderBy: [
        { sort: "startTime", direction: DESC }
        { sort: "siteId", direction: ASC }
        { sort: "roomId", direction: ASC }
      ]
    ) {
      edges {
        node {
          id
          siteId
          roomId
          startTime
          endTime
          status
        }
      }
    }

    phases(
      query: {
        minStartTime: $minTime
        maxEndTime: $maxTime
        siteIds: $siteIds
        roomIds: $roomIds
        sourceType: "${EventSourceType.Unified}"
        type: TERMINAL_CLEAN
        statuses: [VALID]
      }
      orderBy: [
        { sort: "startTime", direction: ASC }
        { sort: "siteId", direction: ASC }
        { sort: "roomId", direction: ASC }
      ]
    ) {
      edges {
        node {
          id
          site {
            id
          }
          room {
            id
          }
          phaseDetail {
            type
            title
            slug
          }
          startTime
          endTime
          duration
          timeRangeVerified
        }
      }
    }
  }`
