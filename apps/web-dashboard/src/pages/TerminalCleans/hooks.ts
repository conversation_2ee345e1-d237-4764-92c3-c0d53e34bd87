import { useEffect, useMemo, useState } from 'react'

import { useQuery } from '@apollo/client'
import { DateTime, Duration } from 'luxon'

import { CaseType, TaskStatus } from 'src/__generated__/globalTypes'
import { ISO_DAYS_OF_WEEK } from 'src/components/DayOfWeekFilter'
import { urlFriendlyDateToJSDate } from 'src/components/Filters/urls'
import { useTimezone } from 'src/Contexts'
import { GetCurrentUser } from 'src/modules/user/__generated__'
import { useCurrentUser } from 'src/modules/user/hooks'

import { ISOWeekDate } from '../Insights/types'
import { ApellaRoom, ApellaSite } from '../types'
import {
  GetApellaCaseData,
  GetApellaCaseDataVariables,
  GetMeasurementPeriod,
  GetTerminalCleansData,
  GetTerminalCleansDataVariables,
} from './__generated__'
import {
  TERMINAL_CLEAN_BASIC_SCORING_ID,
  TERMINAL_CLEAN_EVENT_NAMES,
  TERMINAL_CLEAN_TIME_OFFSET_HOURS,
  TERMINAL_CLEAN_VIDEO_BUFFER_MINUTES,
} from './constants'
import { GET_APELLA_CASE_DATA, GET_TERMINAL_CLEANS_DATA } from './queries'
import { RoomAtSite } from './types'

export type Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>

type GetAnnotationTasks_annotationTasks_edges_node =
  GetTerminalCleansData['annotationTasks']['edges'][number]['node']

const emptyEventCounts = {
  or_table_cleaning_start: 0,
  back_table_cleaning_start: 0,
  mopping_floor_start: 0,
  or_lights_and_booms_cleaning_start: 0,
  prep_table_cleaning_start: 0,
  nursing_fixed_work_station_cleaning_start: 0,
  nurse_mobile_work_station_cleaning_start: 0,
}

interface Timestamps {
  cleaningEndTime?: DateTime
  cleaningStartTime?: DateTime
}

export enum CleaningObserved {
  NO = 'no',
  PENDING = 'pending',
  YES = 'yes',
}

export enum ScoringType {
  BASIC = 'basic',
  FULL = 'full',
}

export interface IBaseTerminalCleansData {
  date: string
  key: string
  roomId: string
  roomName: string
  siteId: string
  siteName: string
  totalCases: number
}

export interface ITerminalCleansData
  extends Timestamps,
    IBaseTerminalCleansData {
  annotationTask: GetAnnotationTasks_annotationTasks_edges_node | undefined
  cleaningDuration: Duration | undefined
  cleaningObserved: CleaningObserved
  cleaningScore: number
  eventCounts: Record<string, number | undefined> | undefined
  measurementPeriodId: string
}

const formatDateTimeAsDate = (date: DateTime) => date.toFormat('yyyy-MM-dd')

const KeyIt = (date: string, roomId: string) => `${date}-${roomId}`

const getTCRowId = (
  time: string | DateTime,
  timezone: string,
  roomId: string
) => {
  if (typeof time === 'string') {
    time = DateTime.fromISO(time)
  }
  const date = formatDateTimeAsDate(
    time.setZone(timezone).minus({ hours: TERMINAL_CLEAN_TIME_OFFSET_HOURS })
  )

  return KeyIt(date, roomId)
}

interface IApellaCase {
  endTime?: DateTime
  id: string
  room: ApellaRoom
  site: ApellaSite
  startTime: DateTime
  type: CaseType
}

interface DayAndRoomToCasesMap {
  [index: string]: IApellaCase[]
}
interface DayAndRoomToTCPhasesMap {
  [index: string]: GetTerminalCleansData['phases']['edges'][number]['node'][]
}
interface DayAndRoomToTaskMap {
  [index: string]: GetAnnotationTasks_annotationTasks_edges_node
}

const calculateCleaningStats = ({
  dayAndRoomToCasesMap,
  dayAndRoomToTCPhasesMap,
  key,
  timezone,
  day,
  dayIdx,
  dayArr,
  room,
}: {
  dayAndRoomToCasesMap: DayAndRoomToCasesMap
  dayAndRoomToTCPhasesMap?: DayAndRoomToTCPhasesMap
  key: string
  timezone: string
  day: string
  dayIdx: number
  dayArr: string[]
  room: RoomAtSite
}) => {
  const DAY_FORMAT = 'M/d/yyyy'

  let cleaningDuration
  let cleaningStartTime
  let cleaningEndTime

  if (
    dayAndRoomToTCPhasesMap?.[key] &&
    dayAndRoomToTCPhasesMap[key].length > 0
  ) {
    cleaningDuration = dayAndRoomToTCPhasesMap[key].reduce<Duration>(
      (acc, curr) =>
        curr.duration ? acc.plus(Duration.fromISO(curr.duration)) : acc,
      Duration.fromMillis(0)
    )
    cleaningStartTime = DateTime.fromISO(
      dayAndRoomToTCPhasesMap[key][0].startTime
    )
    const lastTCEnd =
      dayAndRoomToTCPhasesMap[key][dayAndRoomToTCPhasesMap[key].length - 1]
        .endTime

    if (lastTCEnd) cleaningEndTime = DateTime.fromISO(lastTCEnd)
  } else {
    // No Terminal Cleaning phases found, we'd make best effort determining Case phases for the day to determine video start/end time
    const currCases = dayAndRoomToCasesMap[key]
    const lastCurrCase =
      currCases?.length > 0 ? currCases[currCases.length - 1] : null

    if (lastCurrCase) {
      const curr6AM = DateTime.fromFormat(day, DAY_FORMAT, {
        zone: timezone,
      }).plus({
        hours: TERMINAL_CLEAN_TIME_OFFSET_HOURS,
      })

      if (dayIdx < dayArr.length - 1) {
        const nextKey = KeyIt(dayArr[dayIdx + 1], room.id)
        const nextCases = dayAndRoomToCasesMap[nextKey]
        const firstNextCase = nextCases?.length > 0 ? nextCases[0] : null

        cleaningStartTime = lastCurrCase.endTime
          ? DateTime.max(
              lastCurrCase.endTime.plus({
                minutes: TERMINAL_CLEAN_VIDEO_BUFFER_MINUTES,
              }),
              curr6AM
            )
          : undefined

        if (firstNextCase) {
          const next6AM = DateTime.fromFormat(dayArr[dayIdx + 1], DAY_FORMAT, {
            zone: timezone,
          }).plus({
            hours: TERMINAL_CLEAN_TIME_OFFSET_HOURS,
          })

          cleaningEndTime = DateTime.min(
            firstNextCase.startTime.minus({
              minutes: TERMINAL_CLEAN_VIDEO_BUFFER_MINUTES,
            }),
            next6AM
          )
        }
      }
    }
  }

  return {
    cleaningDuration,
    cleaningStartTime,
    cleaningEndTime,
  }
}

const extractApellaCases = ({
  casesData,
  siteId,
  timezone,
}: {
  casesData: GetApellaCaseData | undefined
  siteId: string
  timezone: string
}) => {
  if (siteId === '' || !casesData?.sites?.edges) {
    return []
  }

  const apellaCases = casesData?.sites.edges.flatMap(({ node: site }) => {
    const siteObj = {
      id: site.id,
      name: site.name,
    }

    return site.rooms.edges.flatMap((r) => {
      const room = {
        id: r.node.id,
        name: r.node.name,
      }

      return r.node.apellaCases.edges.map(({ node: ac }) => {
        const startTime = DateTime.fromISO(ac.startTime).setZone(timezone)
        let endTime
        if (ac.endTime) {
          endTime = DateTime.fromISO(ac.endTime).setZone(timezone)
        }

        return {
          id: ac.id,
          site: siteObj,
          room,
          startTime: startTime,
          endTime: endTime,
          type: ac.type,
        }
      })
    })
  })
  return apellaCases
}

const createDayAndRoomToCasesMap = ({
  apellaCases,
  timezone,
}: {
  apellaCases: IApellaCase[]
  timezone: string
}) => {
  return apellaCases.reduce<DayAndRoomToCasesMap>((acc, currPhase) => {
    const id = getTCRowId(currPhase.startTime, timezone, currPhase.room.id)
    acc[id] = (acc[id] ?? []).concat(currPhase)

    return acc
  }, {})
}

const listIncludedDates = ({
  start,
  end,
  today,
  isoDaysOfWeek,
}: {
  start: DateTime
  end: DateTime
  today: DateTime
  isoDaysOfWeek: ISOWeekDate[]
}) => {
  const dates = [start]
  while (dates[dates.length - 1].plus({ day: 1 }) < DateTime.min(end, today)) {
    dates.push(dates[dates.length - 1].plus({ day: 1 }))
  }
  return dates
    .filter((date) => isoDaysOfWeek.includes(date.weekday))
    .map((date) => formatDateTimeAsDate(date))
}

export interface IUseMeasurementPeriodLocationData {
  organizationId: string
  rooms: RoomAtSite[]
  siteId: string
}

interface IUseMeasurementPeriodData
  extends Optional<
    IUseMeasurementPeriodLocationData,
    'organizationId' | 'siteId'
  > {
  annotationTaskTypeId?: string
  isoDaysOfWeek?: ISOWeekDate[]
  measurementPeriodEnd: DateTime
  measurementPeriodId?: string
  measurementPeriodStart: DateTime
  skip: boolean
  today: DateTime
}

export const useMeasurementPeriodData = ({
  skip,
  measurementPeriodId = '',
  annotationTaskTypeId = '',
  organizationId = '',
  siteId = '',
  rooms,
  today,
  measurementPeriodStart,
  measurementPeriodEnd,
  isoDaysOfWeek = ISO_DAYS_OF_WEEK,
}: IUseMeasurementPeriodData) => {
  const { timezone } = useTimezone()

  const scoringType = useMemo(
    () =>
      TERMINAL_CLEAN_BASIC_SCORING_ID === annotationTaskTypeId
        ? ScoringType.BASIC
        : ScoringType.FULL,
    [annotationTaskTypeId]
  )
  const roomIds = rooms.map((room) => room.id)

  const { loading: casesLoading, data: casesData } = useQuery<
    GetApellaCaseData,
    GetApellaCaseDataVariables
  >(GET_APELLA_CASE_DATA, {
    variables: {
      siteIds: [siteId],
      roomIds,
      minEndTime: measurementPeriodStart.toISO(),
      maxStartTime: measurementPeriodEnd.toISO(),
    },
    skip,
  })

  const { loading: terminalCleansDataLoading, data: terminalCleansData } =
    useQuery<GetTerminalCleansData, GetTerminalCleansDataVariables>(
      GET_TERMINAL_CLEANS_DATA,
      {
        variables: {
          minTime: measurementPeriodStart.toISO(),
          maxTime: measurementPeriodEnd.toISO(),
          siteIds: [siteId],
          roomIds,
          siteId,
          organizationId,
          typeIds: [annotationTaskTypeId],
          eventNames: [...TERMINAL_CLEAN_EVENT_NAMES],
        },
        skip,
      }
    )

  const apellaCases = useMemo(() => {
    return extractApellaCases({ casesData, siteId, timezone })
  }, [siteId, casesData, timezone])

  const dayAndRoomToCasesMap = useMemo(
    () => createDayAndRoomToCasesMap({ apellaCases, timezone }),
    [apellaCases, timezone]
  )

  const dayAndRoomToTCPhasesMap = useMemo(
    () =>
      terminalCleansData?.phases.edges.reduce<DayAndRoomToTCPhasesMap>(
        (acc, { node: currPhase }) => {
          const id = getTCRowId(
            currPhase.startTime,
            timezone,
            currPhase.room.id
          )
          acc[id] = (acc[id] ?? []).concat(currPhase)

          return acc
        },
        {}
      ),
    [terminalCleansData?.phases.edges, timezone]
  )

  const dayAndRoomToTaskMap = useMemo(
    () =>
      terminalCleansData?.annotationTasks.edges.reduce<DayAndRoomToTaskMap>(
        (acc, { node: currTask }) => {
          const id = getTCRowId(currTask.startTime, timezone, currTask.roomId)
          acc[id] = currTask

          return acc
        },
        {}
      ),
    [terminalCleansData?.annotationTasks.edges, timezone]
  )

  const days = useMemo(() => {
    return listIncludedDates({
      start: measurementPeriodStart,
      end: measurementPeriodEnd,
      today,
      isoDaysOfWeek,
    })
  }, [measurementPeriodStart, measurementPeriodEnd, today, isoDaysOfWeek])

  const terminalCleans: ITerminalCleansData[] = useMemo(() => {
    if (!terminalCleansData?.terminalCleanEvents) {
      return []
    }

    const events = terminalCleansData.terminalCleanEvents.edges.map(
      (edge) => edge.node
    )

    const eventCountsByDateAndRoom = events.reduce<{
      [index: string]: { [index: string]: number | undefined } | undefined
    }>((acc, event) => {
      // We subtract the offset hours here so that cleans that happen before that time are counted as the previous day
      const id = getTCRowId(event.startTime, timezone, event.room.id)
      const prevCount = (acc[id] ?? {})[event.name] ?? 0

      acc[id] = {
        ...acc[id],
        [event.name]: prevCount + 1,
      }

      return acc
    }, {})

    return days.flatMap((day, dayIdx, dayArr) =>
      rooms.map((room) => {
        const key = KeyIt(day, room.id)
        const eventCounts = eventCountsByDateAndRoom[key] ?? emptyEventCounts

        const { cleaningDuration, cleaningStartTime, cleaningEndTime } =
          calculateCleaningStats({
            dayAndRoomToCasesMap,
            dayAndRoomToTCPhasesMap,
            key,
            timezone,
            day,
            dayIdx,
            dayArr,
            room,
          })

        let cleaningObserved

        if (
          dayAndRoomToTaskMap?.[key]?.status &&
          [
            TaskStatus.NOT_STARTED,
            TaskStatus.IN_PROGRESS,
            TaskStatus.IN_REVIEW,
            TaskStatus.READY_FOR_REVIEW,
          ].includes(dayAndRoomToTaskMap[key].status)
        ) {
          cleaningObserved = CleaningObserved.PENDING
        } else {
          cleaningObserved = Object.values(eventCounts).some(
            (count) => count && count > 0
          )
            ? CleaningObserved.YES
            : CleaningObserved.NO
        }

        return {
          measurementPeriodId,
          key,
          date: day,
          roomId: room.id,
          roomName: room.name,
          siteId: room.site.id,
          siteName: room.site.name,
          eventCounts,
          annotationTask: dayAndRoomToTaskMap?.[key],
          totalCases: dayAndRoomToCasesMap
            ? (dayAndRoomToCasesMap[key]?.length ?? 0)
            : 0,
          cleaningScore:
            // Currently max score of 6
            (eventCounts.or_table_cleaning_start ? 1 : 0) +
            (eventCounts.back_table_cleaning_start ? 1 : 0) +
            (eventCounts.mopping_floor_start ? 1 : 0) +
            (eventCounts.or_lights_and_booms_cleaning_start ? 1 : 0) +
            (eventCounts.prep_table_cleaning_start ? 1 : 0) +
            ((eventCounts.nursing_fixed_work_station_cleaning_start ? 1 : 0) +
            (eventCounts.nurse_mobile_work_station_cleaning_start ? 1 : 0)
              ? 1
              : 0),
          cleaningDuration,
          cleaningObserved,
          cleaningStartTime,
          cleaningEndTime,
        }
      })
    )
  }, [
    terminalCleansData?.terminalCleanEvents,
    timezone,
    days,
    rooms,
    measurementPeriodId,
    dayAndRoomToTaskMap,
    dayAndRoomToCasesMap,
    dayAndRoomToTCPhasesMap,
  ])

  return {
    isLoading: casesLoading || terminalCleansDataLoading,
    terminalCleans,
    scoringType,
  }
}

export const useTerminalCleansFilterData = (siteId?: string) => {
  const [userOrgData, setUserOrgData] = useState<
    | NonNullable<
        GetCurrentUser['me']
      >['organizations']['edges'][number]['node']
    | undefined
  >(undefined)

  const {
    loading: filtersLoading,
    currentOrganization: activeOrganization,
    permissions: uiPermissions,
  } = useCurrentUser()

  useEffect(() => {
    if (!filtersLoading && activeOrganization) {
      const o = activeOrganization.node
      const userOrgData = activeOrganization
        ? {
            ...o,
            sites: {
              ...o.sites,
              edges: o.sites.edges.map((site) => ({
                ...site,
                node: {
                  ...site.node,
                  rooms: {
                    ...site.node.rooms,
                  },
                },
              })),
            },
          }
        : undefined

      setUserOrgData(userOrgData)
    }
  }, [filtersLoading, activeOrganization])

  const rooms: RoomAtSite[] = useMemo(() => {
    const filteredSites = userOrgData?.sites?.edges.filter(
      (site) => !siteId || siteId === site.node.id
    )
    return (
      filteredSites?.flatMap((site) =>
        site.node.rooms.edges.map((edge) => ({
          ...edge.node,
          site: site.node,
        }))
      ) ?? []
    )
  }, [userOrgData?.sites?.edges, siteId])

  return {
    activeOrganization,
    filtersLoading,
    rooms,
    uiPermissions,
    userOrgData,
  }
}

export const useTerminalCleansTimeOffset = (
  measurementPeriod?: GetMeasurementPeriod['measurementPeriod']
) => {
  const { timezone } = useTimezone()

  const [startWithOffset, endWithOffset, todayWithOffset] = useMemo<
    [DateTime, DateTime, DateTime]
  >(() => {
    if (!measurementPeriod) {
      return [DateTime.now(), DateTime.now(), DateTime.now()]
    }
    return [
      DateTime.fromISO(
        urlFriendlyDateToJSDate(
          measurementPeriod.measurementPeriodStart,
          timezone
        ),
        {
          setZone: true,
        }
      ).plus({ hours: TERMINAL_CLEAN_TIME_OFFSET_HOURS }),
      DateTime.fromISO(
        urlFriendlyDateToJSDate(
          measurementPeriod.measurementPeriodEnd,
          timezone
        ),
        {
          setZone: true,
        }
      ).plus({ hours: TERMINAL_CLEAN_TIME_OFFSET_HOURS }),
      DateTime.now()
        .setZone(timezone)
        .startOf('day')
        .plus({ hours: TERMINAL_CLEAN_TIME_OFFSET_HOURS }),
    ]
  }, [measurementPeriod, timezone])

  return {
    startWithOffset,
    endWithOffset,
    todayWithOffset,
  }
}

export const useCasesData = ({
  minEndTime,
  maxStartTime,
  siteId = '',
  rooms,
  today,
  isoDaysOfWeek = ISO_DAYS_OF_WEEK,
}: {
  minEndTime: DateTime
  maxStartTime: DateTime
  rooms: RoomAtSite[]
  siteId: string
  isoDaysOfWeek?: ISOWeekDate[]
  today: DateTime
}) => {
  const { timezone } = useTimezone()

  const roomIds = rooms.map((room) => room.id)

  const { loading: casesLoading, data: casesData } = useQuery<
    GetApellaCaseData,
    GetApellaCaseDataVariables
  >(GET_APELLA_CASE_DATA, {
    variables: {
      siteIds: [siteId],
      roomIds,
      minEndTime: minEndTime.toISO(),
      maxStartTime: maxStartTime.toISO(),
    },
  })

  const apellaCases = useMemo(() => {
    return extractApellaCases({ casesData, siteId, timezone })
  }, [siteId, casesData, timezone])

  const dayAndRoomToCasesMap = useMemo(
    () => createDayAndRoomToCasesMap({ apellaCases, timezone }),
    [apellaCases, timezone]
  )

  const days = useMemo(() => {
    return listIncludedDates({
      start: minEndTime,
      end: maxStartTime,
      today,
      isoDaysOfWeek,
    })
  }, [minEndTime, maxStartTime, today, isoDaysOfWeek])

  const casesDataForTable: IBaseTerminalCleansData[] = useMemo(() => {
    return days.flatMap((day) =>
      rooms.map((room) => {
        const key = KeyIt(day, room.id)

        return {
          key,
          date: day,
          roomId: room.id,
          roomName: room.name,
          siteId: room.site.id,
          siteName: room.site.name,
          totalCases: dayAndRoomToCasesMap
            ? (dayAndRoomToCasesMap[key]?.length ?? 0)
            : 0,
        }
      })
    )
  }, [dayAndRoomToCasesMap, days, rooms])

  return {
    isLoading: casesLoading,
    casesData: casesDataForTable,
  }
}
