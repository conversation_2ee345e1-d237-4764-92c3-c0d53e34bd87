import { useTheme } from '@emotion/react'

import { ButtonLink, ErrorPage, P2 } from '@apella/component-library'
import { LocationPath } from 'src/router/types'

export const PageNotFound = (): React.JSX.Element => {
  const theme = useTheme()

  return (
    <ErrorPage
      header={'404 Error'}
      body={<P2 color={theme.palette.text.secondary}>{'Page not found'}</P2>}
      actions={
        <ButtonLink
          to={{
            pathname: LocationPath.Root,
          }}
        >
          Go Home
        </ButtonLink>
      }
    />
  )
}
