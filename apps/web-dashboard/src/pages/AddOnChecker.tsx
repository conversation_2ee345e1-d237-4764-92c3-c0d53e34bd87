export const AddOnChecker = (
  procedures: string[] | string,
  isAddOn: boolean,
  uppercase: boolean
) => {
  const procedureNames = Array.isArray(procedures) ? procedures : [procedures]

  if (procedureNames.length === 0) {
    return
  }

  const proceduresString = procedureNames.join('; ')

  const finalString = uppercase
    ? proceduresString.toUpperCase()
    : proceduresString.toLowerCase()

  return isAddOn ? `++ ${finalString}` : finalString
}
