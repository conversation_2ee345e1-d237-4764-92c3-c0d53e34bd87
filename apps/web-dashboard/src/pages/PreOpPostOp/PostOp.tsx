import { useCallback, useEffect, useMemo, useState } from 'react'

import styled from '@emotion/styled'
import { sortBy } from 'lodash'
import { DateTime } from 'luxon'
import { rem } from 'polished'

import {
  H3,
  mediaQueries,
  NavigationTab,
  NavigationTabTitle,
  NavigationTabWrapper,
  P3,
  remSpacing,
  Table,
  TBody,
  TD,
  TH,
  THead,
  TR,
} from '@apella/component-library'
import { CaseType } from 'src/__generated__/globalTypes'
import { ToggleableCaseKey } from 'src/components/Keys'
import { PrePostOpApellaCase } from 'src/pages/PreOpPostOp/types'
import {
  ScheduleFilterProviderContext,
  useScheduleFilterContext,
} from 'src/pages/Schedule/ScheduleFilterContext'
import { ScheduleFilters } from 'src/pages/Schedule/ScheduleFilters'
import { LocationPath } from 'src/router/types'
import { useCurrentMinute } from 'src/utils/useCurrentTime'
import { usePatientReadAccess } from 'src/utils/usePatientReadAccess'

import LoadingOverlay from '../../components/LoadingOverlay'
import { PageContentTemplate } from '../../components/PageContentTemplate'
import { getPatientClassText } from '../Schedule/CaseDetails'
import { CasesMetrics } from '../Schedule/CasesMetrics'
import {
  ScheduleFilterProvider,
  useScheduleFilterProviderUrls,
} from '../Schedule/ScheduleFilterProvider'
import SchedulePageHeader from '../Schedule/SchedulePageHeader'
import {
  AnesthesiaCell,
  EstimatedEndTimeCell,
  PatientCell,
  ProcedureCell,
  RoomCell,
  ScheduledTimeCell,
  SurgeonCell,
} from './ScheduleTableCells'
import { usePrePostOpState } from './usePrePostOpState'

const TEN_MIN_IN_MILLI = 600000

enum SelectedView {
  IN_PROGRESS = 'in_progress',
  NOT_STARTED = 'not_started',
  COMPLETED = 'completed',
}

const PostOpPage = () => {
  const { minTime, maxTime, siteIds, roomIds } = useScheduleFilterContext()

  const currentMinute = useCurrentMinute()

  const { allSearchParams } = useScheduleFilterProviderUrls()

  const { cases, casesLoading } = usePrePostOpState(minTime, maxTime, {
    siteIds,
    roomIds,
  })

  const [selectedView, setSelectedView] = useState<SelectedView>(
    SelectedView.IN_PROGRESS
  )

  const tabOptions = useMemo(
    () => [
      {
        id: SelectedView.IN_PROGRESS,
        display: 'In Progress',
        to: {
          pathname: LocationPath.PostOp,
          search: allSearchParams.toString(),
        },
      },
      {
        id: SelectedView.NOT_STARTED,
        display: 'Not Started',
        to: {
          pathname: LocationPath.PostOp,
          search: allSearchParams.toString(),
        },
      },
      {
        id: SelectedView.COMPLETED,
        display: 'Completed',
        to: {
          pathname: LocationPath.PostOp,
          search: allSearchParams.toString(),
        },
      },
    ],
    [allSearchParams]
  )

  const isTimeWithinTenMinutes = useCallback(
    (time?: DateTime) => {
      if (!time) {
        return false
      }

      return (
        time.toMillis() >= currentMinute.toMillis() - TEN_MIN_IN_MILLI &&
        time.toMillis() <= currentMinute.toMillis()
      )
    },
    [currentMinute]
  )

  const casesForView = useMemo(() => {
    const minTimeDT = DateTime.fromISO(minTime)
    const maxTimeDT = DateTime.fromISO(maxTime)

    if (selectedView === SelectedView.IN_PROGRESS) {
      return cases.filter(
        (c) => c.type === CaseType.LIVE || isTimeWithinTenMinutes(c.endTime)
      )
    }

    if (selectedView === SelectedView.NOT_STARTED) {
      return cases.filter((c) => c.type === CaseType.FORECAST)
    }

    if (selectedView === SelectedView.COMPLETED) {
      return cases.filter(
        (c) =>
          c.type === CaseType.COMPLETE &&
          !isTimeWithinTenMinutes(c.endTime) &&
          c.endTime &&
          c.endTime >= minTimeDT &&
          c.endTime <= maxTimeDT
      )
    }

    return []
  }, [cases, isTimeWithinTenMinutes, maxTime, minTime, selectedView])

  return (
    <PageContentTemplate maxContainerSize="full">
      <SchedulePageHeader selectedSubNavId={'post-op'} />
      <ScheduleFilters
        showToggleScheduledCases={false}
        showSurgeonFilter={false}
      />
      <CasesMetrics cases={cases} isLoading={casesLoading} />
      <div
        css={{
          alignItems: 'center',
          width: '100%',
          [mediaQueries.lg]: {
            width: rem(300),
            padding: `${remSpacing.medium} 0`,
          },
        }}
      >
        <NavigationTabWrapper>
          {tabOptions.map(({ id, to, display }) => (
            <NavigationTab
              isSelected={selectedView === id}
              disabled={false}
              key={id}
            >
              <NavigationTabTitle
                isSelected={selectedView === id}
                onClick={() => setSelectedView(id)}
                to={to}
                disabled={false}
              >
                {display}
              </NavigationTabTitle>
            </NavigationTab>
          ))}
        </NavigationTabWrapper>
      </div>
      <div
        css={{
          overflow: 'auto',
        }}
      >
        {selectedView === SelectedView.IN_PROGRESS ? (
          <PostOpTable
            cases={casesForView}
            isLoading={casesLoading}
            showProgress={true}
          />
        ) : (
          <PostOpTable
            cases={casesForView}
            isLoading={casesLoading}
            showProgress={false}
          />
        )}
      </div>
      <ToggleableCaseKey />
    </PageContentTemplate>
  )
}

type MinWidthTableProps = {
  showPatientData: boolean
}
const MinWidthTable = styled(Table)(
  ({ showPatientData }: MinWidthTableProps) => ({
    width: '100%',
    minWidth: showPatientData ? '1200px' : '1000px',
  })
)

export const PostOp = () => {
  return (
    <ScheduleFilterProvider context={ScheduleFilterProviderContext.POST_OP}>
      <PostOpPage />
    </ScheduleFilterProvider>
  )
}

const PatientClassCell = ({
  apellaCase,
}: {
  apellaCase: PrePostOpApellaCase
}) => <P3>{getPatientClassText(apellaCase.case?.patientClass)}</P3>

const EstimateEndCol = styled.col({
  width: '10%',
  [mediaQueries.xl]: {
    width: '8%',
  },
})

const ScheduledTimeCol = styled.col({
  width: '10%',
  [mediaQueries.xl]: {
    width: '8%',
  },
})

const RoomCol = styled.col({
  width: '17%',
  [mediaQueries.lg]: {
    width: '15%',
  },
})

const ProcedureCol = styled.col({
  width: '21%',
  [mediaQueries.lg]: {
    width: '23%',
  },
  [mediaQueries.xl]: {
    width: '29%',
  },
})

const SurgeonCol = styled.col({
  width: '14%',
})

const PatientCol = styled.col({
  width: '11%',
})

const AnesthesiaCol = styled.col({
  width: '10%',
  [mediaQueries.xl]: {
    width: '8%',
  },
})

const PatientClassCol = styled.col({
  width: '7%',
})

const PostOpTable = ({
  cases,
  isLoading,
  showProgress,
}: {
  cases: PrePostOpApellaCase[]
  isLoading: boolean
  showProgress: boolean
}) => {
  const showPatientData = usePatientReadAccess()
  const [maxHeight, setMaxHeight] = useState(500)
  useEffect(() => {
    setMaxHeight(Math.max(500, window.innerHeight * 0.8))
  }, [])
  return (
    <>
      {isLoading && <LoadingOverlay />}
      <div
        css={{
          maxHeight: maxHeight,
          overflowY: 'auto',
        }}
      >
        <MinWidthTable showPatientData={showPatientData}>
          <colgroup>
            <EstimateEndCol></EstimateEndCol>
            <ScheduledTimeCol></ScheduledTimeCol>
            {showPatientData && <PatientCol></PatientCol>}
            <ProcedureCol></ProcedureCol>
            <AnesthesiaCol></AnesthesiaCol>
            <PatientClassCol></PatientClassCol>
            <SurgeonCol></SurgeonCol>
            <RoomCol></RoomCol>
          </colgroup>
          <THead
            css={{
              position: 'sticky',
              top: 0,
              backgroundColor: 'white',
            }}
          >
            <TR>
              <TH colSpan={2}>Est. end</TH>
              {showPatientData && <TH>Patient</TH>}
              <TH>Procedure</TH>
              <TH>Anesthesia</TH>
              <TH>Patient Class</TH>
              <TH>Surgeon</TH>
              <TH>Room</TH>
            </TR>
          </THead>
          <TBody>
            {!isLoading && cases.length === 0 && (
              <TR>
                <td
                  colSpan={100}
                  css={{ padding: remSpacing.xlarge, textAlign: 'center' }}
                >
                  <H3>No cases found</H3>
                  This may occur if cases have not started yet or are all in
                  progress.
                </td>
              </TR>
            )}
            {sortBy(cases, (ac) => ac.endTime?.valueOf()).map((ac) => (
              <TR key={ac.startTime.toISO() + ac.room.id}>
                <TD>
                  <EstimatedEndTimeCell apellaCase={ac} />
                </TD>
                <TD>
                  <ScheduledTimeCell
                    scheduledTime={ac.case?.scheduledEndTime}
                    predictedTime={ac.endTime}
                  />
                </TD>
                {!!(showPatientData || !showProgress) && (
                  <TD>
                    <PatientCell apellaCase={ac} showStatus={!showProgress} />
                  </TD>
                )}
                <TD>
                  <ProcedureCell apellaCase={ac} />
                </TD>
                <TD>
                  <AnesthesiaCell apellaCase={ac} />
                </TD>
                <TD>
                  <PatientClassCell apellaCase={ac} />
                </TD>
                <TD>
                  <SurgeonCell apellaCase={ac} />
                </TD>
                <TD>
                  <RoomCell apellaCase={ac} showStatus={showProgress} />
                </TD>
              </TR>
            ))}
          </TBody>
        </MinWidthTable>
      </div>
    </>
  )
}
