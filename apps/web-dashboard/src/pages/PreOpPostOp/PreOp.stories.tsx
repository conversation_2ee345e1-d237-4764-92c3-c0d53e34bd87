import { <PERSON><PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import { Auth0Context, initialContext } from '@auth0/auth0-react'
import { Meta, StoryObj } from '@storybook/react'
import { graphql, http, HttpResponse, delay } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'
import { GetSiteOptionsFilterDocument } from 'src/modules/site/__generated__'
import { GetSiteOptionsFilterData } from 'src/modules/site/__mocks__/GetSiteOptionsFilterData'
import { GetCurrentUserDocument } from 'src/modules/user/__generated__'
import MockLDProvider from 'src/test/MockLDProvider'
import { modes } from 'src/test/storybookHelpers'

import { GetRoomDetailsDocument } from '../__generated__'
import { GetCurrentUserData, UserType } from '../test_mocks/GetCurrentUserData'
import { GetPrePostOpDataDocument } from './__generated__'
import {
  mockPreOpData,
  mockGarage0RoomDetailsFragment,
  mockGarage1RoomDetailsFragment,
  mockSiteFragment,
} from './GetPrePostOpData'
import { PreOp } from './PreOp'

const ldParams = {
  dashboardScheduleEnabled: true,
}

const mockedClient = new ApolloClient({
  uri: 'https://your-graphql-endpoint',
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
    query: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
  },
})

const meta: Meta<typeof PreOp> = {
  title: 'Pages/PreOp',
  component: PreOp,
  decorators: [
    (Story, { parameters }) => {
      const flags = parameters.flags || ldParams
      return (
        <TimezoneProvider>
          <MockLDProvider flags={flags}>
            <Auth0Context.Provider
              value={{ ...initialContext, isLoading: false }}
            >
              <ApolloProvider client={mockedClient}>
                <Story />
              </ApolloProvider>
            </Auth0Context.Provider>
          </MockLDProvider>
        </TimezoneProvider>
      )
    },
    withRouter,
  ],
  parameters: {
    chromatic: {
      modes: {
        mobile: modes.mobile,
        xLarge: modes.xLarge,
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof PreOp>
const routing = {
  path: '/schedule/pre-op',
  handle: 'PreOp',
}

const msw = (
  wait?: number,
  { getPreOpPostOpData = mockPreOpData, userType = UserType.ALL_ACCESS } = {}
) => ({
  handlers: [
    http.get('https://events.launchdarkly.com/events/diagnostic/', () => {
      return HttpResponse.json({})
    }),
    http.get('https://app.launchdarkly.com/sdk/goals/', () => {
      return HttpResponse.json({})
    }),
    graphql.query(GetCurrentUserDocument, () => {
      const userData = GetCurrentUserData(userType)
      return HttpResponse.json(userData)
    }),

    graphql.query(GetPrePostOpDataDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(getPreOpPostOpData)
    }),

    graphql.query(GetSiteOptionsFilterDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
    graphql.query(GetRoomDetailsDocument, async (request) => {
      const { roomId } = request.variables
      await delay(wait ?? 300)
      if (roomId === 'garage_0') {
        return HttpResponse.json(mockGarage0RoomDetailsFragment)
      } else {
        return HttpResponse.json(mockGarage1RoomDetailsFragment)
      }
    }),
    graphql.query('GetSiteDetails', async () => {
      await delay(wait ?? 300)
      return HttpResponse.json({
        data: mockSiteFragment,
      })
    }),
  ],
})

export const PreOpDashboard: Story = {
  parameters: {
    date: new Date('2024-12-16T12:05:58.518-07:00'),
    msw: msw(0, { userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule/pre-op',
      },
      routing,
    },
  },
}

export const PreOpDashboardWithPatientData: Story = {
  parameters: {
    date: new Date('2024-12-16T12:05:58.518-07:00'),
    msw: msw(0, { userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule/pre-op',
      },
      routing,
    },
    flags: {
      ...ldParams,
      showPatientData: true,
    },
  },
}
