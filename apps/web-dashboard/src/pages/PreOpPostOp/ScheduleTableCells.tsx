import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import uniq from 'lodash/uniq'
import { DateTime } from 'luxon'
import { rem } from 'polished'

import {
  ApellaDateTimeFormats,
  formatDuration,
  H3,
  P3,
  TD,
  Tooltip,
} from '@apella/component-library'
import { CaseType } from 'src/__generated__/globalTypes'
import { PrePostOpApellaCase } from 'src/pages/PreOpPostOp/types'
import { usePatientReadAccess } from 'src/utils/usePatientReadAccess'

import { useTimezone } from '../../Contexts'
import { useRoomName, useRoomSiteName } from '../../utils/hooks'
import { getPrimarySurgeonText } from '../../utils/roles'
import { useCurrentMinute } from '../../utils/useCurrentTime'
import { AddOnChecker } from '../AddOnChecker'
import { CaseStatusPill } from '../Schedule/CaseDetails'
import { CaseTooltipBody } from '../Schedule/TimelinePill/TooltipBody'
import { CaseTooltipHoverInterConfiguration } from '../types'

export const TDTopAligned = styled(TD)`
  vertical-align: top;
`

export const ScheduledTimeCell = ({
  predictedTime,
  scheduledTime,
  gracePeriodMins = 5,
}: {
  predictedTime?: DateTime
  scheduledTime?: DateTime
  gracePeriodMins?: number
}) => {
  const theme = useTheme()
  const offset =
    predictedTime && scheduledTime
      ? predictedTime.startOf('minute').diff(scheduledTime)
      : undefined
  const isDelayed = offset && offset.as('minutes') > gracePeriodMins

  return (
    !!scheduledTime && (
      <div css={{ whiteSpace: 'nowrap' }}>
        <P3 css={{ fontWeight: 'bold' }}>
          SCH {scheduledTime.toLocaleString(ApellaDateTimeFormats.TIME)}
        </P3>
        {isDelayed && (
          <P3 css={{ color: isDelayed ? theme.palette.red[50] : undefined }}>
            {formatDuration(offset)} delayed
          </P3>
        )}
      </div>
    )
  )
}

export const EstimatedStartTimeCell = ({
  apellaCase,
}: {
  apellaCase: PrePostOpApellaCase
}) => {
  const { timezone } = useTimezone()
  const currentMinute = useCurrentMinute()
  const predictedStartTime = apellaCase.startTime
    .setZone(timezone)
    .startOf('minute')

  const displayedStart =
    currentMinute >= predictedStartTime
      ? 'Starting soon'
      : predictedStartTime.toLocaleString(ApellaDateTimeFormats.TIME)

  return <H3>{displayedStart}</H3>
}

export const EstimatedEndTimeCell = ({
  apellaCase,
}: {
  apellaCase: PrePostOpApellaCase
}) => {
  const { timezone } = useTimezone()
  const currentMinute = useCurrentMinute()
  const predictedEndTime = apellaCase.endTime?.setZone(timezone)

  const displayedEnd =
    apellaCase.type != CaseType.LIVE &&
    apellaCase.type != CaseType.FORECAST &&
    predictedEndTime &&
    predictedEndTime >= currentMinute.minus({ minutes: 10 })
      ? 'Ended recently'
      : predictedEndTime &&
          currentMinute >= predictedEndTime &&
          apellaCase.type !== CaseType.COMPLETE
        ? 'Ending soon'
        : predictedEndTime?.toLocaleString(ApellaDateTimeFormats.TIME)

  return <H3>{displayedEnd}</H3>
}

export const SurgeonCell = ({
  apellaCase,
}: {
  apellaCase: PrePostOpApellaCase
}) => {
  const caseObj = apellaCase.case

  if (!caseObj) {
    return <></>
  }

  return <P3>{getPrimarySurgeonText(caseObj)}</P3>
}

export const ProcedureCell = ({
  apellaCase,
}: {
  apellaCase: PrePostOpApellaCase
}) => {
  const caseObj = apellaCase.case

  if (!caseObj) {
    return <></>
  }
  const procedureNames = caseObj.procedures.map((p) => p.name)
  return (
    <Tooltip
      body={<CaseTooltipBody apellaCase={apellaCase} />}
      strategy={'fixed'}
      maxWidth={undefined}
      middleware={[Tooltip.middleware.autoPlacement()]}
      hoverIntent={CaseTooltipHoverInterConfiguration}
    >
      <P3>
        {caseObj.procedures.length
          ? AddOnChecker(procedureNames, caseObj.isAddOn ?? false, true)
          : 'Scheduled Case'}
      </P3>
    </Tooltip>
  )
}

export const AnesthesiaCell = ({
  apellaCase,
}: {
  apellaCase: PrePostOpApellaCase
}) => {
  const caseObj = apellaCase.case

  if (!caseObj) {
    return <></>
  }

  const anesthesiaDisplay = uniq(
    caseObj.procedures.map((p) => p.anesthesiaType).filter(Boolean)
  ).join('; ')

  return <P3>{anesthesiaDisplay}</P3>
}

export const RoomCell = ({
  apellaCase,
  showStatus = true,
}: {
  apellaCase: PrePostOpApellaCase
  showStatus?: boolean
}) => {
  const roomName = useRoomName(apellaCase.room.id)
  const siteName = useRoomSiteName(apellaCase.room.id)

  const roomStatus = apellaCase.room.status.name
  const roomSince = apellaCase.room.status.since
  const caseStatus = apellaCase.room.status.inProgressApellaCase?.status?.name
  const caseSince = apellaCase.room.status.inProgressApellaCase?.status.since

  const since = caseSince ?? roomSince
  const status = caseStatus ?? roomStatus

  return (
    <>
      <P3>
        {roomName}, {siteName}
      </P3>
      {showStatus && (
        <div css={{ height: rem('20px') }}>
          <div css={{ display: 'flex' }}>
            <CaseStatusPill status={status} since={since} />
          </div>
        </div>
      )}
    </>
  )
}

export const PatientCell = ({
  apellaCase,
  showStatus,
}: {
  apellaCase: PrePostOpApellaCase
  showStatus?: boolean
}) => {
  const showPatientData = usePatientReadAccess()

  if (!showPatientData && !showStatus) {
    return <></>
  }

  return (
    <>
      {showPatientData && !!apellaCase.case?.patient && (
        <P3 css={{ whiteSpace: 'nowrap' }}>
          {apellaCase.case.patient.abbreviatedName}{' '}
          {apellaCase.case.patient.age};{' '}
          {apellaCase.case.patient.administrativeSex.toUpperCase()}
        </P3>
      )}
      {showStatus && (
        <div css={{ display: 'flex' }}>
          <CaseStatusPill status={apellaCase.status.name} />
        </div>
      )}
    </>
  )
}
