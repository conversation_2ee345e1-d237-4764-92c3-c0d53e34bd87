import { DateTime } from 'luxon'

import { CaseType, RoomStatusName } from 'src/__generated__/globalTypes'
import { Case, CaseStatus } from 'src/pages/types'

interface PrePostOpRoomStatus {
  inProgressApellaCase?: {
    status: CaseStatus
  }
  name: RoomStatusName
  since?: DateTime
}

interface PrePostOpApellaRoom {
  id: string
  name: string
  status: PrePostOpRoomStatus
}

export interface PrePostOpApellaCase {
  case?: Case
  endTime?: DateTime
  id: string
  room: PrePostOpApellaRoom
  startTime: DateTime
  status: CaseStatus
  type: CaseType
}
