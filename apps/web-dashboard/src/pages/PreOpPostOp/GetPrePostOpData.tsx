import {
  CaseStatusName,
  CaseType,
  RoomStatusName,
} from 'src/__generated__/globalTypes'

import { GetRoomDetails, GetSiteDetails } from '../__generated__'
import { GetPrePostOpData } from './__generated__'

interface MockedData {
  data: GetPrePostOpData
}

interface MockedRoomData {
  data: GetRoomDetails
}

export const mockPreOpData: MockedData = {
  data: {
    sites: {
      edges: [
        {
          node: {
            id: 'lab_1',
            name: 'Apella Lab',
            rooms: {
              edges: [
                {
                  node: {
                    id: 'AT-DEV-OR1',
                    name: 'Test',
                    apellaCases: {
                      edges: [],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_0',
                    name: 'Garage 0-test',
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:58370290-b7a8-4e79-9e52-4bde74da4001',
                            startTime: '2024-12-16T16:06:56.364946+00:00',
                            endTime: '2024-12-16T17:06:41.807194+00:00',
                            type: CaseType.COMPLETE,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-16T18:52:53.673739+00:00',
                                inProgressApellaCase: {
                                  id: 'case:53efe60e-16aa-45d4-8ded-5576a423a244',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-16T18:52:53.673739+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-12-16T17:06:41.807194+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '58370290-b7a8-4e79-9e52-4bde74da4001',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T16:07:58.789366+00:00',
                              scheduledEndTime:
                                '2024-12-16T17:13:58.789366+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '0448e968-c4f7-4982-9466-6fedceb1c7c3',
                                    firstName: 'John',
                                    lastName: 'Dorian',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'I & D Abdomen',
                                    id: '4513ffd8-6fdc-4408-9bc1-a3a52184bd84',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: null,
                              patientClass: null,
                              isFirstCase: null,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_f74d75b4-37cc-4579-ba49-231b6755f348',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:7278d5d5-4d88-4007-809b-a20829cbd3aa',
                            startTime: '2024-12-16T17:33:43.782238+00:00',
                            endTime: '2024-12-16T18:33:36.639401+00:00',
                            type: CaseType.COMPLETE,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-16T18:52:53.673739+00:00',
                                inProgressApellaCase: {
                                  id: 'case:53efe60e-16aa-45d4-8ded-5576a423a244',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-16T18:52:53.673739+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-12-16T18:33:36.639401+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '7278d5d5-4d88-4007-809b-a20829cbd3aa',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T17:21:31.709665+00:00',
                              scheduledEndTime:
                                '2024-12-16T18:38:31.709665+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'bca3eebb-aa9d-4185-9007-0555b63db319',
                                    firstName: 'Henry',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Colonoscopy',
                                    id: '12c6a767-6706-4abf-a244-441d3f475c31',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_77836353-3773-46e7-96ec-b596aa3258e1',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:53efe60e-16aa-45d4-8ded-5576a423a244',
                            startTime: '2024-12-16T18:52:53.673739+00:00',
                            endTime: '2024-12-16T19:20:04.473739+00:00',
                            type: CaseType.LIVE,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-16T18:52:53.673739+00:00',
                                inProgressApellaCase: {
                                  id: 'case:53efe60e-16aa-45d4-8ded-5576a423a244',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-16T18:52:53.673739+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.PREP,
                              since: '2024-12-16T18:52:53.673739+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '53efe60e-16aa-45d4-8ded-5576a423a244',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T18:50:08.798660+00:00',
                              scheduledEndTime:
                                '2024-12-16T20:06:08.798660+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '63ece80a-d0c6-4b1e-bc96-dc81c16c7100',
                                    firstName: 'Henry',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '50cf4be6-c9f3-4f02-8452-6bd36fd8efab',
                                    firstName: 'Miranda',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_2e78fa31-acfa-447f-9df3-13499e48cf0f',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:194ecb8c-915d-4d73-9994-66db253ee295',
                            startTime: '2024-12-16T20:24:42.378049+00:00',
                            endTime: '2024-12-16T20:51:53.178049+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-16T18:52:53.673739+00:00',
                                inProgressApellaCase: {
                                  id: 'case:53efe60e-16aa-45d4-8ded-5576a423a244',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-16T18:52:53.673739+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-16T10:05:03.620430+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '194ecb8c-915d-4d73-9994-66db253ee295',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T20:21:33.978049+00:00',
                              scheduledEndTime:
                                '2024-12-16T21:44:33.978049+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '6bd4f8ed-ffa6-4117-b625-9041bac6d959',
                                    firstName: 'Henry',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_731b6699-610f-4e94-a759-5827c2e99223',
                              patient: {
                                id: 'p4',
                                personalInfo: {
                                  firstNameAbbreviated: 'S',
                                  lastNameAbbreviated: 'Mog',
                                  age: 30,
                                  administrativeSex: {
                                    text: 'A',
                                    __typename: 'AdministrativeSex',
                                  },
                                  __typename: 'PersonalInfo',
                                },
                                __typename: 'Patient',
                              },
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:3f9104ee-2644-4930-bd70-5920e55e4b45',
                            startTime: '2024-12-16T22:01:26.452972+00:00',
                            endTime: '2024-12-16T22:28:37.252972+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-16T18:52:53.673739+00:00',
                                inProgressApellaCase: {
                                  id: 'case:53efe60e-16aa-45d4-8ded-5576a423a244',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-16T18:52:53.673739+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-16T10:05:13.890250+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '3f9104ee-2644-4930-bd70-5920e55e4b45',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T21:58:18.052972+00:00',
                              scheduledEndTime:
                                '2024-12-16T23:36:18.052972+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'a7d26be7-92ad-41bc-927e-f95fd9a3af96',
                                    firstName: 'Gregory',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: true,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_fae1401e-4b9b-4036-b343-0948f19800a5',
                              patient: {
                                id: 'p4',
                                personalInfo: {
                                  firstNameAbbreviated: 'B',
                                  lastNameAbbreviated: 'Mer',
                                  age: 26,
                                  administrativeSex: {
                                    text: 'A',
                                    __typename: 'AdministrativeSex',
                                  },
                                  __typename: 'PersonalInfo',
                                },
                                __typename: 'Patient',
                              },
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:eae46093-f7d2-4255-be91-d5add2cf2827',
                            startTime: '2024-12-16T23:54:05.876543+00:00',
                            endTime: '2024-12-17T00:21:16.676543+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-16T18:52:53.673739+00:00',
                                inProgressApellaCase: {
                                  id: 'case:53efe60e-16aa-45d4-8ded-5576a423a244',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-16T18:52:53.673739+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-16T10:05:05.085431+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: 'eae46093-f7d2-4255-be91-d5add2cf2827',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T23:50:57.476543+00:00',
                              scheduledEndTime:
                                '2024-12-17T01:08:57.476543+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '6d844577-72c8-4e43-8763-dd95d60526df',
                                    firstName: 'Gregory',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Colonoscopy',
                                    id: '12c6a767-6706-4abf-a244-441d3f475c31',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_7a860bd6-a4a1-4fc1-a10d-6e3d514d9121',
                              patient: {
                                id: 'p4',
                                personalInfo: {
                                  firstNameAbbreviated: 'W',
                                  lastNameAbbreviated: 'Ste',
                                  age: 14,
                                  administrativeSex: {
                                    text: 'A',
                                    __typename: 'AdministrativeSex',
                                  },
                                  __typename: 'PersonalInfo',
                                },
                                __typename: 'Patient',
                              },
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_1',
                    name: 'Garage 1',
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:cef82e83-8c7a-4a20-9a77-c4623662f425',
                            startTime: '2024-12-16T16:10:32.991840+00:00',
                            endTime: '2024-12-16T17:29:27.541900+00:00',
                            type: CaseType.COMPLETE,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.TURNOVER,
                                since: '2024-12-16T18:55:35.599701+00:00',
                                inProgressApellaCase: null,
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-12-16T17:29:27.541900+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: 'cef82e83-8c7a-4a20-9a77-c4623662f425',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T16:10:43.206733+00:00',
                              scheduledEndTime:
                                '2024-12-16T17:42:43.206733+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '6bd4f8ed-ffa6-4117-b625-9041bac6d959',
                                    firstName: 'Henry',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Angiogram',
                                    id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: true,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_c72d1b07-8b31-4217-8eb3-32d36af06ccf',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:456c9abf-39b6-40bc-b958-e3768336e8f7',
                            startTime: '2024-12-16T18:08:14.082367+00:00',
                            endTime: '2024-12-16T18:55:35.599701+00:00',
                            type: CaseType.COMPLETE,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.TURNOVER,
                                since: '2024-12-16T18:55:35.599701+00:00',
                                inProgressApellaCase: null,
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-12-16T18:55:35.599701+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '456c9abf-39b6-40bc-b958-e3768336e8f7',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T18:00:40.624447+00:00',
                              scheduledEndTime:
                                '2024-12-16T19:04:40.624447+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '3008c147-f8b4-4b07-9e58-49b4fb333c77',
                                    firstName: 'Miranda',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Angiogram',
                                    id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_d62b1a63-9855-43d0-a71c-69932c35247e',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:59254f0c-f501-4341-907b-904c1fef0b36',
                            startTime: '2024-12-16T19:14:05.074571+00:00',
                            endTime: '2024-12-16T19:41:15.874571+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.TURNOVER,
                                since: '2024-12-16T18:55:35.599701+00:00',
                                inProgressApellaCase: null,
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-16T10:05:09.527761+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '59254f0c-f501-4341-907b-904c1fef0b36',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T19:10:56.674571+00:00',
                              scheduledEndTime:
                                '2024-12-16T20:29:56.674571+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'a7d26be7-92ad-41bc-927e-f95fd9a3af96',
                                    firstName: 'Gregory',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: true,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_72c1cca5-d44e-4dd6-9f57-fa4e5857902a',
                              patient: {
                                id: 'p4',
                                personalInfo: {
                                  firstNameAbbreviated: 'F',
                                  lastNameAbbreviated: 'Tho',
                                  age: 24,
                                  administrativeSex: {
                                    text: 'B',
                                    __typename: 'AdministrativeSex',
                                  },
                                  __typename: 'PersonalInfo',
                                },
                                __typename: 'Patient',
                              },
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:2d4fd7f5-b162-4d2e-ba8e-8553a0466cc5',
                            startTime: '2024-12-16T20:24:42.378049+00:00',
                            endTime: '2024-12-16T20:51:53.178049+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-16T18:55:35.599701+00:00',
                                inProgressApellaCase: {
                                  id: 'case:53efe60e-16aa-45d4-8ded-5576a423a274',
                                  status: {
                                    name: CaseStatusName.SURGERY,
                                    since: '2024-12-16T18:52:53.673739+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-16T10:05:03.620430+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '2d4fd7f5-b162-4d2e-ba8e-8553a0466cc5',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T20:46:07.895763+00:00',
                              scheduledEndTime:
                                '2024-12-16T22:25:07.895763+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '6d844577-72c8-4e43-8763-dd95d60526df',
                                    firstName: 'Gregory',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Colonoscopy',
                                    id: '12c6a767-6706-4abf-a244-441d3f475c31',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_066ce4ab-0be4-4d8a-8af2-a8dfc96104ef',
                              patient: {
                                id: 'p4',
                                personalInfo: {
                                  firstNameAbbreviated: 'J',
                                  lastNameAbbreviated: 'Oki',
                                  age: 34,
                                  administrativeSex: {
                                    text: 'C',
                                    __typename: 'AdministrativeSex',
                                  },
                                  __typename: 'PersonalInfo',
                                },
                                __typename: 'Patient',
                              },
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:2fa0ac94-bdb4-42fc-be06-5c659d0caad9',
                            startTime: '2024-12-16T22:34:54.070099+00:00',
                            endTime: '2024-12-16T23:02:04.870099+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.TURNOVER,
                                since: '2024-12-16T18:55:35.599701+00:00',
                                inProgressApellaCase: null,
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-16T10:05:05.771174+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '2fa0ac94-bdb4-42fc-be06-5c659d0caad9',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-16T22:31:45.670099+00:00',
                              scheduledEndTime:
                                '2024-12-17T00:03:45.670099+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'bca3eebb-aa9d-4185-9007-0555b63db319',
                                    firstName: 'Henry',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Colonoscopy',
                                    id: '12c6a767-6706-4abf-a244-441d3f475c31',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'I & D Abdomen',
                                    id: '4513ffd8-6fdc-4408-9bc1-a3a52184bd84',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_fc07e5ae-f577-440d-b930-c0b126377948',
                              patient: {
                                id: 'p4',
                                personalInfo: {
                                  firstNameAbbreviated: 'B',
                                  lastNameAbbreviated: 'Sai',
                                  age: 27,
                                  administrativeSex: {
                                    text: 'B',
                                    __typename: 'AdministrativeSex',
                                  },
                                  __typename: 'PersonalInfo',
                                },
                                __typename: 'Patient',
                              },
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_2',
                    name: 'Garage 2',
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'phase:9aae7df9-3cce-4ac8-9e0b-5ceb71e1fef9',
                            startTime: '2024-12-16T16:21:25.286811+00:00',
                            endTime: '2024-12-16T17:22:30.029937+00:00',
                            type: CaseType.COMPLETE,
                            room: {
                              id: 'garage_2',
                              name: 'Garage 2',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-16T17:55:54.580744+00:00',
                                inProgressApellaCase: {
                                  id: 'phase:9e265420-36f7-48df-bf12-010e065b9557',
                                  status: {
                                    name: CaseStatusName.WRAP_UP,
                                    since: '2024-12-16T18:56:15.137326+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-12-16T17:22:30.029937+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:9e265420-36f7-48df-bf12-010e065b9557',
                            startTime: '2024-12-16T17:55:54.580744+00:00',
                            endTime: null,
                            type: CaseType.LIVE,
                            room: {
                              id: 'garage_2',
                              name: 'Garage 2',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-16T17:55:54.580744+00:00',
                                inProgressApellaCase: {
                                  id: 'phase:9e265420-36f7-48df-bf12-010e065b9557',
                                  status: {
                                    name: CaseStatusName.WRAP_UP,
                                    since: '2024-12-16T18:56:15.137326+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.WRAP_UP,
                              since: '2024-12-16T18:56:15.137326+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
              ],
              __typename: 'RoomConnection',
            },
            __typename: 'Site',
          },
          __typename: 'SiteEdge',
        },
      ],
      __typename: 'SiteConnection',
    },
  },
}
export const PostOpMockData: MockedData = {
  data: {
    sites: {
      edges: [
        {
          node: {
            id: 'lab_1',
            name: 'Apella Lab',
            rooms: {
              edges: [
                {
                  node: {
                    id: 'AT-DEV-OR1',
                    name: 'Test',
                    apellaCases: {
                      edges: [],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_0',
                    name: 'Garage 0-test',
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:05007d1c-ed27-4a2e-a305-4ccfc5af69f4',
                            startTime: '2024-12-19T16:12:22.868351+00:00',
                            endTime: '2024-12-19T17:19:49.122124+00:00',
                            type: CaseType.COMPLETE,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:48:55.920806+00:00',
                                inProgressApellaCase: {
                                  id: 'case:642a307b-3c32-40c1-bb75-acbfaf733123',
                                  status: {
                                    name: CaseStatusName.SURGERY,
                                    since: '2024-12-19T18:08:21.276175+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-12-19T17:19:49.122124+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '05007d1c-ed27-4a2e-a305-4ccfc5af69f4',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T16:12:44.150047+00:00',
                              scheduledEndTime:
                                '2024-12-19T17:29:44.150047+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'f8c835e2-72c0-4f35-be14-aa1962371787',
                                    firstName: 'John',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: true,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_956aa4f6-a08f-4719-9e94-1ad21a6c036b',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:642a307b-3c32-40c1-bb75-acbfaf733123',
                            startTime: '2024-12-19T17:48:55.920806+00:00',
                            endTime: '2024-12-19T18:16:59.922124+00:00',
                            type: CaseType.LIVE,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:48:55.920806+00:00',
                                inProgressApellaCase: {
                                  id: 'case:642a307b-3c32-40c1-bb75-acbfaf733123',
                                  status: {
                                    name: CaseStatusName.SURGERY,
                                    since: '2024-12-19T18:08:21.276175+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SURGERY,
                              since: '2024-12-19T18:08:21.276175+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '642a307b-3c32-40c1-bb75-acbfaf733123',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T17:48:15.833200+00:00',
                              scheduledEndTime:
                                '2024-12-19T19:15:15.833200+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '63ece80a-d0c6-4b1e-bc96-dc81c16c7100',
                                    firstName: 'Henry',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_989b0f50-083b-4a5c-9fc9-0235b2cd3d8d',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:833bd7cc-59db-4c97-ba5b-941bd8336823',
                            startTime: '2024-12-19T19:26:55.492404+00:00',
                            endTime: '2024-12-19T19:54:06.292404+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:48:55.920806+00:00',
                                inProgressApellaCase: {
                                  id: 'case:642a307b-3c32-40c1-bb75-acbfaf733123',
                                  status: {
                                    name: CaseStatusName.SURGERY,
                                    since: '2024-12-19T18:08:21.276175+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-19T11:55:58.645845+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '833bd7cc-59db-4c97-ba5b-941bd8336823',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T19:23:47.092404+00:00',
                              scheduledEndTime:
                                '2024-12-19T20:39:47.092404+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '6bd4f8ed-ffa6-4117-b625-9041bac6d959',
                                    firstName: 'Henry',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Angiogram',
                                    id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_4c40c1ab-10fb-4236-ae9d-3903b5eab426',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:c0461a88-4e35-4a70-acc6-c70fbe70c0a5',
                            startTime: '2024-12-19T20:50:20.936232+00:00',
                            endTime: '2024-12-19T21:17:31.736232+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:48:55.920806+00:00',
                                inProgressApellaCase: {
                                  id: 'case:642a307b-3c32-40c1-bb75-acbfaf733123',
                                  status: {
                                    name: CaseStatusName.SURGERY,
                                    since: '2024-12-19T18:08:21.276175+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-19T11:56:02.480888+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: 'c0461a88-4e35-4a70-acc6-c70fbe70c0a5',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T20:47:12.536232+00:00',
                              scheduledEndTime:
                                '2024-12-19T22:13:12.536232+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '8e971e0d-9be7-4129-82b7-3c9db7f6cde8',
                                    firstName: 'Gregory',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Colonoscopy',
                                    id: '12c6a767-6706-4abf-a244-441d3f475c31',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Angiogram',
                                    id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_dcb98510-7ca4-4268-9f46-1701176f4e4b',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:f5fd773f-d24b-42fb-ae2c-82c58897aa64',
                            startTime: '2024-12-19T22:28:26.090860+00:00',
                            endTime: '2024-12-19T22:55:36.890860+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_0',
                              name: 'Garage 0-test',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:48:55.920806+00:00',
                                inProgressApellaCase: {
                                  id: 'case:642a307b-3c32-40c1-bb75-acbfaf733123',
                                  status: {
                                    name: CaseStatusName.SURGERY,
                                    since: '2024-12-19T18:08:21.276175+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-19T11:56:12.528595+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: 'f5fd773f-d24b-42fb-ae2c-82c58897aa64',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T22:25:17.690860+00:00',
                              scheduledEndTime:
                                '2024-12-20T00:03:17.690860+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'e2da4139-4395-45d1-9fef-8c81b1b0d350',
                                    firstName: 'John',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '50cf4be6-c9f3-4f02-8452-6bd36fd8efab',
                                    firstName: 'Miranda',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'I & D Abdomen',
                                    id: '4513ffd8-6fdc-4408-9bc1-a3a52184bd84',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: false,
                              patientClass: null,
                              isFirstCase: false,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_15d152b0-834b-44e8-b4e3-d0e6e0767cac',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_1',
                    name: 'Garage 1',
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:523db646-63ce-4157-82b7-ad288fc21c7d',
                            startTime: '2024-12-19T16:20:02.411955+00:00',
                            endTime: '2024-12-19T17:25:21.011287+00:00',
                            type: CaseType.COMPLETE,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:58:55.014545+00:00',
                                inProgressApellaCase: {
                                  id: 'case:d8c34a55-d2c4-4043-8c65-919978ca6261',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-19T18:06:58.631664+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-12-19T17:25:21.011287+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '523db646-63ce-4157-82b7-ad288fc21c7d',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T16:16:08.815628+00:00',
                              scheduledEndTime:
                                '2024-12-19T17:38:08.815628+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '1196f869-eea6-41fe-8ff8-fc3695b1d38e',
                                    firstName: 'John',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: null,
                              patientClass: null,
                              isFirstCase: null,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_8d5f0c07-eeb9-40d1-9147-dd79a0a48eda',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:d8c34a55-d2c4-4043-8c65-919978ca6261',
                            startTime: '2024-12-19T17:58:55.014545+00:00',
                            endTime: '2024-12-19T18:26:05.814545+00:00',
                            type: CaseType.LIVE,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:58:55.014545+00:00',
                                inProgressApellaCase: {
                                  id: 'case:d8c34a55-d2c4-4043-8c65-919978ca6261',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-19T18:06:58.631664+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.PREP,
                              since: '2024-12-19T18:06:58.631664+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: 'd8c34a55-d2c4-4043-8c65-919978ca6261',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T17:51:10.198195+00:00',
                              scheduledEndTime:
                                '2024-12-19T19:05:10.198195+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '1196f869-eea6-41fe-8ff8-fc3695b1d38e',
                                    firstName: 'John',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: null,
                              patientClass: null,
                              isFirstCase: null,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_df15e9b6-d03e-4dda-9db1-9a52fb32f574',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:18d372e6-e968-44a6-8768-05ea48b4e918',
                            startTime: '2024-12-19T19:23:04.002499+00:00',
                            endTime: '2024-12-19T19:50:14.802499+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:58:55.014545+00:00',
                                inProgressApellaCase: {
                                  id: 'case:d8c34a55-d2c4-4043-8c65-919978ca6261',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-19T18:06:58.631664+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-19T12:46:00.513866+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '18d372e6-e968-44a6-8768-05ea48b4e918',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T19:19:55.602499+00:00',
                              scheduledEndTime:
                                '2024-12-19T20:47:55.602499+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '8e971e0d-9be7-4129-82b7-3c9db7f6cde8',
                                    firstName: 'Gregory',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: null,
                              patientClass: null,
                              isFirstCase: null,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_9837a791-07d3-4cb1-bb2b-6a79946b5aa5',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:d43e2905-987e-4fc1-9a11-de228fe53c67',
                            startTime: '2024-12-19T21:05:32.840539+00:00',
                            endTime: '2024-12-19T21:32:43.640539+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:58:55.014545+00:00',
                                inProgressApellaCase: {
                                  id: 'case:d8c34a55-d2c4-4043-8c65-919978ca6261',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-19T18:06:58.631664+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-19T12:46:00.584316+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: 'd43e2905-987e-4fc1-9a11-de228fe53c67',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T21:02:24.440539+00:00',
                              scheduledEndTime:
                                '2024-12-19T22:23:24.440539+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'd163eed7-2be0-4958-86be-2f5d50814dfc',
                                    firstName: 'Henry',
                                    lastName: 'Dorian',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '1196f869-eea6-41fe-8ff8-fc3695b1d38e',
                                    firstName: 'John',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'I & D Abdomen',
                                    id: '4513ffd8-6fdc-4408-9bc1-a3a52184bd84',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: null,
                              patientClass: null,
                              isFirstCase: null,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_659dbd26-4fb3-484d-a676-790ea259b1a4',
                              patient: {
                                id: 'p1',
                                personalInfo: {
                                  firstNameAbbreviated: 'D',
                                  lastNameAbbreviated: 'Smi',
                                  age: 5,
                                  administrativeSex: {
                                    text: 'M',
                                    __typename: 'AdministrativeSex',
                                  },
                                  __typename: 'PersonalInfo',
                                },
                                __typename: 'Patient',
                              },
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:394ec4e4-a584-4c3e-8707-4f62e06874bf',
                            startTime: '2024-12-19T22:44:45.347167+00:00',
                            endTime: '2024-12-19T23:11:56.147167+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:58:55.014545+00:00',
                                inProgressApellaCase: {
                                  id: 'case:d8c34a55-d2c4-4043-8c65-919978ca6261',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-19T18:06:58.631664+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-19T12:46:00.346939+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '394ec4e4-a584-4c3e-8707-4f62e06874bf',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-19T22:41:36.947167+00:00',
                              scheduledEndTime:
                                '2024-12-19T23:55:36.947167+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '6d844577-72c8-4e43-8763-dd95d60526df',
                                    firstName: 'Gregory',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'Angiogram',
                                    id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: null,
                              patientClass: null,
                              isFirstCase: null,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_0778bb09-373c-4260-833f-6932c6e01970',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:3f586a25-34db-434c-8c46-20141189f035',
                            startTime: '2024-12-20T00:13:05.202514+00:00',
                            endTime: '2024-12-20T00:40:16.002514+00:00',
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_1',
                              name: 'Garage 1',
                              status: {
                                name: RoomStatusName.IN_CASE,
                                since: '2024-12-19T17:58:55.014545+00:00',
                                inProgressApellaCase: {
                                  id: 'case:d8c34a55-d2c4-4043-8c65-919978ca6261',
                                  status: {
                                    name: CaseStatusName.PREP,
                                    since: '2024-12-19T18:06:58.631664+00:00',
                                    __typename: 'ApellaCaseStatus',
                                  },
                                  __typename: 'ApellaCase',
                                },
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-12-19T12:46:00.581328+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: {
                              id: '3f586a25-34db-434c-8c46-20141189f035',
                              site: {
                                id: 'lab_1',
                                __typename: 'Site',
                              },
                              scheduledStartTime:
                                '2024-12-20T00:09:56.802514+00:00',
                              scheduledEndTime:
                                '2024-12-20T01:41:56.802514+00:00',
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '3008c147-f8b4-4b07-9e58-49b4fb333c77',
                                    firstName: 'Miranda',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'bca3eebb-aa9d-4185-9007-0555b63db319',
                                    firstName: 'Henry',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  anesthesia: null,
                                  procedure: {
                                    name: 'I & D Abdomen',
                                    id: '4513ffd8-6fdc-4408-9bc1-a3a52184bd84',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              isAddOn: null,
                              isInFlipRoom: null,
                              patientClass: null,
                              isFirstCase: null,
                              precedingCase: null,
                              caseClassificationType: null,
                              externalCaseId:
                                'load-generator_d0ef89e2-f61c-41d5-aa12-11b66bf39f7d',
                              patient: null,
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_2',
                    name: 'Garage 2',
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'phase:3bee9312-58fd-4891-b80d-1c6c138b1607',
                            startTime: '2024-12-19T16:15:26.281678+00:00',
                            endTime: '2024-12-19T17:34:43.564543+00:00',
                            type: CaseType.COMPLETE,
                            room: {
                              id: 'garage_2',
                              name: 'Garage 2',
                              status: {
                                name: RoomStatusName.TURNOVER,
                                since: '2024-12-19T17:34:43.564543+00:00',
                                inProgressApellaCase: null,
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-12-19T17:34:43.564543+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:4b608d69-1de4-4521-a06a-3c43e70eb412',
                            startTime: '2024-12-19T18:13:29.211704+00:00',
                            endTime: null,
                            type: CaseType.FORECAST,
                            room: {
                              id: 'garage_2',
                              name: 'Garage 2',
                              status: {
                                name: RoomStatusName.TURNOVER,
                                since: '2024-12-19T17:34:43.564543+00:00',
                                inProgressApellaCase: null,
                                __typename: 'RoomStatus',
                              },
                              __typename: 'Room',
                            },
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: null,
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
              ],
              __typename: 'RoomConnection',
            },
            __typename: 'Site',
          },
          __typename: 'SiteEdge',
        },
      ],
      __typename: 'SiteConnection',
    },
  },
}

export const mockGarage0RoomDetailsFragment: MockedRoomData = {
  data: {
    room: {
      name: 'Garage 0-test',
      __typename: 'Room',
      defaultCamera: {
        id: 'at_gar_cam1_720p',
        __typename: 'Camera',
      },
      site: {
        id: 'lab_1',
        name: 'Apella Lab',
        turnoverGoals: {
          goalMinutes: 35,
          maxMinutes: 80,
          __typename: 'TurnoverGoals',
        },
        __typename: 'Site',
      },
      cameras: {
        edges: [
          {
            node: {
              id: 'at-gar-cam1-720p',
              __typename: 'Camera',
            },
            __typename: 'CameraEdge',
          },
          {
            node: {
              id: 'at-gar-cam2-720p',
              __typename: 'Camera',
            },
            __typename: 'CameraEdge',
          },
          {
            node: {
              id: 'at-gar-cam3-720p',
              __typename: 'Camera',
            },
            __typename: 'CameraEdge',
          },
          {
            node: {
              id: 'at_gar_cam0_720p',
              __typename: 'Camera',
            },
            __typename: 'CameraEdge',
          },
          {
            node: {
              id: 'at_gar_cam1_720p',
              __typename: 'Camera',
            },
            __typename: 'CameraEdge',
          },
        ],
        __typename: 'CameraConnection',
      },
    },
  },
}

export const mockGarage1RoomDetailsFragment: MockedRoomData = {
  data: {
    room: {
      name: 'Garage 1',
      __typename: 'Room',
      defaultCamera: {
        id: 'at_gar_cam2_720p',
        __typename: 'Camera',
      },
      site: {
        id: 'lab_1',
        name: 'Apella Lab',
        turnoverGoals: {
          goalMinutes: 35,
          maxMinutes: 80,
          __typename: 'TurnoverGoals',
        },
        __typename: 'Site',
      },
      cameras: {
        edges: [
          {
            node: {
              id: 'at_gar_cam2_720p',
              __typename: 'Camera',
            },
            __typename: 'CameraEdge',
          },
        ],
        __typename: 'CameraConnection',
      },
    },
  },
}

export const mockSiteFragment: GetSiteDetails = {
  site: {
    id: 'lab_1',
    name: 'Apella Lab',
    turnoverGoals: {
      goalMinutes: 35,
      maxMinutes: 80,
      __typename: 'TurnoverGoals',
    },

    __typename: 'Site',
  },
}
