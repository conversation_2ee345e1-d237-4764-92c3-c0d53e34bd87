import { useEffect, useState } from 'react'

import { useTheme } from '@emotion/react'

import { sortBy } from 'lodash'

import {
  FlexContainer,
  H3,
  H4,
  remSpacing,
  shape,
  Table,
  TBody,
  TH,
  THead,
  TR,
} from '@apella/component-library'
import { CaseType } from 'src/__generated__/globalTypes'
import { ToggleableCaseKey } from 'src/components/Keys'
import { PrePostOpApellaCase } from 'src/pages/PreOpPostOp/types'
import {
  ScheduleFilterProviderContext,
  useScheduleFilterContext,
} from 'src/pages/Schedule/ScheduleFilterContext'
import { ScheduleFilters } from 'src/pages/Schedule/ScheduleFilters'

import LoadingOverlay from '../../components/LoadingOverlay'
import { PageContentTemplate } from '../../components/PageContentTemplate'
import { CasesMetrics } from '../Schedule/CasesMetrics'
import { ScheduleFilterProvider } from '../Schedule/ScheduleFilterProvider'
import SchedulePageHeader from '../Schedule/SchedulePageHeader'
import {
  AnesthesiaCell,
  EstimatedStartTimeCell,
  PatientCell,
  ProcedureCell,
  RoomCell,
  ScheduledTimeCell,
  SurgeonCell,
  TDTopAligned,
} from './ScheduleTableCells'
import { usePrePostOpState } from './usePrePostOpState'

const PreOpPage = (): React.JSX.Element => {
  const theme = useTheme()

  const { siteIds, roomIds, minTime, maxTime } = useScheduleFilterContext()

  const { cases, casesLoading } = usePrePostOpState(minTime, maxTime, {
    siteIds,
    roomIds,
  })

  const filteredCases = cases.filter((p) => p.type === CaseType.FORECAST)

  return (
    <PageContentTemplate maxContainerSize="full">
      <SchedulePageHeader selectedSubNavId={'pre-op'} />
      <ScheduleFilters
        showToggleScheduledCases={false}
        showSurgeonFilter={false}
      />
      <CasesMetrics cases={cases} isLoading={casesLoading} />
      <FlexContainer
        css={{
          overflow: 'auto',
          minHeight: 300,
          border: `1px solid ${theme.palette.gray[30]}`,
          borderRadius: shape.borderRadius.xsmall,
          padding: remSpacing.medium,
        }}
        direction="column"
        gap={remSpacing.medium}
      >
        <H4>Patient queue</H4>
        <PreOpTable apellaCases={filteredCases} isLoading={casesLoading} />
      </FlexContainer>
      <ToggleableCaseKey />
    </PageContentTemplate>
  )
}

export const PreOp = () => {
  return (
    <ScheduleFilterProvider context={ScheduleFilterProviderContext.PRE_OP}>
      <PreOpPage />
    </ScheduleFilterProvider>
  )
}

const PreOpTable = ({
  apellaCases,
  isLoading,
}: {
  apellaCases: PrePostOpApellaCase[]
  isLoading: boolean
}): React.JSX.Element => {
  const [maxHeight, setMaxHeight] = useState(500)
  useEffect(() => {
    setMaxHeight(Math.max(500, window.innerHeight * 0.8))
  }, [])

  return (
    <>
      {isLoading && <LoadingOverlay />}
      <div
        css={{
          maxHeight: maxHeight,
          overflowY: 'auto',
        }}
      >
        <Table>
          <THead
            css={{
              position: 'sticky',
              top: 0,
              backgroundColor: 'white',
            }}
          >
            <TR>
              <TH colSpan={2}>Est. start</TH>
              <TH>Patient</TH>
              <TH>Procedure</TH>
              <TH>Anesthesia</TH>
              <TH>Surgeon</TH>
              <TH>Destination Room</TH>
            </TR>
          </THead>
          <TBody>
            {!isLoading && apellaCases.length === 0 && (
              <TR>
                <td
                  colSpan={100}
                  css={{ padding: remSpacing.xlarge, textAlign: 'center' }}
                >
                  <H3>No cases in progress</H3>
                  This may occur if cases have not started yet or are all in
                  progress.
                </td>
              </TR>
            )}
            {sortBy(apellaCases, (ac) => ac.startTime.valueOf()).map((ac) => {
              const isNextCaseForRoom = !apellaCases
                .filter((other) => ac.room.id === other.room.id)
                .some((other) => other.startTime < ac.startTime)

              return (
                <TR key={ac.startTime.toISO() + ac.room.id}>
                  <TDTopAligned>
                    <EstimatedStartTimeCell apellaCase={ac} />
                  </TDTopAligned>
                  <TDTopAligned>
                    <ScheduledTimeCell
                      scheduledTime={ac.case?.scheduledStartTime}
                      predictedTime={ac.startTime}
                    />
                  </TDTopAligned>
                  <TDTopAligned>
                    <PatientCell apellaCase={ac} showStatus />
                  </TDTopAligned>
                  <TDTopAligned>
                    <ProcedureCell apellaCase={ac} />
                  </TDTopAligned>
                  <TDTopAligned>
                    <AnesthesiaCell apellaCase={ac} />
                  </TDTopAligned>
                  <TDTopAligned>
                    <SurgeonCell apellaCase={ac} />
                  </TDTopAligned>
                  <TDTopAligned>
                    <RoomCell apellaCase={ac} showStatus={isNextCaseForRoom} />
                  </TDTopAligned>
                </TR>
              )
            })}
          </TBody>
        </Table>
      </div>
    </>
  )
}
