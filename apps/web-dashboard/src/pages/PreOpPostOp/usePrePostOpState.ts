import { useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { CaseFlag, CaseFlagType } from 'src/modules/planning/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import { PrePostOpApellaCase } from 'src/pages/PreOpPostOp/types'
import { toStaffFullName } from 'src/utils/roles'
import { usePatientReadAccess } from 'src/utils/usePatientReadAccess'
import { usePolling } from 'src/utils/usePolling'

import { useTimezone } from '../../Contexts'
import { getConflictingLastNames } from '../../utils/getFullName'
import { SLOW_POLL_INTERVAL_MS } from '../Live/consts'
import { ApellaRoom } from '../types'
import { GetPrePostOpData, GetPrePostOpDataVariables } from './__generated__'
import { GET_PRE_POST_OP_DATA } from './queries'

interface UsePrePostOpStateOptions {
  roomIds?: string[]
  siteIds?: string[]
}

const rawCaseToCase = (
  {
    id,
    site,
    scheduledStartTime,
    scheduledEndTime,
    caseStaff,
    primaryCaseProcedures,
    isAddOn,
    isInFlipRoom,
    patientClass,
    precedingCase,
    caseClassificationType,
    isFirstCase,
    externalCaseId,
    patient,
    caseFlags,
  }: NonNullable<
    GetPrePostOpData['sites']['edges'][number]['node']['rooms']['edges'][number]['node']['apellaCases']['edges'][number]['node']['case']
  >,
  room: ApellaRoom,
  conflictingLastNames: Set<string>,
  timezone: string
) => ({
  id,
  room,
  site,
  scheduledStartTime: DateTime.fromISO(scheduledStartTime).setZone(timezone),
  scheduledEndTime: DateTime.fromISO(scheduledEndTime).setZone(timezone),
  staff: caseStaff.map((cs) => ({
    firstName: cs.staff.firstName,
    lastName: cs.staff.lastName,
    displayName: conflictingLastNames.has(cs.staff.lastName)
      ? toStaffFullName({ staff: cs.staff, displayLastNameFirst: true })
      : cs.staff.lastName,
    id: cs.staff.id,
    role: cs.role ?? '',
  })),
  procedures: primaryCaseProcedures
    .map((pcp) => ({
      name: pcp.procedure.name,
      anesthesiaType: pcp.anesthesia?.name,
    }))
    .filter(Boolean),
  isAddOn: !!isAddOn,
  isInFlipRoom: !!isInFlipRoom,
  isFirstCase: !!isFirstCase,
  patientClass: patientClass ?? undefined,
  precedingCaseId: precedingCase?.id,
  caseClassificationType: caseClassificationType ?? undefined,
  externalCaseId,
  caseFlags:
    caseFlags?.map<CaseFlag>((cf) => {
      return {
        id: cf.id,
        type: cf.flagType as CaseFlagType,
        active: !cf.archivedTime,
      }
    }) || [],
  caseLabels: undefined,
  patient: !!patient
    ? {
        id: patient.id,
        administrativeSex: patient.personalInfo?.administrativeSex?.text || '',
        abbreviatedName: patient.personalInfo
          ? `${patient.personalInfo.lastNameAbbreviated}., ${patient.personalInfo.firstNameAbbreviated}.`
          : '',
        age: patient.personalInfo?.age ?? undefined,
      }
    : undefined,
})

const usePrePostOpState = (
  minTime: string,
  maxTime: string,
  options?: UsePrePostOpStateOptions,
  skip?: boolean
): {
  cases: PrePostOpApellaCase[]
  casesLoading: boolean
} => {
  const { timezone } = useTimezone()
  const { permissions, loading } = useCurrentUser()
  const showPatientData = usePatientReadAccess()

  const {
    loading: casesLoading,
    data: caseSitesData,
    startPolling,
    stopPolling,
    refetch,
  } = useQuery<GetPrePostOpData, GetPrePostOpDataVariables>(
    GET_PRE_POST_OP_DATA,
    {
      variables: {
        // If a phase started before the time range given but ended in the time
        // range we want that to be returned. Because the end time of the phase
        // is nullable (in the case of incomplete phases, which we want to be
        // returned) we can't query by it, we can only query by the start time.
        // Thus we query a larger set of phases based on start time and do the
        // filtering on the client side.
        minTime,
        maxTime,
        siteIds: options?.siteIds,
        roomIds: options?.roomIds,
        skipPatientInfo: !showPatientData,
        includeStaffPlan: !!permissions?.caseStaffPlanReadEnabled,
      },
      skip: skip || loading,
      // Allow partial data when loading the PreOp/PostOp pages, since it loads data from many
      // different sources, and they're not all required to render the page.
      errorPolicy: 'all',
    }
  )

  usePolling({
    startPolling,
    stopPolling,
    refetch,
    interval: SLOW_POLL_INTERVAL_MS,
    skip,
  })

  const cases: PrePostOpApellaCase[] = useMemo(() => {
    if (!caseSitesData?.sites?.edges) {
      return []
    }

    const allStaff = caseSitesData.sites.edges
      .flatMap(({ node: site }) =>
        site.rooms.edges.flatMap((r) =>
          r.node.apellaCases.edges.flatMap((c) =>
            c.node.case?.caseStaff.map((cs) => cs.staff)
          )
        )
      )
      .filter(Boolean)
    const conflictingLastNames = getConflictingLastNames(allStaff)

    const casesResult = caseSitesData.sites.edges.flatMap(({ node: site }) =>
      site.rooms.edges.flatMap(
        ({
          node: {
            apellaCases: { edges },
          },
        }) =>
          edges.flatMap<PrePostOpApellaCase>(({ node: apellaCase }) => ({
            id: apellaCase.id,
            case: !!apellaCase.case
              ? rawCaseToCase(
                  apellaCase.case,
                  apellaCase.room,
                  conflictingLastNames,
                  timezone
                )
              : undefined,
            room: {
              ...apellaCase.room,
              status: {
                ...apellaCase.room.status,
                inProgressApellaCase: apellaCase.room.status
                  .inProgressApellaCase
                  ? {
                      status: {
                        ...apellaCase.room.status.inProgressApellaCase.status,
                        since: apellaCase.room.status.inProgressApellaCase
                          .status.since
                          ? DateTime.fromISO(
                              apellaCase.room.status.inProgressApellaCase.status
                                .since
                            ).setZone(timezone)
                          : undefined,
                      },
                    }
                  : undefined,
                since: apellaCase.room.status.since
                  ? DateTime.fromISO(apellaCase.room.status.since).setZone(
                      timezone
                    )
                  : undefined,
              },
            },
            site: site,
            status: {
              name: apellaCase.status.name,
              since: apellaCase.status.since
                ? DateTime.fromISO(apellaCase.status.since).setZone(timezone)
                : undefined,
            },
            startTime: DateTime.fromISO(apellaCase.startTime).setZone(timezone),
            endTime: apellaCase.endTime
              ? DateTime.fromISO(apellaCase.endTime).setZone(timezone)
              : undefined,
            type: apellaCase.type,
          }))
      )
    )

    return casesResult
  }, [caseSitesData?.sites?.edges, timezone])

  return { cases, casesLoading }
}

export { usePrePostOpState }
