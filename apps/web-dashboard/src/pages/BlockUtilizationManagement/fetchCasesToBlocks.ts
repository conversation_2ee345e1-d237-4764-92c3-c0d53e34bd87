import { ApolloClient, NormalizedCacheObject } from '@apollo/client'
import { DateTime } from 'luxon'

import { PRIMARY_SURGEON_ROLES } from 'src/utils/roles'

import { CasesToBlocks, CasesToBlocksVariables } from './__generated__'
import { CaseStatus, DEFAULT_TURNOVER_DURATION_IN_MINUTES } from './constants'
import { GET_CASES_TO_BLOCKS } from './queries'
import { CaseToBlockType } from './types'

export const fetchCasesToBlocks = async (
  client: ApolloClient<NormalizedCacheObject>,
  {
    selectedDateRange,
    selectedSiteId,
    timezone,
    selectedBlockId,
  }: {
    selectedDateRange: string[]
    selectedSiteId: string
    timezone: string
    selectedBlockId?: string
  }
) => {
  if (!selectedDateRange || !selectedSiteId) {
    return Promise.resolve({ casesForBlocks: [] })
  }
  const { data: casesToBlocksData } = await client.query<
    CasesToBlocks,
    CasesToBlocksVariables
  >({
    query: GET_CASES_TO_BLOCKS,
    variables: {
      minDate: selectedDateRange[0],
      maxDate: selectedDateRange[1],
      siteId: selectedSiteId,
    },
    fetchPolicy: 'network-only',
  })
  const casesForBlocks: CaseToBlockType[] =
    casesToBlocksData?.casesToBlocks?.edges
      .filter((caseToBlockEdge) => {
        if (selectedBlockId) {
          return selectedBlockId === caseToBlockEdge.node.blockId
        }
        return true
      })
      .map((caseToBlockEdge) => {
        const scheduledEndTime = DateTime.fromISO(
          caseToBlockEdge.node.scheduledCase?.scheduledEndTime ?? '',
          {
            zone: timezone,
          }
        )
        const scheduledStartTime = DateTime.fromISO(
          caseToBlockEdge.node.scheduledCase?.scheduledStartTime ?? '',
          {
            zone: timezone,
          }
        )

        const status =
          caseToBlockEdge.node.scheduledCase?.status ?? CaseStatus.SCHEDULED
        const cancelledAndOverriden =
          status === CaseStatus.CANCELLED &&
          caseToBlockEdge.node.override &&
          caseToBlockEdge.node.override.utilizedProcedureMinutes
        return {
          externalId: caseToBlockEdge.node.scheduledCase?.externalCaseId ?? '',
          caseId: caseToBlockEdge.node.caseId,
          blockId: caseToBlockEdge.node.override
            ? caseToBlockEdge.node.override.blockId
            : caseToBlockEdge.node.blockId,
          scheduledStartTime,
          scheduledEndTime,
          primaryStaffNames:
            caseToBlockEdge.node.scheduledCase?.caseStaff
              ?.filter(
                (caseStaff) =>
                  caseStaff.role !== null &&
                  PRIMARY_SURGEON_ROLES.includes(caseStaff.role)
              )
              .map(
                (caseStaff) =>
                  `${caseStaff.staff.lastName}, ${caseStaff.staff.firstName}`
              ) || [],
          score: caseToBlockEdge.node.score,
          // use actual case duration if override for utilized procedure minutes does not exist
          caseMinutes: caseToBlockEdge.node.override?.utilizedProcedureMinutes
            ? caseToBlockEdge.node.override?.utilizedProcedureMinutes
            : caseToBlockEdge.node.actualCaseSeconds
              ? Math.floor(caseToBlockEdge.node.actualCaseSeconds / 60)
              : undefined,
          actualMinutes: caseToBlockEdge.node.actualCaseSeconds
            ? Math.floor(caseToBlockEdge.node.actualCaseSeconds / 60)
            : undefined,
          utilizedTurnoverMinutes: caseToBlockEdge.node.override
            ?.utilizedTurnoverMinutes
            ? caseToBlockEdge.node.override.utilizedTurnoverMinutes
            : DEFAULT_TURNOVER_DURATION_IN_MINUTES,
          utilizedProcedureMinutes: caseToBlockEdge.node.override
            ?.utilizedProcedureMinutes
            ? caseToBlockEdge.node.override.utilizedProcedureMinutes
            : caseToBlockEdge.node.utilizedCaseSeconds
              ? Math.floor(caseToBlockEdge.node.utilizedCaseSeconds / 60)
              : undefined,
          status: cancelledAndOverriden ? CaseStatus.OVERRIDDEN : status,
          procedures:
            caseToBlockEdge.node.scheduledCase?.primaryCaseProcedures?.map(
              (primaryCaseProcedure) => primaryCaseProcedure.procedure.name
            ) ?? [],
        }
      }) || []

  return {
    casesForBlocks,
  }
}
