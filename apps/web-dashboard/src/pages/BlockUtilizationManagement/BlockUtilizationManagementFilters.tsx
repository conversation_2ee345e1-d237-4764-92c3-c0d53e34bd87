import { DateTime } from 'luxon'

import {
  DatePicker,
  SingleSelect,
  Option,
  Button,
  Close,
  FlexContainer,
  remSpacing,
} from '@apella/component-library'
import { Block } from 'src/__generated__/globalTypes'
import { SitesFilter, SiteShape } from 'src/components/SitesFilter'
import { useTimezone } from 'src/Contexts'

import {
  BLOCK_FILTER,
  DATE_RANGE_FILTER,
  SITE_FILTER,
  useBlockUtilizationManagementFilters,
} from './useBlockUtilizationManagementFilters'

export const BlockUtilizationManagementFilters = ({
  sites,
  blocks,
}: {
  sites: SiteShape[]
  blocks: Block[]
}) => {
  const {
    onSearch,
    selectedDateRange,
    selectedBlockId,
    selectedSiteId,
    clearParams,
  } = useBlockUtilizationManagementFilters()
  const { timezone } = useTimezone()

  return (
    <FlexContainer gap={remSpacing.small}>
      {/* TODO: update datepicker only show up to 1 month */}
      <DatePicker
        value={[
          DateTime.fromISO(selectedDateRange[0], { zone: timezone }).toJSDate(),
          DateTime.fromISO(selectedDateRange[1], { zone: timezone }).toJSDate(),
        ]}
        selectRange
        showPresets
        timezone={timezone}
        setValue={(dates) => {
          if (Array.isArray(dates) && dates[0] && dates[1]) {
            const startDate = DateTime.fromJSDate(dates[0], { zone: timezone })
            const endDate = DateTime.fromJSDate(dates[1], { zone: timezone })
            // check to make sure range is less than 1 month for now
            if (endDate.diff(startDate, 'days').days >= 32) {
              alert('Invalid date range 1 month is the max range')
            } else {
              onSearch({
                [DATE_RANGE_FILTER]: [
                  startDate.toISODate(),
                  endDate.toISODate(),
                ],
              })
            }
          }
        }}
      />
      <SitesFilter
        sites={sites}
        selectedSiteIds={selectedSiteId}
        onChangeSites={(siteId) => {
          onSearch({ [SITE_FILTER]: siteId ?? '', [BLOCK_FILTER]: '' })
        }}
        disableSelectAllOption
      />
      <SingleSelect
        disabled={!selectedSiteId}
        name="Block"
        value={selectedBlockId}
        onChange={(blockId) => {
          onSearch({ [BLOCK_FILTER]: blockId ?? '' })
        }}
        label={'Select a block'}
        selectedOnTop
      >
        {blocks.map((block) => (
          <Option key={block.id} value={block.id} label={block.name} />
        ))}
      </SingleSelect>
      <Button
        appearance="link"
        onClick={() => {
          clearParams()
        }}
      >
        <Close size="sm" />
        Clear
      </Button>
    </FlexContainer>
  )
}
