import {
  ActionFunctionArgs,
  Form,
  LoaderFunctionArgs,
  redirect,
  replace,
  useActionData,
  useLoaderData,
} from 'react-router'

import { getFormProps, useForm } from '@conform-to/react'
import { parseWithYup } from '@conform-to/yup'
import * as yup from 'yup'

import {
  Button,
  ButtonLink,
  FlexContainer,
  InputNumber,
  InputText,
  remSpacing,
  theme,
} from '@apella/component-library'
import { parseParams } from '@apella/hooks'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { defaultTimezone } from 'src/Contexts'
import { useBreadCrumbs } from 'src/router/hooks/useBreadCrumbs'
import { getRouteContext, LocationPath } from 'src/router/types'

import {
  UpsertCaseToBlockOverrides,
  UpsertCaseToBlockOverridesVariables,
} from './__generated__'
import { fetchCasesToBlocks } from './fetchCasesToBlocks'
import { UPSERT_CASES_TO_BLOCKS_OVERRIDES } from './queries'
import { CaseToBlockType } from './types'
import { castParams } from './useBlockUtilizationManagementFilters'

export const loader = async (
  { params, request }: LoaderFunctionArgs,
  context: unknown
) => {
  const { flags, apolloClient, user } = getRouteContext(context)
  if (!flags.enableBlockUtilizationManagementPage) {
    throw replace(LocationPath.Home)
  }
  const caseId = params.caseId
  // add cases to blocks query
  const requestURL = new URL(request.url)
  const parsedParams = parseParams(requestURL.search)
  const { selectedSiteId, selectedDateRange } = castParams(parsedParams)

  // if you haven't selected a site or date range go back to block utilization page
  if (!selectedSiteId || !selectedDateRange) {
    throw replace(LocationPath.BlockUtilizationManagement)
  }

  const timezone =
    user.currentOrganization?.node.sites.edges[0]?.node.timezone ||
    defaultTimezone

  const { casesForBlocks } = await fetchCasesToBlocks(apolloClient, {
    selectedSiteId,
    selectedDateRange,
    timezone,
  })
  const filteredToSelectedCase = casesForBlocks.filter(
    (caseToBlock: CaseToBlockType) => caseToBlock.caseId === caseId
  )

  // unable to find case so redirect to block utilization page
  if (filteredToSelectedCase.length !== 1) {
    throw replace(LocationPath.BlockUtilizationManagement)
  }

  return {
    caseSelected: filteredToSelectedCase[0],
    redirectSearchParams: requestURL.search,
  }
}

const schema = yup.object({
  blockId: yup.string().required(),
  blockDate: yup.string().required(),
  utilizedProcedureMinutes: yup
    .number()
    .positive(
      'Utilized procedure minutes must be greater than 0 if editing a case'
    ),
  utilizedTurnoverMinutes: yup.number(),
  note: yup.string(),
})

export const action = async (
  { params, request }: ActionFunctionArgs,
  context: unknown
) => {
  const formData = await request.formData()
  const submission = parseWithYup(formData, { schema })

  if (submission.status !== 'success') {
    return submission.reply()
  }

  const { apolloClient, user } = getRouteContext(context)

  const { data } = await apolloClient.mutate<
    UpsertCaseToBlockOverrides,
    UpsertCaseToBlockOverridesVariables
  >({
    mutation: UPSERT_CASES_TO_BLOCKS_OVERRIDES,
    variables: {
      input: [
        {
          blockDate: submission.value.blockDate,
          blockId: submission.value.blockId,
          caseId: params.caseId!,
          note: submission.value.note,
          utilizedTurnoverMinutes: submission.value.utilizedTurnoverMinutes,
          utilizedProcedureMinutes: submission.value.utilizedProcedureMinutes,
          userId: user.userId,
        },
      ],
    },
  })
  if (data?.caseToBlockOverridesUpsert?.success) {
    const requestURL = new URL(request.url)
    return redirect(
      LocationPath.BlockUtilizationManagement +
        '?' +
        requestURL.searchParams.toString()
    )
  }
  return submission.reply({
    formErrors: [`Failed to submit case edits`],
  })
}

type FormData = yup.InferType<typeof schema>

export const BlockUtilizationCaseEditView = () => {
  const { caseSelected, redirectSearchParams } =
    useLoaderData<Awaited<ReturnType<typeof loader>>>()

  const scheduledCaseDurationInMinutes = Math.floor(
    caseSelected.scheduledEndTime
      .diff(caseSelected.scheduledStartTime)
      .as('minutes')
  )

  const breadcrumbs = useBreadCrumbs()

  const lastResult = useActionData()
  const [
    form,
    {
      blockDate,
      blockId,
      utilizedProcedureMinutes,
      utilizedTurnoverMinutes,
      note,
    },
  ] = useForm<FormData>({
    lastResult,
    shouldValidate: 'onBlur',
    onValidate({ formData }) {
      return parseWithYup(formData, { schema })
    },
  })
  return (
    <PageContentTemplate
      title={'Case view'}
      maxContainerSize="largeForm"
      breadcrumbs={breadcrumbs}
    >
      <Form method="post" {...getFormProps(form)}>
        <FlexContainer
          direction="column"
          gap={remSpacing.xsmall}
          css={{ paddingBottom: remSpacing.small }}
        >
          <p>Primary Surgeons: {caseSelected.primaryStaffNames?.join('; ')}</p>
          <p>
            Scheduled case duration in minutes: {scheduledCaseDurationInMinutes}
          </p>
          {caseSelected.caseMinutes && (
            <p>Actual case duration in minutes: {caseSelected.caseMinutes}</p>
          )}
        </FlexContainer>
        {form.errors && <ErrorValidationMessage errors={form.errors} />}
        <input type="hidden" name={blockId.name} value={caseSelected.blockId} />
        <input
          type="hidden"
          name={blockDate.name}
          value={caseSelected.scheduledEndTime.toISODate()}
        />
        <InputNumber
          label="Utilized Procedure Minutes"
          id="utilizedProcedureMinutes"
          name={utilizedProcedureMinutes.name}
          defaultValue={caseSelected?.utilizedProcedureMinutes}
        />
        {utilizedProcedureMinutes.errors && (
          <ErrorValidationMessage errors={utilizedProcedureMinutes.errors} />
        )}

        <InputNumber
          label="Turnover Minutes"
          id={utilizedTurnoverMinutes.id}
          name={utilizedTurnoverMinutes.name}
          defaultValue={caseSelected?.utilizedTurnoverMinutes}
        />
        {utilizedTurnoverMinutes.errors && (
          <ErrorValidationMessage errors={utilizedTurnoverMinutes.errors} />
        )}

        <InputText
          name="note"
          label="Note"
          placeholder="Some additional information as to why this edit was made"
          multiline
          rows={3}
        />
        {note.errors && <ErrorValidationMessage errors={note.errors} />}
        <FlexContainer
          justifyContent="space-between"
          gap={remSpacing.small}
          css={{ margin: `${remSpacing.medium} 0 ${remSpacing.large}` }}
        >
          <ButtonLink
            color="alternate"
            to={{
              pathname: LocationPath.BlockUtilizationManagement,
              search: redirectSearchParams,
            }}
          >
            Back
          </ButtonLink>
          <Button type="submit">Save</Button>
        </FlexContainer>
      </Form>
    </PageContentTemplate>
  )
}

const ErrorValidationMessage = ({ errors }: { errors: string[] }) => {
  return (
    <div css={{ color: theme.palette.red[40] }}>
      {Array.isArray(errors)
        ? errors.map((error, index) => <div key={index}>{error}</div>)
        : errors}
    </div>
  )
}
