import { DateTime } from 'luxon'
import { ParsedQs } from 'qs'

import { useParsedSearchParams } from '@apella/hooks'

export const DATE_RANGE_FILTER = 'dateRange'
export const SITE_FILTER = 'site'
export const BLOCK_FILTER = 'block'

export type BlockUtilizationManagementFiltersType = {
  [DATE_RANGE_FILTER]: string[]
  [SITE_FILTER]: string
  [BLOCK_FILTER]: string
}
const today = DateTime.now()
const weekago = DateTime.now().plus({ day: -7 })
export const defaults = {
  [DATE_RANGE_FILTER]: [weekago.toISODate(), today.toISODate()],
  [SITE_FILTER]: '',
  [BLOCK_FILTER]: '',
}

export const castParams = (params: ParsedQs) => {
  const {
    [DATE_RANGE_FILTER]: selectedDateRangeParam,
    [SITE_FILTER]: selectedSiteIdParam,
    [BLOCK_FILTER]: selectedBlockIdParam,
  } = params

  const selectedDateRange =
    Array.isArray(selectedDateRangeParam) &&
    selectedDateRangeParam.length === 2 &&
    selectedDateRangeParam.every(
      (date) => typeof date === 'string' && DateTime.fromISO(date).isValid
    )
      ? (selectedDateRangeParam as string[])
      : defaults[DATE_RANGE_FILTER]

  const selectedSiteId =
    typeof selectedSiteIdParam === 'string'
      ? selectedSiteIdParam
      : defaults[SITE_FILTER]

  const selectedBlockId =
    typeof selectedBlockIdParam === 'string'
      ? selectedBlockIdParam
      : defaults[BLOCK_FILTER]

  return {
    selectedDateRange,
    selectedSiteId,
    selectedBlockId,
  }
}

export const useBlockUtilizationManagementFilters = () => {
  const { params, onSearch, ...rest } =
    useParsedSearchParams<BlockUtilizationManagementFiltersType>({
      defaults,
    })
  const typedParams = castParams(params)
  return {
    params,
    onSearch,
    ...typedParams,
    ...rest,
  }
}
