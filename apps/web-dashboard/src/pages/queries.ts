import { gql } from '@apollo/client'

import { EventSourceType } from './types'

export const ROOM_OBSERVATION_QUERY = gql`
  query RoomObservationQuery(
    $roomIds: [ID!]
    $minObservationTime: DateTime!
    $maxObservationTime: DateTime!
    $types: [ID!]
  ) {
    observation(
      query: {
        roomIds: $roomIds
        typeIds: $types
        minObservationTime: $minObservationTime
        maxObservationTime: $maxObservationTime
      }
    ) {
      edges {
        node {
          id
          observationTime
          observationType {
            id
            name
            description
            color
          }
        }
      }
    }
  }
`

export const ROOM_EVENTS_QUERY = gql`
  query RoomEventsQuery(
    $roomId: ID!
    $minTime: DateTime!
    $maxTime: DateTime!
    $eventNames: [String!]
    $includeDashboardEventsOnly: Boolean!
  ) {
    humanEvents: eventSearch(
      query: {
        minStartTime: $minTime
        maxStartTime: $maxTime
        roomId: $roomId
        sourceType: "${EventSourceType.Human}"
        eventNames: $eventNames
        includeDashboardEventsOnly: $includeDashboardEventsOnly
      }
    ) {
      edges {
        node {
          id
          sourceType
          name
          startTime
          attrs {
            id
            type
            name
            color
          }
        }
      }
    }

    predictedEvents: eventSearch(
      query: {
        minStartTime: $minTime
        maxStartTime: $maxTime
        roomId: $roomId
        sourceType: "${EventSourceType.Prediction}"
        includeDashboardEventsOnly: true
      }
      orderBy: [{ sort: "startTime", direction: ASC }]
    ) {
      edges {
        node {
          id
          sourceType
          name
          startTime
          attrs {
            id
            type
            name
            color
          }
        }
      }
    }
  }
`

export const ROOM_NAME_FRAGMENT = gql`
  fragment RoomNameFragment on Room {
    name
  }
`

export const ROOM_DEFAULT_CAMERA_FRAGMENT = gql`
  fragment RoomDefaultCameraFragment on Room {
    defaultCamera {
      id
    }
  }
`

export const SITE_FRAGMENT = gql`
  fragment SiteFragment on Site {
    id
    name
    turnoverGoals {
      goalMinutes
      maxMinutes
    }
  }
`

export const ROOM_SITE_FRAGMENT = gql`
  ${SITE_FRAGMENT}
  fragment RoomSiteFragment on Room {
    site {
      ...SiteFragment
    }
  }
`

// This fragment conditionally includes fields based on the blurVideoPlayer flag
export const ROOM_CAMERAS_FRAGMENT = gql`
  fragment RoomCamerasFragment on Room {
    cameras {
      edges {
        node {
          id
          patientBoundingBox @include(if: $blurVideoPlayer) {
            leftPct
            bottomPct
            widthPct
            heightPct
          }
        }
      }
    }
  }
`

export const GET_ROOM_DETAILS = gql`
  ${ROOM_NAME_FRAGMENT}
  ${ROOM_DEFAULT_CAMERA_FRAGMENT}
  ${ROOM_SITE_FRAGMENT}
  ${ROOM_CAMERAS_FRAGMENT}
  query GetRoomDetails($roomId: String!, $blurVideoPlayer: Boolean = false) {
    room(id: $roomId) {
      ...RoomNameFragment
      ...RoomDefaultCameraFragment
      ...RoomSiteFragment
      ...RoomCamerasFragment
    }
  }
`

export const GET_ROOM_NAME = gql`
  ${ROOM_NAME_FRAGMENT}
  query GetRoomName($roomId: String!) {
    room(id: $roomId) {
      ...RoomNameFragment
    }
  }
`

export const GET_ROOM_SITE_ID = gql`
  ${ROOM_SITE_FRAGMENT}
  query GetRoomSiteId($roomId: String!) {
    room(id: $roomId) {
      ...RoomSiteFragment
    }
  }
`

export const GET_SITE_DETAILS = gql`
  ${SITE_FRAGMENT}
  query GetSiteDetails($siteId: String!) {
    site(id: $siteId) {
      ...SiteFragment
    }
  }
`

export const GET_EVENT_TYPES = gql`
  query GetEventTypes {
    eventTypes {
      edges {
        node {
          id
          type
          name
          description
          color
          hidden
        }
      }
    }
  }
`
