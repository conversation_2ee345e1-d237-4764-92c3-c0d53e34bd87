import { DateTime } from 'luxon'

import { getCleanedUrlParam } from 'src/utils/getCleanedUrlParam'
import { DATE_KEY } from 'src/utils/useDateState'
import { SITES_KEY } from 'src/utils/useSitesState'

import { ROOMS_PARAM } from './ScheduleFilterProvider'

export const getScheduleSearchParams = (searchParams: URLSearchParams) => {
  const selectedSiteIds = getCleanedUrlParam<string[] | undefined>({
    searchParams,
    key: SITES_KEY,
    defaultValue: undefined,
  })

  const selectedRoomIds = getCleanedUrlParam<string[] | undefined>({
    searchParams,
    key: ROOMS_PARAM,
    defaultValue: undefined,
  })

  const selectedDate = getCleanedUrlParam<string>({
    searchParams,
    key: DATE_KEY,
    defaultValue: DateTime.now().toISODate(),
  })

  return {
    selectedSiteIds,
    selectedRoomIds,
    selectedDate,
  }
}
