import { Placement } from '@floating-ui/react-dom'

import {
  Button,
  HideMetrics,
  ShowMetrics,
  Tooltip,
} from '@apella/component-library'

import { useScheduleFilterContext } from './ScheduleFilterContext'

export const MetricsToggleButton = ({
  placement = 'bottom-end',
}: {
  placement?: Placement
}): React.JSX.Element => {
  const { showMetrics, onToggleShowMetrics } = useScheduleFilterContext()

  const buttonNode = (
    <Button
      color="alternate"
      onClick={() => onToggleShowMetrics(!!!showMetrics)}
      buttonType="icon"
      data-testid="metric-header-clicked"
    >
      {showMetrics ? <HideMetrics size="sm" /> : <ShowMetrics size="sm" />}
    </Button>
  )

  const tooltipNode = (
    <Tooltip
      body={
        <div css={{ whiteSpace: 'nowrap' }}>
          {showMetrics ? 'Hide OR metrics' : 'Show OR metrics'}
        </div>
      }
      strategy={showMetrics ? 'fixed' : undefined}
      placement={placement}
      isFloatingPortal={!showMetrics}
    >
      {buttonNode}
    </Tooltip>
  )

  return tooltipNode
}
