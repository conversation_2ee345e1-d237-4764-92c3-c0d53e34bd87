import { ReactNode } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import {
  Caps3,
  Clock,
  FlexContainer,
  FlexItem,
  H6,
  remSpacing,
  shape,
  mediaQueries,
  formatDuration,
  ApellaDateTimeFormats,
} from '@apella/component-library'
import { EM_DASH } from 'src/utils/htmlCodes'

import {
  CaseStatusName,
  CaseType,
  PatientClass,
} from '../../__generated__/globalTypes'
import { StatusPill } from '../../components/Keys'
import {
  formatStatus,
  StatusSuperset,
  useStatusStyles,
} from '../../utils/status'
import { useCurrentMinute } from '../../utils/useCurrentTime'
import { ApellaCase, Case } from '../types'

export const ScheduledTimingNode = ({
  caseObj,
}: {
  caseObj: ApellaCase['case']
}) => {
  const theme = useTheme()
  return (
    <CaseTimingDatum
      title={'Scheduled'}
      value={
        <FlexContainer gap={remSpacing.xxsmall} alignItems="center">
          <Clock size={'xs'} color={theme.palette.gray[50]} />
          <H6 color={theme.palette.gray[50]}>
            {caseObj ? (
              <>
                {caseObj.scheduledStartTime.toLocaleString(
                  ApellaDateTimeFormats.TIME
                )}{' '}
                -{' '}
                {caseObj.scheduledEndTime.toLocaleString(
                  ApellaDateTimeFormats.TIME
                )}
              </>
            ) : (
              EM_DASH
            )}
          </H6>
        </FlexContainer>
      }
    />
  )
}

export const getPatientClassText = (patientClass?: PatientClass) => {
  if (patientClass === undefined || patientClass === PatientClass.OTHER) {
    return undefined
  }

  const patientClassTranslation = {
    [PatientClass.INPATIENT]: 'Inpatient',
    [PatientClass.EMERGENCY]: 'Emergency',
    [PatientClass.HOSPITAL_OUTPATIENT_SURGERY]: 'Outpatient',
    [PatientClass.PRE_ADMIT]: 'Pre Admit',
    [PatientClass.SURGERY_ADMIT]: 'Surgery Admit',
    [PatientClass.OBSERVATION]: 'Observation',
  }

  return patientClassTranslation[patientClass]
}

export const useCaseTags = (caseObj?: Case) => {
  const result = []

  if (caseObj?.isInFlipRoom) {
    result.push('Flip room')
  }
  if (caseObj?.precedingCaseId !== undefined) {
    result.push('Following')
  }
  if (caseObj?.isAddOn) {
    result.push('Add-on (++)')
  }
  if (caseObj?.caseClassificationType) {
    result.push(caseObj?.caseClassificationType.name)
  }
  const patientClassText = getPatientClassText(caseObj?.patientClass)
  if (patientClassText) {
    result.push(patientClassText)
  }
  return result
}

export const CaseTimingData = ({
  apellaCase,
}: {
  apellaCase?: Pick<ApellaCase, 'type' | 'case' | 'startTime' | 'endTime'>
}) => {
  const theme = useTheme()

  const isActualStart = apellaCase?.type !== CaseType.FORECAST

  const isActualEnd = apellaCase?.type === CaseType.COMPLETE

  const caseDuration =
    apellaCase?.endTime && apellaCase.startTime
      ? Math.round(
          apellaCase.endTime.diff(apellaCase?.startTime, 'minutes').minutes
        )
      : undefined

  const caseDurationText = caseDuration
    ? `${caseDuration} Min${caseDuration === 1 ? '' : 's'}`
    : EM_DASH

  return (
    <FlexContainer
      justifyContent={'space-between'}
      alignItems={'center'}
      css={{
        width: '100%',
        [mediaQueries.lg]: {
          width: 'auto',
        },
      }}
    >
      <FlexItem
        grow={1}
        css={{
          color: theme.palette.gray[50],
        }}
      >
        <CaseTimingDatum
          title={`${isActualEnd ? 'Actual' : 'Forecasted'} Duration`}
          value={caseDurationText}
        />
      </FlexItem>
      <FlexContainer
        css={{
          background: theme.palette.blue.background,
          borderRadius: shape.borderRadius.xxsmall,
          padding: `${remSpacing.xsmall} ${remSpacing.small}`,
          marginLeft: remSpacing.xsmall,
          flexGrow: 1,
        }}
        justifyContent="center"
      >
        <FlexItem
          css={{
            borderRight: `1px solid ${theme.palette.gray[30]}`,
            paddingRight: remSpacing.small,
          }}
        >
          <CaseTimingDatum
            title={`${isActualStart ? 'Actual' : 'Forecasted'} start`}
            value={
              apellaCase?.startTime
                ? apellaCase.startTime.toLocaleString(
                    ApellaDateTimeFormats.TIME
                  )
                : EM_DASH
            }
          />
        </FlexItem>
        <FlexItem
          css={{
            paddingLeft: remSpacing.small,
            textAlign: 'right',
          }}
        >
          <CaseTimingDatum
            title={`${isActualEnd ? 'Actual' : 'Forecasted'} end`}
            value={
              apellaCase?.endTime
                ? apellaCase.endTime.toLocaleString(ApellaDateTimeFormats.TIME)
                : EM_DASH
            }
          />
        </FlexItem>
      </FlexContainer>
    </FlexContainer>
  )
}

export const CaseStatusPill = ({
  status,
  since,
}: {
  status: StatusSuperset
  since?: DateTime
}) => {
  const currentMinute = useCurrentMinute()
  const timeInStatus = since ? currentMinute.diff(since) : undefined
  const statusStyles = useStatusStyles({ isKey: true })

  const displayApellaCaseStatus =
    status === CaseStatusName.COMPLETE ? CaseStatusName.ACTUAL : status

  return (
    <StatusPill
      {...statusStyles(displayApellaCaseStatus)}
      css={{
        display: 'flex',
        padding: `${remSpacing.none} ${remSpacing.xsmall}`,
        whiteSpace: 'nowrap',
      }}
    >
      <Caps3 css={{ color: statusStyles(displayApellaCaseStatus).textColor }}>
        {formatStatus(displayApellaCaseStatus)}
        {timeInStatus && `, ${formatDuration(timeInStatus)} elapsed`}
      </Caps3>
    </StatusPill>
  )
}

export const CaseTimingDatum = ({
  title,
  value,
  previousValue,
}: {
  title: ReactNode
  value: ReactNode
  previousValue?: string
}) => {
  const theme = useTheme()
  return (
    <>
      <Caps3
        css={{ color: theme.palette.text.secondary, whiteSpace: 'nowrap' }}
      >
        {title}
      </Caps3>
      <H6 css={{ color: theme.palette.text.tertiary }}>
        {value}
        {previousValue && (
          <span
            css={{
              paddingLeft: remSpacing.xxsmall,
              textDecoration: 'line-through',
              color: theme.palette.red[30],
            }}
          >
            {previousValue}
          </span>
        )}
      </H6>
    </>
  )
}
