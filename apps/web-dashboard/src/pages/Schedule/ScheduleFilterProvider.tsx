import { ReactNode, useCallback, useEffect, useMemo } from 'react'
import { useLocation, useSearchParams } from 'react-router'

import { isEqual } from 'lodash'
import { DateTime } from 'luxon'

import { useLocalStorageState } from '@apella/hooks'
import { toIds } from 'src/components/Filters/FilterWithCount'
import { USER_VIEW_ID } from 'src/components/UserViews/useUserFilterViews'
import { useTimezone } from 'src/Contexts'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { getCleanedUrlParam } from 'src/utils/getCleanedUrlParam'
import { LiveStatus } from 'src/utils/status'
import {
  dateTimeToUrlFriendlyDate,
  urlFriendlyDateToDateTime,
} from 'src/utils/urlFriendlyDate'
import { DATE_KEY, DateQueryParam } from 'src/utils/useDateState'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { SITES_KEY } from '../../utils/useSitesState'
import {
  EventLog,
  eventMapper,
  ScheduleFilterContext,
  ScheduleFilterProviderContext,
  showScheduledRoutes,
} from './ScheduleFilterContext'
import { TimeRange, ViewRoomMetric } from './types'

type LoggingState = Partial<{
  date: string
  dateStatus: string
  layout: string
  maxTime: string
  minTime: string
  roomIds: string[]
  showClosedRooms: boolean
  showFilters: boolean
  showMetrics: boolean
  showScheduled: boolean
  siteIds: string[]
  sortKeys: LiveStatus[]
  surgeonIds: string[]
  timeRange: string
  viewRoomsState: string
  viewingOffset: number
  secondsWatched: number
}>

const SHOW_SCHEDULED_PARAM = 'showScheduled'
const SHOW_CLOSED_ROOMS = 'showClosedRooms'
const SHOW_METRICS = 'showMetrics'
const SHOW_FILTERS = 'showFilters'
const SURGEONS_PARAM = 'surgeons'
export const ROOMS_PARAM = 'rooms'
const METRIC_PARAM = 'metric'
const SORT_PARAM = 'sortKeys'
const TIME_RANGE_PARAM = 'timeRange'
const NUM_COLS_PARAM = 'numCols'

export const useScheduleFilterProviderUrls = () => {
  const [searchParams] = useSearchParams()

  const allSearchParams = new URLSearchParams()
  const allSearchParamsWithoutDate = new URLSearchParams()

  const allKeys = [
    SURGEONS_PARAM,
    SITES_KEY,
    ROOMS_PARAM,
    DATE_KEY,
    SHOW_CLOSED_ROOMS,
    SHOW_METRICS,
    SHOW_FILTERS,
    SORT_PARAM,
  ]

  if (searchParams) {
    allKeys.forEach((key) => {
      const value = searchParams.get(key)
      if (value) {
        allSearchParams.set(key, value)
      }

      if (value && key !== DATE_KEY) {
        allSearchParamsWithoutDate.set(key, value)
      }
    })
  }

  return {
    allSearchParams,
    allSearchParamsWithoutDate,
  }
}

export const useDateStateSearchParams = (): DateQueryParam => {
  const { timezone } = useTimezone()

  const [searchParams, setSearchParams] = useSearchParams()

  const selectedDateQueryString = getCleanedUrlParam<string | undefined>({
    searchParams,
    key: DATE_KEY,
    defaultValue: undefined,
  })

  const setSelectedDateQueryString = useCallback(
    (date?: string) => {
      if (date) {
        searchParams.set(DATE_KEY, JSON.stringify(date))
      } else {
        searchParams.delete(DATE_KEY)
      }

      searchParams.delete(SURGEONS_PARAM)

      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams]
  )

  const { minTime, maxTime } = useMemo(() => {
    const computedTime = selectedDateQueryString
      ? urlFriendlyDateToDateTime(selectedDateQueryString)
      : DateTime.now()
    return {
      minTime: computedTime
        .startOf('day')
        .setZone(timezone, { keepLocalTime: true })
        .toISO(),
      maxTime: computedTime
        .endOf('day')
        .setZone(timezone, { keepLocalTime: true })
        .toISO(),
    }
  }, [selectedDateQueryString, timezone])

  return [
    {
      minTime,
      maxTime,
    },
    setSelectedDateQueryString,
  ]
}

export const ScheduleFilterProvider = ({
  children,
  minTime: minTimeProp,
  maxTime: maxTimeProp,
  context,
}: {
  children: ReactNode
  minTime?: string
  maxTime?: string
  context: ScheduleFilterProviderContext
}) => {
  const { sites, isLoading: sitesLoading } = useSiteOptions()
  const rooms = useRoomOptions()
  const location = useLocation()
  const { timezone, updateTimezoneState } = useTimezone()
  const eventsLogger = useAnalyticsEventLogger()

  const [searchParams, setSearchParams] = useSearchParams()

  const userViewId = getCleanedUrlParam<string>({
    searchParams,
    key: USER_VIEW_ID,
    defaultValue: '',
  })

  const localStorageStatePrefix = `${userViewId}${context ?? ''}`

  const [showScheduledStore, setShowScheduled] = useLocalStorageState<boolean>(
    `${localStorageStatePrefix}_SHOW_SCHEDULED_PARAM`,
    true
  )

  const [showClosedStore, setShowClosed] = useLocalStorageState(
    `${localStorageStatePrefix}_SHOW_CLOSED_ROOMS`,
    true
  )

  const [showMetricsStore, setShowMetrics] = useLocalStorageState(
    `${localStorageStatePrefix}_SHOW_METRICS`,
    true
  )

  const [showFiltersStore, setShowFilters] = useLocalStorageState(
    `${localStorageStatePrefix}_SHOW_FILTERS`,
    true
  )

  const [siteIdsStore, setSiteIds] = useLocalStorageState<string[] | undefined>(
    `${localStorageStatePrefix}_SITE_IDS`,
    undefined
  )

  const [numColsStore, setNumColsStore] = useLocalStorageState<
    number | undefined
  >(`${localStorageStatePrefix}_NUM_COLS`, undefined)

  const showScheduledParam = getCleanedUrlParam<boolean | undefined>({
    searchParams,
    key: SHOW_SCHEDULED_PARAM,
    defaultValue: undefined,
  })

  const showClosedRoomsParams = getCleanedUrlParam<boolean | undefined>({
    searchParams,
    key: SHOW_CLOSED_ROOMS,
    defaultValue: undefined,
  })

  const showMetricsParam = getCleanedUrlParam<boolean | undefined>({
    searchParams,
    key: SHOW_METRICS,
    defaultValue: undefined,
  })

  const showFiltersParam = getCleanedUrlParam<boolean | undefined>({
    searchParams,
    key: SHOW_FILTERS,
    defaultValue: undefined,
  })

  const roomIds = getCleanedUrlParam<string[] | undefined>({
    searchParams,
    key: ROOMS_PARAM,
    defaultValue: undefined,
  })

  const selectedSurgeons = getCleanedUrlParam<string[] | undefined>({
    searchParams,
    key: SURGEONS_PARAM,
    defaultValue: undefined,
  })

  const siteIdsParams = getCleanedUrlParam<string[] | undefined>({
    searchParams,
    key: SITES_KEY,
    defaultValue: undefined,
  })
  const timeRange = getCleanedUrlParam<TimeRange | undefined>({
    searchParams,
    key: TIME_RANGE_PARAM,
    defaultValue: undefined,
  })

  const numColsParam = getCleanedUrlParam<number | undefined>({
    searchParams,
    key: NUM_COLS_PARAM,
    defaultValue: undefined,
  })

  const sortKeys = getCleanedUrlParam<LiveStatus[]>({
    searchParams,
    key: SORT_PARAM,
    defaultValue: [],
  })

  const viewRoomsState = getCleanedUrlParam<ViewRoomMetric | undefined>({
    searchParams,
    key: METRIC_PARAM,
    defaultValue: undefined,
  })

  const showScheduled =
    showScheduledParam !== undefined
      ? showScheduledParam
      : showScheduledStore || !!showScheduledRoutes[location.pathname]

  const showClosedRooms =
    showClosedRoomsParams !== undefined
      ? showClosedRoomsParams
      : showClosedStore

  const numCols = numColsParam !== undefined ? numColsParam : numColsStore

  const showMetrics =
    showMetricsParam !== undefined ? showMetricsParam : showMetricsStore

  const showFilters =
    showFiltersParam !== undefined ? showFiltersParam : showFiltersStore

  const siteIds = siteIdsParams !== undefined ? siteIdsParams : siteIdsStore

  const [
    { minTime: minTimeState, maxTime: maxTimeState },
    setSelectedDateQueryString,
  ] = useDateStateSearchParams()

  const minTime = minTimeProp ?? minTimeState
  const maxTime = maxTimeProp ?? maxTimeState

  useEffect(() => {
    let newSiteIds: string[] | undefined = siteIds
    if (
      sites.length &&
      (siteIds === undefined ||
        siteIds.some(
          (siteId) => sites.findIndex((s) => s.node.id === siteId) === -1
        ))
    ) {
      newSiteIds = [sites[0].node.id]
      searchParams.set(SITES_KEY, JSON.stringify(toIds(newSiteIds)))
      searchParams.delete(ROOMS_PARAM)
      setSearchParams(searchParams)
    } else if (
      sites.length &&
      !isEqual(siteIdsParams, siteIds) &&
      siteIds?.length &&
      siteIds.every((id) => sites.some((s) => s.node.id === id))
    ) {
      searchParams.set(SITES_KEY, JSON.stringify(siteIds))
      searchParams.delete(ROOMS_PARAM)
      setSearchParams(searchParams)
    }

    updateTimezoneState(
      newSiteIds
        ? sites.filter((s) => newSiteIds.includes(s.node.id)).map((s) => s.node)
        : undefined
    )
  }, [
    updateTimezoneState,
    sites,
    siteIds,
    minTime,
    maxTime,
    searchParams,
    setSearchParams,
    siteIdsParams,
  ])

  const defaultSiteIds = useMemo(() => {
    return sites.length ? [sites[0].node.id] : undefined
  }, [sites])

  const showResetFiltersButton = useMemo(() => {
    return (
      roomIds !== undefined ||
      selectedSurgeons !== undefined ||
      viewRoomsState !== undefined ||
      (timeRange !== undefined && timeRange !== TimeRange.DayBound) ||
      (!sitesLoading && !isEqual(siteIds, defaultSiteIds))
    )
  }, [
    roomIds,
    selectedSurgeons,
    viewRoomsState,
    sitesLoading,
    siteIds,
    defaultSiteIds,
    timeRange,
  ])

  const numFiltersApplied = useMemo(() => {
    let result = 0

    if (!!sites.length) result++
    if (!!roomIds?.length) result++
    if (!!selectedSurgeons?.length) result++
    if (showScheduled) result++

    return result
  }, [sites.length, roomIds?.length, selectedSurgeons?.length, showScheduled])

  const loggingState = useMemo<LoggingState>(
    () => ({
      minTime,
      maxTime,
      siteIds,
      roomIds,
      timeRange,
      showClosedRooms,
      showScheduled,
      sortKeys,
      showFilters,
      showMetrics,
      numCols,
      viewRoomsState,
      surgeonIds: selectedSurgeons,
      date: dateTimeToUrlFriendlyDate(DateTime.fromISO(minTime)),
    }),
    [
      minTime,
      maxTime,
      siteIds,
      roomIds,
      timeRange,
      showClosedRooms,
      showScheduled,
      sortKeys,
      showFilters,
      showMetrics,
      numCols,
      viewRoomsState,
      selectedSurgeons,
    ]
  )

  const newLoggingState = useCallback(
    (data?: LoggingState) => {
      return { ...loggingState, ...(data ?? {}) }
    },
    [loggingState]
  )

  const logEvents = useCallback(
    (key: keyof EventLog, data?: LoggingState) => {
      const event = eventMapper[context][key]
      if (event) {
        eventsLogger(event, newLoggingState(data))
      }
    },
    [context, eventsLogger, newLoggingState]
  )

  useEffect(() => {
    logEvents('onPageLoad')
    const loadedTime = DateTime.now()

    const onUnload = () => {
      const unloadedTime = DateTime.now()
      const secondsWatched = unloadedTime.diff(loadedTime).as('seconds')

      logEvents('onPageUnload', { secondsWatched })
    }

    // This event is fired when the browser page is refreshed or closed, the
    // unmount function below is not
    window.addEventListener('beforeunload', onUnload)

    // This will be run on unmount, which only happens when navigating to
    // another page
    return () => {
      window.removeEventListener('beforeunload', onUnload)
      onUnload()
    }

    // We only want this to run when the context changes, which is the equivalent on a page change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [context])

  const onChangeDate = useCallback(
    (date: Date) => {
      const minTime = DateTime.fromJSDate(date).setZone(timezone).startOf('day')
      const maxTime = DateTime.fromJSDate(date).setZone(timezone).endOf('day')

      const dateStatus = () => {
        const now = DateTime.now().setZone(timezone).day
        const dateSelected = minTime.day
        const offSet = dateSelected - now
        if (offSet < 0) {
          return { status: 'Past', offSet: offSet }
        } else if (offSet > 0) {
          return { status: 'Future', offSet: offSet }
        } else {
          return { status: 'Present', offSet: offSet }
        }
      }
      const getDateStatus = dateStatus()

      const dateUrlFriendly = dateTimeToUrlFriendlyDate(minTime)

      setSelectedDateQueryString(dateTimeToUrlFriendlyDate(minTime))

      logEvents('onChangeDate', {
        minTime: minTime.toISO(),
        maxTime: maxTime.toISO(),
        dateStatus: getDateStatus.status,
        viewingOffset: getDateStatus.offSet,
        surgeonIds: undefined,
        date: dateUrlFriendly,
      })
    },
    [timezone, setSelectedDateQueryString, logEvents]
  )

  const setShowScheduledValue = useCallback(
    (newShowScheduled: boolean) => {
      const event = newShowScheduled
        ? EVENTS.SCHEDULE_PAGE_ENABLE_SHOW_SCHEDULED
        : EVENTS.SCHEDULE_PAGE_DISABLE_SHOW_SCHEDULED
      searchParams.set(SHOW_SCHEDULED_PARAM, JSON.stringify(newShowScheduled))

      setSearchParams(searchParams)
      eventsLogger(event)

      logEvents('onToggleScheduled', { showScheduled: newShowScheduled })
    },
    [eventsLogger, logEvents, searchParams, setSearchParams]
  )

  const onToggleScheduled = useCallback(() => {
    setShowScheduledValue(!showScheduled)
  }, [setShowScheduledValue, showScheduled])

  useEffect(() => {
    if (
      showScheduledStore !== showScheduled &&
      showScheduledParam !== undefined
    ) {
      setShowScheduled(showScheduled)
    }
  }, [setShowScheduled, showScheduled, showScheduledStore, showScheduledParam])

  useEffect(() => {
    if (showMetricsStore !== showMetrics && showMetricsParam !== undefined) {
      setShowMetrics(showMetrics)
    }
  }, [setShowMetrics, showMetrics, showMetricsStore, showMetricsParam])

  useEffect(() => {
    if (showFiltersStore !== showFilters && showFiltersParam !== undefined) {
      setShowFilters(showFilters)
    }
  }, [showFiltersStore, showFilters, showFiltersParam, setShowFilters])

  useEffect(() => {
    if (!isEqual(siteIdsStore, siteIds) && siteIdsParams !== undefined) {
      setSiteIds(siteIds)
    }
  }, [siteIdsStore, siteIds, siteIdsParams, setSiteIds])

  useEffect(() => {
    if (
      showClosedStore !== showClosedRooms &&
      showClosedRoomsParams !== undefined
    ) {
      setShowClosed(showClosedRooms)
    }
  }, [setShowClosed, showClosedRooms, showClosedStore, showClosedRoomsParams])

  useEffect(() => {
    if (numColsStore !== numCols) {
      setNumColsStore(numCols)
    }
  }, [numCols, numColsParam, numColsStore, setNumColsStore])

  const onChangeNumCols = useCallback(
    (numCols?: number) => {
      if (numCols) {
        searchParams.set(NUM_COLS_PARAM, JSON.stringify(numCols))
      } else {
        searchParams.delete(NUM_COLS_PARAM)
        setNumColsStore(undefined)
      }
      setSearchParams(searchParams)

      logEvents('onChangeLayout', { layout: `${numCols}` })
    },
    [logEvents, searchParams, setSearchParams, setNumColsStore]
  )

  const onChangeSites = useCallback(
    (newSiteIds?: string[]) => {
      if (isEqual(newSiteIds, siteIds)) {
        return
      }

      if (newSiteIds) {
        searchParams.set(SITES_KEY, JSON.stringify(toIds(newSiteIds)))
      } else {
        searchParams.delete(SITES_KEY)
        setSiteIds(undefined)
      }

      searchParams.delete(SURGEONS_PARAM)
      setSearchParams(searchParams)

      logEvents('onChangeSites', {
        surgeonIds: undefined,
        siteIds: newSiteIds,
      })
    },
    [siteIds, searchParams, setSearchParams, logEvents, setSiteIds]
  )

  const onChangeRooms = useCallback(
    (roomIds?: string[]) => {
      if (roomIds) {
        searchParams.set(ROOMS_PARAM, JSON.stringify(toIds(roomIds)))
      } else {
        searchParams.delete(ROOMS_PARAM)
      }

      setSearchParams(searchParams)

      logEvents('onChangeRooms', { roomIds })
    },
    [logEvents, searchParams, setSearchParams]
  )

  const onChangeDailyMetric = useCallback(
    (viewRoomsState?: ViewRoomMetric) => {
      if (viewRoomsState) {
        searchParams.set(METRIC_PARAM, JSON.stringify(viewRoomsState))
      } else {
        searchParams.delete(METRIC_PARAM)
      }

      setSearchParams(searchParams)

      logEvents('onChangeDailyMetric', { viewRoomsState })
    },
    [logEvents, searchParams, setSearchParams]
  )

  const onChangeTimeRange = useCallback(
    (timeRange?: TimeRange) => {
      if (timeRange) {
        searchParams.set(TIME_RANGE_PARAM, JSON.stringify(timeRange))
      } else {
        searchParams.delete(TIME_RANGE_PARAM)
      }

      setSearchParams(searchParams)

      logEvents('onChangeTimeRange', { timeRange })
    },
    [logEvents, searchParams, setSearchParams]
  )

  const onChangeSurgeons = useCallback(
    (surgeonIds?: string[]) => {
      if (surgeonIds) {
        searchParams.set(SURGEONS_PARAM, JSON.stringify(toIds(surgeonIds)))
      } else {
        searchParams.delete(SURGEONS_PARAM)
      }

      setSearchParams(searchParams)

      logEvents('onChangeSurgeons', { surgeonIds })
    },
    [setSearchParams, searchParams, logEvents]
  )

  const onToggleBooleanParam = useCallback(
    (newValue: boolean, paramKey: string) => {
      searchParams.set(paramKey, JSON.stringify(!!newValue))
      setSearchParams(searchParams)
    },
    [searchParams, setSearchParams]
  )

  const onToggleShowClosedRooms = useCallback(
    (val: boolean) => {
      onToggleBooleanParam(val, SHOW_CLOSED_ROOMS)
      logEvents('onToggleShowClosedRooms', { showClosedRooms: val })
    },
    [logEvents, onToggleBooleanParam]
  )

  const onToggleShowMetrics = useCallback(
    (val: boolean) => {
      onToggleBooleanParam(val, SHOW_METRICS)
      logEvents('onToggleShowMetrics', { showMetrics: val })
    },
    [logEvents, onToggleBooleanParam]
  )

  const onToggleFilters = useCallback(
    (val: boolean) => {
      onToggleBooleanParam(val, SHOW_FILTERS)
      logEvents('onToggleFilters', { showFilters: val })
    },
    [logEvents, onToggleBooleanParam]
  )

  const onToggleSortKey = useCallback(
    (sortKey: LiveStatus) => {
      const keys = sortKeys ?? []
      const currentPriority = keys.indexOf(sortKey)
      if (currentPriority !== -1) {
        keys.splice(currentPriority, 1)
      } else {
        keys.push(sortKey)
      }
      if (keys.length > 2) {
        keys.slice(0, 2)
      }

      if (keys.length) {
        searchParams.set(SORT_PARAM, JSON.stringify(keys))
      } else {
        searchParams.delete(SORT_PARAM)
      }

      setSearchParams(searchParams)
      logEvents('onToggleSortKey', { sortKeys: keys })
    },
    [logEvents, searchParams, setSearchParams, sortKeys]
  )

  const resetFilters = useCallback(() => {
    onChangeSites(defaultSiteIds)
    onChangeRooms(undefined)
    setShowScheduled && setShowScheduled(true)
    onChangeDailyMetric(undefined)
    onChangeSurgeons(undefined)
    onChangeTimeRange(undefined)
  }, [
    onChangeSites,
    defaultSiteIds,
    onChangeRooms,
    setShowScheduled,
    onChangeDailyMetric,
    onChangeSurgeons,
    onChangeTimeRange,
  ])

  return (
    <ScheduleFilterContext.Provider
      value={{
        sites,
        rooms,
        selectedSurgeons,
        onChangeSurgeons,
        roomIds,
        minTime,
        maxTime,
        siteIds,
        showResetFiltersButton,
        showScheduled,
        numFiltersApplied,
        onChangeDate,
        onChangeSites,
        onChangeRooms,
        onToggleScheduled,
        onToggleShowClosedRooms,
        onToggleShowMetrics,
        viewRoomsState,
        onChangeDailyMetric,
        resetFilters,
        showClosedRooms,
        showMetrics,
        onToggleSortKey,
        sortKeys,
        onChangeTimeRange,
        timeRange: timeRange ?? TimeRange.DayBound,
        onToggleFilters,
        showFilters,
        onChangeNumCols,
        numCols,
      }}
    >
      {children}
    </ScheduleFilterContext.Provider>
  )
}
