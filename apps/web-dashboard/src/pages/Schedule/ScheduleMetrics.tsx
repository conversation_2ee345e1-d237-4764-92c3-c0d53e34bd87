import { useCallback, useMemo } from 'react'

import { DateTime } from 'luxon'

import { MetricHeader } from 'src/components/MetricHeader'
import { useTimezone } from 'src/Contexts'
import { useScheduleMetrics } from 'src/modules/daily-metrics/hooks/useScheduleMetrics'

import { useCurrentMinute, useIsToday } from '../../utils/useCurrentTime'
import { useScheduleFilterContext } from './ScheduleFilterContext'
import { ViewRoomMetric } from './types'

export const ScheduleMetrics = ({
  scheduleMetrics,
}: {
  scheduleMetrics: Partial<ReturnType<typeof useScheduleMetrics>>
}) => {
  const {
    viewRoomsState,
    onChangeDailyMetric: setViewRoomsState,
    minTime,
    showMetrics,
  } = useScheduleFilterContext()

  const isToday = useIsToday(DateTime.fromISO(minTime))
  const { timezone } = useTimezone()
  const currentMinute = useCurrentMinute()

  const CURRENTLY_VIEWING = 'Currently Viewing'
  const VIEW_ROOMS = 'View Rooms'

  const onClick = useCallback(
    (metric: ViewRoomMetric) => {
      return () => {
        setViewRoomsState(metric)
      }
    },
    [setViewRoomsState]
  )

  const determineLabel = useCallback(
    (metric: ViewRoomMetric) => {
      if (viewRoomsState === metric) {
        return CURRENTLY_VIEWING
      }

      return metric && scheduleMetrics[metric]?.length ? VIEW_ROOMS : ''
    },
    [viewRoomsState, scheduleMetrics]
  )

  const metrics = useMemo(
    () => [
      {
        label: 'Open rooms',
        value: scheduleMetrics.openToday?.length,
        subMetric: {
          label: 'Cases Completed',
          value: `${scheduleMetrics.completedCases ?? '--'} / ${scheduleMetrics.totalCases ?? '--'}`,
        },
      },
      {
        label: 'First case starts',
        value: scheduleMetrics.firstCaseStarts?.length,
        subLink: onClick
          ? {
              label: determineLabel('firstCaseStarts'),
              onClick: onClick('firstCaseStarts'),
              disabled: viewRoomsState === 'firstCaseStarts',
            }
          : undefined,
      },
      {
        label: 'Rooms running',
        value: scheduleMetrics.stillOpen?.length,
        subLink: onClick
          ? {
              label: determineLabel('stillOpen'),
              onClick: onClick('stillOpen'),
              disabled: viewRoomsState === 'stillOpen',
            }
          : undefined,
      },
      {
        label: 'Open past 15:00',
        value: scheduleMetrics.openPast15?.length,
        subLink: onClick
          ? {
              label: determineLabel('openPast15'),
              onClick: onClick('openPast15'),
              disabled: viewRoomsState === 'openPast15',
            }
          : undefined,
      },
      {
        label: 'Open past 17:00',
        value: scheduleMetrics.openPast17?.length,
        subLink: onClick
          ? {
              label: determineLabel('openPast17'),
              onClick: onClick('openPast17'),
              disabled: viewRoomsState === 'openPast17',
            }
          : undefined,
      },
      {
        label: 'Open past 19:00',
        value: scheduleMetrics.openPast19?.length,
        subLink: onClick
          ? {
              label: determineLabel('openPast19'),
              onClick: onClick('openPast19'),
              disabled: viewRoomsState === 'openPast19',
            }
          : undefined,
      },
    ],
    [
      determineLabel,
      onClick,
      scheduleMetrics.completedCases,
      scheduleMetrics.firstCaseStarts?.length,
      scheduleMetrics.openPast15,
      scheduleMetrics.openPast17?.length,
      scheduleMetrics.openPast19?.length,
      scheduleMetrics.openToday?.length,
      scheduleMetrics.stillOpen?.length,
      scheduleMetrics.totalCases,
      viewRoomsState,
    ]
  )

  if (!showMetrics) {
    return null
  }

  return (
    <MetricHeader
      metrics={metrics.filter((sm) => {
        const hideRoomsRunning = !isToday && sm.label === 'Rooms running'
        const hideFirstCaseStarts =
          DateTime.fromISO(minTime).setZone(timezone).startOf('day') <=
            currentMinute.setZone(timezone).startOf('day') &&
          sm.label === 'First case starts'

        return hideRoomsRunning || hideFirstCaseStarts ? false : true
      })}
    />
  )
}
