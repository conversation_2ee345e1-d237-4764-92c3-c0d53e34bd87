import { ThemeProvider } from '@emotion/react'

import { fireEvent, render, screen } from '@testing-library/react'

import { theme } from '@apella/component-library'
import {
  aRoom,
  aRoomEdge,
  aSite,
  aSiteEdge,
} from 'src/__generated__/generated-mocks'
import { useScheduleFilterContext } from 'src/pages/Schedule/ScheduleFilterContext'

import { PRIMARY_SURGEON_ROLES } from '../../utils/roles'
import { ScheduleFiltersBlade } from './ScheduleFiltersBlade'
import { TimeRange } from './types'

vi.mock('src/pages/Schedule/ScheduleFilterContext')
const mockedUseScheduleFilterContext = vi.mocked(useScheduleFilterContext)

describe('ScheduleFiltersBlade', () => {
  const siteOptions = [
    aSiteEdge({ node: aSite({ id: 'site1', name: 'Site 1' }) }),
    aSiteEdge({ node: aSite({ id: 'site2', name: 'Site 2' }) }),
  ]

  const roomOptions = [
    aRoomEdge({
      node: aRoom({ id: 'room1', name: 'Room 1', site: siteOptions[0].node }),
    }),
    aRoomEdge({
      node: aRoom({ id: 'room2', name: 'Room 2', site: siteOptions[0].node }),
    }),
    aRoomEdge({
      node: aRoom({ id: 'room3', name: 'Room 3', site: siteOptions[1].node }),
    }),
  ]

  const onChangeSurgeons = vi.fn()
  const onBladeClose = vi.fn()
  const onChangeSiteId = vi.fn()
  const onChangeRoomIds = vi.fn()

  const onToggleScheduled = vi.fn()
  const onResetFilters = vi.fn()
  const onChangeDate = vi.fn()

  const surgeonOptions = [
    {
      firstName: 'f',
      lastName: 'l',
      displayName: 'fl',
      id: '1',
      role: PRIMARY_SURGEON_ROLES[0],
    },
    {
      firstName: 'f2',
      lastName: 'l2',
      displayName: 'fl2',
      id: '2',
      role: PRIMARY_SURGEON_ROLES[0],
    },
  ]

  const selectedSite = siteOptions[0].node
  const selectedRoom = roomOptions[0].node
  const selectedSurgeon = surgeonOptions[0]

  beforeAll(() => {
    mockedUseScheduleFilterContext.mockReturnValue({
      sites: siteOptions,
      rooms: roomOptions,
      selectedSurgeons: [surgeonOptions[0].id],
      onChangeSurgeons: onChangeSurgeons,
      maxTime: '',
      minTime: '',
      siteIds: [selectedSite.id],
      roomIds: [selectedRoom.id],
      showScheduled: true,
      onChangeDate,
      onChangeSites: onChangeSiteId,
      onChangeRooms: onChangeRoomIds,
      onToggleScheduled,
      resetFilters: onResetFilters,
      onChangeDailyMetric: () => {},
      onToggleShowClosedRooms: () => {},
      onToggleShowMetrics: () => {},
      showClosedRooms: true,
      showMetrics: true,
      sortKeys: [],
      onToggleSortKey: () => {},
      onToggleFilters: () => {},
      timeRange: TimeRange.DayBound,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  const renderBlade = () => {
    render(
      <ThemeProvider theme={theme}>
        <ScheduleFiltersBlade
          isBladeOpen={true}
          onBladeClose={onBladeClose}
          surgeonOptions={surgeonOptions}
        />
      </ThemeProvider>
    )
  }

  const clickOn = (element: HTMLElement) =>
    fireEvent(element, new MouseEvent('click', { bubbles: true }))

  it('displays applied filters', () => {
    renderBlade()

    expect(screen.getByText(selectedSite.name)).toBeInTheDocument()
    expect(screen.getByText(selectedRoom.name)).toBeInTheDocument()
    expect(screen.getByText(selectedSurgeon.displayName)).toBeInTheDocument()
    expect(screen.getByTestId('checkbox-checked')).toBeInTheDocument()
  })

  it('allows you to choose sites', () => {
    renderBlade()

    clickOn(screen.getByText(selectedSite.name))
    clickOn(screen.getByText(siteOptions[1].node.name))
    clickOn(screen.getByText('Filters'))

    expect(screen.getByText('2 selected')).toBeInTheDocument()

    clickOn(screen.getByText('Apply'))

    expect(onBladeClose).toHaveBeenCalledTimes(1)
    expect(onChangeSiteId).toHaveBeenCalledTimes(1)
    expect(onChangeSurgeons).not.toHaveBeenCalled()
    expect(onToggleScheduled).not.toHaveBeenCalled()
  })

  it('allows you to choose a room', () => {
    renderBlade()

    clickOn(screen.getByText(selectedRoom.name))
    clickOn(screen.getByText(roomOptions[1].node.name))
    clickOn(screen.getByText('Filters'))

    expect(screen.getByText('2 rooms selected')).toBeInTheDocument()

    clickOn(screen.getByText('Apply'))

    expect(onBladeClose).toHaveBeenCalledTimes(1)
    expect(onChangeSiteId).not.toHaveBeenCalled()
    expect(onChangeRoomIds).toHaveBeenCalledTimes(1)
    expect(onChangeSurgeons).not.toHaveBeenCalled()
    expect(onToggleScheduled).not.toHaveBeenCalled()
  })

  it('allows you to choose a surgeon', () => {
    renderBlade()

    clickOn(screen.getByText(selectedSurgeon.displayName))
    clickOn(
      screen.getByText(
        `${surgeonOptions[0].lastName}, ${surgeonOptions[0].firstName}`
      )
    )
    clickOn(
      screen.getByText(
        `${surgeonOptions[1].lastName}, ${surgeonOptions[1].firstName}`
      )
    )
    clickOn(screen.getByText('Filters'))

    expect(screen.getByText(surgeonOptions[1].displayName)).toBeInTheDocument()

    clickOn(screen.getByText('Apply'))

    expect(onBladeClose).toHaveBeenCalledTimes(1)
    expect(onChangeSiteId).not.toHaveBeenCalled()
    expect(onChangeRoomIds).not.toHaveBeenCalled()
    expect(onChangeSurgeons).toHaveBeenCalledTimes(1)
    expect(onToggleScheduled).not.toHaveBeenCalled()
  })

  it('allows you to toggle scheduled', () => {
    renderBlade()

    clickOn(screen.getByText('Scheduled'))

    expect(screen.getByTestId('checkbox-unchecked')).toBeInTheDocument()

    clickOn(screen.getByText('Apply'))

    expect(onBladeClose).toHaveBeenCalledTimes(1)
    expect(onChangeSiteId).not.toHaveBeenCalled()
    expect(onChangeRoomIds).not.toHaveBeenCalled()
    expect(onChangeSurgeons).not.toHaveBeenCalled()
    expect(onToggleScheduled).toHaveBeenCalledTimes(1)
  })

  it('allows you to clear filters', () => {
    renderBlade()

    expect(screen.getByText('Reset')).toBeInTheDocument()
    clickOn(screen.getByText('Reset'))

    expect(onResetFilters).toHaveBeenCalledTimes(1)
    expect(onBladeClose).toHaveBeenCalledTimes(1)
    expect(onChangeSiteId).not.toHaveBeenCalled()
    expect(onChangeRoomIds).not.toHaveBeenCalled()
    expect(onChangeSurgeons).not.toHaveBeenCalled()
    expect(onToggleScheduled).not.toHaveBeenCalled()
  })
})
