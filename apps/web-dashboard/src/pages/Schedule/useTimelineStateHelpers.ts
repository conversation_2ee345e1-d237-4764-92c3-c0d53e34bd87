import { chain, sortBy } from 'lodash'
import { DateTime } from 'luxon'

import {
  CaseMatchingStatus,
  RoomStatusName,
  TurnoverLabel,
} from '../../__generated__/globalTypes'
import { CaseFlag, CaseFlagType } from '../../modules/planning/types'
import { getConflictingLastNames } from '../../utils/getFullName'
import { dtOrUndefined } from '../../utils/graphqlParsingHelpers'
import { toStaffFullName } from '../../utils/roles'
import { calculateTurnoverLengthStatus } from '../../utils/turnovers'
import { ApellaCase, ApellaSite, EventNotification } from '../types'
import { GetTimelineCaseData } from './__generated__'
import { TimelineRoom } from './types'

type RoomGQLType =
  GetTimelineCaseData['sites']['edges'][number]['node']['rooms']['edges'][number]['node']

type TurnoverGoalsGQLType =
  GetTimelineCaseData['sites']['edges'][number]['node']['turnoverGoals']

export const parseRoomStatus = (
  room: RoomGQLType,
  turnoverGoals: TurnoverGoalsGQLType
) => {
  return {
    name: RoomStatusName[room.status.name],
    inProgressApellaCase: room.status.inProgressApellaCase
      ? {
          ...room.status.inProgressApellaCase,
          startTime: DateTime.fromISO(
            room.status.inProgressApellaCase.startTime
          ),
          endTime: dtOrUndefined(room.status.inProgressApellaCase.endTime),
        }
      : undefined,
    inProgressTurnover: room.status.inProgressTurnover
      ? {
          ...room.status.inProgressTurnover,
          startTime: DateTime.fromISO(room.status.inProgressTurnover.startTime),
          endTime: DateTime.fromISO(room.status.inProgressTurnover.endTime),
          currentLengthStatus: calculateTurnoverLengthStatus(
            DateTime.fromISO(room.status.inProgressTurnover.startTime),
            DateTime.now(),
            turnoverGoals
          ),
          overallLengthStatus: calculateTurnoverLengthStatus(
            DateTime.fromISO(room.status.inProgressTurnover.startTime),
            DateTime.fromISO(room.status.inProgressTurnover.endTime),
            turnoverGoals
          ),
        }
      : undefined,
    since: DateTime.fromISO(room.status.since),
  }
}

export const parseCases = (
  room: RoomGQLType,
  siteObj: ApellaSite,
  timezone: string,
  conflictingLastNames: Set<string>,
  caseMatchingStatuses?: Set<CaseMatchingStatus>
): ApellaCase[] => {
  const cases = room.apellaCases.edges.map<ApellaCase>(({ node: ac }) => {
    const roomObj = {
      id: room.id,
      name: room.name,
    }

    const startTime = DateTime.fromISO(ac.startTime).setZone(timezone)
    let endTime
    if (ac.endTime) {
      endTime = DateTime.fromISO(ac.endTime).setZone(timezone)
    }
    const actualPhase = ac.actual
      ? {
          id: ac.actual.id,
          room: {
            id: ac.actual.room.id,
            name: ac.actual.room.name,
          },
          startTime: DateTime.fromISO(ac.actual.startTime).setZone(timezone),
          endTime: ac.actual.endTime
            ? DateTime.fromISO(ac.actual.endTime).setZone(timezone)
            : undefined,
        }
      : undefined

    const staff = ac.case
      ? ac.case.caseStaff.map((cs) => ({
          firstName: cs.staff.firstName,
          lastName: cs.staff.lastName,
          displayName: conflictingLastNames.has(cs.staff.lastName)
            ? toStaffFullName({
                staff: cs.staff,
                displayLastNameFirst: true,
              })
            : cs.staff.lastName,
          id: cs.staff.id,
          role: cs.role ?? '',
        }))
      : []

    const staffPlan = ac.case?.caseStaffPlan
      ? ac.case.caseStaffPlan.edges
          .map((csp) =>
            csp.node.staff
              ? {
                  firstName: csp.node.staff.firstName,
                  lastName: csp.node.staff.lastName,
                  displayName: conflictingLastNames.has(csp.node.staff.lastName)
                    ? toStaffFullName({
                        staff: csp.node.staff,
                        displayLastNameFirst: true,
                      })
                    : csp.node.staff.lastName,
                  id: csp.node.staff.id,
                  rowId: csp.node.id,
                  role: csp.node.role ?? '',
                }
              : undefined
          )
          .filter(Boolean)
      : []

    return {
      id: ac.id,
      status: {
        name: ac.status.name,
        since: ac.status.since ? DateTime.fromISO(ac.status.since) : undefined,
      },
      case:
        ac.case && ac.case.id
          ? {
              id: ac.case.id,
              room: roomObj,
              scheduledStartTime: DateTime.fromISO(
                ac.case.scheduledStartTime
              ).setZone(timezone),
              scheduledEndTime: DateTime.fromISO(
                ac.case.scheduledEndTime
              ).setZone(timezone),
              staff,
              staffPlan,
              procedures: ac.case.primaryCaseProcedures
                .map((e) => ({
                  name: e.procedure.name,
                }))
                .filter(Boolean),
              isAddOn: ac.case.isAddOn ?? undefined,
              isInFlipRoom: ac.case.isInFlipRoom ?? undefined,
              isFirstCase: ac.case.isFirstCase ?? undefined,
              patientClass: ac.case.patientClass ?? undefined,
              precedingCaseId: ac.case.precedingCase?.id,
              caseClassificationType:
                ac.case.caseClassificationType ?? undefined,
              serviceLine: ac.case.serviceLine
                ? {
                    id: ac.case.serviceLine.id,
                    name: ac.case.serviceLine.name ?? undefined,
                  }
                : undefined,
              externalCaseId: ac.case.externalCaseId,
              caseMatchingStatus: ac.case.caseMatchingStatus,
              caseFlags:
                ac.case.caseFlags?.map<CaseFlag>((cf) => {
                  return {
                    id: cf.id,
                    type: cf.flagType as CaseFlagType,
                    active: !cf.archivedTime,
                  }
                }) || [],
              caseLabels: sortBy(ac.case.caseLabels ?? [], (cl) => cl.fieldId),
              patient: !!ac.case?.patient
                ? {
                    id: ac.case.patient.id,
                    administrativeSex:
                      ac.case.patient.personalInfo?.administrativeSex?.text ||
                      '',
                    abbreviatedName: ac.case.patient.personalInfo
                      ? `${ac.case.patient.personalInfo.lastNameAbbreviated}. ${ac.case.patient.personalInfo.firstNameAbbreviated}.`
                      : '',
                    age: ac.case.patient.personalInfo?.age ?? undefined,
                  }
                : undefined,
              eventNotifications: ac.case.eventNotifications
                ?.filter((en) => en.sentTime)
                .map<EventNotification | undefined>((en) => {
                  if (!en.event && !en.observation) {
                    return undefined
                  }
                  return {
                    sentTime: DateTime.fromISO(en.sentTime!),
                    event: en.event
                      ? {
                          name: en.event.attrs?.name ?? en.event.name,
                          color: en.event.color,
                          id: en.event.id,
                        }
                      : undefined,
                    observation: en.observation
                      ? {
                          id: en.observation.id,
                          name: en.observation.observationType.name,
                          color: en.observation.observationType.color ?? null,
                        }
                      : undefined,
                    notificationEvent: {
                      name:
                        en.event?.attrs?.name ??
                        en.event?.name ??
                        en.observation?.observationType.name ??
                        '',
                      color:
                        en.event?.color ??
                        en.observation?.observationType.color ??
                        null,
                      id: en.event?.id ?? en.observation?.id ?? '',
                    },
                    staffEventContactInformation: {
                      contactInformation: {
                        isApellaEmployee:
                          en.staffEventContactInformation.contactInformation
                            .isApellaEmployee ?? false,
                        name: `${en.staffEventContactInformation.contactInformation.firstName} ${en.staffEventContactInformation.contactInformation.lastName}`,
                      },
                    },
                  }
                })
                .filter(Boolean),
              notePlan: ac.case.notePlan ?? undefined,
            }
          : undefined,
      site: siteObj,
      room: roomObj,
      startTime: startTime,
      endTime: endTime,
      type: ac.type,
      actualPhase: actualPhase,
    }
  })

  return chain(cases)
    .filter((ac) => includeCase(ac, caseMatchingStatuses))
    .sortBy((ac) => ac.startTime.toMillis())
    .value()
}

export const parseTurnovers = (
  room: RoomGQLType,
  timezone: string,
  turnoverGoals: TurnoverGoalsGQLType
) => {
  const turnovers =
    room.turnovers?.map((t) => {
      const startTime = DateTime.fromISO(t.startTime).setZone(timezone)
      const endTime = DateTime.fromISO(t.endTime).setZone(timezone)

      const currentLengthStatus = calculateTurnoverLengthStatus(
        startTime,
        DateTime.now(),
        turnoverGoals
      )
      const overallLengthStatus = calculateTurnoverLengthStatus(
        startTime,
        endTime,
        turnoverGoals
      )

      return {
        id: t.id,
        roomId: room.id,
        startTime,
        endTime,
        type: t.type,
        duration: endTime.diff(startTime),
        followingCaseId: t.followingCase.id,
        precedingCaseId: t.precedingCase.id,
        scheduledEndTime: dtOrUndefined(
          t.followingCase.case?.scheduledStartTime
        ),
        goals: turnoverGoals,
        overallLengthStatus,
        currentLengthStatus,
        labels: t.labels as TurnoverLabel[] | null,
        note: t.note,
      }
    }) ?? []

  return sortBy(turnovers, (t) => t.startTime.toMillis())
}

type PrimeTimeConfig =
  GetTimelineCaseData['sites']['edges'][0]['node']['rooms']['edges'][0]['node']['primeTimeConfig']

type DayOfWeek =
  | 'sunday'
  | 'monday'
  | 'tuesday'
  | 'wednesday'
  | 'thursday'
  | 'friday'
  | 'saturday'

export const parsePrimeTimeConfig = (
  selectedTime: string,
  timezone: string,
  primeTimeConfig: PrimeTimeConfig
): TimelineRoom['primeTime'] => {
  const localSelectedTime = DateTime.fromISO(selectedTime).setZone(timezone)
  const selectedWeekday =
    localSelectedTime.weekdayLong.toLowerCase() as DayOfWeek

  return primeTimeConfig[selectedWeekday]
}

export const computeConflictingLastNames = (casesData: GetTimelineCaseData) => {
  const rawCasesFlattened = casesData?.sites.edges.flatMap(({ node: site }) =>
    site.rooms.edges.flatMap((r) => r.node.apellaCases.edges.map((e) => e.node))
  )

  const allCaseStaff = rawCasesFlattened
    .flatMap((c) => c.case?.caseStaff.map((cs) => cs.staff))
    .filter(Boolean)
  const allCaseStaffPlan = rawCasesFlattened
    .flatMap((c) => c.case?.caseStaffPlan?.edges.map((csp) => csp.node.staff))
    .filter(Boolean)

  const allStaff = allCaseStaff.concat(allCaseStaffPlan)
  return getConflictingLastNames(allStaff)
}

const includeCase = (
  apellaCase: ApellaCase,
  caseMatchingStatuses?: Set<CaseMatchingStatus>
) => {
  return (
    !caseMatchingStatuses ||
    !apellaCase.case ||
    !!(
      apellaCase.case.caseMatchingStatus &&
      caseMatchingStatuses.has(apellaCase.case.caseMatchingStatus)
    )
  )
}
