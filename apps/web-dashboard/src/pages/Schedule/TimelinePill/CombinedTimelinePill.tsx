import { MouseEvent, useCallback, useEffect, useMemo, useState } from 'react'
import { usePageVisibility } from 'react-page-visibility'

import { DateTime } from 'luxon'

import { TooltipProps } from '@apella/component-library'

import { CaseType } from '../../../__generated__/globalTypes'
import { useTimezone } from '../../../Contexts'
import { PillPositionAndSize } from '../../../utils/timelineHelpers'
import { SLOW_POLL_INTERVAL_MS } from '../../Live/consts'
import { ApellaCase, Turnover } from '../../types'
import { MouseMovementHandler } from '../types'
import {
  CaseTimelinePill,
  LiveTimelinePill,
  PhaseTimelinePill,
} from './TimelinePill'
import { useTimelinePillState } from './TimelinePillState'
import { CaseTooltipBody } from './TooltipBody'
import {
  CaseTooltipDisplayText,
  PhaseTooltipDisplayText,
} from './TooltipDisplayText'

export const CombinedTimelinePill = ({
  apellaCase,
  calculatePillPositionAndSize,
  showScheduled,
  onClick: onClickProp,
  hoveredCaseIds,
  minTime,
  maxTime,
  onMouseEnter,
  onMouseLeave,
  ...rest
}: {
  apellaCase: ApellaCase
  isHovered: boolean
  onClick?: (
    mouseEvent: MouseEvent,
    apellaCase?: ApellaCase,
    turnover?: Turnover
  ) => void
  onMouseEnter: MouseMovementHandler
  onMouseLeave: MouseMovementHandler
  tooltipPlacement?: TooltipProps['placement']
  calculatePillPositionAndSize: (
    startTime: DateTime,
    endTime?: DateTime,
    minWidth?: number,
    nowTime?: DateTime
  ) => PillPositionAndSize
  showScheduled: boolean
  hoveredCaseIds: string[]
  minTime: DateTime
  maxTime: DateTime
}) => {
  const pageVisible = usePageVisibility()
  const { timezone } = useTimezone()

  // These functions are curried to create the handler for a given id
  const mouseEnterHandler = useCallback(
    (id: string) => () => onMouseEnter(id),
    [onMouseEnter]
  )
  const mouseLeaveHandler = useCallback(
    (id: string) => () => onMouseLeave(id),
    [onMouseLeave]
  )

  const onClick = (mouseEvent: MouseEvent) => {
    if (onClickProp) {
      onClickProp(mouseEvent, apellaCase)
    }
  }

  const TimelinePillComp =
    apellaCase.type === CaseType.LIVE ? LiveTimelinePill : PhaseTimelinePill

  const [phasePositionAndSize, setPhasePositionAndSize] =
    useState<PillPositionAndSize>()
  const [casePositionAndSize, setCasePositionAndSize] =
    useState<PillPositionAndSize>()
  useEffect(() => {
    const calcPositionAndSize = () => {
      let pillPositionAndSize, casePositionAndSize

      const hidePhasePill =
        apellaCase.startTime > maxTime ||
        (apellaCase.endTime && apellaCase.endTime < minTime)

      if (apellaCase.type === CaseType.LIVE && !hidePhasePill) {
        const forecastEndTimeMillis = apellaCase.endTime?.toMillis()
        pillPositionAndSize = calculatePillPositionAndSize(
          DateTime.fromMillis(apellaCase.startTime.toMillis()),
          forecastEndTimeMillis
            ? DateTime.fromMillis(forecastEndTimeMillis).setZone(timezone)
            : undefined,
          0,
          DateTime.now()
        )
      }

      if (apellaCase.type !== CaseType.LIVE && !hidePhasePill) {
        pillPositionAndSize = calculatePillPositionAndSize(
          apellaCase.startTime,
          apellaCase.endTime
        )
      }

      const hideCasePill =
        !apellaCase.case ||
        apellaCase.case.scheduledStartTime > maxTime ||
        apellaCase.case.scheduledEndTime < minTime

      if (apellaCase.case && !hideCasePill) {
        casePositionAndSize = calculatePillPositionAndSize(
          apellaCase.case.scheduledStartTime,
          apellaCase.case.scheduledEndTime
        )
      }

      setPhasePositionAndSize(pillPositionAndSize)
      setCasePositionAndSize(casePositionAndSize)
    }
    calcPositionAndSize()

    const intervalId =
      pageVisible &&
      apellaCase.type === CaseType.LIVE &&
      setInterval(calcPositionAndSize, SLOW_POLL_INTERVAL_MS)
    return () => {
      intervalId && clearInterval(intervalId)
    }
  }, [
    calculatePillPositionAndSize,
    apellaCase.case,
    apellaCase,
    timezone,
    pageVisible,
    maxTime,
    minTime,
  ])

  const blockHovered = useMemo(() => {
    if (!apellaCase.case) {
      return false
    }
    return hoveredCaseIds.includes(apellaCase.case.id)
  }, [apellaCase, hoveredCaseIds])

  const phasePillState = useTimelinePillState({
    type: apellaCase.type,
    isHovered: rest.isHovered || blockHovered,
    pillPositionAndSize: phasePositionAndSize,
    pillSizeType: showScheduled ? 'lg' : 'full',
    status: apellaCase.status.name,
  })
  const casePillState = useTimelinePillState({
    type: apellaCase.type,
    isHovered: rest.isHovered || blockHovered,
    pillPositionAndSize: casePositionAndSize,
    pillSizeType: 'sm',
    status: apellaCase.status.name,
    isCase: true,
  })

  return (
    <>
      {phasePositionAndSize && (
        <TimelinePillComp
          pillState={phasePillState}
          tooltipBody={<CaseTooltipBody apellaCase={apellaCase} />}
          displayText={
            <PhaseTooltipDisplayText
              phase={apellaCase}
              caseObj={apellaCase.case}
            />
          }
          onClick={onClickProp ? onClick : undefined}
          onMouseEnter={mouseEnterHandler(apellaCase.id)}
          onMouseLeave={mouseLeaveHandler(apellaCase.id)}
        />
      )}
      {showScheduled && !!apellaCase.case && casePositionAndSize && (
        <CaseTimelinePill
          pillState={casePillState}
          tooltipBody={<CaseTooltipBody apellaCase={apellaCase} />}
          displayText={
            <CaseTooltipDisplayText
              phase={apellaCase}
              caseObj={apellaCase.case}
            />
          }
          onClick={onClickProp ? onClick : undefined}
          onMouseEnter={mouseEnterHandler(apellaCase.case.id)}
          onMouseLeave={mouseLeaveHandler(apellaCase.case.id)}
        />
      )}
    </>
  )
}
