import { ApellaDateTimeFormats } from '@apella/component-library'

import { useTimezone } from '../../../Contexts'
import { Case } from '../../types'

export const useNotificationInformationForLatestEvent = (caseObj?: Case) => {
  const { timezone } = useTimezone()
  const eventNotificationInfo = caseObj?.eventNotifications?.sort((a, b) => {
    if (a.sentTime && b.sentTime) {
      return a.sentTime.toMillis() - b.sentTime.toMillis()
    }
    return 0
  })

  if (!eventNotificationInfo) {
    return undefined
  }

  const latestEvent = eventNotificationInfo[eventNotificationInfo.length - 1]
  if (latestEvent && latestEvent.sentTime) {
    const eventName = latestEvent.notificationEvent.name
    const notificationSentTime = latestEvent.sentTime
      .setZone(timezone)
      .toLocaleString(ApellaDateTimeFormats.TIME)
    const eventColor = latestEvent.notificationEvent.color || ''

    const notificationRecipients = new Set(
      eventNotificationInfo
        .filter(
          (e) => e.notificationEvent.id === latestEvent.notificationEvent.id
        )
        .map(
          (recipients) =>
            recipients.staffEventContactInformation.contactInformation.name
        )
    )

    return {
      eventName,
      notificationSentTime,
      eventColor,
      notificationRecipients,
    }
  }
}
