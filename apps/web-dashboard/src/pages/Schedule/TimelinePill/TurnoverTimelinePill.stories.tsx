import { StoryObj } from '@storybook/react'

import { LiveTimelinePill } from './TimelinePill'
import { useTurnoverPillState } from './TimelinePillState'
import { TurnoverTooltipBody } from './TooltipBody'
import { ActiveTurnoverDisplayText } from './TooltipDisplayText'
import { TurnoverPill } from './TurnoverPill'
import { getPillStateData } from './TurnoverPillMockData'

export default {
  title: 'Components/TurnoverTimelinePills',
  component: TurnoverPill,
}

type Story = StoryObj<typeof TurnoverPill>

const pillStateData = getPillStateData(true)
export const TimelinePillState: Story = {
  render: () => (
    <>
      {pillStateData.map((data, i) => (
        <div key={i} style={{ marginLeft: '15px', marginTop: '10px' }}>
          <h4
            style={{
              marginBottom: '40px',
              marginTop: '10px',
              paddingTop: '10px',
            }}
          >
            {data.title}
          </h4>
          <div style={{ ...data.style, left: '10px' } as React.CSSProperties}>
            <LiveTimelinePill
              pillState={useTurnoverPillState({
                pillPositionAndSize: data.calculatePillPositionAndSize(),
                pillSizeType: 'lg',
                turnoverOverallLength: data.turnover.overallLengthStatus,
                turnoverCurrentLength: data.turnover.currentLengthStatus,
                turnoverType: data.type,
                isActive: true,
              })}
              displayText={
                <ActiveTurnoverDisplayText turnover={data.turnover} />
              }
              tooltipBody={<TurnoverTooltipBody turnover={data.turnover} />}
              onClick={() => {}}
            />
          </div>
        </div>
      ))}
    </>
  ),
}
