import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { useFlags } from 'launchdarkly-react-client-sdk'

import { PillPositionAndSize } from 'src/utils/timelineHelpers'

import {
  CaseStatusName,
  CaseType,
  TurnoverType,
} from '../../../__generated__/globalTypes'
import { WebDashboardFeatureFlagSet } from '../../../modules/feature/types'
import {
  CaseStatusNameStyleConfig,
  StatusSuperset,
  TurnoverLengthWithGoal,
  TurnoverLengthWithoutGoal,
  useStatusStyles,
} from '../../../utils/status'

import './TimelinePill.css'

export type PillSizeType = 'sm' | 'lg' | 'full'

export interface TimelinePillState extends PillPositionAndSize {
  backgroundColor: string
  borderRadiusCss: { [key: string]: number }
  containedHeight: number
  containedWidth: number
  forecastedStyles: CaseStatusNameStyleConfig
  height: number
  isHovered?: boolean
  statusStyle: CaseStatusNameStyleConfig
  textColorCss?: string
  top: number
}

export const useTimelinePillState = ({
  isHovered,
  pillPositionAndSize,
  pillSizeType,
  status,
  isCase = false,
  type,
}: {
  isHovered: boolean
  pillPositionAndSize?: PillPositionAndSize
  pillSizeType: PillSizeType
  status: StatusSuperset
  isCase?: boolean
  type?: CaseType
}): TimelinePillState => {
  const theme = useTheme()
  const styles = useStatusStyles()
  const pillSizingCss = usePillSizingCss(pillSizeType, pillPositionAndSize)

  return useMemo(() => {
    const isForecasted = type === CaseType.FORECAST
    const isScheduled = status === CaseStatusName.SCHEDULED
    const isScheduledAndForecasted = isScheduled && isForecasted

    const caseStatusStyles = styles(status)
    const scheduledStyles = styles(CaseStatusName.SCHEDULED)
    const forecastedStyles = styles(CaseType.FORECAST)

    const getStatusStyle = () => {
      if (isCase && isScheduled) {
        return scheduledStyles
      } else if (!isCase && isForecasted) {
        return styles(CaseType.FORECAST)
      } else {
        return styles(status)
      }
    }

    const statusStyle = getStatusStyle()

    const hoverBackgroundColor = theme.palette.blue[10]
    const hoverTextColor = theme.palette.blue[70]
    const textColorCss = isHovered ? hoverTextColor : statusStyle.textColor

    const unhoveredBackgroundColor =
      isScheduledAndForecasted || isCase
        ? statusStyle.backgroundColor
        : caseStatusStyles.backgroundColor

    const backgroundColor = isHovered
      ? hoverBackgroundColor
      : unhoveredBackgroundColor

    const borderRadiusRightCss = pillSizingCss.doesOverflowMaxTime && {
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
    }
    const borderRadiusLeftCss = pillSizingCss.doesOverflowMinTime && {
      borderTopLeftRadius: 0,
      borderBottomLeftRadius: 0,
    }
    return {
      ...pillSizingCss,
      isHovered,
      statusStyle,
      textColorCss,
      backgroundColor,
      forecastedStyles,
      borderRadiusCss: { ...borderRadiusRightCss, ...borderRadiusLeftCss },
    }
  }, [
    styles,
    theme.palette.blue,
    isHovered,
    status,
    isCase,
    type,
    pillSizingCss,
  ])
}

export const useTurnoverPillState = ({
  pillPositionAndSize,
  pillSizeType,
  turnoverCurrentLength,
  turnoverOverallLength,
  turnoverType,
  isActive,
}: {
  pillPositionAndSize?: PillPositionAndSize
  pillSizeType: PillSizeType
  turnoverCurrentLength?: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
  turnoverOverallLength: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
  turnoverType: TurnoverType
  isActive: boolean
}): TimelinePillState => {
  const styles = useStatusStyles()
  const pillSizingCss = usePillSizingCss(pillSizeType, pillPositionAndSize)

  return useMemo(() => {
    const forecastedStyles = styles(CaseType.FORECAST)
    const overallLengthStyles = styles(turnoverOverallLength)
    const currentLengthStyles =
      turnoverOverallLength !== TurnoverLengthWithoutGoal.OVER_MAX &&
      turnoverOverallLength !== TurnoverLengthWithGoal.OVER_MAX &&
      turnoverCurrentLength
        ? styles(turnoverCurrentLength)
        : styles(turnoverOverallLength)

    let statusStyle
    // On the Schedule, where there isn't an active turnover:
    // * the completed, live or forecasted turnovers are styled based on overall length
    if (!isActive) {
      statusStyle = overallLengthStyles
      // In the VideoBlade, where there is an active turnover:
      // * the completed turnovers are styled based on the overall length
      // * the live or forecasted turnovers are styled based on current length with a border style based on the overall length.
    } else {
      if (turnoverType === TurnoverType.COMPLETE) {
        statusStyle = overallLengthStyles
      } else {
        statusStyle = {
          ...currentLengthStyles,
          border: overallLengthStyles.border,
        }
      }
    }

    const borderRadiusRightCss = pillSizingCss.doesOverflowMaxTime && {
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
    }
    const borderRadiusLeftCss = pillSizingCss.doesOverflowMinTime && {
      borderTopLeftRadius: 0,
      borderBottomLeftRadius: 0,
    }
    return {
      ...pillSizingCss,
      statusStyle,
      textColorCss: statusStyle.textColor,
      backgroundColor: statusStyle.backgroundColor,
      forecastedStyles: {
        ...forecastedStyles,
        border: overallLengthStyles.border,
      },
      borderRadiusCss: { ...borderRadiusRightCss, ...borderRadiusLeftCss },
    }
  }, [
    styles,
    turnoverType,
    turnoverCurrentLength,
    turnoverOverallLength,
    isActive,
    pillSizingCss,
  ])
}

const usePillSizingCss = (
  pillSizeType: PillSizeType,
  pillPositionAndSize: PillPositionAndSize = {
    doesOverflowMaxTime: false,
    doesOverflowMinTime: false,
    forecastedWidthPx: 0,
    gradientWidthPx: 0,
    left: '0',
    widthPct: '0',
    widthPx: 0,
  }
) => {
  const { scheduleShowAnesthesiaOnTimeline } =
    useFlags<WebDashboardFeatureFlagSet>()

  let smallPillHeight, tallPillHeight, pillMargin
  if (scheduleShowAnesthesiaOnTimeline) {
    smallPillHeight = 16
    tallPillHeight = 64
    pillMargin = 3
  } else {
    smallPillHeight = 16
    tallPillHeight = 44
    pillMargin = 7
  }
  const fullPillHeight = smallPillHeight + tallPillHeight + pillMargin

  const height =
    pillSizeType === 'sm'
      ? smallPillHeight
      : pillSizeType === 'lg'
        ? tallPillHeight
        : fullPillHeight
  // We need to fit the pill inside the container, so we need to subtract the border width * 2
  const containedHeight = height - 2
  const containedWidth = (pillPositionAndSize.widthPx ?? 2) - 2
  const top =
    pillSizeType === 'sm' ? tallPillHeight + pillMargin * 2 : pillMargin

  return {
    ...pillPositionAndSize,
    containedHeight,
    containedWidth,
    height,
    top,
  }
}
