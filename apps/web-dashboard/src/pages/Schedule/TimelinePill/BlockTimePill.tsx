import { CSSProperties, memo, useMemo } from 'react'

import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { sortBy } from 'lodash'
import { DateTime } from 'luxon'
import { rem } from 'polished'

import {
  Badge,
  Button,
  Caps1Bold,
  Caps2,
  Caps3,
  CARD_HEADER_DATE_FORMAT,
  ComponentTheme,
  EllidedText,
  Flag,
  FlexContainer,
  FlexItem,
  H6,
  P4,
  remSpacing,
  shape,
  Span3,
  Span4,
  TooltipProps,
  ZIndex,
} from '@apella/component-library'
import { CaseStatusName, CaseType } from 'src/__generated__/globalTypes'
import { HR } from 'src/components/HR'
import { useTimezone } from 'src/Contexts'
import { useBlockColors } from 'src/pages/BlockManagement/useBlockColors'
import { getReleaseByAtDateString } from 'src/pages/BlockManagement/utils'
import {
  ApellaBlock,
  ApellaBlockTime,
  ApellaBlockTimeRelease,
} from 'src/pages/types'
import { PillPositionAndSize } from 'src/utils/timelineHelpers'
import { onClickContactSupport } from 'src/utils/toggleBeacon'

import { CaseStatusNameStyleConfig } from '../../../utils/status'
import { getStartToEndTimeString } from '../utils'
import { PillHolder } from './TimelinePill'
import { useTimelinePillState } from './TimelinePillState'

interface BlockTimePillProps {
  caseStatus: CaseStatusName
  isHovered?: boolean
  pillId?: string
  pillPositionAndSize: PillPositionAndSize
  showScheduled: boolean
  tooltipPlacement?: TooltipProps['placement']
  type: CaseType
}

const isoStringToMillis = (isoString: string) =>
  DateTime.fromISO(isoString).toMillis()

const getMinBlockStartTimeMillis = (
  blockTimes: ApellaBlockTime[],
  roomId?: string
) =>
  blockTimes
    .filter((blockTime) => !roomId || blockTime.roomId === roomId)
    .reduce<number>(
      (acc, bl) => Math.min(acc, isoStringToMillis(bl.startTime)),
      Number.MAX_SAFE_INTEGER
    )

export const compareBlocksByMinStartTime = (
  a: Pick<ApellaBlock, 'blockTimes'>,
  b: Pick<ApellaBlock, 'blockTimes'>,
  roomId?: string
) =>
  getMinBlockStartTimeMillis(a.blockTimes, roomId) <
  getMinBlockStartTimeMillis(b.blockTimes, roomId)
    ? -1
    : 1

// Assumption:
// - Release can be full or partial (left, right, middle)
// - Only 1 release per block time
export const BlockTimePillContainer = memo(function BlockTimePillContainer({
  blockTime,
  calculatePillPositionAndSize,
}: {
  blockTime: ApellaBlockTime
  calculatePillPositionAndSize: (
    startTime: DateTime,
    endTime?: DateTime,
    minWidth?: number,
    nowTime?: DateTime
  ) => PillPositionAndSize
}) {
  const { timezone } = useTimezone()
  const [release] = blockTime.releases.filter((r) => !r.unreleasedTime)

  const leftPillPositionAndSize = useMemo(() => {
    const blockStartTime = DateTime.fromISO(blockTime.startTime).setZone(
      timezone
    )
    const blockEndTime = DateTime.fromISO(blockTime.endTime).setZone(timezone)

    if (!release) {
      return calculatePillPositionAndSize(blockStartTime, blockEndTime)
    } else {
      const releaseStartTime = DateTime.fromISO(release.startTime).setZone(
        timezone
      )
      const releaseEndTime = DateTime.fromISO(release.endTime).setZone(timezone)

      if (
        +releaseStartTime === +blockStartTime &&
        +releaseEndTime === +blockEndTime
      ) {
        return null
      } else if (+releaseStartTime > +blockStartTime) {
        return calculatePillPositionAndSize(blockStartTime, releaseStartTime)
      }

      return null
    }
  }, [
    blockTime.endTime,
    blockTime.startTime,
    release,
    calculatePillPositionAndSize,
    timezone,
  ])

  const releasePillPositionAndSize = useMemo(() => {
    if (!release) {
      return null
    }
    return calculatePillPositionAndSize(
      DateTime.fromISO(release.startTime).setZone(timezone),
      DateTime.fromISO(release.endTime).setZone(timezone)
    )
  }, [release, calculatePillPositionAndSize, timezone])

  const rightPillPositionAndSize = useMemo(() => {
    const blockStartTime = DateTime.fromISO(blockTime.startTime).setZone(
      timezone
    )
    const blockEndTime = DateTime.fromISO(blockTime.endTime).setZone(timezone)

    if (!release) {
      return null
    } else {
      const releaseStartTime = DateTime.fromISO(release.startTime).setZone(
        timezone
      )
      const releaseEndTime = DateTime.fromISO(release.endTime).setZone(timezone)

      if (
        +releaseStartTime === +blockStartTime &&
        +releaseEndTime === +blockEndTime
      ) {
        return null
      } else if (+releaseEndTime < +blockEndTime) {
        return calculatePillPositionAndSize(releaseEndTime, blockEndTime)
      }

      return null
    }
  }, [
    blockTime.endTime,
    blockTime.startTime,
    release,
    calculatePillPositionAndSize,
    timezone,
  ])

  return (
    <>
      {!!leftPillPositionAndSize && (
        <BlockTimePill
          blockTime={blockTime}
          pillPositionAndSize={leftPillPositionAndSize}
          timezone={timezone}
          additionalCss={
            releasePillPositionAndSize
              ? {
                  borderTopRightRadius: 0,
                  borderBottomRightRadius: 0,
                  borderRight: 'none',
                }
              : undefined
          }
        />
      )}
      {releasePillPositionAndSize && (
        <BlockTimeReleasePill
          color={blockTime.block.color}
          release={release}
          pillPositionAndSize={releasePillPositionAndSize}
          type={CaseType.FORECAST}
          showScheduled={false}
          caseStatus={CaseStatusName.SCHEDULED}
          additionalCss={{
            ...(leftPillPositionAndSize
              ? {
                  borderTopLeftRadius: 0,
                  borderBottomLeftRadius: 0,
                  borderLeft: 'none',
                }
              : {}),
            ...(rightPillPositionAndSize
              ? {
                  borderTopRightRadius: 0,
                  borderBottomRightRadius: 0,
                  borderRight: 'none',
                }
              : {}),
          }}
        />
      )}

      {!!rightPillPositionAndSize && (
        <BlockTimePill
          blockTime={blockTime}
          pillPositionAndSize={rightPillPositionAndSize}
          timezone={timezone}
          additionalCss={
            releasePillPositionAndSize
              ? {
                  borderTopLeftRadius: 0,
                  borderBottomLeftRadius: 0,
                  borderLeft: 'none',
                }
              : undefined
          }
        />
      )}
    </>
  )
})

export const BlockTimeStatusPill = styled.div<CaseStatusNameStyleConfig>`
    border-radius: ${(props) => props.borderRadius};
    background-color: ${(props) => props.backgroundColor};
    border: ${(props) => props.border ?? 'none'}};
  `

export const BlockTooltipBodyBlockName = ({
  block,
}: {
  block: Pick<
    ApellaBlock,
    'caseIds' | 'color' | 'name' | 'utilizationPercentage'
  >
}) => {
  const colors = useBlockColors(block.color)

  return (
    <FlexContainer
      direction="row"
      justifyContent="space-between"
      gap={remSpacing.xsmall}
    >
      <FlexContainer direction="row" gap={remSpacing.xsmall}>
        <FlexItem
          css={{
            marginTop: 2,
            width: 16,
            height: 16,
            border: `1px solid ${colors.borderColor}`,
            borderRadius: shape.borderRadius.xxsmall,
            background: colors.backgroundColor,
          }}
        />
        <H6>{block.name}</H6>
      </FlexContainer>
      {block.caseIds.length > 0 &&
        block.utilizationPercentage !== undefined &&
        !isNaN(block.utilizationPercentage) && (
          <Span3>{(block.utilizationPercentage * 100).toFixed(0)}%</Span3>
        )}
    </FlexContainer>
  )
}

export const BlockTooltipBodyRoomTimeRelease = ({
  releases,
  color,
}: {
  releases: ApellaBlockTimeRelease[]
  color?: string
}) => {
  const theme: ComponentTheme = useTheme()
  const { timezone } = useTimezone()

  return (
    <>
      <HR />
      <Caps2 color={theme.palette.blue[60]}>
        Release History{' '}
        <Badge variant="secondary" color="blue">
          {releases.length}
        </Badge>
      </Caps2>
      {releases[0] && !releases[0].unreleasedTime && (
        <>
          <Span3 color={color}>
            {getStartToEndTimeString(
              releases[0].startTime,
              releases[0].endTime,
              timezone
            )}
          </Span3>
          <Span4 color={theme.palette.text.tertiary}>
            {getReleaseByAtDateString(releases[0], timezone)}
          </Span4>
        </>
      )}
    </>
  )
}

export const BlockTooltipBodyRoomTimeReleasedFrom = ({
  releasedFrom,
  releaseDate,
}: {
  releasedFrom: string
  releaseDate: string
}) => {
  const theme: ComponentTheme = useTheme()

  return (
    <>
      <HR />
      <Caps2 color={theme.palette.text.tertiary}>Released From</Caps2>
      <div
        css={{
          display: 'flex',
          gap: remSpacing.small,
          justifyContent: 'space-between',
        }}
      >
        <Caps2 color={theme.palette.text.secondary}>{releasedFrom}</Caps2>
        <Caps2
          style={{ minWidth: rem('90px'), textAlign: 'right' }}
          color={theme.palette.text.secondary}
        >
          {releaseDate}
        </Caps2>
      </div>
    </>
  )
}

export const BlockTooltipBodyRoomTimeDescription = ({
  blockTime,
}: {
  blockTime: ApellaBlockTime
}) => {
  const theme: ComponentTheme = useTheme()
  const { timezone } = useTimezone()
  const color = `${theme.palette.gray[10]}80`
  const textColor = theme.palette.text.secondary

  return (
    <FlexContainer
      direction="column"
      css={{
        padding: remSpacing.xsmall,
        border: `1px solid ${color}`,
        borderRadius: shape.borderRadius.xsmall,
        background: color,
      }}
    >
      <FlexContainer gap={remSpacing.small} justifyContent="space-between">
        <Span3 style={{ minWidth: rem('60px') }}>{blockTime.room.name}</Span3>
        <Span3
          style={{ minWidth: rem('90px'), textAlign: 'right' }}
          color={textColor}
        >
          {getStartToEndTimeString(
            blockTime.startTime,
            blockTime.endTime,
            timezone
          )}
        </Span3>
      </FlexContainer>

      {blockTime.releasedFrom && blockTime.createdTime && (
        <BlockTooltipBodyRoomTimeReleasedFrom
          releasedFrom={blockTime.releasedFrom}
          releaseDate={DateTime.fromISO(blockTime.createdTime)
            .setZone(timezone, { keepLocalTime: true })
            .toFormat(CARD_HEADER_DATE_FORMAT)}
        />
      )}

      {blockTime.releases.length > 0 && (
        <BlockTooltipBodyRoomTimeRelease
          releases={blockTime.releases}
          color={textColor}
        />
      )}
    </FlexContainer>
  )
}

export const BlockTooltipBodyBlockRow = ({
  block,
}: {
  block: Pick<ApellaBlock, 'id' | 'name' | 'color' | 'blockTimes' | 'caseIds'>
}) => {
  return (
    <FlexContainer
      key={block.id}
      direction="column"
      css={{ gap: remSpacing.small }}
    >
      <BlockTooltipBodyBlockName block={block} />

      {block.blockTimes.length > 0 && (
        <FlexContainer direction="column" css={{ gap: remSpacing.xxsmall }}>
          {sortBy(block.blockTimes, 'roomId').map((bt) => (
            <BlockTooltipBodyRoomTimeDescription key={bt.id} blockTime={bt} />
          ))}
        </FlexContainer>
      )}
    </FlexContainer>
  )
}

export const BlockTooltipBody = ({
  blocks,
  roomId,
}: {
  blocks: ApellaBlock[]
  roomId: string
}) => {
  const theme: ComponentTheme = useTheme()
  const dateString =
    blocks.length > 0
      ? DateTime.fromISO(blocks[0]?.blockTimes[0]?.startTime).toISODate() +
        ' - '
      : ''

  return (
    <FlexContainer direction="column" css={{ gap: 12, zIndex: ZIndex.ABOVE }}>
      <Caps1Bold
        css={{
          color: theme.palette.text.secondary,
        }}
      >
        {blocks.length > 1 ? 'Blocks' : 'Block'}
      </Caps1Bold>
      {blocks.length > 0 &&
        blocks
          .sort((a, b) => compareBlocksByMinStartTime(a, b, roomId))
          .map((block) => (
            <BlockTooltipBodyBlockRow key={block.id} block={block} />
          ))}
      <Button
        size="sm"
        appearance="link"
        color="gray"
        css={{ justifyContent: 'flex-start' }}
        onClick={(e) =>
          onClickContactSupport(e, {
            subject: `Block Data Request - ${dateString}${roomId} - ${blocks.map((b) => b.name).join()}`,
          })
        }
      >
        <Flag size="xs" />
        Request a change
      </Button>
    </FlexContainer>
  )
}

export const BlockTimePill = memo(function BlockTimePill({
  blockTime,
  pillPositionAndSize,
  additionalCss,
}: Pick<BlockTimePillProps, 'pillPositionAndSize'> & {
  blockTime: ApellaBlockTime
  timezone: string
  additionalCss?: React.CSSProperties
}) {
  const {
    left,
    widthPx,
    height,
    top,
    containedHeight,
    containedWidth,
    textColorCss,
  } = useTimelinePillState({
    type: CaseType.FORECAST,
    isHovered: false,
    pillPositionAndSize,
    pillSizeType: 'full',
    isCase: false,
    status: CaseStatusName.SCHEDULED,
  })

  const pillColors = useBlockColors(blockTime.block.color)

  return (
    <PillHolder
      style={{
        left,
        width: widthPx,
        height,
        top,
        display: 'flex',
      }}
    >
      <BlockTimeStatusPill
        borderRadius={shape.borderRadius.xxsmall}
        backgroundColor={pillColors.backgroundColor}
        border={`1px solid ${pillColors.borderColor}`}
        css={{
          paddingBottom: 8,
          ...additionalCss,
        }}
      >
        <EllidedText
          css={{
            width: containedWidth,
            color: textColorCss,
            height: containedHeight,
          }}
        />
      </BlockTimeStatusPill>
    </PillHolder>
  )
})

export const BlockTimeReleasePill = memo(function BlockTimePill({
  color,
  release,
  pillPositionAndSize,
  type,
  caseStatus,
  isHovered = false,
  additionalCss,
}: BlockTimePillProps & {
  release: ApellaBlockTimeRelease
  color: string
  additionalCss?: CSSProperties
}) {
  const theme: ComponentTheme = useTheme()
  const { timezone } = useTimezone()
  const { left, widthPx, height, top, containedHeight, containedWidth } =
    useTimelinePillState({
      type,
      isHovered,
      pillPositionAndSize,
      pillSizeType: 'full',
      isCase: false,
      status: caseStatus,
    })
  const releaseColors = useBlockColors(color)

  return (
    <PillHolder
      style={{
        left,
        width: widthPx,
        height,
        top,
        display: 'flex',
      }}
    >
      <BlockTimeStatusPill
        borderRadius={shape.borderRadius.xxsmall}
        backgroundColor={theme.palette.background.primary}
        border={`1px dashed ${releaseColors.borderColor}`}
        css={{
          paddingBottom: 8,
          ...additionalCss,
        }}
      >
        <EllidedText
          css={{
            width: containedWidth,
            height: containedHeight,
            overflow: 'visible',
          }}
        >
          <P4
            css={{
              color: releaseColors.color,
              height,
              borderRadius: 4,
              padding: '2px 4px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Caps3 style={{ display: 'block' }}>Released</Caps3>
            <Span3>
              {getStartToEndTimeString(
                release.startTime,
                release.endTime,
                timezone
              )}
            </Span3>
          </P4>
        </EllidedText>
      </BlockTimeStatusPill>
    </PillHolder>
  )
})
