import { memo, MouseEvent, useCallback } from 'react'

import { Theme, useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { rem } from 'polished'

import {
  Caps1Bold,
  Caps2Bold,
  Caps3,
  FlexContainer,
  FlexItem,
  P2,
  P3,
  remSpacing,
  shape,
  mediaQueries,
  Caps1,
  fontWeights,
  MessageBubbleUnfilled,
} from '@apella/component-library'
import { getDisplayNameByTurnoverLength } from 'src/utils/turnovers'

import { useRoomName } from '../../../utils/hooks'
import {
  getAnesthesiaStaff,
  getORStaff,
  getPrimarySurgeonText,
  toStaffFullName,
  toStaffRoleDisplayName,
} from '../../../utils/roles'
import { ApellaCase, Staff, Turnover } from '../../types'
import {
  CaseStatusPill,
  CaseTimingData,
  ScheduledTimingNode,
  useCaseTags,
} from '../CaseDetails'
import { TurnoverTimingDetails } from '../TurnoverTimingDetails'
import { useNotificationInformationForLatestEvent } from './useNotificationInformationForLatestEvent'

export interface CaseTooltipBodyProps {
  apellaCase?: Pick<
    ApellaCase,
    'type' | 'case' | 'startTime' | 'endTime' | 'status'
  >
}

export const CaseTooltipBody = memo(function CaseTooltipBody({
  apellaCase,
}: CaseTooltipBodyProps) {
  const caseObj = apellaCase?.case
  const apellaCaseStatus = apellaCase?.status.name
  const theme = useTheme()
  const roomName = useRoomName(caseObj?.room.id)
  const latestEventNotification = useNotificationInformationForLatestEvent(
    apellaCase?.case
  )

  const primarySurgeon = caseObj ? getPrimarySurgeonText(caseObj) : ''
  const anesthesiaStaff = caseObj ? getAnesthesiaStaff(caseObj) : []
  const orStaff = caseObj ? getORStaff(caseObj) : []
  const numRowsInTable = Math.max(anesthesiaStaff.length, orStaff.length)
  const caseTags = useCaseTags(caseObj)

  const onTooltipClick = useCallback((e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation()
  }, [])

  return (
    <div
      css={{ minWidth: 350, userSelect: 'text', maxWidth: 450 }}
      onClick={onTooltipClick}
    >
      <FlexContainer
        css={{ whiteSpace: 'nowrap' }}
        justifyContent={'space-between'}
        alignItems={'center'}
      >
        <FlexItem>
          <Caps1Bold
            css={{
              color: theme.palette.text.secondary,
            }}
          >
            {primarySurgeon}
          </Caps1Bold>
        </FlexItem>
        {apellaCaseStatus && (
          <FlexItem>
            <CaseStatusPill status={apellaCaseStatus} />
          </FlexItem>
        )}
      </FlexContainer>
      <P3 css={{ color: theme.palette.gray[60] }}>
        {caseObj?.procedures.length
          ? caseObj?.procedures.map((p) => p.name).join('; ')
          : 'Scheduled Case'}
      </P3>
      {!!caseObj?.externalCaseId && (
        <P3 css={{ color: theme.palette.gray[60] }}>
          <FlexContainer
            justifyContent="space-between"
            gap={remSpacing.xxsmall}
            as="span"
          >
            <FlexItem shrink={1} as="span">
              Case ID: {caseObj?.externalCaseId}
            </FlexItem>
          </FlexContainer>
        </P3>
      )}

      <P2 css={{ margin: `${remSpacing.small} 0` }}>
        <Caps2Bold
          css={{
            color: theme.palette.gray[40],
          }}
        >
          {[roomName, ...caseTags].join(' • ')}
        </Caps2Bold>
      </P2>

      {latestEventNotification && (
        <div
          css={{
            display: 'flex',
            flexDirection: 'row',
          }}
        >
          <MessageBubbleUnfilled
            color={latestEventNotification.eventColor}
            size="xs"
            style={{
              marginTop: rem('1px'),
              marginRight: remSpacing.xxsmall,
            }}
          />
          <P3>
            {latestEventNotification.notificationSentTime}:{' '}
            {latestEventNotification.eventName}, text notification:{' '}
            {[...latestEventNotification.notificationRecipients].join(', ')}
          </P3>
        </div>
      )}
      <FlexContainer
        css={{ margin: `${remSpacing.small} 0` }}
        gap={remSpacing.xxsmall}
        alignItems="center"
      >
        <ScheduledTimingNode caseObj={apellaCase?.case} />
      </FlexContainer>
      <div
        css={{
          border: `1px solid ${theme.palette.gray[30]}`,
          borderRadius: shape.borderRadius.xsmall,
          marginBottom: remSpacing.small,
          padding: `${remSpacing.xsmall} 0 ${remSpacing.xsmall} 0`,
          [mediaQueries.lg]: {
            padding: `${remSpacing.xsmall} ${remSpacing.small}`,
          },
        }}
      >
        <CaseTimingData apellaCase={apellaCase} />
      </div>
      {!!numRowsInTable && (
        <TooltipTable>
          <TooltipColumn>
            <Caps3
              css={{
                color: theme.palette.text.tertiary,
                borderBottom: `1px solid ${theme.palette.gray[30]}`,
                paddingBottom: remSpacing.xsmall,
              }}
            >
              Anesthesia
            </Caps3>
            {anesthesiaStaff.map((anesthesiaStaffMember) => (
              <StaffCell
                staff={anesthesiaStaffMember}
                key={anesthesiaStaffMember.id}
              />
            ))}
          </TooltipColumn>
          <TooltipColumn>
            <Caps3
              css={{
                color: theme.palette.text.tertiary,
                borderBottom: `1px solid ${theme.palette.gray[30]}`,
                paddingBottom: remSpacing.xsmall,
              }}
            >
              OR Staff
            </Caps3>
            {orStaff.map((orStafffMember) => (
              <StaffCell staff={orStafffMember} key={orStafffMember.id} />
            ))}
          </TooltipColumn>
        </TooltipTable>
      )}
    </div>
  )
})

export const TurnoverTooltipBody = ({ turnover }: { turnover: Turnover }) => {
  const theme = useTheme()
  return (
    <>
      <Caps1
        css={{
          ...fontWeights.semibold,
          paddingBottom: remSpacing.gutter,
          color: theme.palette.text.secondary,
          display: 'block',
        }}
      >
        {getDisplayNameByTurnoverLength(turnover.overallLengthStatus)}
      </Caps1>
      <TurnoverTimingDetails turnover={turnover} />
    </>
  )
}

const TooltipTable = styled.div(({ theme }: { theme: Theme }) => ({
  display: 'grid',
  gridAutoFlow: 'column',
  columnGap: remSpacing.medium,
  background: theme.palette.background.secondary,
  minWidth: 'calc(350px + 2rem)',
  width: 'calc(100% + 2rem)',
  margin: '0 -1rem -1rem -1rem',
  gridTemplateColumns: '1fr 1fr',
  padding: `${remSpacing.xsmall} ${remSpacing.medium}`,
  borderBottomLeftRadius: shape.borderRadius.xsmall,
  borderBottomRightRadius: shape.borderRadius.xsmall,
}))

const TooltipColumn = styled.div(() => ({
  display: 'grid',
  gridAutoRows: 'min-content',
}))

const StaffCell = ({ staff }: { staff?: Staff }): React.JSX.Element => {
  const theme = useTheme()

  return staff !== undefined ? (
    <div
      css={{
        padding: `${remSpacing.xsmall} ${remSpacing.small} ${remSpacing.xsmall} 0`,
        borderBottom: `1px solid ${theme.palette.gray[30]}`,
      }}
    >
      <P3
        css={{
          color: theme.palette.text.secondary,
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          overflow: 'hidden',
          maxWidth: 182,
        }}
      >
        {toStaffFullName({ staff })}
      </P3>
      <Caps3 css={{ color: theme.palette.gray[50] }}>
        {toStaffRoleDisplayName(staff.role)}
      </Caps3>
    </div>
  ) : (
    <></>
  )
}
