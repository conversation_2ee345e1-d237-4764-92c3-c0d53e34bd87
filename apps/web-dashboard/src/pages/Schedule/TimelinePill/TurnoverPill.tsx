import { MouseEvent, useEffect, useState } from 'react'
import { usePageVisibility } from 'react-page-visibility'

import { DateTime } from 'luxon'

import { TooltipProps } from '@apella/component-library'

import { TurnoverType } from '../../../__generated__/globalTypes'
import { useTimezone } from '../../../Contexts'
import { PillPositionAndSize } from '../../../utils/timelineHelpers'
import { SLOW_POLL_INTERVAL_MS } from '../../Live/consts'
import { ApellaCase, Turnover } from '../../types'
import {
  LiveTimelinePill,
  TurnoverPill as TurnoverPillDisplay,
} from './TimelinePill'
import { useTurnoverPillState } from './TimelinePillState'
import { TurnoverTooltipBody } from './TooltipBody'
import {
  ActiveTurnoverDisplayText,
  TurnoverDisplayText,
} from './TooltipDisplayText'

export const TurnoverPill = ({
  isActive,
  turnover,
  calculatePillPositionAndSize,
  minTime,
  maxTime,
  hasPendingUpdate,
  onClick: onClickProp,
  onRightClick,
  showScheduled,
}: {
  isActive: boolean
  turnover: Turnover
  calculatePillPositionAndSize: (
    startTime: DateTime,
    endTime?: DateTime,
    minWidth?: number,
    nowTime?: DateTime
  ) => PillPositionAndSize
  tooltipPlacement?: TooltipProps['placement']
  minTime: DateTime
  maxTime: DateTime
  hasPendingUpdate?: boolean
  onClick?: (
    mouseEvent: MouseEvent,
    apellaCase?: ApellaCase,
    turnover?: Turnover
  ) => void
  onRightClick?: (e: MouseEvent) => void
  showScheduled?: boolean
}) => {
  const pageVisible = usePageVisibility()
  const { timezone } = useTimezone()

  const [positionAndSize, setPositionAndSize] = useState<PillPositionAndSize>()

  useEffect(() => {
    const calcPositionAndSize = () => {
      let pillPositionAndSize

      const hidePhasePill =
        turnover.startTime > maxTime || turnover.endTime < minTime

      if (!hidePhasePill) {
        pillPositionAndSize = calculatePillPositionAndSize(
          turnover.startTime,
          turnover.endTime,
          0,
          turnover.type === TurnoverType.LIVE && isActive
            ? DateTime.now()
            : undefined
        )
      }
      setPositionAndSize(pillPositionAndSize)
    }
    calcPositionAndSize()

    const intervalId =
      pageVisible && setInterval(calcPositionAndSize, SLOW_POLL_INTERVAL_MS)
    return () => {
      intervalId && clearInterval(intervalId)
    }
  }, [
    calculatePillPositionAndSize,
    turnover,
    timezone,
    pageVisible,
    maxTime,
    minTime,
    isActive,
  ])

  const onClick = (mouseEvent: MouseEvent) => {
    if (onClickProp) {
      onClickProp(mouseEvent, undefined, turnover)
    }
  }

  const turnoverSchedulePillState = useTurnoverPillState({
    pillPositionAndSize: positionAndSize,
    pillSizeType: showScheduled ? 'lg' : 'full',
    turnoverOverallLength: turnover.overallLengthStatus,
    turnoverType: turnover.type,
    isActive: false,
  })
  const turnoverTimelinePillState = useTurnoverPillState({
    pillPositionAndSize: positionAndSize,
    pillSizeType: 'lg',
    turnoverOverallLength: turnover.overallLengthStatus,
    turnoverCurrentLength: turnover.currentLengthStatus,
    turnoverType: turnover.type,
    isActive: true,
  })

  if (!positionAndSize) {
    return <></>
  }

  return isActive ? (
    <LiveTimelinePill
      pillState={turnoverTimelinePillState}
      displayText={<ActiveTurnoverDisplayText turnover={turnover} />}
      tooltipBody={<TurnoverTooltipBody turnover={turnover} />}
      onClick={onClick}
      onRightClick={onRightClick}
    />
  ) : (
    <TurnoverPillDisplay
      pillState={turnoverSchedulePillState}
      displayText={
        <TurnoverDisplayText
          turnover={turnover}
          hasPendingUpdate={hasPendingUpdate}
        />
      }
      tooltipBody={<TurnoverTooltipBody turnover={turnover} />}
      onClick={onClickProp ? onClick : undefined}
      onRightClick={onRightClick}
    />
  )
}
