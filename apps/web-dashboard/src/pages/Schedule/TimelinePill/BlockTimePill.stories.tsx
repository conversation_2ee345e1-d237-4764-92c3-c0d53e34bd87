import { StoryObj } from '@storybook/react'

import {
  Caps2Bold,
  remSpacing,
  shape,
  theme,
  Tooltip,
  Union,
} from '@apella/component-library'

import {
  BlockTimePill,
  BlockTimePillContainer,
  BlockTooltipBody,
} from './BlockTimePill'
import {
  blocks,
  blockTimes,
  calculatePillPositionAndSize,
} from './BlockTimePillMockData'

export default {
  title: 'Components/BlockPills',
  component: BlockTimePillContainer,
}

type Story = StoryObj<typeof BlockTimePill>

const blockTooltipBodyProps = {
  style: {
    background: theme.palette.background.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: shape.borderRadius.xsmall,
    display: 'flex',
    padding: remSpacing.xsmall,
  },
}

export const BlockTimePillState: Story = {
  render: () => (
    <>
      <div
        css={{
          display: 'flex',
          flexDirection: 'row',
          gap: remSpacing.xsmall,
          alignItems: 'flex-end',
          paddingTop: 200,
        }}
      >
        <Caps2Bold
          css={{
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
          }}
        >
          {'garage_1'}
        </Caps2Bold>
        <Tooltip
          body={<BlockTooltipBody blocks={blocks} roomId={'garage_1'} />}
          isHoverable={true}
          isTouchEnabled={true}
          strategy={'fixed'}
          {...blockTooltipBodyProps}
        >
          <div
            css={{
              cursor: 'pointer',
              color: theme.palette.gray[50],
              ':hover': {
                color: theme.palette.background.alternate,
              },
            }}
            onMouseEnter={() => {}}
            onMouseLeave={() => {}}
          >
            <Union size="xs" />
          </div>
        </Tooltip>
      </div>
      <div
        css={{
          width: '100%',
          height: 81,
          position: 'relative',
          '&:after': {
            display: 'block',
            height: '100%',
            margin: `0 - 20px`,
            borderBottom: `1px solid ${theme.palette.gray[20]}`,
          },
        }}
      >
        {blockTimes.map((blockTime) => (
          <BlockTimePillContainer
            key={`block-time-pill-${blockTime.id}`}
            blockTime={blockTime}
            calculatePillPositionAndSize={calculatePillPositionAndSize}
          />
        ))}
      </div>
    </>
  ),
}
