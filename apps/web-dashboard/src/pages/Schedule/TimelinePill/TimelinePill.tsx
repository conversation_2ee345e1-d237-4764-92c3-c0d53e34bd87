import { memo, MouseEvent } from 'react'

import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { omit } from 'lodash'
import { rem } from 'polished'

import {
  EllidedText,
  fontWeights,
  remSpacing,
  Tooltip,
  ZIndex,
} from '@apella/component-library'

import { StatusPill } from '../../../components/Keys'
import { CaseTooltipHoverInterConfiguration } from '../../types'
import { TimelinePillState } from './TimelinePillState'

import './TimelinePill.css'

export interface TimelinePillProps {
  displayText?: React.JSX.Element
  onClick?: (mouseEvent: MouseEvent) => void
  onMouseEnter?: () => void
  onMouseLeave?: () => void
  onRightClick?: (e: React.MouseEvent) => void
  pillState: TimelinePillState
  tooltipBody?: React.JSX.Element
}

export const PillHolder = styled.div`
  position: absolute;
  cursor: ${(props) => (props.onClick ? 'pointer' : 'default')};
  display: flex;
  align-items: center;
`

const tooltipMiddleware = [
  Tooltip.middleware.autoPlacement(),
  Tooltip.middleware.shift(),
  Tooltip.middleware.offset(8),
]

const tooltipZIndexProps = {
  style: {
    zIndex: ZIndex.MAX,
  },
}

export const TurnoverPill = memo(function TurnoverPill({
  pillState,
  displayText,
  tooltipBody,
  onClick,
  onRightClick,
}: TimelinePillProps) {
  const { statusStyle, left, widthPx, containedHeight, height, top } = pillState
  const SPACING_PX = 2

  return (
    <PillHolder
      style={{
        left,
        width: widthPx,
        height,
        top,
        padding: `0 ${SPACING_PX}px`,
      }}
      onClick={onClick}
      onContextMenu={onRightClick}
    >
      <Tooltip
        body={tooltipBody}
        middleware={tooltipMiddleware}
        maxWidth={undefined}
        isHoverable={true}
        {...tooltipZIndexProps}
        isTouchEnabled={false}
        hoverIntent={CaseTooltipHoverInterConfiguration}
        isDismissable={!!onRightClick}
      >
        <div
          css={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: widthPx - SPACING_PX * 2,
            color: statusStyle.textColor,
            height: containedHeight,
            border: `${rem('1px')} solid transparent`,
            borderRadius: statusStyle.borderRadius,
            ':hover': {
              border: statusStyle.border,
            },
          }}
        >
          <StatusPill
            {...omit(statusStyle, 'border')}
            css={{
              fontSize: rem('10px'),
              ...fontWeights.semibold,
              padding: remSpacing.xxsmall,
            }}
          >
            {displayText}
          </StatusPill>
        </div>
      </Tooltip>
    </PillHolder>
  )
})

export const LiveTimelinePill = memo(function LiveTimelinePill({
  onMouseEnter,
  onMouseLeave,
  onClick,
  tooltipBody,
  displayText,
  pillState,
  onRightClick,
}: TimelinePillProps) {
  const theme = useTheme()
  const {
    statusStyle,
    textColorCss,
    backgroundColor,
    left,
    widthPx: solidWidthPx,
    forecastedWidthPx,
    gradientWidthPx,
    containedHeight,
    containedWidth,
    height,
    top,
    isHovered,
    doesOverflowMaxTime,
    forecastedStyles,
    borderRadiusCss,
  } = pillState

  const radiusStyling = !forecastedWidthPx
    ? borderRadiusCss
    : { borderTopRightRadius: 0, borderBottomRightRadius: 0 }

  return (
    <PillHolder
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      style={{
        left,
        width: solidWidthPx + forecastedWidthPx,
        height,
        top,
      }}
      onClick={onClick}
      onContextMenu={onRightClick}
    >
      <Tooltip
        body={tooltipBody}
        middleware={tooltipMiddleware}
        maxWidth={undefined}
        isHoverable={true}
        {...tooltipZIndexProps}
        isTouchEnabled={false}
        hoverIntent={CaseTooltipHoverInterConfiguration}
        isDismissable={!!onRightClick}
      >
        <div
          css={{
            height,
            width: solidWidthPx + forecastedWidthPx,
          }}
        >
          {!!forecastedWidthPx && (
            <StatusPill
              {...forecastedStyles}
              css={{
                background: isHovered
                  ? backgroundColor
                  : forecastedStyles?.backgroundColor,
                borderRadius: statusStyle.borderRadius,
                borderBottomLeftRadius: 0,
                borderTopLeftRadius: 0,
                borderRightWidth: doesOverflowMaxTime ? 0 : 1,
                left: solidWidthPx,
                borderLeftWidth: 0,
                padding: '1px 1px 1px 0',
                position: 'absolute',
                // By nature of this element being where a live phase shows it's
                // predicted portion, the "now" line is always on the left side. To
                // avoid the "now" line showing through the gap left by the 0 left
                // border here and the 0 right border of the gradient element, we
                // shift this element over 2 pixels. We then re-add those 2 px to
                // the width
                marginLeft: -2,
                width: forecastedWidthPx + 2,
              }}
            >
              <div
                className="ForecastedNode"
                css={{
                  borderRadius: statusStyle?.borderRadius,
                  borderBottomLeftRadius: 0,
                  borderTopLeftRadius: 0,
                  width: forecastedWidthPx,
                  height: containedHeight - 2,
                }}
              />
            </StatusPill>
          )}
          <StatusPill
            {...statusStyle}
            css={{
              width: solidWidthPx,
              height: height,
              position: 'absolute',
              backgroundColor: isHovered
                ? backgroundColor
                : statusStyle.backgroundColor,
              ...radiusStyling,
            }}
          ></StatusPill>
          {!!gradientWidthPx && (
            <div
              css={{
                height: containedHeight - 2,
                borderTopRightRadius: 0,
                borderBottomRightRadius: 0,
                right: 1,
                top: 2,
                position: 'absolute',
                width: gradientWidthPx,
                background: `linear-gradient(90deg, ${backgroundColor} -1.67%, ${theme.palette.background.primary} 100%)`,
                borderRightWidth:
                  doesOverflowMaxTime || !forecastedWidthPx ? 0 : 1,
              }}
            />
          )}
          <div
            css={{
              width: containedWidth + forecastedWidthPx,
              color: textColorCss,
              paddingTop: remSpacing.xxsmall,
              paddingLeft: remSpacing.xsmall,
              overflow: 'hidden',
              position: 'relative',
              whiteSpace: 'nowrap',
            }}
          >
            {displayText}
          </div>
        </div>
      </Tooltip>
    </PillHolder>
  )
})
export const PhaseTimelinePill = memo(function PhaseTimelinePill({
  onMouseEnter,
  onMouseLeave,
  onClick,
  tooltipBody,
  displayText,
  pillState,
}: TimelinePillProps) {
  const {
    statusStyle,
    textColorCss,
    backgroundColor,
    left,
    widthPx,
    containedHeight,
    containedWidth,
    height,
    top,
    borderRadiusCss,
  } = pillState

  return (
    <PillHolder
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      style={{
        left,
        width: widthPx,
        height,
        top,
      }}
      onClick={onClick}
    >
      <Tooltip
        body={tooltipBody}
        middleware={tooltipMiddleware}
        maxWidth={undefined}
        isHoverable={true}
        {...tooltipZIndexProps}
        isTouchEnabled={false}
        hoverIntent={CaseTooltipHoverInterConfiguration}
      >
        <StatusPill
          {...statusStyle}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          css={{
            ...borderRadiusCss,
            width: widthPx,
            height,
            padding: rem('1px'),
            backgroundColor,
            zIndex: ZIndex.ABOVE,
            display: 'flex',
            flexDirection: 'row',
          }}
          onClick={onClick}
        >
          <EllidedText
            css={{
              color: textColorCss,
              width: containedWidth - 2,
              height: containedHeight,
              paddingLeft: remSpacing.xxsmall,
              paddingTop: rem('2px'),
            }}
          >
            {displayText}
          </EllidedText>
        </StatusPill>
      </Tooltip>
    </PillHolder>
  )
})

export const CaseTimelinePill = memo(function TimelinePill({
  onMouseEnter,
  onMouseLeave,
  onClick,
  tooltipBody,
  displayText,
  pillState,
}: TimelinePillProps) {
  const {
    statusStyle,
    textColorCss,
    backgroundColor,
    left,
    widthPx,
    containedHeight,
    containedWidth,
    height,
    top,
    borderRadiusCss,
  } = pillState

  return (
    <PillHolder
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      style={{
        left,
        width: widthPx,
        height,
        top,
      }}
      onClick={onClick}
    >
      <Tooltip
        body={tooltipBody}
        middleware={tooltipMiddleware}
        maxWidth={undefined}
        isHoverable={true}
        {...tooltipZIndexProps}
        isTouchEnabled={false}
        hoverIntent={CaseTooltipHoverInterConfiguration}
      >
        <StatusPill
          {...statusStyle}
          css={{
            ...borderRadiusCss,
            backgroundColor,
            zIndex: ZIndex.ABOVE,
          }}
        >
          <EllidedText
            css={{
              width: containedWidth,
              color: textColorCss,
              height: containedHeight,
            }}
          >
            {displayText}
          </EllidedText>
        </StatusPill>
      </Tooltip>
    </PillHolder>
  )
})
