import { Fragment, memo } from 'react'

import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { rem } from 'polished'

import {
  Caps3,
  Caps3Bold,
  remSpacing,
  ApellaDateTimeFormats,
  fontWeights,
  Progress,
  Note,
  NotAllowed,
  MessageBubbleUnfilledTransparent,
} from '@apella/component-library'
import { getDisplayNameByTurnoverLength } from 'src/utils/turnovers'

import { TurnoverType } from '../../../__generated__/globalTypes'
import { CaseLabelIcon } from '../../../components/Timeline/CaseLabelIcon'
import { WebDashboardFeatureFlagSet } from '../../../modules/feature/types'
import {
  getPrimarySurgeonText,
  getStaffMembersByRole,
  toRoleShortForm,
} from '../../../utils/roles'
import { useCurrentTime } from '../../../utils/useCurrentTime'
import { ApellaCase, Case, Turnover } from '../../types'
import { useNotificationInformationForLatestEvent } from './useNotificationInformationForLatestEvent'

const LINE_HEIGHT = 14
const LINE_HEIGHT_OLD = 18

export interface TooltipDisplayTextProps {
  caseObj?: Case
  phase: ApellaCase | null
}

interface PillTextProps {
  lineHeight: number
}

const PillText = styled(Caps3)<PillTextProps>(({ lineHeight }) => ({
  display: 'block',
  lineHeight: `${lineHeight}px`,
  overflow: 'hidden',
}))

export const TurnoverDisplayText = memo(function TurnoverDisplayText({
  hasPendingUpdate,
  turnover,
}: {
  hasPendingUpdate?: boolean
  turnover: Turnover
}) {
  const hasLabel = !!turnover.labels?.length
  const hasNote = !!turnover.note

  return (
    <div css={{ textAlign: 'center' }}>
      {Math.floor(turnover.duration.as('minutes'))}m
      <div css={{ display: 'flex', justifyContent: 'center' }}>
        {hasPendingUpdate ? (
          <Progress size="sm" />
        ) : (
          <>
            {hasLabel && <NotAllowed size="xxs" />}
            {hasNote && <Note size="xxs" />}
          </>
        )}
      </div>
    </div>
  )
})

export const ActiveTurnoverDisplayText = memo(function TurnoverDisplayText({
  turnover,
}: {
  turnover: Turnover
}) {
  const now = useCurrentTime()
  const elapsed =
    turnover.type === TurnoverType.LIVE
      ? now.diff(turnover.startTime).toFormat('mm:ss')
      : turnover.duration.toFormat('mm:ss')

  return (
    <>
      <Caps3Bold>
        {getDisplayNameByTurnoverLength(turnover.overallLengthStatus)}
      </Caps3Bold>
      <br />
      <Caps3>{elapsed}</Caps3>
    </>
  )
})

export const PhaseTooltipDisplayText = memo(function PhaseTooltipDisplayText({
  caseObj,
  phase,
}: TooltipDisplayTextProps) {
  const { scheduleShowAnesthesiaOnTimeline } =
    useFlags<WebDashboardFeatureFlagSet>()
  const theme = useTheme()
  const lineHeight = scheduleShowAnesthesiaOnTimeline
    ? LINE_HEIGHT
    : LINE_HEIGHT_OLD

  let timeStr = ''
  if (phase) {
    const startTimeStr = phase.startTime.toLocaleString(
      ApellaDateTimeFormats.TIME
    )
    const endTimeStr = phase.endTime
      ? ` - ${phase.endTime.toLocaleString(ApellaDateTimeFormats.TIME)}`
      : ''
    timeStr = `${startTimeStr} ${endTimeStr}`
  }
  const primarySurgeonText = caseObj ? getPrimarySurgeonText(caseObj) : ''

  const headerText = !caseObj?.staff.length
    ? timeStr
    : `${primarySurgeonText}, ${timeStr}`

  const staffMembersByRole = getStaffMembersByRole(caseObj)
  const staffNode = (
    <PillText lineHeight={lineHeight} css={fontWeights.semibold}>
      {staffMembersByRole.map(
        ({ role, staff }, index) =>
          !!staff.length && (
            <Fragment key={index}>
              {staff.map((s) => s.lastName.toLocaleLowerCase()).join('; ')}
              &nbsp;
              <span css={{ opacity: 0.4 }}>{toRoleShortForm(role)}</span>
              &nbsp;
            </Fragment>
          )
      )}
    </PillText>
  )

  const latestEventNotification =
    useNotificationInformationForLatestEvent(caseObj)

  const noteNode = (
    <PillText
      lineHeight={lineHeight}
      css={{
        display: 'flex',
        gap: remSpacing.xxsmall,
        color: theme.palette.gray[80],
      }}
    >
      {latestEventNotification && (
        <MessageBubbleUnfilledTransparent
          color={latestEventNotification.eventColor}
          size="xxs"
          style={{
            marginTop: rem('1px'),
          }}
        />
      )}
      {caseObj?.caseLabels?.map((cl) => (
        <CaseLabelIcon
          key={cl.id}
          color={cl.color}
          abbreviation={cl.abbreviation}
          size={'sm'}
        />
      ))}
      {!!caseObj?.notePlan?.note && (
        <div css={{ display: 'flex', alignItems: 'center' }}>
          <Note size={'xxs'} color={theme.palette.gray[80]} />
          {caseObj.notePlan.note}
        </div>
      )}
    </PillText>
  )

  return (
    <>
      <PillText lineHeight={lineHeight} css={fontWeights.semibold}>
        {headerText}
      </PillText>
      {scheduleShowAnesthesiaOnTimeline && staffNode}
      <PillText lineHeight={lineHeight}>
        {caseObj?.isAddOn && '++ '}
        {!!caseObj?.procedures.length &&
          caseObj?.procedures.map((p) => p.name).join('; ')}
      </PillText>
      {scheduleShowAnesthesiaOnTimeline && noteNode}
    </>
  )
})

export const CaseTooltipDisplayText = memo(function CaseTooltipDisplayText({
  caseObj,
}: TooltipDisplayTextProps) {
  if (!caseObj) {
    return null
  }

  const displayText = `SCH: ${caseObj.scheduledStartTime.toLocaleString(
    ApellaDateTimeFormats.TIME
  )} -
  ${caseObj.scheduledEndTime.toLocaleString(ApellaDateTimeFormats.TIME)}`
  return (
    <Caps3
      css={{
        lineHeight: 1.5,
        display: 'block',
        paddingLeft: remSpacing.xsmall,
      }}
    >
      {displayText}
    </Caps3>
  )
})
