import { StoryObj } from '@storybook/react'

import { TurnoverPill } from './TurnoverPill'
import { getPillStateData } from './TurnoverPillMockData'

export default {
  title: 'Components/TurnoverPills',
  component: TurnoverPill,
}

type Story = StoryObj<typeof TurnoverPill>

const pillStateData = getPillStateData(false)

export const SchedulePillState: Story = {
  render: () => (
    <>
      {pillStateData.map((data, i) => (
        <div key={i} style={{ marginLeft: '15px', marginTop: '10px' }}>
          <h4
            style={{
              marginBottom: '40px',
              marginTop: '10px',
              paddingTop: '10px',
            }}
          >
            {data.title}
          </h4>
          <div style={{ ...data.style } as React.CSSProperties}>
            <TurnoverPill
              isActive={false}
              turnover={data.turnover}
              calculatePillPositionAndSize={data.calculatePillPositionAndSize}
              minTime={data.minTime}
              maxTime={data.maxTime}
            />
          </div>
        </div>
      ))}
    </>
  ),
}
