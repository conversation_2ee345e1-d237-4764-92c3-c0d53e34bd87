import { DateTime, Interval } from 'luxon'

import { ApellaBlock } from 'src/pages/types'
import { calculatePillPositionAndSizeHelper } from 'src/utils/timelineHelpers'

import { GetBlockTimeData } from '../__generated__'

const calculatePillPositionAndSize = (
  minTime: DateTime,
  endTime?: DateTime
) => {
  return calculatePillPositionAndSizeHelper({
    startTime: minTime,
    timelineWidth: 1000,
    minHour: minTime,
    maxHour: minTime.plus({ day: 1 }),
    endTime: endTime,
    liveTime: DateTime.local(),
  })
}

export const mockBlockTimesData: { data: GetBlockTimeData } = {
  data: {
    blockTimes: {
      edges: [
        {
          node: {
            id: '3c48a36e-f867-4ab5-966f-47902ec5ba4c',
            blockId: '4947fd70-a870-4ca7-922d-637a5a5f6625',
            startTime: '2025-02-26T15:30:00+00:00',
            endTime: '2025-02-27T01:00:00+00:00',
            roomId: 'garage_1',
            releasedFrom: null,
            createdTime: '2025-03-04T15:10:49.101531+00:00',
            room: {
              id: 'garage_1',
              name: 'Garage 1',
              __typename: 'Room',
            },
            block: {
              id: '4947fd70-a870-4ca7-922d-637a5a5f6625',
              name: 'Ami',
              color: '#ad9dff',
              surgeonIds: [],
              archivedTime: null,
              __typename: 'Block',
            },
            releases: [
              {
                id: '8581506d-309c-4654-a41d-2c1461968027',
                blockTimeId: '3c48a36e-f867-4ab5-966f-47902ec5ba4c',
                reason: 'testing',
                startTime: '2025-02-26T15:30:00+00:00',
                endTime: '2025-02-26T01:00:00+00:00',
                source: 'Ami Patel',
                sourceType: 'human_gt',
                releasedTime: '2025-02-27T03:24:24.124000+00:00',
                unreleasedTime: null,
                unreleasedSource: null,
                __typename: 'BlockTimeRelease',
              },
            ],
            __typename: 'BlockTime',
          },
          __typename: 'BlockTimeEdge',
        },
        {
          node: {
            id: '1c93779b-03a2-42ca-8cf5-acd6ea382ce5',
            blockId: '2d3616e2-4054-46b2-82fa-84272f7869b7',
            startTime: '2025-02-26T15:30:00+00:00',
            endTime: '2025-02-26T01:00:00+00:00',
            roomId: 'garage_1',
            releasedFrom: 'Ami',
            createdTime: '2025-02-27T03:24:43.967924+00:00',
            room: {
              id: 'garage_1',
              name: 'Garage 1',
              __typename: 'Room',
            },
            block: {
              id: '2d3616e2-4054-46b2-82fa-84272f7869b7',
              name: 'testing',
              color: '#f9da90',
              surgeonIds: [],
              archivedTime: null,
              __typename: 'Block',
            },
            releases: [],
            __typename: 'BlockTime',
          },
          __typename: 'BlockTimeEdge',
        },
      ],
      __typename: 'BlockTimeConnection',
    },
  },
}

const blocks = mockBlockTimesData?.data.blockTimes.edges.map((e) => {
  return {
    ...e.node.block,
    blockTimes: [{ ...e.node, intervals: [] }],
    caseIds: [],
  } as ApellaBlock
})

const blockTimes =
  mockBlockTimesData?.data.blockTimes.edges.map((e) => {
    const timezone = 'America/New_York'
    const interval = Interval.fromDateTimes(
      DateTime.fromISO(e.node.startTime, { zone: timezone }),
      DateTime.fromISO(e.node.endTime, { zone: timezone })
    )
    const releaseIntervals = e.node.releases
      ?.filter((r) => !r.unreleasedTime)
      .filter((r) => {
        const blockStartTime = DateTime.fromISO(e.node.startTime, {
          zone: timezone,
        })
        const releasedTime = DateTime.fromISO(r.releasedTime, {
          zone: timezone,
        })

        return (
          blockStartTime.diff(releasedTime, 'seconds').as('seconds') >
          7 * 24 * 60 * 60
        )
      })
      .map((r) => ({
        interval: Interval.fromDateTimes(
          DateTime.fromISO(r.startTime, { zone: timezone }),
          DateTime.fromISO(r.endTime, { zone: timezone })
        ),
      }))

    return {
      ...e.node,
      intervals: interval.difference(
        ...(releaseIntervals?.map((r) => r.interval) ?? [])
      ),
    }
  }) ?? []

export { blocks, blockTimes, calculatePillPositionAndSize }
