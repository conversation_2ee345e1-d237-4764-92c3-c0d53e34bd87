import { DateTime, Duration } from 'luxon'

import { TurnoverType } from 'src/__generated__/globalTypes'
import { Turnover } from 'src/pages/types'
import { TurnoverLengthWithGoal } from 'src/utils/status'
import { calculatePillPositionAndSizeHelper } from 'src/utils/timelineHelpers'

const turnoverData = (
  currentLengthStatus: TurnoverLengthWithGoal,
  overallLengthStatus: TurnoverLengthWithGoal,
  duration: number,
  minTime: DateTime,
  maxTime: DateTime,
  type: TurnoverType
): Turnover => {
  return {
    currentLengthStatus: currentLengthStatus,
    duration: Duration.fromObject({ hours: duration }),
    endTime: maxTime,
    followingCaseId: '',
    goals: {
      __typename: 'TurnoverGoals',
      goalMinutes: 35,
      maxMinutes: 90,
    },
    id: '1',
    overallLengthStatus: overallLengthStatus,
    precedingCaseId: '123',
    roomId: '1',
    startTime: minTime,
    type: type,
    note: null,
    labels: [],
  }
}

const now = DateTime.local()
const calculatePillPositionAndSize = (minTime: DateTime, endTime: DateTime) => {
  return calculatePillPositionAndSizeHelper({
    startTime: minTime,
    timelineWidth: 1000,
    minHour: minTime,
    maxHour: minTime.plus({ hours: 3 }),
    endTime: endTime,
    liveTime: now,
  })
}

const commonPillStateData = [
  {
    title: 'Over Goal and case complete',
    isActive: true,
    turnover: turnoverData(
      TurnoverLengthWithGoal.OVER_GOAL,
      TurnoverLengthWithGoal.OVER_GOAL,
      1,
      now.minus({ hours: 1 }),
      now,
      TurnoverType.COMPLETE
    ),
    calculatePillPositionAndSize: () =>
      calculatePillPositionAndSize(now.minus({ hours: 1 }), now),
    minTime: now.minus({ hours: 1 }),
    maxTime: now,
    onClick: () => {},
    style: { position: 'absolute', top: '30px', left: '183px' },
    type: TurnoverType.COMPLETE,
  },
  {
    title: 'Under Goal',
    isActive: true,
    turnover: turnoverData(
      TurnoverLengthWithGoal.UNDER_GOAL,
      TurnoverLengthWithGoal.UNDER_GOAL,
      0.5,
      now.minus({ hours: 0.5 }),
      now,
      TurnoverType.COMPLETE
    ),
    calculatePillPositionAndSize: () =>
      calculatePillPositionAndSize(now.minus({ hours: 0.5 }), now),
    minTime: now.minus({ hours: 0.5 }),
    maxTime: now,
    onClick: () => {},
    style: { position: 'absolute', top: '95px', left: '265px' },
    type: TurnoverType.COMPLETE,
  },
  {
    title: 'Over Max',
    isActive: true,
    turnover: turnoverData(
      TurnoverLengthWithGoal.OVER_MAX,
      TurnoverLengthWithGoal.OVER_MAX,
      2,
      now.minus({ hours: 2 }),
      now,
      TurnoverType.COMPLETE
    ),
    calculatePillPositionAndSize: () =>
      calculatePillPositionAndSize(now.minus({ hours: 2 }), now),
    minTime: now.minus({ hours: 2 }),
    maxTime: now,
    onClick: () => {},
    style: { position: 'absolute', top: '160px', left: '15px' },
    type: TurnoverType.COMPLETE,
  },
]

const additionalPillStateData = [
  {
    title: 'Projected to be over goal',
    isActive: true,
    turnover: turnoverData(
      TurnoverLengthWithGoal.UNDER_GOAL,
      TurnoverLengthWithGoal.OVER_GOAL,
      0.25,
      now.minus({ hours: 0.25 }),
      now.plus({ hours: 0.5 }),
      TurnoverType.LIVE
    ),
    calculatePillPositionAndSize: () =>
      calculatePillPositionAndSize(
        now.minus({ hours: 0.25 }),
        now.plus({ hours: 0.5 })
      ),
    minTime: now.minus({ hours: 0.25 }),
    maxTime: now.plus({ hours: 0.5 }),
    onClick: () => {},
    style: { position: 'absolute', top: '230px', left: '15px' },
    type: TurnoverType.LIVE,
  },
  {
    title: 'Over goal case not complete',
    isActive: true,
    turnover: turnoverData(
      TurnoverLengthWithGoal.OVER_GOAL,
      TurnoverLengthWithGoal.OVER_GOAL,
      0.66667,
      now.minus({ hours: 0.66667 }),
      now.plus({ hours: 0.66667 }),
      TurnoverType.LIVE
    ),
    calculatePillPositionAndSize: () =>
      calculatePillPositionAndSize(
        now.minus({ hours: 0.66667 }),
        now.plus({ hours: 0.66667 })
      ),
    minTime: now.minus({ hours: 0.66667 }),
    maxTime: now.plus({ hours: 0.66667 }),
    onClick: () => {},
    style: { position: 'absolute', top: '295px', left: '15px' },
    type: TurnoverType.LIVE,
  },
  {
    title: 'Under goal case not complete',
    isActive: true,
    turnover: turnoverData(
      TurnoverLengthWithGoal.UNDER_GOAL,
      TurnoverLengthWithGoal.UNDER_GOAL,
      0.25,
      now.minus({ hours: 0.25 }),
      now.plus({ hours: 0.25 }),
      TurnoverType.LIVE
    ),
    calculatePillPositionAndSize: () =>
      calculatePillPositionAndSize(
        now.minus({ hours: 0.25 }),
        now.plus({ hours: 0.25 })
      ),
    minTime: now.minus({ hours: 0.25 }),
    maxTime: now.plus({ hours: 0.25 }),
    onClick: () => {},
    style: { position: 'absolute', top: '365px', left: '15px' },
    type: TurnoverType.LIVE,
  },
]

export const getPillStateData = (includeAdditionalItems = false) => {
  return includeAdditionalItems
    ? [...commonPillStateData, ...additionalPillStateData]
    : commonPillStateData
}
