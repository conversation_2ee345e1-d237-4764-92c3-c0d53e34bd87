import LoadingOverlay from 'src/components/LoadingOverlay'
import { useQueryFilterState } from 'src/modules/daily-metrics/hooks/useQueryFilterState'

import { StaffPlanningBladeContent } from './StaffPlanningBladeContent'
import { useTimelineState } from './useTimelineState'

const OpenStaffPlanningBlade = (): React.JSX.Element => {
  const { siteIds, minTime, maxTime } = useQueryFilterState()
  const query = new URLSearchParams(location.search)
  const roomId = query.get('roomIds') || undefined

  const { rooms, isLoading, getCases } = useTimelineState({
    minTime,
    maxTime,
    options: {
      roomIds: roomId ? [roomId] : undefined,
      siteIds,
    },
  })

  if (rooms.length === 0) {
    return <></>
  }

  if (isLoading) {
    return <LoadingOverlay />
  }

  return (
    <StaffPlanningBladeContent
      room={rooms[0]}
      date={maxTime}
      refetchCases={getCases}
    />
  )
}

export default OpenStaffPlanningBlade
