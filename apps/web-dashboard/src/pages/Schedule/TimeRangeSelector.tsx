import {
  Button,
  theme,
  ButtonGroup,
  Timeline,
  PrimeTime,
  TwentyFourHours,
  shape,
  remSpacing,
} from '@apella/component-library'
import { TimelineTooltip } from 'src/components/Timeline/TimelineTooltip'

import { TimeRange, TimeRangeOptions } from './types'

export const TimeRangeSelector = ({
  onTimeRangeChange,
  timeRange = TimeRange.DayBound,
}: {
  onTimeRangeChange: (value: TimeRange) => void
  timeRange: TimeRange
}) => {
  const createTimeRangeComponent = (
    value: TimeRange,
    onTimeRangeChange: (value: TimeRange) => void
  ) => {
    const commonProps = {
      size: 'sm',
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color:
          value === timeRange ? theme.palette.gray[90] : theme.palette.gray[50],
      },
    }

    switch (value) {
      case TimeRange.PrimeTime:
        return (
          <PrimeTime
            onClick={() => onTimeRangeChange(TimeRange.PrimeTime)}
            {...commonProps}
          />
        )
      case TimeRange.FullTime:
        return (
          <TwentyFourHours
            onClick={() => onTimeRangeChange(TimeRange.FullTime)}
            {...commonProps}
          />
        )
      default:
        return (
          <Timeline
            onClick={() => onTimeRangeChange(TimeRange.FullTime)}
            {...commonProps}
          />
        )
    }
  }

  return (
    <ButtonGroup
      style={{
        display: 'flex',
        flexDirection: 'row',
        border: 'solid 1px',
        borderColor: theme.palette.gray[20],
        borderRadius: shape.borderRadiusPx.xxsmall,
        boxSizing: 'border-box',
        width: 'max-content',
      }}
    >
      {TimeRangeOptions.map((option) => {
        const isSelected = option.value === timeRange
        return (
          <Button
            key={option.value}
            onClick={() => onTimeRangeChange(option.value)}
            style={{
              color: theme.palette.gray[50],
              backgroundColor: isSelected
                ? theme.palette.blue[10]
                : 'transparent',
              padding: remSpacing.xxsmall,
            }}
            size="sm"
          >
            <TimelineTooltip content={option.label} placement={'bottom-start'}>
              {createTimeRangeComponent(option.value, onTimeRangeChange)}
            </TimelineTooltip>
          </Button>
        )
      })}
    </ButtonGroup>
  )
}
