import { ApolloClient, NormalizedCacheObject } from '@apollo/client'
import { DateTime, Interval } from 'luxon'

import { ApellaBlock } from '../types'
import {
  GetBlockTimeData,
  GetBlockTimeDataVariables,
  GetBlockUtilizationForSite,
  GetBlockUtilizationForSiteVariables,
} from './__generated__'
import { GET_BLOCK_TIME_DATA, GET_BLOCK_UTILIZATIONS_FOR_SITE } from './queries'

const RELEASE_CUTOFF_THRESHOLD_IN_DAYS = 7

export const fetchBlockUtilizationData = async (
  client: ApolloClient<NormalizedCacheObject>,
  {
    minTime,
    maxTime,
    siteIds = [],
    roomIds,
    timezone,
  }: {
    minTime: DateTime
    maxTime: DateTime
    siteIds: string[]
    roomIds: string[]
    timezone: string
  }
) => {
  const [blockTimesResult, ...allBlocksUtilizationResults] = await Promise.all([
    client.query<GetBlockTimeData, GetBlockTimeDataVariables>({
      query: GET_BLOCK_TIME_DATA,
      variables: {
        minEndTime: minTime.toISO(),
        maxStartTime: maxTime.toISO(),
        roomIds: roomIds,
      },
    }),
    ...siteIds?.map((siteId: string) =>
      client.query<
        GetBlockUtilizationForSite,
        GetBlockUtilizationForSiteVariables
      >({
        query: GET_BLOCK_UTILIZATIONS_FOR_SITE,
        variables: {
          query: {
            minDate: minTime.toISODate(),
            maxDate: minTime.toISODate(),
            siteId: siteId,
          },
        },
      })
    ),
  ])
  const allBlockUtilizations = allBlocksUtilizationResults.flatMap(
    (result) => result.data?.blockUtilizations || []
  )
  const validBlockTimes = blockTimesResult.data?.blockTimes.edges.filter(
    (e) => !e.node.block.archivedTime
  ) // excludes blocks with archivedTime

  const blockTimes =
    validBlockTimes.map((e) => {
      const interval = Interval.fromDateTimes(
        DateTime.fromISO(e.node.startTime, { zone: timezone }),
        DateTime.fromISO(e.node.endTime, { zone: timezone })
      )
      const releaseIntervals = e.node.releases
        ?.filter((r) => !r.unreleasedTime) // only consider active releases (unreleasedTime is null)
        .filter((r) => {
          // only count releases that were entered more than RELEASE_CUTOFF_THRESHOLD_IN_DAYS days from block start time
          const blockStartTime = DateTime.fromISO(e.node.startTime, {
            zone: timezone,
          })
          const releasedTime = DateTime.fromISO(r.releasedTime, {
            zone: timezone,
          })

          return (
            blockStartTime.diff(releasedTime, 'seconds').as('seconds') >
            RELEASE_CUTOFF_THRESHOLD_IN_DAYS * 24 * 60 * 60
          )
        })
        .map((r) => ({
          interval: Interval.fromDateTimes(
            DateTime.fromISO(r.startTime, { zone: timezone }),
            DateTime.fromISO(r.endTime, { zone: timezone })
          ),
        }))

      return {
        ...e.node,
        intervals: interval.difference(
          ...(releaseIntervals?.map((r) => r.interval) ?? [])
        ),
      }
    }) ?? []

  const blocks: ApellaBlock[] = []
  let overallActualCaseDuration = 0
  let overallBlockDuration = 0
  for (const blockUtilization of allBlockUtilizations) {
    const blockTimesForBlock = blockTimes.filter(
      (blockTime) => blockTime.blockId === blockUtilization.blockId
    )
    const block =
      blockTimesForBlock.length > 0
        ? blockTimesForBlock[0].block
        : { name: '', color: '', surgeonIds: [] }

    const today = DateTime.now().setZone(timezone)

    const utilizedSeconds =
      today.startOf('day') > minTime.startOf('day')
        ? blockUtilization.utilizedSeconds
        : blockUtilization.utilizedScheduledSeconds

    blocks.push({
      id: blockUtilization.blockId,
      name: block.name,
      color: block.color,
      surgeonIds: block.surgeonIds,
      blockTimes: blockTimesForBlock,
      utilizationPercentage:
        blockUtilization.availableSeconds > 0
          ? utilizedSeconds / blockUtilization.availableSeconds
          : undefined,
      caseIds: blockUtilization.casesForBlockDay.map(
        (caseToBlock) => caseToBlock?.caseId ?? ''
      ),
    })
    overallActualCaseDuration += utilizedSeconds
    overallBlockDuration += blockUtilization.availableSeconds
  }

  return {
    blocks,
    overallBlockUtilization:
      overallBlockDuration > 0
        ? overallActualCaseDuration / overallBlockDuration
        : undefined,
  }
}
