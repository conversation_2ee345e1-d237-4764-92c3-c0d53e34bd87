import { Placement } from '@floating-ui/react-dom'

import { Button, TV, Tooltip } from '@apella/component-library'

import { useDisplayMode } from '../../Contexts'

export const TvModeToggleButton = ({
  placement = 'bottom-end',
  disabled,
}: {
  placement?: Placement
  disabled?: boolean
}): React.JSX.Element => {
  const { isTvModeEnabled, toggleTvMode: onClick } = useDisplayMode()

  const buttonNode = (
    <Button
      color={'alternate'}
      onClick={onClick}
      buttonType="icon"
      disabled={disabled}
    >
      <TV size="sm" />
    </Button>
  )
  const tooltipNode =
    disabled || !onClick ? (
      buttonNode
    ) : (
      <Tooltip
        body={
          <span>
            {isTvModeEnabled ? 'Exit TV Mode.' : 'Enter TV Mode.\n'}
            <br />
            <br />
            For fullscreen mode, press F11 (Windows) or Ctrl+Cmd+F (Mac).
          </span>
        }
        strategy={isTvModeEnabled ? 'fixed' : undefined}
        placement={placement}
        isFloatingPortal={!isTvModeEnabled}
      >
        {buttonNode}
      </Tooltip>
    )
  return tooltipNode
}
