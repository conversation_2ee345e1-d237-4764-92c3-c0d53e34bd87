import { useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router'
import { toast } from 'react-toastify'

import { useMutation } from '@apollo/client'
import { Form, Formik } from 'formik'
import { DateTime } from 'luxon'

import {
  ApellaDateTimeFormats,
  Blade,
  Button,
  Caps2,
  Close,
  fontWeights,
  Input,
  mediaQueries,
  P3,
  remSpacing,
  Span3,
  theme,
} from '@apella/component-library'
import { LocationPath } from 'src/router/types'
import { useBladeState } from 'src/utils/useBladeState'

import { CaseType } from '../../__generated__/globalTypes'
import LoadingOverlay from '../../components/LoadingOverlay'
import { CaseStaffPlanFormValues } from '../../modules/planning/components/CaseStaffPlanForm'
import { generateCaseStaffPlanUpsertInputData } from '../../modules/planning/helpers'
import {
  UpsertCaseStaffPlan,
  UpsertCaseStaffPlanVariables,
} from '../../modules/planning/queries/__generated__'
import { UPSERT_CASE_STAFF_PLAN } from '../../modules/planning/queries/queries'
import { useCurrentUser } from '../../modules/user/hooks'
import { EVENTS, useAnalyticsEventLogger } from '../../utils/analyticsEvents'
import {
  CaseStaffPlanRole,
  toRoleShortForm,
  toStaffFullName,
} from '../../utils/roles'
import {
  BladeActionSnackbar,
  VideoBladeCaseDetails,
} from '../VideoBlade/VideoBlade'
import { onStaffError, StaffPlanningCard } from '../VideoBlade/VideoBladeCard'
import { useScheduleFilterProviderUrls } from './ScheduleFilterProvider'
import { TimelineRoom } from './types'

export interface BulkCaseStaffPlanFormValues extends CaseStaffPlanFormValues {
  caseIds: string[]
}

interface StaffPlanningBladeContentProps {
  date: string
  refetchCases: () => void
  room: TimelineRoom
}

export const StaffPlanningBladeContent = ({
  room,
  date,
  refetchCases,
}: StaffPlanningBladeContentProps) => {
  const eventLogger = useAnalyticsEventLogger()

  const siteId = room.site.id

  const { currentOrganization } = useCurrentUser()
  const orgId = currentOrganization?.node.id

  const { allSearchParams } = useScheduleFilterProviderUrls()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  const { isBladeOpen } = useBladeState({
    hardRefreshFallbackPath:
      LocationPath.Schedule + '?' + searchParams.toString(),
  })

  const handleClose = () => {
    if (isBladeOpen) {
      navigate({
        pathname: LocationPath.Schedule,
        search: allSearchParams.toString(),
      })
    }
  }

  const [upsertCaseStaffPlans, { loading: caseStaffLoading }] = useMutation<
    UpsertCaseStaffPlan,
    UpsertCaseStaffPlanVariables
  >(UPSERT_CASE_STAFF_PLAN, {
    onError: onStaffError,
    awaitRefetchQueries: true,
  })

  const cases = room.cases

  const onCaseStaffSubmit = useCallback(
    (values: BulkCaseStaffPlanFormValues) => {
      if (!siteId || !orgId) {
        return
      }
      const currentCspInput = cases
        .filter((c) => !!c.case && values.caseIds.includes(c.case?.id))
        .flatMap((c) => {
          return c.case?.staffPlan?.map((csp) => ({
            staff: csp,
            role: csp.role,
            caseId: c.case!.id,
            archivedTime: undefined,
            id: csp.rowId,
          }))
        })
        .filter(Boolean)

      const input = generateCaseStaffPlanUpsertInputData({
        caseIds: values.caseIds,
        caseStaffPlans: currentCspInput,
        values,
        siteId,
        orgId,
      })

      eventLogger(EVENTS.UPDATE_CASE_STAFF_ASSIGNMENTS, {
        caseIds: values.caseIds,
        siteId,
        orgId,
        values,
        from: 'Schedule',
      })

      upsertCaseStaffPlans({ variables: { input } }).then((v) => {
        if (v.data?.caseStaffPlanUpsert?.success === true) {
          toast.success('Changes saved successfully')
          refetchCases()
        }
      })
    },
    [refetchCases, cases, orgId, siteId, upsertCaseStaffPlans, eventLogger]
  )

  const defaultValues = Object.values(CaseStaffPlanRole).reduce(
    (accum, curr) => ({ ...accum, [curr]: [] }),
    {
      caseIds: room.cases
        .filter((ac) => ac.type === CaseType.FORECAST)
        .map((ac) => ac.case?.id)
        .filter(Boolean),
    }
  )

  return (
    <Blade
      isOpen={isBladeOpen}
      onClose={handleClose}
      size={'lg'}
      side={'right'}
      overlay={true}
    >
      <Blade.Header css={{ padding: `0 0 0 ${remSpacing.medium}` }}>
        <Blade.Title>
          <div css={{ padding: `${remSpacing.xsmall} 0 ` }}>
            <P3 css={{ ...fontWeights.semibold }}>{room.name}</P3>
            <P3 css={{ color: theme.palette.text.secondary }}>
              <Caps2>{room.site.name}</Caps2>
              <Span3
                css={{
                  marginLeft: remSpacing.xsmall,
                  marginRight: remSpacing.xsmall,
                }}
              >
                &#8226;
              </Span3>
              {DateTime.fromISO(date).toLocaleString(
                ApellaDateTimeFormats.DATE
              )}
            </P3>
          </div>
        </Blade.Title>

        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: remSpacing.small,
          }}
        >
          <Button
            color="black"
            appearance="link"
            onClick={handleClose}
            buttonType={'icon'}
          >
            <Close size={'sm'} />
          </Button>
        </div>
      </Blade.Header>

      <Blade.Body css={{ height: '100%' }}>
        {caseStaffLoading && <LoadingOverlay />}
        <Formik
          initialValues={defaultValues as BulkCaseStaffPlanFormValues}
          onSubmit={onCaseStaffSubmit}
          css={{ height: '100%' }}
        >
          {({ values, setFieldValue }) => {
            const handleCaseCheckboxClick = (caseId: string) => {
              let newVal
              if (values.caseIds.includes(caseId)) {
                newVal = values.caseIds.filter((cId) => cId !== caseId)
              } else {
                newVal = [...values.caseIds, caseId]
              }
              setFieldValue('caseIds', newVal)
            }

            const isAnyStaffPlan = Object.values(CaseStaffPlanRole).some(
              (role) => values[role] !== undefined && !!values[role].length
            )

            const isAnyCaseSelected = !!values.caseIds.length

            return (
              <Form css={{ height: '100%' }}>
                <div
                  css={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  <div
                    css={{
                      padding: `${remSpacing.medium} ${remSpacing.large}`,

                      display: 'grid',
                      columnGap: remSpacing.medium,

                      [mediaQueries.lg]: {
                        gridTemplateColumns: '1fr 1fr',
                      },
                    }}
                  >
                    <StaffPlanningCard
                      values={values}
                      setFieldValue={setFieldValue}
                      title={'Staff'}
                      editable={true}
                    />
                  </div>

                  <div
                    css={{
                      background: theme.palette.background.primary,
                      flex: 1,
                      overflow: 'auto',
                      paddingBottom: remSpacing.xxxxlarge,
                    }}
                  >
                    {room.cases
                      .filter((ac) => ac.type !== CaseType.COMPLETE)
                      .map((ac) => (
                        <label
                          key={ac.id}
                          htmlFor={`checkbox-${ac.id}`}
                          css={{
                            display: 'flex',
                            alignItems: 'start',
                            paddingBottom: remSpacing.gutter,
                            paddingTop: remSpacing.gutter,
                            cursor: 'pointer',
                            borderBottom: `1px solid ${theme.palette.gray[30]}`,
                          }}
                        >
                          <div style={{ marginLeft: remSpacing.gutter }}>
                            <Input.Checkbox
                              name={`checkbox-${ac.id}`}
                              ariaLabel="checkbox"
                              disabled={!ac.case}
                              value={
                                ac.case && values.caseIds.includes(ac.case.id)
                              }
                              onChange={() =>
                                ac.case && handleCaseCheckboxClick(ac.case.id)
                              }
                            />
                          </div>
                          <div
                            style={{
                              flexGrow: 1,
                              marginRight: remSpacing.gutter,
                            }}
                          >
                            <VideoBladeCaseDetails
                              activeApellaCase={ac}
                              roomId={room.id}
                              siteId={ac.site.id}
                              hasLiveCase={false}
                            />
                            <div css={{ display: 'flex' }}>
                              {ac.case?.staffPlan?.map((sp) => (
                                <div
                                  key={sp.id}
                                  css={{ marginRight: remSpacing.small }}
                                >
                                  <Caps2 css={{ ...fontWeights.semibold }}>
                                    {toStaffFullName({
                                      staff: sp,
                                      shorten: true,
                                    })}
                                    &nbsp;
                                  </Caps2>
                                  <Caps2
                                    css={{
                                      ...fontWeights.semibold,
                                      color: theme.palette.gray[60],
                                    }}
                                  >
                                    {toRoleShortForm(sp.role)}
                                  </Caps2>
                                </div>
                              ))}
                            </div>
                          </div>
                        </label>
                      ))}
                  </div>
                </div>

                <BladeActionSnackbar
                  submitText={'Update selected cases'}
                  disabled={!isAnyCaseSelected || !isAnyStaffPlan}
                />
              </Form>
            )
          }}
        </Formik>
      </Blade.Body>
    </Blade>
  )
}
