/**
 * Tests for date-aware room status filtering queries (RT-2172).
 * 
 * These tests verify that the GraphQL queries correctly pass date parameters
 * to both the room filtering and room status field resolvers.
 */

import { gql } from '@apollo/client'
import { GET_TIMELINE_CASE_DATA } from './queries'

describe('Date-aware room status filtering queries', () => {
  it('should include date parameters in rooms resolver', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''
    
    // Verify that the rooms resolver includes the date parameters
    expect(queryString).toContain('rooms(roomIds: $roomIds, statusFilter: $statusFilter, minEndTime: $minEndTime, maxStartTime: $maxStartTime)')
  })

  it('should include date parameters in room status field', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''
    
    // Verify that the status field includes the date parameters
    expect(queryString).toContain('status(minEndTime: $minEndTime, maxStartTime: $maxStartTime)')
  })

  it('should have consistent date parameter names', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''
    
    // Count occurrences of date parameters to ensure consistency
    const minEndTimeCount = (queryString.match(/minEndTime: \$minEndTime/g) || []).length
    const maxStartTimeCount = (queryString.match(/maxStartTime: \$maxStartTime/g) || []).length
    
    // Should appear in multiple places: rooms resolver, status field, apellaCases, turnovers
    expect(minEndTimeCount).toBeGreaterThan(1)
    expect(maxStartTimeCount).toBeGreaterThan(1)
  })

  it('should include required query variables', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''
    
    // Verify that the query declares the required variables
    expect(queryString).toContain('$minEndTime: DateTime!')
    expect(queryString).toContain('$maxStartTime: DateTime!')
    expect(queryString).toContain('$statusFilter: [RoomStatusName!]')
  })
})

describe('Query structure validation', () => {
  it('should have proper GraphQL syntax for rooms with date parameters', () => {
    // Test that we can parse a simplified version of the query
    const testQuery = gql`
      query TestQuery(
        $minEndTime: DateTime!
        $maxStartTime: DateTime!
        $statusFilter: [RoomStatusName!]
        $roomIds: [String!]
        $siteIds: [String!]
      ) {
        sites(siteIds: $siteIds) {
          edges {
            node {
              id
              rooms(
                roomIds: $roomIds
                statusFilter: $statusFilter
                minEndTime: $minEndTime
                maxStartTime: $maxStartTime
              ) {
                edges {
                  node {
                    id
                    name
                    status(
                      minEndTime: $minEndTime
                      maxStartTime: $maxStartTime
                    ) {
                      name
                      since
                    }
                  }
                }
              }
            }
          }
        }
      }
    `
    
    // Should not throw an error when parsing
    expect(testQuery).toBeDefined()
    expect(testQuery.kind).toBe('Document')
  })

  it('should maintain backward compatibility structure', () => {
    // Test that a query without date parameters still works
    const backwardCompatQuery = gql`
      query BackwardCompatQuery(
        $statusFilter: [RoomStatusName!]
        $roomIds: [String!]
        $siteIds: [String!]
      ) {
        sites(siteIds: $siteIds) {
          edges {
            node {
              id
              rooms(roomIds: $roomIds, statusFilter: $statusFilter) {
                edges {
                  node {
                    id
                    name
                    status {
                      name
                      since
                    }
                  }
                }
              }
            }
          }
        }
      }
    `
    
    // Should not throw an error when parsing
    expect(backwardCompatQuery).toBeDefined()
    expect(backwardCompatQuery.kind).toBe('Document')
  })
})

describe('Date parameter consistency across queries', () => {
  it('should use consistent parameter names across all date-aware fields', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''
    
    // Extract all date parameter usages
    const minEndTimeUsages = queryString.match(/minEndTime: \$\w+/g) || []
    const maxStartTimeUsages = queryString.match(/maxStartTime: \$\w+/g) || []
    
    // All should use the same variable names
    minEndTimeUsages.forEach(usage => {
      expect(usage).toBe('minEndTime: $minEndTime')
    })
    
    maxStartTimeUsages.forEach(usage => {
      expect(usage).toBe('maxStartTime: $maxStartTime')
    })
  })

  it('should pass date parameters to all relevant resolvers', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''
    
    // Should pass date parameters to:
    // 1. rooms resolver for filtering
    expect(queryString).toContain('rooms(roomIds: $roomIds, statusFilter: $statusFilter, minEndTime: $minEndTime, maxStartTime: $maxStartTime)')
    
    // 2. status field for consistent calculation
    expect(queryString).toContain('status(minEndTime: $minEndTime, maxStartTime: $maxStartTime)')
    
    // 3. apellaCases resolver (existing)
    expect(queryString).toContain('apellaCases(')
    expect(queryString).toMatch(/apellaCases\([^)]*minEndTime: \$minEndTime/)
    expect(queryString).toMatch(/apellaCases\([^)]*maxStartTime: \$maxStartTime/)
    
    // 4. turnovers resolver (existing)
    expect(queryString).toContain('turnovers(')
    expect(queryString).toMatch(/turnovers\([^)]*minEndTime: \$minEndTime/)
    expect(queryString).toMatch(/turnovers\([^)]*maxStartTime: \$maxStartTime/)
  })
})

describe('Real-world usage scenarios', () => {
  it('should support filtering closed rooms for past dates', () => {
    // This simulates the RT-2172 scenario: user navigates to a past date
    // and applies "Hide Closed Rooms" filter
    const testScenario = {
      variables: {
        minEndTime: '2025-04-22T00:00:00.000Z',
        maxStartTime: '2025-04-22T23:59:59.999Z',
        statusFilter: ['IDLE', 'IN_CASE', 'TURNOVER'], // Hide CLOSED rooms
        siteIds: ['test-site-id'],
        roomIds: null
      }
    }
    
    // Variables should be properly typed
    expect(typeof testScenario.variables.minEndTime).toBe('string')
    expect(typeof testScenario.variables.maxStartTime).toBe('string')
    expect(Array.isArray(testScenario.variables.statusFilter)).toBe(true)
  })

  it('should support filtering for specific room statuses on future dates', () => {
    // This simulates scheduling view for future dates
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 30)
    
    const testScenario = {
      variables: {
        minEndTime: futureDate.toISOString().split('T')[0] + 'T00:00:00.000Z',
        maxStartTime: futureDate.toISOString().split('T')[0] + 'T23:59:59.999Z',
        statusFilter: ['IDLE'], // Only show available rooms
        siteIds: ['test-site-id'],
        roomIds: null
      }
    }
    
    expect(testScenario.variables.statusFilter).toContain('IDLE')
  })

  it('should handle timezone-aware date parameters', () => {
    // Test with timezone-aware dates
    const testDate = new Date('2025-04-22T10:00:00-07:00') // PDT
    const startOfDay = new Date(testDate)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(testDate)
    endOfDay.setHours(23, 59, 59, 999)
    
    const testScenario = {
      variables: {
        minEndTime: startOfDay.toISOString(),
        maxStartTime: endOfDay.toISOString(),
        statusFilter: ['IN_CASE'],
        siteIds: ['test-site-id'],
        roomIds: null
      }
    }
    
    // Should handle ISO string format with timezone
    expect(testScenario.variables.minEndTime).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
    expect(testScenario.variables.maxStartTime).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
  })
})
