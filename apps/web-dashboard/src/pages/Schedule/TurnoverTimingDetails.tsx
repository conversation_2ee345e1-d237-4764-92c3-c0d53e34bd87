import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import {
  formatTimerWithSeconds,
  remSpacing,
  shape,
  ApellaDateTimeFormats,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'
import { TurnoverLengthWithGoal } from 'src/utils/status'

import { TurnoverType } from '../../__generated__/globalTypes'
import { EM_DASH } from '../../utils/htmlCodes'
import { useCurrentTime } from '../../utils/useCurrentTime'
import { Turnover } from '../types'
import { CaseTimingDatum } from './CaseDetails'

export const TurnoverTimingDetails = ({
  turnover,
}: {
  turnover?: Turnover
}) => {
  const theme = useTheme()
  const now = useCurrentTime()

  const elapsedTime =
    turnover?.type === TurnoverType.LIVE
      ? now.diff(turnover.startTime)
      : turnover?.type === TurnoverType.COMPLETE
        ? turnover?.startTime && turnover?.endTime.diff(turnover.startTime)
        : undefined

  const formatTime = useMemo(
    () => (elapsedTime ? formatTimerWithSeconds(elapsedTime) : EM_DASH),
    [elapsedTime]
  )
  const { timezone } = useTimezone()

  const sameTime =
    turnover?.scheduledEndTime?.minute === turnover?.endTime.minute
  return (
    <div css={{ display: 'flex', gap: remSpacing.small, alignItems: 'center' }}>
      {turnover?.goals?.goalMinutes &&
        turnover.overallLengthStatus != TurnoverLengthWithGoal.OVER_MAX && (
          <div
            css={{
              background: theme.palette.yellow[30],
              borderRadius: shape.borderRadius.xxsmall,
              padding: `${remSpacing.xsmall} ${remSpacing.small}`,
            }}
          >
            <CaseTimingDatum
              title={'Goal'}
              value={`${turnover?.goals?.goalMinutes}m`}
            />
          </div>
        )}
      <div>
        <CaseTimingDatum title={'Elapsed Time'} value={formatTime} />
      </div>
      <div
        css={{
          background: theme.palette.blue.background,
          borderRadius: shape.borderRadius.xxsmall,
          padding: `${remSpacing.xsmall} ${remSpacing.small}`,
          marginLeft: remSpacing.xsmall,
          display: 'flex',
          flexDirection: 'row',
          gap: remSpacing.small,
        }}
      >
        <div
          css={{
            borderRight: `1px solid ${theme.palette.gray[30]}`,
            paddingRight: remSpacing.small,
          }}
        >
          <CaseTimingDatum
            title={'Wheels out'}
            value={
              turnover?.startTime
                .setZone(timezone)
                .toLocaleString(ApellaDateTimeFormats.TIME) ?? EM_DASH
            }
          />
        </div>
        <div>
          <CaseTimingDatum
            title={'Wheels in'}
            value={
              turnover?.endTime
                ? turnover.endTime
                    .setZone(timezone)
                    .toLocaleString(ApellaDateTimeFormats.TIME)
                : EM_DASH
            }
            previousValue={
              turnover?.scheduledEndTime && !sameTime
                ? turnover?.scheduledEndTime
                    .setZone(timezone)
                    .toLocaleString(ApellaDateTimeFormats.TIME)
                : undefined
            }
          />
        </div>
      </div>
    </div>
  )
}
