import { useCallback } from 'react'

import { useTheme } from '@emotion/react'

import { Placement } from '@floating-ui/react-dom'

import {
  Button,
  ToggleOff,
  ToggleOn,
  Tooltip,
  Tune,
} from '@apella/component-library'

import { useScheduleFilterContext } from './ScheduleFilterContext'

export const FiltersToggleButton = ({
  placement = 'bottom-end',
}: {
  placement?: Placement
}): React.JSX.Element => {
  const { showFilters, onToggleFilters } = useScheduleFilterContext()
  const theme = useTheme()

  const onClick = useCallback(
    () => onToggleFilters(!showFilters),
    [onToggleFilters, showFilters]
  )

  const tooltipNode = (
    <Tooltip
      body={
        <div css={{ whiteSpace: 'nowrap' }}>
          {showFilters ? 'Hide filters' : 'Show filters'}
        </div>
      }
      strategy={showFilters ? 'fixed' : undefined}
      placement={placement}
      isFloatingPortal={!showFilters}
    >
      <Button
        color={showFilters ? 'active-gray' : 'alternate'}
        onClick={onClick}
        buttonType="icon"
        data-testid="filter-toggle"
      >
        {showFilters ? (
          <ToggleOn size="sm" color={theme.palette.blue[60]} />
        ) : (
          <ToggleOff size="sm" color={theme.palette.gray[60]} />
        )}{' '}
        <Tune size="sm" />
      </Button>
    </Tooltip>
  )

  return tooltipNode
}
