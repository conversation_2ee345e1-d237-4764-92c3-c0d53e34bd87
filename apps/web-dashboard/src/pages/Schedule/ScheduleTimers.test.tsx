import * as reactPageVisibility from 'react-page-visibility'
import * as ReactRouter from 'react-router'

import { ThemeProvider } from '@emotion/react'

import { InMemoryCache } from '@apollo/client'
import { MockedProvider } from '@apollo/client/testing'
import { act, render, screen } from '@testing-library/react'
import { Settings } from 'luxon'

import { theme } from '@apella/component-library'
import { SLOW_POLL_INTERVAL_MS } from 'src/pages/Live/consts'
import { SITES_KEY } from 'src/utils/useSitesState'

import { Schedule } from './Schedule'
import ScheduleMocks, {
  lab1CasePhaseResultsFunc,
} from './test_mocks/Schedule.mock'

// This allows us to override the values per test
const mockReactPageVisibility = reactPageVisibility as {
  usePageVisibility: () => boolean
}

// This has to be done out here, I'm not really a fan but it doesn't work in the before each section
vi.mock('react-page-visibility', () => {
  return {
    __esModule: true,
    usePageVisibility: () => true,
  }
})

vi.mock('react-router', async (importOriginal) => {
  const actual = await importOriginal<typeof ReactRouter>()
  return {
    ...actual,
    useLoaderData: () => ({
      blockUtilizationPromise: new Promise((resolve) =>
        setTimeout(
          () =>
            resolve({
              overallBlockUtilization: undefined,
              blocks: [],
            }),
          1000
        )
      ),
    }),
  }
})

const cache = new InMemoryCache()

// These tests are done separately from the main Schedule tests because use of fake timers
// Requires special usage with await, so it's best to just separate out the tests that rely on timers
describe('Apella schedule timers tests', () => {
  beforeEach(() => {
    const location = {
      ...window.location,
      search: `?${SITES_KEY}=%5B%22lab_1%22%5D`,
    }
    Object.defineProperty(window, 'location', {
      writable: true,
      value: location,
    })
    Settings.now = () => new Date(2023, 0, 6).valueOf()
    vi.useFakeTimers()
    vi.resetModules()
  })
  afterAll(() => {
    vi.resetModules()
    vi.useRealTimers()
  })
  it('Queries the backend regularly', async () => {
    render(
      <MockedProvider
        mocks={ScheduleMocks}
        addTypename={true}
        cache={cache}
        defaultOptions={{
          watchQuery: {
            fetchPolicy: 'no-cache',
          },
        }}
      >
        <ThemeProvider theme={theme}>
          <Schedule />
        </ThemeProvider>
      </MockedProvider>,
      { wrapper: ReactRouter.BrowserRouter }
    )
    await advanceFakeTimersByDoubleTheSlowPollInterval()
    expect(screen.getByText('Garage 0')).toBeInTheDocument()
    expect(screen.getAllByTestId('room-case-timeline-row')).toHaveLength(3)
    expect(lab1CasePhaseResultsFunc).toBeCalled()
    // Resetting the mock makes it simpler
    // for us to check if the periodic calls work
    lab1CasePhaseResultsFunc.mockRestore()
    expect(lab1CasePhaseResultsFunc).not.toBeCalled()
    await advanceFakeTimersByDoubleTheSlowPollInterval()
    expect(lab1CasePhaseResultsFunc).toBeCalled()
  })
  it("Doesn't query the backend when not visible", async () => {
    mockReactPageVisibility.usePageVisibility = () => false
    render(
      <MockedProvider
        mocks={ScheduleMocks}
        addTypename={true}
        cache={cache}
        defaultOptions={{
          watchQuery: {
            fetchPolicy: 'no-cache',
          },
        }}
      >
        <ThemeProvider theme={theme}>
          <Schedule />
        </ThemeProvider>
      </MockedProvider>,
      { wrapper: ReactRouter.BrowserRouter }
    )
    await advanceFakeTimersByDoubleTheSlowPollInterval()
    expect(screen.getByText('Garage 0')).toBeInTheDocument()
    expect(screen.getAllByTestId('room-case-timeline-row')).toHaveLength(3)
    expect(lab1CasePhaseResultsFunc).toBeCalled()

    lab1CasePhaseResultsFunc.mockRestore()
    await advanceFakeTimersByDoubleTheSlowPollInterval()
    expect(lab1CasePhaseResultsFunc).not.toBeCalled()
  })
})

async function advanceFakeTimersByDoubleTheSlowPollInterval() {
  await act(async () => {
    await vi.advanceTimersByTimeAsync(SLOW_POLL_INTERVAL_MS * 2)
  })
}
