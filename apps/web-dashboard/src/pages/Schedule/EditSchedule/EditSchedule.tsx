import { ComponentProps, useCallback, useEffect, useMemo } from 'react'
import { useNavigate, useSearchParams } from 'react-router'

import { useMutation } from '@apollo/client'
import styled from '@emotion/styled'

import {
  ApellaDateTimeFormats,
  ArrowBack,
  Blade,
  Button,
  Cancel,
  Caps3,
  CaseAssociation,
  Edit,
  FlexContainer,
  FlexItem,
  H2,
  H3,
  H5,
  H6,
  InputText,
  P3,
  P4,
  PlaceholderLabel,
  mediaQueries,
  remSpacing,
  shape,
  theme,
} from '@apella/component-library'
import { useLocalStorageState } from '@apella/hooks'
import { CaseMatchingStatus } from 'src/__generated__/globalTypes'
import { useScheduleContext } from 'src/pages/Schedule/ScheduleContextProvider'

import { LocationPath } from '../../../router/types'
import {
  ANESTHESIOLOGIST_ROLES,
  CRNA_ROLES,
  PRIMARY_SURGEON_ROLES,
} from '../../../utils/roles'
import { ApellaCase, Case } from '../../types'
import {
  PublishCaseMatchingEdits,
  PublishCaseMatchingEditsVariables,
} from '../__generated__'
import { PUBLISH_CASE_MATCHING_EDITS } from '../queries'
import { useScheduleFilterProviderUrls } from '../ScheduleFilterProvider'
import { TimelineRoom } from '../types'
import { CaseNode } from './CaseNode'
import { CurrentEdit, EditMode, OperationTypeProps, Source } from './types'

const InfoBox = ({ title, info }: { title: string; info: string }) => {
  return (
    <div
      css={{
        border: `1px solid ${theme.palette.gray[20]}`,
        borderRadius: shape.borderRadius.xsmall,
        padding: remSpacing.xsmall,
        display: 'grid',
        gap: remSpacing.xxsmall,
      }}
    >
      <Caps3 color={theme.palette.gray[60]}>{title}</Caps3>
      <P3
        css={{
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
        }}
      >
        {info}
      </P3>
    </div>
  )
}

const Header = ({
  onClose: onCloseProp,
  onBack,
  title,
}: {
  onClose: () => void
  onBack?: () => void
  title?: string
}) => {
  const navigate = useNavigate()

  const { allSearchParams } = useScheduleFilterProviderUrls()

  const onClose = useCallback(() => {
    onCloseProp()
    navigate({
      pathname: LocationPath.Schedule,
      search: allSearchParams.toString(),
    })
  }, [allSearchParams, navigate, onCloseProp])

  return (
    <Blade.Header>
      <Blade.Title
        icon={
          !!onBack ? (
            <Button
              onClick={onBack}
              appearance="link"
              buttonType="icon"
              color="alternate"
            >
              <ArrowBack size="sm" color={theme.palette.gray[60]} />
            </Button>
          ) : (
            <Edit size={'xs'} />
          )
        }
        title={title}
      />
      <Blade.CloseButton onClose={onClose} />
    </Blade.Header>
  )
}

const Body = ({ children, ...props }: ComponentProps<typeof FlexItem>) => (
  <FlexItem
    grow={0}
    shrink={1}
    css={{
      overflowY: 'auto',
    }}
    {...props}
  >
    {children}
  </FlexItem>
)

const Footer = ({ children, ...props }: ComponentProps<typeof FlexItem>) => (
  <FlexItem
    css={{
      padding: remSpacing.medium,
    }}
    {...props}
  >
    {children}
  </FlexItem>
)

const InfoBoxRow = styled.div({
  display: 'grid',
  gridAutoFlow: 'column',
  gridAutoColumns: 'minmax(0, 1fr)',
  gap: `${remSpacing.xsmall}`,
})

const SelectedPillNode = styled.div({
  padding: `${remSpacing.medium} ${remSpacing.gutter}`,
  border: `1px solid ${theme.palette.gray[20]}`,
  borderRadius: shape.borderRadius.xsmall,
  backgroundColor: theme.palette.gray.background,
  display: 'grid',
  gap: remSpacing.medium,
})
const SelectedCaseInfo = ({ scheduledCase }: { scheduledCase?: Case }) => {
  if (scheduledCase === undefined) {
    return null
  }

  const surgeons = scheduledCase.staff.filter((s) =>
    PRIMARY_SURGEON_ROLES.includes(s.role)
  )
  const anesthesiologists = scheduledCase.staff.filter((s) =>
    ANESTHESIOLOGIST_ROLES.includes(s.role)
  )
  const CRNAs = scheduledCase.staff.filter((s) => CRNA_ROLES.includes(s.role))

  return (
    <SelectedPillNode>
      <H6 as={H3}>Details</H6>
      <InfoBoxRow>
        <InfoBox
          title="Case ID"
          info={scheduledCase.externalCaseId || scheduledCase.id}
        />
        <InfoBox title="Room" info={scheduledCase.room.name} />
      </InfoBoxRow>
      <InfoBoxRow>
        <InfoBox
          title="Surgeon"
          info={
            surgeons.length
              ? surgeons.map((surgeon) => surgeon.displayName).join(', ')
              : '—'
          }
        />
        <InfoBox
          title="Anesthesiologist"
          info={
            anesthesiologists.length
              ? anesthesiologists
                  .map((anesthesiologist) => anesthesiologist.displayName)
                  .join(', ')
              : '—'
          }
        />
      </InfoBoxRow>
      <InfoBoxRow>
        <InfoBox
          title="Scheduled Start"
          info={scheduledCase.scheduledStartTime.toLocaleString(
            ApellaDateTimeFormats.TIME
          )}
        />
        <InfoBox
          title="Scheduled End"
          info={scheduledCase.scheduledEndTime.toLocaleString(
            ApellaDateTimeFormats.TIME
          )}
        />
      </InfoBoxRow>
      <InfoBoxRow>
        <InfoBox
          title="Procedure"
          info={scheduledCase.procedures.map((p) => p.name).join(', ')}
        />
        <InfoBox
          title="CRNA"
          info={
            CRNAs.length
              ? CRNAs.map((CRNA) => CRNA.displayName).join(', ')
              : '—'
          }
        />
      </InfoBoxRow>
    </SelectedPillNode>
  )
}

const SelectedPhaseInfo = ({ apellaCase }: { apellaCase?: ApellaCase }) => {
  const scheduledVsForecastedEnd = !!apellaCase?.actualPhase?.endTime
    ? 'Actual'
    : 'Forecasted'
  const selectedPhaseNode =
    apellaCase?.actualPhase !== undefined ? (
      <>
        <H6 as={H3}>Details </H6>
        <InfoBoxRow>
          <InfoBox title="Phase ID" info={apellaCase?.actualPhase.id} />
          <InfoBox title="Room" info={apellaCase.room.name} />
        </InfoBoxRow>
        <InfoBoxRow>
          <InfoBox
            title="Actual Start"
            info={apellaCase.startTime.toLocaleString(
              ApellaDateTimeFormats.TIME
            )}
          />
          <InfoBox
            title={`${scheduledVsForecastedEnd} End`}
            info={
              apellaCase.endTime?.toLocaleString(ApellaDateTimeFormats.TIME) ||
              '—'
            }
          />
        </InfoBoxRow>
      </>
    ) : (
      <H6 color={theme.palette.gray[60]} as={H3}>
        Select a phase
      </H6>
    )
  return <SelectedPillNode>{selectedPhaseNode}</SelectedPillNode>
}

const ExplanationOfTimeblockInput = ({
  explanationOfTimeblock,
  setExplanationOfTimeblock,
}: {
  explanationOfTimeblock: string
  setExplanationOfTimeblock: (newNote: string) => void
}) => {
  const explanationOfTimeblockChangeHandler = useCallback(
    (newNote: string) => {
      setExplanationOfTimeblock(newNote)
    },
    [setExplanationOfTimeblock]
  )

  return (
    <FlexContainer direction="column" css={{ padding: remSpacing.medium }}>
      <H6 as={H3}>Why are you making this change?</H6>
      <InputText
        name="note"
        label=""
        placeholder="Add a reason (optional)"
        multiline
        resize="vertical"
        value={explanationOfTimeblock}
        rows={3}
        onChange={(e) => {
          explanationOfTimeblockChangeHandler(e.target.value)
        }}
      />
    </FlexContainer>
  )
}

const OperationType = ({
  headerText,
  onClick,
  children,
  disabled = false,
}: OperationTypeProps) => {
  return (
    <div
      css={{
        padding: remSpacing.small,
        borderBottom: `1px solid ${theme.palette.gray[10]}`,
      }}
    >
      <Button
        color="alternate"
        appearance="link"
        disabled={disabled}
        css={{
          display: 'flex',
          textDecoration: 'none',
          justifyContent: 'flex-start',
          alignItems: 'center',
        }}
        onClick={onClick}
      >
        {children}
        <H6
          color={theme.palette.blue[50]}
          as={'span'}
          css={{ textAlign: 'left' }}
        >
          {headerText}
        </H6>
      </Button>
    </div>
  )
}

export const EditNode = () => {
  const {
    editScheduleState,
    setEditScheduleState,
    timelineState: { getCases, rooms },
  } = useScheduleContext()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  const filteredCases = useMemo(() => {
    const getRoomAndFilteredData: CurrentEdit[] = rooms
      .map((room: TimelineRoom) => room.cases)
      .flat()
      .filter(
        (c) =>
          c.case !== undefined &&
          c.case?.caseMatchingStatus !== CaseMatchingStatus.AUTOMATIC
      )
      .map((currCase: ApellaCase) => {
        return {
          externalCaseId: currCase.case?.externalCaseId || '',
          caseId: currCase.case ? currCase.case.id : '',
          matchingStatus:
            currCase.case && currCase.case.caseMatchingStatus
              ? currCase.case.caseMatchingStatus
              : CaseMatchingStatus.AUTOMATIC,
          phaseId: currCase.actualPhase?.id,
          explanationOfTimeblock: '',
          source: 'EXISTING',
          allCaseInfo: currCase.case,
        }
      })

    return getRoomAndFilteredData
  }, [rooms])

  useEffect(() => {
    if (
      editScheduleState.mode !== EditMode.PickCase &&
      !editScheduleState.activeEdit
    ) {
      setEditScheduleState({
        mode: EditMode.PickCase,
        currentEdits: editScheduleState.currentEdits,
        activeEdit: undefined,
      })
    } else if (
      editScheduleState.mode === EditMode.PickCase &&
      editScheduleState.activeEdit
    ) {
      setEditScheduleState({
        ...editScheduleState,
        activeEdit: undefined,
        allEdits: filteredCases,
      })
    }
  }, [editScheduleState, setEditScheduleState, filteredCases])

  const [explanationOfTimeblock, setExplanationOfTimeblock] =
    useLocalStorageState<string>('explanationOfTimeblock', '')
  const { currentEdits, mode, activeEdit, allEdits } = editScheduleState

  const instructionsText = currentEdits.length
    ? 'Publish changes or select another case to edit'
    : 'Select a case to edit'

  const setEditScheduleMode = useCallback(
    (newMode: EditMode) => {
      const newActiveEdit = !!editScheduleState.activeEdit
        ? {
            ...editScheduleState.activeEdit,
            newPhase: undefined,
          }
        : undefined
      setEditScheduleState({
        ...editScheduleState,
        mode: newMode,
        activeEdit: newActiveEdit,
        allEdits: editScheduleState.allEdits
          ? editScheduleState.allEdits
          : filteredCases,
        highlightedEdit: undefined,
      })
      setExplanationOfTimeblock('')

      setEditScheduleState({
        ...editScheduleState,
        mode: newMode,
        highlightedEdit: undefined,
      })
    },
    [
      editScheduleState,
      setEditScheduleState,
      setExplanationOfTimeblock,
      filteredCases,
    ]
  )

  const addActiveEdit = useCallback(
    (matchingStatus: CaseMatchingStatus) => {
      /**
       * If the user is editing a case that has already been edited,
       * we need to remove the case from the allEdits
       * so that it doesn't show up in the "All Edits" section.
       */
      let updatedAllEdits = undefined
      if (allEdits == undefined) {
        updatedAllEdits = filteredCases.filter(
          (c) => currentEdits.find((e) => e.caseId === c.caseId) === undefined
        )
      }
      if (activeEdit === undefined) {
        setEditScheduleState({
          ...editScheduleState,
          mode: EditMode.PickCase,
        })
        return
      }
      const createCurrentEdit = (
        source: Source,
        allEditsUpdated: CurrentEdit[] | undefined
      ) => {
        const curr = {
          caseId: activeEdit.scheduledCase.id,
          explanationOfTimeblock: explanationOfTimeblock,
          externalCaseId: activeEdit.scheduledCase?.externalCaseId || '',
          matchingStatus: matchingStatus,
          phaseId: activeEdit.newPhase?.actualPhase?.id,
          phaseCaseId: activeEdit.newPhase?.id,
          source: source,
          allCaseInfo: activeEdit.scheduledCase,
        }
        return {
          mode: EditMode.PickCase,
          currentEdits: [...currentEdits, curr],
          allEdits: allEditsUpdated,
        }
      }

      if (activeEdit?.source === 'MODIFIED') {
        updatedAllEdits = filteredCases.filter(
          (c) => c.caseId !== activeEdit.scheduledCase.id
        )
        setEditScheduleState(createCurrentEdit('MODIFIED', updatedAllEdits))
        setExplanationOfTimeblock('')
      } else {
        const edit = updatedAllEdits
          ? updatedAllEdits
          : editScheduleState.allEdits
        setEditScheduleState(createCurrentEdit('NEW', edit))
        setExplanationOfTimeblock('')
      }
    },
    [
      currentEdits,
      setEditScheduleState,
      editScheduleState,
      activeEdit,
      explanationOfTimeblock,
      setExplanationOfTimeblock,
      allEdits,
      filteredCases,
    ]
  )

  const iconStyles = {
    css: {
      marginRight: remSpacing.xsmall,
    },
    size: 'xs',
    color: theme.palette.blue[50],
  }

  const onPublishChangesComplete = useCallback(() => {
    getCases()
    setEditScheduleState({
      currentEdits: [],
      mode: EditMode.PickCase,
      allEdits: filteredCases,
    })
    setExplanationOfTimeblock('')
    navigate({
      pathname: LocationPath.Schedule,
      search: searchParams.toString(),
    })
  }, [
    getCases,
    navigate,
    setExplanationOfTimeblock,
    setEditScheduleState,
    searchParams,
    filteredCases,
  ])

  const [publishCaseChangesMutation, { loading: loadingMutation }] =
    useMutation<PublishCaseMatchingEdits, PublishCaseMatchingEditsVariables>(
      PUBLISH_CASE_MATCHING_EDITS,
      {
        onCompleted: onPublishChangesComplete,
      }
    )

  const clearCurrentEdits = useCallback(() => {
    /**
     * When clearing edits, modified cases will appear as automatic matches
     * a user clicks this button and only new edits will be removed.
     */
    editScheduleState.currentEdits.map((c) => {
      if (c.source === 'MODIFIED') {
        c.matchingStatus = CaseMatchingStatus.AUTOMATIC
      }
    })

    setEditScheduleState({
      ...editScheduleState,
      currentEdits: editScheduleState.currentEdits.filter(
        (c) => c.source !== 'NEW'
      ),
    })
  }, [editScheduleState, setEditScheduleState])

  const publishCaseMatchingChanges = useCallback(() => {
    publishCaseChangesMutation({
      variables: {
        input: editScheduleState.currentEdits.map((edit) => ({
          caseId: edit.caseId,
          caseMatchType: edit.matchingStatus,
          explanationForChange: edit.explanationOfTimeblock,
          phaseId: edit.phaseId,
        })),
      },
    })
  }, [editScheduleState.currentEdits, publishCaseChangesMutation])

  const changesInProgressNode =
    currentEdits.length > 0 ? (
      <P4 color={theme.palette.gray[50]} css={{ textAlign: 'center' }}>
        {currentEdits.length} change{currentEdits.length > 1 ? 's' : ''} in
        progress. Changes to cases will only affect Apella Dashboard and will
        not be reflected in your EMR.
      </P4>
    ) : null
  const canPublishChanges = currentEdits.length > 0 && !loadingMutation

  const currentEditsNode = canPublishChanges ? (
    <FlexContainer
      direction="column"
      gap={remSpacing.xsmall}
      css={{ marginTop: remSpacing.small, padding: remSpacing.xsmall }}
    >
      <H6 as={H3}>Pending Changes</H6>
      <CaseNode publishedEdits={currentEdits} isEditMode={true} />
    </FlexContainer>
  ) : null

  /**
   * Initially, when a user enters the edit schedule page the allEditNode will be displaying information
   * from filteredCases. Filtered cases are cases that have been published
   * and don't have automatic matching status.
   *
   * If they are editing a case (this is when there are new pending edits OR they've reverted a case) then the
   *  AllEditsNode will be displaying information from editScheduleState.allEdits.
   *
   * Nothing will be displayed if there are no filteredCases (meaning they haven't ever edited the schedule)
   * OR there are no allEdits (meaning they haven't made any changes in the current session)
   */
  let allEditNode = null
  if (filteredCases && !canPublishChanges && filteredCases.length > 0) {
    allEditNode = (
      <FlexContainer direction="column" gap={remSpacing.xsmall}>
        <H6 as={H3}>All Edits</H6>
        <CaseNode
          publishedEdits={filteredCases}
          isEditMode={true}
          isRestoreMode={true}
          allEdits={allEdits}
        />
      </FlexContainer>
    )
  } else if (
    editScheduleState.allEdits &&
    editScheduleState.allEdits?.length > 0
  ) {
    allEditNode = (
      <FlexContainer direction="column" gap={remSpacing.xsmall}>
        <H6 as={H3}>All Edits</H6>
        <CaseNode
          publishedEdits={editScheduleState.allEdits?.filter(
            (c) => c.source === 'EXISTING'
          )}
          isEditMode={true}
          isRestoreMode={true}
          allEdits={allEdits}
        />
      </FlexContainer>
    )
  }

  const backToSelectOperation = useCallback(() => {
    setEditScheduleMode(EditMode.SelectOperation)
  }, [setEditScheduleMode])

  const clearState = useCallback(() => {
    setEditScheduleState({ mode: EditMode.PickCase, currentEdits: [] })
    setExplanationOfTimeblock('')
  }, [setEditScheduleState, setExplanationOfTimeblock])

  switch (mode) {
    case EditMode.PickCase:
      return (
        <>
          <Header onClose={clearState} title="Edit Schedule" />
          <FlexItem
            css={{
              padding: `${remSpacing.medium} 0`,
              borderBottom: `1px solid ${theme.palette.gray[10]}`,
            }}
          >
            <FlexContainer
              css={{ minHeight: 'auto' }}
              justifyContent="center"
              alignItems="center"
            >
              <H6 color={theme.palette.text.tertiary} as={H3}>
                {instructionsText}
              </H6>
            </FlexContainer>
          </FlexItem>
          <Body grow={1}>
            {allEditNode}
            {currentEditsNode}
          </Body>
          <Footer>
            <FlexContainer
              justifyContent="center"
              direction="Column"
              gap={remSpacing.xsmall}
            >
              {changesInProgressNode}
              <Button
                color="alternate"
                css={{ borderRadius: shape.borderRadius.small }}
                onClick={clearCurrentEdits}
                disabled={!canPublishChanges}
              >
                <H5 as={H2}>Clear changes</H5>
              </Button>
              <Button
                css={{ borderRadius: shape.borderRadius.small }}
                onClick={publishCaseMatchingChanges}
                disabled={!canPublishChanges}
              >
                <H5 as={H2}>Publish changes</H5>
              </Button>
            </FlexContainer>
          </Footer>
        </>
      )
    case EditMode.SelectOperation:
      return (
        <>
          <Header
            onClose={clearState}
            onBack={() => {
              setEditScheduleMode(EditMode.PickCase)
            }}
            title="Edit Schedule"
          />
          <Body>
            <OperationType
              headerText="Case Association"
              onClick={() => setEditScheduleMode(EditMode.CaseAssociation)}
            >
              <CaseAssociation {...iconStyles} />
            </OperationType>
            <OperationType
              headerText="Label case as placeholder"
              disabled={
                activeEdit?.scheduledCase?.caseMatchingStatus ===
                CaseMatchingStatus.NOT_A_CASE
              }
              onClick={() => setEditScheduleMode(EditMode.LabelCasePlaceholder)}
            >
              <PlaceholderLabel {...iconStyles} />
            </OperationType>
            <OperationType
              headerText="Case canceled"
              disabled={
                activeEdit?.scheduledCase?.caseMatchingStatus ===
                CaseMatchingStatus.CANCELED
              }
              onClick={() => setEditScheduleMode(EditMode.CaseCanceled)}
            >
              <Cancel {...iconStyles} />
            </OperationType>
          </Body>
        </>
      )
    case EditMode.CaseAssociation:
      return (
        <>
          <Header
            onClose={clearState}
            onBack={backToSelectOperation}
            title="Case association"
          />
          <Body>
            <FlexContainer
              direction="column"
              css={{ padding: remSpacing.medium }}
              gap={remSpacing.xxsmall}
            >
              <H6 as={H3}>Associate this case information</H6>
              <SelectedCaseInfo scheduledCase={activeEdit?.scheduledCase} />
              <H6 as={H3}>with this phase</H6>
              <SelectedPhaseInfo apellaCase={activeEdit?.newPhase} />
            </FlexContainer>
            <ExplanationOfTimeblockInput
              explanationOfTimeblock={explanationOfTimeblock}
              setExplanationOfTimeblock={setExplanationOfTimeblock}
            />
          </Body>
          <Footer>
            <Button
              disabled={!activeEdit?.newPhase}
              onClick={() => {
                addActiveEdit(CaseMatchingStatus.OVERRIDE)
              }}
            >
              Done
            </Button>
          </Footer>
        </>
      )
    case EditMode.RevertToAutomatic:
    case EditMode.CaseCanceled:
    case EditMode.LabelCasePlaceholder:
      const headerTextLookup = {
        [EditMode.RevertToAutomatic]: 'Case reverted to automatic matching',
        [EditMode.CaseCanceled]: 'Case canceled',
        [EditMode.LabelCasePlaceholder]: 'Label case as placeholder',
      }
      const matchingStatusLookup = {
        [EditMode.RevertToAutomatic]: CaseMatchingStatus.AUTOMATIC,
        [EditMode.CaseCanceled]: CaseMatchingStatus.CANCELED,
        [EditMode.LabelCasePlaceholder]: CaseMatchingStatus.NOT_A_CASE,
      }

      return (
        <>
          <Header
            onClose={clearState}
            onBack={backToSelectOperation}
            title={headerTextLookup[mode]}
          />
          <Body>
            <ExplanationOfTimeblockInput
              explanationOfTimeblock={explanationOfTimeblock}
              setExplanationOfTimeblock={setExplanationOfTimeblock}
            />
          </Body>
          <Footer>
            <Button
              onClick={() => {
                addActiveEdit(matchingStatusLookup[mode])
              }}
            >
              Done
            </Button>
          </Footer>
        </>
      )
  }
}

export const EditSchedule = () => {
  return (
    <FlexContainer
      direction="column"
      css={{
        height: '100%',
        padding: `0`,
        borderTop: `solid 1px ${theme.palette.gray[10]}`,
        [mediaQueries.lg]: {
          borderLeft: `solid 1px ${theme.palette.gray[10]}`,
        },
      }}
    >
      <EditNode />
    </FlexContainer>
  )
}
