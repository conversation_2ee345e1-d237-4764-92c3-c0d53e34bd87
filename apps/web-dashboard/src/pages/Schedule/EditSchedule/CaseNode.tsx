import { useCallback, useEffect } from 'react'

import {
  remSpacing,
  theme,
  FlexContainer,
  shape,
  P3,
  Cancel,
  Button,
  Restore,
} from '@apella/component-library'
import { CaseMatchingStatus } from 'src/__generated__/globalTypes'
import { BoldedSpan } from 'src/components/InsightsTile/BoldedSpan'

import { useScheduleContext } from '../ScheduleContextProvider'
import { CurrentEdit, EditMode, Source } from './types'

export const CaseNode = ({
  publishedEdits: currentEdits,
  isEditMode,
  isRestoreMode,
  allEdits,
}: {
  publishedEdits?: CurrentEdit[]
  allEdits?: CurrentEdit[]
  isEditMode: boolean
  isRestoreMode?: boolean
}) => {
  /**
   * To clarify the language around some of the variables in this component:
   *
   * Current edit: These are the edits that aren't published yet and are under the
   * list of "Pending Changes" in the UI.
   *
   * Active Edit: These are the edits that are in the process of being edited by the user.
   * Essentially the editing state after a user clicks whether the case has been cancelled,
   * labeled as a placeholder, or unmatched and before a user clicks "Done".
   *
   * All Edit: This is the list of publish non-automatic edits that are in the "All Edits" section.
   */
  const { editScheduleState, setEditScheduleState } = useScheduleContext()

  const CurrentEditTextNode = ({
    externalCaseId,
    phaseId,
    matchingStatus,
    onClick,
  }: CurrentEdit) => {
    switch (matchingStatus) {
      case CaseMatchingStatus.CANCELED:
        return (
          <P3 onClick={onClick}>
            Case {externalCaseId} <BoldedSpan>Canceled</BoldedSpan>
          </P3>
        )
      case CaseMatchingStatus.NOT_A_CASE:
        return (
          <P3 onClick={onClick}>
            Case {externalCaseId}{' '}
            <BoldedSpan>labeled as placeholder</BoldedSpan>
          </P3>
        )
      case CaseMatchingStatus.OVERRIDE:
        return phaseId ? (
          <P3 onClick={onClick}>
            Case {externalCaseId} information{' '}
            <BoldedSpan>associated</BoldedSpan> to Phase {phaseId}
          </P3>
        ) : (
          <P3>
            Case {externalCaseId} <BoldedSpan>unmatched</BoldedSpan>
          </P3>
        )
      case CaseMatchingStatus.AUTOMATIC:
        return (
          <P3 onClick={onClick}>
            Case {externalCaseId} reverted to{' '}
            <BoldedSpan>automatic matching</BoldedSpan>
          </P3>
        )
    }
  }
  const removeCurrentEdit = useCallback(
    (idxToRemove: number) => {
      const newCurrentEdits = editScheduleState.currentEdits.filter(
        (_, idx) => idx !== idxToRemove
      )
      setEditScheduleState({
        ...editScheduleState,
        highlightedEdit: undefined,
        currentEdits: newCurrentEdits,
      })
    },
    [editScheduleState, setEditScheduleState]
  )

  const editPendingChange = useCallback(
    /**
     * there are two different types of pending changes: new edits and
     * reverted edits. To account for both cases, we need to check what
     * kind of current edit it is before changing it back to a pending edit.
     */
    (idx: number, source: Source, caseToUpdate: CurrentEdit) => {
      //If a user is editing an existing case that's in the "All Edits" section
      //We need to remove it from all edits and add it as a pending edit
      // return early
      if (!caseToUpdate.allCaseInfo) {
        return
      }
      editScheduleState.currentEdits.splice(idx, 1)
      const newState = {
        ...editScheduleState,
        mode: EditMode.SelectOperation,
        currentEdits: editScheduleState.currentEdits,
      }

      if (source === 'NEW') {
        setEditScheduleState({
          ...newState,
          activeEdit: {
            scheduledCase: caseToUpdate.allCaseInfo,
            source: 'NEW',
          },
          allEdits: editScheduleState.allEdits,
        })
      } else {
        setEditScheduleState({
          ...newState,
          activeEdit: {
            scheduledCase: caseToUpdate.allCaseInfo,
            source: 'MODIFIED',
          },
        })
      }
    },
    [editScheduleState, setEditScheduleState]
  )

  //when a user want to undo an edit that's in the "All Edits" section
  const revertAllEditBackToActiveEdit = useCallback(
    (idx: number) => {
      const currentEditCopy = currentEdits ? currentEdits[idx] : undefined
      if (
        (allEdits === undefined ||
          allEdits.length === 0 ||
          editScheduleState.currentEdits.length === 0) &&
        isRestoreMode == true
      ) {
        if (currentEdits && currentEditCopy) {
          currentEditCopy.source = 'MODIFIED'
          currentEditCopy.matchingStatus = CaseMatchingStatus.AUTOMATIC
        }
        setEditScheduleState({
          ...editScheduleState,
          mode: EditMode.PickCase,
          allEdits: currentEdits?.filter((_, index) => index !== idx),
          currentEdits:
            currentEdits && currentEditCopy
              ? [currentEditCopy, ...editScheduleState.currentEdits]
              : [...editScheduleState.currentEdits],
        })
      } else if (editScheduleState.allEdits !== undefined) {
        const editToAdd = {
          ...editScheduleState.allEdits[idx],
          source: 'MODIFIED' as Source,
          matchingStatus: CaseMatchingStatus.AUTOMATIC,
        }

        setEditScheduleState({
          ...editScheduleState,
          mode: EditMode.PickCase,
          currentEdits: [editToAdd, ...editScheduleState.currentEdits],
          allEdits: editScheduleState.allEdits?.filter(
            (_, index) => index !== idx
          ),
        })
      }
    },
    [
      editScheduleState,
      setEditScheduleState,
      allEdits,
      currentEdits,
      isRestoreMode,
    ]
  )
  useEffect(() => {
    if (
      editScheduleState.mode !== EditMode.PickCase &&
      !editScheduleState.activeEdit
    ) {
      setEditScheduleState({
        mode: EditMode.PickCase,
        currentEdits: editScheduleState.currentEdits,
        activeEdit: undefined,
        allEdits: allEdits,
      })
    } else if (
      editScheduleState.mode === EditMode.PickCase &&
      editScheduleState.activeEdit
    ) {
      setEditScheduleState({
        ...editScheduleState,
        activeEdit: undefined,
        allEdits: allEdits ? allEdits : editScheduleState.allEdits,
      })
    }
  }, [
    editScheduleState,
    setEditScheduleState,
    allEdits,
    currentEdits,
    isRestoreMode,
  ])

  return (
    <FlexContainer direction="column" gap={remSpacing.xsmall}>
      {currentEdits?.map((currentEdit, idx) => (
        <FlexContainer
          justifyContent="space-between"
          alignItems="center"
          gap={remSpacing.medium}
          css={{
            border: `1px solid ${theme.palette.gray[20]}`,
            borderRadius: shape.borderRadius.xsmall,
            padding: remSpacing.xsmall,
          }}
          key={currentEdit.caseId}
          onMouseEnter={() =>
            setEditScheduleState({
              ...editScheduleState,
              highlightedEdit: currentEdit,
            })
          }
          onMouseLeave={() =>
            setEditScheduleState({
              ...editScheduleState,
              highlightedEdit: undefined,
            })
          }
        >
          <CurrentEditTextNode
            {...currentEdit}
            onClick={() =>
              editPendingChange(idx, currentEdit.source, currentEdit)
            }
          />
          {isEditMode && !isRestoreMode ? (
            <Button
              appearance="link"
              color="gray"
              onClick={() => removeCurrentEdit(idx)}
            >
              <Cancel size="sm" />
            </Button>
          ) : (
            <Button
              appearance="link"
              color="gray"
              onClick={() => revertAllEditBackToActiveEdit(idx)}
            >
              <Restore size="sm" />
            </Button>
          )}
        </FlexContainer>
      ))}
    </FlexContainer>
  )
}
