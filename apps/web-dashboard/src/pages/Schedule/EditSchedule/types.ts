import { CaseMatchingStatus } from 'src/__generated__/globalTypes'
import { Case, ApellaCase } from 'src/pages/types'

export enum EditMode {
  PickCase = 'pick_case',
  SelectOperation = 'select_operation',
  CaseAssociation = 'case_association',
  LabelCasePlaceholder = 'label_case_as_placeholder',
  CaseCanceled = 'case_canceled',
  RevertToAutomatic = 'revert_to_automatic',
}
export type Source = 'NEW' | 'EXISTING' | 'MODIFIED'

export interface ActiveEdit {
  caseId?: string
  newPhase?: ApellaCase
  scheduledCase: Case
  source?: Source
}

export interface CurrentEdit {
  allCaseInfo?: Case
  caseId: string
  explanationOfTimeblock: string
  externalCaseId: string
  matchingStatus: CaseMatchingStatus
  onClick?: () => void
  phaseCaseId?: string
  phaseId?: string
  source: Source
}

export interface EditScheduleState {
  activeEdit?: ActiveEdit
  allEdits?: CurrentEdit[]
  currentEdits: CurrentEdit[]
  highlightedEdit?: CurrentEdit
  mode: EditMode
}

export interface EditScheduleSidebarProps {
  editScheduleState: EditScheduleState
  removeCurrentEdit: (caseId: string) => void
  setEditScheduleState: (state: EditScheduleState) => void
}

export interface SaveButtonProps {
  activeEdit: ActiveEdit | undefined
  saveCasePlaceholder?: () => void
}

export interface OperationTypeProps {
  children: React.ReactNode
  disabled?: boolean
  headerText: string
  onClick: () => void
}

export interface MatchCaseInput {
  caseId: string
  caseMatchType: CaseMatchingStatus
  explanationForChange: string
  phaseId?: string
}
