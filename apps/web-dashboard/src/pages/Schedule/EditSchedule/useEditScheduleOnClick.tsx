import { useCallback } from 'react'
import { toast } from 'react-toastify'

import { DateTime } from 'luxon'

import {
  SCHEDULE_TOAST_ID,
  ScheduleViewOnClickHandler,
} from 'src/pages/Schedule/types'
import { ApellaCase } from 'src/pages/types'

import { EditMode, EditScheduleState } from './types'

export const useEditScheduleOnClick = (
  editScheduleState: EditScheduleState,
  setEditScheduleState: (state: EditScheduleState) => void
): ScheduleViewOnClickHandler => {
  const pickCaseCallback = useCallback(
    (roomId: string, clickTime: DateTime, apellaCase?: ApellaCase) => {
      const findId = editScheduleState.currentEdits.find(
        (currEdit) => currEdit.caseId === apellaCase?.case?.id
      )
      if (findId) {
        toast.warn('Case is already being edited.', {
          toastId: SCHEDULE_TOAST_ID,
        })
      } else if (apellaCase?.case == undefined) {
        toast.warn('Please select a scheduled case to edit', {
          toastId: SCHEDULE_TOAST_ID,
        })
      } else {
        setEditScheduleState({
          mode: EditMode.SelectOperation,
          currentEdits: editScheduleState.currentEdits,
          activeEdit: { scheduledCase: apellaCase?.case },
        })
      }
    },
    [editScheduleState, setEditScheduleState]
  )
  const caseAssociationCallback = useCallback(
    (roomId: string, clickTime: DateTime, apellaCase?: ApellaCase) => {
      const findPhaseId = editScheduleState.currentEdits.find(
        (currEdit) => currEdit.phaseId === apellaCase?.actualPhase?.id
      )
      if (findPhaseId) {
        toast.warn('Phase is already being edited.', {
          toastId: SCHEDULE_TOAST_ID,
        })
        return
      }

      if (apellaCase?.actualPhase === undefined) {
        toast.warn('Please select an actual phase', {
          toastId: SCHEDULE_TOAST_ID,
        })
        return
      }
      if (editScheduleState.activeEdit === undefined) {
        setEditScheduleState({
          ...editScheduleState,
          mode: EditMode.PickCase,
        })
        return
      }
      if (editScheduleState.activeEdit.scheduledCase.room.id !== roomId) {
        toast.warn('Please select a phase from the same room', {
          toastId: SCHEDULE_TOAST_ID,
        })
        return
      }
      setEditScheduleState({
        ...editScheduleState,
        mode: EditMode.CaseAssociation,
        activeEdit: {
          ...editScheduleState.activeEdit,
          newPhase: apellaCase,
        },
      })
    },
    [editScheduleState, setEditScheduleState]
  )
  switch (editScheduleState.mode) {
    case EditMode.PickCase:
    case EditMode.SelectOperation:
      return pickCaseCallback
    case EditMode.CaseAssociation:
      return caseAssociationCallback
    case EditMode.RevertToAutomatic:
    case EditMode.LabelCasePlaceholder:
    case EditMode.CaseCanceled:
      return () => {}
  }
}
