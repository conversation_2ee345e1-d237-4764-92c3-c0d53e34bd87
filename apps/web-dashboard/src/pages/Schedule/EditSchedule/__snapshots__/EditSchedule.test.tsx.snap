// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Apella schedule > Test Edit Schedule 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0;
  height: 100%;
  padding: 0;
  border-top: solid 1px #f7f7f7;
}

@media only screen and (min-width: 1024px) {
  .emotion-0 {
    border-left: solid 1px #f7f7f7;
  }
}

.emotion-2 {
  border-bottom: 1px solid #f2f2f2;
  padding: 1rem;
  background: #FFF;
  display: grid;
  grid-auto-flow: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.5rem;
}

.emotion-6 {
  color: inherit;
  fill: currentcolor;
  height: 1rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-8 {
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.75rem;
  letter-spacing: -0.02em;
}

.emotion-10 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: rgba(0,0,0,0.7);
}

.emotion-10:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-10:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-10:active:not(:disabled) {
  background-color: #f2f2f2;
}

.emotion-11 {
  color: inherit;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-13 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  padding: 1rem 0;
  border-bottom: 1px solid #f7f7f7;
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0;
  min-height: auto;
}

.emotion-18 {
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 2rem;
  letter-spacing: -0.02em;
  color: rgba(0,0,0,0.4);
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  color: rgba(0,0,0,0.4);
}

.emotion-20 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  overflow-y: auto;
}

.emotion-22 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  padding: 1rem;
}

.emotion-24 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: Column;
  -ms-flex-direction: Column;
  flex-direction: Column;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0.5rem;
}

.emotion-26 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
  border-radius: 0.75rem;
}

.emotion-26:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-26:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-26:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-28 {
  font-weight: 600;
  font-size: 2.25rem;
  line-height: 3rem;
  letter-spacing: -0.03em;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
}

.emotion-30 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #006FFF;
  color: rgba(255,255,255,0.9);
  border-radius: 0.75rem;
}

.emotion-30:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-30:hover:not(:active):not(:disabled) {
  background-color: #338cff;
}

.emotion-30:active:not(:disabled) {
  background-color: #0059cc;
}

<body>
    <div>
      <div
        class="emotion-0 emotion-1"
        direction="column"
      >
        <div
          class="emotion-2 emotion-3"
        >
          <div
            class="emotion-4 emotion-1"
          >
            <svg
              class="emotion-6 emotion-7"
              viewBox="0 0 24 24"
            >
              <path
                clip-rule="evenodd"
                d="M3 17.4601V20.5001C3 20.7801 3.22 21.0001 3.5 21.0001H6.54C6.67 21.0001 6.8 20.9501 6.89 20.8501L17.81 9.94006L14.06 6.19006L3.15 17.1001C3.05 17.2001 3 17.3201 3 17.4601ZM20.71 7.04006C21.1 6.65006 21.1 6.02006 20.71 5.63006L18.37 3.29006C17.98 2.90006 17.35 2.90006 16.96 3.29006L15.13 5.12006L18.88 8.87006L20.71 7.04006Z"
                fill-rule="evenodd"
              />
            </svg>
            <h2
              class="emotion-8 emotion-9"
            >
              Edit Schedule
            </h2>
          </div>
          <button
            class="emotion-10"
          >
            <svg
              class="emotion-11 emotion-7"
              viewBox="0 0 24 24"
            >
              <path
                clip-rule="evenodd"
                d="M18.3 5.71021C17.91 5.32021 17.28 5.32021 16.89 5.71021L12 10.5902L7.10997 5.70021C6.71997 5.31021 6.08997 5.31021 5.69997 5.70021C5.30997 6.09021 5.30997 6.72021 5.69997 7.11021L10.59 12.0002L5.69997 16.8902C5.30997 17.2802 5.30997 17.9102 5.69997 18.3002C6.08997 18.6902 6.71997 18.6902 7.10997 18.3002L12 13.4102L16.89 18.3002C17.28 18.6902 17.91 18.6902 18.3 18.3002C18.69 17.9102 18.69 17.2802 18.3 16.8902L13.41 12.0002L18.3 7.11021C18.68 6.73021 18.68 6.09021 18.3 5.71021Z"
                fill-rule="evenodd"
              />
            </svg>
          </button>
        </div>
        <div
          class="emotion-13 emotion-14"
        >
          <div
            class="emotion-15 emotion-1"
          >
            <h3
              class="emotion-17 emotion-18 emotion-19"
              color="rgba(0,0,0,0.4)"
            >
              Select a case to edit
            </h3>
          </div>
        </div>
        <div
          class="emotion-20 emotion-14"
        />
        <div
          class="emotion-22 emotion-14"
        >
          <div
            class="emotion-24 emotion-1"
            direction="Column"
          >
            <button
              class="emotion-26"
              disabled=""
            >
              <h2
                class="emotion-27 emotion-28 emotion-29"
              >
                Clear changes
              </h2>
            </button>
            <button
              class="emotion-30"
              disabled=""
            >
              <h2
                class="emotion-27 emotion-28 emotion-29"
              >
                Publish changes
              </h2>
            </button>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0;
  height: 100%;
  padding: 0;
  border-top: solid 1px #f7f7f7;
}

@media only screen and (min-width: 1024px) {
  .emotion-0 {
    border-left: solid 1px #f7f7f7;
  }
}

.emotion-2 {
  border-bottom: 1px solid #f2f2f2;
  padding: 1rem;
  background: #FFF;
  display: grid;
  grid-auto-flow: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.5rem;
}

.emotion-6 {
  color: inherit;
  fill: currentcolor;
  height: 1rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-8 {
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.75rem;
  letter-spacing: -0.02em;
}

.emotion-10 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: rgba(0,0,0,0.7);
}

.emotion-10:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-10:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-10:active:not(:disabled) {
  background-color: #f2f2f2;
}

.emotion-11 {
  color: inherit;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-13 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  padding: 1rem 0;
  border-bottom: 1px solid #f7f7f7;
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0;
  min-height: auto;
}

.emotion-18 {
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 2rem;
  letter-spacing: -0.02em;
  color: rgba(0,0,0,0.4);
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  color: rgba(0,0,0,0.4);
}

.emotion-20 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  overflow-y: auto;
}

.emotion-22 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  padding: 1rem;
}

.emotion-24 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: Column;
  -ms-flex-direction: Column;
  flex-direction: Column;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0.5rem;
}

.emotion-26 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
  border-radius: 0.75rem;
}

.emotion-26:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-26:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-26:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-28 {
  font-weight: 600;
  font-size: 2.25rem;
  line-height: 3rem;
  letter-spacing: -0.03em;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
}

.emotion-30 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #006FFF;
  color: rgba(255,255,255,0.9);
  border-radius: 0.75rem;
}

.emotion-30:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-30:hover:not(:active):not(:disabled) {
  background-color: #338cff;
}

.emotion-30:active:not(:disabled) {
  background-color: #0059cc;
}

<div>
    <div
      class="emotion-0 emotion-1"
      direction="column"
    >
      <div
        class="emotion-2 emotion-3"
      >
        <div
          class="emotion-4 emotion-1"
        >
          <svg
            class="emotion-6 emotion-7"
            viewBox="0 0 24 24"
          >
            <path
              clip-rule="evenodd"
              d="M3 17.4601V20.5001C3 20.7801 3.22 21.0001 3.5 21.0001H6.54C6.67 21.0001 6.8 20.9501 6.89 20.8501L17.81 9.94006L14.06 6.19006L3.15 17.1001C3.05 17.2001 3 17.3201 3 17.4601ZM20.71 7.04006C21.1 6.65006 21.1 6.02006 20.71 5.63006L18.37 3.29006C17.98 2.90006 17.35 2.90006 16.96 3.29006L15.13 5.12006L18.88 8.87006L20.71 7.04006Z"
              fill-rule="evenodd"
            />
          </svg>
          <h2
            class="emotion-8 emotion-9"
          >
            Edit Schedule
          </h2>
        </div>
        <button
          class="emotion-10"
        >
          <svg
            class="emotion-11 emotion-7"
            viewBox="0 0 24 24"
          >
            <path
              clip-rule="evenodd"
              d="M18.3 5.71021C17.91 5.32021 17.28 5.32021 16.89 5.71021L12 10.5902L7.10997 5.70021C6.71997 5.31021 6.08997 5.31021 5.69997 5.70021C5.30997 6.09021 5.30997 6.72021 5.69997 7.11021L10.59 12.0002L5.69997 16.8902C5.30997 17.2802 5.30997 17.9102 5.69997 18.3002C6.08997 18.6902 6.71997 18.6902 7.10997 18.3002L12 13.4102L16.89 18.3002C17.28 18.6902 17.91 18.6902 18.3 18.3002C18.69 17.9102 18.69 17.2802 18.3 16.8902L13.41 12.0002L18.3 7.11021C18.68 6.73021 18.68 6.09021 18.3 5.71021Z"
              fill-rule="evenodd"
            />
          </svg>
        </button>
      </div>
      <div
        class="emotion-13 emotion-14"
      >
        <div
          class="emotion-15 emotion-1"
        >
          <h3
            class="emotion-17 emotion-18 emotion-19"
            color="rgba(0,0,0,0.4)"
          >
            Select a case to edit
          </h3>
        </div>
      </div>
      <div
        class="emotion-20 emotion-14"
      />
      <div
        class="emotion-22 emotion-14"
      >
        <div
          class="emotion-24 emotion-1"
          direction="Column"
        >
          <button
            class="emotion-26"
            disabled=""
          >
            <h2
              class="emotion-27 emotion-28 emotion-29"
            >
              Clear changes
            </h2>
          </button>
          <button
            class="emotion-30"
            disabled=""
          >
            <h2
              class="emotion-27 emotion-28 emotion-29"
            >
              Publish changes
            </h2>
          </button>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
