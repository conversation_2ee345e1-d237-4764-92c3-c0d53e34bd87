import { MemoryRouter, Outlet, Route, Routes } from 'react-router'

import { ThemeProvider } from '@emotion/react'

import { InMemoryCache } from '@apollo/client'
import { MockedProvider } from '@apollo/client/testing'
import { render } from '@testing-library/react'
import { Settings } from 'luxon'

import { theme } from '@apella/component-library'
import { EditMode } from 'src/pages/Schedule/EditSchedule/types'
import { useScheduleContext } from 'src/pages/Schedule/ScheduleContextProvider'
import { useScheduleFilterContext } from 'src/pages/Schedule/ScheduleFilterContext'
import { SITES_KEY } from 'src/utils/useSitesState'

import ScheduleMocks from '../test_mocks/Schedule.mock'
import { TimeRange } from '../types'
import { EditSchedule } from './EditSchedule'

vi.mock('src/pages/Schedule/ScheduleContextProvider')
const mockedUseScheduleContext = vi.mocked(useScheduleContext)

vi.mock('src/pages/Schedule/ScheduleFilterContext')
const mockedUseScheduleFilterContext = vi.mocked(useScheduleFilterContext)

const cache = new InMemoryCache()
describe('Apella schedule', () => {
  beforeAll(() => {
    mockedUseScheduleContext.mockReturnValue({
      editScheduleState: {
        currentEdits: [],
        mode: EditMode.PickCase,
      },
      onClick: () => {},
      setEditScheduleState: () => {},
      showPanel: true,
      isFullscreen: false,
      timelineState: {
        getCases: () => {},
        isLoading: false,
        maxTime: '',
        minTime: '',
        rooms: [],
      },
      filteredTimelineState: {
        getCases: () => {},
        isLoading: false,
        maxTime: '',
        minTime: '',
        rooms: [],
      },
      isEditingSchedule: false,
      turnoverLabels: [],
    })

    mockedUseScheduleFilterContext.mockReturnValue({
      sites: [],
      rooms: [],
      onChangeSurgeons: () => {},
      maxTime: '',
      minTime: '',
      showScheduled: true,
      onChangeDate: () => {},
      onChangeSites: () => {},
      onChangeRooms: () => {},
      onToggleScheduled: () => {},
      resetFilters: () => {},
      onChangeDailyMetric: () => {},
      onToggleShowClosedRooms: () => {},
      onToggleShowMetrics: () => {},
      showClosedRooms: true,
      showMetrics: true,
      sortKeys: [],
      onToggleSortKey: () => {},
      onToggleFilters: () => {},
      timeRange: TimeRange.DayBound,
    })
  })

  beforeEach(() => {
    localStorage.setItem(SITES_KEY, '["lab_1"]')
    Settings.now = () => new Date(2023, 0, 6).valueOf()
    vi.resetModules()
  })
  afterAll(() => {
    vi.resetModules()
  })
  it('Test Edit Schedule', async () => {
    const mock = render(
      <MockedProvider
        mocks={ScheduleMocks}
        addTypename={true}
        cache={cache}
        defaultOptions={{
          watchQuery: {
            fetchPolicy: 'no-cache',
          },
        }}
      >
        <ThemeProvider theme={theme}>
          <MemoryRouter initialEntries={['/schedule/edit']}>
            <Routes>
              <Route path="/schedule" element={<Outlet />}>
                <Route index path="/schedule/edit" element={<EditSchedule />} />
              </Route>
            </Routes>
          </MemoryRouter>
        </ThemeProvider>
      </MockedProvider>
    )
    expect(mock).toMatchSnapshot()
    expect(await mock.findByText('Edit Schedule')).toBeInTheDocument()
  })
})
