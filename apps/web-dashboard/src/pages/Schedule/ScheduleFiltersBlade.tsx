import { useState } from 'react'

import { useTheme } from '@emotion/react'

import { isEqual } from 'lodash'
import { rem } from 'polished'

import {
  Blade,
  Button,
  FlexContainer,
  H6,
  MultiSelect,
  Option,
  remSpacing,
  SelectToggleIcon,
} from '@apella/component-library'
import { useScheduleFilterContext } from 'src/pages/Schedule/ScheduleFilterContext'
import { ScheduleFilterOptions, TimeRange } from 'src/pages/Schedule/types'

import { SitesRoomsFilter } from '../../components/SitesRoomsFilter'
import { Staff } from '../types'
import { TimeRangeSelector } from './TimeRangeSelector'

export interface ScheduleFiltersBladeProps extends ScheduleFilterOptions {
  disabled?: boolean
  isBladeOpen: boolean
  onBladeClose: () => void
  surgeonOptions: Staff[]
}

export const ScheduleFiltersBlade = ({
  isBladeOpen,
  onBladeClose,
  ...props
}: ScheduleFiltersBladeProps) => {
  return (
    <Blade
      size="sm"
      side="right"
      isMobileOnly
      onClose={onBladeClose}
      isOpen={isBladeOpen}
    >
      <ScheduleFiltersBladeContent onBladeClose={onBladeClose} {...props} />
    </Blade>
  )
}

const ScheduleFiltersBladeContent = ({
  onBladeClose: onClose,
  surgeonOptions,
  disabled,
  ...options
}: Omit<ScheduleFiltersBladeProps, 'isBladeOpen' | 'showDateChangeFilter'>) => {
  const theme = useTheme()

  const {
    showSurgeonFilter = true,
    showToggleScheduledCases = true,
    showSiteFilter = true,
    showRoomFilter = true,
    showTimePeriodFilter = false,
  } = options

  const {
    rooms,
    sites,
    selectedSurgeons,
    roomIds: selectedRoomIds,
    onChangeSurgeons: onChangeSurgeons,
    showScheduled,
    onChangeSites,
    onChangeRooms: onChangeRoomIds,
    onToggleScheduled,
    timeRange,
    onChangeTimeRange,
    resetFilters: onResetFilters,
    siteIds: selectedSiteIds,
  } = useScheduleFilterContext()

  const [pendingSites, setPendingSites] = useState(selectedSiteIds)
  const [pendingRooms, setPendingRooms] = useState(selectedRoomIds)
  const [pendingSurgeons, setPendingSurgeons] = useState(selectedSurgeons)
  const [pendingScheduled, setPendingScheduled] = useState(showScheduled)
  const [pendingTimePeriod, setPendingTimePeriod] = useState(timeRange)

  const onReset = () => {
    onResetFilters()
    onClose()
  }

  const onApply = () => {
    if (!isEqual(pendingSites, selectedSiteIds) && onChangeSites) {
      onChangeSites(pendingSites)
    }
    if (!isEqual(pendingRooms, selectedRoomIds) && onChangeRoomIds) {
      onChangeRoomIds(pendingRooms)
    }
    if (!isEqual(pendingSurgeons, selectedSurgeons)) {
      onChangeSurgeons(pendingSurgeons)
    }
    if (pendingScheduled !== showScheduled) {
      onToggleScheduled()
    }

    if (!isEqual(pendingTimePeriod, timeRange) && onChangeTimeRange) {
      onChangeTimeRange(pendingTimePeriod ?? TimeRange.DayBound)
    }

    onClose()
  }

  const siteRoomProps = {
    sites: showSiteFilter ? sites : [],
    rooms: showRoomFilter ? rooms : [],
    selectedRoomIds: pendingRooms,
    onChangeRooms: setPendingRooms,
    bulkSelectRooms: true,
    disableSelectAllOption: false,
    bulkSelectSites: false,
    siteSelectProps: {
      inline: true,
      style: { dropdownWidth: '100%' },
    },
    roomSelectProps: {
      inline: true,
      style: { dropdownWidth: '100%' },
      displayLabel: !pendingRooms?.length
        ? 'All rooms'
        : pendingRooms.length === 1
          ? rooms?.find((room) => room.node.id === pendingRooms[0])?.node.name
          : `${pendingRooms.length} rooms selected`,
    },
    disabled,
  }

  return (
    <FlexContainer direction={'column'} css={{ height: '100%' }}>
      <Blade.Header>
        <Blade.Title title="Filters" />
        <Blade.CloseButton onClose={onClose} />
      </Blade.Header>
      <FlexContainer
        direction={'column'}
        gap={remSpacing.medium}
        css={{ padding: remSpacing.small, flex: '1 1', overflowY: 'auto' }}
      >
        {(!!showSiteFilter || !!showRoomFilter) && (
          <SitesRoomsFilter
            {...siteRoomProps}
            selectedSiteIds={pendingSites}
            onChangeSites={setPendingSites}
            multipleSites={true}
            disableSelectAllOption={false}
          />
        )}
        {!!showSurgeonFilter && !!surgeonOptions.length && (
          <MultiSelect
            name={'surgeon'}
            value={pendingSurgeons}
            onChange={setPendingSurgeons}
            label={'All surgeons'}
            bulkSelect={true}
            search={true}
            inline={true}
            displayLabel={
              !pendingSurgeons?.length
                ? 'All surgeons'
                : pendingSurgeons.length === 1
                  ? surgeonOptions?.find(
                      (surgeon) => surgeon.id === pendingSurgeons[0]
                    )?.displayName
                  : `${pendingSurgeons.length} surgeons selected`
            }
            style={{ dropdownWidth: '100%' }}
            disabled={disabled}
          >
            {surgeonOptions.map((p) => (
              <Option
                key={p.id}
                label={`${p.lastName}, ${p.firstName}`}
                value={p.id}
              />
            ))}
          </MultiSelect>
        )}
        {!!showTimePeriodFilter && !!pendingTimePeriod && (
          <TimeRangeSelector
            timeRange={pendingTimePeriod}
            onTimeRangeChange={setPendingTimePeriod}
          />
        )}
        {!!showToggleScheduledCases && (
          <Button
            onClick={() => setPendingScheduled((prev) => !prev)}
            appearance="link"
            buttonType="icon"
            color="black"
            css={{
              height: rem('40px'),
              justifyContent: 'flex-start',
            }}
          >
            <FlexContainer
              gap={remSpacing.small}
              css={{
                height: rem('36px'),
                alignItems: 'center',
              }}
            >
              <SelectToggleIcon
                type={pendingScheduled ? 'selected' : undefined}
                size={'sm'}
                data-testid={`checkbox-${
                  pendingScheduled ? 'checked' : 'unchecked'
                }`}
              />
              <H6>Scheduled</H6>
            </FlexContainer>
          </Button>
        )}
      </FlexContainer>
      <FlexContainer
        css={{
          borderTop: `1px solid ${theme.palette.gray[20]}`,
          padding: remSpacing.small,
          flex: '0 0',
        }}
        gap={remSpacing.xsmall}
      >
        <Button color={'alternate'} onClick={onReset} css={{ width: '100%' }}>
          Reset
        </Button>
        <Button css={{ width: '100%' }} onClick={onApply}>
          Apply
        </Button>
      </FlexContainer>
    </FlexContainer>
  )
}
