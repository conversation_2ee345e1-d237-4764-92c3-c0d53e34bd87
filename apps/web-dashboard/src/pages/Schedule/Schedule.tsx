import { Fragment, ReactNode, Suspense } from 'react'
import {
  Await,
  LoaderFunctionArgs,
  Outlet,
  useAsyncValue,
  useLoaderData,
  useLocation,
} from 'react-router'

import styled from '@emotion/styled'

import { mediaQueries, remSpacing } from '@apella/component-library'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { defaultTimezone } from 'src/Contexts'
import {
  ScheduleProvider,
  useScheduleContext,
} from 'src/pages/Schedule/ScheduleContextProvider'
import { getRouteContext, LocationPath } from 'src/router/types'
import { urlFriendlyDateToDateTime } from 'src/utils/urlFriendlyDate'

import { maxContainerSizeLookup } from '../../components/PageContentTemplate'
import { fetchBlockUtilizationData } from './fetchBlockUtilizationData'
import { getScheduleSearchParams } from './getScheduleSearchParams'
import { ScheduleFilterProviderContext } from './ScheduleFilterContext'
import { ScheduleFilterProvider } from './ScheduleFilterProvider'
import { ScheduleView } from './ScheduleView'

const FullscreenContainer = styled.div(() => ({
  padding: `0 ${remSpacing.gutter}`,
}))

const PanelContainer = styled.div({
  gridRow: 2,
  overflowY: 'auto',
  [mediaQueries.lg]: {
    gridRow: 1,
  },
})

const EditScheduleContainer = ({
  children,
  fluid,
}: {
  children?: ReactNode
  fluid?: boolean
}) => {
  return (
    <div
      css={{
        display: 'grid',
        width: '100%',
        height: '100%',
        padding: fluid
          ? '0'
          : `0 ${remSpacing.medium} ${remSpacing.medium} ${remSpacing.medium}`,
        [mediaQueries.lg]: {
          padding: fluid
            ? '0'
            : `0 ${remSpacing.gutter} ${remSpacing.gutter} ${remSpacing.gutter}`,
        },
      }}
    >
      <div
        css={{
          display: 'grid',
          width: '100%',
          justifySelf: 'center',
          height: '100%',
          minHeight: 0,
          gridAutoRows: 'minmax(0, 1fr)',
          gap: remSpacing.medium,
          maxWidth: maxContainerSizeLookup.full,
          [mediaQueries.lg]: {
            gap: remSpacing.gutter,
          },
        }}
      >
        {children}
      </div>
    </div>
  )
}

const SchedulePage = () => {
  const { showPanel, isFullscreen } = useScheduleContext()

  const { blocks } = useAsyncValue() as Awaited<
    Awaited<ReturnType<typeof loader>>['blockUtilizationPromise']
  >
  const ContainerEl = isFullscreen ? FullscreenContainer : EditScheduleContainer

  const Panel = showPanel ? PanelContainer : Fragment

  return (
    <ContainerEl fluid={showPanel}>
      <div
        css={{
          position: 'relative',
          width: '100%',
          '&[data-show-panel=true]': {
            display: 'grid',
            gridAutoRows: 'minmax(0,1fr)',
            [mediaQueries.lg]: {
              gridAutoFlow: 'column',
              gridTemplateColumns: '3fr 1fr',
            },
          },
        }}
        style={{ height: isFullscreen ? '100vh' : '100%' }}
        data-show-panel={showPanel}
      >
        <div
          css={{
            display: 'grid',
            justifySelf: 'center',
            gap: remSpacing.medium,
            gridAutoRows: 'max-content',
            gridTemplateColumns: 'minmax(0, 1fr)',
            position: 'relative',
            height: '100%',
            padding: 0,
            width: '100%',
            '&[data-show-panel=true]': {
              overflowY: 'auto',
              padding: `0 ${remSpacing.medium}`,
              [mediaQueries.lg]: {
                padding: `0 ${remSpacing.gutter}`,
              },
            },
            [mediaQueries.lg]: {
              gap: remSpacing.gutter,
            },
          }}
          data-show-panel={showPanel}
        >
          <ScheduleView blocks={blocks} />
        </div>
        <Panel>
          <Outlet />
        </Panel>
      </div>
    </ContainerEl>
  )
}

const ScheduleContextLookup: Partial<{
  [key: string]: ScheduleFilterProviderContext
}> = {
  [LocationPath.EditSchedule]: ScheduleFilterProviderContext.EDIT_SCHEDULE,
  [LocationPath.Schedule]: ScheduleFilterProviderContext.SCHEDULE,
}

export const loader = ({ request }: LoaderFunctionArgs, context: unknown) => {
  const { apolloClient, flags, user } = getRouteContext(context)
  const defaultPromise = Promise.resolve({
    overallBlockUtilization: undefined,
    blocks: [],
  })
  if (!flags?.blockTimeOnSchedulePageEnabled) {
    return { blockUtilizationPromise: defaultPromise }
  }

  const requestURL = new URL(request.url)
  const searchParams = new URLSearchParams(requestURL.search)
  const { selectedDate, selectedRoomIds, selectedSiteIds } =
    getScheduleSearchParams(searchParams)

  const timezone =
    user.currentOrganization?.node.sites.edges[0]?.node.timezone ||
    defaultTimezone

  const computedTime = urlFriendlyDateToDateTime(selectedDate)

  const minTime = computedTime
    .startOf('day')
    .setZone(timezone, { keepLocalTime: true })
  const maxTime = computedTime
    .endOf('day')
    .setZone(timezone, { keepLocalTime: true })

  const stringSiteIdsStore = window.localStorage.getItem(
    `${ScheduleFilterProviderContext.SCHEDULE}_SITE_IDS`
  )
  const siteIdsStore: string[] | undefined =
    stringSiteIdsStore && stringSiteIdsStore !== 'undefined'
      ? JSON.parse(stringSiteIdsStore)
      : undefined

  const siteIds = selectedSiteIds !== undefined ? selectedSiteIds : siteIdsStore

  const roomIds =
    !user.currentOrganization || !siteIds
      ? undefined
      : (selectedRoomIds ??
        siteIds
          .flatMap((siteId) =>
            user.currentOrganization?.node.sites.edges
              .find(({ node }) => node.id === siteId)
              ?.node.rooms.edges.map((e) => e.node.id)
          )
          .filter(Boolean))

  if (!roomIds || (roomIds && roomIds.length === 0)) {
    return { blockUtilizationPromise: defaultPromise }
  }
  return {
    blockUtilizationPromise: fetchBlockUtilizationData(apolloClient, {
      minTime,
      maxTime,
      siteIds: siteIds ?? [],
      timezone,
      roomIds: roomIds,
    }),
  }
}

export const Schedule = () => {
  const location = useLocation()
  const { blockUtilizationPromise } = useLoaderData<ReturnType<typeof loader>>()

  const context =
    ScheduleContextLookup[location.pathname] ??
    ScheduleFilterProviderContext.SCHEDULE

  return (
    <ScheduleFilterProvider context={context}>
      <ScheduleProvider>
        <Suspense fallback={<LoadingOverlay />}>
          <Await resolve={blockUtilizationPromise}>
            <SchedulePage />
          </Await>
        </Suspense>
      </ScheduleProvider>
    </ScheduleFilterProvider>
  )
}
