// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Apella schedule > Test Edit Schedule 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  display: grid;
  width: 100%;
  height: 100%;
  padding: 0;
}

@media only screen and (min-width: 1024px) {
  .emotion-0 {
    padding: 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  justify-self: center;
  height: 100%;
  min-height: 0;
  grid-auto-rows: minmax(0, 1fr);
  gap: 1rem;
}

@media only screen and (min-width: 1024px) {
  .emotion-1 {
    gap: 1.5rem;
  }
}

.emotion-2 {
  position: relative;
  width: 100%;
}

.emotion-2[data-show-panel=true] {
  display: grid;
  grid-auto-rows: minmax(0,1fr);
}

@media only screen and (min-width: 1024px) {
  .emotion-2[data-show-panel=true] {
    grid-auto-flow: column;
    grid-template-columns: 3fr 1fr;
  }
}

.emotion-3 {
  display: grid;
  justify-self: center;
  gap: 1rem;
  grid-auto-rows: max-content;
  grid-template-columns: minmax(0, 1fr);
  position: relative;
  height: 100%;
  padding: 0;
  width: 100%;
}

.emotion-3[data-show-panel=true] {
  overflow-y: auto;
  padding: 0 1rem;
}

@media only screen and (min-width: 1024px) {
  .emotion-3[data-show-panel=true] {
    padding: 0 1.5rem;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-3 {
    gap: 1.5rem;
  }
}

.emotion-4 {
  display: grid;
  gap: 1rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-template-areas: "title actions" "views views";
  width: 100%;
}

@media only screen and (min-width: 1024px) {
  .emotion-4 {
    padding-top: 0.5rem;
    grid-template-areas: "title views actions";
    grid-template-columns: max-content max-content auto;
  }
}

.emotion-5 {
  grid-area: title;
  overflow-x: auto;
}

.emotion-6 {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: max-content;
  gap: 0.5rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.emotion-8 {
  font-weight: 600;
  font-size: 3rem;
  line-height: 3.75rem;
  letter-spacing: -0.03em;
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.75rem;
  letter-spacing: -0.02em;
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: minmax(0, max-content);
  gap: 0.5rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow-x: hidden;
  line-height: 48px;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0;
  width: 100%;
  grid-area: views;
}

.emotion-10>[data-tertiary-view=false]:first-of-type {
  border-radius: 8px 0px 0px 8px;
}

@media only screen and (min-width: 1024px) {
  .emotion-10>[data-tertiary-view=false]:first-of-type {
    border-radius: 0px;
  }
}

.emotion-10>[data-tertiary-view=false]:nth-of-type(3) {
  border-radius: 0px 8px 8px 0px;
}

@media only screen and (min-width: 1024px) {
  .emotion-10>[data-tertiary-view=false]:nth-of-type(3) {
    border-radius: 0px;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-10 {
    width: auto;
  }
}

.emotion-12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  width: 100%;
  border-style: solid;
  border-width: 1px 0.5px 1px 0.5px;
  border-color: #e6e6e6;
  padding: 0 0.25rem;
  background-color: transparent;
}

.emotion-12[data-tertiary-view=true] {
  display: none;
}

@media only screen and (min-width: 1024px) {
  .emotion-12[data-tertiary-view=true] {
    display: grid;
    max-width: 200px;
  }
}

.emotion-12[aria-selected=true] {
  border-color: #006FFF;
  background-color: #006FFF;
}

@media only screen and (min-width: 1024px) {
  .emotion-12[aria-selected=true] {
    box-shadow: 0px -2px 0px 0px #006FFF inset;
    background-color: transparent;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-12[aria-selected=true][aria-disabled=true] {
    box-shadow: 0px -2px 0px 0px #bfbfbf inset;
    background-color: transparent;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-12[aria-selected=false]:hover {
    background-color: #cce2ff;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-12 {
    width: auto;
    display: grid;
    grid-auto-columns: minmax(0, max-content);
    grid-auto-flow: column;
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 0.25rem;
    cursor: default;
    padding: 0 0.5rem;
    border: none;
    background-color: transparent;
    border-radius: 0px;
  }
}

.emotion-13 {
  color: rgba(0,0,0,0.4);
}

.emotion-13[aria-selected=true] {
  color: rgba(255,255,255,0.9);
}

.emotion-13[aria-disabled=true] {
  color: #f7f7f7;
}

@media only screen and (min-width: 1024px) {
  .emotion-13[aria-selected=true] {
    color: rgba(0,0,0,0.9);
  }
}

.emotion-14 {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  text-align: center;
  line-height: 40px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.emotion-24 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  display: none;
  border-style: solid;
  border-width: 1px 0.5px 1px 0.5px;
  border-color: #e6e6e6;
  background-color: transparent;
  border-radius: 0px 8px 8px 0px;
}

@media only screen and (min-width: 1024px) {
  .emotion-24 {
    width: auto;
    cursor: default;
    padding: 0 0.5rem;
    border: none;
    background-color: transparent;
    border-radius: 0px;
    display: none;
  }
}

.emotion-26 {
  display: block;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media only screen and (min-width: 1024px) {
  .emotion-26 {
    cursor: pointer;
    border-style: none;
    border-width: 0px;
    background-color: white;
  }
}

.emotion-28 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: #999;
}

.emotion-28:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-28:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-28:active:not(:disabled) {
  background-color: #f7f7f7;
}

.emotion-29 {
  color: inherit;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-31 {
  display: grid;
  grid-area: actions;
  justify-self: end;
  grid-auto-flow: column;
  gap: 0.25rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: minmax(0, max-content);
}

.emotion-32 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
}

.emotion-32:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-32:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-32:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-35 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 1.5rem;
  position: relative;
  width: 100%;
}

@media only screen and (min-width: 1024px) {
  .emotion-35 {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    -webkit-justify-content: start;
    justify-content: start;
  }
}

.emotion-37 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0.5rem;
}

.emotion-42 {
  display: inline-block;
  position: relative;
}

.emotion-44 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: auto;
}

.emotion-44:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-44:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-44:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-45 {
  color: #006FFF;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-47 {
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5rem;
  color: rgba(0,0,0,0.7);
}

.emotion-52 {
  box-sizing: border-box;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.emotion-56 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
  width: 100%;
}

.emotion-56:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-56:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-56:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-57 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.emotion-60 {
  color: inherit;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  color: #999;
}

.emotion-68 {
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5rem;
  color: rgba(0,0,0,0.4);
}

.emotion-80 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: rgba(0,0,0,0.7);
  height: 2.5rem;
}

.emotion-80:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-80:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-80:active:not(:disabled) {
  background-color: #f2f2f2;
}

.emotion-81 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0.75rem;
  height: 2.25rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-85 {
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
}

.emotion-87 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 1rem;
  overflow-x: auto;
}

.emotion-89 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  border: 1px solid #e6e6e6;
  border-radius: 0.5rem;
  text-align: center;
  color: rgba(0,0,0,0.7);
  display: grid;
  padding: 1.5rem 0.75rem 0.75rem;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-91 {
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.75rem;
  letter-spacing: -0.02em;
}

.emotion-93 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1rem;
}

.emotion-95 {
  color: rgba(0,0,0,0.7);
}

.emotion-96 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.625rem;
  line-height: 1rem;
}

.emotion-98 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.625rem;
  line-height: 1rem;
}

.emotion-100 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  border: 1px solid #e6e6e6;
  border-radius: 0.5rem;
  text-align: center;
  color: rgba(0,0,0,0.7);
  display: grid;
  padding: 1.5rem 0.75rem 1.5rem;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-106 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 4px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 4px 8px;
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: #006FFF;
}

.emotion-106:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-106:hover:not(:active):not(:disabled) {
  background-color: #f2f8ff;
}

.emotion-106:active:not(:disabled) {
  background-color: #cce2ff;
}

.emotion-136 {
  padding-bottom: 10rem;
  pointer-events: auto;
}

.emotion-137 {
  overscroll-behavior: contain;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: relative;
  z-index: 0;
}

.emotion-138 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: auto;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  overflow: hidden;
  z-index: 2;
}

.emotion-138::-webkit-scrollbar {
  display: none;
}

.emotion-140 {
  background: #FFF;
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 1;
  box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.08),0px 3px 5px 0px rgba(0, 0, 0, 0.10);
  -webkit-flex: 0 0 115px;
  -ms-flex: 0 0 115px;
  flex: 0 0 115px;
  border-bottom: 1px solid #f2f2f2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media only screen and (min-width: 1024px) {
  .emotion-140 {
    box-shadow: none;
  }
}

.emotion-142 {
  display: inline-block;
}

.emotion-142>button:not(:first-of-type) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.emotion-142>button:not(:last-of-type) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.emotion-144 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 4px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 4px 8px;
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  background-color: #006FFF;
  color: rgba(255,255,255,0.9);
}

.emotion-144:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-144:hover:not(:active):not(:disabled) {
  background-color: #338cff;
}

.emotion-144:active:not(:disabled) {
  background-color: #0059cc;
}

.emotion-153 {
  background: #FFF;
  padding: 0 20px;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  min-width: 500px;
  border-bottom: 1px solid #f2f2f2;
}

.emotion-155 {
  height: 50px;
  padding-top: 8px;
  position: relative;
}

.emotion-158 {
  position: relative;
  left: -0.5px;
}

.emotion-159 {
  background: #999;
  height: 12px;
  width: 1px;
  margin-top: 20px;
}

.emotion-180 {
  height: 100%;
  position: absolute;
  width: 1px;
  pointer-events: none;
  top: 0.5rem;
  background: #006FFF;
}

.emotion-181 {
  position: absolute;
  top: 0;
  left: -30.5px;
  pointer-events: none;
  height: 16px;
  font-size: 0.625rem;
  color: #FFF;
  background: #006FFF;
  width: 60px;
  border-radius: 16px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-182 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: auto;
}

.emotion-182::-webkit-scrollbar {
  display: none;
}

.emotion-184 {
  background: #FFF;
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 1;
  box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.08),0px 3px 5px 0px rgba(0, 0, 0, 0.10);
  -webkit-flex: 0 0 115px;
  -ms-flex: 0 0 115px;
  flex: 0 0 115px;
  overflow: hidden;
  top: 0;
}

@media only screen and (min-width: 1024px) {
  .emotion-184 {
    box-shadow: none;
  }
}

.emotion-186 {
  height: 81px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  white-space: nowrap;
  background: #FFF;
  border-bottom: 1px solid #f2f2f2;
}

.emotion-187 {
  padding-left: 8px;
}

.emotion-188 {
  font-weight: 400;
  font-size: 0.8125rem;
  line-height: 1.25rem;
}

.emotion-190 {
  font-weight: 400;
  font-size: 0.6875rem;
  line-height: 1rem;
  color: rgba(0,0,0,0.4);
}

.emotion-192 {
  font-weight: 400;
  font-size: 0.6875rem;
  line-height: 1rem;
  background-color: #f2f2f2;
  border-radius: 0.25rem;
  padding: 0 0.25rem;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  font-weight: bold;
}

.emotion-194 {
  font-weight: normal;
}

.emotion-204 {
  font-weight: 400;
  font-size: 0.6875rem;
  line-height: 1rem;
  background-color: #f7ce6b;
  border-radius: 0.25rem;
  padding: 0 0.25rem;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  font-weight: bold;
}

.emotion-222 {
  background: #FFF;
  padding: 0 20px;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  min-width: 500px;
}

.emotion-224 {
  position: relative;
}

.emotion-225 {
  width: 100%;
  height: 81px;
  position: relative;
}

.emotion-225:after {
  content: "";
  display: block;
  height: 100%;
  margin: 0 -20px;
  border-bottom: 1px solid #f2f2f2;
}

.emotion-226 {
  position: absolute;
  cursor: default;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-228 {
  border-radius: 0.5rem;
  background-color: #e6e6e6;
  border: 0.0625rem solid #e6e6e6;
  width: 0;
  height: 44px;
  padding: 0.0625rem;
  background-color: #e6e6e6;
  z-index: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-230 {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: -4px;
  height: 42px;
  padding-left: 0.5rem;
  padding-top: 0.125rem;
}

.emotion-230::after {
  content: "";
  display: block;
}

.emotion-238 {
  border-radius: 0.5rem;
  background-color: #e6e6e6;
  border: 0.0625rem solid #e6e6e6;
  background-color: #e6e6e6;
  z-index: 1;
}

.emotion-240 {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: -2px;
  height: 14px;
}

.emotion-240::after {
  content: "";
  display: block;
}

.emotion-242 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.625rem;
  line-height: 1rem;
  line-height: 1.5;
  display: block;
  padding-left: 0.5rem;
}

.emotion-302 {
  border-radius: 0.5rem;
  background-color: #FFF;
  border: 0.0625rem dashed #bfbfbf;
  width: 0;
  height: 44px;
  padding: 0.0625rem;
  background-color: #FFF;
  z-index: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-312 {
  border-radius: 0.625rem;
  background-color: #f2f2f2;
  border: 0.0625rem solid #e6e6e6;
  background-color: #f2f2f2;
  z-index: 1;
}

.emotion-508 {
  height: 100%;
  position: absolute;
  width: 1px;
  pointer-events: none;
  top: 0;
  background: #006FFF;
}

.emotion-509 {
  position: fixed;
  z-index: 2;
  bottom: 1.5rem;
  right: 1.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-510 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 1rem;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFF;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.1),0px 1px 18px rgba(0, 0, 0, 0.06),0px 6px 10px rgba(0, 0, 0, 0.08);
  padding: 1rem;
  border: 0.0625rem solid 1px solid #e6e6e6;
  border-radius: 0.5rem;
  max-width: 750px;
}

.emotion-512 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.5rem;
}

.emotion-514 {
  color: #999;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-516 {
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
}

.emotion-518 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 1rem;
}

.emotion-520 {
  border-radius: 0.5rem;
  background-color: #72c8c8;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-522 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.625rem;
  line-height: 1rem;
}

.emotion-524 {
  border-radius: 0.5rem;
  background-color: #8CB860;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-528 {
  border-radius: 0.5rem;
  background-color: #A1F98E;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-532 {
  border-radius: 0.5rem;
  background-color: #C0997B;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-534 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.625rem;
  line-height: 1rem;
  color: rgba(0,0,0,0.9);
}

.emotion-536 {
  border-radius: 0.5rem;
  background-color: #FCE995;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-540 {
  border-radius: 0.5rem;
  background-color: #EF8633;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-544 {
  border-radius: 0.5rem;
  background-color: #B1D5E8;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-548 {
  border-radius: 0.5rem;
  background-color: #F19EFA;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-552 {
  border-radius: 0.5rem;
  background-color: #e6e6e6;
  border: 0.0625rem solid #e6e6e6;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-556 {
  border-radius: 0.625rem;
  background-color: #f2f2f2;
  border: 0.0625rem solid #e6e6e6;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-560 {
  border-radius: 0.125rem;
  background-color: #FFF;
  border: 0.0625rem dashed #bfbfbf;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-564 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: rgba(0,0,0,0.7);
}

.emotion-564:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-564:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-564:active:not(:disabled) {
  background-color: #f2f2f2;
}

.emotion-565 {
  color: #bfbfbf;
  fill: currentcolor;
  height: 1rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-567 {
  grid-row: 2;
  overflow-y: auto;
}

@media only screen and (min-width: 1024px) {
  .emotion-567 {
    grid-row: 1;
  }
}

.emotion-569 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0;
  height: 100%;
  padding: 0;
  border-top: solid 1px #f7f7f7;
}

@media only screen and (min-width: 1024px) {
  .emotion-569 {
    border-left: solid 1px #f7f7f7;
  }
}

.emotion-571 {
  border-bottom: 1px solid #f2f2f2;
  padding: 1rem;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  background: #FFF;
  display: grid;
  grid-auto-flow: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-575 {
  color: inherit;
  fill: currentcolor;
  height: 1rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-577 {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
}

.emotion-582 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  padding: 1rem 0;
  border-bottom: 1px solid #f7f7f7;
}

.emotion-584 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0;
  min-height: auto;
}

.emotion-587 {
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 2rem;
  letter-spacing: -0.02em;
  color: rgba(0,0,0,0.4);
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  color: rgba(0,0,0,0.4);
}

.emotion-589 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  overflow-y: auto;
}

.emotion-591 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  padding: 1rem;
}

.emotion-593 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: Column;
  -ms-flex-direction: Column;
  flex-direction: Column;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0.5rem;
}

.emotion-595 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
  border-radius: 0.75rem;
}

.emotion-595:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-595:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-595:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-597 {
  font-weight: 600;
  font-size: 2.25rem;
  line-height: 3rem;
  letter-spacing: -0.03em;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
}

.emotion-599 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #006FFF;
  color: rgba(255,255,255,0.9);
  border-radius: 0.75rem;
}

.emotion-599:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-599:hover:not(:active):not(:disabled) {
  background-color: #338cff;
}

.emotion-599:active:not(:disabled) {
  background-color: #0059cc;
}

<body>
    <div>
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            data-show-panel="true"
            style="height: 100%;"
          >
            <div
              class="emotion-3"
              data-show-panel="true"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="emotion-5"
                >
                  <div
                    class="emotion-6"
                  >
                    <h1
                      class="emotion-7 emotion-8 emotion-9"
                    >
                      Schedule
                    </h1>
                  </div>
                </div>
                <div
                  class="emotion-10 emotion-11"
                >
                  <div
                    aria-disabled="true"
                    aria-selected="true"
                    class="emotion-12"
                    data-tertiary-view="false"
                  >
                    <a
                      aria-disabled="true"
                      aria-selected="true"
                      class="emotion-13"
                      href="/schedule?sites=%5B%22lab_1%22%5D"
                      style="text-decoration: none; pointer-events: none;"
                    >
                      <h5
                        class="emotion-14 emotion-15"
                      >
                        OR
                      </h5>
                    </a>
                  </div>
                  <div
                    aria-disabled="true"
                    aria-selected="false"
                    class="emotion-12"
                    data-tertiary-view="false"
                  >
                    <a
                      aria-disabled="true"
                      aria-selected="false"
                      class="emotion-13"
                      href="/schedule/pre-op?sites=%5B%22lab_1%22%5D"
                      style="text-decoration: none; pointer-events: none;"
                    >
                      <h5
                        class="emotion-14 emotion-15"
                      >
                        PreOp
                      </h5>
                    </a>
                  </div>
                  <div
                    aria-disabled="true"
                    aria-selected="false"
                    class="emotion-12"
                    data-tertiary-view="false"
                  >
                    <a
                      aria-disabled="true"
                      aria-selected="false"
                      class="emotion-13"
                      href="/schedule/post-op?sites=%5B%22lab_1%22%5D"
                      style="text-decoration: none; pointer-events: none;"
                    >
                      <h5
                        class="emotion-14 emotion-15"
                      >
                        PostOp
                      </h5>
                    </a>
                  </div>
                  <div
                    class="emotion-24 emotion-25"
                    data-tertiary-view="false"
                  >
                    <div
                      class="emotion-26 emotion-27"
                    >
                      <button
                        aria-label="More views"
                        class="emotion-28"
                        type="button"
                      >
                        <svg
                          class="emotion-29 emotion-30"
                          viewBox="0 0 24 24"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M6 10C4.9 10 4 10.9 4 12C4 13.1 4.9 14 6 14C7.1 14 8 13.1 8 12C8 10.9 7.1 10 6 10ZM18 10C16.9 10 16 10.9 16 12C16 13.1 16.9 14 18 14C19.1 14 20 13.1 20 12C20 10.9 19.1 10 18 10ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-31"
                >
                  <button
                    class="emotion-32"
                    disabled=""
                  >
                    <svg
                      class="emotion-29 emotion-30"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M6 14C5.45 14 5 14.45 5 15V18C5 18.55 5.45 19 6 19H9C9.55 19 10 18.55 10 18C10 17.45 9.55 17 9 17H7V15C7 14.45 6.55 14 6 14ZM6 10C6.55 10 7 9.55 7 9V7H9C9.55 7 10 6.55 10 6C10 5.45 9.55 5 9 5H6C5.45 5 5 5.45 5 6V9C5 9.55 5.45 10 6 10ZM17 17H15C14.45 17 14 17.45 14 18C14 18.55 14.45 19 15 19H18C18.55 19 19 18.55 19 18V15C19 14.45 18.55 14 18 14C17.45 14 17 14.45 17 15V17ZM14 6C14 6.55 14.45 7 15 7H17V9C17 9.55 17.45 10 18 10C18.55 10 19 9.55 19 9V6C19 5.45 18.55 5 18 5H15C14.45 5 14 5.45 14 6Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div
                class="emotion-35 emotion-11"
                wrap="wrap"
              >
                <div
                  class="emotion-37 emotion-11"
                >
                  <div>
                    <button
                      class="emotion-32"
                    >
                      <svg
                        class="emotion-29 emotion-30"
                        viewBox="0 0 24 24"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M14.71 6.70998C14.32 6.31998 13.69 6.31998 13.3 6.70998L8.70998 11.3C8.31998 11.69 8.31998 12.32 8.70998 12.71L13.3 17.3C13.69 17.69 14.32 17.69 14.71 17.3C15.1 16.91 15.1 16.28 14.71 15.89L10.83 12L14.71 8.11998C15.1 7.72998 15.09 7.08998 14.71 6.70998Z"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                  <div
                    class="emotion-42 emotion-27"
                  >
                    <button
                      class="emotion-44"
                      type="button"
                    >
                      <svg
                        class="emotion-45 emotion-30"
                        color="#006FFF"
                        viewBox="0 0 24 24"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M20 3H19V2C19 1.45 18.55 1 18 1C17.45 1 17 1.45 17 2V3H7V2C7 1.45 6.55 1 6 1C5.45 1 5 1.45 5 2V3H4C2.9 3 2 3.9 2 5V21C2 22.1 2.9 23 4 23H20C21.1 23 22 22.1 22 21V5C22 3.9 21.1 3 20 3ZM19 21H5C4.45 21 4 20.55 4 20V8H20V20C20 20.55 19.55 21 19 21Z"
                          fill-rule="evenodd"
                        />
                      </svg>
                      <span
                        class="emotion-47 emotion-48"
                        color="rgba(0,0,0,0.7)"
                      >
                        01/06/23
                      </span>
                    </button>
                  </div>
                  <div>
                    <button
                      class="emotion-32"
                    >
                      <svg
                        class="emotion-29 emotion-30"
                        viewBox="0 0 24 24"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M9.2925 6.29376C8.9025 6.68376 8.9025 7.31376 9.2925 7.70376L13.1725 11.5838L9.2925 15.4638C8.9025 15.8538 8.9025 16.4838 9.2925 16.8738C9.6825 17.2638 10.3125 17.2638 10.7025 16.8738L15.2925 12.2838C15.6825 11.8938 15.6825 11.2638 15.2925 10.8738L10.7025 6.28376C10.3225 5.90376 9.6825 5.90376 9.2925 6.29376Z"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
                <div
                  class="emotion-52 emotion-53"
                >
                  <div
                    class="emotion-42 emotion-27"
                  >
                    <div>
                      <button
                        aria-label="All sites-filter"
                        class="emotion-56"
                        type="button"
                      >
                        <div
                          class="emotion-57"
                        >
                          <span
                            class="emotion-47 emotion-48"
                            color="rgba(0,0,0,0.7)"
                          >
                            Apella Lab
                          </span>
                          <svg
                            class="emotion-60 emotion-30"
                            viewBox="0 0 24 24"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7.41289 11.3815L11.0199 14.9885C11.563 15.5316 12.4404 15.5316 12.9835 14.9885L16.5905 11.3815C17.4679 10.5041 16.8412 9 15.6017 9H8.38776C7.14829 9 6.53552 10.5041 7.41289 11.3815Z"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-52 emotion-53"
                >
                  <div
                    class="emotion-42 emotion-27"
                  >
                    <button
                      aria-label="All rooms-filter"
                      class="emotion-56"
                      type="button"
                    >
                      <div
                        class="emotion-57"
                      >
                        <span
                          class="emotion-68 emotion-48"
                          color="rgba(0,0,0,0.4)"
                        >
                          All rooms
                        </span>
                        <svg
                          class="emotion-60 emotion-30"
                          viewBox="0 0 24 24"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M7.41289 11.3815L11.0199 14.9885C11.563 15.5316 12.4404 15.5316 12.9835 14.9885L16.5905 11.3815C17.4679 10.5041 16.8412 9 15.6017 9H8.38776C7.14829 9 6.53552 10.5041 7.41289 11.3815Z"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </div>
                    </button>
                  </div>
                </div>
                <div
                  class="emotion-42 emotion-27"
                >
                  <button
                    aria-label="surgeon"
                    class="emotion-56"
                    type="button"
                  >
                    <div
                      class="emotion-57"
                    >
                      <span
                        class="emotion-68 emotion-48"
                        color="rgba(0,0,0,0.4)"
                      >
                        All surgeons
                      </span>
                      <svg
                        class="emotion-60 emotion-30"
                        viewBox="0 0 24 24"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M7.41289 11.3815L11.0199 14.9885C11.563 15.5316 12.4404 15.5316 12.9835 14.9885L16.5905 11.3815C17.4679 10.5041 16.8412 9 15.6017 9H8.38776C7.14829 9 6.53552 10.5041 7.41289 11.3815Z"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </div>
                  </button>
                </div>
                <button
                  class="emotion-80"
                  disabled=""
                >
                  <div
                    class="emotion-81 emotion-11"
                  >
                    <svg
                      class="emotion-45 emotion-30"
                      color="#006FFF"
                      data-testid="checkbox-checked"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM10.71 16.29C10.32 16.68 9.69 16.68 9.3 16.29L5.71 12.7C5.32 12.31 5.32 11.68 5.71 11.29C6.1 10.9 6.73 10.9 7.12 11.29L10 14.17L16.88 7.29C17.27 6.9 17.9 6.9 18.29 7.29C18.68 7.68 18.68 8.31 18.29 8.7L10.71 16.29Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                    <h6
                      class="emotion-85 emotion-86"
                    >
                      Scheduled
                    </h6>
                  </div>
                </button>
              </div>
              <div
                class="emotion-87 emotion-11"
                wrap="nowrap"
              >
                <div
                  class="emotion-89 emotion-25"
                >
                  <h4
                    class="emotion-91 emotion-7"
                  >
                    3
                  </h4>
                  <span
                    class="emotion-93 emotion-94"
                  >
                    Open rooms
                  </span>
                  <div
                    class="emotion-95"
                  >
                    <span
                      class="emotion-96 emotion-97"
                    >
                      16 / 20
                       
                    </span>
                    <span
                      class="emotion-98 emotion-99"
                    >
                      Cases Completed
                    </span>
                  </div>
                </div>
                <div
                  class="emotion-100 emotion-25"
                >
                  <h4
                    class="emotion-91 emotion-7"
                  >
                    3
                  </h4>
                  <span
                    class="emotion-93 emotion-94"
                  >
                    Rooms running
                  </span>
                  <button
                    class="emotion-106"
                  >
                    <span
                      class="emotion-98 emotion-99"
                    >
                      View Rooms
                    </span>
                  </button>
                </div>
                <div
                  class="emotion-100 emotion-25"
                >
                  <h4
                    class="emotion-91 emotion-7"
                  >
                    3
                  </h4>
                  <span
                    class="emotion-93 emotion-94"
                  >
                    Open past 15:00
                  </span>
                  <button
                    class="emotion-106"
                  >
                    <span
                      class="emotion-98 emotion-99"
                    >
                      View Rooms
                    </span>
                  </button>
                </div>
                <div
                  class="emotion-100 emotion-25"
                >
                  <h4
                    class="emotion-91 emotion-7"
                  >
                    2
                  </h4>
                  <span
                    class="emotion-93 emotion-94"
                  >
                    Open past 17:00
                  </span>
                  <button
                    class="emotion-106"
                  >
                    <span
                      class="emotion-98 emotion-99"
                    >
                      View Rooms
                    </span>
                  </button>
                </div>
                <div
                  class="emotion-100 emotion-25"
                >
                  <h4
                    class="emotion-91 emotion-7"
                  >
                    2
                  </h4>
                  <span
                    class="emotion-93 emotion-94"
                  >
                    Open past 19:00
                  </span>
                  <button
                    class="emotion-106"
                  >
                    <span
                      class="emotion-98 emotion-99"
                    >
                      View Rooms
                    </span>
                  </button>
                </div>
              </div>
              <div
                class="emotion-136"
              >
                <div
                  class="emotion-137"
                >
                  <div
                    class="emotion-138 emotion-139"
                  >
                    <div
                      class="emotion-140 emotion-141"
                    >
                      <div
                        class="emotion-142 emotion-143"
                        style="display: flex; flex-direction: row; align-items: center; border: 1px solid; border-color: #f2f2f2; border-radius: 4px; box-sizing: border-box;"
                      >
                        <button
                          class="emotion-144"
                          style="color: rgb(153, 153, 153); background-color: transparent; padding: 0.25rem;"
                        >
                          <div>
                            <svg
                              class="emotion-29 emotion-30"
                              style="display: flex; align-items: center; justify-content: center; color: rgb(153, 153, 153);"
                              viewBox="0 0 24 24"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M9.61825 0.215041C9.21997 -0.114646 8.62983 -0.0590386 8.30014 0.339243C7.97046 0.737525 8.02606 1.32766 8.42435 1.65735L9.56814 2.60415C7.95164 2.98636 6.44551 3.77518 5.20058 4.91226C3.38387 6.5716 2.2555 8.85191 2.03837 11.3028C1.82124 13.7536 2.53114 16.1968 4.02783 18.1497C5.52453 20.1026 7.69918 21.4231 10.1222 21.8505C12.5453 22.278 15.0405 21.7811 17.115 20.4582C19.1896 19.1353 20.6925 17.0824 21.3271 14.7052C21.9616 12.328 21.6815 9.79924 20.5422 7.61847C19.7434 6.08945 18.5629 4.80947 17.1312 3.89253C16.6958 3.61368 16.1168 3.74057 15.8379 4.17596C15.5591 4.61135 15.686 5.19036 16.1214 5.46921C17.2804 6.21155 18.236 7.24773 18.8827 8.48547C19.805 10.2509 20.0317 12.2979 19.5181 14.2224C19.0044 16.1468 17.7877 17.8086 16.1083 18.8795C14.4289 19.9505 12.409 20.3527 10.4475 20.0067C8.48596 19.6607 6.72553 18.5916 5.51392 17.0107C4.30231 15.4298 3.72763 13.452 3.9034 11.468C4.07918 9.48397 4.99261 7.638 6.46329 6.29473C7.93396 4.95146 9.85491 4.20857 11.8467 4.2128C12.2418 4.21364 12.5949 3.96638 12.7291 3.59482C12.8634 3.22326 12.75 2.8074 12.4456 2.55548L9.61825 0.215041ZM6.35578 15.4468C6.35578 13.4653 7.51677 12.6811 8.39324 12.089C8.92468 11.7301 9.35152 11.4417 9.35152 11C9.35152 10.6724 9.07067 10.3915 8.6962 10.3915C8.04088 10.3915 7.80684 11 7.80684 11L6.30897 10.2979C6.30897 10.2979 6.91748 8.70642 8.83662 8.70642C10.2409 8.70642 11.2239 9.68939 11.2239 10.8596C11.2239 11.9713 10.4497 12.4922 9.73475 12.9733C9.36592 13.2215 9.01282 13.4591 8.78982 13.7617H11.2239V15.4468H6.35578ZM14.9166 15.4469V14.4171H11.9677V12.5916L14.7294 8.89372H16.6953V12.8256H17.3974V14.4171H16.6953V15.4469H14.9166ZM13.5591 12.8256H14.9166V11.0001L13.5591 12.8256Z"
                                fill-rule="evenodd"
                              />
                            </svg>
                          </div>
                        </button>
                        <button
                          class="emotion-144"
                          style="color: rgb(153, 153, 153); background-color: transparent; padding: 0.25rem;"
                        >
                          <div>
                            <svg
                              class="emotion-29 emotion-30"
                              style="display: flex; align-items: center; justify-content: center; color: rgb(153, 153, 153);"
                              viewBox="0 0 24 24"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M23.785 11.6182C24.1147 11.22 24.059 10.6298 23.6608 10.3001C23.2625 9.97045 22.6723 10.0261 22.3427 10.4243L21.3959 11.5681C21.0136 9.95164 20.2248 8.44551 19.0877 7.20058C17.4284 5.38386 15.1481 4.2555 12.6973 4.03837C10.2464 3.82124 7.80323 4.53114 5.85035 6.02783C3.89747 7.52452 2.57689 9.69917 2.14948 12.1222C2.04796 12.6977 1.99858 13.2773 2.00003 13.8544C2.00133 14.3714 2.42152 14.7895 2.93855 14.7882C3.45557 14.7869 3.87366 14.3667 3.87236 13.8497C3.87119 13.3826 3.91116 12.9134 3.99335 12.4475C4.33935 10.486 5.40839 8.72553 6.98929 7.51392C8.57019 6.30231 10.548 5.72763 12.532 5.9034C14.516 6.07917 16.362 6.99261 17.7053 8.46328C19.0486 9.93396 19.7914 11.8549 19.7872 13.8467C19.7864 14.2418 20.0336 14.5949 20.4052 14.7291C20.7767 14.8634 21.1926 14.75 21.4445 14.4456L23.785 11.6182ZM11.7127 8.21276C8.61052 8.21276 6.0957 10.7276 6.0957 13.8298C6.0957 14.5674 6.24099 15.2978 6.52327 15.9793C6.80555 16.6608 7.2193 17.28 7.74089 17.8016L7.74991 17.8106C8.76567 18.8218 10.1662 19.4468 11.7127 19.4468C14.8149 19.4468 17.3297 16.932 17.3297 13.8298C17.3297 10.7276 14.8149 8.21276 11.7127 8.21276ZM15.4574 13.8298H7.96804C7.96804 11.7616 9.64459 10.0851 11.7127 10.0851C13.7808 10.0851 15.4574 11.7616 15.4574 13.8298Z"
                                fill-rule="evenodd"
                              />
                            </svg>
                          </div>
                        </button>
                        <button
                          class="emotion-144"
                          style="color: rgb(153, 153, 153); background-color: rgb(204, 226, 255); padding: 0.25rem;"
                        >
                          <div>
                            <svg
                              class="emotion-29 emotion-30"
                              style="display: flex; align-items: center; justify-content: center; color: rgb(25, 25, 25);"
                              viewBox="0 0 24 24"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H3.5C3.77614 2 4 2.22386 4 2.5V21.5C4 21.7761 3.77614 22 3.5 22H2.5C2.22386 22 2 21.7761 2 21.5V2.5ZM20 2.5C20 2.22386 20.2239 2 20.5 2H21.5C21.7761 2 22 2.22386 22 2.5V21.5C22 21.7761 21.7761 22 21.5 22H20.5C20.2239 22 20 21.7761 20 21.5V2.5ZM5.5 4C5.22386 4 5 4.22386 5 4.5V8.5C5 8.77614 5.22386 9 5.5 9H10.5C10.7761 9 11 8.77614 11 8.5V4.5C11 4.22386 10.7761 4 10.5 4H5.5ZM8 10.5C8 10.2239 8.22386 10 8.5 10H16.5C16.7761 10 17 10.2239 17 10.5V14.5C17 14.7761 16.7761 15 16.5 15H8.5C8.22386 15 8 14.7761 8 14.5V10.5ZM13.5 16C13.2239 16 13 16.2239 13 16.5V20.5C13 20.7761 13.2239 21 13.5 21H18.5C18.7761 21 19 20.7761 19 20.5V16.5C19 16.2239 18.7761 16 18.5 16H13.5Z"
                                fill-rule="evenodd"
                              />
                            </svg>
                          </div>
                        </button>
                      </div>
                    </div>
                    <div
                      class="emotion-153 emotion-154"
                    >
                      <div
                        class="emotion-155"
                      >
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            style="position: relative; color: rgba(0, 0, 0, 0.7); left: 0px;"
                          >
                            <span
                              class="emotion-98 emotion-99"
                              style="line-height: inherit; white-space: nowrap;"
                            >
                              08:00
                            </span>
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            style="position: relative; color: rgba(0, 0, 0, 0.7); left: 0px;"
                          >
                            <span
                              class="emotion-98 emotion-99"
                              style="line-height: inherit; white-space: nowrap;"
                            >
                              13:00
                            </span>
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          style="position: absolute; left: 0px;"
                        >
                          <div
                            class="emotion-158"
                          >
                            <div
                              class="emotion-159"
                            />
                          </div>
                        </div>
                        <div
                          class="emotion-180"
                          style="left: 11.481478395061728%;"
                        >
                          <div
                            class="emotion-181"
                          >
                            09:01
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-182 emotion-139"
                  >
                    <div
                      class="emotion-184 emotion-141"
                    >
                      <div
                        class="emotion-186"
                      >
                        <div
                          class="emotion-187"
                        >
                          <p
                            class="emotion-188 emotion-189"
                          >
                            Garage 0
                          </p>
                          <p
                            class="emotion-190 emotion-191"
                          >
                            Apella Lab
                          </p>
                          <p
                            class="emotion-192 emotion-191"
                          >
                            CLOSED
                            <span
                              class="emotion-194"
                            />
                          </p>
                        </div>
                        <div>
                          <button
                            class="emotion-106"
                          >
                            <span
                              class="emotion-98 emotion-99"
                            >
                              View live
                            </span>
                          </button>
                        </div>
                      </div>
                      <div
                        class="emotion-186"
                      >
                        <div
                          class="emotion-187"
                        >
                          <p
                            class="emotion-188 emotion-189"
                          >
                            Garage 1
                          </p>
                          <p
                            class="emotion-190 emotion-191"
                          >
                            Apella Lab
                          </p>
                          <p
                            class="emotion-204 emotion-191"
                          >
                            TURNOVER
                            <span
                              class="emotion-194"
                            >
                               -428M
                            </span>
                          </p>
                        </div>
                        <div>
                          <button
                            class="emotion-106"
                          >
                            <span
                              class="emotion-98 emotion-99"
                            >
                              View live
                            </span>
                          </button>
                        </div>
                      </div>
                      <div
                        class="emotion-186"
                      >
                        <div
                          class="emotion-187"
                        >
                          <p
                            class="emotion-188 emotion-189"
                          >
                            Garage 2
                          </p>
                          <p
                            class="emotion-190 emotion-191"
                          >
                            Apella Lab
                          </p>
                          <p
                            class="emotion-192 emotion-191"
                          >
                            CLOSED
                            <span
                              class="emotion-194"
                            />
                          </p>
                        </div>
                        <div>
                          <button
                            class="emotion-106"
                          >
                            <span
                              class="emotion-98 emotion-99"
                            >
                              View live
                            </span>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-222 emotion-154"
                    >
                      <div
                        class="emotion-224"
                      >
                        <div
                          class="emotion-225"
                          data-testid="room-case-timeline-row"
                          style="cursor: default;"
                        >
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    Bailey, John, 08:09  - 09:14
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    Colonoscopy; Angiogram
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-238 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 08:12 -
  09:47
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    House, John, 09:56  - 11:10
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    Angiogram
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-238 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 11:45 -
  12:59
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    House, Gregory, 11:48  - 12:48
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    I & D Abdomen
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-238 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 15:11 -
  16:31
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    13:03  - 14:25
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    15:17  - 16:11
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-302 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    House, John, 12:19  - 12:19
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    Knee Arthroplasty JRP - Total Replacement
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-312 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 13:09 -
  15:00
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-302 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    Wu, Henry, 12:19  - 12:19
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    Knee Arthroplasty JRP - Total Replacement
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-312 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 09:54 -
  11:32
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-225"
                          data-testid="room-case-timeline-row"
                          style="cursor: default;"
                        >
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    Dorian, John; Wu, Gregory, 08:14  - 09:40
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    Knee Arthroplasty JRP - Total Replacement
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-238 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 08:12 -
  10:07
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    Bailey, Gregory, 10:11  - 11:06
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    I & D Abdomen
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-238 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 11:52 -
  13:27
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    House, Miranda, 11:55  - 13:08
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    Angiogram
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-238 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 15:08 -
  16:35
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    13:43  - 14:32
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    15:09  - 16:10
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-302 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    Wu, Henry; Dorian, Gregory, 12:19  - 12:19
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    Angiogram
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-312 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 13:39 -
  14:55
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-302 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    Bailey, Gregory, 12:19  - 12:19
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  >
                                    Angiogram
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 16px; top: 58px;"
                          >
                            <div>
                              <div
                                class="emotion-312 emotion-229"
                              >
                                <div
                                  class="emotion-240 emotion-1911"
                                >
                                  <span
                                    class="emotion-242 emotion-99"
                                  >
                                    SCH: 10:16 -
  11:36
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-225"
                          data-testid="room-case-timeline-row"
                          style="cursor: default;"
                        >
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    08:09  - 09:06
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    09:46  - 10:29
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    10:46  - 11:25
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    12:03  - 12:41
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    12:59  - 14:07
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-226 emotion-227"
                            style="left: 0px; width: 0px; height: 44px; top: 7px;"
                          >
                            <div>
                              <div
                                class="emotion-228 emotion-229"
                              >
                                <div
                                  class="emotion-230 emotion-1911"
                                >
                                  <span
                                    class="emotion-96 emotion-97"
                                  >
                                    14:56  - 15:51
                                  </span>
                                  <br />
                                  <span
                                    class="emotion-98 emotion-99"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-508"
                          style="left: 11.481478395061728%;"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-509"
              >
                <div
                  class="emotion-510 emotion-11"
                >
                  <div
                    class="emotion-512 emotion-11"
                  >
                    <svg
                      class="emotion-514 emotion-30"
                      color="#999"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M12.6499 10.0001C11.6999 7.31012 8.89994 5.50012 5.76994 6.12012C3.47994 6.58012 1.61994 8.41012 1.13994 10.7001C0.31994 14.5701 3.25994 18.0001 6.99994 18.0001C9.60994 18.0001 11.8299 16.3301 12.6499 14.0001H16.9999V16.0001C16.9999 17.1001 17.8999 18.0001 18.9999 18.0001C20.0999 18.0001 20.9999 17.1001 20.9999 16.0001V14.0001C22.0999 14.0001 22.9999 13.1001 22.9999 12.0001C22.9999 10.9001 22.0999 10.0001 20.9999 10.0001H12.6499ZM6.99994 14.0001C5.89994 14.0001 4.99994 13.1001 4.99994 12.0001C4.99994 10.9001 5.89994 10.0001 6.99994 10.0001C8.09994 10.0001 8.99994 10.9001 8.99994 12.0001C8.99994 13.1001 8.09994 14.0001 6.99994 14.0001Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                    <span
                      class="emotion-516 emotion-1899"
                    >
                      KEY
                    </span>
                  </div>
                  <div
                    class="emotion-518 emotion-11"
                    wrap="wrap"
                  >
                    <div
                      class="emotion-520 emotion-229"
                    >
                      <span
                        class="emotion-522 emotion-99"
                      >
                        Prep
                      </span>
                    </div>
                    <div
                      class="emotion-524 emotion-229"
                    >
                      <span
                        class="emotion-522 emotion-99"
                      >
                        Surgery
                      </span>
                    </div>
                    <div
                      class="emotion-528 emotion-229"
                    >
                      <span
                        class="emotion-522 emotion-99"
                      >
                        Wrap-up
                      </span>
                    </div>
                    <div
                      class="emotion-532 emotion-229"
                    >
                      <span
                        class="emotion-534 emotion-99"
                      >
                        In Fac
                      </span>
                    </div>
                    <div
                      class="emotion-536 emotion-229"
                    >
                      <span
                        class="emotion-534 emotion-99"
                      >
                        Pre Proc
                      </span>
                    </div>
                    <div
                      class="emotion-540 emotion-229"
                    >
                      <span
                        class="emotion-534 emotion-99"
                      >
                        In Hold
                      </span>
                    </div>
                    <div
                      class="emotion-544 emotion-229"
                    >
                      <span
                        class="emotion-534 emotion-99"
                      >
                        Recovery
                      </span>
                    </div>
                    <div
                      class="emotion-548 emotion-229"
                    >
                      <span
                        class="emotion-534 emotion-99"
                      >
                        Phase II
                      </span>
                    </div>
                    <div
                      class="emotion-552 emotion-229"
                    >
                      <span
                        class="emotion-522 emotion-99"
                      >
                        Actual
                      </span>
                    </div>
                    <div
                      class="emotion-556 emotion-229"
                    >
                      <span
                        class="emotion-522 emotion-99"
                      >
                        Scheduled
                      </span>
                    </div>
                    <div
                      class="emotion-560 emotion-229"
                    >
                      <span
                        class="emotion-522 emotion-99"
                      >
                        Forecast
                      </span>
                    </div>
                  </div>
                  <div>
                    <button
                      class="emotion-564"
                    >
                      <svg
                        class="emotion-565 emotion-30"
                        color="#bfbfbf"
                        cursor="pointer"
                        viewBox="0 0 24 24"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M18.3 5.71021C17.91 5.32021 17.28 5.32021 16.89 5.71021L12 10.5902L7.10997 5.70021C6.71997 5.31021 6.08997 5.31021 5.69997 5.70021C5.30997 6.09021 5.30997 6.72021 5.69997 7.11021L10.59 12.0002L5.69997 16.8902C5.30997 17.2802 5.30997 17.9102 5.69997 18.3002C6.08997 18.6902 6.71997 18.6902 7.10997 18.3002L12 13.4102L16.89 18.3002C17.28 18.6902 17.91 18.6902 18.3 18.3002C18.69 17.9102 18.69 17.2802 18.3 16.8902L13.41 12.0002L18.3 7.11021C18.68 6.73021 18.68 6.09021 18.3 5.71021Z"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="emotion-567 emotion-568"
            >
              <div
                class="emotion-569 emotion-11"
                direction="column"
              >
                <div
                  class="emotion-571 emotion-572"
                >
                  <div
                    class="emotion-512 emotion-11"
                  >
                    <svg
                      class="emotion-575 emotion-30"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M3 17.4601V20.5001C3 20.7801 3.22 21.0001 3.5 21.0001H6.54C6.67 21.0001 6.8 20.9501 6.89 20.8501L17.81 9.94006L14.06 6.19006L3.15 17.1001C3.05 17.2001 3 17.3201 3 17.4601ZM20.71 7.04006C21.1 6.65006 21.1 6.02006 20.71 5.63006L18.37 3.29006C17.98 2.90006 17.35 2.90006 16.96 3.29006L15.13 5.12006L18.88 8.87006L20.71 7.04006Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                    <h2
                      class="emotion-577 emotion-15"
                    >
                      Edit Schedule
                    </h2>
                  </div>
                  <button
                    class="emotion-564"
                  >
                    <svg
                      class="emotion-29 emotion-30"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M18.3 5.71021C17.91 5.32021 17.28 5.32021 16.89 5.71021L12 10.5902L7.10997 5.70021C6.71997 5.31021 6.08997 5.31021 5.69997 5.70021C5.30997 6.09021 5.30997 6.72021 5.69997 7.11021L10.59 12.0002L5.69997 16.8902C5.30997 17.2802 5.30997 17.9102 5.69997 18.3002C6.08997 18.6902 6.71997 18.6902 7.10997 18.3002L12 13.4102L16.89 18.3002C17.28 18.6902 17.91 18.6902 18.3 18.3002C18.69 17.9102 18.69 17.2802 18.3 16.8902L13.41 12.0002L18.3 7.11021C18.68 6.73021 18.68 6.09021 18.3 5.71021Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
                <div
                  class="emotion-582 emotion-25"
                >
                  <div
                    class="emotion-584 emotion-11"
                  >
                    <h3
                      class="emotion-86 emotion-587 emotion-1915"
                      color="rgba(0,0,0,0.4)"
                    >
                      Select a case to edit
                    </h3>
                  </div>
                </div>
                <div
                  class="emotion-589 emotion-25"
                />
                <div
                  class="emotion-591 emotion-25"
                >
                  <div
                    class="emotion-593 emotion-11"
                    direction="Column"
                  >
                    <button
                      class="emotion-595"
                      disabled=""
                    >
                      <h2
                        class="emotion-15 emotion-597 emotion-1916"
                      >
                        Clear changes
                      </h2>
                    </button>
                    <button
                      class="emotion-599"
                      disabled=""
                    >
                      <h2
                        class="emotion-15 emotion-597 emotion-1916"
                      >
                        Publish changes
                      </h2>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div />
  </body>,
  "container": .emotion-0 {
  display: grid;
  width: 100%;
  height: 100%;
  padding: 0;
}

@media only screen and (min-width: 1024px) {
  .emotion-0 {
    padding: 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  justify-self: center;
  height: 100%;
  min-height: 0;
  grid-auto-rows: minmax(0, 1fr);
  gap: 1rem;
}

@media only screen and (min-width: 1024px) {
  .emotion-1 {
    gap: 1.5rem;
  }
}

.emotion-2 {
  position: relative;
  width: 100%;
}

.emotion-2[data-show-panel=true] {
  display: grid;
  grid-auto-rows: minmax(0,1fr);
}

@media only screen and (min-width: 1024px) {
  .emotion-2[data-show-panel=true] {
    grid-auto-flow: column;
    grid-template-columns: 3fr 1fr;
  }
}

.emotion-3 {
  display: grid;
  justify-self: center;
  gap: 1rem;
  grid-auto-rows: max-content;
  grid-template-columns: minmax(0, 1fr);
  position: relative;
  height: 100%;
  padding: 0;
  width: 100%;
}

.emotion-3[data-show-panel=true] {
  overflow-y: auto;
  padding: 0 1rem;
}

@media only screen and (min-width: 1024px) {
  .emotion-3[data-show-panel=true] {
    padding: 0 1.5rem;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-3 {
    gap: 1.5rem;
  }
}

.emotion-4 {
  display: grid;
  gap: 1rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-template-areas: "title actions" "views views";
  width: 100%;
}

@media only screen and (min-width: 1024px) {
  .emotion-4 {
    padding-top: 0.5rem;
    grid-template-areas: "title views actions";
    grid-template-columns: max-content max-content auto;
  }
}

.emotion-5 {
  grid-area: title;
  overflow-x: auto;
}

.emotion-6 {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: max-content;
  gap: 0.5rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.emotion-8 {
  font-weight: 600;
  font-size: 3rem;
  line-height: 3.75rem;
  letter-spacing: -0.03em;
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.75rem;
  letter-spacing: -0.02em;
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: minmax(0, max-content);
  gap: 0.5rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow-x: hidden;
  line-height: 48px;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0;
  width: 100%;
  grid-area: views;
}

.emotion-10>[data-tertiary-view=false]:first-of-type {
  border-radius: 8px 0px 0px 8px;
}

@media only screen and (min-width: 1024px) {
  .emotion-10>[data-tertiary-view=false]:first-of-type {
    border-radius: 0px;
  }
}

.emotion-10>[data-tertiary-view=false]:nth-of-type(3) {
  border-radius: 0px 8px 8px 0px;
}

@media only screen and (min-width: 1024px) {
  .emotion-10>[data-tertiary-view=false]:nth-of-type(3) {
    border-radius: 0px;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-10 {
    width: auto;
  }
}

.emotion-12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  width: 100%;
  border-style: solid;
  border-width: 1px 0.5px 1px 0.5px;
  border-color: #e6e6e6;
  padding: 0 0.25rem;
  background-color: transparent;
}

.emotion-12[data-tertiary-view=true] {
  display: none;
}

@media only screen and (min-width: 1024px) {
  .emotion-12[data-tertiary-view=true] {
    display: grid;
    max-width: 200px;
  }
}

.emotion-12[aria-selected=true] {
  border-color: #006FFF;
  background-color: #006FFF;
}

@media only screen and (min-width: 1024px) {
  .emotion-12[aria-selected=true] {
    box-shadow: 0px -2px 0px 0px #006FFF inset;
    background-color: transparent;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-12[aria-selected=true][aria-disabled=true] {
    box-shadow: 0px -2px 0px 0px #bfbfbf inset;
    background-color: transparent;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-12[aria-selected=false]:hover {
    background-color: #cce2ff;
  }
}

@media only screen and (min-width: 1024px) {
  .emotion-12 {
    width: auto;
    display: grid;
    grid-auto-columns: minmax(0, max-content);
    grid-auto-flow: column;
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 0.25rem;
    cursor: default;
    padding: 0 0.5rem;
    border: none;
    background-color: transparent;
    border-radius: 0px;
  }
}

.emotion-13 {
  color: rgba(0,0,0,0.4);
}

.emotion-13[aria-selected=true] {
  color: rgba(255,255,255,0.9);
}

.emotion-13[aria-disabled=true] {
  color: #f7f7f7;
}

@media only screen and (min-width: 1024px) {
  .emotion-13[aria-selected=true] {
    color: rgba(0,0,0,0.9);
  }
}

.emotion-14 {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  text-align: center;
  line-height: 40px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.emotion-24 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  display: none;
  border-style: solid;
  border-width: 1px 0.5px 1px 0.5px;
  border-color: #e6e6e6;
  background-color: transparent;
  border-radius: 0px 8px 8px 0px;
}

@media only screen and (min-width: 1024px) {
  .emotion-24 {
    width: auto;
    cursor: default;
    padding: 0 0.5rem;
    border: none;
    background-color: transparent;
    border-radius: 0px;
    display: none;
  }
}

.emotion-26 {
  display: block;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media only screen and (min-width: 1024px) {
  .emotion-26 {
    cursor: pointer;
    border-style: none;
    border-width: 0px;
    background-color: white;
  }
}

.emotion-28 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: #999;
}

.emotion-28:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-28:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-28:active:not(:disabled) {
  background-color: #f7f7f7;
}

.emotion-29 {
  color: inherit;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-31 {
  display: grid;
  grid-area: actions;
  justify-self: end;
  grid-auto-flow: column;
  gap: 0.25rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-auto-columns: minmax(0, max-content);
}

.emotion-32 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
}

.emotion-32:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-32:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-32:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-35 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 1.5rem;
  position: relative;
  width: 100%;
}

@media only screen and (min-width: 1024px) {
  .emotion-35 {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    -webkit-justify-content: start;
    justify-content: start;
  }
}

.emotion-37 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0.5rem;
}

.emotion-42 {
  display: inline-block;
  position: relative;
}

.emotion-44 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: auto;
}

.emotion-44:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-44:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-44:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-45 {
  color: #006FFF;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-47 {
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5rem;
  color: rgba(0,0,0,0.7);
}

.emotion-52 {
  box-sizing: border-box;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.emotion-56 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
  width: 100%;
}

.emotion-56:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-56:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-56:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-57 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.emotion-60 {
  color: inherit;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  color: #999;
}

.emotion-68 {
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5rem;
  color: rgba(0,0,0,0.4);
}

.emotion-80 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: rgba(0,0,0,0.7);
  height: 2.5rem;
}

.emotion-80:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-80:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-80:active:not(:disabled) {
  background-color: #f2f2f2;
}

.emotion-81 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0.75rem;
  height: 2.25rem;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-85 {
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
}

.emotion-87 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 1rem;
  overflow-x: auto;
}

.emotion-89 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  border: 1px solid #e6e6e6;
  border-radius: 0.5rem;
  text-align: center;
  color: rgba(0,0,0,0.7);
  display: grid;
  padding: 1.5rem 0.75rem 0.75rem;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-91 {
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.75rem;
  letter-spacing: -0.02em;
}

.emotion-93 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1rem;
}

.emotion-95 {
  color: rgba(0,0,0,0.7);
}

.emotion-96 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.625rem;
  line-height: 1rem;
}

.emotion-98 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.625rem;
  line-height: 1rem;
}

.emotion-100 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  border: 1px solid #e6e6e6;
  border-radius: 0.5rem;
  text-align: center;
  color: rgba(0,0,0,0.7);
  display: grid;
  padding: 1.5rem 0.75rem 1.5rem;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-106 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 4px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 4px 8px;
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: #006FFF;
}

.emotion-106:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-106:hover:not(:active):not(:disabled) {
  background-color: #f2f8ff;
}

.emotion-106:active:not(:disabled) {
  background-color: #cce2ff;
}

.emotion-136 {
  padding-bottom: 10rem;
  pointer-events: auto;
}

.emotion-137 {
  overscroll-behavior: contain;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: relative;
  z-index: 0;
}

.emotion-138 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: auto;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  overflow: hidden;
  z-index: 2;
}

.emotion-138::-webkit-scrollbar {
  display: none;
}

.emotion-140 {
  background: #FFF;
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 1;
  box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.08),0px 3px 5px 0px rgba(0, 0, 0, 0.10);
  -webkit-flex: 0 0 115px;
  -ms-flex: 0 0 115px;
  flex: 0 0 115px;
  border-bottom: 1px solid #f2f2f2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media only screen and (min-width: 1024px) {
  .emotion-140 {
    box-shadow: none;
  }
}

.emotion-142 {
  display: inline-block;
}

.emotion-142>button:not(:first-of-type) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.emotion-142>button:not(:last-of-type) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.emotion-144 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 4px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 4px 8px;
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  background-color: #006FFF;
  color: rgba(255,255,255,0.9);
}

.emotion-144:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-144:hover:not(:active):not(:disabled) {
  background-color: #338cff;
}

.emotion-144:active:not(:disabled) {
  background-color: #0059cc;
}

.emotion-153 {
  background: #FFF;
  padding: 0 20px;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  min-width: 500px;
  border-bottom: 1px solid #f2f2f2;
}

.emotion-155 {
  height: 50px;
  padding-top: 8px;
  position: relative;
}

.emotion-158 {
  position: relative;
  left: -0.5px;
}

.emotion-159 {
  background: #999;
  height: 12px;
  width: 1px;
  margin-top: 20px;
}

.emotion-180 {
  height: 100%;
  position: absolute;
  width: 1px;
  pointer-events: none;
  top: 0.5rem;
  background: #006FFF;
}

.emotion-181 {
  position: absolute;
  top: 0;
  left: -30.5px;
  pointer-events: none;
  height: 16px;
  font-size: 0.625rem;
  color: #FFF;
  background: #006FFF;
  width: 60px;
  border-radius: 16px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-182 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: auto;
}

.emotion-182::-webkit-scrollbar {
  display: none;
}

.emotion-184 {
  background: #FFF;
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 1;
  box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.08),0px 3px 5px 0px rgba(0, 0, 0, 0.10);
  -webkit-flex: 0 0 115px;
  -ms-flex: 0 0 115px;
  flex: 0 0 115px;
  overflow: hidden;
  top: 0;
}

@media only screen and (min-width: 1024px) {
  .emotion-184 {
    box-shadow: none;
  }
}

.emotion-186 {
  height: 81px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  white-space: nowrap;
  background: #FFF;
  border-bottom: 1px solid #f2f2f2;
}

.emotion-187 {
  padding-left: 8px;
}

.emotion-188 {
  font-weight: 400;
  font-size: 0.8125rem;
  line-height: 1.25rem;
}

.emotion-190 {
  font-weight: 400;
  font-size: 0.6875rem;
  line-height: 1rem;
  color: rgba(0,0,0,0.4);
}

.emotion-192 {
  font-weight: 400;
  font-size: 0.6875rem;
  line-height: 1rem;
  background-color: #f2f2f2;
  border-radius: 0.25rem;
  padding: 0 0.25rem;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  font-weight: bold;
}

.emotion-194 {
  font-weight: normal;
}

.emotion-204 {
  font-weight: 400;
  font-size: 0.6875rem;
  line-height: 1rem;
  background-color: #f7ce6b;
  border-radius: 0.25rem;
  padding: 0 0.25rem;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  font-weight: bold;
}

.emotion-222 {
  background: #FFF;
  padding: 0 20px;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  min-width: 500px;
}

.emotion-224 {
  position: relative;
}

.emotion-225 {
  width: 100%;
  height: 81px;
  position: relative;
}

.emotion-225:after {
  content: "";
  display: block;
  height: 100%;
  margin: 0 -20px;
  border-bottom: 1px solid #f2f2f2;
}

.emotion-226 {
  position: absolute;
  cursor: default;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-228 {
  border-radius: 0.5rem;
  background-color: #e6e6e6;
  border: 0.0625rem solid #e6e6e6;
  width: 0;
  height: 44px;
  padding: 0.0625rem;
  background-color: #e6e6e6;
  z-index: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-230 {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: -4px;
  height: 42px;
  padding-left: 0.5rem;
  padding-top: 0.125rem;
}

.emotion-230::after {
  content: "";
  display: block;
}

.emotion-238 {
  border-radius: 0.5rem;
  background-color: #e6e6e6;
  border: 0.0625rem solid #e6e6e6;
  background-color: #e6e6e6;
  z-index: 1;
}

.emotion-240 {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: -2px;
  height: 14px;
}

.emotion-240::after {
  content: "";
  display: block;
}

.emotion-242 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.625rem;
  line-height: 1rem;
  line-height: 1.5;
  display: block;
  padding-left: 0.5rem;
}

.emotion-302 {
  border-radius: 0.5rem;
  background-color: #FFF;
  border: 0.0625rem dashed #bfbfbf;
  width: 0;
  height: 44px;
  padding: 0.0625rem;
  background-color: #FFF;
  z-index: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-312 {
  border-radius: 0.625rem;
  background-color: #f2f2f2;
  border: 0.0625rem solid #e6e6e6;
  background-color: #f2f2f2;
  z-index: 1;
}

.emotion-508 {
  height: 100%;
  position: absolute;
  width: 1px;
  pointer-events: none;
  top: 0;
  background: #006FFF;
}

.emotion-509 {
  position: fixed;
  z-index: 2;
  bottom: 1.5rem;
  right: 1.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-510 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 1rem;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFF;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.1),0px 1px 18px rgba(0, 0, 0, 0.06),0px 6px 10px rgba(0, 0, 0, 0.08);
  padding: 1rem;
  border: 0.0625rem solid 1px solid #e6e6e6;
  border-radius: 0.5rem;
  max-width: 750px;
}

.emotion-512 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.5rem;
}

.emotion-514 {
  color: #999;
  fill: currentcolor;
  height: 1.25rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.25rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-516 {
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
}

.emotion-518 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 1rem;
}

.emotion-520 {
  border-radius: 0.5rem;
  background-color: #72c8c8;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-522 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.625rem;
  line-height: 1rem;
}

.emotion-524 {
  border-radius: 0.5rem;
  background-color: #8CB860;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-528 {
  border-radius: 0.5rem;
  background-color: #A1F98E;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-532 {
  border-radius: 0.5rem;
  background-color: #C0997B;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-534 {
  letter-spacing: 0.04em;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.625rem;
  line-height: 1rem;
  color: rgba(0,0,0,0.9);
}

.emotion-536 {
  border-radius: 0.5rem;
  background-color: #FCE995;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-540 {
  border-radius: 0.5rem;
  background-color: #EF8633;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-544 {
  border-radius: 0.5rem;
  background-color: #B1D5E8;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-548 {
  border-radius: 0.5rem;
  background-color: #F19EFA;
  border: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-552 {
  border-radius: 0.5rem;
  background-color: #e6e6e6;
  border: 0.0625rem solid #e6e6e6;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-556 {
  border-radius: 0.625rem;
  background-color: #f2f2f2;
  border: 0.0625rem solid #e6e6e6;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-560 {
  border-radius: 0.125rem;
  background-color: #FFF;
  border: 0.0625rem dashed #bfbfbf;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5rem;
}

.emotion-564 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: rgba(0,0,0,0.7);
}

.emotion-564:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-564:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-564:active:not(:disabled) {
  background-color: #f2f2f2;
}

.emotion-565 {
  color: #bfbfbf;
  fill: currentcolor;
  height: 1rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-567 {
  grid-row: 2;
  overflow-y: auto;
}

@media only screen and (min-width: 1024px) {
  .emotion-567 {
    grid-row: 1;
  }
}

.emotion-569 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0;
  height: 100%;
  padding: 0;
  border-top: solid 1px #f7f7f7;
}

@media only screen and (min-width: 1024px) {
  .emotion-569 {
    border-left: solid 1px #f7f7f7;
  }
}

.emotion-571 {
  border-bottom: 1px solid #f2f2f2;
  padding: 1rem;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  background: #FFF;
  display: grid;
  grid-auto-flow: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-575 {
  color: inherit;
  fill: currentcolor;
  height: 1rem;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1rem;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.emotion-577 {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
}

.emotion-582 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  padding: 1rem 0;
  border-bottom: 1px solid #f7f7f7;
}

.emotion-584 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0;
  min-height: auto;
}

.emotion-587 {
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 2rem;
  letter-spacing: -0.02em;
  color: rgba(0,0,0,0.4);
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  color: rgba(0,0,0,0.4);
}

.emotion-589 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  overflow-y: auto;
}

.emotion-591 {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  padding: 1rem;
}

.emotion-593 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: Column;
  -ms-flex-direction: Column;
  flex-direction: Column;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 0.5rem;
}

.emotion-595 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #FFF;
  color: rgba(0,0,0,0.9);
  border-radius: 0.75rem;
}

.emotion-595:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-595:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-595:active:not(:disabled) {
  background-color: #e6e6e6;
}

.emotion-597 {
  font-weight: 600;
  font-size: 2.25rem;
  line-height: 3rem;
  letter-spacing: -0.03em;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
}

.emotion-599 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.01em;
  background-color: #006FFF;
  color: rgba(255,255,255,0.9);
  border-radius: 0.75rem;
}

.emotion-599:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-599:hover:not(:active):not(:disabled) {
  background-color: #338cff;
}

.emotion-599:active:not(:disabled) {
  background-color: #0059cc;
}

<div>
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          data-show-panel="true"
          style="height: 100%;"
        >
          <div
            class="emotion-3"
            data-show-panel="true"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <div
                  class="emotion-6"
                >
                  <h1
                    class="emotion-7 emotion-8 emotion-9"
                  >
                    Schedule
                  </h1>
                </div>
              </div>
              <div
                class="emotion-10 emotion-11"
              >
                <div
                  aria-disabled="true"
                  aria-selected="true"
                  class="emotion-12"
                  data-tertiary-view="false"
                >
                  <a
                    aria-disabled="true"
                    aria-selected="true"
                    class="emotion-13"
                    href="/schedule?sites=%5B%22lab_1%22%5D"
                    style="text-decoration: none; pointer-events: none;"
                  >
                    <h5
                      class="emotion-14 emotion-15"
                    >
                      OR
                    </h5>
                  </a>
                </div>
                <div
                  aria-disabled="true"
                  aria-selected="false"
                  class="emotion-12"
                  data-tertiary-view="false"
                >
                  <a
                    aria-disabled="true"
                    aria-selected="false"
                    class="emotion-13"
                    href="/schedule/pre-op?sites=%5B%22lab_1%22%5D"
                    style="text-decoration: none; pointer-events: none;"
                  >
                    <h5
                      class="emotion-14 emotion-15"
                    >
                      PreOp
                    </h5>
                  </a>
                </div>
                <div
                  aria-disabled="true"
                  aria-selected="false"
                  class="emotion-12"
                  data-tertiary-view="false"
                >
                  <a
                    aria-disabled="true"
                    aria-selected="false"
                    class="emotion-13"
                    href="/schedule/post-op?sites=%5B%22lab_1%22%5D"
                    style="text-decoration: none; pointer-events: none;"
                  >
                    <h5
                      class="emotion-14 emotion-15"
                    >
                      PostOp
                    </h5>
                  </a>
                </div>
                <div
                  class="emotion-24 emotion-25"
                  data-tertiary-view="false"
                >
                  <div
                    class="emotion-26 emotion-27"
                  >
                    <button
                      aria-label="More views"
                      class="emotion-28"
                      type="button"
                    >
                      <svg
                        class="emotion-29 emotion-30"
                        viewBox="0 0 24 24"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M6 10C4.9 10 4 10.9 4 12C4 13.1 4.9 14 6 14C7.1 14 8 13.1 8 12C8 10.9 7.1 10 6 10ZM18 10C16.9 10 16 10.9 16 12C16 13.1 16.9 14 18 14C19.1 14 20 13.1 20 12C20 10.9 19.1 10 18 10ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <div
                class="emotion-31"
              >
                <button
                  class="emotion-32"
                  disabled=""
                >
                  <svg
                    class="emotion-29 emotion-30"
                    viewBox="0 0 24 24"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M6 14C5.45 14 5 14.45 5 15V18C5 18.55 5.45 19 6 19H9C9.55 19 10 18.55 10 18C10 17.45 9.55 17 9 17H7V15C7 14.45 6.55 14 6 14ZM6 10C6.55 10 7 9.55 7 9V7H9C9.55 7 10 6.55 10 6C10 5.45 9.55 5 9 5H6C5.45 5 5 5.45 5 6V9C5 9.55 5.45 10 6 10ZM17 17H15C14.45 17 14 17.45 14 18C14 18.55 14.45 19 15 19H18C18.55 19 19 18.55 19 18V15C19 14.45 18.55 14 18 14C17.45 14 17 14.45 17 15V17ZM14 6C14 6.55 14.45 7 15 7H17V9C17 9.55 17.45 10 18 10C18.55 10 19 9.55 19 9V6C19 5.45 18.55 5 18 5H15C14.45 5 14 5.45 14 6Z"
                      fill-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div
              class="emotion-35 emotion-11"
              wrap="wrap"
            >
              <div
                class="emotion-37 emotion-11"
              >
                <div>
                  <button
                    class="emotion-32"
                  >
                    <svg
                      class="emotion-29 emotion-30"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.71 6.70998C14.32 6.31998 13.69 6.31998 13.3 6.70998L8.70998 11.3C8.31998 11.69 8.31998 12.32 8.70998 12.71L13.3 17.3C13.69 17.69 14.32 17.69 14.71 17.3C15.1 16.91 15.1 16.28 14.71 15.89L10.83 12L14.71 8.11998C15.1 7.72998 15.09 7.08998 14.71 6.70998Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
                <div
                  class="emotion-42 emotion-27"
                >
                  <button
                    class="emotion-44"
                    type="button"
                  >
                    <svg
                      class="emotion-45 emotion-30"
                      color="#006FFF"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M20 3H19V2C19 1.45 18.55 1 18 1C17.45 1 17 1.45 17 2V3H7V2C7 1.45 6.55 1 6 1C5.45 1 5 1.45 5 2V3H4C2.9 3 2 3.9 2 5V21C2 22.1 2.9 23 4 23H20C21.1 23 22 22.1 22 21V5C22 3.9 21.1 3 20 3ZM19 21H5C4.45 21 4 20.55 4 20V8H20V20C20 20.55 19.55 21 19 21Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                    <span
                      class="emotion-47 emotion-48"
                      color="rgba(0,0,0,0.7)"
                    >
                      01/06/23
                    </span>
                  </button>
                </div>
                <div>
                  <button
                    class="emotion-32"
                  >
                    <svg
                      class="emotion-29 emotion-30"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M9.2925 6.29376C8.9025 6.68376 8.9025 7.31376 9.2925 7.70376L13.1725 11.5838L9.2925 15.4638C8.9025 15.8538 8.9025 16.4838 9.2925 16.8738C9.6825 17.2638 10.3125 17.2638 10.7025 16.8738L15.2925 12.2838C15.6825 11.8938 15.6825 11.2638 15.2925 10.8738L10.7025 6.28376C10.3225 5.90376 9.6825 5.90376 9.2925 6.29376Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div
                class="emotion-52 emotion-53"
              >
                <div
                  class="emotion-42 emotion-27"
                >
                  <div>
                    <button
                      aria-label="All sites-filter"
                      class="emotion-56"
                      type="button"
                    >
                      <div
                        class="emotion-57"
                      >
                        <span
                          class="emotion-47 emotion-48"
                          color="rgba(0,0,0,0.7)"
                        >
                          Apella Lab
                        </span>
                        <svg
                          class="emotion-60 emotion-30"
                          viewBox="0 0 24 24"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M7.41289 11.3815L11.0199 14.9885C11.563 15.5316 12.4404 15.5316 12.9835 14.9885L16.5905 11.3815C17.4679 10.5041 16.8412 9 15.6017 9H8.38776C7.14829 9 6.53552 10.5041 7.41289 11.3815Z"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
              <div
                class="emotion-52 emotion-53"
              >
                <div
                  class="emotion-42 emotion-27"
                >
                  <button
                    aria-label="All rooms-filter"
                    class="emotion-56"
                    type="button"
                  >
                    <div
                      class="emotion-57"
                    >
                      <span
                        class="emotion-68 emotion-48"
                        color="rgba(0,0,0,0.4)"
                      >
                        All rooms
                      </span>
                      <svg
                        class="emotion-60 emotion-30"
                        viewBox="0 0 24 24"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M7.41289 11.3815L11.0199 14.9885C11.563 15.5316 12.4404 15.5316 12.9835 14.9885L16.5905 11.3815C17.4679 10.5041 16.8412 9 15.6017 9H8.38776C7.14829 9 6.53552 10.5041 7.41289 11.3815Z"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </div>
                  </button>
                </div>
              </div>
              <div
                class="emotion-42 emotion-27"
              >
                <button
                  aria-label="surgeon"
                  class="emotion-56"
                  type="button"
                >
                  <div
                    class="emotion-57"
                  >
                    <span
                      class="emotion-68 emotion-48"
                      color="rgba(0,0,0,0.4)"
                    >
                      All surgeons
                    </span>
                    <svg
                      class="emotion-60 emotion-30"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.41289 11.3815L11.0199 14.9885C11.563 15.5316 12.4404 15.5316 12.9835 14.9885L16.5905 11.3815C17.4679 10.5041 16.8412 9 15.6017 9H8.38776C7.14829 9 6.53552 10.5041 7.41289 11.3815Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                </button>
              </div>
              <button
                class="emotion-80"
                disabled=""
              >
                <div
                  class="emotion-81 emotion-11"
                >
                  <svg
                    class="emotion-45 emotion-30"
                    color="#006FFF"
                    data-testid="checkbox-checked"
                    viewBox="0 0 24 24"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM10.71 16.29C10.32 16.68 9.69 16.68 9.3 16.29L5.71 12.7C5.32 12.31 5.32 11.68 5.71 11.29C6.1 10.9 6.73 10.9 7.12 11.29L10 14.17L16.88 7.29C17.27 6.9 17.9 6.9 18.29 7.29C18.68 7.68 18.68 8.31 18.29 8.7L10.71 16.29Z"
                      fill-rule="evenodd"
                    />
                  </svg>
                  <h6
                    class="emotion-85 emotion-86"
                  >
                    Scheduled
                  </h6>
                </div>
              </button>
            </div>
            <div
              class="emotion-87 emotion-11"
              wrap="nowrap"
            >
              <div
                class="emotion-89 emotion-25"
              >
                <h4
                  class="emotion-91 emotion-7"
                >
                  3
                </h4>
                <span
                  class="emotion-93 emotion-94"
                >
                  Open rooms
                </span>
                <div
                  class="emotion-95"
                >
                  <span
                    class="emotion-96 emotion-97"
                  >
                    16 / 20
                     
                  </span>
                  <span
                    class="emotion-98 emotion-99"
                  >
                    Cases Completed
                  </span>
                </div>
              </div>
              <div
                class="emotion-100 emotion-25"
              >
                <h4
                  class="emotion-91 emotion-7"
                >
                  3
                </h4>
                <span
                  class="emotion-93 emotion-94"
                >
                  Rooms running
                </span>
                <button
                  class="emotion-106"
                >
                  <span
                    class="emotion-98 emotion-99"
                  >
                    View Rooms
                  </span>
                </button>
              </div>
              <div
                class="emotion-100 emotion-25"
              >
                <h4
                  class="emotion-91 emotion-7"
                >
                  3
                </h4>
                <span
                  class="emotion-93 emotion-94"
                >
                  Open past 15:00
                </span>
                <button
                  class="emotion-106"
                >
                  <span
                    class="emotion-98 emotion-99"
                  >
                    View Rooms
                  </span>
                </button>
              </div>
              <div
                class="emotion-100 emotion-25"
              >
                <h4
                  class="emotion-91 emotion-7"
                >
                  2
                </h4>
                <span
                  class="emotion-93 emotion-94"
                >
                  Open past 17:00
                </span>
                <button
                  class="emotion-106"
                >
                  <span
                    class="emotion-98 emotion-99"
                  >
                    View Rooms
                  </span>
                </button>
              </div>
              <div
                class="emotion-100 emotion-25"
              >
                <h4
                  class="emotion-91 emotion-7"
                >
                  2
                </h4>
                <span
                  class="emotion-93 emotion-94"
                >
                  Open past 19:00
                </span>
                <button
                  class="emotion-106"
                >
                  <span
                    class="emotion-98 emotion-99"
                  >
                    View Rooms
                  </span>
                </button>
              </div>
            </div>
            <div
              class="emotion-136"
            >
              <div
                class="emotion-137"
              >
                <div
                  class="emotion-138 emotion-139"
                >
                  <div
                    class="emotion-140 emotion-141"
                  >
                    <div
                      class="emotion-142 emotion-143"
                      style="display: flex; flex-direction: row; align-items: center; border: 1px solid; border-color: #f2f2f2; border-radius: 4px; box-sizing: border-box;"
                    >
                      <button
                        class="emotion-144"
                        style="color: rgb(153, 153, 153); background-color: transparent; padding: 0.25rem;"
                      >
                        <div>
                          <svg
                            class="emotion-29 emotion-30"
                            style="display: flex; align-items: center; justify-content: center; color: rgb(153, 153, 153);"
                            viewBox="0 0 24 24"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M9.61825 0.215041C9.21997 -0.114646 8.62983 -0.0590386 8.30014 0.339243C7.97046 0.737525 8.02606 1.32766 8.42435 1.65735L9.56814 2.60415C7.95164 2.98636 6.44551 3.77518 5.20058 4.91226C3.38387 6.5716 2.2555 8.85191 2.03837 11.3028C1.82124 13.7536 2.53114 16.1968 4.02783 18.1497C5.52453 20.1026 7.69918 21.4231 10.1222 21.8505C12.5453 22.278 15.0405 21.7811 17.115 20.4582C19.1896 19.1353 20.6925 17.0824 21.3271 14.7052C21.9616 12.328 21.6815 9.79924 20.5422 7.61847C19.7434 6.08945 18.5629 4.80947 17.1312 3.89253C16.6958 3.61368 16.1168 3.74057 15.8379 4.17596C15.5591 4.61135 15.686 5.19036 16.1214 5.46921C17.2804 6.21155 18.236 7.24773 18.8827 8.48547C19.805 10.2509 20.0317 12.2979 19.5181 14.2224C19.0044 16.1468 17.7877 17.8086 16.1083 18.8795C14.4289 19.9505 12.409 20.3527 10.4475 20.0067C8.48596 19.6607 6.72553 18.5916 5.51392 17.0107C4.30231 15.4298 3.72763 13.452 3.9034 11.468C4.07918 9.48397 4.99261 7.638 6.46329 6.29473C7.93396 4.95146 9.85491 4.20857 11.8467 4.2128C12.2418 4.21364 12.5949 3.96638 12.7291 3.59482C12.8634 3.22326 12.75 2.8074 12.4456 2.55548L9.61825 0.215041ZM6.35578 15.4468C6.35578 13.4653 7.51677 12.6811 8.39324 12.089C8.92468 11.7301 9.35152 11.4417 9.35152 11C9.35152 10.6724 9.07067 10.3915 8.6962 10.3915C8.04088 10.3915 7.80684 11 7.80684 11L6.30897 10.2979C6.30897 10.2979 6.91748 8.70642 8.83662 8.70642C10.2409 8.70642 11.2239 9.68939 11.2239 10.8596C11.2239 11.9713 10.4497 12.4922 9.73475 12.9733C9.36592 13.2215 9.01282 13.4591 8.78982 13.7617H11.2239V15.4468H6.35578ZM14.9166 15.4469V14.4171H11.9677V12.5916L14.7294 8.89372H16.6953V12.8256H17.3974V14.4171H16.6953V15.4469H14.9166ZM13.5591 12.8256H14.9166V11.0001L13.5591 12.8256Z"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </div>
                      </button>
                      <button
                        class="emotion-144"
                        style="color: rgb(153, 153, 153); background-color: transparent; padding: 0.25rem;"
                      >
                        <div>
                          <svg
                            class="emotion-29 emotion-30"
                            style="display: flex; align-items: center; justify-content: center; color: rgb(153, 153, 153);"
                            viewBox="0 0 24 24"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M23.785 11.6182C24.1147 11.22 24.059 10.6298 23.6608 10.3001C23.2625 9.97045 22.6723 10.0261 22.3427 10.4243L21.3959 11.5681C21.0136 9.95164 20.2248 8.44551 19.0877 7.20058C17.4284 5.38386 15.1481 4.2555 12.6973 4.03837C10.2464 3.82124 7.80323 4.53114 5.85035 6.02783C3.89747 7.52452 2.57689 9.69917 2.14948 12.1222C2.04796 12.6977 1.99858 13.2773 2.00003 13.8544C2.00133 14.3714 2.42152 14.7895 2.93855 14.7882C3.45557 14.7869 3.87366 14.3667 3.87236 13.8497C3.87119 13.3826 3.91116 12.9134 3.99335 12.4475C4.33935 10.486 5.40839 8.72553 6.98929 7.51392C8.57019 6.30231 10.548 5.72763 12.532 5.9034C14.516 6.07917 16.362 6.99261 17.7053 8.46328C19.0486 9.93396 19.7914 11.8549 19.7872 13.8467C19.7864 14.2418 20.0336 14.5949 20.4052 14.7291C20.7767 14.8634 21.1926 14.75 21.4445 14.4456L23.785 11.6182ZM11.7127 8.21276C8.61052 8.21276 6.0957 10.7276 6.0957 13.8298C6.0957 14.5674 6.24099 15.2978 6.52327 15.9793C6.80555 16.6608 7.2193 17.28 7.74089 17.8016L7.74991 17.8106C8.76567 18.8218 10.1662 19.4468 11.7127 19.4468C14.8149 19.4468 17.3297 16.932 17.3297 13.8298C17.3297 10.7276 14.8149 8.21276 11.7127 8.21276ZM15.4574 13.8298H7.96804C7.96804 11.7616 9.64459 10.0851 11.7127 10.0851C13.7808 10.0851 15.4574 11.7616 15.4574 13.8298Z"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </div>
                      </button>
                      <button
                        class="emotion-144"
                        style="color: rgb(153, 153, 153); background-color: rgb(204, 226, 255); padding: 0.25rem;"
                      >
                        <div>
                          <svg
                            class="emotion-29 emotion-30"
                            style="display: flex; align-items: center; justify-content: center; color: rgb(25, 25, 25);"
                            viewBox="0 0 24 24"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M2 2.5C2 2.22386 2.22386 2 2.5 2H3.5C3.77614 2 4 2.22386 4 2.5V21.5C4 21.7761 3.77614 22 3.5 22H2.5C2.22386 22 2 21.7761 2 21.5V2.5ZM20 2.5C20 2.22386 20.2239 2 20.5 2H21.5C21.7761 2 22 2.22386 22 2.5V21.5C22 21.7761 21.7761 22 21.5 22H20.5C20.2239 22 20 21.7761 20 21.5V2.5ZM5.5 4C5.22386 4 5 4.22386 5 4.5V8.5C5 8.77614 5.22386 9 5.5 9H10.5C10.7761 9 11 8.77614 11 8.5V4.5C11 4.22386 10.7761 4 10.5 4H5.5ZM8 10.5C8 10.2239 8.22386 10 8.5 10H16.5C16.7761 10 17 10.2239 17 10.5V14.5C17 14.7761 16.7761 15 16.5 15H8.5C8.22386 15 8 14.7761 8 14.5V10.5ZM13.5 16C13.2239 16 13 16.2239 13 16.5V20.5C13 20.7761 13.2239 21 13.5 21H18.5C18.7761 21 19 20.7761 19 20.5V16.5C19 16.2239 18.7761 16 18.5 16H13.5Z"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </div>
                      </button>
                    </div>
                  </div>
                  <div
                    class="emotion-153 emotion-154"
                  >
                    <div
                      class="emotion-155"
                    >
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          style="position: relative; color: rgba(0, 0, 0, 0.7); left: 0px;"
                        >
                          <span
                            class="emotion-98 emotion-99"
                            style="line-height: inherit; white-space: nowrap;"
                          >
                            08:00
                          </span>
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          style="position: relative; color: rgba(0, 0, 0, 0.7); left: 0px;"
                        >
                          <span
                            class="emotion-98 emotion-99"
                            style="line-height: inherit; white-space: nowrap;"
                          >
                            13:00
                          </span>
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        style="position: absolute; left: 0px;"
                      >
                        <div
                          class="emotion-158"
                        >
                          <div
                            class="emotion-159"
                          />
                        </div>
                      </div>
                      <div
                        class="emotion-180"
                        style="left: 11.481478395061728%;"
                      >
                        <div
                          class="emotion-181"
                        >
                          09:01
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-182 emotion-139"
                >
                  <div
                    class="emotion-184 emotion-141"
                  >
                    <div
                      class="emotion-186"
                    >
                      <div
                        class="emotion-187"
                      >
                        <p
                          class="emotion-188 emotion-189"
                        >
                          Garage 0
                        </p>
                        <p
                          class="emotion-190 emotion-191"
                        >
                          Apella Lab
                        </p>
                        <p
                          class="emotion-192 emotion-191"
                        >
                          CLOSED
                          <span
                            class="emotion-194"
                          />
                        </p>
                      </div>
                      <div>
                        <button
                          class="emotion-106"
                        >
                          <span
                            class="emotion-98 emotion-99"
                          >
                            View live
                          </span>
                        </button>
                      </div>
                    </div>
                    <div
                      class="emotion-186"
                    >
                      <div
                        class="emotion-187"
                      >
                        <p
                          class="emotion-188 emotion-189"
                        >
                          Garage 1
                        </p>
                        <p
                          class="emotion-190 emotion-191"
                        >
                          Apella Lab
                        </p>
                        <p
                          class="emotion-204 emotion-191"
                        >
                          TURNOVER
                          <span
                            class="emotion-194"
                          >
                             -428M
                          </span>
                        </p>
                      </div>
                      <div>
                        <button
                          class="emotion-106"
                        >
                          <span
                            class="emotion-98 emotion-99"
                          >
                            View live
                          </span>
                        </button>
                      </div>
                    </div>
                    <div
                      class="emotion-186"
                    >
                      <div
                        class="emotion-187"
                      >
                        <p
                          class="emotion-188 emotion-189"
                        >
                          Garage 2
                        </p>
                        <p
                          class="emotion-190 emotion-191"
                        >
                          Apella Lab
                        </p>
                        <p
                          class="emotion-192 emotion-191"
                        >
                          CLOSED
                          <span
                            class="emotion-194"
                          />
                        </p>
                      </div>
                      <div>
                        <button
                          class="emotion-106"
                        >
                          <span
                            class="emotion-98 emotion-99"
                          >
                            View live
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-222 emotion-154"
                  >
                    <div
                      class="emotion-224"
                    >
                      <div
                        class="emotion-225"
                        data-testid="room-case-timeline-row"
                        style="cursor: default;"
                      >
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  Bailey, John, 08:09  - 09:14
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  Colonoscopy; Angiogram
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-238 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 08:12 -
  09:47
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  House, John, 09:56  - 11:10
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  Angiogram
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-238 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 11:45 -
  12:59
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  House, Gregory, 11:48  - 12:48
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  I & D Abdomen
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-238 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 15:11 -
  16:31
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  13:03  - 14:25
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  15:17  - 16:11
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-302 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  House, John, 12:19  - 12:19
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  Knee Arthroplasty JRP - Total Replacement
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-312 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 13:09 -
  15:00
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-302 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  Wu, Henry, 12:19  - 12:19
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  Knee Arthroplasty JRP - Total Replacement
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-312 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 09:54 -
  11:32
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        class="emotion-225"
                        data-testid="room-case-timeline-row"
                        style="cursor: default;"
                      >
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  Dorian, John; Wu, Gregory, 08:14  - 09:40
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  Knee Arthroplasty JRP - Total Replacement
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-238 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 08:12 -
  10:07
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  Bailey, Gregory, 10:11  - 11:06
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  I & D Abdomen
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-238 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 11:52 -
  13:27
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  House, Miranda, 11:55  - 13:08
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  Angiogram
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-238 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 15:08 -
  16:35
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  13:43  - 14:32
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  15:09  - 16:10
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-302 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  Wu, Henry; Dorian, Gregory, 12:19  - 12:19
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  Angiogram
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-312 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 13:39 -
  14:55
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-302 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  Bailey, Gregory, 12:19  - 12:19
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                >
                                  Angiogram
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 16px; top: 58px;"
                        >
                          <div>
                            <div
                              class="emotion-312 emotion-229"
                            >
                              <div
                                class="emotion-240 emotion-1911"
                              >
                                <span
                                  class="emotion-242 emotion-99"
                                >
                                  SCH: 10:16 -
  11:36
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        class="emotion-225"
                        data-testid="room-case-timeline-row"
                        style="cursor: default;"
                      >
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  08:09  - 09:06
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  09:46  - 10:29
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  10:46  - 11:25
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  12:03  - 12:41
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  12:59  - 14:07
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-226 emotion-227"
                          style="left: 0px; width: 0px; height: 44px; top: 7px;"
                        >
                          <div>
                            <div
                              class="emotion-228 emotion-229"
                            >
                              <div
                                class="emotion-230 emotion-1911"
                              >
                                <span
                                  class="emotion-96 emotion-97"
                                >
                                  14:56  - 15:51
                                </span>
                                <br />
                                <span
                                  class="emotion-98 emotion-99"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        class="emotion-508"
                        style="left: 11.481478395061728%;"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="emotion-509"
            >
              <div
                class="emotion-510 emotion-11"
              >
                <div
                  class="emotion-512 emotion-11"
                >
                  <svg
                    class="emotion-514 emotion-30"
                    color="#999"
                    viewBox="0 0 24 24"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M12.6499 10.0001C11.6999 7.31012 8.89994 5.50012 5.76994 6.12012C3.47994 6.58012 1.61994 8.41012 1.13994 10.7001C0.31994 14.5701 3.25994 18.0001 6.99994 18.0001C9.60994 18.0001 11.8299 16.3301 12.6499 14.0001H16.9999V16.0001C16.9999 17.1001 17.8999 18.0001 18.9999 18.0001C20.0999 18.0001 20.9999 17.1001 20.9999 16.0001V14.0001C22.0999 14.0001 22.9999 13.1001 22.9999 12.0001C22.9999 10.9001 22.0999 10.0001 20.9999 10.0001H12.6499ZM6.99994 14.0001C5.89994 14.0001 4.99994 13.1001 4.99994 12.0001C4.99994 10.9001 5.89994 10.0001 6.99994 10.0001C8.09994 10.0001 8.99994 10.9001 8.99994 12.0001C8.99994 13.1001 8.09994 14.0001 6.99994 14.0001Z"
                      fill-rule="evenodd"
                    />
                  </svg>
                  <span
                    class="emotion-516 emotion-1899"
                  >
                    KEY
                  </span>
                </div>
                <div
                  class="emotion-518 emotion-11"
                  wrap="wrap"
                >
                  <div
                    class="emotion-520 emotion-229"
                  >
                    <span
                      class="emotion-522 emotion-99"
                    >
                      Prep
                    </span>
                  </div>
                  <div
                    class="emotion-524 emotion-229"
                  >
                    <span
                      class="emotion-522 emotion-99"
                    >
                      Surgery
                    </span>
                  </div>
                  <div
                    class="emotion-528 emotion-229"
                  >
                    <span
                      class="emotion-522 emotion-99"
                    >
                      Wrap-up
                    </span>
                  </div>
                  <div
                    class="emotion-532 emotion-229"
                  >
                    <span
                      class="emotion-534 emotion-99"
                    >
                      In Fac
                    </span>
                  </div>
                  <div
                    class="emotion-536 emotion-229"
                  >
                    <span
                      class="emotion-534 emotion-99"
                    >
                      Pre Proc
                    </span>
                  </div>
                  <div
                    class="emotion-540 emotion-229"
                  >
                    <span
                      class="emotion-534 emotion-99"
                    >
                      In Hold
                    </span>
                  </div>
                  <div
                    class="emotion-544 emotion-229"
                  >
                    <span
                      class="emotion-534 emotion-99"
                    >
                      Recovery
                    </span>
                  </div>
                  <div
                    class="emotion-548 emotion-229"
                  >
                    <span
                      class="emotion-534 emotion-99"
                    >
                      Phase II
                    </span>
                  </div>
                  <div
                    class="emotion-552 emotion-229"
                  >
                    <span
                      class="emotion-522 emotion-99"
                    >
                      Actual
                    </span>
                  </div>
                  <div
                    class="emotion-556 emotion-229"
                  >
                    <span
                      class="emotion-522 emotion-99"
                    >
                      Scheduled
                    </span>
                  </div>
                  <div
                    class="emotion-560 emotion-229"
                  >
                    <span
                      class="emotion-522 emotion-99"
                    >
                      Forecast
                    </span>
                  </div>
                </div>
                <div>
                  <button
                    class="emotion-564"
                  >
                    <svg
                      class="emotion-565 emotion-30"
                      color="#bfbfbf"
                      cursor="pointer"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M18.3 5.71021C17.91 5.32021 17.28 5.32021 16.89 5.71021L12 10.5902L7.10997 5.70021C6.71997 5.31021 6.08997 5.31021 5.69997 5.70021C5.30997 6.09021 5.30997 6.72021 5.69997 7.11021L10.59 12.0002L5.69997 16.8902C5.30997 17.2802 5.30997 17.9102 5.69997 18.3002C6.08997 18.6902 6.71997 18.6902 7.10997 18.3002L12 13.4102L16.89 18.3002C17.28 18.6902 17.91 18.6902 18.3 18.3002C18.69 17.9102 18.69 17.2802 18.3 16.8902L13.41 12.0002L18.3 7.11021C18.68 6.73021 18.68 6.09021 18.3 5.71021Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-567 emotion-568"
          >
            <div
              class="emotion-569 emotion-11"
              direction="column"
            >
              <div
                class="emotion-571 emotion-572"
              >
                <div
                  class="emotion-512 emotion-11"
                >
                  <svg
                    class="emotion-575 emotion-30"
                    viewBox="0 0 24 24"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M3 17.4601V20.5001C3 20.7801 3.22 21.0001 3.5 21.0001H6.54C6.67 21.0001 6.8 20.9501 6.89 20.8501L17.81 9.94006L14.06 6.19006L3.15 17.1001C3.05 17.2001 3 17.3201 3 17.4601ZM20.71 7.04006C21.1 6.65006 21.1 6.02006 20.71 5.63006L18.37 3.29006C17.98 2.90006 17.35 2.90006 16.96 3.29006L15.13 5.12006L18.88 8.87006L20.71 7.04006Z"
                      fill-rule="evenodd"
                    />
                  </svg>
                  <h2
                    class="emotion-577 emotion-15"
                  >
                    Edit Schedule
                  </h2>
                </div>
                <button
                  class="emotion-564"
                >
                  <svg
                    class="emotion-29 emotion-30"
                    viewBox="0 0 24 24"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M18.3 5.71021C17.91 5.32021 17.28 5.32021 16.89 5.71021L12 10.5902L7.10997 5.70021C6.71997 5.31021 6.08997 5.31021 5.69997 5.70021C5.30997 6.09021 5.30997 6.72021 5.69997 7.11021L10.59 12.0002L5.69997 16.8902C5.30997 17.2802 5.30997 17.9102 5.69997 18.3002C6.08997 18.6902 6.71997 18.6902 7.10997 18.3002L12 13.4102L16.89 18.3002C17.28 18.6902 17.91 18.6902 18.3 18.3002C18.69 17.9102 18.69 17.2802 18.3 16.8902L13.41 12.0002L18.3 7.11021C18.68 6.73021 18.68 6.09021 18.3 5.71021Z"
                      fill-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
              <div
                class="emotion-582 emotion-25"
              >
                <div
                  class="emotion-584 emotion-11"
                >
                  <h3
                    class="emotion-86 emotion-587 emotion-1915"
                    color="rgba(0,0,0,0.4)"
                  >
                    Select a case to edit
                  </h3>
                </div>
              </div>
              <div
                class="emotion-589 emotion-25"
              />
              <div
                class="emotion-591 emotion-25"
              >
                <div
                  class="emotion-593 emotion-11"
                  direction="Column"
                >
                  <button
                    class="emotion-595"
                    disabled=""
                  >
                    <h2
                      class="emotion-15 emotion-597 emotion-1916"
                    >
                      Clear changes
                    </h2>
                  </button>
                  <button
                    class="emotion-599"
                    disabled=""
                  >
                    <h2
                      class="emotion-15 emotion-597 emotion-1916"
                    >
                      Publish changes
                    </h2>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
