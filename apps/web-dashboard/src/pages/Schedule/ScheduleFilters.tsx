import { useMemo, useState } from 'react'

import { sortBy, uniqBy } from 'lodash'

import {
  Button,
  Counter,
  mediaQueries,
  MultiSelect,
  Option,
  P3,
  remSpacing,
  theme,
  Tune,
} from '@apella/component-library'
import { FilterBarContainer } from 'src/components/PageContentTemplate'
import { SingleDatePicker } from 'src/components/SingleDatePicker'
import { useScheduleContext } from 'src/pages/Schedule/ScheduleContextProvider'
import { useScheduleFilterContext } from 'src/pages/Schedule/ScheduleFilterContext'
import { ScheduleFilterOptions } from 'src/pages/Schedule/types'

import { ResetFilters } from '../../components/ResetFilters'
import { SitesRoomsFilter } from '../../components/SitesRoomsFilter'
import { ToggleSelect } from '../../components/ToggleSelect'
import { isPrimarySurgeon } from '../../utils/roles'
import { ScheduleFiltersBlade } from './ScheduleFiltersBlade'
import { TimeRangeSelector } from './TimeRangeSelector'

export const ScheduleFilters = (options?: ScheduleFilterOptions) => {
  const showSurgeonFilter = +(options?.showSurgeonFilter ?? true)
  const showToggleScheduledCases = +(options?.showToggleScheduledCases ?? true)
  const showDateChangeFilter = +(options?.showDateChangeFilter ?? true)
  const showSiteFilter = +(options?.showSiteFilter ?? true)
  const showRoomFilter = +(options?.showRoomFilter ?? true)
  const showTimePeriodFilter = +(options?.showTimePeriodFilter ?? false)
  const dateChangeOptions = options?.dateChangeOptions

  const numFiltersEnabled =
    showSurgeonFilter +
    showToggleScheduledCases +
    showDateChangeFilter +
    showSiteFilter +
    showRoomFilter +
    showTimePeriodFilter

  const isBladeFilterEnabled = numFiltersEnabled > 2

  const { showPanel: disabled, timelineState } = useScheduleContext()
  const [isBladeOpen, setIsBladeOpen] = useState(false)
  const {
    sites,
    rooms,
    selectedSurgeons,
    onChangeSurgeons: setSelectedSurgeons,
    roomIds,
    minTime,
    siteIds,
    showResetFiltersButton,
    showScheduled,
    numFiltersApplied,
    onChangeDate,
    onChangeSites,
    onToggleScheduled,
    onChangeRooms,
    resetFilters,
    timeRange,
    onChangeTimeRange,
    showFilters,
  } = useScheduleFilterContext()

  const siteName = useMemo(
    () =>
      sites
        .filter(({ node: { id } }) => siteIds?.includes(id))
        .map(({ node: { name } }) => name)
        .join(', '),
    [sites, siteIds]
  )

  const primarySurgeons = useMemo(() => {
    if (!showSurgeonFilter) {
      return []
    }
    const cases = timelineState.rooms
      .flatMap((r) => r.cases.map((c) => c.case))
      .filter(Boolean)
    const staff = cases
      .flatMap((caseObj) => caseObj.staff)
      .filter(isPrimarySurgeon)
    const uniqStaff = uniqBy(staff, (s) => s.id)
    return sortBy(uniqStaff, (s) => [s.lastName, s.firstName])
  }, [timelineState, showSurgeonFilter])

  const BladeFilters = (
    <div
      css={{
        display: 'grid',
        justifyContent: 'space-between',
        gridTemplateAreas: '"sites sites" "site-filter filter-blade-button"',
        gap: remSpacing.medium,
        width: '100%',
      }}
    >
      <P3
        css={{
          color: theme.palette.gray[50],
          display: 'inline',
          width: '100%',
          gridArea: 'sites',
        }}
      >
        {`Viewing site${siteIds && siteIds.length > 1 ? `s` : ''} `}
        <span css={{ color: 'black' }}>{siteName}</span>
      </P3>
      {!!showDateChangeFilter && (
        <div css={{ gridArea: 'site-filter' }}>
          <SingleDatePicker
            onChangeDate={onChangeDate}
            date={minTime}
            showAdvanceByDay
            calendarProps={dateChangeOptions}
          />
        </div>
      )}

      <div
        css={{
          position: 'relative',
          gridArea: 'filter-blade-button',
        }}
      >
        {!!numFiltersApplied && (
          <div css={{ position: 'absolute', top: -4, right: -4 }}>
            <Counter count={numFiltersApplied} size={'sm'} color={'red'} />
          </div>
        )}
        <Button onClick={() => setIsBladeOpen(true)} color={'alternate'}>
          <Tune />
        </Button>
      </div>
    </div>
  )

  const StandardFilters = (
    <>
      {!!showDateChangeFilter && (
        <SingleDatePicker
          onChangeDate={onChangeDate}
          date={minTime}
          showAdvanceByDay
          calendarProps={dateChangeOptions}
        />
      )}
      {(!!showSiteFilter || !!showRoomFilter) && (
        <SitesRoomsFilter
          sites={showSiteFilter ? sites : []}
          selectedSiteIds={siteIds}
          onChangeSites={onChangeSites}
          rooms={showRoomFilter ? rooms : []}
          selectedRoomIds={roomIds}
          onChangeRooms={onChangeRooms}
          multipleSites={true}
          bulkSelectRooms={true}
          disableSelectAllOption={false}
          bulkSelectSites={false}
        />
      )}
      {!!showSurgeonFilter && (
        <MultiSelect
          name={'surgeon'}
          value={selectedSurgeons}
          onChange={setSelectedSurgeons}
          label={'All surgeons'}
          bulkSelect={true}
          search={true}
        >
          {primarySurgeons.map((p) => (
            <Option
              key={p.id}
              label={`${p.lastName}, ${p.firstName}`}
              value={p.id}
            />
          ))}
        </MultiSelect>
      )}
      {!!showTimePeriodFilter && !!timeRange && onChangeTimeRange && (
        <TimeRangeSelector
          timeRange={timeRange}
          onTimeRangeChange={onChangeTimeRange}
        />
      )}
      {!!showToggleScheduledCases && (
        <ToggleSelect
          onClick={onToggleScheduled}
          value={showScheduled}
          label={'Scheduled'}
          disabled={disabled}
        />
      )}
      {showResetFiltersButton && <ResetFilters resetActions={resetFilters} />}
    </>
  )

  if (!showFilters) {
    return null
  }

  return (
    <>
      {!!isBladeFilterEnabled && (
        <ScheduleFiltersBlade
          isBladeOpen={isBladeOpen}
          onBladeClose={() => setIsBladeOpen(false)}
          surgeonOptions={primarySurgeons}
          {...options}
        />
      )}
      <div
        css={{
          display: !!isBladeFilterEnabled ? 'block' : 'none',
          [mediaQueries.lg]: { display: 'none' },
        }}
      >
        <FilterBarContainer>{BladeFilters}</FilterBarContainer>
      </div>
      <div
        css={{
          display: !!isBladeFilterEnabled ? 'none' : 'block',
          [mediaQueries.lg]: { display: 'block' },
        }}
      >
        <FilterBarContainer>{StandardFilters}</FilterBarContainer>
      </div>
    </>
  )
}
