import { useMemo } from 'react'

import { DateTime, Duration } from 'luxon'

import { useTimezone } from 'src/Contexts'
import { BigBoardCase } from 'src/modules/board/types'
import {
  TimelineDateTimeBounds,
  TimelineHourBoundsCalculationMethod,
  TimeRange,
} from 'src/pages/Schedule/types'
import { ApellaCase } from 'src/pages/types'
import { useCurrentMinute } from 'src/utils/useCurrentTime'

export const useTimelineHourBounds = <Case extends ApellaCase | BigBoardCase>({
  minTime,
  maxTime,
  cases,
  calculationMethod,
  primeTime,
}: {
  minTime: string
  maxTime: string
  cases: Case[]
  calculationMethod?: TimelineHourBoundsCalculationMethod
  primeTime?: { startTime: DateTime; endTime: DateTime }
}): TimelineDateTimeBounds => {
  const { timezone } = useTimezone()

  const minTimeDateTime = DateTime.fromISO(minTime).setZone(timezone)
  const maxTimeDateTime = DateTime.fromISO(maxTime).setZone(timezone)
  const currentMinute = useCurrentMinute(calculationMethod !== 'sliding')

  const apellaCases = cases.filter(
    (ac) =>
      (ac.endTime ?? DateTime.now()) >= minTimeDateTime &&
      ac.startTime <= maxTimeDateTime
  )

  const scheduledCases = cases
    .flatMap((c) => c.case)
    .filter(Boolean)
    .filter(
      (c) =>
        c &&
        c.scheduledEndTime >= minTimeDateTime &&
        c.scheduledStartTime <= maxTimeDateTime
    )

  // Generate a DateTime[] of every hour that encapsulates the data - used to
  // show the headers at the top of the page.
  const { minHour, maxHour } = useMemo(() => {
    if (calculationMethod === 'sliding') {
      return {
        minHour: currentMinute.minus({ hour: 1 }),
        maxHour: maxTimeDateTime,
        calculationMethod,
      }
    }

    if (calculationMethod === 'fullTimeBounded') {
      return {
        minHour: minTimeDateTime.startOf('hour'),
        maxHour: maxTimeDateTime.startOf('hour'),
        calculationMethod,
      }
    }

    if (calculationMethod === TimeRange.PrimeTime && primeTime) {
      const isEndTimeOnHour = primeTime.endTime.minute === 0
      return {
        minHour: minTimeDateTime
          .startOf('day')
          .plus({ hours: primeTime.startTime.hour }),
        maxHour: minTimeDateTime.startOf('day').plus({
          hours: isEndTimeOnHour
            ? primeTime.endTime.hour
            : primeTime.endTime.hour + 1,
        }),
        calculationMethod,
      }
    }

    const hasNoPhasesOrCases =
      apellaCases.length === 0 && scheduledCases.length === 0

    const showFullRange = calculationMethod === 'fullTime' || hasNoPhasesOrCases

    if (showFullRange) {
      return {
        minHour: minTimeDateTime,
        maxHour: maxTimeDateTime,
        calculationMethod,
      }
    }

    const caseStartTime = DateTime.min(
      ...apellaCases.map((datum) => datum.startTime),
      ...scheduledCases.map((c) => c.scheduledStartTime)
    ).setZone(timezone)

    const caseEndTime = DateTime.max(
      ...apellaCases.map((datum) => datum.endTime ?? DateTime.now()),
      ...scheduledCases.map((c) => (c ? c.scheduledEndTime : DateTime.now()))
    )
      .plus(Duration.fromObject({ hours: 1 }))
      .setZone(timezone)

    if (calculationMethod === 'dayBound') {
      return {
        minHour: DateTime.max(
          caseStartTime,
          minTimeDateTime.startOf('day')
        ).startOf('hour'),
        maxHour: DateTime.min(
          caseEndTime,
          maxTimeDateTime.plus({ day: 1 }).startOf('day')
        ).startOf('hour'),
        calculationMethod,
      }
    }

    return {
      minHour: caseStartTime.startOf('hour'),
      maxHour: caseEndTime.startOf('hour'),
      calculationMethod,
      currentMinute,
    }
  }, [
    calculationMethod,
    apellaCases,
    scheduledCases,
    timezone,
    currentMinute,
    minTimeDateTime,
    maxTimeDateTime,
    primeTime,
  ])

  return {
    minDateTime: minHour,
    maxDateTime: maxHour,
    calculationMethod,
  }
}
