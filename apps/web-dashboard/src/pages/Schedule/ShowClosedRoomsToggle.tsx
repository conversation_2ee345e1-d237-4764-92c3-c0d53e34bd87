import { useCallback } from 'react'

import { Button, ClosedRoom, Tooltip } from '@apella/component-library'

import { useScheduleFilterContext } from './ScheduleFilterContext'

export const ShowClosedRoomsToggle = () => {
  const { showClosedRooms, onToggleShowClosedRooms } =
    useScheduleFilterContext()

  const onClick = useCallback(
    () => onToggleShowClosedRooms(!showClosedRooms),
    [onToggleShowClosedRooms, showClosedRooms]
  )

  return (
    <Tooltip
      body={showClosedRooms ? 'Hide closed rooms' : 'Show closed rooms'}
      placement="bottom"
    >
      <Button
        color={showClosedRooms ? 'active-gray' : 'alternate'}
        buttonType="icon"
        onClick={onClick}
      >
        <ClosedRoom size="sm" />
      </Button>
    </Tooltip>
  )
}
