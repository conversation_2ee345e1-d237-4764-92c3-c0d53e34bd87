import { Link } from 'react-router'

import { useFlags } from 'launchdarkly-react-client-sdk'
import { rem } from 'polished'

import { remSpacing, P3 } from '@apella/component-library'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { LocationPath } from 'src/router/types'

import { useScheduleFilterProviderUrls } from './ScheduleFilterProvider'

interface RoomScheduleNameProps {
  caseLength: number
  roomId: string
  roomNameSchedule: string
}
export const RoomScheduleName = ({
  roomId,
  roomNameSchedule,
  caseLength,
}: RoomScheduleNameProps) => {
  const { allSearchParams } = useScheduleFilterProviderUrls()
  const { assignStaffPlanningEnabled } = useFlags<WebDashboardFeatureFlagSet>()

  const casePlanningSearchParams = new URLSearchParams(allSearchParams)
  casePlanningSearchParams.set('roomIds', roomId)
  return caseLength > 0 && assignStaffPlanningEnabled ? (
    <Link
      to={{
        pathname: LocationPath.StaffPlanning,
        search: casePlanningSearchParams.toString(),
      }}
      css={{
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'center',
        padding: `${rem('2px')} ${remSpacing.xsmall} 0`,
        textDecoration: 'none',
      }}
    >
      <P3
        css={{
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          fontWeight: 'bold',
        }}
      >
        {roomNameSchedule}
      </P3>
    </Link>
  ) : (
    <P3
      css={{
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        padding: `0 ${remSpacing.xsmall}`,
      }}
    >
      {roomNameSchedule}
    </P3>
  )
}
