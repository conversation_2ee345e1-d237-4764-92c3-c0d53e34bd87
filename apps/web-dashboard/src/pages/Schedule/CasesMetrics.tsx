import { CaseType } from 'src/__generated__/globalTypes'
import { <PERSON>ricHeader } from 'src/components/MetricHeader'

import { ApellaCase } from '../types'
import { useScheduleFilterContext } from './ScheduleFilterContext'

export interface CasesMetricsProps {
  cases: Pick<ApellaCase, 'type'>[]
  isLoading: boolean
}

export const CasesMetrics = ({ isLoading, cases }: CasesMetricsProps) => {
  const { showMetrics } = useScheduleFilterContext()

  let totalCases, casesCompleted, casesInProgress, casesNotStarted
  if (!isLoading) {
    totalCases = cases.length
    casesCompleted = cases.filter(
      (p) => p.type !== CaseType.LIVE && p.type !== CaseType.FORECAST
    ).length
    casesInProgress = cases.filter((p) => p.type === CaseType.LIVE).length
    casesNotStarted = totalCases - casesCompleted - casesInProgress
  }

  const metrics = [
    { label: 'Total cases', value: totalCases },
    { label: 'Cases completed', value: casesCompleted },
    { label: 'Cases in progress', value: casesInProgress },
    { label: 'Cases not started', value: casesNotStarted },
  ]

  if (!showMetrics) {
    return null
  }

  return <MetricHeader metrics={metrics} />
}
