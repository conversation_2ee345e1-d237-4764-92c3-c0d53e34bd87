import { ComponentProps } from 'react'

import { DateTime } from 'luxon'

import { SingleDatePicker } from 'src/components/SingleDatePicker'
import { DailyMetrics } from 'src/modules/daily-metrics/types'

import {
  CaseStatusName,
  RoomStatusName,
  TurnoverType,
} from '../../__generated__/globalTypes'
import {
  TurnoverLengthWithGoal,
  TurnoverLengthWithoutGoal,
} from '../../utils/status'
import { ApellaBlockTime, ApellaCase, Turnover } from '../types'

export const SCHEDULE_TOAST_ID = 'schedule-toast-id'

export type ScheduleFilterOptions = Partial<{
  dateChangeOptions: ComponentProps<typeof SingleDatePicker>['calendarProps']
  showDateChangeFilter: boolean
  showSiteFilter: boolean
  showRoomFilter: boolean
  showSurgeonFilter: boolean
  showTimePeriodFilter: boolean
  showToggleScheduledCases: boolean
}>

export type ScheduleViewOnClickHandler = (
  roomId: string,
  clickTime: DateTime,
  apellaCase?: ApellaCase,
  turnover?: Turnover
) => void

export interface IdMapping {
  [key: string]: string[]
}

export interface MouseHoverState {
  [key: string]: boolean
}

export type MouseMovementHandler = (id?: string) => void

export interface ApellaEvent {
  color: string
  id: string
  label: string
  name: string
  sourceType?: string
  startTime: DateTime
  type: string
}

export interface RoomShape {
  id: string
  name: string
}

export interface RoomStatus {
  inProgressApellaCase?: {
    id: string
    startTime: DateTime
    endTime?: DateTime
    status: {
      name: CaseStatusName
      since: string | null
    }
  }
  inProgressTurnover?: {
    currentLengthStatus: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
    id: string
    startTime: DateTime
    endTime: DateTime
    overallLengthStatus: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
    type: TurnoverType
  }
  name: RoomStatusName
  since: DateTime
}

export interface TimelineRoom extends RoomShape {
  blockTimes?: ApellaBlockTime[]
  cases: ApellaCase[]
  primeTime: { startTime: string; endTime: string } | null
  since: DateTime
  site: {
    id: string
    name: string
  }
  sortKey: string | null
  status: RoomStatus
  turnovers: Turnover[]
}

export type TimelineHourBoundsCalculationMethod =
  | 'normal'
  | 'dayBound'
  | 'fullTime'
  | 'fullTimeBounded'
  | 'sliding'
  | 'primeTime'

export interface TimelineDateTimeBounds {
  calculationMethod?: TimelineHourBoundsCalculationMethod
  maxDateTime: DateTime
  minDateTime: DateTime
}

export enum TimeRange {
  DayBound = 'dayBound',
  FullTime = 'fullTime',
  PrimeTime = 'primeTime',
}

export const TimeRangeOptions = [
  { label: '24h', value: TimeRange.FullTime },
  { label: 'Prime time', value: TimeRange.PrimeTime },
  { label: "Today's cases", value: TimeRange.DayBound },
]

export type ViewRoomMetric = keyof Pick<
  DailyMetrics,
  | 'firstCaseStarts'
  | 'stillOpen'
  | 'openPast15'
  | 'openPast17'
  | 'openPast19'
  | 'openToday'
>
