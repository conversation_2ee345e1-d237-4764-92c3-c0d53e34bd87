import { useMemo } from 'react'
import { generatePath, useLocation } from 'react-router'

import styled from '@emotion/styled'
import { Placement } from '@floating-ui/react-dom'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime } from 'luxon'

import {
  ButtonLink,
  Counter,
  Edit,
  InsertChart,
  mediaQueries,
  People,
} from '@apella/component-library'
import { TurnoverType } from 'src/__generated__/globalTypes'
import { PageHeader } from 'src/components/PageHeader'
import { SecondaryView } from 'src/components/types'
import { UserViewContext } from 'src/components/UserViews/useUserFilterViews'
import { useTimezone } from 'src/Contexts'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import { useScheduleContext } from 'src/pages/Schedule/ScheduleContextProvider'
import { useScheduleFilterContext } from 'src/pages/Schedule/ScheduleFilterContext'
import { TvModeToggleButton } from 'src/pages/Schedule/TvModeToggleButton'
import { LocationPath } from 'src/router/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { LiveStatus } from 'src/utils/status'
import { useCurrentMinute } from 'src/utils/useCurrentTime'

import { StatusButton } from '../Live/StatusHelper'
import { useTurnoverState } from '../Turnovers/useTurnoverState'
import { FiltersToggleButton } from './FiltersToggleButton'
import { MetricsToggleButton } from './MetricsToggleButton'
import { useScheduleFilterProviderUrls } from './ScheduleFilterProvider'
import { ShowClosedRoomsToggle } from './ShowClosedRoomsToggle'

export interface SchedulePageHeaderProps {
  selectedSubNavId?: 'general' | 'pre-op' | 'post-op' | 'staff' | 'turnovers'
  sorts?: { key: LiveStatus; count: number }[]
}

const ScheduleFullscreenToggle = ({
  placement = 'bottom-end',
}: {
  placement?: Placement
}) => {
  const { showPanel: disabled } = useScheduleContext()

  return <TvModeToggleButton disabled={disabled} placement={placement} />
}

const useSubNavOptions = () => {
  const { siteIds, roomIds, minTime, maxTime } = useScheduleFilterContext()

  const { allSearchParamsWithoutDate } = useScheduleFilterProviderUrls()
  const { permissions } = useCurrentUser()

  const { turnoverDashboardInfo, turnoverLoading } = useTurnoverState({
    minEndTime: minTime,
    maxStartTime: maxTime,
    siteIds,
    roomIds,
    skip: false,
  })

  const getNumOfLiveTurnovers = turnoverDashboardInfo
    .filter((t) => t.type === TurnoverType.LIVE)
    .flatMap((turnovers) => turnovers).length

  const displayNumOfTurnovers = useMemo(() => {
    return (
      <div
        // This width and height matches the counter sizing for small,
        // this is done intentionally to eliminate layout shift
        css={{
          width: 19.3,
          height: 17,
          display: 'flex',
        }}
      >
        {turnoverLoading ? (
          <></>
        ) : (
          <Counter
            size="sm"
            color={!!getNumOfLiveTurnovers ? 'yellow' : 'gray'}
            count={getNumOfLiveTurnovers}
          />
        )}
      </div>
    )
  }, [getNumOfLiveTurnovers, turnoverLoading])

  const subNavOptions = useMemo(() => {
    const tabs = []

    if (permissions?.dashboardScheduleEnabled) {
      tabs.push({
        id: 'general',
        display: 'OR',
        path: LocationPath.Schedule,
        to: {
          pathname: LocationPath.Schedule,
          search: allSearchParamsWithoutDate.toString(),
        },
      })
    }

    if (permissions?.dashboardTurnoversEnabled) {
      tabs.push({
        id: 'turnovers',
        display: 'Turnovers',
        path: LocationPath.TurnoversDashboard,
        icon: displayNumOfTurnovers,
        to: {
          pathname: LocationPath.TurnoversDashboard,
          search: allSearchParamsWithoutDate.toString(),
        },
      })
    }

    if (permissions?.dashboardScheduleEnabled) {
      tabs.push(
        {
          id: 'pre-op',
          display: 'PreOp',
          path: LocationPath.PreOp,
          to: {
            pathname: LocationPath.PreOp,
            search: allSearchParamsWithoutDate.toString(),
          },
        },
        {
          id: 'post-op',
          display: 'PostOp',
          path: LocationPath.PostOp,
          to: {
            pathname: LocationPath.PostOp,
            search: allSearchParamsWithoutDate.toString(),
          },
        }
      )
    }

    return tabs
  }, [
    permissions?.dashboardScheduleEnabled,
    permissions?.dashboardTurnoversEnabled,
    allSearchParamsWithoutDate,
    displayNumOfTurnovers,
  ])

  return subNavOptions
}

const ActionItemTitle = styled.span({
  display: 'none',
  [mediaQueries.lg]: {
    display: 'block',
  },
})

const StaffManagementNavItem = () => {
  return (
    <>
      <People size={'xs'} />
      <ActionItemTitle>Staff management</ActionItemTitle>
    </>
  )
}

const DailyInsights = () => {
  return (
    <>
      <InsertChart size={'xs'} />
      <ActionItemTitle>Daily Insights</ActionItemTitle>
    </>
  )
}

const EditSchedule = () => {
  return (
    <>
      <Edit size={'xs'} />
      <ActionItemTitle>Edit Schedule</ActionItemTitle>
    </>
  )
}

const useSecondaryRoutes = ({
  showSecondaryNav,
}: {
  showSecondaryNav?: boolean
}) => {
  const { minTime: minTimeFilter, maxTime } = useScheduleFilterContext()

  const { allSearchParams, allSearchParamsWithoutDate } =
    useScheduleFilterProviderUrls()

  const { showPanel: disabled } = useScheduleContext()

  const date = { minTime: minTimeFilter, maxTime }

  const { timezone } = useTimezone()
  const currentMinute = useCurrentMinute()
  const location = useLocation()

  const minTime = date?.minTime ?? DateTime.now().setZone(timezone).toISO()

  const showStaffMangement = useMemo(
    () =>
      DateTime.fromISO(minTime) < currentMinute.plus({ days: 7 }).endOf('day'),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [currentMinute.day, minTime]
  )

  const { permissions: uiPermissions } = useCurrentUser()
  const { showEditScheduleRoute } = useFlags<WebDashboardFeatureFlagSet>()

  const routes = [
    {
      children: <EditSchedule />,
      id: 'edit-schedule',
      to: {
        pathname: LocationPath.EditSchedule,
        search: allSearchParams.toString(),
      },
      isHidden:
        !(
          uiPermissions?.dashboardScheduleEditEnabled && showEditScheduleRoute
        ) || !showSecondaryNav,
      state: {
        background: location,
      },
    },
    {
      children: <DailyInsights />,
      id: 'daily-insights',
      to: {
        pathname: generatePath(`${LocationPath.DailyInsights}/:viewId`, {
          viewId: LocationPath.CasesDataView,
        }),
        search: allSearchParams.toString(),
      },
      isHidden: !showSecondaryNav,
      state: {
        background: location,
      },
    },
    {
      children: <StaffManagementNavItem />,
      id: 'staff-management',
      to: {
        pathname: LocationPath.StaffManagement,
        search: allSearchParamsWithoutDate.toString(),
      },
      isHidden:
        !showStaffMangement ||
        !uiPermissions?.dashboardStaffManagementEnabled ||
        disabled,
    },
  ].filter((r) => !r.isHidden)

  return routes
}

const SchedulePageHeader = ({
  selectedSubNavId,
  sorts,
}: SchedulePageHeaderProps) => {
  const eventsLogger = useAnalyticsEventLogger()
  const { showPanel: disabled } = useScheduleContext()
  const { sortKeys, onToggleSortKey } = useScheduleFilterContext()

  const subNavOptions = useSubNavOptions()

  const showSecondaryNav = !disabled && selectedSubNavId === 'general'

  const onClickSubNavOption = (option: SecondaryView) => {
    if (disabled) {
      return
    } else if (option.id === 'post-op') {
      eventsLogger(EVENTS.POSTOP_CLICK_TAB)
    } else if (option.id === 'pre-op') {
      eventsLogger(EVENTS.PREOP_CLICK_TAB)
    } else if (option.id === 'turnovers') {
      eventsLogger(EVENTS.TURNOVERS_DASHBOARD_CLICK_TAB)
    }
  }

  const hideMetricsToggle =
    selectedSubNavId === 'turnovers' || selectedSubNavId === 'staff'

  const showClosedRoomsToggle =
    selectedSubNavId === 'general' || selectedSubNavId === 'turnovers'

  return (
    <PageHeader
      selectedViewId={selectedSubNavId}
      onClick={onClickSubNavOption}
      disabled={disabled}
      views={subNavOptions}
      actions={
        <>
          <SecondaryScheduleRoutes showSecondaryNav={showSecondaryNav} />
          {sorts?.map(({ key, count }) => (
            <StatusButton
              key={key}
              status={key}
              count={count}
              isActive={sortKeys.includes(key)}
              handleClick={() => {
                onToggleSortKey(key)
              }}
            />
          ))}
          {!disabled && showClosedRoomsToggle && <ShowClosedRoomsToggle />}
          {!disabled && <FiltersToggleButton />}
          {!hideMetricsToggle && !disabled && <MetricsToggleButton />}
          <ScheduleFullscreenToggle />
        </>
      }
      title="Schedule"
      userViewContexts={
        !disabled
          ? [UserViewContext.Schedule, UserViewContext.StaffManagement]
          : undefined
      }
    />
  )
}

const SecondaryScheduleRoutes = ({
  showSecondaryNav,
}: {
  showSecondaryNav: boolean
}) => {
  const routes = useSecondaryRoutes({ showSecondaryNav })

  return routes.map(({ children, id, to, state }) => (
    <ButtonLink key={id} appearance={'link'} size={'sm'} to={to} state={state}>
      {children}
    </ButtonLink>
  ))
}

export default SchedulePageHeader
