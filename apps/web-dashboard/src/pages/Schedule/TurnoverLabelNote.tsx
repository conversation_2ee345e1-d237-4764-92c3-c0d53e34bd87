import {
  ChangeEvent,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react'

import { rem } from 'polished'

import {
  Error,
  P2,
  P3,
  remSpacing,
  theme,
  typography,
} from '@apella/component-library'
import { TurnoverLabel } from 'src/__generated__/globalTypes'
import { ToggleSelect } from 'src/components/ToggleSelect'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

import { Turnover } from '../types'

export interface LabelsNoteProps {
  labels: TurnoverLabel[] | null
  note: string | null
}

const isLabelListEqual = (arr1?: string[], arr2?: string[]) => {
  if (!arr1 && !arr2) {
    return true
  } else if (!arr1 || !arr2) {
    return false
  } else {
    return (
      arr1.length === arr2.length &&
      arr1.every((value) => arr2.includes(value)) &&
      arr2.every((value) => arr1.includes(value))
    )
  }
}

const isNoteEqual = (
  labelsNote: string | null,
  existingNote: string | null
): boolean => {
  const noteValue = labelsNote?.trim() || ''
  const existingNoteValue = existingNote?.trim() || ''
  return noteValue === existingNoteValue
}

export const TurnoverLabelNote = ({
  position,
  turnover,
  onClose,
  turnoverLabels,
  updateFunc,
}: {
  position?: { x: number; y: number }
  turnover: Turnover
  updateFunc: ({
    id,
    note,
    labelIds,
  }: {
    id: string
    note: string | null
    labelIds: string[] | null
  }) => void
  onClose: (mutationStarted: boolean) => void
  turnoverLabels: TurnoverLabel[]
}) => {
  const popupRef = useRef<HTMLDivElement | null>(null)
  const [labelsNote, setLabelsNote] = useState<LabelsNoteProps>({
    note: turnover.note,
    labels: turnover.labels,
  })
  const eventsLogger = useAnalyticsEventLogger()
  const [adjustedPosition, setAdjustedPosition] = useState<{
    x: number
    y: number
  }>({ x: position?.x || 0, y: position?.y || 0 })

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node)
      ) {
        // Compare the labels and notes with the current value of the selected turnover.
        // If the values differ, update the turnover using the mutation.
        // Otherwise, simply close the popup.
        let mutationStarted = false
        if (
          !isLabelListEqual(
            labelsNote.labels?.map((label) => label.id),
            turnover.labels?.map((label) => label.id)
          ) ||
          !isNoteEqual(labelsNote.note, turnover.note)
        ) {
          updateFunc({
            id: turnover.id,
            labelIds: labelsNote.labels?.map((label) => label.id) ?? null,
            note: labelsNote.note,
          })
          mutationStarted = true
          eventsLogger(EVENTS.SCHEDULE_PAGE_UPDATE_TURNOVER_DELAY_NOTE, {
            turnoverId: turnover.id,
            labels: labelsNote.labels,
            note: labelsNote.note,
          })
        }
        //return the mutation status to the parent component
        onClose(mutationStarted)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [onClose, updateFunc, popupRef, labelsNote, turnover, eventsLogger])

  useLayoutEffect(() => {
    if (popupRef.current) {
      const popupRect = popupRef.current.getBoundingClientRect()
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      let x = position?.x || 0
      let y = position?.y || 0

      // Adjust for horizontal overflow
      if (x + popupRect.width > viewportWidth) {
        x = viewportWidth - popupRect.width - 10
      }
      if (x < 0) x = 10

      // Adjust for vertical overflow
      if (y + popupRect.height > viewportHeight) {
        y = viewportHeight - popupRect.height - 10
      }
      if (y < 0) y = 10

      setAdjustedPosition({ x, y })
    }
  }, [position])

  const noteChangeHandler = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const trimmedNotes = e.target.value.trim()
    setLabelsNote((prev) => ({ ...prev, note: trimmedNotes }))
  }

  const toggleReason = useCallback(
    (labelId: string) => {
      const assocToDelete = labelsNote.labels?.find(
        (label) => label.id === labelId
      )

      if (assocToDelete) {
        setLabelsNote((prev) => ({
          ...prev,
          labels: prev.labels!.filter((label) => label.id !== labelId),
        }))
      } else {
        setLabelsNote((prev) => ({
          ...prev,
          labels: prev.labels
            ? [...prev.labels, { id: labelId } as TurnoverLabel]
            : [{ id: labelId } as TurnoverLabel],
        }))
      }
    },
    [labelsNote.labels]
  )

  const onPanelClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation()
  }, [])

  const labels = useMemo(
    () =>
      turnoverLabels.map((label) => ({
        ...label,
        onClick: () => toggleReason(label.id),
        isSelected:
          labelsNote.labels?.some(
            (existingLabel) => existingLabel.id === label.id
          ) ?? false,
      })),
    [labelsNote.labels, turnoverLabels, toggleReason]
  )

  return (
    <div
      ref={popupRef}
      css={{
        position: 'fixed',
        borderRadius: remSpacing.xsmall,
        zIndex: 1,
        top: `${adjustedPosition.y}px`,
        left: `${adjustedPosition.x}px`,
        border: `1px solid ${theme.palette.gray[20]}`,
        backgroundColor: theme.palette.background.primary,
        width: rem('293px'),
        padding: remSpacing.xsmall,
        display: 'grid',
      }}
      onClick={onPanelClick}
    >
      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
        }}
      >
        {labels.map((label) => (
          <ToggleSelect
            key={label.id}
            onClick={label.onClick}
            value={label.isSelected}
          >
            <P2>{label.name}</P2>
          </ToggleSelect>
        ))}
        <hr
          css={{
            width: `calc(100% - 20px)`,
            margin: '0 auto',
            border: 0,
            height: '1px',
            backgroundColor: theme.palette.gray[30],
            // Give the hr a bit of padding for toggle focus states aren't cut off
            marginTop: '1px',
          }}
        />
      </div>
      <div
        css={{
          display: 'grid',
          padding: `0 ${remSpacing.small}`,
          gap: remSpacing.medium,
        }}
      >
        <label
          css={{
            paddingTop: remSpacing.small,
            ...typography.caps2Bold,
            color: theme.palette.gray[50],
          }}
          htmlFor="note"
        >
          Note
        </label>
        <textarea
          css={{
            color: theme.palette.gray[70],
            padding: remSpacing.xsmall,
            borderRadius: remSpacing.xsmall,
            border: `1px solid ${theme.palette.gray[30]}`,
            width: '100%',
            minHeight: '80px',
            resize: 'vertical',
            overflowY: 'auto',
            textAlign: 'start',
            verticalAlign: 'top',
          }}
          name="note"
          defaultValue={labelsNote.note || ''}
          onChange={noteChangeHandler}
        />
        <div
          css={{
            display: 'grid',
            gridTemplateColumns: 'max-content auto',
            gap: remSpacing.xsmall,
          }}
        >
          <Error color={theme.palette.gray[50]} size="sm" />
          <P3>
            Turnover information may take several minutes to propagate to
            Insights
          </P3>
        </div>
      </div>
    </div>
  )
}
