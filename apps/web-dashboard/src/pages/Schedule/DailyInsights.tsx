import { useEffect, useMemo } from 'react'
import { Outlet, useMatch, useSearchParams } from 'react-router'

import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime } from 'luxon'

import {
  Blade,
  FlexContainer,
  InsertChart,
  mediaQueries,
  NavigationTab,
  NavigationTabTitle,
  NavigationTabWrapper,
  remSpacing,
} from '@apella/component-library'
import { SecondaryView } from 'src/components/types'
import { useTimezone } from 'src/Contexts'
import { useQueryFilterState } from 'src/modules/daily-metrics/hooks/useQueryFilterState'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { LocationPath } from 'src/router/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { dateTimeToUrlFriendlyDate } from 'src/utils/urlFriendlyDate'
import { useBladeState } from 'src/utils/useBladeState'

import { useScheduleFilterContext } from './ScheduleFilterContext'

export const CASES_TAB = 'cases'
export const BLOCKS_TAB = 'blocks'

const DailyInsightsHeader = ({ onClose }: { onClose: () => void }) => {
  return (
    <Blade.Header>
      <Blade.Title title="Daily insights" icon={<InsertChart size={'sm'} />} />
      <Blade.CloseButton onClose={onClose} />
    </Blade.Header>
  )
}

const useSubNavOptions = (
  blockTimeOnSchedulePageEnabled: boolean,
  currSearchParams: URLSearchParams
) => {
  const { timezone } = useTimezone()
  const { siteIds } = useScheduleFilterContext()
  const params = new URLSearchParams()
  const date = currSearchParams.has('date')
    ? currSearchParams.get('date')
    : `"${dateTimeToUrlFriendlyDate(DateTime.now().setZone(timezone))}"`
  params.set('date', date || '')
  params.set('sites', JSON.stringify(siteIds))
  const searchString = params.toString()

  const subNavOptions = useMemo(() => {
    const options = [
      {
        id: CASES_TAB,
        display: 'Cases',
        path: LocationPath.CasesDataView,
        to: {
          pathname: LocationPath.CasesDataView,
          search: searchString,
        },
      },
    ]
    if (blockTimeOnSchedulePageEnabled) {
      options.push({
        id: BLOCKS_TAB,
        display: 'Blocks',
        path: LocationPath.BlocksDataView,
        to: {
          pathname: LocationPath.BlocksDataView,
          search: searchString,
        },
      })
    }

    return options
  }, [blockTimeOnSchedulePageEnabled, searchString])

  return subNavOptions
}

export const DailyInsights = () => {
  const { blockTimeOnSchedulePageEnabled } =
    useFlags<WebDashboardFeatureFlagSet>()
  const filterState = useQueryFilterState()
  const eventsLogger = useAnalyticsEventLogger()
  const match = useMatch('/schedule/daily-insights/:viewId')
  const [searchParams] = useSearchParams()
  const viewId = match?.params?.viewId
  const subNavOptions = useSubNavOptions(
    blockTimeOnSchedulePageEnabled,
    searchParams
  )
  const { isBladeOpen, onBladeClose } = useBladeState({
    hardRefreshFallbackPath:
      LocationPath.Schedule + '?' + searchParams.toString(),
  })

  useEffect(() => {
    eventsLogger(EVENTS.LOAD_DAILY_INSIGHTS_PAGE, filterState)
    // Only run this effect once
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const onClickSubNavOption = (option: SecondaryView) => {
    if (option.id === CASES_TAB) {
      eventsLogger(EVENTS.CLICK_CASES_DAILY_INSIGHTS_TAB)
    } else if (option.id === BLOCKS_TAB) {
      eventsLogger(EVENTS.CLICK_BLOCKS_DAILY_INSIGHTS_TAB)
    }
  }

  return (
    <Blade isOpen={isBladeOpen} onClose={onBladeClose} side="right" size="xs">
      <FlexContainer
        css={{ height: 'inherit', overflowY: 'auto' }}
        direction="column"
      >
        <DailyInsightsHeader onClose={onBladeClose} />
        <FlexContainer
          direction="column"
          css={{
            paddingLeft: remSpacing.medium,
          }}
        >
          <div
            css={{
              display: 'grid',
              [mediaQueries.lg]: {
                paddingTop: remSpacing.xsmall,
                gridTemplateAreas: `"views"`,
                gridTemplateColumns: 'max-content auto',
              },
            }}
          >
            <NavigationTabWrapper>
              {subNavOptions.map((subNavOption) => {
                const isSelected = subNavOption.id === viewId
                return (
                  <NavigationTab isSelected={isSelected} key={subNavOption.id}>
                    <NavigationTabTitle
                      isSelected={isSelected}
                      onClick={() => onClickSubNavOption(subNavOption)}
                      to={subNavOption.to}
                      replace
                    >
                      {subNavOption.display}
                    </NavigationTabTitle>
                  </NavigationTab>
                )
              })}
            </NavigationTabWrapper>
          </div>
          <Outlet />
        </FlexContainer>
      </FlexContainer>
    </Blade>
  )
}
