import {
  Dispatch,
  Fragment,
  memo,
  <PERSON><PERSON><PERSON>,
  MouseEventH<PERSON>ler,
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { ScrollSync, ScrollSyncPane } from 'react-scroll-sync'

import { useTheme } from '@emotion/react'

import { useQuery } from '@apollo/client'
import styled from '@emotion/styled'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime, Duration } from 'luxon'

import {
  ComponentTheme,
  flexboxgrid,
  FlexContainer,
  FlexItem,
  formatDuration,
  MarkerShape,
  mediaQueries,
  P3,
  P4,
  remSpacing,
  shape,
  Tooltip,
  TooltipProps,
  Union,
  VideoProgress,
} from '@apella/component-library'
import { usePosition, useSize } from '@apella/hooks'
import { CaseType, RoomStatusName } from 'src/__generated__/globalTypes'
import LoadingOverlay from 'src/components/LoadingOverlay'
import OccupancySlider, { GRAPH_HEIGHT } from 'src/components/OccupancySlider'
import HourLabel from 'src/components/Timeline/HourLabel'
import MinuteMarker from 'src/components/Timeline/MinuteMarker'
import {
  ProgressLine,
  ProgressPill,
} from 'src/components/Timeline/ProgressLine'
import {
  TimelineEventMarker,
  TimelineType,
  UnifiedTimelineEventMarkers,
} from 'src/components/UnifiedTimelineEventMarkers'
import { useTimezone } from 'src/Contexts'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import {
  RoomEventsQuery,
  RoomEventsQueryVariables,
  RoomObservationQuery,
  RoomObservationQueryVariables,
} from 'src/pages/__generated__'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { formatStatus, useStatusStyles } from 'src/utils/status'
import {
  calculatePillPositionAndSizeHelper,
  calculateTimeMarkerPercentHelper,
  calculateTimeMarkerPositionHelper,
  calculateTimeMarkers,
  PillPositionAndSize,
} from 'src/utils/timelineHelpers'
import { useCurrentMinute, useIsToday } from 'src/utils/useCurrentTime'
import { usePolling } from 'src/utils/usePolling'

import { LIVE_POLL_INTERVAL_MS } from '../Live/consts'
import { OBSERVATION_TYPE, OBSERVATIONS_TO_QUERY } from '../Live/livePhases'
import { ROOM_EVENTS_QUERY, ROOM_OBSERVATION_QUERY } from '../queries'
import {
  ApellaBlock,
  ApellaBlockTime,
  ApellaCase,
  Turnover,
  Uncategorized,
  Unlabeled,
} from '../types'
import { EditMode } from './EditSchedule/types'
import { RoomScheduleName } from './RoomScheduleName'
import { useScheduleContext } from './ScheduleContextProvider'
import { useScheduleFilterContext } from './ScheduleFilterContext'
import {
  BlockTimePillContainer,
  BlockTooltipBody,
} from './TimelinePill/BlockTimePill'
import { CombinedTimelinePill } from './TimelinePill/CombinedTimelinePill'
import { TurnoverPill } from './TimelinePill/TurnoverPill'
import { TimeRangeSelector } from './TimeRangeSelector'
import { TurnoverLabelNote } from './TurnoverLabelNote'
import {
  ApellaEvent,
  IdMapping,
  MouseHoverState,
  MouseMovementHandler,
  ScheduleViewOnClickHandler,
  TimelineDateTimeBounds,
  TimelineRoom,
  TimeRange,
} from './types'
import { TimelineState } from './useTimelineState'
import { useTurnoverUpdate } from './useTurnoverLabelNote'

interface LeftColumnProps {
  theme?: ComponentTheme
}

const HOUR_LABEL_WIDTH_ESTIMATE_PX = 50
const EVENT_HEIGHT = 40
const Y_AXIS_LEFT_PADDING = 8
const ROW_HEIGHT_EXTRA_LINES = 89
const ROW_HEIGHT_OLD = 81

const LEFT_COL_WIDTH = 115

const TableSection = styled.div({
  display: 'flex',
  overflow: 'auto',
  '&::-webkit-scrollbar': { display: 'none' },
})

const LeftColumn = styled.div<LeftColumnProps>(({ theme }) => ({
  background: theme.palette.background.primary,
  position: 'sticky',
  left: 0,
  zIndex: 1,
  boxShadow:
    '0px 6px 10px 0px rgba(0, 0, 0, 0.08), 0px 3px 5px 0px rgba(0, 0, 0, 0.10)',
  flex: `0 0 ${LEFT_COL_WIDTH}px`,
  [mediaQueries.lg]: {
    boxShadow: 'none',
  },
}))

const ROOM_PADDING = '20px'

const useRowHeight = () => {
  const { scheduleShowAnesthesiaOnTimeline } =
    useFlags<WebDashboardFeatureFlagSet>()

  return scheduleShowAnesthesiaOnTimeline
    ? ROW_HEIGHT_EXTRA_LINES
    : ROW_HEIGHT_OLD
}

const RightColumn = styled.div(({ theme }: { theme: ComponentTheme }) => ({
  background: theme.palette.background.primary,
  padding: `0 ${ROOM_PADDING}`,
  flexGrow: 1,
  minWidth: flexboxgrid.breakpoints.sm,
}))

export interface TimelineProps
  extends Omit<TimelineState, 'minTime' | 'maxTime'>,
    TimelineDateTimeBounds {
  activeTurnoverId?: string
  blocks?: ApellaBlock[]
  enableRightClick?: boolean
  eventNames?: string[]
  onClick?: ScheduleViewOnClickHandler
  onTimeRangeChange?: (value: TimeRange) => void
  progress?: VideoProgress
  roomAction?: (roomId: string) => ReactNode
  showDetails?: boolean
  showLabels?: boolean
  showScheduled?: boolean
  timeRange?: TimeRange
  tooltipPlacement?: TooltipProps['placement']
}
export interface RecipientMapProp {
  [eventId: string]: string[]
}

const TimelineView = ({
  blocks,
  rooms,
  isLoading,
  tooltipPlacement,
  minDateTime,
  maxDateTime,
  onClick,
  progress,
  showLabels = false,
  showDetails = false,
  roomAction,
  showScheduled = true,
  eventNames,
  activeTurnoverId,
  onTimeRangeChange,
  timeRange,
  enableRightClick = false,
}: TimelineProps): React.JSX.Element => {
  const theme: ComponentTheme = useTheme()
  const { timezone } = useTimezone()
  const currentTime = useCurrentMinute() // only update every minute
  const timelineMinDateTime = minDateTime.setZone(timezone)
  const timelineMaxDateTime = maxDateTime.setZone(timezone)
  const [scrollTimelinePosition, setScrolledTimelinePosition] = useState(0)
  const onSync = useCallback(
    (el: HTMLElement) => setScrolledTimelinePosition(el.scrollLeft),
    []
  )
  const idMappings = useMemo(() => {
    const mapping: IdMapping = {}
    // 2-way mapping, we can only get information from phase { case { id } }
    rooms.forEach((r) => {
      r.cases.forEach((apellaCase) => {
        if (apellaCase.case?.id) {
          mapping[apellaCase.case?.id] = (
            mapping[apellaCase.case?.id] ?? []
          ).concat([apellaCase.id])
          mapping[apellaCase.id] = (mapping[apellaCase.id] ?? []).concat([
            apellaCase.case.id,
          ])
        }
      })
    })
    return mapping
  }, [rooms])
  const [headerRef, setHeaderRef] = useState<HTMLDivElement | null>(null)

  const timelineSize = useSize(headerRef)
  const timelinePosition = usePosition(headerRef)

  // Mouse events to set the position and visibility of the hover line
  const [hoverLineTime, setHoverLineTime] = useState<DateTime>()
  const onMouseLeave = useCallback(() => {
    setHoverLineTime(undefined)
  }, [])

  const timelineDurationMillis = useMemo(
    () => timelineMaxDateTime.diff(timelineMinDateTime).toMillis(),
    [timelineMaxDateTime, timelineMinDateTime]
  )

  const onMouseMove: MouseEventHandler = useCallback(
    (e) => {
      if (!timelinePosition || isLoading) return

      const left = e.clientX - timelinePosition.x + scrollTimelinePosition

      // Over the room labels ("OR 1", "OR 2") or missing bounds, so don't show
      if (left < 0 || !timelineSize?.width || left > timelineSize.width) {
        setHoverLineTime(undefined)
      } else {
        // left is pixels from the left of the start of the timeline, so the hover line should be that plus some padding
        const hoverSeconds = Math.round(
          (left / timelineSize.width) * (timelineDurationMillis / 1000)
        )
        const newHoverLineDuration = Duration.fromMillis(hoverSeconds * 1000)

        const newHoverLineTime = timelineMinDateTime.plus(newHoverLineDuration)
        if (!hoverLineTime || !newHoverLineTime.equals(hoverLineTime)) {
          setHoverLineTime(newHoverLineTime)
        }
      }
    },
    [
      timelinePosition,
      timelineSize?.width,
      timelineDurationMillis,
      timelineMinDateTime,
      hoverLineTime,
      isLoading,
      scrollTimelinePosition,
      setHoverLineTime,
    ]
  )

  const hoveringInThePast = useMemo(() => {
    if (!hoverLineTime) return false

    return (
      hoverLineTime <
      DateTime.now()
        .setZone(timezone)
        .minus(
          Duration.fromObject({
            minutes: 5,
          })
        )
    )
  }, [hoverLineTime, timezone])

  const onClickRoom = useCallback(
    (roomId: string, apellaCase?: ApellaCase, turnover?: Turnover) => {
      if (onClick && hoverLineTime) {
        onClick(roomId, hoverLineTime, apellaCase, turnover)
      }
    },
    [hoverLineTime, onClick]
  )

  const calculatePillPositionAndSize = useCallback(
    (
      startTime: DateTime,
      endTime?: DateTime,
      minWidth?: number,
      liveTime?: DateTime
    ): PillPositionAndSize => {
      if (!timelineSize?.width)
        return {
          left: '0',
          widthPct: '0%',
          widthPx: 0,
          forecastedWidthPx: 0,
          doesOverflowMinTime: false,
          doesOverflowMaxTime: false,
          gradientWidthPx: 0,
        }
      return calculatePillPositionAndSizeHelper({
        startTime,
        timelineWidth: timelineSize.width,
        minHour: timelineMinDateTime,
        maxHour: timelineMaxDateTime,
        minWidth,
        endTime,
        liveTime,
      })
    },
    [timelineSize?.width, timelineMinDateTime, timelineMaxDateTime]
  )

  const showNowLine = useMemo(
    () =>
      currentTime > timelineMinDateTime && currentTime < timelineMaxDateTime,
    [currentTime, timelineMinDateTime, timelineMaxDateTime]
  )

  const [hoveredBlockTimes, setHoveredBlockTimes] = useState<ApellaBlockTime[]>(
    []
  )
  const [hoveredCaseIds, setHoveredCaseIds] = useState<string[]>([])

  const eventsLogger = useAnalyticsEventLogger()

  if (isLoading) {
    return <LoadingOverlay />
  }

  return (
    <ScrollSync onSync={onSync} proportional={false}>
      <div
        css={{
          overscrollBehavior: 'contain',
          userSelect: 'none',
          // to create a stacking context for the sticky headers
          position: 'relative',
          zIndex: 0,
        }}
        onMouseMove={onMouseMove}
        onMouseLeave={onMouseLeave}
      >
        <ScrollSyncPane>
          <TableSection
            css={{
              position: 'sticky',
              top: 0,
              overflow: 'hidden',
              // above the main timeline and the room names
              zIndex: 2,
            }}
          >
            {showLabels && onTimeRangeChange && timeRange && (
              <LeftColumn
                css={{
                  borderBottom: `1px solid ${theme.palette.gray[20]}`,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <TimeRangeSelector
                  onTimeRangeChange={onTimeRangeChange}
                  timeRange={timeRange}
                />
              </LeftColumn>
            )}
            <RightColumn
              css={{
                borderBottom: `1px solid ${theme.palette.gray[20]}`,
              }}
            >
              <HourHeader
                setHeaderRef={setHeaderRef}
                minDateTime={timelineMinDateTime}
                maxDateTime={timelineMaxDateTime}
              >
                {showNowLine && (
                  <ProgressPill
                    time={currentTime.setZone(timezone)}
                    minDateTime={timelineMinDateTime}
                    maxDateTime={timelineMaxDateTime}
                    color={theme.palette.blue[50]}
                  />
                )}
                {!!progress && (
                  <ProgressPill
                    time={progress.playerCurrentTime}
                    minDateTime={minDateTime}
                    maxDateTime={maxDateTime}
                    color={theme.palette.gray[50]}
                  />
                )}
                {!!hoverLineTime && (
                  <ProgressPill
                    time={hoverLineTime}
                    minDateTime={minDateTime}
                    maxDateTime={maxDateTime}
                    color={theme.palette.gray[50]}
                  />
                )}
              </HourHeader>
            </RightColumn>
          </TableSection>
        </ScrollSyncPane>
        <ScrollSyncPane>
          <TableSection>
            {showLabels && (
              <LeftColumn
                css={{
                  overflow: 'hidden',
                  top: 0,
                }}
              >
                {rooms.map((room) => (
                  <RoomName
                    key={`room-name-${room.id}`}
                    room={room}
                    roomAction={roomAction}
                    blocks={blocks ?? []}
                    setHoveredBlockTimes={setHoveredBlockTimes}
                    setHoveredCaseIds={setHoveredCaseIds}
                    eventsLogger={eventsLogger}
                    minDateTime={minDateTime}
                  />
                ))}
              </LeftColumn>
            )}
            <RightColumn>
              <div css={{ position: 'relative' }}>
                {rooms.map((room, idx) => (
                  <Fragment key={`room-cases-wrapper-${room.id}`}>
                    <RoomCaseTimeline
                      key={`room-cases-${room.id}`}
                      room={room}
                      minTime={timelineMinDateTime.toISO()}
                      maxTime={timelineMaxDateTime.toISO()}
                      idMappings={idMappings}
                      showScheduled={showScheduled}
                      onClick={onClickRoom}
                      hoveringInThePast={hoveringInThePast}
                      calculatePillPositionAndSize={
                        calculatePillPositionAndSize
                      }
                      tooltipPlacement={
                        tooltipPlacement ??
                        (idx > 0 && idx > rooms.length / 2 ? 'top' : 'bottom')
                      }
                      hoveredBlockTimes={hoveredBlockTimes}
                      hoveredCaseIds={hoveredCaseIds}
                      activeTurnoverId={activeTurnoverId}
                      enableRightClick={enableRightClick}
                      eventsLogger={eventsLogger}
                    />
                    {showDetails && (
                      <RoomDetails
                        key={room.id}
                        room={room}
                        minTime={timelineMinDateTime.toISO()}
                        maxTime={timelineMaxDateTime.toISO()}
                        onClick={onClickRoom}
                        hoveringInThePast={hoveringInThePast}
                        eventNames={eventNames}
                        apellaCases={room.cases}
                      />
                    )}
                  </Fragment>
                ))}
                {showNowLine && (
                  <ProgressLine
                    time={currentTime.setZone(timezone)}
                    minDateTime={timelineMinDateTime}
                    maxDateTime={timelineMaxDateTime}
                    color={theme.palette.blue[50]}
                  />
                )}
                {!!progress && (
                  <ProgressLine
                    time={progress.playerCurrentTime}
                    minDateTime={minDateTime}
                    maxDateTime={maxDateTime}
                    color={theme.palette.gray[50]}
                  />
                )}
                {!!hoverLineTime && (
                  <ProgressLine
                    time={hoverLineTime}
                    minDateTime={minDateTime}
                    maxDateTime={maxDateTime}
                    color={theme.palette.gray[50]}
                  />
                )}
              </div>
            </RightColumn>
          </TableSection>
        </ScrollSyncPane>
      </div>
    </ScrollSync>
  )
}
export default TimelineView

const HourHeader = memo(function HourHeader({
  setHeaderRef,
  minDateTime,
  maxDateTime,
  children,
}: TimelineDateTimeBounds & {
  setHeaderRef: Dispatch<SetStateAction<HTMLDivElement | null>>
  children?: ReactNode
}) {
  const [internalHeaderRef, setInternalHeaderRef] =
    useState<HTMLDivElement | null>(null)
  const timelineSize = useSize(internalHeaderRef)
  useEffect(() => {
    setHeaderRef(internalHeaderRef)
  }, [internalHeaderRef, setHeaderRef])

  // figure out how many hour ticks we can show in the timeline
  const maxHourLabels = useMemo(() => {
    return timelineSize?.width
      ? Math.floor(timelineSize?.width / HOUR_LABEL_WIDTH_ESTIMATE_PX)
      : 2
  }, [timelineSize?.width])

  const timeMarkers = useMemo(() => {
    return calculateTimeMarkers(minDateTime, maxDateTime, maxHourLabels).map(
      (marker) => ({
        ...marker,
        shouldDisplayLabel: marker.shouldDisplayLabel,
      })
    )
  }, [minDateTime, maxDateTime, maxHourLabels])

  const calculateTimeMarkerPosition = useCallback(
    (time: DateTime): string | number => {
      if (!timelineSize?.width) return 0
      return calculateTimeMarkerPositionHelper(time, minDateTime, maxDateTime)
    },
    [timelineSize?.width, minDateTime, maxDateTime]
  )

  return (
    <div
      css={{
        height: 50,
        paddingTop: 8,
        position: 'relative',
      }}
      ref={setInternalHeaderRef}
    >
      {timeMarkers.map((h) => {
        const left = calculateTimeMarkerPosition(h.time)
        return <TimeMarker key={h.time.toISO()} marker={h} left={left} />
      })}
      {children}
    </div>
  )
})

const TimeMarker = memo(function TimeMarker({
  marker,
  left,
}: {
  marker: { time: DateTime; shouldDisplayLabel: boolean }
  left: string | number
}) {
  return (
    <>
      {marker.shouldDisplayLabel && <HourLabel dt={marker.time} left={left} />}
      <MinuteMarker dt={marker.time} left={left} />
    </>
  )
})

const RoomName = memo(function RoomName({
  room,
  roomAction,
  blocks,
  setHoveredCaseIds = () => undefined,
  setHoveredBlockTimes = () => undefined,
  eventsLogger,
  minDateTime,
}: {
  room: TimelineRoom
  roomAction?: (roomId: string) => ReactNode
  blocks: ApellaBlock[]
  setHoveredCaseIds?: Dispatch<SetStateAction<string[]>>
  setHoveredBlockTimes?: Dispatch<SetStateAction<ApellaBlockTime[]>>
  eventsLogger: (e: EVENTS, data?: Record<string, any>) => void
  minDateTime: DateTime
}) {
  const theme = useTheme()
  const { timezone } = useTimezone()
  const timelineMinDateTime = minDateTime.setZone(timezone)
  const { blockTimeOnSchedulePageEnabled } =
    useFlags<WebDashboardFeatureFlagSet>()
  const rowHeight = useRowHeight()

  const {
    name: roomNameSchedule,
    site: { name: siteName },
  } = room

  const blockTooltipBodyProps = {
    style: {
      background: theme.palette.background.primary,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: shape.borderRadius.xsmall,
      display: 'flex',
      padding: remSpacing.xsmall,
    },
  }

  const blocksAtThisRoom = useMemo(() => {
    return blocks.filter((block) =>
      block.blockTimes.some((bt) => bt.roomId === room.id)
    )
  }, [blocks, room.id])

  const blockIdsAtThisRoom = useMemo(
    () => blocksAtThisRoom.map((block) => block.id),
    [blocksAtThisRoom]
  )

  const blockTimesAtThisRoom: ApellaBlockTime[] = useMemo(
    () =>
      blocks.flatMap((block) =>
        block.blockTimes.filter((blockTime) =>
          blockIdsAtThisRoom.includes(blockTime.blockId)
        )
      ),
    [blockIdsAtThisRoom, blocks]
  )

  const blockAtThisRoomCaseIds = useMemo(
    () => blocksAtThisRoom.flatMap((block) => block.caseIds),
    [blocksAtThisRoom]
  )
  const onBlockTooltipMouseEnter = useCallback(() => {
    setHoveredCaseIds(blockAtThisRoomCaseIds)
    setHoveredBlockTimes(blockTimesAtThisRoom)
    eventsLogger(EVENTS.SCHEDULE_PAGE_ENTER_BLOCK_TOOLTIP, {
      roomId: room.id,
      roomName: room.name,
      blockIdsAtThisRoom,
      selectedDate: timelineMinDateTime.toISODate(),
    })
  }, [
    room,
    blockIdsAtThisRoom,
    timelineMinDateTime,
    blockTimesAtThisRoom,
    blockAtThisRoomCaseIds,
    setHoveredBlockTimes,
    setHoveredCaseIds,
    eventsLogger,
  ])
  const onBlockTooltipMouseLeave = useCallback(() => {
    setHoveredCaseIds([])
    setHoveredBlockTimes([])
  }, [setHoveredCaseIds, setHoveredBlockTimes])

  const statusStyles = useStatusStyles()
  let roomStatusColor = undefined
  let roomStatusName = undefined
  let timeElapsed = undefined
  let roomStatusTextColor = undefined
  const { minTime } = useScheduleFilterContext()
  const isToday = useIsToday(DateTime.fromISO(minTime))

  const { assignStaffPlanningEnabled } = useFlags<WebDashboardFeatureFlagSet>()
  const getSpacingValue = (
    enabled: boolean,
    enabledValue: string | number,
    disabledValue: string | number
  ) => (enabled ? enabledValue : disabledValue)

  const getMarginSize = getSpacingValue(
    assignStaffPlanningEnabled,
    remSpacing.xsmall,
    0
  )
  const getGapSize = getSpacingValue(
    assignStaffPlanningEnabled,
    0,
    remSpacing.xsmall
  )
  const getPaddingSize = getSpacingValue(
    assignStaffPlanningEnabled,
    0,
    Y_AXIS_LEFT_PADDING
  )

  if (room.status.name !== RoomStatusName.IN_CASE) {
    timeElapsed = DateTime.now().diff(room.status.since)
    roomStatusName = room.status.name
    const statusStyle = statusStyles(roomStatusName)
    roomStatusTextColor = statusStyle.textColor
    roomStatusColor = statusStyle.backgroundColor
  }

  const showDuration =
    room.status?.name !== RoomStatusName.CLOSED && !!timeElapsed
  return (
    <div
      css={{
        height: rowHeight,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        whiteSpace: 'nowrap',
        background: theme.palette.background.primary,
        borderBottom: `1px solid ${theme.palette.gray[20]}`,
      }}
    >
      <div
        css={{
          paddingLeft: getPaddingSize,
        }}
      >
        {blockTimeOnSchedulePageEnabled ? (
          <div
            css={{
              display: 'flex',
              flexDirection: 'row',
              gap: getGapSize,
              alignItems: 'flex-end',
            }}
          >
            {assignStaffPlanningEnabled ? (
              <RoomScheduleName
                caseLength={room.cases.length}
                roomId={room.id}
                roomNameSchedule={roomNameSchedule}
              />
            ) : (
              <P3
                css={{
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                }}
              >
                {roomNameSchedule}
              </P3>
            )}

            {blocksAtThisRoom.length > 0 && (
              <Tooltip
                body={
                  <BlockTooltipBody
                    blocks={blocksAtThisRoom}
                    roomId={room.id}
                  />
                }
                middleware={[
                  Tooltip.middleware.autoPlacement({
                    allowedPlacements: ['bottom', 'top'],
                  }),
                  Tooltip.middleware.offset({
                    mainAxis: 24,
                  }),
                ]}
                isHoverable={true}
                isTouchEnabled={true}
                strategy={'fixed'}
                {...blockTooltipBodyProps}
              >
                <div
                  css={{
                    cursor: 'pointer',
                    color: theme.palette.gray[50],
                    ':hover': {
                      color: theme.palette.background.alternate,
                    },
                  }}
                  onMouseEnter={onBlockTooltipMouseEnter}
                  onMouseLeave={onBlockTooltipMouseLeave}
                >
                  <Union size="xs" />
                </div>
              </Tooltip>
            )}
          </div>
        ) : (
          <P3>{roomNameSchedule}</P3>
        )}

        <P4
          css={{
            color: theme.palette.text.tertiary,
            paddingLeft: assignStaffPlanningEnabled
              ? `${Y_AXIS_LEFT_PADDING}px`
              : 0,
          }}
        >
          {siteName}
        </P4>
        {isToday && (
          <P4
            css={{
              color: roomStatusTextColor,
              backgroundColor: roomStatusColor,
              borderRadius: shape.borderRadius.xxsmall,
              padding: `0 ${remSpacing.xxsmall}`,
              width: 'fit-content',
              fontWeight: 'bold',
              marginLeft: getMarginSize,
            }}
          >
            {roomStatusName
              ? formatStatus(roomStatusName).toLocaleUpperCase()
              : undefined}
            <span css={{ fontWeight: 'normal' }}>
              {showDuration &&
                ` ${formatDuration(timeElapsed).toLocaleUpperCase()}`}
            </span>
          </P4>
        )}
      </div>
      {roomAction && <div>{roomAction(room.id)}</div>}
    </div>
  )
})

const RoomCaseTimeline = memo(function RoomCaseTimeline({
  minTime,
  maxTime,
  idMappings,
  onClick,
  hoveringInThePast,
  calculatePillPositionAndSize,
  tooltipPlacement,
  showScheduled,
  room,
  hoveredBlockTimes = [],
  hoveredCaseIds = [],
  activeTurnoverId,
  enableRightClick = false,
  eventsLogger,
}: {
  room: TimelineRoom
  minTime: string
  maxTime: string
  idMappings: IdMapping
  onClick: (
    roomId: string,
    apellaCase?: ApellaCase,
    turnover?: Turnover
  ) => void
  hoveringInThePast: boolean
  calculatePillPositionAndSize: (
    startTime: DateTime,
    endTime?: DateTime,
    minWidth?: number
  ) => PillPositionAndSize
  tooltipPlacement?: TooltipProps['placement']
  showScheduled: boolean
  hoveredBlockTimes?: ApellaBlockTime[]
  hoveredCaseIds?: string[]
  activeTurnoverId?: string
  enableRightClick?: boolean
  eventsLogger: (e: EVENTS, data?: Record<string, any>) => void
}) {
  const theme: ComponentTheme = useTheme()
  const { blockTimeOnSchedulePageEnabled } =
    useFlags<WebDashboardFeatureFlagSet>()
  const [hoverState, setHoverState] = useState<MouseHoverState>({})
  const rowHeight = useRowHeight()

  const {
    editScheduleState: { activeEdit: activeEdit, mode, highlightedEdit },
    isEditingSchedule,
    turnoverLabels,
  } = useScheduleContext()
  const { id, cases } = room

  const [selectedTurnover, setSelectedTurnover] = useState<
    Turnover | undefined
  >(undefined)

  const minTimeDT = DateTime.fromISO(minTime)
  const maxTimeDT = DateTime.fromISO(maxTime)

  const onTimelineClick = useCallback(
    (mouseEvent: MouseEvent, apellaCase?: ApellaCase, turnover?: Turnover) => {
      // Stop propagation of click events. This is needed to avoid onClick being called for
      // both the timeline and the pill when the user clicks on a pill.
      mouseEvent.stopPropagation()
      onClick(id, apellaCase, turnover)
    },
    [id, onClick]
  )
  const highlightedEdits = useMemo(() => {
    const pillsToHighlight: MouseHoverState = {}
    if (isEditingSchedule) {
      if (mode !== EditMode.PickCase && !!activeEdit) {
        const caseDomainId = activeEdit.scheduledCase.id
        idMappings[caseDomainId]?.forEach(
          (rangeId) => (pillsToHighlight[rangeId] = true)
        )
        pillsToHighlight[caseDomainId] = true
        if (!!activeEdit.newPhase) {
          const phaseCaseDomainId = activeEdit.newPhase.id
          idMappings[phaseCaseDomainId]?.forEach(
            (rangeId) => (pillsToHighlight[rangeId] = true)
          )
          pillsToHighlight[phaseCaseDomainId] = true
        }
      }

      if (!!highlightedEdit) {
        const caseDomainId = highlightedEdit.caseId
        idMappings[caseDomainId]?.forEach(
          (rangeId) => (pillsToHighlight[rangeId] = true)
        )
        pillsToHighlight[caseDomainId] = true
        if (!!highlightedEdit.phaseCaseId) {
          const phaseCaseDomainId = highlightedEdit.phaseCaseId
          idMappings[phaseCaseDomainId]?.forEach(
            (rangeId) => (pillsToHighlight[rangeId] = true)
          )
          pillsToHighlight[phaseCaseDomainId] = true
        }
      }
    }
    return pillsToHighlight
  }, [mode, activeEdit, highlightedEdit, idMappings, isEditingSchedule])

  // Helper that set hover state of hovered element and
  // all its linked elements at the same time
  const setState = (value: boolean) => (domainId: string) => {
    setHoverState((prevHoverState) => ({
      ...prevHoverState,
      [domainId]: value,
    }))

    if (idMappings[domainId]) {
      for (const rangeId of idMappings[domainId]) {
        setHoverState((prevHoverState) => ({
          ...prevHoverState,
          [rangeId]: value,
        }))
      }
    }
  }
  const activeTurnover = activeTurnoverId
    ? room.turnovers.find((t) => t.id === activeTurnoverId)
    : undefined

  const onMouseEnter: MouseMovementHandler = (id?: string) =>
    id && setState(true)(id)
  const onMouseLeave: MouseMovementHandler = (id?: string) =>
    id && setState(false)(id)

  const [popupPosition, setPopupPosition] = useState<{
    x: number
    y: number
  } | null>(null)

  const handleRightClick =
    (selectedTurnover: Turnover) => (event: MouseEvent) => {
      if (turnoversUpdateState?.get(selectedTurnover.id)) {
        event.preventDefault()
        return
      }

      event.preventDefault()
      setSelectedTurnover(selectedTurnover)
      setPopupPosition({
        x: event.clientX,
        y: event.clientY,
      })

      eventsLogger(EVENTS.SCHEDULE_PAGE_OPEN_TURNOVER_DELAY_NOTE, {
        turnoverId: selectedTurnover.id,
      })
    }

  const [turnoversUpdateState, setTurnoversUpdateState] =
    useState<Map<string, boolean>>()

  const handleMutationComplete = useCallback((turnoverId: string) => {
    setTurnoversUpdateState((prev) => {
      const updatedMap = new Map(prev)
      updatedMap.set(turnoverId, false)
      return updatedMap
    })
  }, [])

  const { updateTurnoverLabelsAndNote: updateFunc } = useTurnoverUpdate(
    handleMutationComplete
  )

  const handleClosePopup = useCallback(
    (mutationStarted: boolean) => {
      if (selectedTurnover) {
        setTurnoversUpdateState((prev) => {
          const updatedMap = new Map(prev)
          updatedMap.set(selectedTurnover.id, mutationStarted)
          return updatedMap
        })
      }

      setPopupPosition(null)
    },
    [selectedTurnover]
  )

  return (
    <div
      css={{
        width: '100%',
        height: rowHeight,
        position: 'relative',
        '&:after': {
          content: '""',
          display: 'block',
          height: '100%',
          margin: `0 -${ROOM_PADDING}`,
          borderBottom: `1px solid ${theme.palette.gray[20]}`,
        },
      }}
      style={{
        cursor: hoveringInThePast ? 'pointer' : 'default',
      }}
      onClick={hoveringInThePast ? onTimelineClick : undefined}
      data-testid="room-case-timeline-row"
    >
      {blockTimeOnSchedulePageEnabled &&
        hoveredBlockTimes.length > 0 &&
        hoveredBlockTimes
          .filter(
            (blockTime) =>
              blockTime.roomId === room.id &&
              minTimeDT <= DateTime.fromISO(blockTime.endTime) &&
              DateTime.fromISO(blockTime.startTime) <= maxTimeDT
          )
          .map((blockTime) => (
            <BlockTimePillContainer
              key={`block-time-pill-${blockTime.id}`}
              blockTime={blockTime}
              calculatePillPositionAndSize={calculatePillPositionAndSize}
            />
          ))}
      {cases.map((apellaCase) => (
        <CombinedTimelinePill
          key={apellaCase.id}
          apellaCase={apellaCase}
          isHovered={
            hoverState[apellaCase.id] || highlightedEdits[apellaCase.id]
          }
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          calculatePillPositionAndSize={calculatePillPositionAndSize}
          tooltipPlacement={tooltipPlacement}
          onClick={onTimelineClick}
          showScheduled={showScheduled}
          hoveredCaseIds={hoveredCaseIds}
          minTime={minTimeDT}
          maxTime={maxTimeDT}
        />
      ))}
      {!activeTurnover &&
        room.turnovers.map((t) => (
          <TurnoverPill
            key={t.id}
            isActive={false}
            turnover={t}
            calculatePillPositionAndSize={calculatePillPositionAndSize}
            minTime={minTimeDT}
            maxTime={maxTimeDT}
            hasPendingUpdate={turnoversUpdateState?.get(t.id) ?? false}
            onClick={onTimelineClick}
            onRightClick={enableRightClick ? handleRightClick(t) : undefined}
            showScheduled={showScheduled}
          />
        ))}
      {activeTurnover && (
        <TurnoverPill
          isActive={true}
          turnover={activeTurnover}
          hasPendingUpdate={
            turnoversUpdateState?.get(activeTurnover.id) ?? false
          }
          calculatePillPositionAndSize={calculatePillPositionAndSize}
          minTime={minTimeDT}
          maxTime={maxTimeDT}
          onClick={onTimelineClick}
          onRightClick={
            enableRightClick ? handleRightClick(activeTurnover) : undefined
          }
        />
      )}
      {/* Turnover Delay Reasons and Notes Popup */}
      {selectedTurnover && popupPosition && (
        <TurnoverLabelNote
          position={popupPosition}
          turnover={selectedTurnover}
          updateFunc={updateFunc}
          onClose={(val) => handleClosePopup(val)}
          turnoverLabels={turnoverLabels}
        />
      )}
    </div>
  )
})

const RoomDetails = memo(function RoomDetails({
  minTime,
  maxTime,
  onClick,
  hoveringInThePast,
  room,
  eventNames,
  apellaCases,
}: {
  room: TimelineRoom
  minTime: string
  maxTime: string
  onClick: (roomId: string, apellaCase?: ApellaCase) => void
  hoveringInThePast: boolean
  eventNames?: string[]
  apellaCases: ApellaCase[]
}) {
  const { id } = room

  const onTimelineClick = useCallback(
    (mouseEvent: MouseEvent, apellaCase?: ApellaCase) => {
      // Stop propagation of click events. This is needed to avoid onClick being called for
      // both the timeline and the pill when the user clicks on a pill.
      mouseEvent.stopPropagation()

      if (apellaCase?.type !== CaseType.FORECAST || hoveringInThePast) {
        onClick(id, apellaCase)
      }
    },
    [id, onClick, hoveringInThePast]
  )

  const eventToRecipientsMap: { [key: string]: string[] } = useMemo(() => {
    return apellaCases
      .map((c) => c.case?.eventNotifications)
      .filter((notifications) => notifications && notifications.length > 0)
      .flat()
      .reduce(
        (acc, notification) => {
          if (!notification) return acc
          // Use `notificationEvent.id` as the key for grouping notification recipients.
          // This ensures consistency with the updated `EventNotification` structure,
          // where `notificationEvent.id` uniquely identifies each notification event.
          const recipientName =
            notification.staffEventContactInformation.contactInformation.name
          if (acc[notification.notificationEvent.id]) {
            acc[notification.notificationEvent.id].push(recipientName)
          } else {
            acc[notification?.notificationEvent.id] = [recipientName]
          }
          return acc
        },
        {} as { [key: string]: string[] }
      )
  }, [apellaCases])

  return (
    <>
      <div
        css={{
          width: '100%',
          verticalAlign: 'middle',
          height: EVENT_HEIGHT,
          overflowY: 'hidden',
        }}
        style={{ cursor: hoveringInThePast ? 'pointer' : 'default' }}
        onClick={hoveringInThePast ? onTimelineClick : undefined}
      >
        <div
          css={{
            width: '100%',
            position: 'relative',
          }}
          style={{ cursor: hoveringInThePast ? 'pointer' : 'default' }}
          onClick={hoveringInThePast ? onTimelineClick : undefined}
        >
          <EventTimeline
            minTime={minTime}
            maxTime={maxTime}
            roomId={id}
            eventNames={eventNames}
            eventToRecipientsMap={eventToRecipientsMap}
          />
        </div>
      </div>

      <div
        css={{
          width: '100%',
          overflowY: 'hidden',
          height: EVENT_HEIGHT,
        }}
        style={{ cursor: hoveringInThePast ? 'pointer' : 'default' }}
        onClick={hoveringInThePast ? onTimelineClick : undefined}
      >
        <div
          css={{
            width: '100%',
            height: EVENT_HEIGHT,
            position: 'relative',
          }}
          style={{ cursor: hoveringInThePast ? 'pointer' : 'default' }}
          onClick={hoveringInThePast ? onTimelineClick : undefined}
        >
          <ObservationsTimeline
            minTime={DateTime.fromISO(minTime)}
            maxTime={DateTime.fromISO(maxTime)}
            roomId={id}
            types={OBSERVATIONS_TO_QUERY}
            eventToRecipientsMap={eventToRecipientsMap}
          />
        </div>
      </div>

      <div
        css={{
          width: '100%',
          overflowY: 'hidden',
          position: 'relative',
          height: GRAPH_HEIGHT,
        }}
        style={{ cursor: hoveringInThePast ? 'pointer' : 'default' }}
        onClick={hoveringInThePast ? onTimelineClick : undefined}
      >
        <OccupancySlider
          roomId={id}
          startTime={DateTime.fromISO(minTime)}
          endTime={DateTime.fromISO(maxTime)}
        />
      </div>
    </>
  )
})

const EventTimeline = memo(function EventTimeline({
  minTime,
  maxTime,
  roomId,
  eventNames,
  eventToRecipientsMap,
}: {
  minTime: string
  maxTime: string
  roomId: string
  eventNames?: string[]
  eventToRecipientsMap?: { [key: string]: string[] }
}) {
  const videoStartTime = useMemo(() => DateTime.fromISO(minTime), [minTime])
  const videoEndTime = useMemo(() => DateTime.fromISO(maxTime), [maxTime])
  const theme = useTheme()
  const { timezone } = useTimezone()
  const isValidTimeRange = useMemo(
    () => videoEndTime > videoStartTime,
    [videoStartTime, videoEndTime]
  )
  const {
    data: eventData,
    refetch,
    startPolling,
    stopPolling,
  } = useQuery<RoomEventsQuery, RoomEventsQueryVariables>(ROOM_EVENTS_QUERY, {
    variables: {
      roomId,
      minTime: videoStartTime.toISO(),
      maxTime: videoEndTime.toISO(),
      eventNames: eventNames,
      includeDashboardEventsOnly: !eventNames,
    },
    skip: !isValidTimeRange,
  })

  usePolling({
    refetch,
    startPolling,
    stopPolling,
    interval: LIVE_POLL_INTERVAL_MS,
  })

  const getEvents = useCallback(
    (
      events: Array<
        | RoomEventsQuery['humanEvents']['edges'][number]
        | RoomEventsQuery['predictedEvents']['edges'][number]
      >
    ) => {
      return events.map<ApellaEvent>((eventEdge) => ({
        type: eventEdge.node.attrs?.type || Uncategorized,
        label: eventEdge.node.attrs?.name || Unlabeled,
        color: eventEdge.node.attrs?.color || theme.palette.gray[50],
        name: eventEdge.node.name,
        id: eventEdge.node.id,
        startTime: DateTime.fromISO(eventEdge.node.startTime),
        sourceType: eventEdge.node.sourceType,
      }))
    },
    [theme.palette.gray]
  )
  const humanEvents = useMemo(
    () => eventData?.humanEvents.edges || [],
    [eventData]
  )
  const predictedEvents = useMemo(
    () => eventData?.predictedEvents.edges || [],
    [eventData]
  )

  const events = useMemo(
    () => getEvents(humanEvents.concat(predictedEvents)),
    [getEvents, humanEvents, predictedEvents]
  )

  const getEventMarkers = useCallback(
    (events: ApellaEvent[]) => {
      return events.map<TimelineEventMarker>((event) => {
        const recipients = eventToRecipientsMap?.[event.id] ?? []
        return {
          id: event.id,
          color: event.color,
          startLocationPercent: calculateTimeMarkerPercentHelper(
            event.startTime,
            videoStartTime,
            videoEndTime
          ),
          startTime: event.startTime.setZone(timezone),
          label: event.label,
          name: event.name,
          recipients: recipients,
          sourceType: event.sourceType,
          filled: event.sourceType == 'human_gt',
          shape: recipients.length
            ? MarkerShape.MESSAGE_BUBBLE
            : MarkerShape.CIRCLE,
        }
      })
    },
    [eventToRecipientsMap, videoStartTime, videoEndTime, timezone]
  )

  const eventMarkers = useMemo(
    () => getEventMarkers(events),
    [getEventMarkers, events]
  )

  const [timelineSizeRef, setTimelineSizeRef] = useState<HTMLDivElement | null>(
    null
  )

  return (
    <div
      css={{
        width: '100%',
        height: 30,
        position: 'relative',
      }}
    >
      <FlexContainer>
        <FlexItem grow ref={setTimelineSizeRef}>
          <UnifiedTimelineEventMarkers
            markers={eventMarkers}
            boundary={timelineSizeRef ?? undefined}
          />
        </FlexItem>
      </FlexContainer>
    </div>
  )
})

const ObservationTypeTranslations: {
  [key in OBSERVATION_TYPE | string]?: string
} = {
  [OBSERVATION_TYPE.OBSERVED_ANESTHESIA_READY]: 'Anesthesia Ready',
  [OBSERVATION_TYPE.OBSERVED_CASE_START]: 'Case Start',
  [OBSERVATION_TYPE.OBSERVED_CASE_CLOSING]: 'Case Closing',
  [OBSERVATION_TYPE.OBSERVED_PRE_PROCEDURE_COMPLETE]: 'Pre Procedure Complete',
  [OBSERVATION_TYPE.OBSERVED_IN_PRE_PROCEDURE]: 'In Pre Procedure',
} as const

const ObservationsTimeline = memo(function ObxTimeline({
  minTime,
  maxTime,
  roomId,
  types,
  eventToRecipientsMap,
}: {
  minTime: DateTime
  maxTime: DateTime
  roomId: string
  types: OBSERVATION_TYPE[]
  eventToRecipientsMap?: { [key: string]: string[] }
}) {
  const { timezone } = useTimezone()
  const isValidTimeRange = useMemo(() => maxTime > minTime, [minTime, maxTime])
  const {
    data: observationData,
    refetch,
    startPolling,
    stopPolling,
  } = useQuery<RoomObservationQuery, RoomObservationQueryVariables>(
    ROOM_OBSERVATION_QUERY,
    {
      variables: {
        roomIds: [roomId],
        minObservationTime: minTime.toISO(),
        maxObservationTime: maxTime.toISO(),
        types,
      },
      skip: !roomId || !types.length || !isValidTimeRange,
    }
  )

  usePolling({
    refetch,
    startPolling,
    stopPolling,
    interval: LIVE_POLL_INTERVAL_MS,
  })

  const obxEventMarkers = useMemo(
    () =>
      observationData?.observation.edges?.map<TimelineEventMarker>(
        ({ node: observation }) => {
          const recipients = eventToRecipientsMap?.[observation.id] ?? []

          return {
            id: observation.id,
            label:
              ObservationTypeTranslations[observation.observationType.id] ??
              'Unknown',
            color: observation.observationType.color,
            startLocationPercent: calculateTimeMarkerPercentHelper(
              DateTime.fromISO(observation.observationTime),
              minTime,
              maxTime
            ),
            startTime: DateTime.fromISO(observation.observationTime).setZone(
              timezone
            ),
            name: observation.observationType.name,
            recipients,
            shape: recipients.length
              ? MarkerShape.MESSAGE_BUBBLE
              : MarkerShape.BAR,
          }
        }
      ) ?? [],
    [
      observationData?.observation.edges,
      minTime,
      maxTime,
      timezone,
      eventToRecipientsMap,
    ]
  )

  const [timelineSizeRef, setTimelineSizeRef] = useState<HTMLDivElement | null>(
    null
  )

  return (
    <div
      css={{
        width: '100%',
        height: EVENT_HEIGHT,
        position: 'relative',
        marginBottom: remSpacing.medium,
      }}
    >
      <FlexContainer>
        <FlexItem grow ref={setTimelineSizeRef}>
          <UnifiedTimelineEventMarkers
            markers={obxEventMarkers}
            boundary={timelineSizeRef ?? undefined}
            type={TimelineType.OBX}
          />
        </FlexItem>
      </FlexContainer>
    </div>
  )
})
