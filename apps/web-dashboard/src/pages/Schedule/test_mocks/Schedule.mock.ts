import { DateTime } from 'luxon'

import {
  CaseMatchingStatus,
  CaseStatusName,
  CaseType,
  RoomStatusName,
} from 'src/__generated__/globalTypes'
import { GET_SITE_OPTIONS_FILTER } from 'src/modules/site/queries'
import { PERMISSIONS } from 'src/modules/user/hooks'
import { GET_CURRENT_USER } from 'src/modules/user/queries'
import { GetTimelineCaseData } from 'src/pages/Schedule/__generated__'

import {
  GET_LIVE_FROM_SCHEDULE_ENABLED,
  GET_TIMELINE_CASE_DATA,
} from '../queries'

const lab1CaseResults: { data: GetTimelineCaseData } = {
  data: {
    sites: {
      edges: [
        {
          node: {
            name: 'Apella Lab',
            id: 'lab_1',

            rooms: {
              edges: [
                {
                  node: {
                    id: 'garage_2',
                    name: 'Garage 2',
                    sortKey: '2',
                    primeTimeConfig: {
                      __typename: 'RoomPrimeTimeConfig',
                      id: 'primeTimeConfig:1',
                      sunday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      monday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      tuesday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      wednesday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      thursday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      friday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      saturday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                    },
                    status: {
                      name: RoomStatusName.CLOSED,
                      inProgressApellaCase: null,
                      inProgressTurnover: null,
                      since: '2023-01-06T23:51:28.066471+00:00',
                      __typename: 'RoomStatus',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'phase:367eac62-3999-4ee8-8527-3b16d34e52b4',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T16:09:39.621462+00:00',
                            endTime: '2023-01-06T17:06:26.795944+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T17:06:26.795944+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            actual: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:37a6136b-0439-4d96-8eb7-8c47efc01d6b',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T17:46:48.565996+00:00',
                            endTime: '2023-01-06T18:29:41.658606+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T18:29:41.658606+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            actual: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:cce2f0cc-34f6-4d55-bca4-26128e261ca1',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T18:46:22.285528+00:00',
                            endTime: '2023-01-06T19:25:22.837493+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T19:25:22.837493+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            actual: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:fb6027d1-7752-4655-bc9d-0bc101d19af5',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T20:03:37.681058+00:00',
                            endTime: '2023-01-06T20:41:03.819346+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T20:41:03.819346+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            actual: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:32a49613-db85-457d-9ef5-774991dfe89f',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T20:59:19.043479+00:00',
                            endTime: '2023-01-06T22:07:45.083406+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T22:07:45.083406+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            actual: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:1f024646-fe33-45bc-80ac-1d3a4fbd0fd8',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T22:56:30.157850+00:00',
                            endTime: '2023-01-06T23:51:28.066471+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T23:51:28.066471+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            case: null,
                            actual: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_1',
                    name: 'Garage 1',
                    sortKey: '1',
                    primeTimeConfig: {
                      __typename: 'RoomPrimeTimeConfig',
                      id: 'primeTimeConfig:1',
                      sunday: {
                        __typename: 'TimeRange',
                        startTime: '07:00',
                        endTime: '19:00',
                      },
                      monday: {
                        __typename: 'TimeRange',
                        startTime: '07:00',
                        endTime: '19:00',
                      },
                      tuesday: {
                        __typename: 'TimeRange',
                        startTime: '07:00',
                        endTime: '19:00',
                      },
                      wednesday: {
                        __typename: 'TimeRange',
                        startTime: '07:00',
                        endTime: '19:00',
                      },
                      thursday: {
                        __typename: 'TimeRange',
                        startTime: '07:00',
                        endTime: '19:00',
                      },
                      friday: {
                        __typename: 'TimeRange',
                        startTime: '07:00',
                        endTime: '19:00',
                      },
                      saturday: {
                        __typename: 'TimeRange',
                        startTime: '07:00',
                        endTime: '19:00',
                      },
                    },
                    status: {
                      name: RoomStatusName.TURNOVER,
                      since: '2023-01-07T00:10:05.746026+00:00',
                      inProgressApellaCase: null,
                      inProgressTurnover: null,
                      __typename: 'RoomStatus',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:f5cf1cc5-e654-48ab-988a-ac8bd35d9198',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T16:14:26.772789+00:00',
                            endTime: '2023-01-06T17:40:16.006724+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T17:40:16.006724+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              id: 'f5cf1cc5-e654-48ab-988a-ac8bd35d9198',
                              scheduledStartTime:
                                '2023-01-06T16:12:31.026236+00:00',
                              scheduledEndTime:
                                '2023-01-06T18:07:31.026236+00:00',
                              serviceLine: null,
                              isFirstCase: true,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_066e448f-9aff-47d6-9006-7c4d539b28e6',
                              precedingCase: null,
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '0448e968-c4f7-4982-9466-6fedceb1c7c3',
                                    firstName: 'John',
                                    lastName: 'Dorian',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'a7d26be7-92ad-41bc-927e-f95fd9a3af96',
                                    firstName: 'Gregory',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              notePlan: null,
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:45ed9770-06eb-4228-8252-6338c3584446',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T18:11:24.399391+00:00',
                            endTime: '2023-01-06T19:06:06.351537+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T19:06:06.351537+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              id: '45ed9770-06eb-4228-8252-6338c3584446',
                              scheduledStartTime:
                                '2023-01-06T19:52:31.816287+00:00',
                              scheduledEndTime:
                                '2023-01-06T21:27:31.816287+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              serviceLine: null,
                              isAddOn: null,
                              patientClass: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_5d10ee17-a258-4353-b1f8-e8fd281daa13',
                              precedingCase: {
                                id: '830ed62f-e421-4d48-9c8e-cd854a571b81',
                                __typename: 'ScheduledCase',
                              },
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '8e971e0d-9be7-4129-82b7-3c9db7f6cde8',
                                    firstName: 'Gregory',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'I & D Abdomen',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:494731ea-745d-4ac1-bae8-5bf70ea2bc3a',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T19:55:59.702297+00:00',
                            endTime: '2023-01-06T21:08:05.603818+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T21:08:05.603818+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: '494731ea-745d-4ac1-bae8-5bf70ea2bc3a',
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              scheduledStartTime:
                                '2023-01-06T23:08:05.715324+00:00',
                              scheduledEndTime:
                                '2023-01-07T00:35:05.715324+00:00',
                              serviceLine: null,
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_69ec364d-5c12-4eb1-89e9-ef3aaf0d8bbd',
                              precedingCase: null,
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '50cf4be6-c9f3-4f02-8452-6bd36fd8efab',
                                    firstName: 'Miranda',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'Angiogram',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:4f2ee0e3-94c9-45bd-b0d7-fcae816c8639',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T21:43:32.018472+00:00',
                            endTime: '2023-01-06T22:32:15.936266+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T22:32:15.936266+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:dc047ab2-a32b-40c6-a107-350d54e5dadb',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T23:09:08.646207+00:00',
                            endTime: '2023-01-07T00:10:05.746026+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-07T00:10:05.746026+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:bd1730ab-95ce-43c5-8467-0378ead4535b',
                            type: CaseType.FORECAST,
                            startTime: '2023-10-05T19:19:50.042780+00:00',
                            endTime: '2023-10-05T19:19:50.042878+00:00',
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2023-01-07T00:10:05.746026+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: 'bd1730ab-95ce-43c5-8467-0378ead4535b',
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              scheduledStartTime:
                                '2023-01-06T21:39:10.762460+00:00',
                              scheduledEndTime:
                                '2023-01-06T22:55:10.762460+00:00',
                              isFirstCase: false,
                              serviceLine: null,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_a0c4d395-6295-4163-87e9-5bff478dc0b0',
                              precedingCase: null,
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'bca3eebb-aa9d-4185-9007-0555b63db319',
                                    firstName: 'Henry',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'a9b1793e-3f64-4f90-90d2-4c903cd30ef4',
                                    firstName: 'Gregory',
                                    lastName: 'Dorian',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'Angiogram',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:830ed62f-e421-4d48-9c8e-cd854a571b81',
                            type: CaseType.FORECAST,
                            startTime: '2023-10-05T19:19:50.043664+00:00',
                            endTime: '2023-10-05T19:19:50.043756+00:00',
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2023-01-07T00:10:05.746026+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: '830ed62f-e421-4d48-9c8e-cd854a571b81',
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              scheduledStartTime:
                                '2023-01-06T18:16:08.255140+00:00',
                              scheduledEndTime:
                                '2023-01-06T19:36:08.255140+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              serviceLine: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_3e3c5673-37b6-4bfa-9e65-3efc4c2511a5',
                              precedingCase: null,
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '8e971e0d-9be7-4129-82b7-3c9db7f6cde8',
                                    firstName: 'Gregory',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'Angiogram',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_0',
                    name: 'Garage 0',
                    sortKey: '0',
                    primeTimeConfig: {
                      __typename: 'RoomPrimeTimeConfig',
                      id: 'primeTimeConfig:1',
                      sunday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      monday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      tuesday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      wednesday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      thursday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      friday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                      saturday: {
                        startTime: '07:00',
                        endTime: '19:00',
                        __typename: 'TimeRange',
                      },
                    },
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2023-01-06T23:51:28.066471+00:00',
                      inProgressApellaCase: null,
                      inProgressTurnover: null,
                      __typename: 'RoomStatus',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:4db41606-547f-4e5d-9137-c2915c5252d6',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T16:09:10.511689+00:00',
                            endTime: '2023-01-06T17:14:13.331271+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T17:14:13.331271+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              serviceLine: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              id: '4db41606-547f-4e5d-9137-c2915c5252d6',
                              scheduledStartTime:
                                '2023-01-06T16:12:59.422526+00:00',
                              scheduledEndTime:
                                '2023-01-06T17:47:59.422526+00:00',
                              isFirstCase: true,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_e1174fe9-77ea-43c9-946d-436369ba4622',
                              precedingCase: null,
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'e2da4139-4395-45d1-9fef-8c81b1b0d350',
                                    firstName: 'John',
                                    lastName: 'Bailey',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'Colonoscopy',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'Angiogram',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:b28d466b-27e8-4240-8908-0891f11078a5',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T17:56:19.347934+00:00',
                            endTime: '2023-01-06T19:10:49.731177+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T19:10:49.731177+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              id: 'b28d466b-27e8-4240-8908-0891f11078a5',
                              scheduledStartTime:
                                '2023-01-06T19:45:49.355609+00:00',
                              scheduledEndTime:
                                '2023-01-06T20:59:49.355609+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              serviceLine: null,
                              patientClass: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_81772527-8e68-40c6-a5f9-9abb0f999dbb',
                              precedingCase: null,
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'f8c835e2-72c0-4f35-be14-aa1962371787',
                                    firstName: 'John',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'Angiogram',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:af13285b-c57d-4333-903b-697e2fef0773',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T19:48:00.729137+00:00',
                            endTime: '2023-01-06T20:48:15.774637+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T20:48:15.774637+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              id: 'af13285b-c57d-4333-903b-697e2fef0773',
                              scheduledStartTime:
                                '2023-01-06T23:11:22.929660+00:00',
                              scheduledEndTime:
                                '2023-01-07T00:31:22.929660+00:00',
                              serviceLine: null,
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_1290352e-417b-4c11-b7c7-f897c9b95dc7',
                              precedingCase: null,
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '6d844577-72c8-4e43-8763-dd95d60526df',
                                    firstName: 'Gregory',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'I & D Abdomen',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:e5031e1b-cc7d-4b24-9d31-9a62c39a3169',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T21:03:57.042154+00:00',
                            endTime: '2023-01-06T22:25:53.273868+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-06T22:25:53.273868+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:756bcf1b-660e-431e-93d8-c01c80a5692e',
                            type: CaseType.COMPLETE,
                            startTime: '2023-01-06T23:17:48.043470+00:00',
                            endTime: '2023-01-07T00:11:41.791100+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2023-01-07T00:11:41.791100+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:a0e74fd2-a356-48e7-b9cb-1475e332fff4',
                            type: CaseType.FORECAST,
                            startTime: '2023-10-05T19:19:50.048964+00:00',
                            endTime: '2023-10-05T19:19:50.049316+00:00',
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2023-01-07T00:11:41.791100+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              id: 'a0e74fd2-a356-48e7-b9cb-1475e332fff4',
                              scheduledStartTime:
                                '2023-01-06T21:09:07.400043+00:00',
                              scheduledEndTime:
                                '2023-01-06T23:00:07.400043+00:00',
                              isFirstCase: false,
                              serviceLine: null,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_8dd3931c-6cff-4c93-ba18-1c6ba9784be4',
                              precedingCase: {
                                id: 'b28d466b-27e8-4240-8908-0891f11078a5',
                                __typename: 'ScheduledCase',
                              },
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'f8c835e2-72c0-4f35-be14-aa1962371787',
                                    firstName: 'John',
                                    lastName: 'House',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:53af2e5b-1dfa-4eb7-b03c-bcec0c28bf0f',
                            type: CaseType.FORECAST,
                            startTime: '2023-10-05T19:19:50.050099+00:00',
                            endTime: '2023-10-05T19:19:50.050195+00:00',
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2023-01-07T00:11:41.791100+00:00',
                              __typename: 'ApellaCaseStatus',
                            },
                            actual: null,
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              id: '53af2e5b-1dfa-4eb7-b03c-bcec0c28bf0f',
                              scheduledStartTime:
                                '2023-01-06T17:54:48.029218+00:00',
                              scheduledEndTime:
                                '2023-01-06T19:32:48.029218+00:00',
                              isFirstCase: false,
                              isInFlipRoom: true,
                              isAddOn: null,
                              patientClass: null,
                              serviceLine: null,
                              externalCaseId:
                                'apella_internal_0_load-generator_f55c7bfd-dcbc-4966-8056-6b8a7dc0600c',
                              precedingCase: null,
                              caseClassificationType: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection',
                              },
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'bca3eebb-aa9d-4185-9007-0555b63db319',
                                    firstName: 'Henry',
                                    lastName: 'Wu',
                                    __typename: 'Staff',
                                  },
                                  __typename: 'CaseStaff',
                                },
                              ],
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'test',
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    __typename: 'Procedure',
                                  },
                                  __typename: 'CaseProcedure',
                                },
                              ],
                              patient: {
                                id: 'SOMEGUY',
                                personalInfo: null,
                                __typename: 'Patient',
                              },
                              eventNotifications: [
                                {
                                  sentTime: '2023-01-06T16:12:31.026236+00:00',
                                  event: {
                                    id: 'event:1',
                                    name: 'patient_wheels_in',
                                    color: 'green',
                                    attrs: {
                                      name: 'patient wheels in',
                                      __typename: 'EventType',
                                    },
                                    __typename: 'Event',
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'John',
                                      lastName: 'Dorian',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation',
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation',
                                  },
                                  __typename: 'EventNotification',
                                },
                              ],
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
              ],
              __typename: 'RoomConnection',
            },
            __typename: 'Site',
            turnoverGoals: {
              __typename: 'TurnoverGoals',
              goalMinutes: null,
              maxMinutes: 0,
            },
          },
          __typename: 'SiteEdge',
        },
      ],
      __typename: 'SiteConnection',
    },
  },
}

export const lab1CasePhaseResultsFunc = vi.fn(() => {
  return lab1CaseResults
})

const enteredDate = DateTime.fromISO('2023-01-06').setZone(
  'America/Los_Angeles',
  { keepLocalTime: true }
)

const ScheduleMocks = [
  {
    request: {
      query: GET_TIMELINE_CASE_DATA,
      variables: {
        maxStartTime: enteredDate.endOf('day').toISO(),
        minEndTime: enteredDate.startOf('day').toISO(),
        siteIds: ['lab_1'],
        caseMatchingStatuses: undefined,
        includeStaffPlan: false,
        includeNotePlan: false,
        includeTurnovers: true,
        includeEventNotifications: false,
        includePatientData: false,
      },
    },
    result: lab1CasePhaseResultsFunc,
    maxUsageCount: Number.POSITIVE_INFINITY,
  },
  {
    request: {
      query: GET_TIMELINE_CASE_DATA,
      variables: {
        maxStartTime: enteredDate.endOf('day').toISO(),
        minEndTime: enteredDate.startOf('day').toISO(),
        siteIds: ['lab_1'],
        caseMatchingStatuses: Object.values(CaseMatchingStatus),
        includeStaffPlan: false,
        includeNotePlan: false,
        includeTurnovers: true,
        includeEventNotifications: false,
        includePatientData: false,
      },
    },
    result: lab1CasePhaseResultsFunc,
    maxUsageCount: Number.POSITIVE_INFINITY,
  },
  {
    request: {
      query: GET_TIMELINE_CASE_DATA,
      variables: {
        maxStartTime: enteredDate.endOf('day').toISO(),
        minEndTime: enteredDate.startOf('day').toISO(),
        siteIds: [],
        caseMatchingStatuses: Object.values(CaseMatchingStatus),
        includeTurnovers: true,
        includeEventNotifications: true,
        includePatientData: false,
      },
    },
    result: {
      data: {
        sites: {
          edges: [
            {
              name: 'Apella Palo Alto',
              rooms: {
                edges: [
                  {
                    node: {
                      id: 'palo_alto_room_0',
                      name: 'OR 1',
                      apellaCases: {
                        edges: [
                          {
                            node: {
                              id: 'case:91d0f83e-e6a5-4cb9-9cb7-0504d6deca05',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T16:06:01.992868+00:00',
                              endTime: '2023-01-06T17:01:56.336059+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: {
                                id: '91d0f83e-e6a5-4cb9-9cb7-0504d6deca05',
                                scheduledStartTime:
                                  '2023-01-06T16:08:06.237029+00:00',
                                scheduledEndTime:
                                  '2023-01-06T17:24:06.237029+00:00',
                                isFirstCase: true,
                                isInFlipRoom: false,
                                isAddOn: null,
                                patientClass: null,
                                externalCaseId:
                                  'apella_internal_0_load-generator_4763b20b-3acd-4f1a-b929-b3d9a7acf485',
                                precedingCase: null,
                                caseClassificationType: null,
                                caseStaffPlan: {
                                  edges: [],
                                  __typename: 'CaseStaffPlanConnection',
                                },
                                caseStaff: [
                                  {
                                    role: 'Primary Surgeon',
                                    staff: {
                                      id: '1196f869-eea6-41fe-8ff8-fc3695b1d38e',
                                      firstName: 'John',
                                      lastName: 'Wu',
                                      __typename: 'Staff',
                                    },
                                    __typename: 'CaseStaff',
                                  },
                                ],
                                observations: {
                                  edges: [],
                                  __typename: 'ObservationConnection',
                                },
                                primaryCaseProcedures: [
                                  {
                                    procedure: {
                                      id: 'test',
                                      name: 'Colonoscopy',
                                      __typename: 'Procedure',
                                    },
                                    __typename: 'CaseProcedure',
                                  },
                                ],
                                patient: {
                                  id: 'SOMEGUY',
                                  personalInfo: null,
                                },
                                __typename: 'ScheduledCase',
                              },
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                          {
                            node: {
                              id: 'case:3738161a-5a2f-452e-85bd-f046a4aeab47',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T17:30:30.439085+00:00',
                              endTime: '2023-01-06T18:14:36.058479+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: {
                                id: '3738161a-5a2f-452e-85bd-f046a4aeab47',
                                scheduledStartTime:
                                  '2023-01-06T17:32:21.740796+00:00',
                                scheduledEndTime:
                                  '2023-01-06T18:38:21.740796+00:00',
                                isFirstCase: false,
                                isInFlipRoom: false,
                                isAddOn: null,
                                patientClass: null,
                                externalCaseId:
                                  'apella_internal_0_load-generator_ea6afe60-821c-41b0-8a04-03ef5a78628c',
                                precedingCase: {
                                  id: '91d0f83e-e6a5-4cb9-9cb7-0504d6deca05',
                                  __typename: 'ScheduledCase',
                                },
                                caseClassificationType: null,
                                caseStaffPlan: {
                                  edges: [],
                                  __typename: 'CaseStaffPlanConnection',
                                },
                                caseStaff: [
                                  {
                                    role: 'Primary Surgeon',
                                    staff: {
                                      id: '1196f869-eea6-41fe-8ff8-fc3695b1d38e',
                                      firstName: 'John',
                                      lastName: 'Wu',
                                      __typename: 'Staff',
                                    },
                                    __typename: 'CaseStaff',
                                  },
                                ],
                                observations: {
                                  edges: [],
                                  __typename: 'ObservationConnection',
                                },
                                primaryCaseProcedures: [
                                  {
                                    procedure: {
                                      id: 'test',
                                      name: 'Angiogram',
                                      __typename: 'Procedure',
                                    },
                                    __typename: 'CaseProcedure',
                                  },
                                ],
                                patient: {
                                  id: 'SOMEGUY',
                                  personalInfo: null,
                                },
                                __typename: 'ScheduledCase',
                              },
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                          {
                            node: {
                              id: 'case:f71b3b59-9eec-4e27-8af1-cf31b79df116',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T21:03:23.553047+00:00',
                              endTime: '2023-01-06T22:13:00.866794+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: {
                                id: 'f71b3b59-9eec-4e27-8af1-cf31b79df116',
                                scheduledStartTime:
                                  '2023-01-06T18:50:22.741921+00:00',
                                scheduledEndTime:
                                  '2023-01-06T20:47:22.741921+00:00',
                                isFirstCase: false,
                                isInFlipRoom: true,
                                isAddOn: null,
                                patientClass: null,
                                externalCaseId:
                                  'apella_internal_0_load-generator_2c7988ba-3068-40f7-aa2b-a96d5ae649a0',
                                precedingCase: null,
                                caseClassificationType: null,
                                caseStaffPlan: {
                                  edges: [],
                                  __typename: 'CaseStaffPlanConnection',
                                },
                                caseStaff: [
                                  {
                                    role: 'Primary Surgeon',
                                    staff: {
                                      id: 'bca3eebb-aa9d-4185-9007-0555b63db319',
                                      firstName: 'Henry',
                                      lastName: 'Wu',
                                      __typename: 'Staff',
                                    },
                                    __typename: 'CaseStaff',
                                  },
                                ],
                                observations: {
                                  edges: [],
                                  __typename: 'ObservationConnection',
                                },
                                primaryCaseProcedures: [
                                  {
                                    procedure: {
                                      id: 'test',
                                      name: 'Knee Arthroplasty JRP - Total Replacement',
                                      __typename: 'Procedure',
                                    },
                                    __typename: 'CaseProcedure',
                                  },
                                ],
                                patient: {
                                  id: 'SOMEGUY',
                                  personalInfo: null,
                                },
                                __typename: 'ScheduledCase',
                              },
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                          {
                            node: {
                              id: 'case:6cf54824-5c11-4bea-b0ae-cc142a2f2dd2',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T22:51:03.995877+00:00',
                              endTime: '2023-01-06T23:49:57.323263+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: {
                                id: '6cf54824-5c11-4bea-b0ae-cc142a2f2dd2',
                                scheduledStartTime:
                                  '2023-01-06T20:59:55.551687+00:00',
                                scheduledEndTime:
                                  '2023-01-06T22:32:55.551687+00:00',
                                isFirstCase: false,
                                isInFlipRoom: false,
                                isAddOn: null,
                                patientClass: null,
                                externalCaseId:
                                  'apella_internal_0_load-generator_81550c33-d36e-4efd-a32f-f44d2990ffda',
                                precedingCase: null,
                                caseClassificationType: null,
                                caseStaffPlan: {
                                  edges: [],
                                  __typename: 'CaseStaffPlanConnection',
                                },
                                caseStaff: [
                                  {
                                    role: 'Primary Surgeon',
                                    staff: {
                                      id: 'd4f808b1-aaee-47e1-b0f9-74fb8576988a',
                                      firstName: 'Miranda',
                                      lastName: 'Dorian',
                                      __typename: 'Staff',
                                    },
                                    __typename: 'CaseStaff',
                                  },
                                ],
                                observations: {
                                  edges: [],
                                  __typename: 'ObservationConnection',
                                },
                                primaryCaseProcedures: [
                                  {
                                    procedure: {
                                      id: 'test',
                                      name: 'Knee Arthroplasty JRP - Total Replacement',
                                      __typename: 'Procedure',
                                    },
                                    __typename: 'CaseProcedure',
                                  },
                                ],
                                patient: {
                                  id: 'SOMEGUY',
                                  personalInfo: null,
                                },
                                __typename: 'ScheduledCase',
                              },
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                          {
                            node: {
                              id: 'case:9d3e7c3f-9034-4076-9ed0-bed29c0e0ec0',
                              type: CaseType.FORECAST,
                              startTime: '2023-10-05T19:22:00.282305+00:00',
                              endTime: '2023-10-05T19:22:00.282362+00:00',
                              status: {
                                name: CaseStatusName.SCHEDULED,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: {
                                id: '9d3e7c3f-9034-4076-9ed0-bed29c0e0ec0',
                                scheduledStartTime:
                                  '2023-01-06T22:48:33.159932+00:00',
                                scheduledEndTime:
                                  '2023-01-07T00:09:33.159932+00:00',
                                isFirstCase: false,
                                isInFlipRoom: false,
                                isAddOn: null,
                                patientClass: null,
                                externalCaseId:
                                  'apella_internal_0_load-generator_a6a7e60b-b7ae-412c-9b12-2895dc58ec62',
                                precedingCase: null,
                                caseClassificationType: null,
                                caseStaffPlan: {
                                  edges: [],
                                  __typename: 'CaseStaffPlanConnection',
                                },
                                caseStaff: [
                                  {
                                    role: 'Primary Surgeon',
                                    staff: {
                                      id: '1196f869-eea6-41fe-8ff8-fc3695b1d38e',
                                      firstName: 'John',
                                      lastName: 'Wu',
                                      __typename: 'Staff',
                                    },
                                    __typename: 'CaseStaff',
                                  },
                                ],
                                observations: {
                                  edges: [],
                                  __typename: 'ObservationConnection',
                                },
                                primaryCaseProcedures: [
                                  {
                                    procedure: {
                                      id: 'test',
                                      name: 'Angiogram',
                                      __typename: 'Procedure',
                                    },
                                    __typename: 'CaseProcedure',
                                  },
                                ],
                                patient: {
                                  id: 'SOMEGUY',
                                  personalInfo: null,
                                },
                                __typename: 'ScheduledCase',
                              },
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                        ],
                        __typename: 'ApellaCaseConnection',
                      },
                      __typename: 'Room',
                    },
                    __typename: 'RoomEdge',
                  },
                  {
                    node: {
                      id: 'palo_alto_room_1',
                      name: 'OR 2',
                      apellaCases: {
                        edges: [
                          {
                            node: {
                              id: 'phase:27531739-4ea3-4d8c-8673-5be5994cd451',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T16:09:07.129266+00:00',
                              endTime: '2023-01-06T17:05:43.379324+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: null,
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                          {
                            node: {
                              id: 'phase:47435e46-7f56-4952-8b37-fa46cd30fa95',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T17:48:57.055582+00:00',
                              endTime: '2023-01-06T18:39:21.360047+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: null,
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                          {
                            node: {
                              id: 'phase:bc7b4fee-daaf-4fb0-83f3-aa9bafdfe3f1',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T19:02:09.138770+00:00',
                              endTime: '2023-01-06T20:03:30.603378+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: null,
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                          {
                            node: {
                              id: 'phase:c645f81b-b900-47b3-b9b5-efee94e92a94',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T20:40:14.199163+00:00',
                              endTime: '2023-01-06T21:33:45.484105+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: null,
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                          {
                            node: {
                              id: 'phase:bcfa355b-69a7-4043-8d6c-bbb8ad52c8a0',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T22:11:04.041192+00:00',
                              endTime: '2023-01-06T22:57:35.829741+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: null,
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                          {
                            node: {
                              id: 'phase:259a8925-f0ff-449f-8010-476720769405',
                              type: CaseType.COMPLETE,
                              startTime: '2023-01-06T23:29:31.259520+00:00',
                              endTime: '2023-01-07T00:38:29.445689+00:00',
                              status: {
                                name: CaseStatusName.ACTUAL,
                                __typename: 'ApellaCaseStatus',
                              },
                              case: null,
                              __typename: 'ApellaCase',
                            },
                            __typename: 'ApellaCaseEdge',
                          },
                        ],
                        __typename: 'ApellaCaseConnection',
                      },
                      __typename: 'Room',
                    },
                    __typename: 'RoomEdge',
                  },
                ],
                __typename: 'RoomConnection',
              },
              __typename: 'Site',
            },
          ],
        },
      },
    },
  },
  {
    request: {
      query: GET_LIVE_FROM_SCHEDULE_ENABLED,
      variables: {},
    },
    result: {
      data: {
        me: {
          __typename: 'User',
          uiPermissions: {
            __typename: 'UserUiPermissions',
            dashboardLiveFromScheduleEnabled: true,
          },
        },
      },
    },
  },
  {
    request: {
      query: GET_SITE_OPTIONS_FILTER,
      variables: {},
    },
    result: {
      data: {
        sites: {
          __typename: 'SiteConnection',
          edges: [
            {
              __typename: 'SiteEdge',
              node: {
                __typename: 'Site',
                id: 'lab_1',
                name: 'Apella Lab',
                rooms: {
                  __typename: 'RoomConnection',
                  edges: [
                    {
                      __typename: 'RoomEdge',
                      node: {
                        __typename: 'Room',
                        id: 'garage_2',
                        site: {
                          __typename: 'Site',
                          id: 'lab_1',
                          name: 'Apella Lab',
                        },
                        name: 'Garage 2',
                      },
                    },
                    {
                      __typename: 'RoomEdge',
                      node: {
                        __typename: 'Room',
                        id: 'garage_1',
                        site: {
                          __typename: 'Site',
                          id: 'lab_1',
                          name: 'Apella Lab',
                        },
                        name: 'Garage 1',
                      },
                    },
                    {
                      __typename: 'RoomEdge',
                      node: {
                        __typename: 'Room',
                        id: 'garage_0',
                        site: {
                          __typename: 'Site',
                          id: 'lab_1',
                          name: 'Apella Lab',
                        },
                        name: 'Garage 0',
                      },
                    },
                    {
                      __typename: 'RoomEdge',
                      node: {
                        __typename: 'Room',
                        id: 'AT-DEV-OR1',
                        site: {
                          __typename: 'Site',
                          id: 'lab_1',
                          name: 'Apella Lab',
                        },
                        name: 'Test',
                      },
                    },
                  ],
                },
              },
            },
            {
              __typename: 'SiteEdge',
              node: {
                __typename: 'Site',
                id: 'palo_alto_1',
                name: 'Apella Palo Alto',
                rooms: {
                  __typename: 'RoomConnection',
                  edges: [
                    {
                      __typename: 'RoomEdge',
                      node: {
                        __typename: 'Room',
                        id: 'palo_alto_room_0',
                        site: {
                          __typename: 'Site',
                          id: 'palo_alto_1',
                          name: 'Apella Palo Alto',
                        },
                        name: 'OR 1',
                      },
                    },
                    {
                      __typename: 'RoomEdge',
                      node: {
                        __typename: 'Room',
                        id: 'palo_alto_room_1',
                        site: {
                          __typename: 'Site',
                          id: 'palo_alto_1',
                          name: 'Apella Palo Alto',
                        },
                        name: 'OR 2',
                      },
                    },
                  ],
                },
              },
            },
          ],
        },
        caseStaffPlan: {
          edges: [],
          __typename: 'CaseStaffPlanConnection',
        },
        caseStaff: [],
        procedures: {
          __typename: 'ProcedureConnection',
          edges: [
            {
              __typename: 'ProcedureEdge',
              node: {
                __typename: 'Procedure',
                id: '12c6a767-6706-4abf-a244-441d3f475c31',
                name: 'Colonoscopy',
              },
            },
            {
              __typename: 'ProcedureEdge',
              node: {
                __typename: 'Procedure',
                id: '4513ffd8-6fdc-4408-9bc1-a3a52184bd84',
                name: 'I & D Abdomen',
              },
            },
            {
              __typename: 'ProcedureEdge',
              node: {
                __typename: 'Procedure',
                id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                name: 'Angiogram',
              },
            },
            {
              __typename: 'ProcedureEdge',
              node: {
                __typename: 'Procedure',
                id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
              },
            },
            {
              __typename: 'ProcedureEdge',
              node: {
                __typename: 'Procedure',
                id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                name: 'Knee Arthroplasty JRP - Total Replacement',
              },
            },
          ],
        },
      },
    },
  },
  {
    request: {
      query: GET_CURRENT_USER,
      variables: {},
    },
    result: {
      data: {
        me: {
          id: 'google-apps|<EMAIL>',
          organizations: {
            edges: [
              {
                node: {
                  id: 'apella_internal_0',
                  auth0OrgId: 'org_azS0mj1wtTf1FVJK',
                  name: 'Apella Internal',
                  sites: {
                    edges: [
                      {
                        node: {
                          id: 'lab_1',
                          timezone: 'America/Los_Angeles',
                          name: 'Apella Lab',
                          rooms: {
                            edges: [
                              {
                                node: {
                                  id: 'garage_2',
                                  name: 'Garage 2',
                                  __typename: 'Room',
                                },
                                __typename: 'RoomEdge',
                              },
                              {
                                node: {
                                  id: 'garage_1',
                                  name: 'Garage 1',
                                  __typename: 'Room',
                                },
                                __typename: 'RoomEdge',
                              },
                              {
                                node: {
                                  id: 'garage_0',
                                  name: 'Garage 0',
                                  __typename: 'Room',
                                },
                                __typename: 'RoomEdge',
                              },
                              {
                                node: {
                                  id: 'AT-DEV-OR1',
                                  name: 'Test',
                                  __typename: 'Room',
                                },
                                __typename: 'RoomEdge',
                              },
                            ],
                            __typename: 'RoomConnection',
                          },
                          __typename: 'Site',
                        },
                        __typename: 'SiteEdge',
                      },
                      {
                        node: {
                          id: 'palo_alto_1',
                          timezone: 'America/Los_Angeles',
                          name: 'Apella Palo Alto',
                          rooms: {
                            edges: [
                              {
                                node: {
                                  id: 'palo_alto_room_0',
                                  name: 'OR 1',
                                  __typename: 'Room',
                                },
                                __typename: 'RoomEdge',
                              },
                              {
                                node: {
                                  id: 'palo_alto_room_1',
                                  name: 'OR 2',
                                  __typename: 'Room',
                                },
                                __typename: 'RoomEdge',
                              },
                            ],
                            __typename: 'RoomConnection',
                          },
                          __typename: 'Site',
                        },
                        __typename: 'SiteEdge',
                      },
                    ],
                    __typename: 'SiteConnection',
                  },
                  __typename: 'Organization',
                },
                __typename: 'OrganizationEdge',
              },
            ],
            __typename: 'OrganizationConnection',
          },
          uiPermissions: {
            dashboardInsightsEnabled: true,
            dashboardHighlightsEnabled: true,
            dashboardScheduleEnabled: true,
            dashboardLiveEnabled: true,
            dashboardLiveFromScheduleEnabled: true,
            dashboardTerminalCleansEnabled: true,
            dashboardCreateMeasurementPeriodsEnabled: false,
            dashboardStaffManagementEnabled: true,
            dashboardScheduleEditEnabled: true,
            bigBoardEnabled: true,
            __typename: 'UserUiPermissions',
          },
          permissions: [PERMISSIONS.READ_DASHBOARD_TURNOVERS],
          __typename: 'User',
        },
      },
    },
  },
]
export default ScheduleMocks
