import {
  CaseMatching<PERSON>tatus,
  <PERSON><PERSON>tatusName,
  <PERSON>Type,
  Room<PERSON>tatus<PERSON>ame,
  Turnover<PERSON>abel,
  TurnoverType,
} from 'src/__generated__/globalTypes'
import { GetSiteOptionsFilter } from 'src/modules/site/__generated__'

import { GetBlockUtilizationForSite } from '../__generated__'

export const ScheduleTimelineCaseData = {
  data: {
    sites: {
      edges: [
        {
          node: {
            id: 'lab_1',
            name: 'Apella Lab',
            turnoverGoals: {
              goalMinutes: 35,
              maxMinutes: 80,
              __typename: 'TurnoverGoals' as const,
            },
            rooms: {
              edges: [
                {
                  node: {
                    sortKey: null,
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-29T00:00:58-07:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus' as const,
                    },
                    id: 'AT-DEV-OR1',
                    name: 'Test',
                    primeTimeConfig: {
                      id: '9e11890b-0686-464e-9a55-33082972302b',
                      sunday: {
                        startTime: '13:00:00',
                        endTime: '15:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      monday: {
                        startTime: '08:30:00',
                        endTime: '17:30:00',
                        __typename: 'TimeRange' as const,
                      },
                      tuesday: {
                        startTime: '09:30:00',
                        endTime: '17:30:00',
                        __typename: 'TimeRange' as const,
                      },
                      wednesday: {
                        startTime: '08:30:00',
                        endTime: '17:30:00',
                        __typename: 'TimeRange' as const,
                      },
                      thursday: null,
                      friday: null,
                      saturday: null,
                      __typename: 'RoomPrimeTimeConfig' as const,
                    },
                    turnovers: [],
                    apellaCases: {
                      edges: [],
                      __typename: 'ApellaCaseConnection' as const,
                    },
                    __typename: 'Room' as const,
                  },
                  __typename: 'RoomEdge' as const,
                },
                {
                  node: {
                    sortKey: null,
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-29T22:48:14.770799+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:3f780886-41e6-4cb2-a8b0-d661d1b3cbfe',
                        startTime: '2024-10-29T22:48:14.770799+00:00',
                        endTime: '2024-10-29T23:24:35.243398+00:00',
                        status: {
                          name: CaseStatusName.SURGERY,
                          since: '2024-10-29T23:09:08.904918+00:00',
                          __typename: 'ApellaCaseStatus' as const,
                        },
                        __typename: 'ApellaCase' as const,
                      },
                      __typename: 'RoomStatus' as const,
                    },
                    id: 'garage_0',
                    name: 'Garage 0-test',
                    primeTimeConfig: {
                      id: '0c1eec95-f25a-4505-aa16-ae5fbca1925f',
                      sunday: {
                        startTime: '10:00:00',
                        endTime: '15:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      monday: {
                        startTime: '09:00:00',
                        endTime: '15:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      tuesday: {
                        startTime: '11:34:00',
                        endTime: '22:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      wednesday: null,
                      thursday: null,
                      friday: null,
                      saturday: null,
                      __typename: 'RoomPrimeTimeConfig' as const,
                    },
                    turnovers: [
                      {
                        id: 'turnover-case:310868c2-a3e9-48a2-9484-bfdad26c550f-case:b623e51c-c8cc-40a9-b19e-f9f286d69e33',
                        startTime: '2024-10-29T15:51:58.729428+00:00',
                        endTime: '2024-10-29T16:31:58.169028+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:b623e51c-c8cc-40a9-b19e-f9f286d69e33',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T16:23:09.536290+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:310868c2-a3e9-48a2-9484-bfdad26c550f',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-case:b623e51c-c8cc-40a9-b19e-f9f286d69e33-case:3492bdd4-aa23-4646-a691-f854cd82777d',
                        startTime: '2024-10-29T17:42:03.225213+00:00',
                        endTime: '2024-10-29T18:18:39.786769+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:3492bdd4-aa23-4646-a691-f854cd82777d',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T18:05:50.813076+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:b623e51c-c8cc-40a9-b19e-f9f286d69e33',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-case:3492bdd4-aa23-4646-a691-f854cd82777d-case:1e30532b-0ab4-462c-a891-38cc72ec3568',
                        startTime: '2024-10-29T19:14:17.935330+00:00',
                        endTime: '2024-10-29T19:40:52.288356+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:1e30532b-0ab4-462c-a891-38cc72ec3568',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T19:41:33.546725+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:3492bdd4-aa23-4646-a691-f854cd82777d',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-case:1e30532b-0ab4-462c-a891-38cc72ec3568-case:12e37444-db14-4776-9b6a-6087a91643f7',
                        startTime: '2024-10-29T20:38:38.958544+00:00',
                        endTime: '2024-10-29T21:10:21.226017+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:12e37444-db14-4776-9b6a-6087a91643f7',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T21:04:15.777192+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:1e30532b-0ab4-462c-a891-38cc72ec3568',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-case:12e37444-db14-4776-9b6a-6087a91643f7-case:3f780886-41e6-4cb2-a8b0-d661d1b3cbfe',
                        startTime: '2024-10-29T22:22:04.205256+00:00',
                        endTime: '2024-10-29T22:48:14.770799+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:3f780886-41e6-4cb2-a8b0-d661d1b3cbfe',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T22:46:46.408450+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:12e37444-db14-4776-9b6a-6087a91643f7',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                    ],
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:310868c2-a3e9-48a2-9484-bfdad26c550f',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T15:11:00.556680+00:00',
                            endTime: '2024-10-29T15:51:58.729428+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T15:51:58.729428+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: 'c9af974e-5c30-4bbb-b047-49f700147770',
                              room: {
                                id: 'garage_0',
                                name: 'Garage 0-test',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T15:11:00.556680+00:00',
                              endTime: '2024-10-29T15:51:58.729428+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              notePlan: null,
                              caseLabels: [],
                              id: '310868c2-a3e9-48a2-9484-bfdad26c550f',
                              scheduledStartTime:
                                '2024-10-29T15:14:12.543177+00:00',
                              scheduledEndTime:
                                '2024-10-29T16:09:12.543177+00:00',
                              isFirstCase: true,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_42ef1654-1b7c-43e2-9303-a1d652b21b1e',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'f8c835e2-72c0-4f35-be14-aa1962371787',
                                    firstName: 'John',
                                    lastName: 'House',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:b623e51c-c8cc-40a9-b19e-f9f286d69e33',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T16:31:58.169028+00:00',
                            endTime: '2024-10-29T17:42:03.225213+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T17:42:03.225213+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '277fa00e-8689-43e4-bf34-2d3872e3c055',
                              room: {
                                id: 'garage_0',
                                name: 'Garage 0-test',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T16:31:58.169028+00:00',
                              endTime: '2024-10-29T17:42:03.225213+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: 'b623e51c-c8cc-40a9-b19e-f9f286d69e33',
                              scheduledStartTime:
                                '2024-10-29T16:23:09.536290+00:00',
                              scheduledEndTime:
                                '2024-10-29T17:48:09.536290+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_5b2a7aeb-4383-45ff-9340-b75ef1d3696c',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '63ece80a-d0c6-4b1e-bc96-dc81c16c7100',
                                    firstName: 'Henry',
                                    lastName: 'Bailey',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: '4513ffd8-6fdc-4408-9bc1-a3a52184bd84',
                                    name: 'I & D Abdomen',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                                {
                                  procedure: {
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:3492bdd4-aa23-4646-a691-f854cd82777d',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T18:18:39.786769+00:00',
                            endTime: '2024-10-29T19:14:17.935330+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T19:14:17.935330+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '74886869-f8f7-47e4-91d7-6050c7cd18dc',
                              room: {
                                id: 'garage_0',
                                name: 'Garage 0-test',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T18:18:39.786769+00:00',
                              endTime: '2024-10-29T19:14:17.935330+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: '3492bdd4-aa23-4646-a691-f854cd82777d',
                              scheduledStartTime:
                                '2024-10-29T18:05:50.813076+00:00',
                              scheduledEndTime:
                                '2024-10-29T19:28:50.813076+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_f1ed616a-e9ea-4f64-9a6a-ee7cf99fca60',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '1196f869-eea6-41fe-8ff8-fc3695b1d38e',
                                    firstName: 'John',
                                    lastName: 'Wu',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'd163eed7-2be0-4958-86be-2f5d50814dfc',
                                    firstName: 'Henry',
                                    lastName: 'Dorian',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:1e30532b-0ab4-462c-a891-38cc72ec3568',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T19:40:52.288356+00:00',
                            endTime: '2024-10-29T20:38:38.958544+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T20:38:38.958544+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '80c31718-b75c-4057-aeb1-94455c83f077',
                              room: {
                                id: 'garage_0',
                                name: 'Garage 0-test',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T19:40:52.288356+00:00',
                              endTime: '2024-10-29T20:38:38.958544+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: '1e30532b-0ab4-462c-a891-38cc72ec3568',
                              scheduledStartTime:
                                '2024-10-29T19:41:33.546725+00:00',
                              scheduledEndTime:
                                '2024-10-29T20:52:33.546725+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_2f664280-c57b-440b-b15b-35023aee46a1',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'e2da4139-4395-45d1-9fef-8c81b1b0d350',
                                    firstName: 'John',
                                    lastName: 'Bailey',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:12e37444-db14-4776-9b6a-6087a91643f7',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T21:10:21.226017+00:00',
                            endTime: '2024-10-29T22:22:04.205256+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T22:22:04.205256+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '7be6b85f-2cef-400c-ae30-57e68b8849e0',
                              room: {
                                id: 'garage_0',
                                name: 'Garage 0-test',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T21:10:21.226017+00:00',
                              endTime: '2024-10-29T22:22:04.205256+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: '12e37444-db14-4776-9b6a-6087a91643f7',
                              scheduledStartTime:
                                '2024-10-29T21:04:15.777192+00:00',
                              scheduledEndTime:
                                '2024-10-29T22:36:15.777192+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_cb2096ec-b14e-454c-89d7-3154b9a0a0b2',
                              precedingCase: {
                                id: '1e30532b-0ab4-462c-a891-38cc72ec3568',
                                __typename: 'ScheduledCase' as const,
                              },
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'e2da4139-4395-45d1-9fef-8c81b1b0d350',
                                    firstName: 'John',
                                    lastName: 'Bailey',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                    name: 'Knee Arthroplasty JRP - Total Replacement',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:3f780886-41e6-4cb2-a8b0-d661d1b3cbfe',
                            type: CaseType.LIVE,
                            startTime: '2024-10-29T22:48:14.770799+00:00',
                            endTime: '2024-10-29T23:24:35.243398+00:00',
                            status: {
                              name: CaseStatusName.SURGERY,
                              since: '2024-10-29T23:09:08.904918+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: 'c6c9feed-9cb4-4036-9ffa-bffbb23a533e',
                              room: {
                                id: 'garage_0',
                                name: 'Garage 0-test',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T22:48:14.770799+00:00',
                              endTime: null,
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: '3f780886-41e6-4cb2-a8b0-d661d1b3cbfe',
                              scheduledStartTime:
                                '2024-10-29T22:46:46.408450+00:00',
                              scheduledEndTime:
                                '2024-10-30T00:22:46.408450+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_35b59575-ad49-4191-a94d-eaa4e0ccf7ef',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '3008c147-f8b4-4b07-9e58-49b4fb333c77',
                                    firstName: 'Miranda',
                                    lastName: 'Wu',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                      ],
                      __typename: 'ApellaCaseConnection' as const,
                    },
                    __typename: 'Room' as const,
                  },
                  __typename: 'RoomEdge' as const,
                },
                {
                  node: {
                    sortKey: null,
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-29T23:13:22.244854+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                        startTime: '2024-10-29T23:13:22.244854+00:00',
                        endTime: '2024-10-29T23:40:33.044854+00:00',
                        status: {
                          name: CaseStatusName.PREP,
                          since: '2024-10-29T23:20:12.260449+00:00',
                          __typename: 'ApellaCaseStatus' as const,
                        },
                        __typename: 'ApellaCase' as const,
                      },
                      __typename: 'RoomStatus' as const,
                    },
                    id: 'garage_1',
                    name: 'Garage 1',
                    primeTimeConfig: {
                      id: '85e8e709-082f-4970-bc23-656bd24036d6',
                      sunday: null,
                      monday: {
                        startTime: '08:00:00',
                        endTime: '17:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      tuesday: {
                        startTime: '11:01:00',
                        endTime: '15:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      wednesday: {
                        startTime: '06:50:00',
                        endTime: '17:54:00',
                        __typename: 'TimeRange' as const,
                      },
                      thursday: {
                        startTime: '05:15:00',
                        endTime: '19:16:00',
                        __typename: 'TimeRange' as const,
                      },
                      friday: {
                        startTime: '13:00:00',
                        endTime: '14:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      saturday: null,
                      __typename: 'RoomPrimeTimeConfig' as const,
                    },
                    turnovers: [
                      {
                        id: 'turnover-case:3344d864-40fc-4adc-aec7-a15c8f466bce-case:ec9c5465-3aa7-4e58-9dcb-ae9ca068cc81',
                        startTime: '2024-10-29T16:15:34.894109+00:00',
                        endTime: '2024-10-29T16:37:39.231967+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:ec9c5465-3aa7-4e58-9dcb-ae9ca068cc81',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T16:43:13.884672+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:3344d864-40fc-4adc-aec7-a15c8f466bce',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-case:ec9c5465-3aa7-4e58-9dcb-ae9ca068cc81-case:a5dde9c0-b9ec-4e13-bcdf-da6cf37e3a06',
                        startTime: '2024-10-29T17:35:44.642644+00:00',
                        endTime: '2024-10-29T18:17:12.980195+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:a5dde9c0-b9ec-4e13-bcdf-da6cf37e3a06',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T18:10:31.324935+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:ec9c5465-3aa7-4e58-9dcb-ae9ca068cc81',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-case:a5dde9c0-b9ec-4e13-bcdf-da6cf37e3a06-case:ef6b701d-ea5c-4ee0-96b3-3cd2cdaa243e',
                        startTime: '2024-10-29T19:14:11.658930+00:00',
                        endTime: '2024-10-29T19:41:16.253353+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:ef6b701d-ea5c-4ee0-96b3-3cd2cdaa243e',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T19:44:37.240557+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:a5dde9c0-b9ec-4e13-bcdf-da6cf37e3a06',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-case:ef6b701d-ea5c-4ee0-96b3-3cd2cdaa243e-case:e1afba78-1fb1-470b-bec0-9fb2c9f49372',
                        startTime: '2024-10-29T20:48:39.324388+00:00',
                        endTime: '2024-10-29T21:44:43.311017+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:e1afba78-1fb1-470b-bec0-9fb2c9f49372',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T21:38:10.001447+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:ef6b701d-ea5c-4ee0-96b3-3cd2cdaa243e',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-case:e1afba78-1fb1-470b-bec0-9fb2c9f49372-case:b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                        startTime: '2024-10-29T22:38:13.806962+00:00',
                        endTime: '2024-10-29T23:13:22.244854+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T23:08:59.691216+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:e1afba78-1fb1-470b-bec0-9fb2c9f49372',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                    ],
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:3344d864-40fc-4adc-aec7-a15c8f466bce',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T15:06:59.309817+00:00',
                            endTime: '2024-10-29T16:15:34.894109+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T16:15:34.894109+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: 'aa8ecdff-ed24-499d-a486-3291d943d76c',
                              room: {
                                id: 'garage_1',
                                name: 'Garage 1',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T15:06:59.309817+00:00',
                              endTime: '2024-10-29T16:15:34.894109+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: '3344d864-40fc-4adc-aec7-a15c8f466bce',
                              scheduledStartTime:
                                '2024-10-29T15:07:00.878675+00:00',
                              scheduledEndTime:
                                '2024-10-29T16:31:00.878675+00:00',
                              isFirstCase: true,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_a6f58893-6d12-453a-a05f-89f4dcaad88d',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'a7d26be7-92ad-41bc-927e-f95fd9a3af96',
                                    firstName: 'Gregory',
                                    lastName: 'Wu',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:ec9c5465-3aa7-4e58-9dcb-ae9ca068cc81',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T16:37:39.231967+00:00',
                            endTime: '2024-10-29T17:35:44.642644+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T17:35:44.642644+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: 'd4547e60-401a-45b7-a33b-5174e974cd9e',
                              room: {
                                id: 'garage_1',
                                name: 'Garage 1',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T16:37:39.231967+00:00',
                              endTime: '2024-10-29T17:35:44.642644+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: 'ec9c5465-3aa7-4e58-9dcb-ae9ca068cc81',
                              scheduledStartTime:
                                '2024-10-29T16:43:13.884672+00:00',
                              scheduledEndTime:
                                '2024-10-29T17:53:13.884672+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_ffcb40d4-72b1-4999-b76e-afa343c1724c',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '6bd4f8ed-ffa6-4117-b625-9041bac6d959',
                                    firstName: 'Henry',
                                    lastName: 'House',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:a5dde9c0-b9ec-4e13-bcdf-da6cf37e3a06',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T18:17:12.980195+00:00',
                            endTime: '2024-10-29T19:14:11.658930+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T19:14:11.658930+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '9ed149d0-93d1-4ab3-bb0f-42ea3b8a0447',
                              room: {
                                id: 'garage_1',
                                name: 'Garage 1',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T18:17:12.980195+00:00',
                              endTime: '2024-10-29T19:14:11.658930+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: 'a5dde9c0-b9ec-4e13-bcdf-da6cf37e3a06',
                              scheduledStartTime:
                                '2024-10-29T18:10:31.324935+00:00',
                              scheduledEndTime:
                                '2024-10-29T19:30:31.324935+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [
                                {
                                  sentTime: '2024-10-29T18:12:16.165814+00:00',
                                  event: {
                                    id: '94a65ea0-cf01-477c-9f48-dc9d6ddae9c8',
                                    name: 'patient_wheels_in',
                                    color: '#f36f66',
                                    attrs: {
                                      name: 'Patient wheels in',
                                      __typename: 'EventType' as const,
                                    },
                                    __typename: 'Event' as const,
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'Selam',
                                      lastName: 'Moges',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation' as const,
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation' as const,
                                  },
                                  __typename: 'EventNotification' as const,
                                },
                                {
                                  sentTime: '2024-10-29T18:33:16.228176+00:00',
                                  event: {
                                    id: '4711eae6-75ba-4f44-9fb0-ad36e91ccebf',
                                    name: 'patient_draped',
                                    color: '#72c8c8',
                                    attrs: {
                                      name: 'Patient draped (Procedural)',
                                      __typename: 'EventType' as const,
                                    },
                                    __typename: 'Event' as const,
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'Selam',
                                      lastName: 'Moges',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation' as const,
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation' as const,
                                  },
                                  __typename: 'EventNotification' as const,
                                },
                                {
                                  sentTime: '2024-10-29T18:46:05.162010+00:00',
                                  event: {
                                    id: '4a921159-0975-4986-93f5-a77da5417c2c',
                                    name: 'patient_draped',
                                    color: '#72c8c8',
                                    attrs: {
                                      name: 'Patient draped (Procedural)',
                                      __typename: 'EventType' as const,
                                    },
                                    __typename: 'Event' as const,
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'Selam',
                                      lastName: 'Moges',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation' as const,
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation' as const,
                                  },
                                  __typename: 'EventNotification' as const,
                                },
                                {
                                  sentTime: '2024-10-29T18:56:14.598378+00:00',
                                  event: {
                                    id: '779374ed-b411-46ad-9b6f-8e3e39f2e3d5',
                                    name: 'patient_undraped',
                                    color: '#108383',
                                    attrs: {
                                      name: 'Patient undraped (Procedural)',
                                      __typename: 'EventType' as const,
                                    },
                                    __typename: 'Event' as const,
                                  },
                                  observation: null,
                                  staffEventContactInformation: {
                                    contactInformation: {
                                      firstName: 'Selam',
                                      lastName: 'Moges',
                                      isApellaEmployee: true,
                                      __typename: 'ContactInformation' as const,
                                    },
                                    __typename:
                                      'StaffEventNotificationContactInformation' as const,
                                  },
                                  __typename: 'EventNotification' as const,
                                },
                              ],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_65ce6761-cc6e-4856-ae60-6211d06ee738',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'facf1cac-4807-44bb-afbf-b562fdb1363d',
                                    firstName: 'Miranda',
                                    lastName: 'Bailey',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: '4513ffd8-6fdc-4408-9bc1-a3a52184bd84',
                                    name: 'I & D Abdomen',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:ef6b701d-ea5c-4ee0-96b3-3cd2cdaa243e',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T19:41:16.253353+00:00',
                            endTime: '2024-10-29T20:48:39.324388+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T20:48:39.324388+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: 'bf6ca2d2-f5ea-409e-8e1a-f3e89f7e5ee9',
                              room: {
                                id: 'garage_1',
                                name: 'Garage 1',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T19:41:16.253353+00:00',
                              endTime: '2024-10-29T20:48:39.324388+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: 'ef6b701d-ea5c-4ee0-96b3-3cd2cdaa243e',
                              scheduledStartTime:
                                '2024-10-29T19:44:37.240557+00:00',
                              scheduledEndTime:
                                '2024-10-29T21:20:37.240557+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_72a3ca03-66e9-40ff-a4e1-315d35c2c2b3',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'f8c835e2-72c0-4f35-be14-aa1962371787',
                                    firstName: 'John',
                                    lastName: 'House',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: '12c6a767-6706-4abf-a244-441d3f475c31',
                                    name: 'Colonoscopy',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                                {
                                  procedure: {
                                    id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                                    name: 'Angiogram',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:e1afba78-1fb1-470b-bec0-9fb2c9f49372',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T21:44:43.311017+00:00',
                            endTime: '2024-10-29T22:38:13.806962+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T22:38:13.806962+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '35ce3dcc-0a24-450f-b681-370d57127fd9',
                              room: {
                                id: 'garage_1',
                                name: 'Garage 1',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T21:44:43.311017+00:00',
                              endTime: '2024-10-29T22:38:13.806962+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: 'e1afba78-1fb1-470b-bec0-9fb2c9f49372',
                              scheduledStartTime:
                                '2024-10-29T21:38:10.001447+00:00',
                              scheduledEndTime:
                                '2024-10-29T22:52:10.001447+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_0981e4a8-7d29-4780-9f9d-50f94073d9cc',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '63ece80a-d0c6-4b1e-bc96-dc81c16c7100',
                                    firstName: 'Henry',
                                    lastName: 'Bailey',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                            type: CaseType.LIVE,
                            startTime: '2024-10-29T23:13:22.244854+00:00',
                            endTime: '2024-10-29T23:40:33.044854+00:00',
                            status: {
                              name: CaseStatusName.PREP,
                              since: '2024-10-29T23:20:12.260449+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '0fef9148-ab6c-4be4-a208-f6871884a7bd',
                              room: {
                                id: 'garage_1',
                                name: 'Garage 1',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T23:13:22.244854+00:00',
                              endTime: null,
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: 'b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                              scheduledStartTime:
                                '2024-10-29T23:08:59.691216+00:00',
                              scheduledEndTime:
                                '2024-10-30T00:28:59.691216+00:00',
                              isFirstCase: null,
                              isInFlipRoom: null,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_7fb1e34a-6243-4fca-836f-7a5c91be047d',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'd163eed7-2be0-4958-86be-2f5d50814dfc',
                                    firstName: 'Henry',
                                    lastName: 'Dorian',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                                    name: 'Angiogram',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                      ],
                      __typename: 'ApellaCaseConnection' as const,
                    },
                    __typename: 'Room' as const,
                  },
                  __typename: 'RoomEdge' as const,
                },
                {
                  node: {
                    sortKey: null,
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-29T23:09:51.101694+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      __typename: 'RoomStatus' as const,
                    },
                    id: 'garage_2',
                    name: 'Garage 2',
                    primeTimeConfig: {
                      id: '9e11890b-0686-464e-9a55-33082972302b',
                      sunday: {
                        startTime: '13:00:00',
                        endTime: '15:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      monday: {
                        startTime: '08:30:00',
                        endTime: '17:30:00',
                        __typename: 'TimeRange' as const,
                      },
                      tuesday: {
                        startTime: '09:30:00',
                        endTime: '17:30:00',
                        __typename: 'TimeRange' as const,
                      },
                      wednesday: {
                        startTime: '08:30:00',
                        endTime: '17:30:00',
                        __typename: 'TimeRange' as const,
                      },
                      thursday: null,
                      friday: null,
                      saturday: null,
                      __typename: 'RoomPrimeTimeConfig' as const,
                    },
                    turnovers: [
                      {
                        id: 'turnover-phase:06e06d1d-e5fc-49a0-8874-a6fb23b05b85-phase:5f51a298-aa9c-4667-8d89-ce603c78b77a',
                        startTime: '2024-10-29T16:05:49.674468+00:00',
                        endTime: '2024-10-29T16:48:20.898668+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'phase:5f51a298-aa9c-4667-8d89-ce603c78b77a',
                          case: null,
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'phase:06e06d1d-e5fc-49a0-8874-a6fb23b05b85',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-phase:5f51a298-aa9c-4667-8d89-ce603c78b77a-phase:20cfd3c8-a92e-4041-b767-514fe84a7313',
                        startTime: '2024-10-29T17:20:10.213582+00:00',
                        endTime: '2024-10-29T17:43:31.026232+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'phase:20cfd3c8-a92e-4041-b767-514fe84a7313',
                          case: null,
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'phase:5f51a298-aa9c-4667-8d89-ce603c78b77a',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-phase:20cfd3c8-a92e-4041-b767-514fe84a7313-phase:7770beea-bb01-4401-8027-9e610bd35c9c',
                        startTime: '2024-10-29T18:12:44.475597+00:00',
                        endTime: '2024-10-29T18:47:18.441643+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'phase:7770beea-bb01-4401-8027-9e610bd35c9c',
                          case: null,
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'phase:20cfd3c8-a92e-4041-b767-514fe84a7313',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-phase:7770beea-bb01-4401-8027-9e610bd35c9c-phase:c0b58610-f15e-4577-b2bd-c2ddc768bd18',
                        startTime: '2024-10-29T19:42:38.296277+00:00',
                        endTime: '2024-10-29T19:59:35.827505+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'phase:c0b58610-f15e-4577-b2bd-c2ddc768bd18',
                          case: null,
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'phase:7770beea-bb01-4401-8027-9e610bd35c9c',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                      {
                        id: 'turnover-phase:c0b58610-f15e-4577-b2bd-c2ddc768bd18-phase:b269fec2-d7be-40a5-b798-7303c10fd8df',
                        startTime: '2024-10-29T21:10:39.234500+00:00',
                        endTime: '2024-10-29T21:46:23.483370+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'phase:b269fec2-d7be-40a5-b798-7303c10fd8df',
                          case: null,
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'phase:c0b58610-f15e-4577-b2bd-c2ddc768bd18',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                    ],
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'phase:06e06d1d-e5fc-49a0-8874-a6fb23b05b85',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T15:17:11.610107+00:00',
                            endTime: '2024-10-29T16:05:49.674468+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T16:05:49.674468+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '06e06d1d-e5fc-49a0-8874-a6fb23b05b85',
                              room: {
                                id: 'garage_2',
                                name: 'Garage 2',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T15:17:11.610107+00:00',
                              endTime: '2024-10-29T16:05:49.674468+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: null,
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'phase:5f51a298-aa9c-4667-8d89-ce603c78b77a',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T16:48:20.898668+00:00',
                            endTime: '2024-10-29T17:20:10.213582+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T17:20:10.213582+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '5f51a298-aa9c-4667-8d89-ce603c78b77a',
                              room: {
                                id: 'garage_2',
                                name: 'Garage 2',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T16:48:20.898668+00:00',
                              endTime: '2024-10-29T17:20:10.213582+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: null,
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'phase:20cfd3c8-a92e-4041-b767-514fe84a7313',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T17:43:31.026232+00:00',
                            endTime: '2024-10-29T18:12:44.475597+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T18:12:44.475597+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '20cfd3c8-a92e-4041-b767-514fe84a7313',
                              room: {
                                id: 'garage_2',
                                name: 'Garage 2',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T17:43:31.026232+00:00',
                              endTime: '2024-10-29T18:12:44.475597+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: null,
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'phase:7770beea-bb01-4401-8027-9e610bd35c9c',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T18:47:18.441643+00:00',
                            endTime: '2024-10-29T19:42:38.296277+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T19:42:38.296277+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '7770beea-bb01-4401-8027-9e610bd35c9c',
                              room: {
                                id: 'garage_2',
                                name: 'Garage 2',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T18:47:18.441643+00:00',
                              endTime: '2024-10-29T19:42:38.296277+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: null,
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'phase:c0b58610-f15e-4577-b2bd-c2ddc768bd18',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T19:59:35.827505+00:00',
                            endTime: '2024-10-29T21:10:39.234500+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T21:10:39.234500+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: 'c0b58610-f15e-4577-b2bd-c2ddc768bd18',
                              room: {
                                id: 'garage_2',
                                name: 'Garage 2',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T19:59:35.827505+00:00',
                              endTime: '2024-10-29T21:10:39.234500+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: null,
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'phase:b269fec2-d7be-40a5-b798-7303c10fd8df',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T21:46:23.483370+00:00',
                            endTime: '2024-10-29T23:09:51.101694+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T23:09:51.101694+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: 'b269fec2-d7be-40a5-b798-7303c10fd8df',
                              room: {
                                id: 'garage_2',
                                name: 'Garage 2',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T21:46:23.483370+00:00',
                              endTime: '2024-10-29T23:09:51.101694+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: null,
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                      ],
                      __typename: 'ApellaCaseConnection' as const,
                    },
                    __typename: 'Room' as const,
                  },
                  __typename: 'RoomEdge' as const,
                },
              ],
              __typename: 'RoomConnection' as const,
            },
            __typename: 'Site' as const,
          },
          __typename: 'SiteEdge' as const,
        },
      ],
      __typename: 'SiteConnection' as const,
    },
  },
}

export const ScheduleTimelineCaseDataOverFlow = {
  data: {
    sites: {
      edges: [
        {
          node: {
            id: 'lab_1',
            name: 'Apella Lab',
            turnoverGoals: {
              goalMinutes: 35,
              maxMinutes: 80,
              __typename: 'TurnoverGoals' as const,
            },
            rooms: {
              edges: [
                {
                  node: {
                    sortKey: null,
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-29T23:13:22.244854+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                        startTime: '2024-10-29T23:13:22.244854+00:00',
                        endTime: '2024-10-29T23:40:33.044854+00:00',
                        status: {
                          name: CaseStatusName.PREP,
                          since: '2024-10-29T23:20:12.260449+00:00',
                          __typename: 'ApellaCaseStatus' as const,
                        },
                        __typename: 'ApellaCase' as const,
                      },
                      __typename: 'RoomStatus' as const,
                    },
                    id: 'garage_1',
                    name: 'Garage 1',
                    primeTimeConfig: {
                      id: '85e8e709-082f-4970-bc23-656bd24036d6',
                      sunday: null,
                      monday: {
                        startTime: '09:00:00',
                        endTime: '17:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      tuesday: {
                        startTime: '09:00:00',
                        endTime: '15:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      wednesday: {
                        startTime: '09:00:00',
                        endTime: '15:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      thursday: {
                        startTime: '09:00:00',
                        endTime: '15:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      friday: {
                        startTime: '09:00:00',
                        endTime: '15:00:00',
                        __typename: 'TimeRange' as const,
                      },
                      saturday: null,
                      __typename: 'RoomPrimeTimeConfig' as const,
                    },
                    turnovers: [
                      {
                        id: 'turnover-case:3344d864-40fc-4adc-aec7-a15c8f466bce-case:ec9c5465-3aa7-4e58-9dcb-ae9ca068cc81',
                        startTime: '2024-10-29T16:15:34.894109+00:00',
                        endTime: '2024-10-29T16:37:39.231967+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:ec9c5465-3aa7-4e58-9dcb-ae9ca068cc81',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T16:43:13.884672+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:3344d864-40fc-4adc-aec7-a15c8f466bce',
                          __typename: 'ApellaCase' as const,
                        },
                        __typename: 'Turnover' as const,
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                      },
                      {
                        id: 'turnover-case:e1afba78-1fb1-470b-bec0-9fb2c9f49372-case:b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                        startTime: '2024-10-29T22:38:13.806962+00:00',
                        endTime: '2024-10-29T23:13:22.244854+00:00',
                        type: TurnoverType.COMPLETE,
                        followingCase: {
                          id: 'case:b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                          case: {
                            scheduledStartTime:
                              '2024-10-29T23:08:59.691216+00:00',
                            __typename: 'ScheduledCase' as const,
                          },
                          __typename: 'ApellaCase' as const,
                        },
                        precedingCase: {
                          id: 'case:e1afba78-1fb1-470b-bec0-9fb2c9f49372',
                          __typename: 'ApellaCase' as const,
                        },
                        note: 'This is a note',
                        labels: [{ id: 'labelId:1' }] as TurnoverLabel[] | null,
                        __typename: 'Turnover' as const,
                      },
                    ],
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:3344d864-40fc-4adc-aec7-a15c8f466bce',
                            type: CaseType.COMPLETE,
                            startTime: '2024-10-29T06:06:59.309817+00:00',
                            endTime: '2024-10-29T16:15:34.894109+00:00',
                            status: {
                              name: CaseStatusName.ACTUAL,
                              since: '2024-10-29T16:15:34.894109+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: 'aa8ecdff-ed24-499d-a486-3291d943d76c',
                              room: {
                                id: 'garage_1',
                                name: 'Garage 1',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T06:06:59.309817+00:00',
                              endTime: '2024-10-29T16:15:34.894109+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: '3344d864-40fc-4adc-aec7-a15c8f466bce',
                              scheduledStartTime:
                                '2024-10-29T06:07:00.878675+00:00',
                              scheduledEndTime:
                                '2024-10-29T16:31:00.878675+00:00',
                              isFirstCase: true,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_a6f58893-6d12-453a-a05f-89f4dcaad88d',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'a7d26be7-92ad-41bc-927e-f95fd9a3af96',
                                    firstName: 'Gregory',
                                    lastName: 'Wu',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:e1afba78-1fb1-470b-bec0-9fb2c9f49372',
                            type: CaseType.LIVE,
                            startTime: '2024-10-29T21:44:43.311017+00:00',
                            endTime: '2024-10-29T22:38:13.806962+00:00',
                            status: {
                              name: CaseStatusName.PREP,
                              since: '2024-10-29T22:38:13.806962+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '35ce3dcc-0a24-450f-b681-370d57127fd9',
                              room: {
                                id: 'garage_1',
                                name: 'Garage 1',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T21:44:43.311017+00:00',
                              endTime: '2024-10-29T22:38:13.806962+00:00',
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: 'e1afba78-1fb1-470b-bec0-9fb2c9f49372',
                              scheduledStartTime:
                                '2024-10-29T21:38:10.001447+00:00',
                              scheduledEndTime:
                                '2024-10-29T22:52:10.001447+00:00',
                              isFirstCase: false,
                              isInFlipRoom: false,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_0981e4a8-7d29-4780-9f9d-50f94073d9cc',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: '63ece80a-d0c6-4b1e-bc96-dc81c16c7100',
                                    firstName: 'Henry',
                                    lastName: 'Bailey',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'aad1a037-7372-44b7-acf7-037b1c914ddd',
                                    name: 'Shoulder Arthroscopy w/Rotator Cuff Repair',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                        {
                          node: {
                            id: 'case:b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                            type: CaseType.FORECAST,
                            startTime: '2024-10-29T23:13:22.244854+00:00',
                            endTime: '2024-10-30T07:40:33.044854+00:00',
                            status: {
                              name: CaseStatusName.SCHEDULED,
                              since: '2024-10-29T23:20:12.260449+00:00',
                              __typename: 'ApellaCaseStatus' as const,
                            },
                            actual: {
                              id: '0fef9148-ab6c-4be4-a208-f6871884a7bd',
                              room: {
                                id: 'garage_1',
                                name: 'Garage 1',
                                __typename: 'Room' as const,
                              },
                              startTime: '2024-10-29T23:13:22.244854+00:00',
                              endTime: null,
                              __typename: 'Phase' as const,
                            },
                            case: {
                              caseLabels: [],
                              notePlan: null,
                              id: 'b4b4e35e-ecdb-4e8d-a4eb-5d2e7c25ca89',
                              scheduledStartTime:
                                '2024-10-29T23:08:59.691216+00:00',
                              scheduledEndTime:
                                '2024-10-30T07:28:59.691216+00:00',
                              isFirstCase: null,
                              isInFlipRoom: null,
                              isAddOn: null,
                              patientClass: null,
                              eventNotifications: [],
                              serviceLine: null,
                              externalCaseId:
                                'load-generator_7fb1e34a-6243-4fca-836f-7a5c91be047d',
                              precedingCase: null,
                              caseMatchingStatus: CaseMatchingStatus.AUTOMATIC,
                              caseClassificationType: null,
                              caseFlags: [],
                              caseStaff: [
                                {
                                  role: 'Primary Surgeon',
                                  staff: {
                                    id: 'd163eed7-2be0-4958-86be-2f5d50814dfc',
                                    firstName: 'Henry',
                                    lastName: 'Dorian',
                                    __typename: 'Staff' as const,
                                  },
                                  __typename: 'CaseStaff' as const,
                                },
                              ],
                              patient: null,
                              caseStaffPlan: {
                                edges: [],
                                __typename: 'CaseStaffPlanConnection' as const,
                              },
                              primaryCaseProcedures: [
                                {
                                  procedure: {
                                    id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                                    name: 'Angiogram',
                                    __typename: 'Procedure' as const,
                                  },
                                  __typename: 'CaseProcedure' as const,
                                },
                              ],
                              __typename: 'ScheduledCase' as const,
                            },
                            __typename: 'ApellaCase' as const,
                          },
                          __typename: 'ApellaCaseEdge' as const,
                        },
                      ],
                      __typename: 'ApellaCaseConnection' as const,
                    },
                    __typename: 'Room' as const,
                  },
                  __typename: 'RoomEdge' as const,
                },
              ],
              __typename: 'RoomConnection' as const,
            },
            __typename: 'Site' as const,
          },
          __typename: 'SiteEdge' as const,
        },
      ],
      __typename: 'SiteConnection' as const,
    },
  },
}

export const BlockTimesData = {
  data: {
    blockTimes: {
      edges: [
        {
          node: {
            id: '31410b3a-33b8-49fa-a744-f3275ad48218',
            blockId: 'f99ab569-8f02-4d36-9333-1b19a05999ae',
            startTime: '2024-10-29T14:30:00+00:00',
            endTime: '2024-10-30T00:00:00+00:00',
            roomId: 'garage_1',
            releasedFrom: null,
            createdTime: '2024-10-27T14:30:00+00:00',
            room: {
              id: 'garage_1',
              name: 'Garage 1',
              __typename: 'Room' as const,
            },
            block: {
              id: 'f99ab569-8f02-4d36-9333-1b19a05999ae',
              name: "Luke's Block2",
              color: '#72c8c8',
              surgeonIds: [],
              archivedTime: null,
              __typename: 'Block' as const,
            },
            releases: [],
            __typename: 'BlockTime' as const,
          },
          __typename: 'BlockTimeEdge' as const,
        },
      ],
      __typename: 'BlockTimeConnection' as const,
    },
  },
}

export const BlockUtilizationData: { data: GetBlockUtilizationForSite } = {
  data: {
    blockUtilizations: [
      {
        availableSeconds: 30 * 60,
        utilizedScheduledSeconds: 45 * 60,
        utilizedSeconds: 20 * 60,
        casesForBlockDay: [
          {
            caseId: '1',
            __typename: 'CaseToBlock',
          },
        ],
        blockId: 'f99ab569-8f02-4d36-9333-1b19a05999ae',
        __typename: 'BlockUtilization' as const,
      },
    ],
  },
}

export const GetSiteOptionsFilterDataSchedule: { data: GetSiteOptionsFilter } =
  {
    data: {
      sites: {
        edges: [
          {
            node: {
              id: 'lab_1',
              name: 'Apella Lab',
              timezone: 'America/Los_Angeles',
              rooms: {
                edges: [
                  {
                    node: {
                      id: 'AT-DEV-OR1',
                      site: {
                        id: 'lab_1',
                        name: 'Apella Lab',
                        __typename: 'Site',
                      },
                      __typename: 'Room',
                      name: 'OR1',
                      tags: [],
                    },
                    __typename: 'RoomEdge',
                  },
                  {
                    node: {
                      id: 'garage_0',
                      site: {
                        id: 'lab_1',
                        name: 'Apella Lab',
                        __typename: 'Site',
                      },
                      name: 'Garage 0-test',
                      __typename: 'Room',
                      tags: [],
                    },
                    __typename: 'RoomEdge',
                  },
                  {
                    node: {
                      id: 'garage_1',
                      site: {
                        id: 'lab_1',
                        name: 'Apella Lab',
                        __typename: 'Site',
                      },
                      name: 'Garage 1',
                      __typename: 'Room',
                      tags: [],
                    },
                    __typename: 'RoomEdge',
                  },
                  {
                    node: {
                      id: 'garage_2',
                      site: {
                        id: 'lab_1',
                        name: 'Apella Lab',
                        __typename: 'Site',
                      },
                      name: 'Garage 2',
                      __typename: 'Room',
                      tags: [],
                    },
                    __typename: 'RoomEdge',
                  },
                ],
                __typename: 'RoomConnection',
              },
              __typename: 'Site',
            },
            __typename: 'SiteEdge',
          },
          {
            node: {
              id: 'palo_alto_1',
              name: 'Apella Palo Alto',
              timezone: 'America/Los_Angeles',
              rooms: {
                edges: [
                  {
                    node: {
                      id: 'palo_alto_room_0',
                      site: {
                        id: 'palo_alto_1',
                        name: 'Apella Palo Alto',
                        __typename: 'Site',
                      },
                      name: 'OR 1',
                      __typename: 'Room',
                      tags: [],
                    },
                    __typename: 'RoomEdge',
                  },
                  {
                    node: {
                      id: 'palo_alto_room_1',
                      site: {
                        id: 'palo_alto_1',
                        name: 'Apella Palo Alto',
                        __typename: 'Site',
                      },
                      name: 'OR 2',
                      __typename: 'Room',
                      tags: [],
                    },
                    __typename: 'RoomEdge',
                  },
                ],
                __typename: 'RoomConnection',
              },
              __typename: 'Site',
            },
            __typename: 'SiteEdge',
          },
          {
            node: {
              id: 'palo_alto_1_DISABLED',
              name: 'Apella Palo Alto (Disabled Cameras)',
              timezone: 'America/Los_Angeles',
              rooms: {
                edges: [
                  {
                    node: {
                      id: 'palo_alto_room_0_DISABLED',
                      site: {
                        id: 'palo_alto_1_DISABLED',
                        name: 'Apella Palo Alto (Disabled Cameras)',
                        __typename: 'Site',
                      },
                      name: 'OR 1 (Disabled)',
                      __typename: 'Room',
                      tags: [],
                    },
                    __typename: 'RoomEdge',
                  },
                  {
                    node: {
                      id: 'palo_alto_room_1_DISABLED',
                      site: {
                        id: 'palo_alto_1_DISABLED',
                        name: 'Apella Palo Alto (Disabled Cameras)',
                        __typename: 'Site',
                      },
                      name: 'OR 2 (Disabled)',
                      __typename: 'Room',
                      tags: [],
                    },
                    __typename: 'RoomEdge',
                  },
                ],
                __typename: 'RoomConnection',
              },
              __typename: 'Site',
            },
            __typename: 'SiteEdge',
          },
        ],
        __typename: 'SiteConnection',
      },
    },
  }

export const GetLiveFromSchedulePermission = {
  data: {
    me: {
      uiPermissions: {
        dashboardLiveFromScheduleEnabled: true,
        __typename: 'UserUiPermissions' as const,
      },
      __typename: 'User' as const,
    },
  },
}
