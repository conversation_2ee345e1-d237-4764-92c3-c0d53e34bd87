import { <PERSON><PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import { Meta, StoryObj } from '@storybook/react'
import { graphql, http, HttpResponse, delay } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'
import { GetSiteOptionsFilterDocument } from 'src/modules/site/__generated__'
import { GetCurrentUserDocument } from 'src/modules/user/__generated__'
import {
  GetCurrentUserData,
  UserType,
} from 'src/pages/test_mocks/GetCurrentUserData'
import { GetTurnoverDataDocument } from 'src/pages/Turnovers/__generated__'
import { MockTurnoverData } from 'src/pages/Turnovers/mock/GetTurnoverData'
import { ApellaRouteContext } from 'src/router/types'
import MockLDProvider from 'src/test/MockLDProvider'
import { modes } from 'src/test/storybookHelpers'

import {
  GetBlockTimeDataDocument,
  GetBlockUtilizationForSiteDocument,
  GetLiveFromScheduleEnabledDocument,
  GetTimelineCaseDataDocument,
} from '../__generated__'
import { Schedule, loader as scheduleLoader } from '../Schedule'
import {
  BlockTimesData,
  BlockUtilizationData,
  GetLiveFromSchedulePermission,
  GetSiteOptionsFilterDataSchedule,
  ScheduleTimelineCaseData,
  ScheduleTimelineCaseDataOverFlow,
} from './ScheduleMockData'

const baseLdParams = {}

const mockDataDependencies = (): Partial<ApellaRouteContext> => ({
  apolloClient: new ApolloClient({
    uri: 'https://your-graphql-endpoint',
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
      query: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
    },
  }),
  user: {
    userId: '',
    email: '',
    isApellaEmployee: false,
    userOrganizations: [],
    currentOrganization: {
      node: {
        id: 'home_hospital',
        auth0OrgId: 'org_123456',
        name: 'Home Hospital',
        sites: {
          edges: [
            {
              node: {
                id: 'lab_1',
                name: 'Apella Lab',
                timezone: 'America/Chicago',
                rooms: {
                  edges: [
                    {
                      node: {
                        id: 'AT-DEV-OR1',
                        name: 'OR1',
                        __typename: 'Room',
                      },
                      __typename: 'RoomEdge',
                    },
                    {
                      node: {
                        id: 'garage_0',
                        name: 'Garage 0-test',
                        __typename: 'Room',
                      },
                      __typename: 'RoomEdge',
                    },
                    {
                      node: {
                        id: 'garage_1',
                        name: 'Garage 1',
                        __typename: 'Room',
                      },
                      __typename: 'RoomEdge',
                    },
                    {
                      node: {
                        id: 'garage_2',
                        name: 'Garage 2',
                        __typename: 'Room',
                      },
                      __typename: 'RoomEdge',
                    },
                  ],
                  __typename: 'RoomConnection',
                },
                __typename: 'Site',
              },
              __typename: 'SiteEdge',
            },
          ],
          __typename: 'SiteConnection',
        },
        __typename: 'Organization',
      },
      __typename: 'OrganizationEdge',
    },
  },
})

const meta: Meta<typeof Schedule> = {
  title: 'Pages/Schedule',
  component: Schedule,
  decorators: [
    (Story, { parameters }) => (
      <TimezoneProvider>
        <MockLDProvider flags={parameters.ldFlags}>
          <ApolloProvider client={parameters.reactRouter.context.apolloClient}>
            <Story />
          </ApolloProvider>
        </MockLDProvider>
      </TimezoneProvider>
    ),
    withRouter,
  ],
}

export default meta
type Story = StoryObj<typeof Schedule>
const routing = {
  path: '/schedule',
  handle: 'Schedule',
  loader: scheduleLoader,
}

const msw = (
  wait?: number,
  {
    GetTimelineCaseData = ScheduleTimelineCaseData,
    userType = UserType.ALL_ACCESS,
  } = {}
) => ({
  handlers: [
    http.get('https://events.launchdarkly.com/events/diagnostic/', () => {
      return HttpResponse.json({})
    }),
    http.get('https://app.launchdarkly.com/sdk/goals/', () => {
      return HttpResponse.json({})
    }),
    graphql.query(GetCurrentUserDocument, () => {
      return HttpResponse.json(GetCurrentUserData(userType))
    }),
    graphql.query(GetTimelineCaseDataDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(GetTimelineCaseData)
    }),
    graphql.query(GetBlockTimeDataDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(BlockTimesData)
    }),
    graphql.query(GetTurnoverDataDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(MockTurnoverData)
    }),
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(GetSiteOptionsFilterDataSchedule)
    }),
    graphql.query(GetLiveFromScheduleEnabledDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(GetLiveFromSchedulePermission)
    }),
    graphql.query(GetBlockUtilizationForSiteDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(BlockUtilizationData)
    }),
  ],
})

const baseParameters = {
  chromatic: {
    modes: {
      mobile: modes.mobile,
      xLarge: modes.xLarge,
    },
    delay: 4000,
  },
}

export const SchedulePage: Story = {
  parameters: {
    ...baseParameters,
    ldFlags: {
      ...baseLdParams,
      blockTimeOnSchedulePageEnabled: true,
    },
    date: new Date('2024-10-29T16:50:00-07:00'),
    msw: msw(0, { userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule',
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const SchedulePageOverflowCases: Story = {
  parameters: {
    ...baseParameters,
    ldFlags: {
      ...baseLdParams,
      blockTimeOnSchedulePageEnabled: true,
    },
    date: new Date('2024-10-29T15:09:00-07:00'),
    msw: msw(0, {
      userType: UserType.ALL_ACCESS,
      GetTimelineCaseData: ScheduleTimelineCaseDataOverFlow,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule',
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const SchedulePageNoBlockTimesEnabled: Story = {
  parameters: {
    ...baseParameters,
    ldFlags: {
      ...baseLdParams,
      blockTimeOnSchedulePageEnabled: false,
    },
    date: new Date('2024-10-29T16:50:00-07:00'),
    msw: msw(0, { userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule',
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}
