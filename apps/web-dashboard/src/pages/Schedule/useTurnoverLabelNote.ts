import { useCallback } from 'react'

import { gql, useMutation } from '@apollo/client'

import {
  UpsertTurnoverLabelNote,
  UpsertTurnoverLabelNoteVariables,
} from './__generated__'

const TURNOVER_LABEL_NOTE_FRAGMENT = gql`
  fragment TurnoverLabelNoteFragment on Turnover {
    id
    labels {
      id
    }
    note
  }
`

const UPSERT_TURNOVER_LABELS_NOTE = gql`
  mutation UpsertTurnoverLabelNote($input: TurnoverLabelNoteUpsertInput!) {
    turnoverLabelNoteUpsert(input: $input) {
      success
      id
      labelIds
      note
    }
  }
`

export const useTurnoverUpdate = (
  onMutationCompleted: (id: string) => void
) => {
  const [upsertTurnoverLabelsNote, { called: mutationStarted }] = useMutation<
    UpsertTurnoverLabelNote,
    UpsertTurnoverLabelNoteVariables
  >(UPSERT_TURNOVER_LABELS_NOTE, {
    update: (cache, { data }) => {
      if (!data) return

      const updatedTurnoverLabelNote = data.turnoverLabelNoteUpsert
      if (!updatedTurnoverLabelNote) return

      // Write the updated data back to the cache
      cache.writeFragment({
        id: `Turnover:${updatedTurnoverLabelNote.id}`, // Cache ID for the specific turnover
        fragment: TURNOVER_LABEL_NOTE_FRAGMENT,
        data: {
          id: updatedTurnoverLabelNote.id,
          labels: updatedTurnoverLabelNote.labelIds?.map((labelId) => ({
            id: labelId,
          })),
          note: updatedTurnoverLabelNote.note,
        },
      })

      onMutationCompleted(updatedTurnoverLabelNote.id)
    },
  })

  const updateTurnoverLabelsAndNote = useCallback(
    ({
      id,
      note,
      labelIds,
    }: {
      id: string
      note: string | null
      labelIds: string[] | null
    }) => {
      upsertTurnoverLabelsNote({
        variables: {
          input: {
            id,
            note,
            labelIds,
          },
        },
      })
    },
    [upsertTurnoverLabelsNote]
  )

  return { updateTurnoverLabelsAndNote, mutationStarted }
}
