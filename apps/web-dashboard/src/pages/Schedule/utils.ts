import { DateTime } from 'luxon'

import { ApellaDateTimeFormats } from '@apella/component-library'

import { Scalars } from '../../__generated__/globalTypes'

export const formatHourMinute = (
  time: Scalars['DateTime']['output'],
  timezone: string
) =>
  DateTime.fromISO(time)
    .setZone(timezone)
    .toLocaleString(ApellaDateTimeFormats.TIME)

export const getStartToEndTimeString = (
  startTime: Scalars['DateTime']['output'],
  endTime: Scalars['DateTime']['output'],
  timezone: string
) =>
  `${formatHourMinute(startTime, timezone)} - ${formatHourMinute(
    endTime,
    timezone
  )}`
