import { renderHook } from '@testing-library/react'
import { DateTime } from 'luxon'

import { useTimezone } from '../../Contexts'
import { mockApellaCase, mockCase } from '../../test/testMocks'
import { useTimelineHourBounds } from './useTimelineHourBounds'

describe('useTimelineHourBounds', () => {
  const { result: timezoneResult } = renderHook(() => useTimezone())
  const minTime = DateTime.fromObject(
    {
      year: 2022,
      month: 10,
      day: 1,
      hour: 5,
      minute: 5,
    },
    { zone: timezoneResult.current.timezone }
  )
  const maxTime = minTime.plus({ hours: 5 })

  const cases = [
    mockApellaCase(
      '1',
      minTime.plus({ minute: 1 }),
      minTime.plus({ minute: 3 }),
      mockCase(
        '1',
        minTime.plus({ hour: 1 }),
        minTime.plus({ hour: 1, minute: 3 })
      )
    ),
  ]

  describe('`normal` method', () => {
    it('has a lower hour bound based on the phases and cases passed in', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases,
        })
      )

      const minHourDt = result.current.minDateTime

      expect(minHourDt.toUTC()).toEqual(minTime.startOf('hour').toUTC())
    })

    it('has an upper hour bound based on the phases and cases passed in', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases,
        })
      )

      const maxHourDt = result.current.maxDateTime

      expect(maxHourDt.toUTC()).toEqual(
        minTime.plus({ hour: 2 }).startOf('hour').toUTC()
      )
    })
  })

  describe('`fullTimeBounded` method', () => {
    it('uses the minTime as a lower bound', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases,
          calculationMethod: 'fullTimeBounded',
        })
      )

      const minHourDt = result.current.minDateTime

      expect(minHourDt.toUTC()).toEqual(minTime.startOf('hour').toUTC())
    })

    it('uses the maxTime as an upper bound', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases,
          calculationMethod: 'fullTimeBounded',
        })
      )

      const maxHourDt = result.current.maxDateTime

      expect(maxHourDt.toUTC()).toEqual(maxTime.startOf('hour').toUTC())
    })
  })

  describe('`fullTime` method', () => {
    it('uses the minTime as a lower bound', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases,
          calculationMethod: 'fullTime',
        })
      )

      const minHourDt = result.current.minDateTime

      expect(minHourDt.toUTC()).toEqual(minTime.toUTC())
    })

    it('uses the maxTime as an upper bound', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases,
          calculationMethod: 'fullTime',
        })
      )

      const maxHourDt = result.current.maxDateTime

      expect(maxHourDt.toUTC()).toEqual(maxTime.toUTC())
    })
  })

  describe('`dayBound` method', () => {
    it('has a lower hour bound based on the phases and cases passed in', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases,
          calculationMethod: 'dayBound',
        })
      )

      const minHourDt = result.current.minDateTime

      expect(minHourDt.toUTC()).toEqual(minTime.startOf('hour').toUTC())
    })

    it('has an upper hour bound based on the phases and cases passed in', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases,
          calculationMethod: 'dayBound',
        })
      )

      const maxHourDt = result.current.maxDateTime

      expect(maxHourDt.toUTC()).toEqual(
        minTime.plus({ hour: 2 }).startOf('hour').toUTC()
      )
    })

    const casesWithOverflow = [
      mockApellaCase(
        '1',
        minTime.minus({ day: 1, minute: 1 }),
        minTime.plus({ minute: 3 }),
        mockCase(
          '1',
          minTime.plus({ hour: 1 }),
          minTime.plus({ day: 1, hour: 1, minute: 3 })
        )
      ),
    ]

    it('has a lower hour bound of midnight of the day of minTime', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases: casesWithOverflow,
          calculationMethod: 'dayBound',
        })
      )

      const minHourDt = result.current.minDateTime

      expect(minHourDt.toUTC()).toEqual(minTime.startOf('day').toUTC())
    })

    it('has an upper hour bound based on the phases and cases passed in', () => {
      const { result } = renderHook(() =>
        useTimelineHourBounds({
          minTime: minTime.toISO(),
          maxTime: maxTime.toISO(),
          cases: casesWithOverflow,
          calculationMethod: 'dayBound',
        })
      )

      const maxHourDt = result.current.maxDateTime

      expect(maxHourDt.toUTC()).toEqual(
        maxTime.plus({ day: 1 }).startOf('day').toUTC()
      )
    })
  })
})
