import React, { <PERSON>actN<PERSON>, useMemo, useState } from 'react'
import { toast } from 'react-toastify'

import { useQuery } from '@apollo/client'
import { Field, FormikHelpers } from 'formik'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { debounce } from 'lodash'

import {
  Caps2,
  ChildrenProps,
  H5,
  InputText,
  mediaQueries,
  MultiSelect,
  Option,
  remSpacing,
  SingleSelect,
  theme,
  Tile,
} from '@apella/component-library'
import { CaseLabelIcon } from 'src/components/Timeline/CaseLabelIcon'
import { ApellaCase, Staff, StaffPlan } from 'src/pages/types'
import {
  CaseStaffPlanRole,
  PRIMARY_SURGEON_ROLES,
  toStaffFullName,
  toStaffRoleDisplayName,
} from 'src/utils/roles'

import { CaseLabelFieldType, CaseType } from '../../__generated__/globalTypes'
import { WebDashboardFeatureFlagSet } from '../../modules/feature/types'
import { CaseStaffPlanFormValues } from '../../modules/planning/components/CaseStaffPlanForm'
import { useCurrentUser } from '../../modules/user/hooks'
import { EM_DASH } from '../../utils/htmlCodes'
import { GetStaff, GetStaffVariables } from '../Notifications/__generated__'
import { GET_STAFF } from '../Notifications/queries'
import { getPatientClassText } from '../Schedule/CaseDetails'
import { GetCaseLabelsForm } from './__generated__'
import { CasePlanningFormValues } from './types'

type CaseLabelFormType = NonNullable<GetCaseLabelsForm['site']>['caseLabelForm']

export interface VideoBladeCardProps extends ChildrenProps {
  style?: React.CSSProperties
  title?: string
  titleAction?: ReactNode
}

export const VideoBladeCardRow = ({
  title,
  value,
  icon,
}: {
  title?: string
  value: ReactNode
  icon?: ReactNode
}) => {
  return (
    <div
      css={{
        display: 'grid',
        gridAutoFlow: 'column',
        gridTemplateColumns: !!title ? 'minmax(0, 1fr) minmax(0, 1fr)' : 'auto',
        padding: `${remSpacing.small} 0`,
        columnGap: remSpacing.small,
        width: '100%',
        ':last-child': {
          borderBottom: 'none',
        },
        borderBottom: `1px solid ${theme.palette.gray[20]}`,
        alignItems: 'center',
      }}
    >
      {title && (
        <Caps2 css={{ color: theme.palette.text.tertiary }}>{title}</Caps2>
      )}
      <div
        css={{
          display: 'grid',
          gridAutoFlow: 'column',
          columnGap: remSpacing.small,
          gridTemplateColumns: !!icon ? 'minmax(0, 1fr) min-content' : 'auto',
        }}
      >
        <Caps2 css={{ color: theme.palette.text.secondary }}>{value}</Caps2>
        {icon}
      </div>
    </div>
  )
}

export interface VideoBladeCardsProps {
  apellaCase?: ApellaCase
  caseLabelForm: CaseLabelFormType
  setFieldValue: FormikHelpers<CasePlanningFormValues>['setFieldValue']
  values: CasePlanningFormValues
}

export const VideoBladeCards = ({
  values,
  setFieldValue,
  apellaCase,
  caseLabelForm,
}: VideoBladeCardsProps): React.JSX.Element => {
  const { assignStaffPlanningEnabled } = useFlags<WebDashboardFeatureFlagSet>()

  if (!apellaCase) {
    return <></>
  }

  const { isAddOn, caseClassificationType, patientClass } =
    apellaCase?.case ?? {}
  const addOnText = isAddOn === undefined ? EM_DASH : isAddOn ? 'Yes' : 'No'

  const caseClassificationTypeText = !caseClassificationType
    ? EM_DASH
    : caseClassificationType.name

  const patientClassTextRaw = getPatientClassText(patientClass)

  const patientClassText = !patientClassTextRaw ? EM_DASH : patientClassTextRaw

  const isEditable = apellaCase?.type !== CaseType.COMPLETE

  return (
    <div
      css={{
        display: 'grid',
        columnGap: remSpacing.medium,

        [mediaQueries.lg]: {
          gridTemplateColumns: '1fr 1fr',
        },
      }}
    >
      <div>
        {!assignStaffPlanningEnabled && (
          <VideoBladeCard
            title="Overview"
            style={{ marginBottom: remSpacing.medium }}
          >
            <VideoBladeCardRow title="Add-on" value={addOnText} />
            <VideoBladeCardRow
              title="Case"
              value={caseClassificationTypeText}
            />
            <VideoBladeCardRow title="Patient" value={patientClassText} />
          </VideoBladeCard>
        )}
        <NoteCard
          values={values}
          setFieldValue={setFieldValue}
          editable={isEditable}
        />
        <CaseLabelForm
          form={caseLabelForm}
          values={values}
          setFieldValue={setFieldValue}
          editable={isEditable}
        />
      </div>
      <StaffPlanningCard
        title={'Staff'}
        apellaCase={apellaCase}
        values={values}
        setFieldValue={setFieldValue}
        editable={isEditable}
      />
    </div>
  )
}

export const VideoBladeCard = ({
  title,
  children,
  style,
  titleAction,
}: VideoBladeCardProps): React.JSX.Element => {
  const hasTitleSection = !!title || !!titleAction
  return (
    <Tile style={style}>
      <div
        css={{
          display: 'grid',
          padding: `${hasTitleSection ? remSpacing.medium : remSpacing.xsmall} ${remSpacing.medium} ${remSpacing.xsmall} ${remSpacing.medium}`,
          ...style,
        }}
      >
        {hasTitleSection && (
          <div
            css={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <H5
              style={{
                color: theme.palette.text.primary,
              }}
            >
              {title}
            </H5>
            {titleAction}
          </div>
        )}
        {children}
      </div>
    </Tile>
  )
}

export const onStaffError = () => {
  toast.error('There was an error saving the staffing plan, please try again!')
}

export const StaffPlanningCard = ({
  apellaCase,
  title,
  values,
  setFieldValue,
  editable = false,
}: {
  apellaCase?: {
    case?: {
      staff: Staff[]
      staffPlan?: StaffPlan[]
    }
  }
  values: CaseStaffPlanFormValues
  setFieldValue: FormikHelpers<CaseStaffPlanFormValues>['setFieldValue']
  title: string
  editable?: boolean
}) => {
  const { assignStaffPlanningEnabled } = useFlags<WebDashboardFeatureFlagSet>()
  const {
    currentOrganization,
    loading: isUserLoading,
    permissions,
  } = useCurrentUser()

  const { data: staffData } = useQuery<GetStaff, GetStaffVariables>(GET_STAFF, {
    variables: { orgId: currentOrganization?.node.id || '' },
    skip: !currentOrganization?.node.id || isUserLoading || !editable,
  })

  const staffOptions = useMemo(
    () =>
      staffData?.staff.edges.map(({ node: { id, name } }) => (
        <Option key={id} value={id} label={name} />
      )),
    [staffData]
  )

  const allEHRStaff =
    apellaCase?.case?.staff.filter(
      (s) => !PRIMARY_SURGEON_ROLES.includes(s.role)
    ) ?? []

  return (
    <VideoBladeCard title={title}>
      {assignStaffPlanningEnabled &&
        Object.values(CaseStaffPlanRole).map((role) => {
          const existingStaffPlans =
            apellaCase?.case?.staffPlan
              ?.filter((sp) => sp.role === role.valueOf())
              .map((sp) =>
                toStaffFullName({
                  staff: sp,
                })
              ) ?? []

          return (
            <VideoBladeCardRow
              key={role}
              title={toStaffRoleDisplayName(role)}
              value={
                permissions?.caseStaffPlanWriteEnabled && editable ? (
                  <MultiSelect
                    label={toStaffRoleDisplayName(role)}
                    name={role}
                    search
                    value={values[role] ?? []}
                    onChange={(ids?: string[]) =>
                      setFieldValue(role, ids ?? [])
                    }
                    css={{ width: '100%' }}
                    left={true}
                    buttonSize={'sm'}
                  >
                    {staffOptions}
                  </MultiSelect>
                ) : existingStaffPlans.length ? (
                  existingStaffPlans.join(', ')
                ) : (
                  <>&mdash;</>
                )
              }
            />
          )
        })}
      {!!allEHRStaff.length && (
        <>
          <div
            css={{
              color: theme.palette.text.secondary,
              margin: `${remSpacing.small} 0`,
            }}
          >
            <Caps2>EHR</Caps2>
          </div>
          {allEHRStaff.map((staff) => (
            <VideoBladeCardRow
              key={staff.id}
              title={toStaffRoleDisplayName(staff.role)}
              value={toStaffFullName({ staff })}
            />
          ))}
        </>
      )}
      {!allEHRStaff.length && !assignStaffPlanningEnabled && (
        <div
          css={{
            paddingTop: remSpacing.gutter,
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            color: theme.palette.text.tertiary,
          }}
        >
          No staff assigned
        </div>
      )}
    </VideoBladeCard>
  )
}

export const NoteCard = ({
  values,
  setFieldValue,
  editable = false,
}: {
  values: CasePlanningFormValues
  setFieldValue: FormikHelpers<CasePlanningFormValues>['setFieldValue']
  editable?: boolean
}) => {
  const { assignStaffPlanningEnabled } = useFlags<WebDashboardFeatureFlagSet>()
  const { permissions } = useCurrentUser()
  const [note, setNote] = useState(values.note.note)
  const debouncedChangeHandler = useMemo(
    () =>
      debounce((note) => {
        setFieldValue('note', { id: values.note.id, note })
      }, 1000),
    [setFieldValue, values.note.id]
  )

  if (!assignStaffPlanningEnabled) {
    return <></>
  }

  return (
    <VideoBladeCard
      title={'Remarks'}
      style={{ marginBottom: remSpacing.medium }}
    >
      <InputText
        name="note"
        placeholder="Instructions to the team"
        multiline
        disabled={!editable || !permissions?.caseStaffPlanWriteEnabled}
        value={note}
        rows={3}
        onChange={(e) => {
          setNote(e.target.value)
          debouncedChangeHandler(e.target.value)
        }}
      />
    </VideoBladeCard>
  )
}

const CaseLabelForm = ({
  form,
  values,
  setFieldValue,
  editable = false,
}: {
  form: CaseLabelFormType
  setFieldValue: FormikHelpers<CasePlanningFormValues>['setFieldValue']
  values: CasePlanningFormValues
  editable?: boolean
}) => {
  const { assignStaffPlanningEnabled } = useFlags<WebDashboardFeatureFlagSet>()
  const { permissions } = useCurrentUser()

  if (!assignStaffPlanningEnabled) {
    return <></>
  }

  return form.map((category) => {
    return (
      <VideoBladeCard key={category.id} title={category.name}>
        {category.fields.map((field) =>
          field.type === CaseLabelFieldType.BOOLEAN ? (
            <CaseLabelFormCheckbox
              key={field.id}
              field={field}
              value={values.labels[field.id]}
              setFieldValue={setFieldValue}
              disabled={!editable || !permissions?.caseStaffPlanWriteEnabled}
            />
          ) : field.type === CaseLabelFieldType.SINGLE_SELECT ? (
            <CaseLabelFormSingleSelect
              key={field.id}
              field={field}
              value={values.labels[field.id]}
              setFieldValue={setFieldValue}
              disabled={!editable || !permissions?.caseStaffPlanWriteEnabled}
            />
          ) : (
            <></>
          )
        )}
      </VideoBladeCard>
    )
  })
}

const CaseLabelFormCheckbox = ({
  field,
  value,
  setFieldValue,
  disabled,
}: {
  field: CaseLabelFormType[number]['fields'][number]
  setFieldValue: FormikHelpers<CasePlanningFormValues>['setFieldValue']
  value: string | undefined
  disabled: boolean
}) => {
  const trueOption = field.options.find((o) => o.booleanValue)
  const currentValue = value === trueOption?.id
  return (
    <VideoBladeCardRow
      value={
        <label
          css={{
            display: 'flex',
            cursor: 'pointer',
            alignItems: 'center',
            gap: remSpacing.xxsmall,
          }}
        >
          <Field
            id={field.id}
            type="checkbox"
            checked={currentValue}
            disabled={disabled}
            onChange={() =>
              setFieldValue(
                `labels.${field.id}`,
                currentValue ? undefined : trueOption?.id
              )
            }
          />
          {field.name}
        </label>
      }
      icon={
        <CaseLabelIcon
          abbreviation={trueOption?.abbreviation}
          color={trueOption?.color}
          selected={currentValue}
        />
      }
    />
  )
}

const CaseLabelFormSingleSelect = ({
  field,
  value,
  setFieldValue,
  disabled,
}: {
  field: CaseLabelFormType[number]['fields'][number]
  setFieldValue: FormikHelpers<CasePlanningFormValues>['setFieldValue']
  value: string | undefined
  disabled: boolean
}) => {
  const options = [
    <Option key={'none'} value={undefined} label={'None'} />,
    ...field.options.map((option) => (
      <Option key={option.id} value={option.id} label={option.value}>
        <div
          css={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
          }}
        >
          {option.value}
          <CaseLabelIcon
            abbreviation={option.abbreviation}
            color={option.color}
          />
        </div>
      </Option>
    )),
  ]

  return (
    <VideoBladeCardRow
      title={field.name}
      value={
        <div css={{ textTransform: 'none' }}>
          <SingleSelect
            label={field.name}
            name={field.name}
            search
            value={value}
            onChange={(id?: string) =>
              setFieldValue(`labels.${field.id}`, id ?? undefined)
            }
            css={{ width: '100%' }}
            left={true}
            buttonSize={'sm'}
            disabled={disabled}
          >
            {options}
          </SingleSelect>
        </div>
      }
    />
  )
}
