import { useLocation } from 'react-router'

import { useBladeState } from 'src/utils/useBladeState'

import { VideoBlade } from './VideoBlade'

// Used for removing video blade params from the URL when closing the video blade after a hard refresh or inital visit (clicking a link/bookmark)
const ALL_VIDEO_BLADE_PARAMS = [
  'roomId',
  'startTime',
  'endTime',
  'time',
  'apellaCaseId',
  'turnoverId',
]

export const VideoViewPage = (): React.JSX.Element => {
  const location = useLocation()
  const query = new URLSearchParams(location.search)
  const roomId = query.get('roomId') || undefined
  const startTime = query.get('startTime') || undefined
  const endTime = query.get('endTime') || undefined
  const time = query.get('time') || undefined
  const apellaCaseId = query.get('apellaCaseId') || undefined
  const turnoverId = query.get('turnoverId') || undefined

  const currentUrlSearchParamsWithoutVideoBladeParams = new URLSearchParams(
    Array.from(query.entries()).filter(
      ([key]) => !ALL_VIDEO_BLADE_PARAMS.includes(key)
    )
  ).toString()

  const { isBladeOpen, onBladeClose } = useBladeState({
    // When hard refreshing a video blade, closing the video blade should go "up" one route segment (pop off the /video segement) and use the original URL params without the video blade params
    // Example:
    //    Refresh/Follow Link to Video Blade:
    //    /schedule?sites=site1&roomId=123&startTime=123&endTime=321&time=123&apellaCaseId=abc
    //    Close Video Blade:
    //    /schedule?sites=site1
    hardRefreshFallbackPath: `../?${currentUrlSearchParamsWithoutVideoBladeParams}`,
  })

  return (
    <VideoBlade
      isBladeOpen={isBladeOpen}
      onClose={onBladeClose}
      roomId={roomId}
      startTime={startTime}
      endTime={endTime}
      time={time}
      apellaCaseId={apellaCaseId}
      turnoverId={turnoverId}
    />
  )
}
