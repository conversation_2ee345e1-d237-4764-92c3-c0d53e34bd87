import { Theme<PERSON>rovider } from '@emotion/react'

import { Mocked<PERSON>rovider } from '@apollo/client/testing'
import { faker } from '@faker-js/faker'
import { render, screen } from '@testing-library/react'

import { theme } from '@apella/component-library'
import { aUserUiPermissions } from 'src/__generated__/generated-mocks'

import { useCurrentUser } from '../../modules/user/hooks'
import MockLD<PERSON>rovider from '../../test/MockLDProvider'
import { CaseStaffPlanRole } from '../../utils/roles'
import { GET_STAFF } from '../Notifications/queries'
import { StaffPlanningCard } from './VideoBladeCard'

// Mock the useCurrentUser hook
vi.mock('src/modules/user/hooks')
const mockedUseCurrentUser = vi.mocked(useCurrentUser)

const mockStaffData = {
  staff: {
    edges: [
      { node: { id: 'staff-1', name: '<PERSON>' } },
      { node: { id: 'staff-2', name: '<PERSON>' } },
    ],
  },
}

const mockApolloResponse = {
  request: {
    query: GET_STAFF,
    variables: { orgId: 'someOrgId' },
  },
  result: {
    data: mockStaffData,
  },
}

const defaultProps = {
  title: 'Staff Planning',
  values: {
    [CaseStaffPlanRole.Anesthesiologist]: [],
    [CaseStaffPlanRole.CRNA]: [],
    [CaseStaffPlanRole.ScrubTech]: [],
    [CaseStaffPlanRole.Circulator]: [],
  },
  setFieldValue: vi.fn(),
  editable: true,
}

describe('StaffPlanningCard', () => {
  mockedUseCurrentUser.mockReturnValue({
    loading: false,
    permissions: {
      ...aUserUiPermissions({ caseStaffPlanWriteEnabled: true }),
      caseNotePlanReadEnabled: faker.datatype.boolean(),
      patientReadEnabled: faker.datatype.boolean(),
      dashboardTurnoversEnabled: faker.datatype.boolean(),
      availableTimesEmailEnabled: faker.datatype.boolean(),
    },
    currentOrganization: {
      __typename: 'OrganizationEdge',
      node: {
        __typename: 'Organization',
        id: 'someOrgId',
        name: 'Some Org',
        auth0OrgId: 'someOrgId',
        sites: {
          __typename: 'SiteConnection',
          edges: [],
        },
      },
    },
    userOrganizations: undefined,
    error: undefined,
    userId: undefined,
    email: undefined,
    isApellaEmployee: false,
  })

  it('renders MultiSelect when editable is true', async () => {
    render(
      <ThemeProvider theme={theme}>
        <MockLDProvider flags={{ assignStaffPlanningEnabled: true }}>
          <MockedProvider mocks={[mockApolloResponse]} addTypename={false}>
            <StaffPlanningCard {...defaultProps} editable={true} />
          </MockedProvider>
        </MockLDProvider>
      </ThemeProvider>
    )

    // Wait for data to load
    Object.values(CaseStaffPlanRole).forEach((role) => {
      expect(screen.getByRole('button', { name: role })).toBeInTheDocument()
    })
  })

  it('renders static text when editable is false', async () => {
    const apellaCase = {
      case: {
        staffPlan: [
          {
            role: CaseStaffPlanRole.Anesthesiologist,
            firstName: 'John',
            lastName: 'Doe',
            displayName: 'John Doe',
            id: 'johndoe',
            rowId: 'johndoe',
          },
        ],
        staff: [],
      },
    }

    render(
      <ThemeProvider theme={theme}>
        <MockLDProvider flags={{ assignStaffPlanningEnabled: true }}>
          <MockedProvider mocks={[mockApolloResponse]} addTypename={false}>
            <StaffPlanningCard
              {...defaultProps}
              editable={false}
              apellaCase={apellaCase}
            />
          </MockedProvider>
        </MockLDProvider>
      </ThemeProvider>
    )

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    Object.values(CaseStaffPlanRole).forEach((role) => {
      expect(
        screen.queryByRole('button', { name: role })
      ).not.toBeInTheDocument()
    })
  })

  it('shows EHR staff when present', async () => {
    const apellaCase = {
      case: {
        staffPlan: [],
        staff: [
          {
            id: 'ehr-1',
            role: CaseStaffPlanRole.Anesthesiologist,
            firstName: 'Sarah',
            lastName: 'Jones',
            displayName: 'Sarah Jones',
          },
        ],
      },
    }

    render(
      <ThemeProvider theme={theme}>
        <MockLDProvider flags={{ assignStaffPlanningEnabled: true }}>
          <MockedProvider mocks={[mockApolloResponse]} addTypename={false}>
            <StaffPlanningCard {...defaultProps} apellaCase={apellaCase} />
          </MockedProvider>
        </MockLDProvider>
      </ThemeProvider>
    )

    expect(screen.getByText('EHR')).toBeInTheDocument()
    expect(screen.getByText('Sarah Jones')).toBeInTheDocument()
  })
})
