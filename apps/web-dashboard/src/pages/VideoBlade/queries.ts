import { gql } from '@apollo/client'

export const GET_CASE_LABELS_FORM = gql`
  query GetCaseLabelsForm($siteId: String!) {
    site(id: $siteId) {
      caseLabelForm {
        id
        name
        fields {
          id
          name
          ordinal
          type
          options {
            id
            abbreviation
            color
            value
            booleanValue
          }
        }
      }
    }
  }
`

export const UPSERT_CASE_NOTE = gql`
  mutation UpsertCaseNote($input: CaseNotePlanUpsertInput!) {
    caseNotePlanUpsert(input: $input) {
      success
    }
  }
`

export const UPSERT_CASE_STAFF = gql`
  mutation UpsertCaseStaff($input: [CaseStaffPlanUpsertInput!]!) {
    caseStaffPlanUpsert(input: $input) {
      success
    }
  }
`

export const UPSERT_CASE_LABELS = gql`
  mutation UpsertCaseLabels($input: [CaseLabelUpsertInput!]!) {
    caseLabelUpsert(input: $input) {
      success
    }
  }
`
