import {
  Button,
  Tooltip,
  Unsnooze,
  remSpacing,
} from '@apella/component-library'

export interface LiveSnoozeBannerProps {
  onUnsnooze: () => void
  snoozedRooms: string[]
}

const LiveSnoozeBanner = ({
  snoozedRooms,
  onUnsnooze,
}: LiveSnoozeBannerProps): React.JSX.Element => {
  return (
    <div
      css={{
        display: 'inline-block',
        marginLeft: remSpacing.medium,
      }}
      style={{ display: !snoozedRooms.length ? 'none' : undefined }}
    >
      {!!snoozedRooms.length && (
        <Tooltip
          body={<div css={{ whiteSpace: 'nowrap' }}>Unsnooze</div>}
          placement={'bottom'}
        >
          <Button color={'alternate'} onClick={onUnsnooze} buttonType="icon">
            <Unsnooze size="sm" />
          </Button>
        </Tooltip>
      )}
    </div>
  )
}

export default LiveSnoozeBanner
