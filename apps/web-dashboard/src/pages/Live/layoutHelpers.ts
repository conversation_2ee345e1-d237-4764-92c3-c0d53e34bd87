import { Size } from '@apella/hooks'

export interface AspectRatio {
  height: number
  width: number
}

/**
 * Gets the maximum width that will fit in a container for an asset with a
 * given aspect ratio.
 */
export const getMaxBoundingWidth = (
  containerWidth: number,
  containerHeight: number,
  aspectRatio: AspectRatio
) => {
  // determine if the container is bounded by height or width
  const videoWidthUnits = containerWidth / aspectRatio.width
  const videoHeightUnits = containerHeight / aspectRatio.height

  return aspectRatio.width * Math.min(videoHeightUnits, videoWidthUnits)
}

/**
 * Calculates how many rows and cols should be used to preserve the target
 * aspectRatio for each of n items in a container.
 * @param containerSize - The size of the container
 * @param aspectRatio - The target aspect ratio
 * @param numItems - The number of items to distribute
 * @param columnGap - The gap between columns in pixels
 * @param rowGap - The gap between rows in pixels
 * @param additionalTileHeight - Additional static height in each tile
 */
export const calculateNumRowsAndCols = (
  containerSize: Size | undefined,
  aspectRatio: AspectRatio,
  numItems: number,
  columnGap?: number,
  rowGap?: number,
  additionalTileHeight?: number
) => {
  if (!containerSize || numItems <= 1) {
    return { rows: 1, cols: 1 }
  }
  // Iteratively add rows and columns until there are enough tiles.
  // At each step, choose either a row or column depending on which will make
  // the tiles closer to the target aspect ratio.
  let rows = 1
  let cols = 1
  while (cols * rows < numItems) {
    const tileWidthAddingRow = getMaxTileWidth(
      containerSize,
      aspectRatio,
      rows + 1,
      cols,
      columnGap,
      rowGap,
      additionalTileHeight
    )
    const tileWidthAddingCol = getMaxTileWidth(
      containerSize,
      aspectRatio,
      rows,
      cols + 1,
      columnGap,
      rowGap,
      additionalTileHeight
    )
    if (tileWidthAddingRow > tileWidthAddingCol) {
      rows++
    } else {
      cols++
    }
  }

  return { rows, cols }
}

export const getMaxTileWidth = (
  containerSize: Size,
  aspectRatio: AspectRatio,
  rows: number,
  cols: number,
  columnGap: number = 0,
  rowGap: number = 0,
  additionalTileHeight: number = 0,
  checkHeightBound: boolean = true
): number => {
  if (!checkHeightBound) {
    return (containerSize.width - columnGap * (cols - 1)) / cols
  } else {
    const maxVideoSize = {
      width: (containerSize.width - columnGap * (cols - 1)) / cols,
      height: (containerSize.height - rowGap * (rows - 1)) / rows,
    }
    maxVideoSize.height = maxVideoSize.height - additionalTileHeight
    return getMaxBoundingWidth(
      maxVideoSize.width,
      maxVideoSize.height,
      aspectRatio
    )
  }
}
