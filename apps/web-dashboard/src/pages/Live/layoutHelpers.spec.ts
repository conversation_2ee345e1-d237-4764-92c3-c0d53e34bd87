import { calculateNumRowsAndCols, getMaxBoundingWidth } from './layoutHelpers'

const SQUARE_ASPECT_RATIO = { height: 1, width: 1 }

describe('layoutHelpers', () => {
  describe('getMaxBoundingWidth', () => {
    it('returns the full width of the container if bounded by width', () => {
      const result = getMaxBoundingWidth(8, 10, SQUARE_ASPECT_RATIO)
      expect(result).toEqual(8)
    })

    it('returns the full height of the container if bounded by height', () => {
      const result = getMaxBoundingWidth(10, 8, SQUARE_ASPECT_RATIO)
      expect(result).toEqual(8)
    })
  })

  describe('calculateNumRowsAndCols', () => {
    // I'm notating these [rows, cols]

    it('returns [1, 1] if container size is not provided', () => {
      const result = calculateNumRowsAndCols(undefined, SQUARE_ASPECT_RATIO, 4)
      expect(result).toMatchObject({ rows: 1, cols: 1 })
    })

    it('returns [1, 1] if numItems is 0', () => {
      const result = calculateNumRowsAndCols(
        { width: 100, height: 100 },
        SQUARE_ASPECT_RATIO,
        0
      )
      expect(result).toMatchObject({ rows: 1, cols: 1 })
    })

    it('returns [1, 1] if numItems is negative', () => {
      const result = calculateNumRowsAndCols(
        { width: 100, height: 100 },
        SQUARE_ASPECT_RATIO,
        -3
      )
      expect(result).toMatchObject({ rows: 1, cols: 1 })
    })

    it('returns [2, 2] for 4 squares in a square container', () => {
      const result = calculateNumRowsAndCols(
        { width: 100, height: 100 },
        SQUARE_ASPECT_RATIO,
        4
      )
      expect(result).toMatchObject({ rows: 2, cols: 2 })
    })

    it('returns [2, 3] for 5 squares in a square container, showing it prefers to add columns', () => {
      const result = calculateNumRowsAndCols(
        { width: 100, height: 100 },
        SQUARE_ASPECT_RATIO,
        5
      )
      expect(result).toMatchObject({ rows: 2, cols: 3 })
    })

    it('returns [2, 4] for 8 squares in a 1000x600 container', () => {
      const result = calculateNumRowsAndCols(
        { width: 1000, height: 600 },
        SQUARE_ASPECT_RATIO,
        8
      )
      expect(result).toMatchObject({ rows: 2, cols: 4 })
    })

    it('returns [4, 2] for 8 squares in a 600x1000 container', () => {
      const result = calculateNumRowsAndCols(
        { width: 600, height: 1000 },
        SQUARE_ASPECT_RATIO,
        8
      )
      expect(result).toMatchObject({ rows: 4, cols: 2 })
    })

    it('returns [1, 2] for 2 squares in a 100x100 container with 20px rowGap', () => {
      const result = calculateNumRowsAndCols(
        { width: 100, height: 100 },
        SQUARE_ASPECT_RATIO,
        2,
        0,
        20
      )
      expect(result).toMatchObject({ rows: 1, cols: 2 })
    })
  })

  it('returns [2, 1] for 2 squares in a 100x100 container with 20px columnGap', () => {
    const result = calculateNumRowsAndCols(
      { width: 100, height: 100 },
      SQUARE_ASPECT_RATIO,
      2,
      20,
      0
    )
    expect(result).toMatchObject({ rows: 2, cols: 1 })
  })
})
