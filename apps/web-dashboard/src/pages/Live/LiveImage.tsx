import { ReactNode, useContext, useEffect, useMemo, useState } from 'react'

import { ThemeProvider, useTheme } from '@emotion/react'

import {
  Button,
  darkTheme,
  getRandomStaticImage,
  Moon,
  Progress,
  pxSpacing,
  remSpacing,
  VideoPlayerBlurOverlayContainer,
  VideoPlayerCamera,
  VideoPlayerCameraSelector,
  VisibilityOff,
} from '@apella/component-library'
import { RoomPrivacyToggle } from 'src/components/RoomPrivacyToggle'
import { ShowVideoContext, useTouchDeviceContext } from 'src/Contexts'
import { UpdateRoomPrivacy } from 'src/hooks/__generated__'
import { PrivacyReason } from 'src/hooks/useRoomUpdate'
import { LiveRoomStatus } from 'src/pages/Live/types'

import { ASPECT_RATIO } from './consts'
import { MAX_TILE_WIDTH, MIN_TILE_WIDTH } from './cssConsts'
import { CameraStatus } from './LiveCameraStatus'

export interface LiveImageProps {
  activeCameraId?: string
  blurCenter?: boolean
  blurredGap?: string | number
  borderRadius?: string
  cameras: VideoPlayerCamera[]
  cameraStatus: CameraStatus
  controls?: 'visible' | 'hidden'
  fontSize?: string | number
  height?: string | number
  iconSize?: React.ComponentProps<typeof VisibilityOff>['size']
  image?: string
  imgWidth?: number
  keyboardShortcutEnabled?: boolean
  maxWidth?: string | number
  minWidth?: string | number
  overlay?: ReactNode
  privacyReason?: PrivacyReason
  roomId: string
  roomStatus?: LiveRoomStatus
  setCameraId: (newCameraId: string) => void
  snoozeRoom?: () => void
  turnoverGoal?: number | null
  width?: string | number
}

const BLUR_COPY = {
  PATIENT_IN_ROOM: 'Patient in room',
  PRIVACY_ENABLED: 'Privacy on',
} as const

const LiveImage = ({
  image,
  cameras,
  activeCameraId,
  setCameraId,
  cameraStatus,
  snoozeRoom,
  controls,
  keyboardShortcutEnabled,
  privacyReason: privacyReasonProp,
  height,
  minWidth = MIN_TILE_WIDTH,
  maxWidth = MAX_TILE_WIDTH,
  fontSize = '1rem',
  borderRadius,
  iconSize = 'xs',
  blurCenter = false,
  blurredGap = 8,
  width,
  roomId,
  overlay,
  imgWidth,
}: LiveImageProps): React.JSX.Element => {
  const theme = useTheme()
  const showVideoContext = useContext(ShowVideoContext)
  const randomStaticImage = useMemo(
    () =>
      getRandomStaticImage(
        typeof showVideoContext === 'number' ? showVideoContext : undefined
      ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [activeCameraId]
  )
  const size = imgWidth && imgWidth < 300 ? 'sm' : 'md'

  // We use state here so that we can blur the current image when privacy
  // is enabled. This is to provide a better user experience so that we don't
  // need to wait for the next polling tick to enable the blurred images. There are
  // some additional complications as we also need to check the polling image response
  // passed in via the prop in case the privacy state changes in some other location. Imagine
  // person A sets privacy and then person B unsets the privacy - we can't manage this via props
  // alone and have an experience to blur the current image before the polling tick, hence the
  // state and effect here
  const [privacyReason, setPrivacyReason] = useState<PrivacyReason>()

  useEffect(() => {
    if (privacyReason !== privacyReasonProp) {
      setPrivacyReason(privacyReasonProp)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [privacyReasonProp])

  const onMutationCompleted = (data: UpdateRoomPrivacy) => {
    if (data.roomUpdateConfiguration?.success) {
      setPrivacyReason(
        data.roomUpdateConfiguration.updatedRoom?.privacyEnabled
          ? PrivacyReason.PRIVACY_ENABLED
          : undefined
      )
    }
  }

  const isTouchDevice = useTouchDeviceContext()

  const isControlsVisible =
    controls == 'visible' || (isTouchDevice && controls !== 'hidden')

  const activeCamera = cameras.find((camera) => camera.id === activeCameraId)

  /**
   * Because items are positioned absolutely here the order of these elements is
   * important to stack correctly within the live image. Make sure you're careful to
   * test how stacking iteracts with each other if you change the order of the absolutely
   * posititioned elements.
   *
   * We also do not use z indices here as they tend to not play nicely with our other components
   * like our dropdowns
   */
  return (
    <div
      css={{
        minWidth,
        height,
        maxWidth,
        width,
        aspectRatio: `${ASPECT_RATIO.width} / ${ASPECT_RATIO.height}`,
        position: 'relative',
        background: theme.palette.gray[80],
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 'auto',
        borderRadius,
        overflow: 'hidden',
      }}
    >
      {privacyReason && (
        <div
          css={{
            position: 'absolute',
            left: 0,
            top: 0,
            width: '100%',
            height: '100%',
            backdropFilter: 'blur(1rem)',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            gap: blurredGap,
            alignItems: 'center',
            justifyContent: 'center',
            fontSize,
            color: 'rgba(255, 255, 255, 0.75)',
          }}
        >
          <VisibilityOff size={iconSize} />
          {BLUR_COPY[privacyReason]}
        </div>
      )}
      {!privacyReason && showVideoContext && !image && cameras.length ? (
        <Progress />
      ) : !privacyReason &&
        showVideoContext &&
        (cameraStatus === 'offline' || !cameras.length) ? (
        <div
          css={{
            color: theme.palette.gray[10],
            fontSize,
            textAlign: 'center',
          }}
        >
          No live image
        </div>
      ) : (
        <>
          {!privacyReason && blurCenter && (
            <VideoPlayerBlurOverlayContainer
              bottom={activeCamera?.patientBoundingBox?.bottomPct}
              left={activeCamera?.patientBoundingBox?.leftPct}
              width={activeCamera?.patientBoundingBox?.widthPct}
              height={activeCamera?.patientBoundingBox?.heightPct}
            />
          )}
          <img
            css={{
              height: '100%',
              width: '100%',
              pointerEvents: 'none',
            }}
            src={showVideoContext && !privacyReason ? image : randomStaticImage}
          />
        </>
      )}
      {overlay && (
        <div
          css={{
            // Give this element a little more spacing on top than bottom so that
            // the gradient has room to fade out
            padding: `${remSpacing.small}`,
            paddingBottom: `${remSpacing.xsmall}`,
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            height: 'fit-content',
            background:
              'linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.42) 48.96%, rgba(0, 0, 0, 0.665) 100%)',
            color: theme.palette.gray[10],
          }}
        >
          {overlay}
        </div>
      )}
      {isControlsVisible && (
        <div
          css={{
            position: 'absolute',
            top: remSpacing.small,
            right: remSpacing.small,
            display: 'grid',
            gridAutoFlow: 'column',
            columnGap: remSpacing.medium,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <RoomPrivacyToggle
            onMutationCompleted={onMutationCompleted}
            roomId={roomId}
            isPrivacyEnabled={privacyReason === PrivacyReason.PRIVACY_ENABLED}
          />
          {snoozeRoom && (
            <ThemeProvider theme={darkTheme}>
              <Button
                onClick={snoozeRoom}
                color="alternate"
                buttonType="icon"
                size={size}
                css={{
                  height: size === 'sm' ? 44 : 56,
                  width: size === 'sm' ? 44 : 56,
                  borderRadius: pxSpacing.small,
                }}
              >
                <Moon size={size} />
              </Button>
            </ThemeProvider>
          )}
        </div>
      )}
      {isControlsVisible &&
        cameras.length > 0 &&
        cameras.some((c) => c.id !== activeCameraId) && (
          <ThemeProvider theme={darkTheme}>
            <div
              css={{
                position: 'absolute',
                top: remSpacing.small,
                left: remSpacing.small,
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <VideoPlayerCameraSelector
                cameras={cameras}
                isVisible={true}
                activeCameraId={activeCameraId}
                onChangeCamera={setCameraId}
                keyboardShortcutEnabled={keyboardShortcutEnabled}
                size={size}
              />
            </div>
          </ThemeProvider>
        )}
    </div>
  )
}

export default LiveImage
