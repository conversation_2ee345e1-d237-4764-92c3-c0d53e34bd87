import { useMemo } from 'react'

import { keyframes } from '@emotion/react'

import styled from '@emotion/styled'
import { DateTime, Duration } from 'luxon'

import {
  Caps2Bold,
  formatDuration,
  formatTimerWithSeconds,
  H5,
  remSpacing,
  shape,
  Span4,
  theme,
} from '@apella/component-library'
import { useCurrentTime } from 'src/utils/useCurrentTime'

import { RoomStatusName } from '../../__generated__/globalTypes'
import { formatStatus, LiveStatus, useStatusStyles } from '../../utils/status'

const gradientAnimation = keyframes`
    0%{background-position:100%}
    100%{background-position:0}
`

interface StyledBannerProps {
  background?: string
  borderColor?: string
  isFlatBottom: boolean
  progressWidthPct?: number
  textColor?: string
}

const StyledBanner = styled.div<StyledBannerProps>(
  ({
    textColor,
    background,
    borderColor,
    progressWidthPct = 0,
    isFlatBottom,
  }) => ({
    display: 'grid',
    gridTemplateAreas: '"progress rest"',
    gridTemplateColumns: `${Math.round(progressWidthPct)}% ${100 - Math.round(progressWidthPct)}%`,
    color: textColor,
    background: background,
    border: borderColor ? `0.5px solid ${borderColor}` : 'none',
    justifyContent: 'space-between',
    borderRadius: shape.borderRadiusPx.xsmall,
    borderBottomLeftRadius: isFlatBottom ? 0 : shape.borderRadiusPx.xsmall,
    borderBottomRightRadius: isFlatBottom ? 0 : shape.borderRadiusPx.xsmall,
  })
)

interface LiveTurnoverBannerProps {
  emphasizeText?: boolean
  isIdleOnTurnoverDash?: boolean
  isOnLive?: boolean
  liveStatus: LiveStatus
  since: DateTime
  turnoverGoal?: number | null
}

export const LiveStatusBanner = ({
  liveStatus,
  since,
  turnoverGoal,
  isOnLive = false,
  emphasizeText = false,
  isIdleOnTurnoverDash = false,
}: LiveTurnoverBannerProps): React.JSX.Element => {
  const statusStyles = useStatusStyles()
  const currentTime = useCurrentTime()
  const timeElapsed = since ? currentTime.diff(since) : undefined
  const formattedTime =
    liveStatus === RoomStatusName.TURNOVER
      ? formatTimerWithSeconds(timeElapsed)
      : formatDuration(timeElapsed)

  const showDuration = liveStatus !== RoomStatusName.CLOSED && !!formattedTime

  const turnoverGoalDuration = useMemo(
    () =>
      turnoverGoal ? Duration.fromObject({ minutes: turnoverGoal }) : undefined,
    [turnoverGoal]
  )

  const progressBarStyles = useMemo(() => {
    if (!timeElapsed || !turnoverGoalDuration) return undefined

    const elapsedDurationMs = timeElapsed.toMillis()
    const turnoverGoalDurationMs = turnoverGoalDuration.toMillis()

    const isUnderGoal = elapsedDurationMs < turnoverGoalDurationMs

    const background = isUnderGoal
      ? theme.palette.yellow[10]
      : theme.palette.red[30]

    const progressWidth = isUnderGoal
      ? (elapsedDurationMs / turnoverGoalDurationMs) * 100
      : (turnoverGoalDurationMs / elapsedDurationMs) * 100

    return {
      background,
      progressWidth,
    }
  }, [timeElapsed, turnoverGoalDuration])

  const isTurnover = liveStatus === RoomStatusName.TURNOVER
  const isTurnoverWithGoal = isTurnover && turnoverGoalDuration
  const isFlatBottom = isOnLive
  const TextWrapper = emphasizeText ? H5 : 'span'

  const bannerProps = isTurnoverWithGoal
    ? {
        textColor: 'black',
        borderColor: statusStyles(liveStatus).backgroundColor,
        background: progressBarStyles?.background,
        progressWidthPct: progressBarStyles?.progressWidth,
        isFlatBottom,
      }
    : {
        textColor: !isTurnover ? statusStyles(liveStatus).textColor : 'black',
        background: isIdleOnTurnoverDash
          ? theme.palette.gray[30]
          : statusStyles(liveStatus).backgroundColor,
        isFlatBottom,
      }

  return (
    <StyledBanner {...bannerProps}>
      {isTurnoverWithGoal && (
        <div
          css={{
            gridArea: 'progress',
            background:
              'linear-gradient(to right,' +
              // first 20% is solid, since the backgroundSize is 500%, this means it starts 100% solid
              `${theme.palette.yellow[50]}, ${theme.palette.yellow[50]} 20%,` +
              // from 20%-40% there is a gradient from the dark color to the light color
              // then from 40%-60% here there is a solid dark color
              // from 60%-80% there is another gradient back  to the light color
              `${theme.palette.yellow[20]} 40%, ${theme.palette.yellow[20]} 60%,` +
              // last 20% is again solid, so that the loop on the animation is seamless back to the solid 0-20%
              `${theme.palette.yellow[50]} 80%,  ${theme.palette.yellow[50]})`,
            backgroundSize: '500% 100%',
            borderTopLeftRadius: shape.borderRadiusPx.xsmall,
            borderBottomLeftRadius: isFlatBottom
              ? 0
              : shape.borderRadiusPx.xsmall,
            animation: `${gradientAnimation} 3s ease infinite`,
          }}
        />
      )}
      <Span4
        css={{
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
          gap: remSpacing.gutter,
          padding: `${remSpacing.xxsmall} ${remSpacing.xsmall}`,
          gridArea: 'progress / progress / rest / rest',
        }}
      >
        {isOnLive && (
          <Caps2Bold css={{ textTransform: 'uppercase' }}>
            {formatStatus(liveStatus)}
          </Caps2Bold>
        )}
        {showDuration && (
          <TextWrapper>
            {formattedTime}
            {isTurnover ? 'm' : ' elapsed'}
          </TextWrapper>
        )}
      </Span4>
    </StyledBanner>
  )
}
