import { useTheme } from '@emotion/react'

import {
  Button,
  Close,
  P3,
  fontWeights,
  remSpacing,
  shape,
} from '@apella/component-library'

export const Banner = ({
  numSnoozedVideo,
  open,
  onChangeOpen,
}: {
  numSnoozedVideo: number
  open: boolean
  onChangeOpen: (open: boolean) => void
}) => {
  const theme = useTheme()
  return open ? (
    <div
      css={{
        display: 'grid',
        gridAutoFlow: 'column',
        alignItems: 'center',
        backgroundColor: theme.palette.blue[40],
        borderRadius: shape.borderRadius.xsmall,
        gridTemplateColumns: 'auto 1fr',
        columnGap: remSpacing.medium,
        padding: remSpacing.medium,
      }}
    >
      <P3 css={{ ...fontWeights.semibold, color: 'white' }}>
        Hiding {numSnoozedVideo} snoozed room
        {numSnoozedVideo !== 1 && 's'} until tomorrow. Unsnooze to show all
        snoozed rooms.
      </P3>

      <Button
        color="primary"
        appearance="link"
        size={'sm'}
        buttonType={'icon'}
        css={{
          justifySelf: 'end',
          padding: 0,
        }}
        onClick={() => onChangeOpen(false)}
      >
        <Close
          size={'sm'}
          css={{
            color: 'white',
            ':hover': {
              backgroundColor: theme.palette.blue[30],
              borderRadius: shape.borderRadius.xxsmall,
            },
          }}
        />
      </Button>
    </div>
  ) : null
}
