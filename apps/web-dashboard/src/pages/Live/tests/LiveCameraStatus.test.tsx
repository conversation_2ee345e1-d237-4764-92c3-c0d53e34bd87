import { ThemeProvider } from '@emotion/react'

import { render, screen } from '@testing-library/react'

import { theme } from '@apella/component-library'

import LiveCameraStatus, { MAX_TILE_WIDTH } from '../LiveCameraStatus'

describe('Live Camera Status', () => {
  it('displays text when tileWidth > MAX_TILE_WIDTH', async () => {
    render(
      <ThemeProvider theme={theme}>
        <LiveCameraStatus status={'live'} tileWidth={MAX_TILE_WIDTH + 1} />
      </ThemeProvider>
    )
    expect(await screen.findByText('LIVE')).toBeInTheDocument()
  })
  it('does not display text when tileWidth < MAX_TILE_WIDTH', async () => {
    render(
      <ThemeProvider theme={theme}>
        <LiveCameraStatus status={'live'} tileWidth={MAX_TILE_WIDTH - 1} />
      </ThemeProvider>
    )
    expect(screen.queryByText('LIVE')).not.toBeInTheDocument()
  })
})
