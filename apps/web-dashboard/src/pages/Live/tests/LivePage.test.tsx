import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router'

import { ThemeProvider } from '@emotion/react'

import { InMemoryCache } from '@apollo/client'
import { MockedProvider } from '@apollo/client/testing'
import { render, screen } from '@testing-library/react'
import { DateTime, Settings } from 'luxon'

import { theme } from '@apella/component-library'
import { SITES_KEY } from 'src/utils/useSitesState'

import LivePage from '../Live'
import { LiveMocks } from '../mocks/LivePage.mock'

const cache = new InMemoryCache()

const now = DateTime.fromISO('2023-12-19T19:00:00.000-00:00')

describe('Live Page tests', () => {
  beforeEach(() => {
    const location = {
      ...window.location,
      search: `?${SITES_KEY}=%5B%22lab_1%22%5D`,
    }
    Object.defineProperty(window, 'location', {
      writable: true,
      value: location,
    })
    Settings.defaultZone = 'America/Los_Angeles'
    Settings.now = () => now.valueOf()
    vi.mock('launchdarkly-react-client-sdk', async (importOriginal) => ({
      ...(await importOriginal<
        typeof import('launchdarkly-react-client-sdk')
      >()),
      useFlags: () => ({
        ehrEnabled: true,
      }),
    }))

    vi.resetModules()
  })
  it('Test Live Page, no interaction, 4 rooms(Closed, In Progress, Final Case, Behind Schedule)', async () => {
    render(
      <MockedProvider
        mocks={LiveMocks}
        addTypename={true}
        cache={cache}
        defaultOptions={{
          watchQuery: {
            fetchPolicy: 'no-cache',
          },
        }}
      >
        <ThemeProvider theme={theme}>
          <LivePage />
        </ThemeProvider>
      </MockedProvider>,
      { wrapper: BrowserRouter }
    )

    expect(await screen.findByText('Garage 0')).toBeInTheDocument()
    expect(await screen.findByText('Garage 1')).toBeInTheDocument()
    expect(await screen.findByText('Garage 2')).toBeInTheDocument()
    expect(await screen.findByText('Garage 3')).toBeInTheDocument()
  })
})
