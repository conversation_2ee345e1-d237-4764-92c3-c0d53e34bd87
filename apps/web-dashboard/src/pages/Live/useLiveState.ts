import { useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { sortBy } from 'lodash'
import { DateTime } from 'luxon'

import { RoomStatusName } from 'src/__generated__/globalTypes'
import { useTimezone } from 'src/Contexts'
import { toInsightApellaCase } from 'src/modules/daily-metrics/hooks/useApellaCaseDailyMetrics'
import { useCurrentMinute } from 'src/utils/useCurrentTime'
import { usePolling } from 'src/utils/usePolling'

import { dtOrUndefined } from '../../utils/graphqlParsingHelpers'
import { PRIMARY_SURGEON_ROLES } from '../../utils/roles'
import { getLiveStatus } from '../../utils/status'
import { calculateTurnoverLengthStatus } from '../../utils/turnovers'
import { Staff } from '../types'
import {
  GetLiveRooms,
  GetLiveRoomsVariables,
  LiveApellaCaseFragment,
} from './__generated__'
import { SLOW_POLL_INTERVAL_MS } from './consts'
import { GET_LIVE_ROOMS } from './queries'
import { LiveApellaCase, LiveRoom } from './types'

export const useLiveState = ({
  roomStatusFilter,
  siteIds,
  roomIds,
}: {
  roomStatusFilter?: RoomStatusName[]
  siteIds?: string[]
  roomIds?: string[]
}): {
  roomsBySiteId: { [siteId: string]: LiveRoom[] }
  isLoading: boolean
} => {
  const currentMinute = useCurrentMinute()

  const { timezone } = useTimezone()

  const {
    data: roomsData,
    loading: isLoading,
    startPolling,
    stopPolling,
    refetch,
  } = useQuery<GetLiveRooms, GetLiveRoomsVariables>(GET_LIVE_ROOMS, {
    variables: {
      minTime: currentMinute.setZone(timezone).startOf('day').toISO(),
      maxTime: currentMinute.setZone(timezone).endOf('day').toISO(),
      statusFilter: roomStatusFilter,
      siteIds,
      roomIds,
    },
    // Allow partial data when loading the Live page, since it loads data from many
    // different sources, and they're not all required to render the page.
    errorPolicy: 'all',
  })
  const sites = useMemo(() => roomsData?.sites?.edges ?? [], [roomsData])

  usePolling({
    refetch,
    startPolling,
    stopPolling,
    interval: SLOW_POLL_INTERVAL_MS,
  })

  // This processes all the room cases to determine the room status
  // Then maps the sorted rooms to the site
  // TODO: Move this logic to the backend

  const roomsBySiteId = useMemo(
    () =>
      sites.reduce<{
        [x: string]: LiveRoom[]
      }>(
        (roomsBySite, { node: site }) => ({
          ...roomsBySite,
          [site.id]: sortBy(
            site.rooms.edges,
            (e) => e.node.sortKey ?? e.node.id
          ).map<LiveRoom>(({ node: room }) => {
            const cases = !!room.apellaCases
              ? room.apellaCases.edges.map(({ node }) => node)
              : []

            const insightsCases = cases.map((c) =>
              toInsightApellaCase({ ...c, room })
            )

            const mostRecentEvent =
              room.roomEvents.edges.length > 0
                ? room.roomEvents.edges[0].node
                : null

            const liveStatus = getLiveStatus(
              room.status,
              room.status.inProgressApellaCase?.status
            )

            return {
              id: room.id,
              name: room.name,
              mostRecentEvent,
              status: {
                name: room.status.name,
                liveStatus: liveStatus.name,
                since: liveStatus.since,
                inProgressApellaCase: room.status.inProgressApellaCase
                  ? parseLiveApellaCase(
                      room.status.inProgressApellaCase,
                      timezone
                    )
                  : undefined,
                inProgressTurnover: parseInProgressTurnover(
                  room.status.inProgressTurnover,
                  site.turnoverGoals,
                  timezone
                ),
                nextCase: room.status.nextCase
                  ? parseLiveApellaCase(room.status.nextCase, timezone)
                  : undefined,
              },
              cases: insightsCases,
              turnoverGoal: site.turnoverGoals.goalMinutes,
              siteId: site.id,
            }
          }),
        }),
        {}
      ),
    [sites, timezone]
  )

  return {
    roomsBySiteId,
    isLoading,
  }
}

const parseLiveApellaCase = (
  liveApellaCaseRaw: LiveApellaCaseFragment,
  timezone: string
): LiveApellaCase => {
  const primarySurgeons: Staff[] =
    liveApellaCaseRaw.case?.caseStaff.map((cs) => ({
      ...cs.staff,
      role: cs.role ?? PRIMARY_SURGEON_ROLES[0],
      displayName: cs.staff.lastName,
    })) ?? []
  return {
    id: liveApellaCaseRaw.id,
    startTime: DateTime.fromISO(liveApellaCaseRaw.startTime).setZone(timezone),
    endTime: dtOrUndefined(liveApellaCaseRaw.endTime)?.setZone(timezone),
    type: liveApellaCaseRaw.type,
    primarySurgeons,
    primaryProcedures:
      liveApellaCaseRaw.case?.primaryCaseProcedures.map(
        (p) => p.procedure.name
      ) ?? [],
    isAddOn: liveApellaCaseRaw?.case?.isAddOn,
  }
}

const parseInProgressTurnover = (
  inProgressTurnover: GetLiveRooms['sites']['edges'][number]['node']['rooms']['edges'][number]['node']['status']['inProgressTurnover'],
  turnoverGoals: GetLiveRooms['sites']['edges'][number]['node']['turnoverGoals'],
  timezone: string
) => {
  if (inProgressTurnover) {
    const startTime = DateTime.fromISO(inProgressTurnover.startTime)
    const endTime = DateTime.fromISO(inProgressTurnover.endTime)
    const currentLengthStatus = calculateTurnoverLengthStatus(
      startTime,
      DateTime.now(),
      turnoverGoals
    )
    const overallLengthStatus = calculateTurnoverLengthStatus(
      startTime,
      endTime,
      turnoverGoals
    )

    return {
      id: inProgressTurnover.id,
      followingCase: parseLiveApellaCase(
        inProgressTurnover.followingCase,
        timezone
      ),
      startTime,
      endTime,
      overallLengthStatus,
      currentLengthStatus,
      type: inProgressTurnover.type,
    }
  }
}
