import { useCallback, useEffect, useMemo, useState } from 'react'
import { generatePath, Outlet } from 'react-router'

import { intersection, pickBy } from 'lodash'
import { DateTime } from 'luxon'

import {
  Button,
  ButtonGroup,
  FlexContainer,
  FlexItem,
  mediaQueries,
  pxSpacing,
  remSpacing,
  ZoomIn,
  ZoomOut,
} from '@apella/component-library'
import { useLocalStorageState } from '@apella/hooks'
import { CaseStatusName, RoomStatusName } from 'src/__generated__/globalTypes'
import { ToggleableCaseKey } from 'src/components/Keys'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { UserViewContext } from 'src/components/UserViews/useUserFilterViews'
import { useScheduleMetrics } from 'src/modules/daily-metrics/hooks/useScheduleMetrics'
import LiveSnoozeBanner from 'src/pages/Live/LiveSnoozeBanner'
import { ScheduleMetrics } from 'src/pages/Schedule/ScheduleMetrics'
import { TvModeToggleButton } from 'src/pages/Schedule/TvModeToggleButton'
import { LocationPath } from 'src/router/types'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

import { useDisplayMode, useTimezone } from '../../Contexts'
import { useCurrentMinute } from '../../utils/useCurrentTime'
import { FiltersToggleButton } from '../Schedule/FiltersToggleButton'
import { MetricsToggleButton } from '../Schedule/MetricsToggleButton'
import {
  ScheduleFilterProviderContext,
  useScheduleFilterContext,
} from '../Schedule/ScheduleFilterContext'
import { ScheduleFilterProvider } from '../Schedule/ScheduleFilterProvider'
import { ScheduleFilters } from '../Schedule/ScheduleFilters'
import { ShowClosedRoomsToggle } from '../Schedule/ShowClosedRoomsToggle'
import { Banner } from './Banner'
import { ASPECT_RATIO } from './consts'
import {
  MAX_TILE_WIDTH,
  MIN_TILE_WIDTH,
  TILE_GAP,
  TILE_INFO_HEIGHT,
} from './cssConsts'
import { useLiveTileContainerSize } from './hooks'
import { calculateNumRowsAndCols, getMaxTileWidth } from './layoutHelpers'
import { LiveTile } from './LiveTile'
import { prioritizeRoomsByStatus, StatusButton } from './StatusHelper'
import { useLiveState } from './useLiveState'

const IMAGE_WIDTH_CLASS_NAME = 'videoImageSize'

const ImgStyles = ({
  imgWidth,
  isSingleColumn,
}: {
  imgWidth: number
  isSingleColumn: boolean
}) =>
  useMemo(
    () => (
      <style>
        {`
          .${IMAGE_WIDTH_CLASS_NAME} {
            flex-basis: 100%;
            ${mediaQueries.lg} {
              flex-basis: ${isSingleColumn ? '100%' : `${imgWidth}px`};
            }
          }
        `}
      </style>
    ),
    [imgWidth, isSingleColumn]
  )

const Live = (): React.JSX.Element => {
  const { timezone } = useTimezone()
  const currentMinute = useCurrentMinute()

  const {
    siteIds,
    viewRoomsState,
    minTime,
    showClosedRooms,
    sortKeys,
    onToggleSortKey,
    roomIds,
    showFilters,
    showMetrics,
    numCols: numColsOverride,
    onChangeNumCols,
  } = useScheduleFilterContext()

  const startOfDay = DateTime.fromISO(minTime)
  const endOfDayISO = currentMinute.setZone(timezone).endOf('day').toISO()

  const { isTvModeEnabled: isFullscreen } = useDisplayMode()

  const eventsLogger = useAnalyticsEventLogger()

  const [snoozedRoomsTimestamps, setSnoozedRoomsTimestamps] =
    useLocalStorageState<Record<string, string>>('liveSnoozedRooms', {})

  const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null)

  const [open, setOpen] = useState(true)
  const roomStatusFilter = showClosedRooms
    ? undefined
    : Object.values(RoomStatusName).filter(
        (status) => status !== RoomStatusName.CLOSED
      )

  const { roomsBySiteId, isLoading } = useLiveState({
    roomStatusFilter,
    siteIds,
    roomIds,
  })

  const currentRooms = useMemo(() => {
    if (!siteIds) {
      return []
    }

    return siteIds
      .sort()
      .flatMap((siteId) => roomsBySiteId[siteId])
      .filter(Boolean)
  }, [siteIds, roomsBySiteId])

  const snoozedRooms = useMemo(() => {
    const minTime = startOfDay
    // Filter out rooms that were snoozed yesterday
    const currentlySnoozedRooms = pickBy(
      snoozedRoomsTimestamps,
      (v) => DateTime.fromISO(v).setZone(timezone) > minTime
    )
    const currentlySnoozedRoomIds = Object.keys(currentlySnoozedRooms)
    const currentSiteRoomsIds = currentRooms.map((r) => r.id)
    return intersection(currentlySnoozedRoomIds, currentSiteRoomsIds)
  }, [startOfDay, snoozedRoomsTimestamps, currentRooms, timezone])

  const cases = useMemo(
    () => Object.values(currentRooms).flatMap((r) => r.cases),
    [currentRooms]
  )

  const scheduleMetrics = useScheduleMetrics({
    apellaCases: cases,
    minTime,
  })

  const roomsToShow = useMemo(
    () =>
      currentRooms.filter(
        (r) =>
          !snoozedRooms.includes(r.id) &&
          (!viewRoomsState || scheduleMetrics[viewRoomsState].includes(r.id))
      ),
    [currentRooms, snoozedRooms, viewRoomsState, scheduleMetrics]
  )

  const snoozeRoom = useCallback(
    (roomId: string, roomName: string) => () => {
      eventsLogger(EVENTS.SNOOZE_LIVE_STREAM, { roomId, roomName })
      setSnoozedRoomsTimestamps({
        ...snoozedRoomsTimestamps,
        [roomId]: DateTime.now().toISO(),
      })
    },
    [snoozedRoomsTimestamps, setSnoozedRoomsTimestamps, eventsLogger]
  )

  const unsnoozeAll = useCallback(
    (isAutomatic: boolean) => {
      eventsLogger(EVENTS.UNSNOOZE_LIVE_STREAMS, { isAutomatic })
      setSnoozedRoomsTimestamps({})
    },
    [setSnoozedRoomsTimestamps, eventsLogger]
  )

  const showBanner = !!snoozedRooms.length && open

  const containerSize = useLiveTileContainerSize(containerRef, {
    showBanner,
    showFilters,
    showMetrics,
  })

  // Unsnooze at midnight. This will work the first midnight, as well as
  // subsequent midnights, because the unsnoozeAll callback changes when you
  // snooze a room and thus the timeout gets reset.
  useEffect(() => {
    const msUntilNextMidnight = DateTime.fromISO(endOfDayISO)
      .diff(DateTime.now())
      .as('milliseconds')
    if (msUntilNextMidnight > 0) {
      const timeoutId = setTimeout(() => {
        unsnoozeAll(true)
      }, msUntilNextMidnight)

      return () => clearTimeout(timeoutId)
    }
  }, [unsnoozeAll, endOfDayISO])

  const { cols: numColsForFitLayout } = useMemo(() => {
    if (!containerSize) return { cols: 1, rows: 1 }
    const size = {
      height:
        window.innerHeight - (containerRef?.offsetTop || 0) - pxSpacing.gutter,
      width: Math.max(containerSize.width, MIN_TILE_WIDTH),
    }
    return calculateNumRowsAndCols(
      size,
      ASPECT_RATIO,
      roomsToShow.length,
      TILE_GAP,
      TILE_GAP,
      TILE_INFO_HEIGHT
    )
  }, [containerSize, roomsToShow.length, containerRef])

  const numCols = useMemo(() => {
    return numColsOverride && numColsOverride < numColsForFitLayout
      ? numColsOverride
      : numColsForFitLayout
  }, [numColsForFitLayout, numColsOverride])

  const numRows = useMemo(() => {
    return Math.ceil(roomsToShow.length / numCols)
  }, [roomsToShow.length, numCols])

  const isFitLayout = useMemo(() => {
    return numCols === numColsForFitLayout
  }, [numCols, numColsForFitLayout])

  const isSingleColumnLayout = useMemo(() => {
    return numCols === 1
  }, [numCols])

  const imgWidth = useMemo(() => {
    if (!containerSize) return 0
    const size = {
      height:
        window.innerHeight - (containerRef?.offsetTop || 0) - pxSpacing.gutter,
      width: Math.max(containerSize.width, MIN_TILE_WIDTH),
    }

    if (numCols !== 1) {
      return Math.floor(
        getMaxTileWidth(
          size,
          ASPECT_RATIO,
          numRows,
          numCols,
          TILE_GAP,
          TILE_GAP,
          TILE_INFO_HEIGHT,
          isFitLayout
        )
      )
    }

    return containerSize.width
  }, [isFitLayout, containerSize, containerRef, numCols, numRows])

  const [turnoverCount, wrapUpCount] = roomsToShow.reduce(
    ([turnoverCount, wrapUpCount], room) => {
      return [
        room.status.liveStatus === RoomStatusName.TURNOVER
          ? turnoverCount + 1
          : turnoverCount,
        room.status.liveStatus === CaseStatusName.WRAP_UP
          ? wrapUpCount + 1
          : wrapUpCount,
      ]
    },
    [0, 0]
  )

  const LiveTiles = useMemo(
    () =>
      prioritizeRoomsByStatus(roomsToShow, sortKeys).map((room) => {
        return (
          <FlexItem
            key={`${room.id}`}
            css={{
              minWidth: MIN_TILE_WIDTH,
              maxWidth: MAX_TILE_WIDTH,
              aspectRatio: `${ASPECT_RATIO.width} / ${ASPECT_RATIO.height}`,
            }}
            className={IMAGE_WIDTH_CLASS_NAME}
          >
            <LiveTile
              room={room}
              snoozeRoom={snoozeRoom(room.id, room.name)}
              imgWidth={imgWidth}
            />
          </FlexItem>
        )
      }),
    [roomsToShow, snoozeRoom, sortKeys, imgWidth]
  )

  const onClickZoomOut = useCallback(() => {
    if (!onChangeNumCols) return

    const newNumCols = numCols + 1 // Increase columns -> Zoom out
    onChangeNumCols(newNumCols === numColsForFitLayout ? undefined : newNumCols)
  }, [numCols, onChangeNumCols, numColsForFitLayout])

  const onClickZoomIn = useCallback(() => {
    onChangeNumCols && onChangeNumCols(numCols - 1) // Decrease columns -> Zoom in
  }, [numCols, onChangeNumCols])

  return (
    <div
      css={{
        height: '100%',
        width: '100%',
        userSelect: 'none',
      }}
    >
      <ImgStyles isSingleColumn={isSingleColumnLayout} imgWidth={imgWidth} />
      <PageContentTemplate
        title={!isFullscreen ? 'Live' : undefined}
        actionGap={remSpacing.xsmall}
        maxContainerSize="full"
        userViewContexts={!isFullscreen ? [UserViewContext.Live] : undefined}
        selectedViewId="live-page"
        views={
          !isFullscreen
            ? [
                {
                  id: 'live-page',
                  display: 'OR',
                  to: generatePath(LocationPath.Live),
                },
              ]
            : undefined
        }
        filters={
          showFilters ? (
            <ScheduleFilters
              showToggleScheduledCases={false}
              showSurgeonFilter={false}
              showDateChangeFilter={false}
              showSiteFilter
              showRoomFilter
            />
          ) : undefined
        }
        actions={
          <>
            <StatusButton
              key={RoomStatusName.TURNOVER}
              status={RoomStatusName.TURNOVER}
              count={turnoverCount}
              isActive={sortKeys.includes(RoomStatusName.TURNOVER)}
              handleClick={() => {
                onToggleSortKey(RoomStatusName.TURNOVER)
              }}
            />
            <StatusButton
              key={CaseStatusName.WRAP_UP}
              status={CaseStatusName.WRAP_UP}
              count={wrapUpCount}
              isActive={sortKeys.includes(CaseStatusName.WRAP_UP)}
              handleClick={() => {
                onToggleSortKey(CaseStatusName.WRAP_UP)
              }}
            />

            <LiveSnoozeBanner
              snoozedRooms={snoozedRooms}
              onUnsnooze={() => unsnoozeAll(false)}
            />
            <ShowClosedRoomsToggle />
            <FiltersToggleButton />
            <MetricsToggleButton />
            <ButtonGroup
              css={{
                display: 'none',
                [mediaQueries.lg]: { display: 'inline' },
              }}
            >
              <Button
                onClick={onClickZoomOut}
                color={'alternate'}
                disabled={isFitLayout}
                buttonType={'icon'}
              >
                <ZoomOut size={'sm'} />
              </Button>
              <Button
                onClick={onClickZoomIn}
                color={'alternate'}
                disabled={isSingleColumnLayout}
                buttonType={'icon'}
              >
                <ZoomIn size={'sm'} />
              </Button>
            </ButtonGroup>
            <div
              css={{
                display: 'none',
                [mediaQueries.md]: { display: 'inline' },
              }}
            >
              <TvModeToggleButton />
            </div>
          </>
        }
      >
        {showBanner && (
          <Banner
            numSnoozedVideo={snoozedRooms.length}
            open={open}
            onChangeOpen={setOpen}
          />
        )}

        <ScheduleMetrics scheduleMetrics={scheduleMetrics} />
        <FlexContainer
          ref={setContainerRef}
          css={{
            height: '100%',
            justifyContent: 'center',
            gap: TILE_GAP,
          }}
          wrap="wrap"
        >
          {isLoading ? <LoadingOverlay /> : LiveTiles}
          {isLoading ? <></> : <ToggleableCaseKey />}
        </FlexContainer>
        <Outlet />
      </PageContentTemplate>
    </div>
  )
}

const LivePage = () => {
  const { timezone } = useTimezone()

  const minTime = DateTime.now()
    .startOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  const maxTime = DateTime.now()
    .endOf('day')
    .setZone(timezone, { keepLocalTime: true })
    .toISO()

  return (
    <ScheduleFilterProvider
      minTime={minTime}
      maxTime={maxTime}
      context={ScheduleFilterProviderContext.LIVE}
    >
      <Live />
    </ScheduleFilterProvider>
  )
}

export default LivePage
