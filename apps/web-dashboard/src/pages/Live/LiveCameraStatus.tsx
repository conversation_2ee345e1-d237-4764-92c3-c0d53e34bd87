import { memo } from 'react'

import { keyframes, useTheme } from '@emotion/react'

import styled from '@emotion/styled'

import { CameraOff, remSpacing, Span3 } from '@apella/component-library'

const pulse = (color: string) => keyframes`
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 ${color}70;
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px ${color}00;
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 ${color}00;
  }
`

const StatusContainer = styled.div({
  display: 'inline-flex',
  alignItems: 'center',
})

const getDotCss = (color: string) => ({
  display: 'inline-block',
  width: 5,
  height: 5,
  borderRadius: '50%',
  transform: 'scale(1)',
  background: color,
  boxShadow: `0 0 0 0 ${color}`,
  animation: `${pulse(color)} 2s infinite`,
})
export const MAX_TILE_WIDTH = 360
interface StatusProps {
  tileWidth?: number
}
const StatusLive = ({ tileWidth }: StatusProps): React.JSX.Element => {
  const theme = useTheme()
  return (
    <StatusContainer>
      <span css={getDotCss(theme.palette.red[50])} />
      {(tileWidth == undefined || tileWidth >= MAX_TILE_WIDTH) && (
        <Span3
          css={{
            color: theme.palette.red[50],
            marginLeft: remSpacing.xsmall,
            whiteSpace: 'nowrap',
          }}
        >
          LIVE
        </Span3>
      )}
    </StatusContainer>
  )
}

const StatusBuffering = ({ tileWidth }: StatusProps): React.JSX.Element => {
  const theme = useTheme()

  return (
    <StatusContainer>
      <div css={getDotCss(theme.palette.yellow[50])} />
      {(tileWidth == undefined || tileWidth >= MAX_TILE_WIDTH) && (
        <Span3
          css={{
            color: theme.palette.yellow[50],
            marginLeft: remSpacing.xsmall,
            whiteSpace: 'nowrap',
          }}
        >
          Buffering
        </Span3>
      )}
    </StatusContainer>
  )
}

const StatusOffline = ({ tileWidth }: StatusProps): React.JSX.Element => {
  const theme = useTheme()
  return (
    <StatusContainer>
      <CameraOff color={theme.palette.orange[50]} size={'xs'} />
      {(tileWidth == undefined || tileWidth >= MAX_TILE_WIDTH) && (
        <Span3
          css={{
            color: theme.palette.orange[50],
            marginLeft: remSpacing.xsmall,
            whiteSpace: 'nowrap',
          }}
        >
          Connection lost
        </Span3>
      )}
    </StatusContainer>
  )
}

export type CameraStatus = 'live' | 'buffering' | 'offline' | 'loading'

export interface LiveCameraStatusProps extends Pick<StatusProps, 'tileWidth'> {
  status: CameraStatus
}

const LiveCameraStatus = memo(function LiveCameraStatus({
  status,
  tileWidth,
}: LiveCameraStatusProps): React.JSX.Element {
  if (status === 'live') {
    return <StatusLive tileWidth={tileWidth} />
  } else if (status === 'buffering') {
    return <StatusBuffering tileWidth={tileWidth} />
  } else if (status === 'offline') {
    return <StatusOffline tileWidth={tileWidth} />
  } else {
    return <></>
  }
})

export default LiveCameraStatus
