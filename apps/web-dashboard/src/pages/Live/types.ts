import { DateTime } from 'luxon'

import {
  CaseType,
  RoomStatusName,
  TurnoverType,
} from 'src/__generated__/globalTypes'
import { InsightApellaCase } from 'src/modules/daily-metrics/hooks/useScheduleMetrics'

import {
  LiveStatus,
  TurnoverLengthWithGoal,
  TurnoverLengthWithoutGoal,
} from '../../utils/status'
import { LiveEvent, Staff } from '../types'
import { GetLiveRooms } from './__generated__'

export interface Camera {
  id: string
  name: string
}

export interface LiveApellaCase {
  endTime?: DateTime
  id: string
  isAddOn?: boolean | null
  primaryProcedures: string[]
  primarySurgeons: Staff[]
  startTime: DateTime
  type: CaseType
}

interface LiveTurnover {
  currentLengthStatus: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
  endTime: DateTime
  followingCase: LiveApellaCase
  id: string
  overallLengthStatus: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
  startTime: DateTime
  type: TurnoverType
}

export interface LiveRoomStatus {
  inProgressApellaCase?: LiveApellaCase
  inProgressTurnover?: LiveTurnover
  liveStatus: LiveStatus
  name: RoomStatusName
  nextCase?: LiveApellaCase
  since: DateTime
}

export interface LiveRoom {
  cases: InsightApellaCase[]
  id: string
  mostRecentEvent: LiveEvent | null
  name: string
  siteId: string
  status: LiveRoomStatus
  turnoverGoal: number | null
}

export type Site = GetLiveRooms['sites']['edges'][number]
