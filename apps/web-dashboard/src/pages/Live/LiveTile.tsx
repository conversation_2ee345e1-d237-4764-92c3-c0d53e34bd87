import { useCallback, useEffect, useMemo, useState } from 'react'

import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime } from 'luxon'

import {
  ApellaDateTimeFormats,
  Caps3,
  EllidedText,
  EventCircle,
  fontWeights,
  formatDuration,
  P4,
  remSpacing,
  shape,
  Span2,
  Tooltip,
} from '@apella/component-library'
import { Size, useSize } from '@apella/hooks'

import { RoomStatusName } from '../../__generated__/globalTypes'
import { PrivacyReason } from '../../hooks/useRoomUpdate'
import { WebDashboardFeatureFlagSet } from '../../modules/feature/types'
import { EVENTS, useAnalyticsEventLogger } from '../../utils/analyticsEvents'
import {
  useBestCameraForRoom,
  useRoomCameras,
  useRoomName,
  useRoomSiteId,
  useSiteName,
} from '../../utils/hooks'
import { getAnalyticsAddtlTurnoverData } from '../../utils/turnovers'
import { useCurrentMinute } from '../../utils/useCurrentTime'
import { useOpenVideoBlade } from '../../utils/useOpenVideoBlade'
import { AddOnChecker } from '../AddOnChecker'
import { LiveEvent } from '../types'
import { LIVE_POLL_INTERVAL_MS } from './consts'
import { TILE_ABOVE_HEIGHT, TILE_BELOW_HEIGHT } from './cssConsts'
import { useCameraStatus, useMostRecentImage } from './hooks'
import LiveCameraStatus, { CameraStatus } from './LiveCameraStatus'
import LiveImage, { LiveImageProps } from './LiveImage'
import { LiveStatusBanner } from './LiveStatusBanner'
import { LiveRoom } from './types'

export const LiveTile = ({
  room,
  snoozeRoom,
  imgWidth,
}: {
  room: LiveRoom
  snoozeRoom: () => void
  imgWidth: number
}) => {
  const eventsLogger = useAnalyticsEventLogger()
  const now = useCurrentMinute()
  const { blurVideoPlayer, ehrEnabled } = useFlags<WebDashboardFeatureFlagSet>()
  const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null)
  const containerSize = useSize(containerRef)
  const openVideoBlade = useOpenVideoBlade({ appendParams: true })

  const { id: roomId, status, turnoverGoal } = room

  const roomName = useRoomName(roomId)
  const { siteId } = useRoomSiteId(roomId)
  const siteName = useSiteName(siteId)
  const { cameras } = useRoomCameras(roomId)
  const [cameraId, setCameraId] = useBestCameraForRoom(roomId, cameras)
  const {
    mostRecentImage,
    privacyEnabled,
    refetch: refetchImg,
    startPolling: startPollingImg,
    stopPolling: stopPollingImg,
  } = useMostRecentImage(cameraId)

  const mostRecentEvent = room.mostRecentEvent ?? null
  const cameraStatus: CameraStatus = useCameraStatus(
    mostRecentImage?.captureTime
  )

  const rootIntersectionObserver = useMemo(
    () =>
      new IntersectionObserver((entries) => {
        entries.forEach((e) => {
          if (e.isIntersecting) {
            refetchImg()
            startPollingImg(LIVE_POLL_INTERVAL_MS)
          } else {
            stopPollingImg()
          }
        })
      }),
    [refetchImg, startPollingImg, stopPollingImg]
  )

  useEffect(() => {
    if (containerRef) {
      rootIntersectionObserver.observe(containerRef)
    }
  }, [containerRef, rootIntersectionObserver])

  const [controls, setControls] = useState<LiveImageProps['controls']>()
  const onMouseEnter = useCallback(() => setControls('visible'), [])
  const onMouseLeave = useCallback(() => setControls(undefined), [])

  const inProgressCase = room.status.inProgressApellaCase
  const inProgressTurnover = room.status.inProgressTurnover

  const getEventContext = useCallback(() => {
    const lastSeenEventSeconds = mostRecentEvent?.startTime
      ? now.diff(DateTime.fromISO(mostRecentEvent.startTime)).as('seconds')
      : undefined
    const lastImageCaptureSeconds = mostRecentImage?.captureTime
      ? now.diff(mostRecentImage.captureTime).as('seconds')
      : undefined

    return {
      roomId: roomId,
      roomName: roomName,
      cameraId: cameraId,
      cameraName: cameras.find((c) => c.id === cameraId)?.name,
      liveStatus: room.status.liveStatus,
      lastSeenEvent: mostRecentEvent?.name,
      lastSeenEventSeconds,
      lastImageCaptureSeconds,
      ...getAnalyticsAddtlTurnoverData(inProgressTurnover),
    }
  }, [
    mostRecentEvent,
    now,
    mostRecentImage,
    roomId,
    roomName,
    cameraId,
    cameras,
    room.status.liveStatus,
    inProgressTurnover,
  ])

  const onChangeCamera = useCallback(
    (cId: string) => {
      eventsLogger(EVENTS.CHANGE_LIVE_CAMERA, {
        ...getEventContext(),
        newCameraId: cId,
        newCameraName: cameras.find((c) => c.id === cId)?.name,
      })
      setCameraId(cId)
    },
    [setCameraId, cameras, getEventContext, eventsLogger]
  )

  const onOpen = useCallback(() => {
    openVideoBlade(roomId, {
      apellaCase: inProgressCase,
      turnover: inProgressTurnover,
      analyticsEvent: EVENTS.EXPAND_LIVE_STREAM,
      analyticsAddtlData: getEventContext(),
    })
  }, [
    roomId,
    inProgressCase,
    inProgressTurnover,
    openVideoBlade,
    getEventContext,
  ])

  return (
    <div
      css={{
        boxShadow: '0px 1px 18px 0px rgba(0, 0, 0, 0.12)',
        borderRadius: shape.borderRadius.small,
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div css={{ height: TILE_ABOVE_HEIGHT }}>
        {ehrEnabled && room.status && (
          <LiveStatusBanner
            liveStatus={room.status.liveStatus}
            since={room.status.since}
            turnoverGoal={turnoverGoal}
            isOnLive={true}
          />
        )}
      </div>
      <div
        ref={setContainerRef}
        css={{
          overflow: 'hidden',
          cursor: 'pointer',
          position: 'relative',
        }}
        onClick={onOpen}
      >
        <LiveImage
          image={mostRecentImage?.url}
          blurCenter={blurVideoPlayer}
          privacyReason={
            privacyEnabled ? PrivacyReason.PRIVACY_ENABLED : undefined
          }
          cameras={cameras}
          activeCameraId={cameraId}
          setCameraId={onChangeCamera}
          controls={controls}
          snoozeRoom={snoozeRoom}
          cameraStatus={cameraStatus}
          keyboardShortcutEnabled={false}
          roomId={roomId}
          roomStatus={status}
          turnoverGoal={turnoverGoal}
          imgWidth={imgWidth}
          overlay={
            <LiveTileTextOverlay
              roomName={roomName}
              cameraStatus={cameraStatus}
              containerSize={containerSize}
              siteName={siteName}
            />
          }
        />
      </div>
      <div
        css={{
          height: TILE_BELOW_HEIGHT,
          padding: `${remSpacing.xsmall} ${remSpacing.small}`,
        }}
      >
        <LiveCaseDetails room={room} containerSize={containerSize} />
      </div>
    </div>
  )
}

const LiveCaseDetailsRow = styled.div(() => ({
  display: 'flex',
  gap: remSpacing.medium,
  alignItems: 'center',
  justifyContent: 'space-between',
  overflow: 'hidden',
  height: '1rem',
}))

const LiveCaseDetails = ({
  room,
  containerSize,
}: {
  room: LiveRoom
  containerSize?: Size
}) => {
  if (
    room.status.name === RoomStatusName.IN_CASE &&
    room.status.inProgressApellaCase
  ) {
    const liveCase = room.status.inProgressApellaCase
    const mostRecentEvent = room.mostRecentEvent

    const primarySurgeonText = liveCase.primarySurgeons
      .map((ps) => ps.displayName)
      .join('; ')
    const primaryProcedureText = liveCase.primaryProcedures.join('; ')
    return (
      <div>
        <LiveCaseDetailsRow>
          <EllidedText
            css={{
              flexShrink: 1,
            }}
          >
            <Caps3 css={fontWeights.semibold} title={primarySurgeonText}>
              {primarySurgeonText}
            </Caps3>
          </EllidedText>
          <LiveEventDisplay
            mostRecentEvent={mostRecentEvent}
            containerSize={containerSize}
          />
        </LiveCaseDetailsRow>
        <LiveCaseDetailsRow>
          <EllidedText
            css={{
              flexShrink: 1,
            }}
          >
            <Caps3 title={primaryProcedureText}>
              {liveCase?.isAddOn && '++ '}
              {primaryProcedureText}
            </Caps3>
          </EllidedText>
          <LiveEventDuration mostRecentEvent={mostRecentEvent} />
        </LiveCaseDetailsRow>
      </div>
    )
  } else if (
    (room.status.name === RoomStatusName.TURNOVER ||
      room.status.name === RoomStatusName.IDLE) &&
    room.status.nextCase
  ) {
    const nextCase = room.status.nextCase

    const primarySurgeonText = nextCase.primarySurgeons
      .map((ps) => ps.displayName)
      .join('; ')
    const primaryProcedureText = nextCase.primaryProcedures.join('; ')

    return (
      <div
        css={{
          display: 'flex',
          gap: remSpacing.medium,
          overflow: 'hidden',
        }}
      >
        <div css={{ flexShrink: 0, whiteSpace: 'nowrap' }}>
          <Caps3
            css={{
              display: 'block',
            }}
          >
            Next case
          </Caps3>
          <Caps3 css={{ display: 'block', ...fontWeights.semibold }}>
            {nextCase.startTime.toLocaleString(ApellaDateTimeFormats.TIME)}
          </Caps3>
        </div>
        <EllidedText
          css={{
            flexShrink: 1,
          }}
        >
          <Caps3
            css={{
              display: 'block',
              ...fontWeights.semibold,
            }}
            title={primarySurgeonText}
          >
            {primarySurgeonText}
          </Caps3>
          <Caps3 title={primaryProcedureText}>
            {AddOnChecker(
              primaryProcedureText,
              nextCase.isAddOn ?? false,
              false
            )}
          </Caps3>
        </EllidedText>
      </div>
    )
  } else {
    const mostRecentEvent = room.mostRecentEvent
    return (
      <div css={{ display: 'flex', justifyContent: 'flex-end' }}>
        <div
          css={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-end',
            flexShrink: 0,
          }}
        >
          <LiveEventDisplay mostRecentEvent={mostRecentEvent} />
          <LiveEventDuration mostRecentEvent={mostRecentEvent} />
        </div>
      </div>
    )
  }
}

const LiveEventDuration = ({
  mostRecentEvent,
}: {
  mostRecentEvent: LiveEvent | null
}) => {
  const currentMinute = useCurrentMinute()

  return mostRecentEvent ? (
    <P4 css={{ ...fontWeights.semibold }}>
      {formatDuration(
        currentMinute.diff(DateTime.fromISO(mostRecentEvent.startTime))
      )}
    </P4>
  ) : (
    <></>
  )
}

const LiveEventDisplay = ({
  mostRecentEvent,
  containerSize,
}: {
  mostRecentEvent: LiveEvent | null
  containerSize?: Size
}) => {
  const theme = useTheme()

  if (!mostRecentEvent) {
    return <></>
  }

  const bigEnoughToShowEventName = (containerSize?.width ?? 216) > 215

  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.xxsmall,
        color: theme.palette.gray[60],
        whiteSpace: 'nowrap',
      }}
    >
      {bigEnoughToShowEventName ? (
        <>
          <EventCircle
            color={mostRecentEvent.attrs?.color || theme.palette.gray[50]}
            style={{
              transform: 'scale(0.7)',
            }}
          />
          <P4>{mostRecentEvent.attrs?.name}</P4>
        </>
      ) : (
        <Tooltip body={<P4>{mostRecentEvent.attrs?.name}</P4>}>
          <EventCircle
            color={mostRecentEvent.attrs?.color || theme.palette.gray[50]}
            style={{
              transform: 'scale(0.7)',
            }}
          />
        </Tooltip>
      )}
    </div>
  )
}

const LiveTileTextOverlay = ({
  roomName,
  siteName,
  cameraStatus,
  containerSize,
}: {
  roomName?: string
  cameraStatus: CameraStatus
  containerSize?: Size
  siteName?: string
}) => {
  const theme = useTheme()
  return (
    <>
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-end',
          gap: remSpacing.medium,
        }}
      >
        <div
          css={{
            color: theme.palette.text.alternate,
          }}
        >
          <Span2 css={{ ...fontWeights.semibold }}>{roomName}</Span2>
          <Span2>&nbsp;{siteName}</Span2>
        </div>
        <div>
          <LiveCameraStatus
            status={cameraStatus}
            tileWidth={containerSize?.width}
          />
        </div>
      </div>
    </>
  )
}
