import { fontWeights } from '@apella/component-library'

import { LiveEvent } from '../types'
import { useLivePhaseInfo } from './hooks'

export interface LivePhaseTextProps {
  mostRecentEvent: LiveEvent | null
}

const LivePhaseText = function LivePhaseText({
  mostRecentEvent,
}: LivePhaseTextProps): React.JSX.Element {
  const livePhaseInfo = useLivePhaseInfo(mostRecentEvent)

  if (livePhaseInfo.timeElapsed) {
    return (
      <>
        {livePhaseInfo.label}:{' '}
        <span css={{ ...fontWeights.semibold }}>
          {livePhaseInfo.timeElapsed} ago
        </span>
      </>
    )
  }

  return <>{livePhaseInfo.label}</>
}

export default LivePhaseText
