import { useCallback } from 'react'

import _, { lowerCase } from 'lodash'
import { rem } from 'polished'

import {
  Button,
  H6,
  remSpacing,
  shape,
  Tooltip,
} from '@apella/component-library'
import { CaseStatusName, RoomStatusName } from 'src/__generated__/globalTypes'
import { formatStatus, LiveStatus, useStatusStyles } from 'src/utils/status'

import { LiveRoom } from './types'

export interface StatusMapProps {
  count: number
  description: string
  label: string
  name: RoomStatusName.TURNOVER | CaseStatusName.WRAP_UP
}

export const prioritizeRoomsByStatus = (
  roomsToShow: LiveRoom[],
  sortKeys: LiveStatus[]
) => {
  return _.sortBy(roomsToShow, [
    (room) => {
      const priority = sortKeys.indexOf(room.status.liveStatus)
      return priority === -1 ? Number.MAX_SAFE_INTEGER : priority
    },
    (room) => room.siteId,
  ])
}

export const StatusButton = ({
  status,
  count,
  isActive,
  handleClick,
}: {
  status: LiveStatus
  count: number
  isActive: boolean
  handleClick: (name: string) => void
}) => {
  const statusStyles = useStatusStyles()
  const label = formatStatus(status)
  const backgroundColor = statusStyles(status).backgroundColor
  const description = `Highlight rooms with cases currently in ${lowerCase(label)}`

  const onClick = useCallback(() => handleClick(status), [handleClick, status])

  return (
    <Tooltip body={description} placement="bottom">
      <Button
        color={isActive ? 'active-gray' : 'alternate'}
        buttonType="icon"
        onClick={onClick}
        css={{ padding: `${rem('2px')} ${remSpacing.xsmall}` }}
      >
        {label}
        <span
          css={{
            backgroundColor: backgroundColor,
            padding: `0 ${rem('6px')}`,
            borderRadius: shape.borderRadius.xxsmall,
            margin: `${remSpacing.xsmall} 0`,
          }}
        >
          <H6>{count}</H6>
        </span>
      </Button>
    </Tooltip>
  )
}
