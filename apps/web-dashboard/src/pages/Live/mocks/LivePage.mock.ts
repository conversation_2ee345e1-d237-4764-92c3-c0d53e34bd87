import {
  anApellaCase,
  anApellaCaseConnection,
  anApellaCaseEdge,
  anEvent,
  anEventConnection,
  anEventEdge,
  anEventType,
  anOrganization,
  anOrganizationConnection,
  anOrganizationEdge,
  aRoom,
  aRoomConnection,
  aRoomEdge,
  aScheduledCase,
  aSite,
  aSiteConnection,
  aSiteEdge,
  aUser,
} from 'src/__generated__/generated-mocks'
import { CaseType } from 'src/__generated__/globalTypes'
import { GET_CURRENT_USER } from 'src/modules/user/queries'
import { GetLiveRooms } from 'src/pages/Live/__generated__'
import {
  GET_ROOM_DETAILS,
  GET_ROOM_NAME,
  GET_ROOM_SITE_ID,
  GET_SITE_DETAILS,
} from 'src/pages/queries'

import { GET_LIVE_ROOMS } from '../queries'

const userData = {
  data: {
    me: aUser({
      id: 'google-apps|<EMAIL>',
      organizations: anOrganizationConnection({
        edges: [
          anOrganizationEdge({
            node: anOrganization({
              id: 'apella_internal_0',
              auth0OrgId: 'apella_internal_0',
            }),
          }),
        ],
      }),
    }),
  },
}
const getUserData = vi.fn(() => userData)

const garage0Details = {
  data: {
    room: aRoom({
      name: 'Garage 0',
      defaultCamera: null,
    }),
  },
}
const getGarage0Details = vi.fn(() => garage0Details)

const garage3Details = {
  data: {
    room: aRoom({
      name: 'Garage 3',
      defaultCamera: null,
    }),
  },
}
const getGarage3Details = vi.fn(() => garage3Details)

const garage1Details = {
  data: {
    room: aRoom({
      name: 'Garage 1',
      defaultCamera: null,
    }),
  },
}
const getGarage1Details = vi.fn(() => garage1Details)

const garage2Details = {
  data: {
    room: aRoom({
      name: 'Garage 2',
      defaultCamera: null,
    }),
  },
}
const getGarage2Details = vi.fn(() => garage2Details)

const siteDetails = {
  data: {
    site: null,
  },
}
const getSiteDetails = vi.fn(() => siteDetails)

export const garage1MostRecentEvent = anEvent({
  id: '7b69589f-0198-413e-9508-4d9a4ece10f4',
  name: 'anesthesia_undraping',
  startTime: '2023-12-19T16:34:01.917213+00:00',
  attrs: anEventType({
    id: 'anesthesia_undraping',
    name: 'Anesthesia undraping',
    color: 'green',
  }),
})

export const garage2MostRecentEvent = anEvent({
  id: '3a9dd40e-366f-4928-8e92-d6c913b022bc',
  name: 'patient_wheels_out',
  startTime: '2023-12-19T18:29:55.549246+00:00',
  attrs: anEventType({
    id: 'patient_wheels_out',
    name: 'Patient wheels out',
    color: 'green',
  }),
})

const otherEvent = anEvent({
  id: '21b9a2f1-d842-45fd-8238-4eb2c0984ae4',
  name: 'patient_wheels_in',
  startTime: '2023-12-19T16:24:33.970688+00:00',
  attrs: anEventType({
    id: 'patient_wheels_in',
    name: 'Patient wheels in',
    color: 'green',
  }),
})

const garage1Cases = anApellaCaseConnection({
  edges: [
    anApellaCaseEdge({
      node: anApellaCase({
        id: 'case:be58964e-3196-4026-b034-7ce268658de7',
        type: CaseType.COMPLETE,
        startTime: '2023-12-19T16:13:48.327083+00:00',
        endTime: '2023-12-19T17:07:30.259089+00:00',
        case: aScheduledCase({
          id: 'be58964e-3196-4026-b034-7ce268658de7',
          scheduledStartTime: '2023-12-19T16:11:36.588099+00:00',
          scheduledEndTime: '2023-12-19T17:16:36.588099+00:00',
          isFirstCase: true,
          isInFlipRoom: false,
          isAddOn: null,
          patientClass: null,
          externalCaseId: 'load-generator_463e39ee-23e6-4f70-a99c-98392646a05b',
          precedingCase: null,
          caseClassificationType: null,
        }),
      }),
    }),
    anApellaCaseEdge({
      node: anApellaCase({
        id: 'case:1621deea-c978-48fb-a2c5-413bfe03f903',
        type: CaseType.COMPLETE,
        startTime: '2023-12-19T17:19:59.081236+00:00',
        endTime: '2023-12-19T18:27:39.745817+00:00',
        case: aScheduledCase({
          id: '1621deea-c978-48fb-a2c5-413bfe03f903',
          scheduledStartTime: '2023-12-19T17:27:02.111210+00:00',
          scheduledEndTime: '2023-12-19T18:56:02.111210+00:00',
          isFirstCase: false,
          isInFlipRoom: false,
          isAddOn: null,
          patientClass: null,
          externalCaseId: 'load-generator_713c315e-13b7-441d-ad6e-c3d7ed49eec0',
          precedingCase: null,
          caseClassificationType: null,
        }),
      }),
    }),
    anApellaCaseEdge({
      node: anApellaCase({
        id: 'case:cbddfa32-d656-44b6-ad8b-c4d7a369a210',
        type: CaseType.LIVE,
        startTime: '2023-12-19T19:07:07.884899+00:00',
        endTime: '2023-12-19T20:19:07.884899+00:00',
        case: aScheduledCase({
          id: 'cbddfa32-d656-44b6-ad8b-c4d7a369a210',
          scheduledStartTime: '2023-12-19T19:08:00.649723+00:00',
          scheduledEndTime: '2023-12-19T20:20:00.649723+00:00',
          isFirstCase: false,
          isInFlipRoom: false,
          isAddOn: null,
          patientClass: null,
          externalCaseId: 'load-generator_4e7e59e5-4f9e-4088-90fe-8933d8b974b5',
          precedingCase: null,
          caseClassificationType: null,
        }),
      }),
    }),
    anApellaCaseEdge({
      node: anApellaCase({
        id: 'case:c955ce16-f821-4725-876f-f7052adecc3c',
        type: CaseType.FORECAST,
        startTime: '2023-12-19T20:33:32.880346+00:00',
        endTime: '2023-12-19T22:15:32.880346+00:00',
        case: aScheduledCase({
          id: 'c955ce16-f821-4725-876f-f7052adecc3c',
          scheduledStartTime: '2023-12-19T20:33:32.880346+00:00',
          scheduledEndTime: '2023-12-19T22:15:32.880346+00:00',
          isFirstCase: null,
          isInFlipRoom: null,
          isAddOn: null,
          patientClass: null,
          externalCaseId: 'load-generator_addb8bf9-29e6-493b-a4ba-03f16080ad5c',
          precedingCase: null,
          caseClassificationType: null,
        }),
      }),
    }),
    anApellaCaseEdge({
      node: anApellaCase({
        id: 'case:bca4edd3-9af2-4cb9-9af2-de9b3a7fffd4',
        type: CaseType.FORECAST,
        startTime: '2023-12-19T22:31:56.640075+00:00',
        endTime: '2023-12-19T23:41:56.640075+00:00',
        case: aScheduledCase({
          id: 'bca4edd3-9af2-4cb9-9af2-de9b3a7fffd4',
          scheduledStartTime: '2023-12-19T22:31:56.640075+00:00',
          scheduledEndTime: '2023-12-19T23:41:56.640075+00:00',
          isFirstCase: false,
          isInFlipRoom: false,
          isAddOn: null,
          patientClass: null,
          externalCaseId: 'load-generator_c3317c3f-a72a-4ad6-961a-18ebcf596c85',
          precedingCase: null,
          caseClassificationType: null,
        }),
      }),
    }),
  ],
})

const garage2Cases = anApellaCaseConnection({
  edges: [
    anApellaCaseEdge({
      node: anApellaCase({
        id: 'phase:2968650c-62ca-4d07-b249-f13268ec9f1b',
        type: CaseType.COMPLETE,
        startTime: '2023-12-19T16:09:43.137761+00:00',
        endTime: '2023-12-19T17:31:52.101893+00:00',
        case: null,
      }),
    }),
    anApellaCaseEdge({
      node: anApellaCase({
        id: 'phase:ceecbe56-d1a7-41fb-a455-a9b21f95fab3',
        type: CaseType.LIVE,
        startTime: '2023-12-19T18:24:33.970688+00:00',
        endTime: null,
        case: null,
      }),
    }),
  ],
})

const garage3Cases = anApellaCaseConnection({
  edges: [
    anApellaCaseEdge({
      node: anApellaCase({
        id: 'case:2968650c-62ca-4d07-b249-f13268ec9f1b',
        type: CaseType.FORECAST,
        startTime: '2023-12-19T18:09:43.137761+00:00',
        endTime: '2023-12-19T20:31:52.101893+00:00',
        case: aScheduledCase({
          id: '2968650c-62ca-4d07-b249-f13268ec9f1b',
          scheduledStartTime: '2023-12-19T16:09:43.137761+00:00',
          scheduledEndTime: '2023-12-19T18:31:52.101893+00:00',
          isFirstCase: null,
          isInFlipRoom: null,
          isAddOn: null,
          patientClass: null,
          externalCaseId: 'load-generator_463e39ee-23e6-4f70-a99c-98392646a05b',
          precedingCase: null,
          caseClassificationType: null,
        }),
      }),
    }),
  ],
})

const liveRooms: { data: GetLiveRooms } = {
  data: {
    sites: aSiteConnection({
      edges: [
        aSiteEdge({
          node: aSite({
            id: 'lab_1',
            name: 'Apella Lab',
            rooms: aRoomConnection({
              edges: [
                aRoomEdge({
                  node: aRoom({
                    id: 'garage_2',
                    name: 'Garage 2',
                    roomEvents: anEventConnection({
                      edges: [
                        anEventEdge({
                          node: garage2MostRecentEvent,
                        }),
                        anEventEdge({
                          node: otherEvent,
                        }),
                      ],
                    }),
                    apellaCases: garage2Cases,
                  }),
                }),
                aRoomEdge({
                  node: aRoom({
                    id: 'garage_1',
                    name: 'Garage 1',
                    roomEvents: anEventConnection({
                      edges: [
                        anEventEdge({
                          node: garage1MostRecentEvent,
                        }),
                        anEventEdge({
                          node: otherEvent,
                        }),
                      ],
                    }),
                    apellaCases: garage1Cases,
                  }),
                }),
                aRoomEdge({
                  node: aRoom({
                    id: 'garage_0',
                    name: 'Garage 0',
                    roomEvents: anEventConnection({
                      edges: [],
                    }),
                  }),
                }),
                aRoomEdge({
                  node: aRoom({
                    id: 'garage_3',
                    name: 'Garage 3',
                    roomEvents: anEventConnection({
                      edges: [],
                    }),
                    apellaCases: garage3Cases,
                  }),
                }),
              ],
            }),
          }),
        }),
      ],
    }),
  },
}
const getLiveRooms = vi.fn(() => liveRooms)

export const LiveMocks = [
  {
    request: {
      query: GET_CURRENT_USER,
      variables: {},
    },
    result: getUserData,
    newData: getUserData,
  },
  {
    request: {
      variables: {
        roomId: 'garage_1',
      },
      query: GET_ROOM_DETAILS,
    },
    result: getGarage1Details,
    newData: getGarage1Details,
  },
  {
    request: {
      variables: {
        siteId: 'garage_1',
      },
      query: GET_SITE_DETAILS,
    },
    result: getSiteDetails,
    newData: getSiteDetails,
  },
  {
    request: {
      variables: {
        roomId: 'garage_1',
      },
      query: GET_ROOM_NAME,
    },
    result: getGarage1Details,
    newData: getGarage1Details,
  },
  {
    request: {
      variables: {
        siteId: 'garage_1',
      },
      query: GET_ROOM_SITE_ID,
    },
    result: getSiteDetails,
    newData: getSiteDetails,
  },
  {
    request: {
      variables: {
        roomId: 'garage_2',
      },
      query: GET_ROOM_DETAILS,
    },
    result: getGarage2Details,
    newData: getGarage2Details,
  },
  {
    request: {
      variables: {
        siteId: 'garage_2',
      },
      query: GET_SITE_DETAILS,
    },
    result: getSiteDetails,
    newData: getSiteDetails,
  },
  {
    request: {
      variables: {
        roomId: 'garage_2',
      },
      query: GET_ROOM_NAME,
    },
    result: getGarage2Details,
    newData: getGarage2Details,
  },
  {
    request: {
      variables: {
        siteId: 'garage_2',
      },
      query: GET_ROOM_SITE_ID,
    },
    result: getSiteDetails,
    newData: getSiteDetails,
  },
  {
    request: {
      variables: {
        roomId: 'garage_0',
      },
      query: GET_ROOM_DETAILS,
    },
    result: getGarage0Details,
    newData: getGarage0Details,
  },
  {
    request: {
      variables: {
        siteId: 'garage_0',
      },
      query: GET_SITE_DETAILS,
    },
    result: getSiteDetails,
    newData: getSiteDetails,
  },
  {
    request: {
      variables: {
        roomId: 'garage_0',
      },
      query: GET_ROOM_NAME,
    },
    result: getGarage0Details,
    newData: getGarage0Details,
  },
  {
    request: {
      variables: {
        siteId: 'garage_0',
      },
      query: GET_ROOM_SITE_ID,
    },
    result: getSiteDetails,
    newData: getSiteDetails,
  },
  {
    request: {
      variables: {
        roomId: 'garage_3',
      },
      query: GET_ROOM_DETAILS,
    },
    result: getGarage3Details,
    newData: getGarage3Details,
  },
  {
    request: {
      variables: {
        siteId: 'garage_3',
      },
      query: GET_SITE_DETAILS,
    },
    result: getSiteDetails,
    newData: getSiteDetails,
  },
  {
    request: {
      variables: {
        roomId: 'garage_3',
      },
      query: GET_ROOM_NAME,
    },
    result: getGarage3Details,
    newData: getGarage3Details,
  },
  {
    request: {
      variables: {
        siteId: 'garage_3',
      },
      query: GET_ROOM_SITE_ID,
    },
    result: getSiteDetails,
    newData: getSiteDetails,
  },
  {
    request: {
      query: GET_LIVE_ROOMS,
      variables: {
        minTime: '2023-12-19T00:00:00.000-08:00',
        maxTime: '2023-12-19T23:59:59.999-08:00',
        siteIds: ['lab_1'],
        statusFilter: undefined,
      },
    },
    result: getLiveRooms,
    newData: getLiveRooms,
  },
]
