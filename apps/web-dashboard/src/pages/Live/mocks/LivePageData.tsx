import {
  aRoom,
  aRoomConnection,
  aRoomEdge,
  aSite,
  aSiteConnection,
  aSiteEdge,
} from 'src/__generated__/generated-mocks'
import {
  CaseStatusName,
  CaseType,
  RoomStatusName,
  TurnoverType,
} from 'src/__generated__/globalTypes'

import { GetLiveRooms } from '../__generated__'

interface MockedData {
  data: GetLiveRooms
}

export const LivePageData: MockedData = {
  data: {
    sites: {
      edges: [
        {
          node: {
            id: 'lab_1',
            name: 'Apella Lab',
            turnoverGoals: {
              goalMinutes: 35,
              maxMinutes: 80,
              __typename: 'TurnoverGoals',
            },
            rooms: {
              edges: [
                {
                  node: {
                    id: 'AT-DEV-OR1',
                    sortKey: null,
                    name: 'Test',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T00:00:57-07:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or1-test',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or1-test',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_0',
                    sortKey: null,
                    name: 'Garage 0-test',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T16:38:30.642843+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:129607f7-06c8-4c6b-81f4-6213b80b8fb7',
                        startTime: '2024-10-30T16:38:30.642843+00:00',
                        endTime: '2024-10-30T17:58:59.764140+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary Surgeon',
                              staff: {
                                id: '3008c147-f8b4-4b07-9e58-49b4fb333c77',
                                firstName: 'Miranda',
                                lastName: 'Wu',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                name: 'Knee Arthroplasty JRP - Total Replacement',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: null,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.WRAP_UP,
                          since: '2024-10-30T17:45:01.287581+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: {
                        id: 'case:aa9f9cb8-407a-42c6-a197-bfedd230e22f',
                        startTime: '2024-10-30T18:34:26.435105+00:00',
                        endTime: '2024-10-30T19:01:37.235105+00:00',
                        type: CaseType.FORECAST,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary Surgeon',
                              staff: {
                                id: '3008c147-f8b4-4b07-9e58-49b4fb333c77',
                                firstName: 'Miranda',
                                lastName: 'Wu',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '12c6a767-6706-4abf-a244-441d3f475c31',
                                name: 'Colonoscopy',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                            {
                              procedure: {
                                id: '4513ffd8-6fdc-4408-9bc1-a3a52184bd84',
                                name: 'I & D Abdomen',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: null,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '6e367f51-7834-425b-ac30-5ffe69b425c6',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T17:45:01.287581+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0d4a7320-db95-4aa4-b5dd-a8c18154b5b8',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:31:12.328212+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'a4c2b277-0cc1-4c61-8e1b-fccbb75a245b',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T17:16:39.986637+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3d965276-49f1-4c28-896b-539ceea39dd8',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:03:53.231022+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'cb501af8-f1ce-4405-b1a8-80084a9e5387',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T16:50:52.428036+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'da3f4f40-854b-419a-9c42-275daaab49a8',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T16:38:30.642843+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ac25937a-db0f-4cdf-8831-eb57c65393cc',
                            name: 'back_table_open',
                            startTime: '2024-10-30T16:17:29.494214+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table open',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b3db58b6-4056-410f-abab-472bcf66efa4',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:11:07.067852+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Patient wheels out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'dccf58db-3719-4691-86b4-8e7be7e58020',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:03:26.683475+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Bed occupied',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e8703eed-6149-42c3-a746-0cd36548470c',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T15:55:47.842456+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '2045c75b-9608-4a5f-bd98-947027e3b389',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:47:37.076391+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '15afbb73-defe-410a-919d-6203500079b7',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T15:40:57.485485+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '37d913aa-51f2-433a-b7b7-607b724b426f',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:34:33.596489+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '75f7fc02-f000-496c-ad4d-5142dae3233d',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T15:27:53.465392+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'aa17051c-8469-4d8c-a15e-0bb14f7eb9e3',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T15:18:08.860670+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b81b9d8c-044a-4ebc-b241-715c04d4fc68',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T11:57:44.654637+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning end',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ab672ee8-cbae-4d74-b2d1-cf14edb869fb',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T11:41:38.815796+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning start',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:ac271762-312d-4bfd-988a-36e8304462e3',
                            startTime: '2024-10-30T15:18:08.860670+00:00',
                            endTime: '2024-10-30T16:11:07.067852+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: 'ac271762-312d-4bfd-988a-36e8304462e3',
                              isFirstCase: null,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T15:11:17.083276+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:129607f7-06c8-4c6b-81f4-6213b80b8fb7',
                            startTime: '2024-10-30T16:38:30.642843+00:00',
                            endTime: '2024-10-30T17:58:59.764140+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: '129607f7-06c8-4c6b-81f4-6213b80b8fb7',
                              isFirstCase: null,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T16:37:48.749939+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:aa9f9cb8-407a-42c6-a197-bfedd230e22f',
                            startTime: '2024-10-30T18:34:26.435105+00:00',
                            endTime: '2024-10-30T19:01:37.235105+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: 'aa9f9cb8-407a-42c6-a197-bfedd230e22f',
                              isFirstCase: null,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T18:31:18.035105+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:868ef6a9-ca0d-4fe4-852b-aa8138ec7b18',
                            startTime: '2024-10-30T20:12:44.085231+00:00',
                            endTime: '2024-10-30T20:39:54.885231+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: '868ef6a9-ca0d-4fe4-852b-aa8138ec7b18',
                              isFirstCase: null,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T20:09:35.685231+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:a8d11b54-6deb-4c58-af7a-7adcd590ca90',
                            startTime: '2024-10-30T21:43:28.193581+00:00',
                            endTime: '2024-10-30T22:10:38.993581+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: 'a8d11b54-6deb-4c58-af7a-7adcd590ca90',
                              isFirstCase: null,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T21:40:19.793581+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at_gar_cam1_720p',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-gar-cam1-720p',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-gar-cam2-720p',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-gar-cam3-720p',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at_gar_cam0_720p',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at_gar_cam1_720p',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_1',
                    sortKey: null,
                    name: 'Garage 1',
                    status: {
                      name: RoomStatusName.TURNOVER,
                      since: '2024-10-30T17:45:59.565486+00:00',
                      inProgressTurnover: {
                        id: 'turnover-case:ecba2f9f-134f-4e91-8a27-9cdfe8c59425-case:5c294267-f94e-4bbb-925a-28c035e0785c',
                        startTime: '2024-10-30T17:45:59.565486+00:00',
                        endTime: '2024-10-30T18:08:59.770107+00:00',
                        followingCase: {
                          id: 'case:5c294267-f94e-4bbb-925a-28c035e0785c',
                          startTime: '2024-10-30T18:08:59.770107+00:00',
                          endTime: '2024-10-30T18:36:10.570107+00:00',
                          type: CaseType.FORECAST,
                          case: {
                            caseStaff: [
                              {
                                role: 'Primary Surgeon',
                                staff: {
                                  id: '50cf4be6-c9f3-4f02-8452-6bd36fd8efab',
                                  firstName: 'Miranda',
                                  lastName: 'House',
                                  __typename: 'Staff',
                                },
                                __typename: 'CaseStaff',
                              },
                            ],
                            primaryCaseProcedures: [
                              {
                                procedure: {
                                  id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                  name: 'Knee Arthroplasty JRP - Total Replacement',
                                  __typename: 'Procedure',
                                },
                                __typename: 'CaseProcedure',
                              },
                            ],
                            isAddOn: null,
                            __typename: 'ScheduledCase',
                          },
                          __typename: 'ApellaCase',
                        },
                        type: TurnoverType.LIVE,
                        __typename: 'Turnover',
                      },
                      inProgressApellaCase: null,
                      nextCase: {
                        id: 'case:5c294267-f94e-4bbb-925a-28c035e0785c',
                        startTime: '2024-10-30T18:08:59.770107+00:00',
                        endTime: '2024-10-30T18:36:10.570107+00:00',
                        type: CaseType.FORECAST,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary Surgeon',
                              staff: {
                                id: '50cf4be6-c9f3-4f02-8452-6bd36fd8efab',
                                firstName: 'Miranda',
                                lastName: 'House',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '259dd623-1cd8-4777-9a93-295e48ba5b7c',
                                name: 'Knee Arthroplasty JRP - Total Replacement',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: null,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '03a2cffb-093b-4319-9bac-190164abe142',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T17:45:59.565486+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Patient wheels out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'cbdeb25e-b3b7-4a57-932a-7fe5204409ef',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T17:34:36.305353+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Bed occupied',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'cc9d20cd-9112-4ac7-b9cc-c05fe6b57d2e',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T17:23:54.543077+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd565b9ec-add5-4803-ab76-0262208f21a0',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:12:58.000652+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5b513cf7-e9d7-4b6c-b5aa-387bbd051fd4',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T17:01:13.071722+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6a5b2ab1-ac56-4954-a660-adc8e86e657d',
                            name: 'patient_draped',
                            startTime: '2024-10-30T16:52:09.125581+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5e003e60-028b-4fb3-9f16-a92a0f02708f',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T16:42:13.952867+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'bc5e8c47-ffc6-484a-8047-14b484cf1838',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T16:31:24.039646+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e698425b-5c29-4476-9a76-9d67b6c35f32',
                            name: 'back_table_open',
                            startTime: '2024-10-30T16:27:07.131849+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table open',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd602fa40-ce36-490f-9868-4fc7e384d423',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:20:12.095466+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Patient wheels out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '1d088e88-b6d7-4917-9d0b-9d0ee7b5ea42',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:10:04.458805+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Bed occupied',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4f47877b-88ab-4d35-a34d-af73a58184a2',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T16:02:07.787185+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd1d225fb-777c-4f8c-8ada-9bcdbe2a69a6',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:50:40.256227+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '2f3aca91-df35-4800-8e09-571adb319199',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T15:40:47.937420+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '82e91aa4-2977-4467-9439-2b9d2811c2e6',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:34:05.737787+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '28c833f7-998b-4eac-8c3c-88065c6b2f90',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T15:27:59.567459+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'bbec9cea-8daf-488b-8540-a9d1b276aa3d',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T15:19:24.566101+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:7ac02837-e950-403a-861e-06e918e9c48f',
                            startTime: '2024-10-30T15:19:24.566101+00:00',
                            endTime: '2024-10-30T16:20:12.095466+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '7ac02837-e950-403a-861e-06e918e9c48f',
                              isFirstCase: true,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T15:05:54.146905+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:ecba2f9f-134f-4e91-8a27-9cdfe8c59425',
                            startTime: '2024-10-30T16:31:24.039646+00:00',
                            endTime: '2024-10-30T17:45:59.565486+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: 'ecba2f9f-134f-4e91-8a27-9cdfe8c59425',
                              isFirstCase: false,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T16:29:40.566487+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:5c294267-f94e-4bbb-925a-28c035e0785c',
                            startTime: '2024-10-30T18:08:59.770107+00:00',
                            endTime: '2024-10-30T18:36:10.570107+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: '5c294267-f94e-4bbb-925a-28c035e0785c',
                              isFirstCase: false,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: {
                                id: 'ecba2f9f-134f-4e91-8a27-9cdfe8c59425',
                                __typename: 'ScheduledCase',
                              },
                              scheduledStartTime:
                                '2024-10-30T18:05:51.370107+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:18213c05-1e47-462a-86fa-467a23872415',
                            startTime: '2024-10-30T19:46:11.446632+00:00',
                            endTime: '2024-10-30T20:13:22.246632+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: '18213c05-1e47-462a-86fa-467a23872415',
                              isFirstCase: false,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T19:43:03.046632+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:b0af9a49-1a56-459f-b3be-b97007dfbfd6',
                            startTime: '2024-10-30T21:05:50.723978+00:00',
                            endTime: '2024-10-30T21:33:01.523978+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: 'b0af9a49-1a56-459f-b3be-b97007dfbfd6',
                              isFirstCase: false,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T21:02:42.323978+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:b3bc3bc3-725e-4d40-9c89-e8f5b3e33783',
                            startTime: '2024-10-30T22:28:52.181351+00:00',
                            endTime: '2024-10-30T22:56:02.981351+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: 'b3bc3bc3-725e-4d40-9c89-e8f5b3e33783',
                              isFirstCase: false,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T22:25:43.781351+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at_gar_cam2_720p',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at_gar_cam2_720p',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_2',
                    sortKey: null,
                    name: 'Garage 2',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T17:04:17.372796+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'phase:36a48cae-828d-4f1d-a15f-4e431d5bbe6c',
                        startTime: '2024-10-30T17:04:17.372796+00:00',
                        endTime: null,
                        type: CaseType.LIVE,
                        case: null,
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.WRAP_UP,
                          since: '2024-10-30T17:45:52.882610+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '4a324ad87e57',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T17:45:52.882610+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c4c4249adeb64335',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:36:47.050946+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b3d43366833',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T17:27:01.387144+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'dba25ca6a',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:18:44.237424+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ea0d8c5c652acaf',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T17:11:27.776215+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '97c513792a73',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T17:04:17.372796+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0174e42ecbf65',
                            name: 'back_table_open',
                            startTime: '2024-10-30T16:31:01.187187+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table open',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6aa05b051ca042b',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:17:49.709836+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Patient wheels out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '93267c76bd7',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:07:27.946111+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Bed occupied',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '23e52ac39282c',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T15:56:26.576683+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '813b9606915',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:47:48.906915+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '395c4981bfe3d',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T15:38:28.140896+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '09e8ec8d87',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:26:35.523353+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c7657524fb3',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T15:17:45.597494+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '36079cb5c7d58',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T15:08:12.340048+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'phase:06ab9156-7234-43dd-9c01-28b3c7d6420a',
                            startTime: '2024-10-30T15:08:12.340048+00:00',
                            endTime: '2024-10-30T16:17:49.709836+00:00',
                            type: CaseType.COMPLETE,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:36a48cae-828d-4f1d-a15f-4e431d5bbe6c',
                            startTime: '2024-10-30T17:04:17.372796+00:00',
                            endTime: null,
                            type: CaseType.LIVE,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: null,
                    cameras: {
                      edges: [],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
              ],
              __typename: 'RoomConnection',
            },
            __typename: 'Site',
          },
          __typename: 'SiteEdge',
        },
        {
          node: {
            id: 'palo_alto_1',
            name: 'Apella Palo Alto',
            turnoverGoals: {
              goalMinutes: 40,
              maxMinutes: 60,
              __typename: 'TurnoverGoals',
            },
            rooms: {
              edges: [
                {
                  node: {
                    id: 'palo_alto_room_1',
                    sortKey: 'A',
                    name: 'OR 2',
                    status: {
                      name: RoomStatusName.TURNOVER,
                      since: '2024-10-30T17:30:39.407178+00:00',
                      inProgressTurnover: {
                        id: 'turnover-phase:4f38d4f0-914a-4568-8c2c-0da7a488c0a7-phase:3dd52965-17ab-4d25-b0ec-708a50d0efa6',
                        startTime: '2024-10-30T17:30:39.407178+00:00',
                        endTime: '2024-10-30T18:00:17.811778+00:00',
                        followingCase: {
                          id: 'phase:3dd52965-17ab-4d25-b0ec-708a50d0efa6',
                          startTime: '2024-10-30T18:00:17.811778+00:00',
                          endTime: null,
                          type: CaseType.FORECAST,
                          case: null,
                          __typename: 'ApellaCase',
                        },
                        type: TurnoverType.LIVE,
                        __typename: 'Turnover',
                      },
                      inProgressApellaCase: null,
                      nextCase: {
                        id: 'phase:3dd52965-17ab-4d25-b0ec-708a50d0efa6',
                        startTime: '2024-10-30T18:00:17.811778+00:00',
                        endTime: null,
                        type: CaseType.FORECAST,
                        case: null,
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '069b36b9fcab6',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T18:00:17.811778+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c4a3580981f',
                            name: 'back_table_open',
                            startTime: '2024-10-30T17:35:46.266571+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table open',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6467921bd3f2',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T17:30:39.407178+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Patient wheels out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '19844799aa47690',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T17:24:28.170403+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Bed occupied',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '39ad37c3030be',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T17:17:31.411339+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'cdce20943f6',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:12:51.713805+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8e6ed8dabc4c',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T17:06:06.115868+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '833c2cecce33a',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:00:40.625293+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '69489f301f4',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T16:53:11.828710+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5cb19d8cc9447',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T16:45:56.231208+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c07f9336a6251',
                            name: 'back_table_open',
                            startTime: '2024-10-30T16:15:35.894408+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table open',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '44aa7f7662f',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:08:36.933104+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Patient wheels out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '63982b7be4c',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T15:58:35.708485+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Bed occupied',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '08dec726c155c',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T15:51:41.636672+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5b513f5aeba76e',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:42:01.424807+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3fe5e4d2',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T15:33:02.755472+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '9482228463e5',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:26:08.353241+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'bef2a62329f',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T15:20:32.386309+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'bc2eecc9b9',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T15:12:34.426579+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'phase:1e97fb5740b84',
                            startTime: '2024-10-30T15:12:34.426579+00:00',
                            endTime: '2024-10-30T16:08:36.933104+00:00',
                            type: CaseType.COMPLETE,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:4f38d4f88c0a7',
                            startTime: '2024-10-30T16:45:56.231208+00:00',
                            endTime: '2024-10-30T17:30:39.407178+00:00',
                            type: CaseType.COMPLETE,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:3dd508a50d0efa6',
                            startTime: '2024-10-30T18:00:17.811778+00:00',
                            endTime: null,
                            type: CaseType.FORECAST,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-pa-or02-cam-01',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-pa-or02-cam-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-pa-or02-cam-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'palo_alto_room_0',
                    sortKey: 'B',
                    name: 'OR 1',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T17:12:12.996408+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:db66f1cfa1b4d4',
                        startTime: '2024-10-30T17:12:12.996408+00:00',
                        endTime: '2024-10-30T17:58:56.690863+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary Surgeon',
                              staff: {
                                id: 'facf1cac-4807-44bb-afbf-b562fdb1363d',
                                firstName: 'Miranda',
                                lastName: 'Bailey',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: 'cf6ab590-6ce4-44b4-a000-7ff1c0e529f3',
                                name: 'Angiogram',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: null,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.WRAP_UP,
                          since: '2024-10-30T17:49:38.419408+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: {
                        id: 'case:53aa3a7f-b674-470c-8098-79020f6f206e',
                        startTime: '2024-10-30T18:24:31.284802+00:00',
                        endTime: '2024-10-30T18:51:42.084802+00:00',
                        type: CaseType.FORECAST,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary Surgeon',
                              staff: {
                                id: 'a9b1793e-3f64-4f90-90d2-4c903cd30ef4',
                                firstName: 'Gregory',
                                lastName: 'Dorian',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '12c6a767-6706-4abf-a244-441d3f475c31',
                                name: 'Colonoscopy',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: null,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '4012dde9-aa6c-4095-adbd-d57f0f6b8b71',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T17:49:38.419408+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Bed occupied',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '76767eea-d104-4b05-a1a0-d4eb105e8e2b',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T17:44:09.193316+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'a28e16e1-3478-468c-99b2-b9c98b53e975',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:38:23.553967+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '629c49eb-923b-4d0d-821d-06ff9d48577f',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T17:31:25.983199+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '9470422d-3e72-478b-ae41-71d528ffe676',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:24:04.943366+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6ad28e56-e6a4-40c2-b443-c01a292f3194',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T17:17:59.344922+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '32c73664-7b30-4ec1-8a57-6e6e6a34a566',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T17:12:12.996408+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0aba11db-9ee0-47ff-acfb-9f9a5f8edd44',
                            name: 'back_table_open',
                            startTime: '2024-10-30T16:46:47.794021+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table open',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '60ab5398-412c-4ac2-8ec9-3b62b748c7b6',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:37:18.430730+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Patient wheels out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ce9e2408-31e4-4aae-b952-5d9e08506328',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:25:56.021167+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Bed occupied',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '48d068b8-d068-4e9d-9ccb-52a5064ea385',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T16:14:38.497450+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Patient undraped (Procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '73c69a95-7763-4154-92e0-d0d77e3e544c',
                            name: 'patient_draped',
                            startTime: '2024-10-30T16:00:35.798543+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '655f8346-d81e-41cd-a326-a17f77d97d3f',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T15:48:53.977642+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Anesthesia undraping',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f49e0098-2ce9-4777-b337-953e117dbfb1',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:37:55.236268+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Patient draped (Procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '39de9550-b024-470d-aae0-5c6087b43aa8',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T15:25:53.416357+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'OR table occupied',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6115abb8-fe69-476f-aa45-75356ad829db',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T15:14:49.276142+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Patient wheels in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:c85f2f82-e7d4-4173-954e-0e56d8d8d179',
                            startTime: '2024-10-30T15:14:49.276142+00:00',
                            endTime: '2024-10-30T16:37:18.430730+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: 'c85f2f82-e7d4-4173-954e-0e56d8d8d179',
                              isFirstCase: true,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T15:07:55.007908+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:db66d5bb-2521-45f3-8346-6bf1cfa1b4d4',
                            startTime: '2024-10-30T17:12:12.996408+00:00',
                            endTime: '2024-10-30T17:58:56.690863+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: 'db66d5bb-2521-45f3-8346-6bf1cfa1b4d4',
                              isFirstCase: false,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T17:11:56.399317+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:53aa3a7f-b674-470c-8098-79020f6f206e',
                            startTime: '2024-10-30T18:24:31.284802+00:00',
                            endTime: '2024-10-30T18:51:42.084802+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: '53aa3a7f-b674-470c-8098-79020f6f206e',
                              isFirstCase: false,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T18:21:22.884802+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:69216b90-51a3-47f1-a611-f6a196ea4146',
                            startTime: '2024-10-30T20:17:51.976386+00:00',
                            endTime: '2024-10-30T20:45:02.776386+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: '69216b90-51a3-47f1-a611-f6a196ea4146',
                              isFirstCase: false,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T20:14:43.576386+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:4c66ff00-f2c1-4cd5-ac80-95c8a427cd54',
                            startTime: '2024-10-30T21:44:58.040045+00:00',
                            endTime: '2024-10-30T22:12:08.840045+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: '4c66ff00-f2c1-4cd5-ac80-95c8a427cd54',
                              isFirstCase: null,
                              isAddOn: null,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime:
                                '2024-10-30T21:41:49.640045+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'ATPA-OR1-CAM-0001',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'ATPA-OR1-CAM-0002',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'ATPA-OR1-CAM-0005',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'ATPA-OR1-CAM-0001',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
              ],
              __typename: 'RoomConnection',
            },
            __typename: 'Site',
          },
          __typename: 'SiteEdge',
        },
      ],
      __typename: 'SiteConnection',
    },
  },
}

export const SiteData = {
  data: {
    sites: aSiteConnection({
      edges: [
        aSiteEdge({
          node: aSite({
            id: 'lab_1',
            name: 'Apella Lab',
            rooms: aRoomConnection({
              edges: [
                aRoomEdge({
                  node: aRoom({
                    id: 'AT-DEV-OR1',
                    site: aSite({
                      id: 'lab_1',
                      name: 'Apella Lab',
                    }),
                  }),
                }),
                aRoomEdge({
                  node: aRoom({
                    id: 'garage_0',
                    site: aSite({
                      id: 'lab_1',
                      name: 'Apella Lab',
                    }),
                    name: 'Garage 0-test',
                  }),
                }),
                aRoomEdge({
                  node: aRoom({
                    id: 'garage_1',
                    site: aSite({
                      id: 'lab_1',
                      name: 'Apella Lab',
                    }),
                    name: 'Garage 1',
                  }),
                }),
                aRoomEdge({
                  node: aRoom({
                    id: 'garage_2',
                    site: aSite({
                      id: 'lab_1',
                      name: 'Apella Lab',
                    }),
                    name: 'Garage 2',
                  }),
                }),
              ],
            }),
          }),
        }),
        aSiteEdge({
          node: aSite({
            id: 'palo_alto_1',
            name: 'Apella Palo Alto',
            rooms: aRoomConnection({
              edges: [
                aRoomEdge({
                  node: aRoom({
                    id: 'palo_alto_room_0',
                    site: aSite({
                      id: 'palo_alto_1',
                      name: 'Apella Palo Alto',
                    }),
                    name: 'OR 1',
                  }),
                }),
                aRoomEdge({
                  node: aRoom({
                    id: 'palo_alto_room_1',
                    site: aSite({
                      id: 'palo_alto_1',
                      name: 'Apella Palo Alto',
                    }),
                    name: 'OR 2',
                  }),
                }),
              ],
            }),
          }),
        }),
        aSiteEdge({
          node: aSite({
            id: 'palo_alto_1_DISABLED',
            name: 'Apella Palo Alto (Disabled Cameras)',
            rooms: aRoomConnection({
              edges: [
                aRoomEdge({
                  node: aRoom({
                    id: 'palo_alto_room_0_DISABLED',
                    site: aSite({
                      id: 'palo_alto_1_DISABLED',
                      name: 'Apella Palo Alto (Disabled Cameras)',
                    }),
                    name: 'OR 1 (Disabled)',
                  }),
                }),
                aRoomEdge({
                  node: aRoom({
                    id: 'palo_alto_room_1_DISABLED',
                    site: aSite({
                      id: 'palo_alto_1_DISABLED',
                      name: 'Apella Palo Alto (Disabled Cameras)',
                    }),
                    name: 'OR 2 (Disabled)',
                  }),
                }),
              ],
            }),
          }),
        }),
      ],
    }),
  },
}

export const LivePageManyRoomsData: MockedData = {
  data: {
    sites: {
      edges: [
        {
          node: {
            id: 'lab_1',
            name: 'Apella Lab',
            turnoverGoals: {
              goalMinutes: 35,
              maxMinutes: 90,
              __typename: 'TurnoverGoals',
            },
            rooms: {
              edges: [
                {
                  node: {
                    id: 'AT-DEV-OR1',
                    sortKey: null,
                    name: 'Test',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T18:55:30+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '1',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T18:55:30+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '2',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T18:33:26+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T18:32:14+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T17:51:21+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T17:50:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T17:27:59+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7',
                            name: 'patient_draped',
                            startTime: '2024-10-30T14:27:34+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T12:56:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '9',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T12:54:31+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '10',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:36:30+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '11',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:49:33.299000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '12',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:42:38.262000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '13',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:03:03.047000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: '14',
                            startTime: '2024-10-30T12:54:31+00:00',
                            endTime: '2024-10-30T17:51:21+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '791',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'phase:cd3d2f8',
                            startTime: '2024-10-30T18:32:14+00:00',
                            endTime: '2024-10-30T18:55:30+00:00',
                            type: CaseType.COMPLETE,
                            case: null,
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or1-test',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or1-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or1-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or1-test',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or1-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_0',
                    sortKey: null,
                    name: 'Garage 0-test',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T19:25:35+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:44506',
                        startTime: '2024-10-30T19:25:35+00:00',
                        endTime: '2024-10-30T23:48:27.803955+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: 'e11e',
                                firstName: 'EMILY',
                                lastName: 'JOHNSON',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '2856a',
                                name: 'CRANIECTOMY, WITH LESION EXCISION',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: false,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.PREP,
                          since: '2024-10-30T19:58:06+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'e094a3d',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T19:58:06+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Pt undraped (anesthesia)',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '874e65f4-ee08-4949-ad98-2a4a1501baa8',
                            name: 'anesthesia_draping',
                            startTime: '2024-10-30T19:45:43+00:00',
                            attrs: {
                              id: 'anesthesia_draping',
                              name: 'Pt draped (anesthesia)',
                              color: '#f89dca',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b8dfff',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T19:28:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4bf717d3f5',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T19:25:35+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7545b',
                            name: 'back_table_open',
                            startTime: '2024-10-30T16:11:58+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '793e3cdc8',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T07:41:54.973000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '48dc24',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T06:47:43.263000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '53c83',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:07:22.443000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:8336',
                            startTime: '2024-10-30T19:25:35+00:00',
                            endTime: '2024-10-30T23:48:27.803955+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: '8a82344506',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T17:50:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or2-CAM-02',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or2-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or2-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or2-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or2-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'garage_1',
                    sortKey: null,
                    name: 'Garage 1',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T17:30:22+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:59aef9b',
                        startTime: '2024-10-30T17:30:22+00:00',
                        endTime: '2024-10-30T21:55:22+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: '548c24e38',
                                firstName: 'SOPHIA',
                                lastName: 'WILLIAMSON',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                            {
                              role: 'Primary',
                              staff: {
                                id: 'aec85b02eb',
                                firstName: 'MICHAEL',
                                lastName: 'SMITH',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: 'f46',
                                name: 'EXCISION, MASS',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                            {
                              procedure: {
                                id: '4190552bb04',
                                name: 'CLOSURE, WOUND, TORSO, USING MYOCUTANEOUS FLAP GRAFT',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: false,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.SURGERY,
                          since: '2024-10-30T18:32:17+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'cbe5',
                            name: 'patient_draped',
                            startTime: '2024-10-30T18:32:17+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '2ce9f874',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T17:32:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '490b493da',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T17:30:22+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'effec25c8d',
                            name: 'back_table_open',
                            startTime: '2024-10-30T16:27:01+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '11131040d',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:18:02+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '1f42a0d67',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:12:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'eaeb755df46',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T15:59:09+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e51ba7a6a82d',
                            name: 'patient_draped',
                            startTime: '2024-10-30T14:06:35+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0051d79126',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T12:57:59+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6ce975d9',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T12:56:04+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f29dacd0',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:20:40+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8d0fef2',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T09:42:07.681000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '9da9eb6a58',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T09:16:01.685000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:f45871e',
                            startTime: '2024-10-30T12:56:04+00:00',
                            endTime: '2024-10-30T16:18:02+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: 'f45871e',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:58959b',
                            startTime: '2024-10-30T17:30:22+00:00',
                            endTime: '2024-10-30T21:55:22+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: '58959b',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: {
                                id: 'f4ece',
                                __typename: 'ScheduledCase',
                              },
                              scheduledStartTime: '2024-10-30T18:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR01-CAM-04',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR01-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR01-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR01-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR01-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR02',
                    sortKey: null,
                    name: 'OR 02',
                    status: {
                      name: RoomStatusName.IDLE,
                      since: '2024-10-30T00:00:04-05:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: {
                        id: 'case:82bbdce91b',
                        startTime: '2024-10-30T22:12:11.090556+00:00',
                        endTime: '2024-10-31T05:58:29.426738+00:00',
                        type: CaseType.FORECAST,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: '82bbdce91b',
                                firstName: 'ASHTON',
                                lastName: 'KUTCHER',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '1416d90',
                                name: 'TRANSPLANTATION, LIVER',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: true,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '9dd6d1',
                            name: 'back_table_open',
                            startTime: '2024-10-30T16:12:01+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '932c6c9',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T09:47:54+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '296f9f',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T09:11:37+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e636c78d025',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T06:58:05.827000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6855f7e',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T06:28:43.622000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:82ce91b',
                            startTime: '2024-10-30T22:12:11.090556+00:00',
                            endTime: '2024-10-31T05:58:29.426738+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: '82ce91b',
                              isFirstCase: false,
                              isAddOn: true,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T22:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR02-CAM-02',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR02-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR02-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR02-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR02-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR03',
                    sortKey: null,
                    name: 'OR 03',
                    status: {
                      name: RoomStatusName.TURNOVER,
                      since: '2024-10-30T19:38:55+00:00',
                      inProgressTurnover: {
                        id: 'turnover-case:e678fd8-case:98f57828-ee81c031',
                        startTime: '2024-10-30T19:38:55+00:00',
                        endTime: '2024-10-30T20:25:11.434431+00:00',
                        followingCase: {
                          id: 'case:98f57828-ee81c031',
                          startTime: '2024-10-30T20:25:11.434431+00:00',
                          endTime: '2024-10-30T22:22:28.834028+00:00',
                          type: CaseType.FORECAST,
                          case: {
                            caseStaff: [
                              {
                                role: 'Primary',
                                staff: {
                                  id: '48a6e83',
                                  firstName: 'OLIVIA',
                                  lastName: 'JONES',
                                  __typename: 'Staff',
                                },
                                __typename: 'CaseStaff',
                              },
                            ],
                            primaryCaseProcedures: [
                              {
                                procedure: {
                                  id: '92a786ffa',
                                  name: 'CHOLECYSTECTOMY, LAPAROSCOPIC',
                                  __typename: 'Procedure',
                                },
                                __typename: 'CaseProcedure',
                              },
                            ],
                            isAddOn: true,
                            __typename: 'ScheduledCase',
                          },
                          __typename: 'ApellaCase',
                        },
                        type: TurnoverType.LIVE,
                        __typename: 'Turnover',
                      },
                      inProgressApellaCase: null,
                      nextCase: {
                        id: 'case:98f578031',
                        startTime: '2024-10-30T20:25:11.434431+00:00',
                        endTime: '2024-10-30T22:22:28.834028+00:00',
                        type: CaseType.FORECAST,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: '48a415796e83',
                                firstName: 'AVA',
                                lastName: 'MARTINEZ',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '926ffa',
                                name: 'CHOLECYSTECTOMY, LAPAROSCOPIC',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: true,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '3df235318',
                            name: 'back_table_open',
                            startTime: '2024-10-30T19:56:59+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'bf94768d',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T19:38:55+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '20a16da3d158',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T19:37:01+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '57466341',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T19:31:58+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '41ef28b3138',
                            name: 'patient_draped',
                            startTime: '2024-10-30T16:44:43+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '639034d9969',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T16:18:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '96bcecf6070ad',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T16:16:30+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4c34d1e13eb0',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:31:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fdeecd0af1',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T09:07:36.220000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6cb40717cc94',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T08:49:11.847000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '39c3ceaa6b7ab59',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T08:21:36.363000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '279397395a40d',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T08:12:46.089000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:e678-863fd8',
                            startTime: '2024-10-30T16:16:30+00:00',
                            endTime: '2024-10-30T19:38:55+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: 'e678-863fd8',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_URGENT',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T15:30:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:98f57828-ee80-4ee0-895c-c4824271c031',
                            startTime: '2024-10-30T20:25:11.434431+00:00',
                            endTime: '2024-10-30T22:22:28.834028+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: '98f57828-ee80-4ee0-895c-c4824271c031',
                              isFirstCase: false,
                              isAddOn: true,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T19:50:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR03-CAM-04',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR03-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR03-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR03-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR03-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR04',
                    sortKey: null,
                    name: 'OR 04',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T13:53:30+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:5fc7103e823b',
                        startTime: '2024-10-30T13:53:30+00:00',
                        endTime: '2024-10-30T21:12:59.254761+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: 'a280580f04db4a6',
                                firstName: 'ASHTON',
                                lastName: 'KUTCHER',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '14fcdf59b16d90',
                                name: 'TRANSPLANTATION, LIVER',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: false,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.SURGERY,
                          since: '2024-10-30T15:27:38+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'e0cc56d826f2f7e',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:27:38+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '390b12bb10f2467',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T14:45:31+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Pt undraped (anesthesia)',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'bd1ae7c1efee',
                            name: 'anesthesia_draping',
                            startTime: '2024-10-30T14:23:31+00:00',
                            attrs: {
                              id: 'anesthesia_draping',
                              name: 'Pt draped (anesthesia)',
                              color: '#f89dca',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3051ced08ef1f0e618',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:57:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '28c1206561e9c99',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T13:53:30+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '05a3afcbf8c50c9165d',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:40:30+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '100e125b3af763eddb',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T12:14:59+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '61989dd826357a5',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T12:12:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b0a0947ff6e7278d',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T12:00:42+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3426d2ce440a27874f',
                            name: 'patient_draped',
                            startTime: '2024-10-30T10:30:13.382000+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f9cde7a21d26cc929c',
                            name: 'anesthesia_undraping',
                            startTime: '2024-10-30T10:01:10.999000+00:00',
                            attrs: {
                              id: 'anesthesia_undraping',
                              name: 'Pt undraped (anesthesia)',
                              color: '#c34a86',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '32d10222d9c5da9',
                            name: 'anesthesia_draping',
                            startTime: '2024-10-30T09:38:09.729000+00:00',
                            attrs: {
                              id: 'anesthesia_draping',
                              name: 'Pt draped (anesthesia)',
                              color: '#f89dca',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'cbe98fc2-6d7d08a5382',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T09:15:28.380000+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ace1820c125df57a13a',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T09:13:55.830000+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'a42ef7a5666b2753',
                            name: 'back_table_open',
                            startTime: '2024-10-30T07:32:24.088000+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '63c1e22e0de16019676',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T06:17:53.297000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fcfd336ccf3f4952',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:49:13.432000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7698a43bb8edeab504',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:02:35.217000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:36317e066f071514',
                            startTime: '2024-10-30T09:13:55.830000+00:00',
                            endTime: '2024-10-30T12:14:59+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '36317d53-6927-4f0d-a359-ae066f071514',
                              isFirstCase: false,
                              isAddOn: true,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_URGENT',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T09:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:5fc7752c-1ac1-4151-9ec4-fc80103e823b',
                            startTime: '2024-10-30T13:53:30+00:00',
                            endTime: '2024-10-30T21:12:59.254761+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: '5fc7752c-1ac1-4151-9ec4-fc80103e823b',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T14:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR04-CAM-02',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR04-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR04-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR04-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR04-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR05',
                    sortKey: null,
                    name: 'OR 05',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T17:47:30+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '1415985324aa9d5',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T17:47:30+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8edcd85eb07bac2116',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T17:39:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '06b7e0df3e8ca7d8',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T17:34:22+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e105ea3a9fd9d7f3',
                            name: 'patient_draped',
                            startTime: '2024-10-30T16:13:36+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '557a113dfe5d09',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T15:41:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0fc3b40d1edaa622',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T15:40:29+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7e2b33458a7672823',
                            name: 'back_table_open',
                            startTime: '2024-10-30T15:22:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7fead0c12df25119c',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T15:10:00+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f2b69b727800e78',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T15:09:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '02c0ca4175f5cbb4',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T15:01:18+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7db0ee92b32cbfb',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:29:03+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '26153d2c3acd22d20',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T12:54:32+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b05f5a33d38b62',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T12:53:00+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '577e11a25458d0da',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:33:30+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4ea3f4974a9f23a7',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T08:43:23.266000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '67aeb15ebae00cf87',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T08:21:54.182000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0c9df28467e7d',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T08:12:36.179000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'a0c6fbe3b6fc982',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T07:52:22.900000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:6a3d2e3c420d127f',
                            startTime: '2024-10-30T12:53:00+00:00',
                            endTime: '2024-10-30T15:10:00+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '6a3d28e3c420d127f',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:4afe208d8696',
                            startTime: '2024-10-30T15:40:29+00:00',
                            endTime: '2024-10-30T17:47:30+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '4afe20a8d8696',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: {
                                id: '6a3d2883c420d127f',
                                __typename: 'ScheduledCase',
                              },
                              scheduledStartTime: '2024-10-30T16:05:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR05-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR05-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR05-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR05-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR05-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR06',
                    sortKey: null,
                    name: 'OR 06',
                    status: {
                      name: RoomStatusName.IDLE,
                      since: '2024-10-30T16:36:30+00:00',
                      inProgressTurnover: {
                        id: 'turnover-case:88b250dc382940-case:60e28d391b43135e',
                        startTime: '2024-10-30T16:36:30+00:00',
                        endTime: '2024-10-30T20:18:00.006243+00:00',
                        followingCase: {
                          id: 'case:60e28d391b43135e',
                          startTime: '2024-10-30T20:18:00.006243+00:00',
                          endTime: '2024-10-30T23:50:56.605059+00:00',
                          type: CaseType.FORECAST,
                          case: {
                            caseStaff: [
                              {
                                role: 'Primary',
                                staff: {
                                  id: 'a2805b80f04db4a6',
                                  firstName: 'ASHTON',
                                  lastName: 'KUTCHER',
                                  __typename: 'Staff',
                                },
                                __typename: 'CaseStaff',
                              },
                            ],
                            primaryCaseProcedures: [
                              {
                                procedure: {
                                  id: '0ba4b88b70ad91aa0',
                                  name: 'DILATION, BILE DUCT, WITHOUT STENT PLACEMENT',
                                  __typename: 'Procedure',
                                },
                                __typename: 'CaseProcedure',
                              },
                            ],
                            isAddOn: true,
                            __typename: 'ScheduledCase',
                          },
                          __typename: 'ApellaCase',
                        },
                        type: TurnoverType.LIVE,
                        __typename: 'Turnover',
                      },
                      inProgressApellaCase: null,
                      nextCase: {
                        id: 'case:60e28d391b43135e',
                        startTime: '2024-10-30T20:18:00.006243+00:00',
                        endTime: '2024-10-30T23:50:56.605059+00:00',
                        type: CaseType.FORECAST,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: 'a280580f04db4a6',
                                firstName: 'ASHTON',
                                lastName: 'KUTCHER',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '0ba4b0ad91aa0',
                                name: 'DILATION, BILE DUCT, WITHOUT STENT PLACEMENT',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: true,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '889063f63df6d1',
                            name: 'back_table_open',
                            startTime: '2024-10-30T17:03:23+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8f7bfdf854eaaa9',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:36:30+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '9c8138a6cb01d',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:32:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3bd12aec4c82cb',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T16:25:25+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5c4dbe381c965c',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:12:32+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0a0df08f34d67516',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T14:50:20+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '51257c657b19a9',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T14:48:03+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '2908e62723a67f',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:32:57+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '385c01002b96518',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T09:00:31.527000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fabe4cdf065dad6',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T08:27:38.616000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0a97b24fce4eb2',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T07:25:28.926000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '40ed72c6d80f8ed7',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T06:58:55+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:88b20dc382940',
                            startTime: '2024-10-30T14:48:03+00:00',
                            endTime: '2024-10-30T16:36:30+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '88b250fd-0cea-49f2-9a91-3980dc382940',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T14:15:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:60e28b43135e',
                            startTime: '2024-10-30T20:18:00.006243+00:00',
                            endTime: '2024-10-30T23:50:56.605059+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: '60e285391b43135e',
                              isFirstCase: false,
                              isAddOn: true,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T20:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR06-CAM-02',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR06-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR06-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR06-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR06-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR07',
                    sortKey: null,
                    name: 'OR 07',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T00:00:04-05:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'ca91892abe99020',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T07:51:04.787000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '014d60069a791144',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T07:32:07.806000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '9df9974be6',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T07:15:08.833000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e9aee553193f5c',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T06:52:45.290000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR07-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR07-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR07-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR07-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR07-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR08',
                    sortKey: null,
                    name: 'OR 08',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T12:59:57+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:e664cc2fbd0cf9',
                        startTime: '2024-10-30T12:59:57+00:00',
                        endTime: '2024-10-30T20:42:00+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: '1b0bede9d45a8a6',
                                firstName: 'DAVID',
                                lastName: 'RODRIGUEZ',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                            {
                              role: 'Primary',
                              staff: {
                                id: '50d40',
                                firstName: 'MATT',
                                lastName: 'LOPEZ',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '5f9b341d0ebef444',
                                name: 'PANCREATECTOMY, SUBTOTAL, DISTAL',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                            {
                              procedure: {
                                id: '1368bdb76250f6a',
                                name: 'NEPHRECTOMY, RADICAL',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: false,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.SURGERY,
                          since: '2024-10-30T13:54:11+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'f23d52ed2758b8a',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:54:11+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'cc49717f1a62eba',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:01:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b73bd5d56006431c',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T12:59:57+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'a9ec7d801ddc185a0',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:15:30+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '70636afb1c2e62b2',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T07:51:52+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '781dec66743850caa',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T07:26:10+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fafa93c8b4baf9b76',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T07:07:18+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4863777076c32491c',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T06:40:19+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:e664ccc22fbd0cf9',
                            startTime: '2024-10-30T12:59:57+00:00',
                            endTime: '2024-10-30T20:42:00+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: 'e664cc3c22fbd0cf9',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR08-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR08-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR08-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR08-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR08-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR09',
                    sortKey: null,
                    name: 'OR 09',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T17:52:35+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:f5587d1e227a03b',
                        startTime: '2024-10-30T17:52:35+00:00',
                        endTime: '2024-10-30T21:43:57.446899+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: 'bae99bd54b86e8fd7',
                                firstName: 'WILLIAM',
                                lastName: 'MILLS',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '020d94281a80509',
                                name: 'FUSION,ANTERIOR AND LATERAL APPROACHES,LUMBAR',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: false,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.SURGERY,
                          since: '2024-10-30T19:38:25+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'f7d17781183d3ab0a',
                            name: 'patient_draped',
                            startTime: '2024-10-30T19:38:25+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ddd1d3d65c2476b78',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T19:09:28+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '30d3e05680ce25',
                            name: 'patient_draped',
                            startTime: '2024-10-30T18:21:48+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0c1b42a00b2d948a2',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T17:55:04+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '33ae93d9d527879072d',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T17:52:35+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e7792b43-88bdfddad',
                            name: 'back_table_open',
                            startTime: '2024-10-30T17:15:31+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '9d5da6762d884b8',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:55:09+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3c092f7842861d28c',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:51:55+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5733a402120c7696',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T16:35:38+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ffd04c57c5975e1e1',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:47:46+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7c9d29f456cbc53c5b',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:10:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '2fd7c53b6b660261a3',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T13:08:30+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd11bed8c043a93e',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:17:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'feb4546b3e1464579',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T07:30:50.969000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '756cece36dcc4dc49',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T07:19:22.670000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '1531789acc247',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T06:51:52.065000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '596769757766139c704',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T06:33:00.371000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:9ed1d763d86c23',
                            startTime: '2024-10-30T13:08:30+00:00',
                            endTime: '2024-10-30T16:55:09+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '9edc8e26-a113-4982-8f35-41d763d86c23',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:f558760e227a03b',
                            startTime: '2024-10-30T17:52:35+00:00',
                            endTime: '2024-10-30T21:43:57.446899+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: 'f5582c60e227a03b',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: null,
                              precedingCase: {
                                id: '9edc763d86c23',
                                __typename: 'ScheduledCase',
                              },
                              scheduledStartTime: '2024-10-30T17:20:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR09-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR09-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR09-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR09-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR09-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR10',
                    sortKey: null,
                    name: 'OR 10',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T17:58:58+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:8e28f32fd553b39',
                        startTime: '2024-10-30T17:58:58+00:00',
                        endTime: '2024-10-30T22:04:23.748291+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: '1f50b7b51b35a90a',
                                firstName: 'PETER',
                                lastName: 'YEH',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: 'a1110f58c831017',
                                name: 'FUSION, SPINE, LUMBAR, INTERBODY, TRANSFORAMINAL APPROACH',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: false,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.SURGERY,
                          since: '2024-10-30T19:01:11+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: {
                        id: 'case:bd7057460a8bd',
                        startTime: '2024-10-30T22:40:41.252746+00:00',
                        endTime: '2024-10-31T02:09:23.569335+00:00',
                        type: CaseType.FORECAST,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: '1f50b7b51b35a90a',
                                firstName: 'PETER',
                                lastName: 'GRIFFIN',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '85fe1fb26e43',
                                name: 'DISCECTOMY, CERVICAL, WITH FUSION, ANTERIOR APPROACH',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: true,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                      },
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '43ca67608a364ff9',
                            name: 'patient_draped',
                            startTime: '2024-10-30T19:01:11+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '9a7470d4-518e38d35',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T18:29:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '12dd6e4c8d31bb',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T17:58:58+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c5f07e4ca63f8a2',
                            name: 'back_table_open',
                            startTime: '2024-10-30T14:59:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0d721ab0ba87a1',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T14:42:00+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '2db15dfa698e306f',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T14:33:27+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ce685abdb3797264f1',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T14:27:47+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '284f44a00a6fa87',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:29:04+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c66f445739ada8ff',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:13:01+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '393694bbb8f',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T12:59:30+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7ad8dd8221',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:24:53+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '37c59330713961',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T09:15:39.187000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3658f9c3beb76f',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T08:44:23.385000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '052909112964b1',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T08:23:20.600000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '80e88d32ff7572',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T07:53:01.625000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:bb710160191c44',
                            startTime: '2024-10-30T12:59:30+00:00',
                            endTime: '2024-10-30T14:42:00+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: 'bb710160191c44',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:8e28ffd553b39',
                            startTime: '2024-10-30T17:58:58+00:00',
                            endTime: '2024-10-30T22:04:23.748291+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: '8e28f3-b081fd553b39',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T17:30:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:bd5d7460a8bd',
                            startTime: '2024-10-30T22:40:41.252746+00:00',
                            endTime: '2024-10-31T02:09:23.569335+00:00',
                            type: CaseType.FORECAST,
                            case: {
                              id: 'bd5d57460a8bd',
                              isFirstCase: false,
                              isAddOn: true,
                              caseClassificationType: null,
                              precedingCase: {
                                id: '8e2b081fd553b39',
                                __typename: 'ScheduledCase',
                              },
                              scheduledStartTime: '2024-10-30T22:05:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR10-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR10-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR10-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR10-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR10-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR11',
                    sortKey: null,
                    name: 'OR 11',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T19:13:13+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'e7509017-d2d8bde252',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T19:13:13+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '96bbe8fffcd19ddfe',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T19:07:59+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'a5c5b6eb6b38a2',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T19:03:02+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '680534a6627',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:59:43+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3e10d052852455',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T17:43:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '71b6fec60d7a39',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T17:30:04+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'dbabbc56-8ef2-486f-a65a-30bc4a4c4c94',
                            name: 'back_table_open',
                            startTime: '2024-10-30T17:11:30+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b5d72bd433fc08875',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:57:30+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '9dfa-8780d33232ea',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:46:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8a1-a9dc-fcf79c80d38e',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T16:35:59+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '70a75a10-9c6656a7b74b',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:55:33+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd3c26820ccc396c11d',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:42:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'bbd797e-6df4a6446f5a',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T13:01:30+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '355290397ddb4fe5e9',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:12:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '1a2dfa34259b6fd5',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T10:24:47.782000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '002fe71e074bce',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T09:59:59.815000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'abf4a021c4b383',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T06:18:35.493000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8c93d-e9032c8292c5',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:55:20.018000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:63a058c8b607755c',
                            startTime: '2024-10-30T13:01:30+00:00',
                            endTime: '2024-10-30T16:57:30+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '63a198-47c8b607755c',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:0954c57f70',
                            startTime: '2024-10-30T17:30:04+00:00',
                            endTime: '2024-10-30T19:13:13+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '0957c57f70',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: {
                                id: '63a058-47c8b607755c',
                                __typename: 'ScheduledCase',
                              },
                              scheduledStartTime: '2024-10-30T16:30:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR11-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR11-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR11-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR11-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR11-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR12',
                    sortKey: null,
                    name: 'OR 12',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T19:36:35+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'd5937c9f615e1adc',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T19:36:35+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '94b8700ac1d312',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T19:31:03+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4310278c14aed46',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T19:26:24+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '407aef0f3ae310545a',
                            name: 'patient_draped',
                            startTime: '2024-10-30T18:48:36+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '31a69d185b2b468d',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T18:08:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '035980998bb6',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T18:06:00+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6e6c3fcd04a177',
                            name: 'back_table_open',
                            startTime: '2024-10-30T15:59:59+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3b7068494c',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T15:33:53+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e216253a124',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T15:27:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4822cce51c3348',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T15:10:23+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'cd246cb6966d6e0e',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:45:14+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd1f9b5fafde5ae43',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T12:59:08+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b41df52c6b6e8cb7b',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T12:56:02+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fa214d91d1e4af9',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:22:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'cea8563d-d58c0a8bfbc',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T08:19:58.430000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f935bb753f2f38c',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T07:52:38.567000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b02279e432a82568',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T06:37:39.294000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '1d3c76b184a95c',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T06:06:59+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:2b87e63a-a9dd-4aed-90a5-c2540d641cb2',
                            startTime: '2024-10-30T12:56:02+00:00',
                            endTime: '2024-10-30T15:33:53+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '2b87e63a-a9dd-4aed-90a5-c2540d641cb2',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:05:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:e2dd26fd2cd9c3ae',
                            startTime: '2024-10-30T18:06:00+00:00',
                            endTime: '2024-10-30T19:36:35+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: 'e2dd22cd9c3ae',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T18:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR12-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR12-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR12-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR12-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR12-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR14',
                    sortKey: null,
                    name: 'OR 14',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T19:03:32+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '46fb25970e7af73586',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T19:03:32+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c4c2db90e17b89',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T19:02:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'a9d59dc09a97309c4',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T18:48:14+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '68ae6ef6e55dbb8',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:45:37+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f2871791d8f4c',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:01:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c9aef833724e08f',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T13:00:00+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c3c2507a2ff',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:33:59+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4e97ebc88b388',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T08:42:49+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7db20dbee5a',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T08:21:16.234000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f823d833d434415',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T06:03:42.583000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '51aff947f75ba',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:26:43.241000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:654-a29e-ea6c5975b5fa',
                            startTime: '2024-10-30T13:00:00+00:00',
                            endTime: '2024-10-30T19:03:32+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '65ea06c5975b5fa',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR14-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR14-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR14-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR14-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR14-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR15',
                    sortKey: null,
                    name: 'OR 15',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T17:43:17+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:106b452e8a958806',
                        startTime: '2024-10-30T17:43:17+00:00',
                        endTime: '2024-10-30T20:39:46.880981+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: 'd0f5ddc8-0778853eb',
                                firstName: 'JEN',
                                lastName: 'BROWN',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: 'c452df5757ff7247',
                                name: 'CLOSURE, ILEOSTOMY, LAPAROSCOPIC',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: false,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.SURGERY,
                          since: '2024-10-30T18:54:56+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '3f0fcf3a-4d9e68c8e',
                            name: 'patient_draped',
                            startTime: '2024-10-30T18:54:56+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fcf6ee508d93854a',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T17:46:14+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f751d9f28-62a3cf3231a6',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T17:43:17+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8a14e2e2706ec9',
                            name: 'back_table_open',
                            startTime: '2024-10-30T15:47:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ebe4291004d850d',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T15:31:57+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '09f54fe315bbdd',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T15:28:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd21304d0-ea326ce8a10f',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T15:20:16+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c75d835c-c1dc573ede5d',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:35:03+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '829d6f9569cdc',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:01:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e7057912219c832a',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T12:58:30+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b33c5965b68089',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:23:01+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd7e02e3275af6d',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T06:31:39.176000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5f4e5db1b7',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T06:19:10.811000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '55624622fce3',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:28:02.638000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '82a3d00df',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:16:50.229000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:695a07-a9e55d0019f6',
                            startTime: '2024-10-30T12:58:30+00:00',
                            endTime: '2024-10-30T15:31:57+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '695a0d0019f6',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:106b4524-491e-41d9-8f83-bc0e8a958806',
                            startTime: '2024-10-30T17:43:17+00:00',
                            endTime: '2024-10-30T20:39:46.880981+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: '106b4524-491e-41d9-8f83-bc0e8a958806',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T17:30:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR15-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR15-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR15-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR15-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR15-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR16',
                    sortKey: null,
                    name: 'OR 16',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T19:32:34+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:e723ddd5b3a86',
                        startTime: '2024-10-30T19:32:34+00:00',
                        endTime: '2024-10-30T20:52:00+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: 'aaaf91988526c59',
                                firstName: 'MICHAEL',
                                lastName: 'GARCIA',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: '78604f1b7e',
                                name: 'CATHETER REMOVAL, TUNNELED CENTRAL VENOUS, WITH PORT',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: false,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.SURGERY,
                          since: '2024-10-30T19:57:46+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'b51c61d-6717ce392bbb',
                            name: 'patient_draped',
                            startTime: '2024-10-30T19:57:46+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '91bf7eda-485d-45d2',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T19:35:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e8366-676a9f7308b2',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T19:32:34+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '2171571b-5a5d4641',
                            name: 'back_table_open',
                            startTime: '2024-10-30T19:14:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '12513119-5ecb3c11013c',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T18:58:28+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '55ea2641-edffce4e',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T18:56:20+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '7f-a757-353975da0bd9',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T18:41:11+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5747e76d-9131-eddea61',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:32:31+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0975dfaa974665',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T17:09:27+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'de175-db81424a677a',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T17:07:00+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f0bd03ac-dc393b',
                            name: 'back_table_open',
                            startTime: '2024-10-30T15:57:30+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fd6-14c1bde4d5eb',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T15:40:35+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'df816cbe4116148',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T15:30:42+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'a12a7c9cb9bcadb',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T15:23:33+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '753eb398c6c64de',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:33:08+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '2aac4b8823785',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:07:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b4aca0b4b4af1608',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T13:03:30+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0a332d4f-675a-b2e4fd',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:20:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ee2-ae70-dd5b6a22c835',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T09:25:25.195000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5b72e0f8-7cc86dd0',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T09:16:24.954000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f4f1267e40d45a',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:26:04.399000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '708a-90e7-efddd6d7c7a8',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:04:20.927000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:4614fae19cab23f',
                            startTime: '2024-10-30T13:03:30+00:00',
                            endTime: '2024-10-30T15:40:35+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '4614f9cab23f',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:a2ff5afa3133',
                            startTime: '2024-10-30T17:07:00+00:00',
                            endTime: '2024-10-30T18:58:28+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '953-de263afa3133',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T17:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:eb234-723ddd5b3a86',
                            startTime: '2024-10-30T19:32:34+00:00',
                            endTime: '2024-10-30T20:52:00+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: 'e42ddd5b3a86',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T19:40:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR16-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR16-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR16-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR16-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR16-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR17',
                    sortKey: null,
                    name: 'OR 17',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T16:47:01+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '30274-9bbf-6c2881c1d55f',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:47:01+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '94c08ca2-22f4-7048a29',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:38:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ae4-a0c6-3a3292318e4d',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T16:30:30+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b5eb48f4-769b-4a52-9299',
                            name: 'patient_draped',
                            startTime: '2024-10-30T15:42:09+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'afa8e9ec5684a',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T15:18:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6d-83e1-0b4f776d640c',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T15:16:29+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4a1b-ea7dfc21e0ae',
                            name: 'back_table_open',
                            startTime: '2024-10-30T15:02:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fe271850-94b5-4e',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T14:53:29+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '37ab-a641370423b2',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T14:51:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0e-1f2d17445aeb',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T14:46:12+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'a123af7f2aa',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:39:23+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5cb1ee-044c7068c328',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:11:08+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '85db16f1-305a50a',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T13:09:00+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '17737988-6f6ce47fc',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:40:01+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '36ca-abab-883eeec052e5',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T09:58:33+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b7c16289-197be',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T09:37:32+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'e0-95c8-80aa37915249',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:52:17.460000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '0534dfa7-0cf6-4f7',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:29:42.529000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd6-b9c1-c4061ca3b9b6',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:16:18.030000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4d442596cf55b7',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:15:08.550000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:52b56524-78503b030cd',
                            startTime: '2024-10-30T13:09:00+00:00',
                            endTime: '2024-10-30T14:53:29+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '52b565030cd',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:15:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:703-fa33fcd3365c',
                            startTime: '2024-10-30T15:16:29+00:00',
                            endTime: '2024-10-30T16:47:01+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '7acafb27-80d5',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: null,
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T15:45:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR17-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR17-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR17-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR17-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR17-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR18',
                    sortKey: null,
                    name: 'OR 18',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T17:02:30+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: 'e44b-7052060e5cc8',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T17:02:30+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c75928af-87771b3',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:58:30+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'd687b5-8e79be84d124',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T16:48:38+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'ca1100b1-23e4-4d0',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:38:21+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8-b800-c11fb471dd53',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:00:25+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '709560fc-9e0e',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T12:59:00+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '56d-84f8-471e5b6603c0',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:11:47+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '4dd249653f',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:17:45.264000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:69cafa4a3',
                            startTime: '2024-10-30T12:59:00+00:00',
                            endTime: '2024-10-30T17:02:30+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '690276289a4a3',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR18-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR18-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR18-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR18-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR18-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR19',
                    sortKey: null,
                    name: 'OR 19',
                    status: {
                      name: RoomStatusName.IN_CASE,
                      since: '2024-10-30T16:48:30+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: {
                        id: 'case:7b25124d-de6ee',
                        startTime: '2024-10-30T16:48:30+00:00',
                        endTime: '2024-10-31T00:21:58.803386+00:00',
                        type: CaseType.LIVE,
                        case: {
                          caseStaff: [
                            {
                              role: 'Primary',
                              staff: {
                                id: '784abf16d',
                                firstName: 'MEGAN',
                                lastName: 'CHIN',
                                __typename: 'Staff',
                              },
                              __typename: 'CaseStaff',
                            },
                          ],
                          primaryCaseProcedures: [
                            {
                              procedure: {
                                id: 'a110c831017',
                                name: 'FUSION, SPINE, LUMBAR, INTERBODY, TRANSFORAMINAL APPROACH',
                                __typename: 'Procedure',
                              },
                              __typename: 'CaseProcedure',
                            },
                          ],
                          isAddOn: false,
                          __typename: 'ScheduledCase',
                        },
                        __typename: 'ApellaCase',
                        status: {
                          name: CaseStatusName.SURGERY,
                          since: '2024-10-30T17:43:34+00:00',
                          __typename: 'ApellaCaseStatus',
                        },
                      },
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '84-5ae8e3dcf111',
                            name: 'patient_draped',
                            startTime: '2024-10-30T17:43:34+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'bd72c3ab90fe',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T17:29:59+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'c-98b7-512e23458226',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T16:48:30+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6e2c39ff-f945a',
                            name: 'back_table_open',
                            startTime: '2024-10-30T16:30:00+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '3fb5-99fe0eecc541',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T16:16:30+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '88bcb-c7ecd5fb79b5',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T16:09:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b80a-f98d05442ef2',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T16:00:17+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '6950605af93ab7',
                            name: 'patient_draped',
                            startTime: '2024-10-30T13:41:03+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f8bb25-b4a2ff615918',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:24:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fb1c357',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T12:58:00+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f2c97002c7a',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:42:30+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '53dd03',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T06:17:11+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '34e78fd0-23bc-41b',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:33:35+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'da0-9dc9-c6b90cdd55a0',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T05:26:24.831000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '09c1d12b-992',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T05:19:27+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:07f164b2c8f',
                            startTime: '2024-10-30T12:58:00+00:00',
                            endTime: '2024-10-30T16:16:30+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '07f10f8d464b2c8f',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                        {
                          node: {
                            id: 'case:7b25124df8208eee',
                            startTime: '2024-10-30T16:48:30+00:00',
                            endTime: '2024-10-31T00:21:58.803386+00:00',
                            type: CaseType.LIVE,
                            case: {
                              id: '7b25124d-deee',
                              isFirstCase: false,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: {
                                id: '4d77-9744-498d464b2c8f',
                                __typename: 'ScheduledCase',
                              },
                              scheduledStartTime: '2024-10-30T15:45:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR19-CAM-03',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR19-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR19-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR19-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR19-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'at-dev-or3-OR20',
                    sortKey: null,
                    name: 'OR 20',
                    status: {
                      name: RoomStatusName.CLOSED,
                      since: '2024-10-30T19:28:00+00:00',
                      inProgressTurnover: null,
                      inProgressApellaCase: null,
                      nextCase: null,
                      __typename: 'RoomStatus',
                    },
                    roomEvents: {
                      edges: [
                        {
                          node: {
                            id: '604229-ab33-1f288a536d8c',
                            name: 'patient_wheels_out',
                            startTime: '2024-10-30T19:28:00+00:00',
                            attrs: {
                              id: 'patient_wheels_out',
                              name: 'Pt wheeled out',
                              color: '#bc0c00',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8b937c-fb43cefc2c12',
                            name: 'patient_xfer_to_bed',
                            startTime: '2024-10-30T19:19:00+00:00',
                            attrs: {
                              id: 'patient_xfer_to_bed',
                              name: 'Pt xfered to bed',
                              color: '#5e49cc',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '096-8995-ce1c459aab63',
                            name: 'patient_undraped',
                            startTime: '2024-10-30T19:02:09+00:00',
                            attrs: {
                              id: 'patient_undraped',
                              name: 'Pt undraped (procedural)',
                              color: '#108383',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'fdc35cfc8',
                            name: 'patient_draped',
                            startTime: '2024-10-30T14:04:06+00:00',
                            attrs: {
                              id: 'patient_draped',
                              name: 'Pt draped (procedural)',
                              color: '#72c8c8',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '8db6-a6e5-ff1915c032e7',
                            name: 'patient_xfer_to_or_table',
                            startTime: '2024-10-30T13:12:29+00:00',
                            attrs: {
                              id: 'patient_xfer_to_or_table',
                              name: 'Pt xfered to OR table',
                              color: '#ad9dff',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '5742e2ca-cdee-43',
                            name: 'patient_wheels_in',
                            startTime: '2024-10-30T13:09:00+00:00',
                            attrs: {
                              id: 'patient_wheels_in',
                              name: 'Pt wheeled in',
                              color: '#f36f66',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'f8a-af35-c57b098c7830',
                            name: 'back_table_open',
                            startTime: '2024-10-30T12:34:30+00:00',
                            attrs: {
                              id: 'back_table_open',
                              name: 'Back table opened',
                              color: '#006FFF',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: 'b9d-bbb8-4ee8f7d57ec7',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T08:10:10.565000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '124d6a51-43a7-40',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T08:00:43.353000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '53-86db-860f5a340e9c',
                            name: 'terminal_clean_end',
                            startTime: '2024-10-30T06:47:30.131000+00:00',
                            attrs: {
                              id: 'terminal_clean_end',
                              name: 'Terminal cleaning ended',
                              color: '#0c8856',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                        {
                          node: {
                            id: '55136278-f9dd',
                            name: 'terminal_clean_start',
                            startTime: '2024-10-30T06:21:58.333000+00:00',
                            attrs: {
                              id: 'terminal_clean_start',
                              name: 'Terminal cleaning started',
                              color: '#6fcca6',
                              __typename: 'EventType',
                            },
                            __typename: 'Event',
                          },
                          __typename: 'EventEdge',
                        },
                      ],
                      __typename: 'EventConnection',
                    },
                    apellaCases: {
                      edges: [
                        {
                          node: {
                            id: 'case:0-8e8b-e5942ca04360',
                            startTime: '2024-10-30T13:09:00+00:00',
                            endTime: '2024-10-30T19:28:00+00:00',
                            type: CaseType.COMPLETE,
                            case: {
                              id: '0b-e5942ca04360',
                              isFirstCase: true,
                              isAddOn: false,
                              caseClassificationType: {
                                id: 'CASE_CLASSIFICATION_ELECTIVE',
                                __typename: 'CaseClassificationType',
                              },
                              precedingCase: null,
                              scheduledStartTime: '2024-10-30T13:00:00+00:00',
                              __typename: 'ScheduledCase',
                            },
                            __typename: 'ApellaCase',
                          },
                          __typename: 'ApellaCaseEdge',
                        },
                      ],
                      __typename: 'ApellaCaseConnection',
                    },
                    defaultCamera: {
                      id: 'at-dev-or3-OR20-CAM-02',
                      __typename: 'Camera',
                    },
                    cameras: {
                      edges: [
                        {
                          node: {
                            id: 'at-dev-or3-OR20-CAM-01',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR20-CAM-02',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR20-CAM-03',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                        {
                          node: {
                            id: 'at-dev-or3-OR20-CAM-04',
                            __typename: 'Camera',
                          },
                          __typename: 'CameraEdge',
                        },
                      ],
                      __typename: 'CameraConnection',
                    },
                    __typename: 'Room',
                  },
                  __typename: 'RoomEdge',
                },
              ],
              __typename: 'RoomConnection',
            },
            __typename: 'Site',
          },
          __typename: 'SiteEdge',
        },
      ],
      __typename: 'SiteConnection',
    },
  },
}
