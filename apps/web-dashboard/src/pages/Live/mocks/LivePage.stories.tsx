import { <PERSON><PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import { Meta, StoryObj } from '@storybook/react'
import { graphql, http, HttpResponse, delay } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'
import { GetSiteOptionsFilterDocument } from 'src/modules/site/__generated__'
import { GetCurrentUserDocument } from 'src/modules/user/__generated__'
import { ScheduleFilterProviderContext } from 'src/pages/Schedule/ScheduleFilterContext'
import MockLDProvider from 'src/test/MockLDProvider'
import { modes } from 'src/test/storybookHelpers'

import {
  GetCurrentUserData,
  UserType,
} from '../../test_mocks/GetCurrentUserData'
import { GetLiveRoomsDocument } from '../__generated__'
import Live from '../Live'
import { LivePageData, LivePageManyRoomsData, SiteData } from './LivePageData'

const ldParams = {
  ehrEnabled: true,
}

const mockedClient = new ApolloClient({
  uri: 'https://your-graphql-endpoint',
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
    query: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
  },
})

const meta: Meta<typeof Live> = {
  title: 'Pages/Live',
  component: Live,
  args: {
    showMetricsTile: true,
  },
  decorators: [
    (Story) => (
      <TimezoneProvider orgDefaultTimezone={'America/Chicago'}>
        <MockLDProvider flags={ldParams}>
          <ApolloProvider client={mockedClient}>
            <Story />
          </ApolloProvider>
        </MockLDProvider>
      </TimezoneProvider>
    ),
    withRouter,
  ],
  beforeEach: async () => {
    window.localStorage.setItem(
      `${ScheduleFilterProviderContext.LIVE}_SHOW_METRICS`,
      JSON.stringify(true)
    )
  },
  parameters: {
    chromatic: {
      modes: {
        mobile: modes.mobile,
        xLarge: modes.xLarge,
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof Live>
const routing = {
  path: '/live',
  handle: 'Live',
}

const msw = (
  wait?: number,
  { getLivePageData = LivePageData, userType = UserType.ALL_ACCESS } = {}
) => ({
  handlers: [
    http.get('https://events.launchdarkly.com/events/diagnostic/', () => {
      return HttpResponse.json({})
    }),
    http.get('https://app.launchdarkly.com/sdk/goals/', () => {
      return HttpResponse.json({})
    }),
    graphql.query(GetCurrentUserDocument, () => {
      return HttpResponse.json(GetCurrentUserData(userType))
    }),
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(SiteData)
    }),
    graphql.query(GetLiveRoomsDocument, async () => {
      await delay(wait ?? 300)
      return HttpResponse.json(getLivePageData)
    }),
  ],
})

// TODO (RT-2092): Fix flakey test
// export const LivePage: Story = {
//   parameters: {
//     date: new Date('2024-10-30T10:54:58.518-07:00'),
//     msw: msw(0, { userType: UserType.ALL_ACCESS }),
//     decorators: [withRouter],
//     reactRouter: {
//       location: {
//         path: '/live',
//       },
//       routing,
//     },
//   },
// }

// TODO (RT-2092, RT-2090): Fix flakey test
// export const LivePageWithoutMetricsHeader: Story = {
//   parameters: {
//     date: new Date('2024-10-30T10:54:58.518-07:00'),
//     msw: msw(0, { userType: UserType.ALL_ACCESS }),
//     decorators: [withRouter],
//     reactRouter: {
//       location: {
//         path: '/live',
//       },
//       routing,
//     },
//   },
//   play: async ({ canvasElement }) => {
//     const canvas = within(canvasElement)
//     await userEvent.click(canvas.getByTestId('metric-header-clicked'))
//   },
// }

export const LivePageColumnView: Story = {
  parameters: {
    date: new Date('2024-10-30T10:54:58.518-07:00'),
    msw: msw(0, { userType: UserType.ALL_ACCESS }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/live',
        searchParams: { numCols: '1' },
      },
      routing,
    },
  },
}

export const LivePageManyTiles: Story = {
  parameters: {
    date: new Date('2024-10-30T13:15:58.518-07:00'),
    msw: msw(0, {
      userType: UserType.ALL_ACCESS,
      getLivePageData: LivePageManyRoomsData,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/live',
      },
      routing,
    },
  },
}
