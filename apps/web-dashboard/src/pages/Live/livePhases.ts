export enum LIVE_EVENTS {
  PATIENT_WHEELS_IN = 'patient_wheels_in',
  PATIENT_WHEELS_OUT = 'patient_wheels_out',
  BACK_TABLE_OPEN = 'back_table_open',
  ENDO_PACK_OPEN = 'endo_pack_open',
  PATIENT_DRAPED = 'patient_draped',
  PATIENT_UNDRAPED = 'patient_undraped',
  ANESTHESIA_DRAPING = 'anesthesia_draping',
  ANESTHESIA_UNDRAPING = 'anesthesia_undraping',
  PATIENT_XFER_TO_BED = 'patient_xfer_to_bed',
  PATIENT_XFER_TO_OR_TABLE = 'patient_xfer_to_or_table',
  TERMINAL_CLEAN_START = 'terminal_clean_start',
  TERMINAL_CLEAN_END = 'terminal_clean_end',
}

export enum OBSERVATION_TYPE {
  OBSERVED_CASE_CLOSING = 'OBSERVED_CASE_CLOSING',
  OBSERVED_CASE_START = 'OBSERVED_CASE_START',
  OBSERVED_ANESTHESIA_READY = 'OBSERVED_ANESTHESIA_READY',
  OBSERVED_PRE_PROCEDURE_COMPLETE = 'OBSERVED_PRE_PROCEDURE_COMPLETE',
  OBSERVED_IN_PRE_PROCEDURE = 'OBSERVED_IN_PRE_PROCEDURE',
}

export const OBSERVATIONS_TO_QUERY = Object.values(OBSERVATION_TYPE)
