import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useState,
} from 'react'

import { useQuery } from '@apollo/client'
import { isEqual, keyBy, mapValues, maxBy } from 'lodash'
import { DateTime } from 'luxon'

import { Size, useSize } from '@apella/hooks'
import { useCurrentMinute, useCurrentTime } from 'src/utils/useCurrentTime'
import { usePolling } from 'src/utils/usePolling'

import { LiveEvent, Unlabeled } from '../types'
import {
  GetLiveEvents,
  GetLiveEventsVariables,
  GetLiveImage,
  GetLiveImageVariables,
} from './__generated__'
import {
  BUFFERING_IMAGE_CUTOFF_MS,
  LIVE_IMAGE_CUTOFF_MS,
  LIVE_POLL_INTERVAL_MS,
} from './consts'
import { LIVE_EVENTS } from './livePhases'
import { GET_LIVE_EVENTS, GET_LIVE_IMAGE } from './queries'

export const useMostRecentImage = (cameraId?: string, skip?: boolean) => {
  const [mostRecentImage, setMostRecentImage] = useState<
    { url: string; captureTime: DateTime } | undefined
  >()

  const maybeSetMostRecentImage = useCallback(
    (latestImageData: GetLiveImage) => {
      const imageBytes = latestImageData?.camera?.latestImage?.imageBytes
      const captureTime = latestImageData?.camera?.latestImage?.captureTime
      if (imageBytes && captureTime) {
        setMostRecentImage((prevImage) => {
          const newCaptureTime = DateTime.fromISO(captureTime)
          if (
            !prevImage ||
            newCaptureTime.toMillis() > prevImage.captureTime.toMillis()
          ) {
            // Explicitly revoke the previous image URL to prevent memory leaks
            if (prevImage) {
              URL.revokeObjectURL(prevImage.url)
            }
            return {
              url: base64JpegToUrl(imageBytes),
              captureTime: newCaptureTime,
            }
          }
          return prevImage
        })
      }
    },
    [setMostRecentImage]
  )

  const {
    data: imageData,
    startPolling,
    stopPolling,
    refetch,
  } = useQuery<GetLiveImage, GetLiveImageVariables>(GET_LIVE_IMAGE, {
    variables: {
      cameraId: cameraId ?? '',
    },
    skip: !cameraId || skip,
    fetchPolicy: 'no-cache',
    onCompleted: maybeSetMostRecentImage,
    notifyOnNetworkStatusChange: true,
  })

  usePolling({
    refetch,
    startPolling,
    stopPolling,
    interval: LIVE_POLL_INTERVAL_MS,
    skip: !cameraId || skip,
  })

  return useMemo(() => {
    return {
      mostRecentImage,
      privacyEnabled: imageData?.camera?.room?.privacyEnabled,
      refetch,
      startPolling,
      stopPolling,
    }
  }, [
    imageData?.camera?.room?.privacyEnabled,
    mostRecentImage,
    refetch,
    startPolling,
    stopPolling,
  ])
}

const base64JpegToUrl = (base64: string) =>
  URL.createObjectURL(
    new Blob([base64ToByteArray(base64)], {
      type: 'image/jpeg',
    })
  )

function base64ToByteArray(base64: string) {
  const binaryString = atob(base64)
  const length = binaryString.length
  const bytes = new Uint8Array(length)
  for (let i = 0; i < length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  return bytes
}

const useMostRecentEventBase = <T>(
  eventSelectionFn: (events: LiveEvent[]) => T,
  siteId?: string,
  skip?: boolean
): Record<string, T> => {
  const currentMinute = useCurrentMinute()
  const [mostRecentEvents, setMostRecentEvents] = useState<Record<string, T>>(
    {}
  )

  const {
    data: eventData,
    startPolling,
    stopPolling,
    refetch,
  } = useQuery<GetLiveEvents, GetLiveEventsVariables>(GET_LIVE_EVENTS, {
    variables: {
      siteId: siteId!,
      minTime: currentMinute.minus({ hours: 24 }).toISO(),
      maxTime: currentMinute.plus({ hours: 1 }).toISO(),
    },
    skip: !siteId || skip,
    fetchPolicy: 'no-cache',
  })

  usePolling({
    refetch,
    startPolling,
    stopPolling,
    interval: LIVE_POLL_INTERVAL_MS,
    skip: !siteId || skip,
  })

  useEffect(() => {
    if (!eventData?.site) {
      return
    }

    const rooms = eventData.site.rooms.edges.map((e) => e.node)
    const roomsById = keyBy(rooms, (r) => r.id)

    // Find the most recent event for each room
    const newValues = mapValues(roomsById, (room) => {
      return eventSelectionFn(room.roomEvents.edges.map((e) => e.node))
    })

    if (!isEqual(newValues, mostRecentEvents)) {
      setMostRecentEvents(newValues)
    }
  }, [eventData, eventSelectionFn, mostRecentEvents])

  return mostRecentEvents
}

export const useMostRecentEvent = (
  siteId?: string,
  skip?: boolean
): Record<string, LiveEvent | null> => {
  const eventSelectionFn = (events: LiveEvent[]): LiveEvent | null => {
    return maxBy(events, (e) => DateTime.fromISO(e.startTime).valueOf()) ?? null
  }
  return useMostRecentEventBase(eventSelectionFn, siteId, skip)
}

export const useCameraStatus = (mostRecentImageCaptureTime?: DateTime) => {
  const now = useCurrentTime()

  return useMemo(() => {
    if (!mostRecentImageCaptureTime) return 'loading'

    const imageAge = now.diff(mostRecentImageCaptureTime)

    if (imageAge.milliseconds < LIVE_IMAGE_CUTOFF_MS) {
      return 'live'
    } else if (imageAge.milliseconds < BUFFERING_IMAGE_CUTOFF_MS) {
      return 'buffering'
    } else {
      return 'offline'
    }
  }, [mostRecentImageCaptureTime, now])
}

export const useLivePhaseInfo = (
  mostRecentEvent: LiveEvent | null
): {
  label: string
  isIdle: boolean
  eventType?: string
  timeElapsed?: string
} => {
  const currentMinute = useCurrentMinute()
  const idleText = 'Idle'

  if (!mostRecentEvent) {
    return { label: idleText, timeElapsed: undefined, isIdle: true }
  }

  const mostRecentEventAge = currentMinute
    .diff(DateTime.fromISO(mostRecentEvent.startTime), ['hours', 'minutes'])
    .toObject()

  const moreThan2HoursAgo =
    mostRecentEventAge.hours !== undefined && mostRecentEventAge.hours >= 2
  const wasWheelsOut =
    mostRecentEvent.name === `${LIVE_EVENTS.PATIENT_WHEELS_OUT}`

  if (moreThan2HoursAgo && wasWheelsOut) {
    return { label: idleText, timeElapsed: undefined, isIdle: true }
  }

  const timeElapsed = `${mostRecentEventAge.hours}h ${
    mostRecentEventAge.minutes ? Math.floor(mostRecentEventAge.minutes) : 0
  }m`
  return {
    label: mostRecentEvent?.attrs?.name || Unlabeled,
    isIdle: false,
    eventType: mostRecentEvent?.attrs?.id,
    timeElapsed,
  }
}

export const useLiveTileContainerSize = (
  target: HTMLElement | null,
  { showBanner = false, showMetrics = false, showFilters = false } = {}
): Size | undefined => {
  const [size, setSize] = useState<Size>()

  const targetSize = useSize(target)

  useLayoutEffect(() => {
    if (targetSize) {
      setSize(targetSize)
    }
  }, [targetSize])

  useLayoutEffect(() => {
    if (!target) return

    const rect = target.getBoundingClientRect()
    setSize({ width: rect.width, height: rect.height })
  }, [showBanner, showMetrics, showFilters, target])

  return size
}
