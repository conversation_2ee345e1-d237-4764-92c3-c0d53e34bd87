import { DateTime } from 'luxon'

import {
  aCaseFlag,
  aCaseFlagUpsertInput,
  aCaseNotePlan,
  aCaseNotePlanUpsertInput,
  anApellaCase,
  anApellaCaseConnection,
  anApellaCaseEdge,
  anOrganization,
  anOrganizationConnection,
  anOrganizationEdge,
  aRoom,
  aRoomConnection,
  aRoomEdge,
  aScheduledCase,
  aSite,
  aSiteConnection,
  aSiteEdge,
  aStaffConnection,
  aUser,
} from 'src/__generated__/generated-mocks'
import {
  GET_CASE_PLANNING_ROOM_DATA,
  SET_CASE_PATIENT_DATA,
  UPSERT_CASE_NOTE_PLAN,
} from 'src/modules/planning/queries/queries'
import { GET_CURRENT_USER } from 'src/modules/user/queries'
import { GET_STAFF } from 'src/pages/Notifications/queries'

const enteredDateThe7th = DateTime.fromISO('2023-12-07').setZone(
  'America/Los_Angeles',
  { keepLocalTime: true }
)
const enteredDateThe12th = DateTime.fromISO('2023-12-12').setZone(
  'America/Los_Angeles',
  { keepLocalTime: true }
)

const requestThe7th = {
  query: GET_CASE_PLANNING_ROOM_DATA,
  variables: {
    first: 100,
    siteIds: 'lab_1',
    roomIds: 'garage_0',
    orderBy: [],
    maxStartTime: enteredDateThe7th.endOf('day').toISO(),
    minEndTime: enteredDateThe7th.startOf('day').toISO(),
  },
}
const resultThe7th = {
  data: {
    sites: aSiteConnection({
      edges: [
        aSiteEdge({
          node: aSite({
            rooms: aRoomConnection({
              edges: [
                aRoomEdge({
                  node: aRoom({
                    name: 'Garage 0',
                    id: 'garage_0',
                    apellaCases: anApellaCaseConnection({
                      edges: [anApellaCaseEdge(), anApellaCaseEdge()],
                    }),
                  }),
                }),
              ],
            }),
          }),
        }),
      ],
    }),
  },
}
const the12thInitialFlags = [
  aCaseFlag({ flagType: 'MALIGNANT_HYPERTHERMIA', archivedTime: null }),
  aCaseFlag({ flagType: 'BLOOD_PRODUCTS', archivedTime: null }),
  aCaseFlag({
    flagType: 'LATEX_ALLERGY',
    archivedTime: '2023-12-13T05:31:51.532113+00:00',
  }),
]

const the12thInitialNote = aCaseNotePlan({ note: 'My note' })
const requestThe12th = {
  query: GET_CASE_PLANNING_ROOM_DATA,
  variables: {
    first: 100,
    siteIds: 'lab_1',
    roomIds: 'garage_0',
    orderBy: [],
    maxStartTime: enteredDateThe12th.endOf('day').toISO(),
    minEndTime: enteredDateThe12th.startOf('day').toISO(),
  },
}

const scheduledCase = aScheduledCase({
  caseFlags: the12thInitialFlags,
  notePlan: the12thInitialNote,
})

const resultThe12th = {
  data: {
    sites: aSiteConnection({
      edges: [
        aSiteEdge({
          node: aSite({
            rooms: aRoomConnection({
              edges: [
                aRoomEdge({
                  node: aRoom({
                    name: 'Garage 0',
                    id: 'garage_0',
                    apellaCases: anApellaCaseConnection({
                      edges: [
                        anApellaCaseEdge({
                          node: anApellaCase({
                            case: scheduledCase,
                          }),
                        }),
                      ],
                    }),
                  }),
                }),
              ],
            }),
          }),
        }),
      ],
    }),
  },
}

const userData = {
  data: {
    me: aUser({
      organizations: anOrganizationConnection({
        edges: [
          anOrganizationEdge({
            node: anOrganization({
              id: 'apella_internal_0',
              auth0OrgId: 'apella_internal_0',
            }),
          }),
        ],
      }),
    }),
  },
}

export const resultThe12thFn = vi.fn(() => resultThe12th)
export const upsertCaseFlagDataResults = vi.fn(() => ({
  data: {
    caseFlagUpsert: {
      success: true,
    },
  },
}))
export const upsertCaseNotePlanResults = vi.fn(() => ({
  data: {
    caseNotePlanUpsert: {
      success: true,
      caseNotePlan: the12thInitialNote,
    },
  },
}))
const get_staff = vi.fn(() => ({
  data: {
    staff: aStaffConnection(),
  },
}))

const CasePlanningRoomMocks = [
  {
    request: requestThe7th,
    result: resultThe7th,
  },
  {
    request: requestThe12th,
    result: resultThe12th,
    newData: resultThe12thFn,
  },
  {
    request: {
      query: GET_CURRENT_USER,
      variables: {},
    },
    result: userData,
    newData: () => userData,
  },
  {
    request: {
      query: SET_CASE_PATIENT_DATA,
      variables: {
        caseFlags: [
          aCaseFlagUpsertInput({
            id: the12thInitialFlags[0].id,
            caseId: scheduledCase.id,
            flagType: 'MALIGNANT_HYPERTHERMIA',
          }),
          aCaseFlagUpsertInput({
            id: the12thInitialFlags[1].id,
            caseId: scheduledCase.id,
            flagType: 'BLOOD_PRODUCTS',
          }),
          aCaseFlagUpsertInput({
            id: the12thInitialFlags[2].id,
            caseId: scheduledCase.id,
            flagType: 'LATEX_ALLERGY',
          }),
        ],
      },
    },
    results: upsertCaseFlagDataResults,
    newData: upsertCaseFlagDataResults,
  },
  {
    request: {
      query: UPSERT_CASE_NOTE_PLAN,
      variables: {
        input: aCaseNotePlanUpsertInput({
          id: the12thInitialNote.id,
          caseId: scheduledCase.id,
          note: the12thInitialNote.note + 'Added note',
        }),
      },
    },
    results: upsertCaseNotePlanResults,
    newData: upsertCaseNotePlanResults,
  },
  {
    request: {
      query: GET_STAFF,
      variables: {
        orgId: 'apella_internal_0',
      },
    },
    results: get_staff,
    newData: get_staff,
  },
]
export default CasePlanningRoomMocks
