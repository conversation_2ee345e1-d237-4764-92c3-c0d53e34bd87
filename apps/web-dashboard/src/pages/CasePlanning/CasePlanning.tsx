import { useMemo } from 'react'
import { generatePath } from 'react-router'

import { DateTime } from 'luxon'

import {
  Badge,
  Button,
  ButtonLink,
  Caps3,
  Edit,
  P3,
  PaginatedTableColumn,
  RemotePaginatedTable,
} from '@apella/component-library'
import { Direction } from 'src/__generated__/globalTypes'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { ResetFilters } from 'src/components/ResetFilters'
import { SingleDatePicker } from 'src/components/SingleDatePicker'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import {
  TableDataRow,
  useCasePlanningSearch,
} from 'src/modules/planning/hooks/useCasePlanningSearch'
import { GetCasePlanningRoomDataVariables } from 'src/modules/planning/queries/__generated__'
import { LocationPath } from 'src/router/types'
import { dateTimeToUrlFriendlyDate } from 'src/utils/urlFriendlyDate'
import useDateState from 'src/utils/useDateState'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

type ORDER_BY = 'orderBy'
const NAME = 'name'

const SORTABLE_FIELDS = [NAME]

const DEFAULT_ORDER_BY_STATE: Pick<GetCasePlanningRoomDataVariables, ORDER_BY> =
  {
    orderBy: [
      {
        sort: NAME,
        direction: Direction.ASC,
      },
    ],
  }

const DEFAULT_STATE = {
  after: undefined,
  first: 10,
  siteIds: undefined,
  roomIds: undefined,
  ...DEFAULT_ORDER_BY_STATE,
}

export const CasePlanning = () => {
  const [{ minTime }] = useDateState()

  const { sites } = useSiteOptions()
  const rooms = useRoomOptions()

  const {
    selectedDate,
    pageCursors,
    tableData,
    rowKeySelector,
    resetFilters,
    dataLoading,
    onSiteIdsChange,
    onRoomIdsChange,
    onSortChange,
    onDateChange,
    state,
    updateTableFilterSortState,
    showResetFilters,
  } = useCasePlanningSearch({
    DEFAULT_DATE: DateTime.fromISO(minTime),
    DEFAULT_STATE,
    SORTABLE_FIELDS,
    DEFAULT_ORDER_BY_STATE,
  })
  const columns: PaginatedTableColumn<(typeof tableData)[number]>[] = useMemo(
    () => [
      {
        name: 'Name',
        sortAttribute: NAME,
        contents: (row: TableDataRow) => {
          return (
            <>
              <P3>{row.room.name}</P3>
              <Caps3>{row.site.name}</Caps3>
            </>
          )
        },
      },
      {
        name: 'Cases',
        contents: (row: TableDataRow) => (
          <Badge variant="secondary" color="gray">
            {row.cases.length}
          </Badge>
        ),
      },
      {
        name: 'Planned staff',
        contents: (row: TableDataRow) => (
          <div
            css={{
              display: 'grid',
              gridAutoFlow: 'column',
              gridAutoColumns: 'max-content',
              justifyContent: 'space-between',
            }}
          >
            <Badge variant="secondary" color="gray">
              {row.totalPlannedStaff}
            </Badge>
            {row.cases ? (
              <ButtonLink
                size={'sm'}
                appearance="link"
                state={{
                  tableData,
                  orderBy: state.orderBy,
                }}
                to={{
                  pathname: generatePath(LocationPath.CasePlanningRoom),
                  search: `date=%22${dateTimeToUrlFriendlyDate(
                    DateTime.fromJSDate(selectedDate)
                  )}%22&siteIds=%22${row.site.id || ''}%22&roomIds=%22${
                    row.room.id || ''
                  }%22`,
                }}
              >
                <Edit size={'xs'} />
                Edit
              </ButtonLink>
            ) : (
              <Button size={'sm'} appearance="link" disabled>
                <Edit size={'xs'} />
                Edit
              </Button>
            )}
          </div>
        ),
      },
    ],
    [selectedDate, state.orderBy, tableData]
  )

  return (
    <>
      {dataLoading && <LoadingOverlay />}
      <PageContentTemplate
        title={'Boards'}
        selectedViewId="case-planning"
        views={[
          {
            to: {
              pathname: LocationPath.Boards,
            },
            id: 'boards',
            display: 'Boards',
          },
          {
            to: {
              pathname: LocationPath.CasePlanning,
              search: state.siteIds
                ? new URLSearchParams({
                    siteIds: JSON.stringify(state.siteIds),
                  }).toString()
                : undefined,
            },
            id: 'case-planning',
            display: 'Case Planning',
          },
        ]}
        filters={
          <>
            <SingleDatePicker
              onChangeDate={onDateChange}
              date={selectedDate.toISOString()}
            />
            <SitesRoomsFilter
              sites={sites}
              selectedSiteIds={state.siteIds || undefined}
              onChangeSites={onSiteIdsChange}
              rooms={rooms}
              selectedRoomIds={state.roomIds || undefined}
              onChangeRooms={onRoomIdsChange}
              multipleSites
              bulkSelectSites
              bulkSelectRooms
            />
            {showResetFilters && (
              <ResetFilters
                resetActions={resetFilters}
                disabled={dataLoading}
              />
            )}
          </>
        }
      >
        <RemotePaginatedTable
          columns={columns}
          data={tableData}
          isLoading={dataLoading}
          paginationType="cursor"
          rowKeySelector={rowKeySelector}
          onChangePage={(cursorItem) => {
            updateTableFilterSortState({ after: cursorItem.cursor })
          }}
          sortOrder={state.orderBy ?? undefined}
          onChangeSort={onSortChange}
          isMultiSort={false}
          {...pageCursors}
        />
      </PageContentTemplate>
    </>
  )
}
