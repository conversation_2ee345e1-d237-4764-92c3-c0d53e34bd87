import { useCallback, useEffect, useMemo } from 'react'
import { generatePath, useLocation } from 'react-router'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import { useMutation } from '@apollo/client'

import {
  Button,
  ButtonLink,
  ChevronLeft,
  ChevronRight,
  Direction,
  H4,
  P3,
  remSpacing,
} from '@apella/component-library'
import { useQueryParamState } from '@apella/hooks'
import { OrderBy } from 'src/__generated__/globalTypes'
import { orderByToUrlFriendly } from 'src/components/Filters/urls'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { PageHeader } from 'src/components/PageHeader'
import { CASE_FLAG_MAP } from 'src/modules/planning/components/CasePatientDataForm'
import {
  CaseStaffPlanForm,
  CaseStaffPlanFormProps,
  CaseStaffPlanFormValues,
} from 'src/modules/planning/components/CaseStaffPlanForm'
import { generateCaseStaffPlanUpsertInputData } from 'src/modules/planning/helpers'
import {
  DATE,
  ROOM_IDS,
  SITE_IDS,
  TableDataRow,
  useCasePlanningSearch,
} from 'src/modules/planning/hooks/useCasePlanningSearch'
import {
  GetCasePlanningRoomDataVariables,
  SetCasePatientData,
  SetCasePatientDataVariables,
  UpsertCaseNotePlan,
  UpsertCaseNotePlanVariables,
  UpsertCaseStaffPlan,
  UpsertCaseStaffPlanVariables,
} from 'src/modules/planning/queries/__generated__'
import {
  GET_CASE_PLANNING_ROOM_DATA,
  SET_CASE_PATIENT_DATA,
  UPSERT_CASE_NOTE_PLAN,
  UPSERT_CASE_STAFF_PLAN,
} from 'src/modules/planning/queries/queries'
import { CaseNote } from 'src/modules/planning/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import { LocationPath } from 'src/router/types'
import {
  dateTimeToUrlFriendlyDate,
  urlFriendlyDateToDateTime,
} from 'src/utils/urlFriendlyDate'

import { Case } from '../../modules/planning/components/Case'
import { EVENTS, useAnalyticsEventLogger } from '../../utils/analyticsEvents'

const ORDER_BY = 'orderBy'
const NAME = 'name'

const DEFAULT_ORDER_BY_STATE: Pick<
  GetCasePlanningRoomDataVariables,
  typeof ORDER_BY
> = {
  orderBy: [
    {
      sort: NAME,
      direction: Direction.ASC,
    },
  ],
}

export const CasePlanningRoom = () => {
  const theme = useTheme()
  const eventLogger = useAnalyticsEventLogger()

  const location = useLocation()

  const [roomId, setRoomId] = useQueryParamState<string | undefined>(
    ROOM_IDS,
    undefined
  )

  const [siteId, setSiteId] = useQueryParamState<string | undefined>(
    SITE_IDS,
    undefined
  )

  const [urlDate] = useQueryParamState<string | undefined>(DATE, undefined)

  const date = urlFriendlyDateToDateTime(urlDate || '')

  const { tableData: stateTableData, orderBy } = (location.state || {}) as {
    tableData?: TableDataRow[]
    orderBy?: OrderBy[]
  }

  const { currentOrganization } = useCurrentUser()

  const orgId = currentOrganization?.node.id

  const currentIx = stateTableData?.findIndex(
    (td) => td.site.id === siteId && td.room.id === roomId
  )

  const prevIx =
    stateTableData && currentIx !== undefined
      ? Math.max(currentIx - 1, 0)
      : undefined
  const nextIx =
    stateTableData && currentIx !== undefined
      ? Math.min(currentIx + 1, stateTableData?.length - 1)
      : undefined

  const {
    tableData,
    dataLoading,
    onRoomIdsChange,
    onSiteIdsChange,
    state,
    refetchData,
  } = useCasePlanningSearch({
    DEFAULT_DATE: date,
    DEFAULT_STATE: {
      after: undefined,
      first: 100,
      siteIds: siteId ? [siteId] : [],
      roomIds: roomId ? [roomId] : [],
      ...DEFAULT_ORDER_BY_STATE,
    },
    SORTABLE_FIELDS: [],
    DEFAULT_ORDER_BY_STATE,
  })

  const onNoteError = () => {
    toast.error('There was an error saving the note, please try again!')
  }

  const onStaffError = () => {
    toast.error(
      'There was an error saving the staffing plan, please try again!'
    )
  }

  const onPatientDataError = () => {
    // We need this to catch the unlikely case of someone else editing the patient data at the same time
    toast.error(
      'There was an error saving the patient data, likely due to someone else editing at the same time, please try again!'
    )
    refetchData()
  }

  const [upsertCaseNotePlan, { loading: caseNoteLoading }] = useMutation<
    UpsertCaseNotePlan,
    UpsertCaseNotePlanVariables
  >(UPSERT_CASE_NOTE_PLAN, {
    onError: onNoteError,
    onCompleted: () => {
      toast.success('Note saved.')
    },
    refetchQueries: [
      {
        query: GET_CASE_PLANNING_ROOM_DATA,
        variables: state,
        fetchPolicy: 'cache-only',
      },
    ],
    awaitRefetchQueries: true,
  })

  const [upsertCaseStaffPlans, { loading: caseStaffLoading }] = useMutation<
    UpsertCaseStaffPlan,
    UpsertCaseStaffPlanVariables
  >(UPSERT_CASE_STAFF_PLAN, {
    onError: onStaffError,
    refetchQueries: [
      {
        query: GET_CASE_PLANNING_ROOM_DATA,
        variables: state,
        fetchPolicy: 'cache-only',
      },
    ],
    awaitRefetchQueries: true,
  })

  const [updateCasePatientData, { loading: casePatientDataLoading }] =
    useMutation<SetCasePatientData, SetCasePatientDataVariables>(
      SET_CASE_PATIENT_DATA,
      {
        onError: onPatientDataError,
        refetchQueries: [
          {
            query: GET_CASE_PLANNING_ROOM_DATA,
            variables: state,
            fetchPolicy: 'cache-only',
          },
        ],
        awaitRefetchQueries: true,
      }
    )

  const cases = tableData.flatMap((td) => td.cases)

  const filteredCases = cases?.filter(Boolean)

  const onCaseStaffSubmit = useCallback(
    (args: CaseStaffPlanFormProps) => {
      return (values: CaseStaffPlanFormValues) => {
        if (!siteId || !orgId) {
          return
        }
        const currentCspInput = filteredCases.flatMap((c) => {
          return c.caseStaffPlan.filter((csp) => !!csp.staff?.id)
        })

        const input = generateCaseStaffPlanUpsertInputData({
          caseIds: args.caseIds,
          caseStaffPlans: currentCspInput,
          values,
          siteId,
          orgId,
        })

        eventLogger(EVENTS.UPDATE_CASE_STAFF_ASSIGNMENTS, {
          caseIds: args.caseIds,
          siteId,
          orgId,
          values,
        })

        upsertCaseStaffPlans({ variables: { input } })
      }
    },
    [filteredCases, orgId, siteId, upsertCaseStaffPlans, eventLogger]
  )

  const onChangeCaseNoteField = useCallback(
    (caseId: string, case_note: CaseNote) => {
      if (!siteId || !orgId) {
        return
      }
      const input = {
        caseId: caseId,
        id: case_note.id,
        orgId: orgId,
        siteId: siteId,
      }

      eventLogger(EVENTS.UPDATE_CASE_NOTE, input)

      upsertCaseNotePlan({
        variables: {
          input: { ...input, note: case_note.note },
        },
      })
    },
    [orgId, siteId, upsertCaseNotePlan, eventLogger]
  )

  const onChangeCaseStaffField = useCallback(
    <T extends keyof CaseStaffPlanFormValues>(
      caseId: string,
      role: T,
      values: CaseStaffPlanFormValues[T]
    ) => {
      if (!siteId || !orgId) {
        return
      }
      const currentCspInput = filteredCases
        .filter((c) => c.scheduledCaseId === caseId)
        .flatMap((c) =>
          c.caseStaffPlan.filter((csp) => !!csp.staff?.id && csp.role === role)
        )

      const input = generateCaseStaffPlanUpsertInputData({
        caseIds: [caseId],
        caseStaffPlans: currentCspInput,
        values: { [role]: values },
        siteId,
        orgId,
      })

      eventLogger(EVENTS.UPDATE_CASE_STAFF_ASSIGNMENTS, {
        caseId,
        role,
        values,
      })

      upsertCaseStaffPlans({ variables: { input } })
    },
    [eventLogger, filteredCases, orgId, siteId, upsertCaseStaffPlans]
  )

  const onChangeCasePatientDataField = useCallback(
    (caseId: string, caseFlags: CASE_FLAG_MAP) => {
      if (!currentOrganization?.node.id) {
        return
      }

      const input = Object.values(caseFlags).map((cf) => ({
        id: cf.id,
        archived: !cf.active,
        flagType: cf.type,
        caseId: caseId,
        orgId: currentOrganization?.node.id,
        siteId: siteId || '',
      }))

      eventLogger(EVENTS.UPDATE_CASE_FLAGS, {
        siteId: input.length ? input[0].siteId : '',
        orgId: input.length ? input[0].orgId : '',
        caseId: input.length ? input[0].caseId : '',
        caseFlags: input.filter((cf) => !cf.archived).map((cf) => cf.flagType),
      })

      updateCasePatientData({
        variables: {
          caseFlags: input,
        },
      })
    },
    [eventLogger, currentOrganization?.node.id, siteId, updateCasePatientData]
  )

  const backSearch = useMemo(() => {
    const orderByUrl =
      orderBy || state.orderBy || DEFAULT_ORDER_BY_STATE.orderBy || []
    return [
      `${DATE}="${dateTimeToUrlFriendlyDate(date)}"`,
      orderByUrl.length
        ? `${ORDER_BY}="${orderByToUrlFriendly(orderByUrl)}"`
        : '',
    ].join('&')
  }, [date, orderBy, state.orderBy])

  const roomName = tableData.map((td) => td.room.name)?.[0]
  const siteName = tableData.map((td) => td.site.name)?.[0]

  useEffect(() => {
    const needRoomUpdate =
      roomId &&
      (state?.roomIds || '') !== roomId &&
      !state.roomIds?.includes(roomId)

    if (needRoomUpdate) {
      onRoomIdsChange(roomId)
      onSiteIdsChange(siteId || undefined)
    }
  }, [state, onRoomIdsChange, onSiteIdsChange, roomId, siteId])

  const onNavigate = useCallback(
    (ix: number) => {
      if (stateTableData) {
        eventLogger(EVENTS.UPDATE_ROOM_NEXT_PREV, {
          siteId: stateTableData[ix].site.id,
          roomId: stateTableData[ix].room.id,
        })
        setSiteId(stateTableData[ix].site.id)
        setRoomId(stateTableData[ix].room.id)
      }
    },
    [eventLogger, setRoomId, setSiteId, stateTableData]
  )

  if (!roomId || !siteId) {
    return null
  }

  return (
    <>
      {(dataLoading || caseStaffLoading || casePatientDataLoading) && (
        <LoadingOverlay />
      )}
      <div
        css={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          height: '100%',
          width: '100%',
        }}
      >
        <div
          css={{
            display: 'grid',
            justifyItems: 'start',
            alignContent: 'start',
            padding: `0 ${remSpacing.large}`,
            rowGap: remSpacing.medium,
            borderTop: `solid 1px ${theme.palette.gray[30]}`,
          }}
        >
          <PageHeader
            title={roomName}
            subTitle={siteName}
            actions={
              !!stateTableData &&
              prevIx !== undefined &&
              nextIx !== undefined && (
                <div
                  css={{
                    display: 'grid',
                    gridAutoFlow: 'column',
                    width: '100%',
                  }}
                >
                  {prevIx !== currentIx ? (
                    <ButtonLink
                      appearance={'link'}
                      size={'sm'}
                      onClick={() => onNavigate(prevIx)}
                      to={{
                        pathname: generatePath(LocationPath.CasePlanningRoom),
                        search: `date=%22${dateTimeToUrlFriendlyDate(
                          date
                        )}%22&siteIds=%22${
                          stateTableData[prevIx].site.id || ''
                        }%22&roomIds=%22${
                          stateTableData[prevIx].room.id || ''
                        }%22`,
                      }}
                      state={{
                        tableData: stateTableData,
                      }}
                    >
                      <ChevronLeft color={theme.palette.gray[50]} size={'xs'} />
                      Prev Room
                    </ButtonLink>
                  ) : (
                    <Button
                      appearance={'link'}
                      size={'sm'}
                      disabled
                      onClick={() => onNavigate(prevIx)}
                    >
                      <ChevronLeft color={theme.palette.gray[50]} size={'xs'} />
                      Prev Room
                    </Button>
                  )}
                  {nextIx !== currentIx ? (
                    <ButtonLink
                      appearance={'link'}
                      size={'sm'}
                      onClick={() => onNavigate(nextIx)}
                      to={{
                        pathname: generatePath(LocationPath.CasePlanningRoom),
                        search: `date=%22${dateTimeToUrlFriendlyDate(
                          date
                        )}%22&siteIds=%22${
                          stateTableData[nextIx].site.id || ''
                        }%22&roomIds=%22${
                          stateTableData[nextIx].room.id || ''
                        }%22`,
                      }}
                      state={{
                        tableData: stateTableData,
                      }}
                    >
                      Next Room
                      <ChevronRight
                        color={theme.palette.gray[50]}
                        size={'xs'}
                      />
                    </ButtonLink>
                  ) : (
                    <Button
                      appearance={'link'}
                      size={'sm'}
                      disabled
                      onClick={() => onNavigate(nextIx)}
                    >
                      Next Room
                      <ChevronRight
                        color={theme.palette.gray[50]}
                        size={'xs'}
                      />
                    </Button>
                  )}
                </div>
              )
            }
            breadcrumbs={[
              {
                to: {
                  pathname: LocationPath.CasePlanning,
                  search: backSearch,
                },
                id: 'case-planning',
                display: 'Case Planning',
              },
            ]}
          />
          <div>
            <P3>
              Set staff for the room and apply to cases. Edit individual case
              information to the right, if needed.
            </P3>
          </div>
          <CaseStaffPlanForm
            cancelDestination={{
              pathname: LocationPath.CasePlanning,
              search: backSearch,
            }}
            direction="column"
            caseIds={filteredCases.map((c) => c.scheduledCaseId)}
            onSubmit={onCaseStaffSubmit}
            disabled={caseStaffLoading}
          />
        </div>
        <div
          css={{
            display: 'grid',
            justifyItems: 'start',
            alignContent: 'start',
            padding: `${remSpacing.medium} ${remSpacing.large}`,
            rowGap: remSpacing.xsmall,
            background: theme.palette.background.secondary,
            borderLeft: `solid 1px ${theme.palette.gray[30]}`,
            borderTop: `solid 1px ${theme.palette.gray[30]}`,
          }}
        >
          {!dataLoading && (
            <>
              <H4 as="h2">
                {filteredCases.length || 0} cases in {roomName}
              </H4>

              <div
                css={{
                  display: 'grid',
                  justifyItems: 'start',
                  alignContent: 'start',
                  gap: remSpacing.medium,
                  width: '100%',
                  height: '100%',
                }}
              >
                {filteredCases.map((c, ix) => (
                  <Case
                    case={c}
                    key={c.scheduledCaseId}
                    defaultIsOpen={!ix}
                    caseNoteDataDisabled={caseNoteLoading}
                    caseStaffDisabled={caseStaffLoading}
                    casePatientDataDisabled={casePatientDataLoading}
                    onChangeCaseNoteField={onChangeCaseNoteField}
                    onChangeCaseStaffField={onChangeCaseStaffField}
                    onChangeCasePatientDataField={onChangeCasePatientDataField}
                  />
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </>
  )
}
