import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router'

import { ThemeProvider } from '@emotion/react'

import { InMemoryCache } from '@apollo/client'
import { MockedProvider } from '@apollo/client/testing'
import { act, render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Settings } from 'luxon'

import { theme } from '@apella/component-library'
import { SITES_KEY } from 'src/utils/useSitesState'

import { CasePlanningRoom } from '../CasePlanningRoom'
import CasePlanningRoomMocks, {
  resultThe12thFn,
  upsertCaseFlagDataResults,
  upsertCaseNotePlanResults,
} from '../test_mocks/CasePlanningRoom.mock'

// Mock useAuth0 hook
vi.mock('@auth0/auth0-react', () => ({
  withAuthenticationRequired: () => {},
  useAuth0: vi.fn(() => ({
    user: {
      org_id: 'apella_internal_0',
    },
  })),
}))
const cache = new InMemoryCache()
describe('Case Planning Room', () => {
  beforeAll(() => {
    // We rely on randomUUID to generate unique keys for the CasePlanningRoom component, for testing purposes we want to mock this to always return the same value
    Object.defineProperty(globalThis, 'crypto', {
      value: {
        randomUUID: () => '00000000-0000-0000-0000-000000000000',
      },
    })
  })

  beforeEach(() => {
    localStorage.setItem(SITES_KEY, '["lab_1"]')
    Settings.now = () => new Date(2023, 11, 15).valueOf()
    vi.resetModules()
  })
  afterAll(() => {
    vi.resetModules()
  })
  it('Renders the Case Planning Room for the 7th', async () => {
    const location = {
      ...window.location,
      search: `date="2023-12-07"&siteIds="lab_1"&roomIds="garage_0"`,
    }
    Object.defineProperty(window, 'location', {
      writable: true,
      value: location,
    })
    render(
      <MockedProvider
        mocks={CasePlanningRoomMocks}
        addTypename={true}
        cache={cache}
        defaultOptions={{
          watchQuery: {
            fetchPolicy: 'no-cache',
          },
        }}
      >
        <ThemeProvider theme={theme}>
          <CasePlanningRoom />
        </ThemeProvider>
      </MockedProvider>,
      { wrapper: BrowserRouter }
    )
    expect(await screen.findByText('Add staff to cases')).toBeInTheDocument()
    expect(await screen.findAllByText('Garage 0')).toHaveLength(1)
    expect(await screen.findByText('2 cases in Garage 0')).toBeInTheDocument()
  })
  it('Renders the Case Planning Room for the 12th', async () => {
    const location = {
      ...window.location,
      search: `date="2023-12-12"&siteIds="lab_1"&roomIds="garage_0"`,
    }
    Object.defineProperty(window, 'location', {
      writable: true,
      value: location,
    })
    render(
      <MockedProvider
        mocks={CasePlanningRoomMocks}
        addTypename={true}
        cache={cache}
        defaultOptions={{
          watchQuery: {
            fetchPolicy: 'no-cache',
          },
        }}
      >
        <ThemeProvider theme={theme}>
          <CasePlanningRoom />
        </ThemeProvider>
      </MockedProvider>,
      { wrapper: BrowserRouter }
    )
    expect(await screen.findByText('Add staff to cases')).toBeInTheDocument()
    expect(await screen.findAllByText('Garage 0')).toHaveLength(1)
    expect(await screen.findByText('1 cases in Garage 0')).toBeInTheDocument()

    expect(
      screen.getByRole('checkbox', {
        name: 'Latex Allergy Checkbox',
      })
    ).toBeInTheDocument()

    expect(
      screen.getByRole('checkbox', {
        name: 'Blood Products Needed Checkbox',
      })
    ).toBeInTheDocument()

    expect(
      screen.getByRole('checkbox', {
        name: 'Risk of Malignant Hyperthermia Checkbox',
      })
    ).toBeInTheDocument()

    expect(upsertCaseFlagDataResults).toHaveBeenCalledTimes(0)
    expect(resultThe12thFn).toHaveBeenCalledTimes(1)
    await userEvent.click(
      screen.getByRole('checkbox', { name: 'Latex Allergy Checkbox' })
    )

    expect(upsertCaseFlagDataResults).toHaveBeenCalled()
    expect(resultThe12thFn).toHaveBeenCalledTimes(2)

    expect(
      screen.getByRole('textbox', {
        name: 'Notes',
      })
    ).toBeInTheDocument()
    await userEvent.click(
      screen.getByRole('textbox', {
        name: 'Notes',
      })
    )
    // Handle Debounce.
    vi.useFakeTimers()
    const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime })

    // TODO: this should be await-ed, but I couldn't figure out how to make it not hang
    // See https://github.com/testing-library/user-event/issues/565
    user.type(screen.getByRole('textbox', { name: 'Notes' }), 'Added note')

    await act(async () => {
      await vi.runAllTimersAsync()
    })
    expect(upsertCaseNotePlanResults).toHaveBeenCalledTimes(1)
  })
})
