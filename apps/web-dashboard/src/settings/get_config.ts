import developmentConfig from './development'
import localhostConfig from './localhost'
import productionConfig from './production'
import stagingConfig from './staging'

export const getConfigByEnvironment = (apella_env: string) => {
  switch (apella_env) {
    case 'localhost':
      return localhostConfig
    case 'development':
      return developmentConfig
    case 'staging':
      return stagingConfig
    case 'production':
      return productionConfig
    default:
      throw new Error(`Invalid environment: ${apella_env}`)
  }
}
