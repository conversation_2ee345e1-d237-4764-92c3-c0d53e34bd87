import commonConfig from './commonConfig'
import { AppConfig } from './types'

const prodConfig: AppConfig = {
  ...commonConfig,
  environment: 'production',
  analytics: {
    amplitude: '8fd50c278d86ca1a291d958b4a803579',
  },
  api: {
    domain: 'https://api.apella.io',
    graphQLUrl: '/v1/graphql',
    url: '/v1',
  },
  auth0: {
    clientKey: 'nwUcAQsutBhaOSmfpNMK3119I9AdRE4S',
    domain: 'auth.apella.io',
    audience: 'https://api.apella.io',
  },
  auth0Boards: {
    clientKey: 'JUCyJlrKd0rJVkvkS1tWlD3Agf6lWTpw',
  },
  launchDarkly: {
    clientSideID: '61dc92a2f8dbfa1390e6687a',
  },
  logging: {
    environment: 'production',
  },
  cubejs: {
    url: 'https://cubejs.apella.io',
  },
}

export default prodConfig
