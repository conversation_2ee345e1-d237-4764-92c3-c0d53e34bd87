import { BrowserOptions } from '@sentry/react'

import { Environment } from '@apella/component-library'

export interface AnalyticsConfig {
  amplitude?: string
}

interface ApiConfig {
  domain: string
  graphQLUrl: string
  url: string
}

interface Auth0Config {
  audience: string
  clientKey: string
  domain: string
}

interface Auth0BoardsConfig {
  clientKey: string
}

interface LaunchDarklyConfig {
  clientSideID: string
}

interface CubejsConfig {
  url: string
}

interface DatadogConfig {
  applicationId: string
  clientToken: string
}

export interface AppConfig {
  analytics?: AnalyticsConfig
  api: ApiConfig
  auth0: Auth0Config
  auth0Boards: Auth0BoardsConfig
  cubejs: CubejsConfig
  datadog: DatadogConfig
  environment: Environment
  launchDarkly: LaunchDarklyConfig
  logging: Pick<BrowserOptions, 'debug' | 'environment' | 'release'>
  version?: string
}
