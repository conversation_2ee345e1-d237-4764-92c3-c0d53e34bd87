import commonConfig from './commonConfig'
import { AppConfig } from './types'

const localhostConfig: AppConfig = {
  ...commonConfig,
  environment: 'localhost',
  analytics: {
    amplitude: 'fb6c5f5f66434ec445d41ef51e92ad45',
  },
  api: {
    domain: 'http://0.0.0.0:8080',
    graphQLUrl: '/v1/graphql',
    url: '/v1',
  },
  auth0: {
    clientKey: '68ncmj2qHc3IEjyKs3rZjt8lzzDug5AU',
    domain: 'apella-dev.us.auth0.com',
    audience: 'https://api.dev.apella.io',
  },
  auth0Boards: {
    clientKey: 'Kf2ZlnryOyJDcOaWJ7FMRvsSyCBtaUh0',
  },
  launchDarkly: {
    clientSideID: '61dc941293e3e013ad50c4fe',
  },
  logging: { environment: 'development' },
  cubejs: {
    url: 'http://localhost:4000',
  },
}

export default localhostConfig
