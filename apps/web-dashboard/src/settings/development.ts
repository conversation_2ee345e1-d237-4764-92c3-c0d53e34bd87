import commonConfig from './commonConfig'
import { AppConfig } from './types'

const devConfig: AppConfig = {
  ...commonConfig,
  environment: 'development',
  analytics: {
    amplitude: 'fb6c5f5f66434ec445d41ef51e92ad45',
  },
  api: {
    domain: 'https://api.dev.apella.io',
    graphQLUrl: '/v1/graphql',
    url: '/v1',
  },
  auth0: {
    clientKey: '68ncmj2qHc3IEjyKs3rZjt8lzzDug5AU',
    domain: 'apella-dev.us.auth0.com',
    audience: 'https://api.dev.apella.io',
  },
  auth0Boards: {
    clientKey: 'Kf2ZlnryOyJDcOaWJ7FMRvsSyCBtaUh0',
  },
  launchDarkly: {
    clientSideID: '61dc941293e3e013ad50c4fe',
  },
  logging: { environment: 'development' },
  cubejs: {
    url: 'https://cubejs.dev.apella.io',
  },
}

export default devConfig
