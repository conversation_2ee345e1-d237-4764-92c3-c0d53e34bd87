import commonConfig from './commonConfig'
import { AppConfig } from './types'

const stagingConfig: AppConfig = {
  ...commonConfig,
  environment: 'staging',
  analytics: {
    amplitude: '64a9760b7c597105abd8964c7ee3ccc7',
  },
  api: {
    domain: 'https://api.staging.apella.io',
    graphQLUrl: '/v1/graphql',
    url: '/v1',
  },
  auth0: {
    clientKey: 'ZiEDGjr7ZiRkx02BuSSeT4y2cAObh6KQ',
    domain: 'auth.staging.apella.io',
    audience: 'https://api.staging.apella.io',
  },
  auth0Boards: {
    clientKey: '7vaxvK59XBGbTNCs7EpoGntdVC0TqiSe',
  },
  launchDarkly: {
    clientSideID: '61dc9412d4ba94130937d3ec',
  },
  logging: {
    environment: 'staging',
  },
  cubejs: {
    url: 'https://cubejs.staging.apella.io',
  },
}

export default stagingConfig
