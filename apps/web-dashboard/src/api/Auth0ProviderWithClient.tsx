/* eslint-disable no-console */

/*
 * NOTE: This file is a forked version of the `Auth0Provider` component from the `@auth0/auth0-react` package.
 * The main difference is that it uses the `Auth0Client` instance provided to it instead of creating a new one.
 * This allows the same client instance to be used in loaders/actions and in the Provider itself.
 */

import { useCallback, useEffect, useMemo, useReducer } from 'react'

import {
  Auth0Context,
  Auth0ContextInterface,
  Auth0ProviderOptions,
  OAuthError,
} from '@auth0/auth0-react'
import {
  Auth0Client,
  PopupLoginOptions,
  PopupConfigOptions,
  GetTokenWithPopupOptions,
  RedirectLoginResult,
  GetTokenSilentlyOptions,
  User,
  RedirectLoginOptions,
  LogoutOptions,
} from '@auth0/auth0-spa-js'

const CODE_RE = /[?&]code=[^&]+/
const STATE_RE = /[?&]state=[^&]+/
const ERROR_RE = /[?&]error=[^&]+/

export const hasAuthParams = (searchParams = window.location.search): boolean =>
  (CODE_RE.test(searchParams) || ERROR_RE.test(searchParams)) &&
  STATE_RE.test(searchParams)

const normalizeErrorFn =
  (fallbackMessage: string) =>
  (error: unknown): Error => {
    if (error instanceof Error) {
      return error
    }
    // try to check errors of the following form: {error: string; error_description?: string}
    if (
      error !== null &&
      typeof error === 'object' &&
      'error' in error &&
      typeof error.error === 'string'
    ) {
      if (
        'error_description' in error &&
        typeof error.error_description === 'string'
      ) {
        return new OAuthError(error.error, error.error_description)
      }
      return new OAuthError(error.error)
    }
    return new Error(fallbackMessage)
  }

export const loginError = normalizeErrorFn('Login failed')

export const tokenError = normalizeErrorFn('Get access token failed')

/**
 * @ignore
 * Helper function to map the v1 `redirectUri` option to the v2 `authorizationParams.redirect_uri`
 * and log a warning.
 */
export const deprecateRedirectUri = (options?: any) => {
  if (options?.redirectUri) {
    console.warn(
      'Using `redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `redirectUri` will be no longer supported in a future version'
    )
    options.authorizationParams = options.authorizationParams || {}
    options.authorizationParams.redirect_uri = options.redirectUri
    delete options.redirectUri
  }

  if (options?.authorizationParams?.redirectUri) {
    console.warn(
      'Using `authorizationParams.redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `authorizationParams.redirectUri` will be removed in a future version'
    )
    options.authorizationParams.redirect_uri =
      options.authorizationParams.redirectUri
    delete options.authorizationParams.redirectUri
  }
}

/**
 * The auth state which, when combined with the auth methods, make up the return object of the `useAuth0` hook.
 */
interface AuthState<TUser extends User = User> {
  error?: Error
  isAuthenticated: boolean
  isLoading: boolean
  user?: TUser
}

/**
 * The initial auth state.
 */
const initialAuthState: AuthState = {
  isAuthenticated: false,
  isLoading: true,
}

/**
 * The state of the application before the user was redirected to the login page.
 */
export type AppState = {
  returnTo?: string
  [key: string]: any
}

interface Auth0ProviderPropsWithClient
  extends Omit<Auth0ProviderOptions, 'domain' | 'clientId'> {
  client: Auth0Client
}

type Action =
  | { type: 'LOGIN_POPUP_STARTED' }
  | {
      type:
        | 'INITIALISED'
        | 'LOGIN_POPUP_COMPLETE'
        | 'GET_ACCESS_TOKEN_COMPLETE'
        | 'HANDLE_REDIRECT_COMPLETE'
      user?: User
    }
  | { type: 'LOGOUT' }
  | { type: 'ERROR'; error: Error }

/**
 * Handles how that state changes in the `useAuth0` hook.
 */
export const reducer = (state: AuthState, action: Action): AuthState => {
  switch (action.type) {
    case 'LOGIN_POPUP_STARTED':
      return {
        ...state,
        isLoading: true,
      }
    case 'LOGIN_POPUP_COMPLETE':
    case 'INITIALISED':
      return {
        ...state,
        isAuthenticated: !!action.user,
        user: action.user,
        isLoading: false,
        error: undefined,
      }
    case 'HANDLE_REDIRECT_COMPLETE':
    case 'GET_ACCESS_TOKEN_COMPLETE':
      if (state.user === action.user) {
        return state
      }
      return {
        ...state,
        isAuthenticated: !!action.user,
        user: action.user,
      }
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: undefined,
      }
    case 'ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.error,
      }
  }
}

/**
 * ```jsx
 * <Auth0Provider client={client}>
 *   <MyApp />
 * </Auth0Provider>
 * ```
 *
 * Provides the Auth0Context to its child components.
 */
export const Auth0ProviderWithClient = (
  opts: Auth0ProviderPropsWithClient
): React.JSX.Element => {
  const { client, children, context = Auth0Context } = opts
  const [state, dispatch] = useReducer(reducer, initialAuthState)

  const handleError = useCallback((error: Error) => {
    dispatch({ type: 'ERROR', error })
    return error
  }, [])

  useEffect(() => {
    ;(async (): Promise<void> => {
      try {
        const user = await client.getUser()
        dispatch({ type: 'INITIALISED', user })
      } catch (error) {
        handleError(loginError(error))
      }
    })()
  }, [client, handleError])

  const loginWithRedirect = useCallback(
    (opts?: RedirectLoginOptions): Promise<void> => {
      deprecateRedirectUri(opts)

      return client.loginWithRedirect(opts)
    },
    [client]
  )

  const loginWithPopup = useCallback(
    async (
      options?: PopupLoginOptions,
      config?: PopupConfigOptions
    ): Promise<void> => {
      dispatch({ type: 'LOGIN_POPUP_STARTED' })
      try {
        await client.loginWithPopup(options, config)
      } catch (error) {
        handleError(loginError(error))
        return
      }
      const user = await client.getUser()
      dispatch({ type: 'LOGIN_POPUP_COMPLETE', user })
    },
    [client, handleError]
  )

  const logout = useCallback(
    async (opts: LogoutOptions = {}): Promise<void> => {
      await client.logout(opts)
      if (opts.openUrl || opts.openUrl === false) {
        dispatch({ type: 'LOGOUT' })
      }
    },
    [client]
  )

  const getAccessTokenSilently = useCallback(
    async (opts?: GetTokenSilentlyOptions): Promise<any> => {
      let token
      try {
        token = await client.getTokenSilently(opts)
      } catch (error) {
        throw tokenError(error)
      } finally {
        dispatch({
          type: 'GET_ACCESS_TOKEN_COMPLETE',
          user: await client.getUser(),
        })
      }
      return token
    },
    [client]
  )

  const getAccessTokenWithPopup = useCallback(
    async (
      opts?: GetTokenWithPopupOptions,
      config?: PopupConfigOptions
    ): Promise<string | undefined> => {
      let token
      try {
        token = await client.getTokenWithPopup(opts, config)
      } catch (error) {
        throw tokenError(error)
      } finally {
        dispatch({
          type: 'GET_ACCESS_TOKEN_COMPLETE',
          user: await client.getUser(),
        })
      }
      return token
    },
    [client]
  )

  const getIdTokenClaims = useCallback(
    () => client.getIdTokenClaims(),
    [client]
  )

  const handleRedirectCallback = useCallback(
    async (url?: string): Promise<RedirectLoginResult> => {
      try {
        return await client.handleRedirectCallback(url)
      } catch (error) {
        throw tokenError(error)
      } finally {
        dispatch({
          type: 'HANDLE_REDIRECT_COMPLETE',
          user: await client.getUser(),
        })
      }
    },
    [client]
  )

  const contextValue = useMemo<Auth0ContextInterface<User>>(() => {
    return {
      ...state,
      getAccessTokenSilently,
      getAccessTokenWithPopup,
      getIdTokenClaims,
      loginWithRedirect,
      loginWithPopup,
      logout,
      handleRedirectCallback,
    }
  }, [
    state,
    getAccessTokenSilently,
    getAccessTokenWithPopup,
    getIdTokenClaims,
    loginWithRedirect,
    loginWithPopup,
    logout,
    handleRedirectCallback,
  ])

  return <context.Provider value={contextValue}>{children}</context.Provider>
}
