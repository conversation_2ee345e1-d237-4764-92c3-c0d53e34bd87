import { replace } from 'react-router'

import { Auth0Client } from '@auth0/auth0-spa-js'

export async function getAccessTokenSilentlyOrLogout(
  auth0: Auth0Client
): Promise<string> {
  let token = ''
  try {
    token = await auth0.getTokenSilently()
  } catch (e) {
    if (e instanceof Error) {
      if (Object.prototype.hasOwnProperty.call(e, 'error')) {
        const error = Object.getOwnPropertyDescriptor(e, 'error')
        if (
          ['missing_refresh_token', 'invalid_grant', 'login_required'].includes(
            error?.value
          )
        ) {
          await auth0.loginWithRedirect()
        }
      } else {
        await auth0.logout({
          openUrl: () => {
            replace('/')
          },
        })
      }
    } else {
      await auth0.logout({
        openUrl: () => {
          replace('/')
        },
      })
    }
  }
  return token
}
