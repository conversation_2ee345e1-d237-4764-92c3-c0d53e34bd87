import {
  <PERSON>Link,
  ServerError,
  TypePolicies,
  createHttpLink,
} from '@apollo/client'
import { onError } from '@apollo/client/link/error'
import { addBreadcrumb, startSpanManual } from '@sentry/browser'
import { OperationTypeNode, OperationDefinitionNode, Kind } from 'graphql'

import { settings } from 'src/settings'
import { logger } from 'src/utils/exceptionLogging'

export const typePolicies: TypePolicies = {
  ApellaCaseStatus: { keyFields: ['name', 'since'] },
  RoomStatus: { keyFields: ['name', 'since'] },
  CaseStaff: { keyFields: ['role', 'staff', ['id']] },
  Room: { fields: { turnovers: { merge: false } } },
}

const getOperationType = (types: Set<OperationTypeNode>) => {
  if (types.size > 1) {
    return 'multiple'
  }

  if (types.size < 1) {
    return 'unknown'
  }

  const [type] = types

  return type
}

export const sentrySpanLink = new ApolloLink((operation, forward) => {
  const operationName = operation.operationName || 'Unnamed Operation'

  const operationTypes = operation.query.definitions
    .filter(
      (def): def is OperationDefinitionNode =>
        def.kind === Kind.OPERATION_DEFINITION
    )
    .reduce<
      Set<OperationTypeNode>
    >((acc, def) => acc.add(def.operation), new Set())

  const operationType = getOperationType(operationTypes)

  addBreadcrumb({
    category: 'graphql',
    data: {
      operationName,
      operation: operationType,
      body: operation.query.loc?.source.body.slice(0, 100),
    },
  })

  return startSpanManual(
    {
      name: operationName,
      op: 'http.graphql.' + operationType,
      attributes: {
        body: operation.query.loc?.source.body.slice(0, 100),
      },
    },
    (span) =>
      forward(operation).map((request) => {
        span?.end()
        return request
      })
  )
})

export const createErrorLink = (userId: string) =>
  onError(({ graphQLErrors, networkError, operation }) => {
    if (graphQLErrors) {
      graphQLErrors.forEach(({ message, locations, path }) =>
        logger.warn(
          `[GraphQL error]: Message: ${message}`,
          {
            locations,
            path,
            operationName: operation.operationName,
          },
          userId,
          {
            operationName: operation.operationName,
          }
        )
      )
    }

    // This might change to a log or a warning in the future,
    // but because of 500 errors that we can't see returning to the client
    // I want an error for the time being
    if (networkError) {
      const extras =
        'response' in networkError
          ? Object.fromEntries(networkError.response.headers.entries())
          : {}

      logger.error(
        `[Network error]: ${networkError.message}`,
        {
          ...extras,
          operationName: operation.operationName,
        },
        userId,
        { operationName: operation.operationName }
      )
    }
  })

const customFetch = (uri: RequestInfo | URL, options?: RequestInit) => {
  return fetch(uri, options).then(async (response) => {
    if (response.status >= 500) {
      // or handle 400 errors
      const responseBody = await response.text()
      const networkError: ServerError = {
        name: 'NetworkError',
        response,
        message: responseBody,
        statusCode: response.status,
        result: {},
      }
      return Promise.reject(networkError)
    }
    return response
  })
}

export const httpLink = createHttpLink({
  uri: `${settings.api.domain}${settings.api.graphQLUrl}`,
  fetch: customFetch,
})
