import {
  ApolloClient,
  createQ<PERSON><PERSON><PERSON>reloader,
  InMemoryCache,
  NormalizedCacheObject,
  PreloadQueryFunction,
} from '@apollo/client'
import { Auth0Client } from '@auth0/auth0-spa-js'
import { initialize, LDClient, LDFlagSet } from 'launchdarkly-react-client-sdk'

import { ACTIVE_ORG_ID_KEY } from 'src/router/constants'
import { settings } from 'src/settings'
import { logger } from 'src/utils/exceptionLogging'
import { createLDFlagsProxy } from 'src/utils/launchDarklyFlagProxy'

import { typePolicies } from './apolloClient'

export type ApellaDataDependencies = {
  apolloClient: ApolloClient<NormalizedCacheObject>
  auth0: Auth0Client
  boardsAuth0: Auth0Client
  ldClient: LDClient
  preloadQuery: PreloadQueryFunction
  flags: LDFlagSet
}

export const createDataDependencies = () => {
  const urlSearchParams = new URLSearchParams(window.location.search)
  const invitation = getInvitationCode(urlSearchParams)
  let previousActiveOrgId = ''
  try {
    previousActiveOrgId = JSON.parse(
      window.localStorage.getItem(ACTIVE_ORG_ID_KEY) || '""'
    )
  } catch (e) {
    logger.error('Error parsing previous active org id from local storage', {
      errorMessage: e,
    })
  }
  const organization = getOrganizationId(urlSearchParams, previousActiveOrgId)

  const auth0 = new Auth0Client({
    domain: settings.auth0.domain,
    clientId: settings.auth0.clientKey,
    useRefreshTokens: true,
    useRefreshTokensFallback: false,
    authorizationParams: {
      redirect_uri: window.location.origin,
      audience: settings.auth0.audience,
      organization,
      invitation,
    },
  })

  const boardsAuth0 = new Auth0Client({
    domain: settings.auth0.domain,
    clientId: settings.auth0Boards.clientKey,
    useRefreshTokens: true,
    useRefreshTokensFallback: false,
    authorizationParams: {
      redirect_uri: window.location.origin,
      audience: settings.auth0.audience,
      organization,
      invitation,
    },
  })

  const ldClient = initialize(
    settings.launchDarkly.clientSideID,
    // Set as anonymous for now, but the identify call in AnalyticsEventsProvider overrides this
    { kind: 'user', key: 'anonymous' },
    {
      streaming: true,
      logger: {
        ...logger,
        debug: () => undefined,
        info: () => undefined,
        // don't log launchdarkly connection errors
        // https://github.com/launchdarkly/js-client-sdk/issues/249#issuecomment-**********
        warn: (message) =>
          !message.indexOf(
            'Error on stream connection: {"isTrusted":true}, will continue retrying'
          ) && logger.warn(message),
        error: (message) =>
          !message.indexOf('network error') && logger.error(message),
      },
    }
  )
  // don't log network errors
  // https://support.launchdarkly.com/hc/en-us/articles/12998125691419-Error-LaunchDarklyFlagFetchError-network-error
  ldClient.on('error', () => {})

  const flags = createLDFlagsProxy(ldClient)

  const cache = new InMemoryCache({ typePolicies })
  const apolloClient = new ApolloClient({ cache })
  const preloadQuery = createQueryPreloader(apolloClient)

  return {
    auth0,
    boardsAuth0,
    ldClient,
    apolloClient,
    preloadQuery,
    flags,
  }
}

const getInvitationCode = (
  urlSearchParams: URLSearchParams
): string | undefined => {
  return urlSearchParams.get('invitation') ?? undefined
}

/**
 * Get the `organization` from either:
 * 1. In the context of an Auth0 Invite acceptance, fetch from the urlSearchParams
 * 2. In the context of a user who has previously logged in, fetch from local storage
 */
const getOrganizationId = (
  urlSearchParams: URLSearchParams,
  previousActiveOrgId: string
): string | undefined => {
  const orgFromUrl = urlSearchParams.get('organization') ?? undefined
  if (orgFromUrl) {
    return orgFromUrl
  }
  return previousActiveOrgId || undefined
}
