import { useContext } from 'react'

import { ResultSet } from '@cubejs-client/core'
import { CubeContext, useCubeQuery } from '@cubejs-client/react'

import {
  CASE_STAFF,
  CASES,
  CUBE_QUERY_LIMIT,
  PROCEDURES,
  STAFF,
} from 'src/modules/cube/types/dataCubes'
import { RawClinicalData } from 'src/pages/Insights/types'

import {
  ANESTHESIA_ROLES,
  CIRCULATOR_ROLES,
  PRIMARY_SURGEON_ROLES,
  SCRUB_TECH_ROLES,
} from '../../../utils/roles'

export interface ClinicalData {
  anesthesiaNames: string[]
  caseId: string
  circulatorNames?: string[] // ORCA-91: consider removing nullability once used across Insights page tabs
  procedureNames: string[]
  scrubTechNames?: string[] // ORCA-91: consider removing nullability once used across Insights page tabs
  surgeonNames: string[]
}

interface ClinicalDataMap {
  caseIdToClinicalData: Map<string, ClinicalData>
  isLoading: boolean
}

interface RowLevelClinicalData {
  caseId: string
  procedureName?: string
  staffFirstName?: string
  staffLastName?: string
  staffRole?: string
}

const getIdToClinicalData = (rowLevelClinicalData?: RowLevelClinicalData[]) => {
  return (
    rowLevelClinicalData?.reduce((acc, edge) => {
      const id = edge.caseId
      const staffName = edge.staffLastName
        ? `${edge.staffLastName}, ${edge.staffFirstName}`
        : undefined
      const procedureName = edge.procedureName

      const isSurgeon =
        !edge.staffRole ||
        (edge.staffRole && PRIMARY_SURGEON_ROLES.includes(edge.staffRole))

      const isAnesthesia =
        !edge.staffRole ||
        (edge.staffRole && ANESTHESIA_ROLES.includes(edge.staffRole))

      const isCirculator =
        !edge.staffRole ||
        (edge.staffRole && CIRCULATOR_ROLES.includes(edge.staffRole))

      const isScrubTech =
        !edge.staffRole ||
        (edge.staffRole && SCRUB_TECH_ROLES.includes(edge.staffRole))

      const record = acc.get(id)
      if (record) {
        if (
          staffName &&
          !record.surgeonNames.includes(staffName) &&
          isSurgeon
        ) {
          record.surgeonNames.push(staffName)
        } else if (
          staffName &&
          !record.anesthesiaNames.includes(staffName) &&
          isAnesthesia
        ) {
          record.anesthesiaNames.push(staffName)
        } else if (
          staffName &&
          !record.circulatorNames?.includes(staffName) &&
          isCirculator
        ) {
          record.circulatorNames?.push(staffName)
        } else if (
          staffName &&
          !record.scrubTechNames?.includes(staffName) &&
          isScrubTech
        ) {
          record.scrubTechNames?.push(staffName)
        }
        if (procedureName && !record.procedureNames.includes(procedureName)) {
          record.procedureNames.push(procedureName)
        }
      } else {
        acc.set(id, {
          caseId: id,
          surgeonNames: isSurgeon && staffName ? [staffName] : [],
          anesthesiaNames: isAnesthesia && staffName ? [staffName] : [],
          procedureNames: procedureName ? [procedureName] : [],
          scrubTechNames: isScrubTech && staffName ? [staffName] : [],
          circulatorNames: isCirculator && staffName ? [staffName] : [],
        })
      }

      return acc
    }, new Map<string, ClinicalData>()) ?? new Map<string, ClinicalData>()
  )
}

// React hook to be used in the UI.
export const useCaseIdToClinicalDataMap = (
  caseIds: string[]
): ClinicalDataMap => {
  const skip = !caseIds.length || caseIds.every((id) => !id)

  const { isLoading, resultSet: rawClinicalData } =
    useCubeQuery<RawClinicalData>(
      {
        dimensions: [
          CASES.CASE_ID,
          CASE_STAFF.ROLE,
          STAFF.FIRST_NAME,
          STAFF.LAST_NAME,
        ],
        filters: [
          {
            member: CASES.CASE_ID,
            operator: 'equals',
            values: caseIds,
          },
          {
            member: CASE_STAFF.ROLE,
            operator: 'equals',
            values: [
              ...PRIMARY_SURGEON_ROLES,
              ...ANESTHESIA_ROLES,
              ...CIRCULATOR_ROLES,
              ...SCRUB_TECH_ROLES,
            ],
          },
        ],
      },
      { skip }
    )

  const { isLoading: isLoadingProcedures, resultSet: procedureData } =
    useCubeQuery<RawClinicalData>(
      {
        dimensions: [CASES.CASE_ID, PROCEDURES.NAME],
        filters: [
          {
            member: CASES.CASE_ID,
            operator: 'equals',
            values: caseIds,
          },
          {
            member: PROCEDURES.NAME,
            operator: 'set',
          },
        ],
      },
      { skip }
    )

  const proceduresAndStaff =
    !isLoadingProcedures && !isLoading
      ? (procedureData?.rawData() ?? []).concat(
          rawClinicalData?.rawData() ?? []
        )
      : undefined

  // Generate Case to ClinicalDimensions mapping
  const caseIdToClinicalData = getIdToClinicalData(
    proceduresAndStaff?.map<RowLevelClinicalData>((edge) => ({
      caseId: edge[CASES.CASE_ID]?.toString() ?? '',
      staffRole: edge[CASE_STAFF.ROLE]?.toString(),
      staffFirstName: edge[STAFF.FIRST_NAME] ?? undefined,
      staffLastName: edge[STAFF.LAST_NAME] ?? undefined,
      procedureName: edge[PROCEDURES.NAME] ?? undefined,
    }))
  )

  return {
    isLoading: isLoadingProcedures || isLoading,
    caseIdToClinicalData,
  }
}

// Manual call to the Cube API for data export.
export const useClinicalData = () => {
  const { cubeApi } = useContext(CubeContext)

  const fetchClinicalData = async (ids: string[]) => {
    const [staff, procedures] = (await Promise.all([
      cubeApi.load({
        limit: CUBE_QUERY_LIMIT,
        dimensions: [
          CASES.CASE_ID,
          CASE_STAFF.ROLE,
          STAFF.FIRST_NAME,
          STAFF.LAST_NAME,
        ],
        filters: [
          {
            member: CASES.CASE_ID,
            operator: 'equals',
            values: ids,
          },
          {
            member: CASE_STAFF.ROLE,
            operator: 'equals',
            values: [
              ...PRIMARY_SURGEON_ROLES,
              ...ANESTHESIA_ROLES,
              ...CIRCULATOR_ROLES,
              ...SCRUB_TECH_ROLES,
            ],
          },
        ],
      }),
      cubeApi.load({
        dimensions: [CASES.CASE_ID, PROCEDURES.NAME],
        filters: [
          {
            member: CASES.CASE_ID,
            operator: 'equals',
            values: ids,
          },
          {
            member: PROCEDURES.NAME,
            operator: 'set',
          },
        ],
      }),
    ])) as [
      ResultSet<RawClinicalData> | null,
      ResultSet<RawClinicalData> | null,
    ]

    const staffAndProcedures = (procedures?.rawData() || []).concat(
      staff?.rawData() || []
    )

    return getIdToClinicalData(
      staffAndProcedures.map((edge) => ({
        caseId: edge[CASES.CASE_ID]?.toString() ?? '',
        staffRole: edge[CASE_STAFF.ROLE]?.toString(),
        staffFirstName: edge[STAFF.FIRST_NAME] ?? undefined,
        staffLastName: edge[STAFF.LAST_NAME] ?? undefined,
        procedureName: edge[PROCEDURES.NAME] ?? undefined,
      }))
    )
  }

  return { fetchClinicalData }
}
