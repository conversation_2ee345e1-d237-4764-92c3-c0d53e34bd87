import { useEffect, useMemo, useState } from 'react'

import { DocumentNode, gql, useQuery } from '@apollo/client'
import { Query } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'

import { ProcedureShape } from 'src/components/ProceduresFilter'
import { StaffShape } from 'src/components/StaffFilter'
import {
  CASE_PROCEDURES,
  CASE_STAFF_ANESTHESIA,
  CASE_STAFF_CIRCULATOR,
  CASE_STAFF_SCRUB_TECH,
  CASE_STAFF_SURGEONS,
  PROCEDURES,
} from 'src/modules/cube/types/dataCubes'
import { getFullName } from 'src/utils/getFullName'

import {
  GetAnesthesiaFilter,
  GetCirculatorFilter,
  GetPrimarySurgeonsFilter,
  GetProceduresFilter,
  GetScrubTechsFilter,
} from './__generated__'

export const useProceduresWithCount = (baseParams: Query, measure: string) => {
  const queryWithoutProcedureFilter = useMemo(
    () =>
      removeExistingFilterFromCubeParams(
        CASE_PROCEDURES.PROCEDURE_ID,
        baseParams
      ),
    [baseParams]
  )

  // Filters data
  const { data: filtersData, loading: isLoadingFilterData } =
    useQuery<GetProceduresFilter>(gql`
      query GetProceduresFilter {
        procedures(query: {}) {
          edges {
            node {
              id
              name
            }
          }
        }
      }
    `)

  const procedures = useMemo(
    () => filtersData?.procedures.edges ?? [],
    [filtersData?.procedures.edges]
  )

  const [proceduresWithCount, setProceduresWithCount] = useState<
    ProcedureShape[]
  >([])

  const { isLoading, resultSet: clinicalProcedureResultSet } = useCubeQuery({
    ...queryWithoutProcedureFilter,
    dimensions: [PROCEDURES.NAME],
    measures: [measure],
    order: { [measure]: 'desc' },
  })

  const procedureCount = useMemo(() => {
    const clinicalProcedureResult = clinicalProcedureResultSet?.rawData() ?? []
    return clinicalProcedureResult.map((procedureCount) => ({
      procedureName: procedureCount[PROCEDURES.NAME],
      count: procedureCount[measure],
    }))
  }, [clinicalProcedureResultSet, measure])

  useEffect(() => {
    if (!isLoading && !isLoadingFilterData) {
      // counted and sorted
      const countMap = new Map<string, number>()

      procedureCount.forEach((record: any) => {
        countMap.set(record.procedureName, +record.count)
      })

      setProceduresWithCount(
        procedures.map((p) => ({
          node: {
            id: p.node.id,
            name: p.node.name,
            count: countMap.get(p.node.name) ?? 0,
          },
        }))
      )
    }
  }, [isLoading, procedureCount, isLoadingFilterData, procedures])

  return {
    proceduresWithCount,
  }
}

const useBaseStaffWithCount = <
  FilterQueryType extends
    | GetAnesthesiaFilter
    | GetPrimarySurgeonsFilter
    | GetCirculatorFilter
    | GetScrubTechsFilter,
>(
  gqlQuery: DocumentNode,
  dimension: string,
  baseParams: Query,
  measure: string
) => {
  const { data: filtersData, loading: isLoadingFilterData } =
    useQuery<FilterQueryType>(gqlQuery)

  const staff = useMemo(() => {
    const staff = filtersData?.staff.edges.filter((edge) => edge !== null)
    return staff ?? []
  }, [filtersData?.staff.edges])

  const [staffWithCount, setStaffWithCount] = useState<StaffShape[]>([])

  const { isLoading, resultSet: staffResults } = useCubeQuery({
    ...baseParams,
    dimensions: [dimension],
    measures: [measure],
    filters: [...(baseParams.filters ?? [])],
    order: { [measure]: 'desc' },
  })

  const staffCountMap = useMemo(() => {
    const staffCountEntries: [string, number][] = (
      staffResults?.rawData() ?? []
    ).map((staffCount) => [
      String(staffCount[dimension]),
      Number(staffCount[measure]),
    ])
    return Object.fromEntries(staffCountEntries)
  }, [staffResults, dimension, measure])

  useEffect(() => {
    if (!isLoading && !isLoadingFilterData) {
      setStaffWithCount(
        staff.map((s) => ({
          node: {
            ...s.node,
            name: getFullName(s.node.lastName, s.node.firstName),
            // Match the CubeJS and GraphQL query results on the staff ID.
            count: staffCountMap[s.node.id] ?? 0,
          },
        }))
      )
    }
  }, [isLoading, staffCountMap, isLoadingFilterData, staff])

  return {
    staffWithCount,
  }
}

export const useAnesthesiaStaffWithCount = (
  baseParams: Query,
  measure: string
) => {
  const queryWithoutAnesthesiaFilter = useMemo(
    () =>
      removeExistingFilterFromCubeParams(
        CASE_STAFF_ANESTHESIA.STAFF_ID,
        baseParams
      ),
    [baseParams]
  )
  const gqlQueryString = gql`
    query GetAnesthesiaFilter {
      staff(query: { staffRoles: [ANESTHESIA] }) {
        edges {
          node {
            id
            lastName
            firstName
          }
        }
      }
    }
  `
  return useBaseStaffWithCount<GetAnesthesiaFilter>(
    gqlQueryString,
    CASE_STAFF_ANESTHESIA.STAFF_ID,
    queryWithoutAnesthesiaFilter,
    measure
  )
}

export const usePrimarySurgeonsWithCount = (
  baseParams: Query,
  measure: string
) => {
  const queryWithoutPrimarySurgeonFilter = useMemo(
    () =>
      removeExistingFilterFromCubeParams(
        CASE_STAFF_SURGEONS.STAFF_ID,
        baseParams
      ),
    [baseParams]
  )
  const gqlQuery = gql`
    query GetPrimarySurgeonsFilter {
      staff(query: { onlyPrimarySurgeons: true }) {
        edges {
          node {
            id
            lastName
            firstName
          }
        }
      }
    }
  `
  return useBaseStaffWithCount<GetPrimarySurgeonsFilter>(
    gqlQuery,
    // We have to use CASE_STAFF_SURGEONS.STAFF_ID here because otherwise the query results will include cases in which
    // a surgeon might have had a different role (e.g. "First Assist" or "Second Assist"), and we do not want to include
    // those in the count of "primary" role cases for the surgeon.
    CASE_STAFF_SURGEONS.STAFF_ID,
    queryWithoutPrimarySurgeonFilter,
    measure
  )
}

export const useCirculatorsWithCount = (baseParams: Query, measure: string) => {
  const queryWithoutCirculatorFilter = useMemo(
    () =>
      removeExistingFilterFromCubeParams(
        CASE_STAFF_CIRCULATOR.STAFF_ID,
        baseParams
      ),
    [baseParams]
  )
  const gqlQueryString = gql`
    query GetCirculatorFilter {
      staff(query: { staffRoles: [CIRCULATOR] }) {
        edges {
          node {
            id
            lastName
            firstName
          }
        }
      }
    }
  `

  return useBaseStaffWithCount<GetCirculatorFilter>(
    gqlQueryString,
    CASE_STAFF_CIRCULATOR.STAFF_ID,
    queryWithoutCirculatorFilter,
    measure
  )
}

export const useScrubTechsWithCount = (baseParams: Query, measure: string) => {
  const queryWithoutScrubTechFilter = useMemo(
    () =>
      removeExistingFilterFromCubeParams(
        CASE_STAFF_SCRUB_TECH.STAFF_ID,
        baseParams
      ),
    [baseParams]
  )
  const gqlQueryString = gql`
    query GetScrubTechsFilter {
      staff(query: { staffRoles: [SCRUB_TECH] }) {
        edges {
          node {
            id
            lastName
            firstName
          }
        }
      }
    }
  `

  return useBaseStaffWithCount<GetScrubTechsFilter>(
    gqlQueryString,
    CASE_STAFF_SCRUB_TECH.STAFF_ID,
    queryWithoutScrubTechFilter,
    measure
  )
}

/**
 * Given Cube params, this function removes an existing filter with id value of param
 * filterIdToRemove, return a new Cube params object; removing a filter is most helpful for when
 * unfiltered queries are needed, too (eg: wanting to know how many cases would be filtered to _if_
 * a surgeon was filtered on, like for dropdowns)
 */
const removeExistingFilterFromCubeParams = (
  filterIdToRemove: string,
  baseParams: Query
) => {
  return {
    ...baseParams,
    filters: baseParams.filters?.filter(
      (filter) => !('member' in filter) || filter.member !== filterIdToRemove
    ),
  }
}
