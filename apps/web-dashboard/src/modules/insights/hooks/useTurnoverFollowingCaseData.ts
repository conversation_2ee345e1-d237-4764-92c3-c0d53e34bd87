import { useCubeQuery } from '@cubejs-client/react'

import {
  CASE_DERIVED_PROPERTIES,
  CASE_STAFF,
  CASES,
  PROCEDURES,
  SERVICE_LINES,
  STAFF,
} from 'src/modules/cube/types/dataCubes'
import { RawClinicalData } from 'src/pages/Insights/types'

import { ANESTHESIA_ROLES, PRIMARY_SURGEON_ROLES } from '../../../utils/roles'

export interface FollowingCaseData {
  actualStartTime?: string
  anesthesiaNames: string[]
  caseClassificationTypeId?: string
  caseId: string
  customerCaseId?: string
  isAddOn?: boolean
  isFollowing?: boolean
  isInFlipRoom?: boolean
  patientClass?: string
  procedureNames: string[]
  scheduledStartTime?: string
  serviceLineName?: string
  surgeonNames: string[]
}

interface FollowingCaseDataMap {
  caseIdToClinicalData: Map<string, FollowingCaseData>
  isLoading: boolean
}

interface RowLevelFollowingCaseData {
  actualStartTime?: string
  caseClassificationId?: string
  caseId: string
  customerCaseId?: string
  isAddOn?: boolean
  isInFlipRoom?: boolean
  patientClassificationId?: string
  precedingCaseId?: string
  procedureName?: string
  scheduledStartTime?: string
  serviceLineName?: string
  staffFirstName?: string
  staffLastName?: string
  staffRole?: string
}

export const getIdToFollowingCaseData = (
  rowLevelClinicalData?: RowLevelFollowingCaseData[]
) => {
  return (
    rowLevelClinicalData?.reduce((acc, edge) => {
      const caseId = edge.caseId
      const staffName = edge.staffLastName
        ? `${edge.staffLastName}, ${edge.staffFirstName}`
        : undefined
      const procedureName = edge.procedureName

      const isSurgeon =
        !edge.staffRole ||
        (edge.staffRole && PRIMARY_SURGEON_ROLES.includes(edge.staffRole))

      const isAnesthesia =
        !edge.staffRole ||
        (edge.staffRole && ANESTHESIA_ROLES.includes(edge.staffRole))

      const record = acc.get(caseId)
      if (record) {
        if (
          staffName &&
          !record.surgeonNames.includes(staffName) &&
          isSurgeon
        ) {
          record.surgeonNames.push(staffName)
        } else if (
          staffName &&
          !record.anesthesiaNames.includes(staffName) &&
          isAnesthesia
        ) {
          record.anesthesiaNames.push(staffName)
        }
        if (procedureName && !record.procedureNames.includes(procedureName)) {
          record.procedureNames.push(procedureName)
        }
        if (edge.customerCaseId) {
          record.customerCaseId = edge.customerCaseId
        }
        if (edge.serviceLineName) {
          record.serviceLineName = edge.serviceLineName
        }
        if (edge.isAddOn) {
          record.isAddOn = edge.isAddOn
        }
        if (edge.isInFlipRoom) {
          record.isInFlipRoom = edge.isInFlipRoom
        }
        if (edge.precedingCaseId) {
          record.isFollowing = true
        }
        if (edge.caseClassificationId) {
          record.caseClassificationTypeId = edge.caseClassificationId
        }
        if (edge.patientClassificationId) {
          record.patientClass = edge.patientClassificationId
        }
        if (edge.actualStartTime) {
          record.actualStartTime = edge.actualStartTime
        }
        if (edge.scheduledStartTime) {
          record.scheduledStartTime = edge.scheduledStartTime
        }
      } else {
        acc.set(caseId, {
          caseId: caseId,
          surgeonNames: isSurgeon && staffName ? [staffName] : [],
          anesthesiaNames: isAnesthesia && staffName ? [staffName] : [],
          procedureNames: procedureName ? [procedureName] : [],
          customerCaseId: edge.customerCaseId,
          serviceLineName: edge.serviceLineName,
          isAddOn: edge.isAddOn ?? false,
          isInFlipRoom: edge.isInFlipRoom ?? false,
          isFollowing: !!edge.precedingCaseId,
          caseClassificationTypeId: edge.caseClassificationId,
          patientClass: edge.patientClassificationId,
          actualStartTime: edge.actualStartTime,
          scheduledStartTime: edge.scheduledStartTime,
        })
      }

      return acc
    }, new Map<string, FollowingCaseData>()) ??
    new Map<string, FollowingCaseData>()
  )
}

export const useCaseIdToFollowingCaseDataMap = (
  caseIds: string[]
): FollowingCaseDataMap => {
  const skip = !caseIds.length || caseIds.every((id) => !id)

  const { isLoading: isFollowingCaseLoading, resultSet: rawFollowingCaseData } =
    useCubeQuery<RawClinicalData>(
      {
        dimensions: [
          CASES.CASE_ID,
          CASES.CUSTOMER_CASE_ID,
          CASES.IS_ADD_ON,
          CASES.CASE_CLASSIFICATION_ID,
          CASES.PATIENT_CLASSIFICATION_ID,
          SERVICE_LINES.NAME,
          CASE_DERIVED_PROPERTIES.IS_IN_FLIP_ROOM,
          CASE_DERIVED_PROPERTIES.PRECEDING_CASE_ID,
          CASES.ACTUAL_START_TIMESTAMP,
          CASES.SCHEDULED_START_TIMESTAMP,
        ],
        filters: [
          {
            member: CASES.CASE_ID,
            operator: 'equals',
            values: caseIds,
          },
        ],
      },
      { skip }
    )

  const { isLoading, resultSet: rawClinicalData } =
    useCubeQuery<RawClinicalData>(
      {
        dimensions: [
          CASES.CASE_ID,
          CASE_STAFF.ROLE,
          STAFF.FIRST_NAME,
          STAFF.LAST_NAME,
        ],
        filters: [
          {
            member: CASES.CASE_ID,
            operator: 'equals',
            values: caseIds,
          },
          {
            member: CASE_STAFF.ROLE,
            operator: 'equals',
            values: [...PRIMARY_SURGEON_ROLES, ...ANESTHESIA_ROLES],
          },
        ],
      },
      { skip }
    )

  const { isLoading: isLoadingProcedures, resultSet: procedureData } =
    useCubeQuery<RawClinicalData>(
      {
        dimensions: [CASES.CASE_ID, PROCEDURES.NAME],
        filters: [
          {
            member: CASES.CASE_ID,
            operator: 'equals',
            values: caseIds,
          },
          {
            member: PROCEDURES.NAME,
            operator: 'set',
          },
        ],
      },
      { skip }
    )

  const concatenatedFollowingCaseData =
    !isLoadingProcedures && !isLoading && !isFollowingCaseLoading
      ? (procedureData?.rawData() ?? [])
          .concat(rawClinicalData?.rawData() ?? [])
          .concat(rawFollowingCaseData?.rawData() ?? [])
      : undefined

  // Generate Case to ClinicalDimensions mapping
  const caseIdToClinicalData = getIdToFollowingCaseData(
    concatenatedFollowingCaseData?.map<RowLevelFollowingCaseData>((edge) => ({
      caseId: edge[CASES.CASE_ID]?.toString() ?? '',
      staffRole: edge[CASE_STAFF.ROLE]?.toString(),
      staffFirstName: edge[STAFF.FIRST_NAME] ?? undefined,
      staffLastName: edge[STAFF.LAST_NAME] ?? undefined,
      procedureName: edge[PROCEDURES.NAME] ?? undefined,
      customerCaseId: edge[CASES.CUSTOMER_CASE_ID]?.toString(),
      serviceLineName: edge[SERVICE_LINES.NAME]?.toString(),
      isAddOn: Boolean(edge[CASES.IS_ADD_ON]),
      isInFlipRoom: Boolean(edge[CASE_DERIVED_PROPERTIES.IS_IN_FLIP_ROOM]),
      precedingCaseId:
        edge[CASE_DERIVED_PROPERTIES.PRECEDING_CASE_ID]?.toString(),
      caseClassificationId: edge[CASES.CASE_CLASSIFICATION_ID]?.toString(),
      patientClassificationId:
        edge[CASES.PATIENT_CLASSIFICATION_ID]?.toString(),
      actualStartTime: edge[CASES.ACTUAL_START_TIMESTAMP]?.toString(),
      scheduledStartTime: edge[CASES.SCHEDULED_START_TIMESTAMP]?.toString(),
    }))
  )

  return {
    isLoading: isLoadingProcedures || isLoading,
    caseIdToClinicalData,
  }
}
