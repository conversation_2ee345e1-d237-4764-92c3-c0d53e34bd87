import { Badge } from '@apella/component-library'
import { FeedbackStatus } from 'src/__generated__/globalTypes'

export const StatusBadge = ({ status }: { status: FeedbackStatus }) => {
  const copy = FeedbackStatus.COMPLETE === status ? 'Complete' : 'Incomplete'
  return (
    <Badge
      {...(status === FeedbackStatus.COMPLETE && { color: 'gray' })}
      variant="secondary"
    >
      {copy}
    </Badge>
  )
}
