import { gql } from '@apollo/client'

import { PAGE_CURSORS_FRAGMENT } from 'src/utils/graphqlFragments'

export const GET_HIGHLIGHTS_COUNT = gql`
  query GetHighlightCount(
    $startTime: DateTime!
    $endTime: DateTime!
    $organizationIds: [ID!]!
    $assignedUserIds: [String!]!
    $feedbackStatus: FeedbackStatus
  ) {
    highlightSearch(
      query: {
        minTime: $startTime
        maxTime: $endTime
        organizationIds: $organizationIds
        assignedUserIds: $assignedUserIds
        feedbackStatus: $feedbackStatus
      }
    ) {
      totalRecords
    }
  }
`

export const GET_HIGHLIGHT_SEARCH_DATA = gql`
  ${PAGE_CURSORS_FRAGMENT}
  query GetHighlightSearchData(
    $startTime: DateTime!
    $endTime: DateTime!
    $orderBy: [OrderBy!]
    $organizationIds: [ID!]!
    $siteIds: [ID!]
    $roomIds: [ID!]
    $categories: [String!]
    $assignedUserIds: [String!]
    $after: String
    $first: Int
    $feedbackStatus: FeedbackStatus
  ) {
    highlightSearch(
      query: {
        minTime: $startTime
        maxTime: $endTime
        organizationIds: $organizationIds
        siteIds: $siteIds
        roomIds: $roomIds
        categories: $categories
        assignedUserIds: $assignedUserIds
        feedbackStatus: $feedbackStatus
      }
      orderBy: $orderBy
      after: $after
      first: $first
    ) {
      pageCursors {
        ...PageCursorsFragment
      }
      edges {
        node {
          id
          description
          startTime
          endTime
          category
          hasVideoAvailable
          myFeedback {
            id
            comment
            rating
          }
          organization {
            id
            name
          }
          site {
            id
            name
          }
          room {
            id
            name
          }
        }
      }
    }
  }
`
