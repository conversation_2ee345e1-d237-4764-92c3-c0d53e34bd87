import { useCallback, useEffect, useMemo, useState } from 'react'

import { useApolloClient, useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { OrderBy } from '@apella/component-library'
import { useQueryParamAndLocalStorageState } from '@apella/hooks'
import { FeedbackStatus, Scalars } from 'src/__generated__/globalTypes'
import { toIds } from 'src/components/Filters/FilterWithCount'
import {
  dateTimeToUrlFriendlyDate,
  orderByToUrlFriendly,
  urlFriendlyDateToJSDate,
  urlFriendlyOrderStrToOrderBy,
} from 'src/components/Filters/urls'
import { useTimezone } from 'src/Contexts'
import {
  GetHighlightSearchData,
  GetHighlightSearchDataVariables,
} from 'src/modules/highlights/queries/__generated__'
import { GET_HIGHLIGHT_SEARCH_DATA } from 'src/modules/highlights/queries/search'
import { GetCurrentUser } from 'src/modules/user/__generated__'
import { useCurrentUser } from 'src/modules/user/hooks'

interface HighlightCategory {
  name: string
  type:
    | 'first_case_on_time_starts'
    | 'pre_operative_gap'
    | 'post_operative_gap'
    | 'terminal_clean'
    | 'timeout'
    | 'turnover'
    | 'uncategorized'
}

export const useHighlightCategories = () => {
  return [
    { type: 'first_case_on_time_starts', name: 'First case on time starts' },
    { type: 'pre_operative_gap', name: 'Pre-operative gap' },
    { type: 'post_operative_gap', name: 'Post-operative gap' },
    { type: 'snippet', name: 'Snippet' },
    { type: 'terminal_clean', name: 'Terminal clean' },
    { type: 'timeout', name: 'Timeout' },
    { type: 'turnover', name: 'Turnover' },
    { type: 'uncategorized', name: 'Uncategorized' },
  ] as HighlightCategory[]
}

const START_TIME = 'startTime'
const END_TIME = 'endTime'
const SITE_IDS = 'siteIds'
const ROOM_IDS = 'roomIds'
const CATEGORIES = 'categories'
const ORDER_BY = 'orderBy'
const FEEDBACK_STATUS = 'feedbackStatus'

export const useHighlightSearch = ({
  DEFAULT_START_TIME,
  DEFAULT_END_TIME,
  SORTABLE_FIELDS,
  DEFAULT_STATE,
  DEFAULT_ORDER_BY_STATE,
}: {
  DEFAULT_START_TIME: DateTime
  DEFAULT_END_TIME: DateTime
  SORTABLE_FIELDS: string[]
  DEFAULT_STATE: Partial<GetHighlightSearchDataVariables>
  DEFAULT_ORDER_BY_STATE: Pick<GetHighlightSearchDataVariables, typeof ORDER_BY>
}) => {
  const { timezone: locale } = useTimezone()

  const [roomIds, setRoomIds] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(ROOM_IDS, undefined)

  const [categories, setCategories] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(CATEGORIES, undefined)

  const [orderBy, setOrderBy] = useQueryParamAndLocalStorageState<
    string | undefined
  >(ORDER_BY, orderByToUrlFriendly(DEFAULT_STATE.orderBy!))

  const [siteIds, setSiteIds] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(SITE_IDS, undefined)

  const [startTime, setStartTime] = useQueryParamAndLocalStorageState<
    Scalars['DateTime']['output']
  >(START_TIME, dateTimeToUrlFriendlyDate(DEFAULT_START_TIME))
  const [endTime, setEndTime] = useQueryParamAndLocalStorageState<
    Scalars['DateTime']['output']
  >(END_TIME, dateTimeToUrlFriendlyDate(DEFAULT_END_TIME))
  const [feedbackStatus, setFeedbackStatus] = useQueryParamAndLocalStorageState<
    FeedbackStatus | undefined
  >(FEEDBACK_STATUS, undefined)

  const stateActionMapper = useMemo(
    () => ({
      startTime: (time: string) =>
        setStartTime(dateTimeToUrlFriendlyDate(DateTime.fromISO(time))),
      endTime: (time: string) =>
        setEndTime(dateTimeToUrlFriendlyDate(DateTime.fromISO(time))),
      siteIds: setSiteIds,
      roomIds: setRoomIds,
      categories: setCategories,
      feedbackStatus: setFeedbackStatus,
      orderBy: (val: OrderBy[]) => setOrderBy(orderByToUrlFriendly(val)),
      first: undefined,
      after: undefined,
    }),
    [
      setStartTime,
      setEndTime,
      setSiteIds,
      setRoomIds,
      setCategories,
      setOrderBy,
      setFeedbackStatus,
    ]
  )

  const [filterData, setFilterData] = useState<
    | NonNullable<
        GetCurrentUser['me']
      >['organizations']['edges'][number]['node']
    | undefined
  >(undefined)

  const [state, setState] = useState<GetHighlightSearchDataVariables>({
    ...DEFAULT_STATE,
    startTime:
      urlFriendlyDateToJSDate(startTime, locale) || DEFAULT_START_TIME.toISO(),
    endTime:
      urlFriendlyDateToJSDate(endTime, locale) || DEFAULT_END_TIME.toISO(),
    assignedUserIds: [],
    organizationIds: filterData?.id ? [filterData.id] : [],
    roomIds: roomIds,
    siteIds: siteIds,
    feedbackStatus: feedbackStatus,
    categories: categories,
    orderBy:
      urlFriendlyOrderStrToOrderBy(orderBy, SORTABLE_FIELDS) ||
      DEFAULT_ORDER_BY_STATE.orderBy,
  })

  const highlightCategories = useHighlightCategories()

  const { loading: highlightsLoading, data: highlightsData } = useQuery<
    GetHighlightSearchData,
    GetHighlightSearchDataVariables
  >(GET_HIGHLIGHT_SEARCH_DATA, {
    variables: state,
    fetchPolicy: 'no-cache',
  })

  const client = useApolloClient()

  const fetchExportRows = useCallback(async () => {
    const { data } = await client.query<GetHighlightSearchData>({
      query: GET_HIGHLIGHT_SEARCH_DATA,
      variables: { ...state, first: undefined, after: undefined },
      fetchPolicy: 'no-cache',
    })

    return data.highlightSearch?.edges.map(({ node }) => node)
  }, [client, state])

  const {
    loading: filtersLoading,
    currentOrganization: activeOrganization,
    userId,
  } = useCurrentUser()

  useEffect(() => {
    if (!filtersLoading && activeOrganization) {
      const o = activeOrganization.node
      const filteredData = activeOrganization
        ? {
            ...o,
            sites: {
              ...o.sites,
              edges: o.sites.edges.map((site) => ({
                ...site,
                node: {
                  ...site.node,
                  rooms: {
                    ...site.node.rooms,
                    edges: site.node.rooms.edges.filter(
                      () => !siteIds || siteIds.includes(site.node.id)
                    ),
                  },
                },
              })),
            },
          }
        : undefined

      setFilterData(filteredData)
      setState((prev) => ({
        ...prev,
        organizationIds: filteredData?.id ? [filteredData.id] : [],
        assignedUserIds: userId ? [userId] : [],
      }))
    }
  }, [filtersLoading, activeOrganization, siteIds, userId])

  // This goes through all the state changes:
  // - Update LocalStorage and QueryParams
  // - Update the state
  const updateTableFilterSortState = useCallback(
    (stateDiff: Partial<GetHighlightSearchDataVariables>) => {
      setState((prevState: GetHighlightSearchDataVariables) => {
        const newState = {
          ...prevState,
          ...stateDiff,
        }

        for (const key of Object.keys(stateActionMapper)) {
          const keyOf = key as keyof typeof stateActionMapper
          const setter = stateActionMapper[keyOf]
          const newVal = newState[keyOf]

          setter && setter(newVal as any)
        }

        return newState
      })
    },
    [stateActionMapper, setState]
  )

  const onCategoryChange = useCallback(
    (categories?: string | string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          categories: toIds(categories),
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )

  const onSiteIdsChange = useCallback(
    (siteIds?: string | string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          siteIds: toIds(siteIds),
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )
  const onRoomIdsChange = useCallback(
    (roomIds?: string | string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          roomIds: toIds(roomIds),
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )

  const onSortChange = useCallback(
    (orderBy?: OrderBy[]) => {
      updateTableFilterSortState({
        orderBy,
      })
    },
    [updateTableFilterSortState]
  )

  const onFeedbackStatusChange = useCallback(
    (feedbackStatus?: FeedbackStatus) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          feedbackStatus: feedbackStatus,
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )

  const onDateChange = useCallback(
    (dates: Date[]) => {
      if (!filtersLoading) {
        const newstartTime = DateTime.fromJSDate(dates[0]).setZone(locale)
        const newendTime = DateTime.fromJSDate(dates[1]).setZone(locale)

        updateTableFilterSortState({
          startTime: newstartTime.toISO(),
          endTime: newendTime.toISO(),
        })
      }
    },
    [filtersLoading, locale, updateTableFilterSortState]
  )

  const resetFilters = useCallback(() => {
    updateTableFilterSortState({
      ...DEFAULT_STATE,
      startTime: DEFAULT_START_TIME.toISO(),
      endTime: DEFAULT_END_TIME.toISO(),
      organizationIds: filterData?.id ? [filterData.id] : [],
      assignedUserIds: userId ? [userId] : [],
    })
  }, [
    DEFAULT_END_TIME,
    DEFAULT_STATE,
    DEFAULT_START_TIME,
    updateTableFilterSortState,
    userId,
    filterData?.id,
  ])

  const rowKeySelector = useCallback(
    (
      rowData: GetHighlightSearchData['highlightSearch']['edges'][number]['node']
    ) => rowData.id,
    []
  )

  const highlightSearch = highlightsData?.highlightSearch ?? undefined

  const tableData = useMemo(
    () => (highlightSearch?.edges ?? []).map((highlight) => highlight.node),
    [highlightSearch?.edges]
  )

  const selectedDateTime: [Date, Date] = useMemo(
    () => [new Date(state.startTime), new Date(state.endTime)],
    [state.startTime, state.endTime]
  )

  const pageCursors = highlightSearch?.pageCursors

  return {
    selectedDateTime,
    pageCursors,
    tableData,
    rowKeySelector,
    resetFilters,
    filterData,
    highlightCategories,
    highlightsLoading,
    onCategoryChange,
    onSiteIdsChange,
    onRoomIdsChange,
    onSortChange,
    onDateChange,
    state,
    filtersLoading,
    updateTableFilterSortState,
    onFeedbackStatusChange,
    fetchExportRows,
  }
}
