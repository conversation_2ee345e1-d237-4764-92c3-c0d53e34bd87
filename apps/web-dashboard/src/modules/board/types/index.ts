import { DateTime, Duration } from 'luxon'

import {
  BoardViewType,
  CaseType,
  RoomStatusName,
} from 'src/__generated__/globalTypes'
import { CaseFlagType } from 'src/modules/planning/types'
import { CaseStatus, Staff } from 'src/pages/types'

export interface BoardConfig {
  blurVideo: boolean
  boardViewType: BoardViewType
  enableVideo: boolean
  id: string
  lastUpdated?: DateTime
  lastUpdatedBy?: string
  name: string
  pageDurationSeconds: number
  roomIds?: string[]
  roomsPerPage: number
  showClosedRooms: boolean
  siteId: string
  zoom: number
}

export interface BigBoardSite {
  id: string
  name: string
  rooms: BigBoardRoom[]
}

export interface BigBoardRoomStatus {
  inProgressApellaCase?: {
    status: CaseStatus
  }
  name: RoomStatusName
  since?: DateTime
}

export interface BigBoardRoom {
  cases: BigBoardCase[]
  id: string
  name: string
  site: Pick<BigBoardSite, 'id' | 'name'>
  status: BigBoardRoomStatus
}
export interface CaseFlag {
  flagType: CaseFlagType
  id: string
}
export interface BigBoardCase {
  case?: {
    isAddOn: boolean
    id: string
    scheduledStartTime: DateTime
    scheduledEndTime: DateTime
    staff: Staff[]
    staffPlan: Staff[]
    procedures: { name: string; anesthesiaType?: string }[]
    caseFlags?: CaseFlag[]
    notePlan?: {
      id: string
      note: string
    }
    patient?: {
      id: string
      age?: number
      abbreviatedName: string
      administrativeSex: string
    }
  }
  endTime?: DateTime
  endTimeOffset?: Duration
  id: string
  startTime: DateTime
  startTimeOffset?: Duration
  status: CaseStatus
  type: CaseType
}
