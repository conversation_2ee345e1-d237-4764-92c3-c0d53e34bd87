import { Duration } from 'luxon'

// Sizing
export const RoomGap = 8
export const CaseSizeConstant = {
  width: 215,
  duration: Duration.fromObject({ minutes: 60 }),
}
export const RoomNameMarginOffset = 44
export const RoomNameAndCaseGap = 16
export const CurrentHourPadding = 2
export const VideoBorderRadius = 4
export const OverflowCaseWidth = 83

// Pagination
export const PageSize = 10
export const PageTimerSeconds = 30

export const BoardContainerId = 'board-container'
export const DelayOffsetDurationMinimumMinutes = 1
export const EarlyOffsetDurationMinimumMinutes = 10

// We're basing a lot of our designs on a 4k resolution, so hardcoding this resolution here in order
// to dynamically generate ratios. Primarily, we're using this as a way to generate ratios for different
// height resolutions so that we can support fitting our app into the vh and the heights in our app scale
// proportionate to the screen resolution
export const screenSizes = {
  fourK: {
    height: 2160,
    width: 3840,
  },
}
