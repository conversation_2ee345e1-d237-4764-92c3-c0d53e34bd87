import LoadingOverlay from 'src/components/LoadingOverlay'
import { RoomGap } from 'src/modules/board/constants'
import { useBoardContext } from 'src/modules/board/hooks/BoardContextProvider'
import { BigBoardRoom } from 'src/modules/board/types'
import { useTimelineHourBounds } from 'src/pages/Schedule/useTimelineHourBounds'

import { pxToVh } from '../pxToVh'
import { HourMarkers } from './HourMarkers'
import { TimelineRoom } from './TimelineRoom'

export const Timeline = ({
  rooms,
  minTime,
  maxTime,
  isLoading,
}: {
  rooms: BigBoardRoom[]
  minTime: string
  maxTime: string
  isLoading: boolean
}) => {
  const {
    boardConstants: {
      caseSize: { hourMarkerContainerSize },
    },
  } = useBoardContext()

  const timelineDateTimeBounds = useTimelineHourBounds({
    minTime: minTime,
    maxTime: maxTime,
    cases: rooms.flatMap((r) => r.cases),
    calculationMethod: 'sliding',
  })

  return (
    <div
      css={{
        height: '100%',
        position: 'relative',
      }}
    >
      <div
        css={{
          display: 'grid',
          rowGap: pxToVh({ pixels: RoomGap }),
          paddingTop: pxToVh({ pixels: hourMarkerContainerSize }),
        }}
      >
        {isLoading && <LoadingOverlay />}
        <HourMarkers
          timelineDateTimeBounds={timelineDateTimeBounds}
          hideMarkers={isLoading}
        />
        {rooms.map((room) => (
          <TimelineRoom
            key={room.id}
            room={room}
            timelineDateTimeBounds={timelineDateTimeBounds}
          />
        ))}
      </div>
    </div>
  )
}
