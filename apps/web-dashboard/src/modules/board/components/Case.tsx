import { Fragment } from 'react'

import { CSSObject, useTheme } from '@emotion/react'

import { DateTime, Duration } from 'luxon'

import {
  ApellaDateTimeFormats,
  EllidedText,
  fontWeights,
  formatDuration,
} from '@apella/component-library'
import { CaseStatusName, CaseType } from 'src/__generated__/globalTypes'
import {
  CaseSizeConstant,
  DelayOffsetDurationMinimumMinutes,
  EarlyOffsetDurationMinimumMinutes,
  VideoBorderRadius,
} from 'src/modules/board/constants'
import { calculateOffsetLeft } from 'src/modules/board/helpers'
import { useBoardContext } from 'src/modules/board/hooks/BoardContextProvider'
import { useRoomHeight } from 'src/modules/board/hooks/useRoomHeight'
import { elevations, pxToVw, useFontSizes } from 'src/modules/board/styles'
import { BigBoardCase } from 'src/modules/board/types'
import { AddOnChecker } from 'src/pages/AddOnChecker'
import { TimelineDateTimeBounds } from 'src/pages/Schedule/types'
import { useStatusStyles } from 'src/utils/status'

import { Staff } from '../../../pages/types'
import {
  getPrimarySurgeonText,
  getStaffMembersByRole,
  toRoleShortForm,
} from '../../../utils/roles'
import { useCaseFlagIconMap } from '../../planning/hooks/useCaseFlagIconMap'
import { useResponsiveSizing } from '../useResponsiveSizing'

const calculateWidth = ({
  apellaCase,
  timelineDateTimeBounds,
}: {
  apellaCase: BigBoardCase
  timelineDateTimeBounds: TimelineDateTimeBounds
}) => {
  const { startTime, endTime } = apellaCase
  const numberOfMillisVisible =
    timelineDateTimeBounds.maxDateTime.toMillis() -
    timelineDateTimeBounds.minDateTime.toMillis()
  if (!numberOfMillisVisible) {
    return 0
  }
  const widthMillis =
    Math.min(
      timelineDateTimeBounds.maxDateTime.toMillis(),
      (endTime ?? DateTime.now()).toMillis()
    ) -
    Math.max(
      startTime.toMillis(),
      timelineDateTimeBounds.minDateTime.toMillis()
    )

  if (widthMillis <= 0) {
    return 0
  }
  return (
    (widthMillis / CaseSizeConstant.duration.toMillis()) *
    CaseSizeConstant.width
  )
}

export const OffsetBadge = ({
  offset,
  delayOffsetDurationMinimumMinutes = DelayOffsetDurationMinimumMinutes,
  earlyOffsetDurationMinimumMinutes = EarlyOffsetDurationMinimumMinutes,
  styles,
}: {
  offset: Duration
  delayOffsetDurationMinimumMinutes?: number
  earlyOffsetDurationMinimumMinutes?: number
  styles?: CSSObject
}) => {
  const theme = useTheme()
  const fontSizes = useFontSizes()

  const minsOffset = offset.as('minutes')
  const offsetDurationAbs = Math.abs(minsOffset)
  const isDelayed = minsOffset > 0

  if (
    (isDelayed && offsetDurationAbs < delayOffsetDurationMinimumMinutes) ||
    (!isDelayed && offsetDurationAbs < earlyOffsetDurationMinimumMinutes)
  ) {
    return null
  }

  const { text, ...additionalStyles } = isDelayed
    ? {
        color: theme.palette.text.alternate,
        background: theme.palette.red[50],
        text: 'Delayed',
      }
    : {
        color: theme.palette.text.alternate,
        background: theme.palette.green[50],
        text: 'Early',
      }

  return (
    <span
      css={{
        ...styles,
        ...fontWeights.semibold,
        borderRadius: styles?.borderRadius || 10,
        padding: styles?.padding || `0 ${pxToVw({ pixels: 5 })}`,
        display: 'grid',
        textTransform: 'capitalize',
        fontSize: styles?.fontSize || fontSizes.t1125.fontSize,
        lineHeight: styles?.lineHeight || fontSizes.t1125.lineHeight,
        ...additionalStyles,
      }}
    >
      {formatDuration(
        Duration.fromObject({
          minutes: isDelayed
            ? Math.floor(offsetDurationAbs)
            : offsetDurationAbs,
        })
      )}{' '}
      {text}
    </span>
  )
}

const StaffCell = ({ staff, role }: { staff: Staff[]; role: string }) => {
  const theme = useTheme()
  const fontSizes = useFontSizes()

  const roleAcronym = toRoleShortForm(role)

  return (
    <div
      css={{
        display: 'grid',
        gridTemplateRows: 'repeat(2, minmax(0, 1fr))',
      }}
    >
      <EllidedText
        css={{
          ...fontSizes.t1250,
          ...fontWeights.semibold,
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          overflow: 'hidden',
          textTransform: 'uppercase',
          color: theme.palette.text.secondary,
        }}
      >
        {roleAcronym}
      </EllidedText>
      {!!staff.length && (
        <EllidedText
          css={{
            ...fontSizes.t1750,
            ...fontWeights.semibold,
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            textTransform: 'capitalize',
          }}
        >
          {staff
            .map((s) => `${s.lastName}, ${s.firstName}`.toLocaleLowerCase())
            .join('; ')}
        </EllidedText>
      )}
    </div>
  )
}

export const BoardCase = ({
  apellaCase,
  type,
  timelineDateTimeBounds,
}: {
  apellaCase: BigBoardCase
  type: 'active' | 'upcoming'
  timelineDateTimeBounds?: TimelineDateTimeBounds
}) => {
  const roomHeight = useRoomHeight()
  const theme = useTheme()
  const fontSizes = useFontSizes()
  const caseFlagIconMap = useCaseFlagIconMap(fontSizes.t2250.fontSize)

  const caseStatus = apellaCase.status?.name ?? CaseStatusName.SCHEDULED
  const caseColors = useStatusStyles()
  const {
    boardConstants: { caseSize: tileCaseSize },
  } = useBoardContext()

  const { vw, vh } = useResponsiveSizing()

  const isActive = type === 'active'

  const staffMembersByRole = getStaffMembersByRole(apellaCase.case)
  const procedureName = apellaCase.case?.procedures?.map((p) => p.name)
  return (
    <div
      css={{
        height: roomHeight,
        overflow: 'hidden',
        border: `${caseColors(caseStatus)?.border ?? 'solid black'}`,
        borderRadius: isActive
          ? `0 ${VideoBorderRadius}px ${VideoBorderRadius}px 0`
          : VideoBorderRadius,
        borderWidth: isActive ? `1px 1px 1px 0` : 1,
        backgroundColor:
          caseColors(caseStatus)?.backgroundColor ?? theme.palette.gray[30],
        padding: `${vh({ pixels: 5 })} ${vw({ pixels: 10 })}`,
        display: 'grid',
        rowGap: 0,
        alignContent: 'start',
        boxShadow: !isActive ? elevations.r100 : undefined,
        ...(timelineDateTimeBounds
          ? {
              position: 'absolute',
              width: `${calculateWidth({
                apellaCase,
                timelineDateTimeBounds,
              })}px`,
              left: `${calculateOffsetLeft({
                startTime: apellaCase.startTime,
                timelineDateTimeBounds,
              })}px`,
            }
          : { width: pxToVw({ pixels: tileCaseSize.width }) }),
      }}
      key={apellaCase.id}
      data-testid={`board-case-${apellaCase.id}`}
    >
      <EllidedText
        css={{
          display: 'grid',
          justifyContent: 'space-between',
          gridAutoFlow: 'column',
        }}
      >
        <div
          css={{
            display: 'grid',
            gridAutoFlow: 'column',
            gridAutoColumns: 'min-content',
            columnGap: pxToVw({ pixels: 16 }),
            alignItems: 'baseline',
          }}
        >
          <EllidedText
            css={{
              ...fontSizes.t2000,
            }}
          >
            {apellaCase.startTime.toLocaleString(ApellaDateTimeFormats.TIME)} -{' '}
            {apellaCase.endTime?.toLocaleString(ApellaDateTimeFormats.TIME) ??
              ''}
          </EllidedText>
          {!!apellaCase.case && (
            <EllidedText
              css={{
                ...fontSizes.t1750,
                color: theme.palette.text.secondary,
              }}
              as="p"
            >
              <span
                css={{
                  ...fontSizes.t1250,
                }}
              >
                SCH{' '}
              </span>
              {apellaCase.case?.scheduledStartTime.toLocaleString(
                ApellaDateTimeFormats.TIME
              )}{' '}
              -{' '}
              {apellaCase.case?.scheduledEndTime?.toLocaleString(
                ApellaDateTimeFormats.TIME
              ) ?? ''}
            </EllidedText>
          )}
          {apellaCase.type === CaseType.LIVE && !!apellaCase.endTimeOffset ? (
            <OffsetBadge
              offset={apellaCase.endTimeOffset}
              styles={{
                ...fontSizes.t1500,
                padding: `0 ${pxToVw({ pixels: 10 })}`,
                borderRadius: 20,
              }}
            />
          ) : apellaCase.type === CaseType.FORECAST &&
            !!apellaCase.startTimeOffset ? (
            <OffsetBadge
              offset={apellaCase.startTimeOffset}
              styles={{
                ...fontSizes.t1500,
                padding: `0 ${pxToVw({ pixels: 10 })}`,
                borderRadius: 20,
              }}
            />
          ) : null}
        </div>
        {!!apellaCase.case?.caseFlags?.length && (
          <div
            css={{
              display: 'grid',
              gridAutoFlow: 'column',
              gap: pxToVw({ pixels: 8 }),
              gridAutoColumns: 'min-content',
            }}
          >
            {apellaCase.case?.caseFlags.map((flag) => (
              <Fragment key={flag.id}>
                {caseFlagIconMap[flag.flagType]}
              </Fragment>
            ))}
          </div>
        )}
      </EllidedText>

      {!!apellaCase.case && (
        <EllidedText
          css={{
            ...fontSizes.t3000,
            ...fontWeights.semibold,
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            textTransform: 'capitalize',
          }}
          as="p"
        >
          {getPrimarySurgeonText(apellaCase.case).toLocaleLowerCase()}
        </EllidedText>
      )}
      <div
        css={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 25%)',
          columnGap: pxToVw({ pixels: 4 }),
        }}
      >
        {staffMembersByRole.map((s) => (
          <StaffCell key={s.role} role={s.role} staff={s.staff} />
        ))}
      </div>
      {!!apellaCase.case?.procedures &&
        !!apellaCase.case?.procedures.length && (
          <p
            css={{
              ...fontSizes.t2000,
              lineClamp: 2,
              textTransform: 'capitalize',
              boxOrient: 'vertical',
              display: '-webkit-box',
              overflow: 'hidden',
              maxWidth: '100%',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: 2,
            }}
          >
            {AddOnChecker(procedureName ?? [], apellaCase.case.isAddOn, false)}
          </p>
        )}
      {!!apellaCase.case?.patient && (
        <p css={{ ...fontSizes.t1500 }}>
          <span css={{ ...fontWeights.semibold }}>
            {apellaCase.case.patient.abbreviatedName}
          </span>{' '}
          - {apellaCase.case.patient.age} -{' '}
          {apellaCase.case.patient.administrativeSex.toUpperCase()}
        </p>
      )}
      {!!apellaCase.case?.notePlan?.note && (
        <div
          css={{
            ...fontSizes.t2000,
            lineClamp: 2,
            boxOrient: 'vertical',
            display: '-webkit-box',
            overflow: 'hidden',
            maxWidth: '100%',
            WebkitBoxOrient: 'vertical',
            WebkitLineClamp: 2,
          }}
        >
          <p css={{ ...fontWeights.semibold }}>Notes</p>
          <p>{apellaCase.case?.notePlan?.note}</p>
        </div>
      )}
    </div>
  )
}
