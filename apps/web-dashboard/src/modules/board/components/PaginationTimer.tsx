import { useEffect, useRef } from 'react'

import { keyframes, useTheme } from '@emotion/react'

import { useBoardContext } from 'src/modules/board/hooks/BoardContextProvider'

import { PageTimerSeconds } from '../constants'
import { pxToVh } from '../pxToVh'

export const PaginationTimer = ({
  goToNextPage,
  numSeconds = PageTimerSeconds,
  currentPage,
}: {
  goToNextPage: () => void
  numSeconds?: number
  currentPage: number
}) => {
  const clockAnimation = keyframes`
    0% { clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0); }
    25% { clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0); }
    50% { clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%); }
    75% { clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%); }
    100% { clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0); }
  `
  const theme = useTheme()
  const {
    boardConstants: { footerHeight },
  } = useBoardContext()

  const totalSize = pxToVh({ pixels: footerHeight * 0.5 })
  const outerCircleSize = pxToVh({ pixels: footerHeight * 0.05 })

  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const intervalId = setInterval(() => {
      goToNextPage()
    }, numSeconds * 1000)

    const animatedCircleRef = ref.current

    // This restarts the animation when the current page changes,
    // which is necessary to support when manually changing pages
    // so that pages don't change at seemingly random intervals
    if (animatedCircleRef) {
      animatedCircleRef.style.animationName = 'none'
      requestAnimationFrame(() => {
        animatedCircleRef.style.animationName = ''
      })
    }

    return () => {
      clearInterval(intervalId)
    }
  }, [numSeconds, goToNextPage, currentPage])

  return (
    <div
      css={{
        display: 'inline-flex',
        width: totalSize,
        height: totalSize,
        borderRadius: '100%',
        border: `${outerCircleSize} solid ${theme.palette.gray[50]}`,
        padding: outerCircleSize,
      }}
    >
      <div
        ref={ref}
        css={{
          width: '100%',
          height: '100%',
          borderRadius: '50%',
          animation: `${clockAnimation} ${numSeconds}s infinite linear`,
          backgroundColor: theme.palette.gray[50],
          transform: 'rotate(45deg)',
          transformOrigin: 'center',
        }}
      />
    </div>
  )
}
