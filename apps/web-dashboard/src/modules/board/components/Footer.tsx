import { ReactNode } from 'react'

import { useTheme } from '@emotion/react'

import { FlexContainer, fontWeights, ZIndex } from '@apella/component-library'
import { CaseStatusName } from 'src/__generated__/globalTypes'
import { buildEnhancedKeyCategories, StatusPill } from 'src/components/Keys'
import { RoomNameMarginOffset } from 'src/modules/board/constants'
import {
  RoomNameSize,
  useBoardContext,
} from 'src/modules/board/hooks/BoardContextProvider'
import { useBoardZoom } from 'src/modules/board/hooks/useBoardZoom'
import { pxToVw, useFontSizes } from 'src/modules/board/styles'
import { useCaseFlagIconMap } from 'src/modules/planning/hooks/useCaseFlagIconMap'
import { CASE_FLAGS_LABELS, CaseFlagOrdering } from 'src/modules/planning/types'
import { useEventTypes } from 'src/utils/hooks'

import {
  formatStatus,
  StatusSuperset,
  useStatusStyles,
} from '../../../utils/status'
import { pxToVh } from '../pxToVh'
import { PaginationTimer } from './PaginationTimer'

export const Footer = ({
  currentPageText,
  goToNextPage,
  pageDuration,
  currentPage,
}: {
  currentPageText?: ReactNode
  goToNextPage: () => void
  pageDuration: number
  currentPage: number
}) => {
  const {
    boardConstants: { footerHeight },
  } = useBoardContext()

  const { zoom } = useBoardZoom({
    zoomMultiplier: 1,
  })

  const fontSizes = useFontSizes({ zoom })
  const caseColors = useStatusStyles({ isKey: true })
  const theme = useTheme()
  const caseFlagIconMap = useCaseFlagIconMap(fontSizes.t2250.fontSize)

  const { eventTypes } = useEventTypes()
  const enhancedSections = buildEnhancedKeyCategories(eventTypes)

  return (
    <footer
      css={{
        height: `${pxToVh({ pixels: footerHeight })}`,
        width: '100%',
        display: 'grid',
        alignItems: 'center',
        padding: `${pxToVh({ pixels: 10 })} ${pxToVw({ pixels: 44 })}`,
        position: 'relative',
        background: theme.palette.background.primary,
        borderTop: `solid 1px ${theme.palette.gray[30]}`,
        zIndex: ZIndex.ABOVE,
      }}
    >
      {!!currentPageText && (
        <div
          css={{
            display: 'grid',
            gridAutoFlow: 'column',
            margin: `0 0 0 ${pxToVw({ pixels: RoomNameMarginOffset })}`,
            gridTemplateColumns: `${pxToVw({
              pixels: RoomNameSize * Math.max(1, zoom),
            })} auto`,
            alignItems: 'center',
            gridColumn: 1,
            gridRow: 1,
            justifySelf: 'start',
          }}
        >
          <>
            <PaginationTimer
              goToNextPage={goToNextPage}
              numSeconds={pageDuration}
              currentPage={currentPage}
            />
            <FlexContainer alignItems={'center'} gap={pxToVw({ pixels: 16 })}>
              {currentPageText}
            </FlexContainer>
          </>
        </div>
      )}
      <div
        css={{
          display: 'grid',
          gridAutoFlow: 'column',
          columnGap: pxToVw({ pixels: 64 }),
          alignItems: 'center',
          gridAutoColumns: 'max-content',
          gridColumn: 1,
          gridRow: 1,
          justifySelf: 'center',
        }}
      >
        <div
          css={{
            display: 'grid',
            gridAutoFlow: 'column',
            columnGap: pxToVw({ pixels: 48 }),
            alignItems: 'center',
            gridAutoColumns: 'max-content',
          }}
        >
          <p css={{ ...fontSizes.t1500, textTransform: 'uppercase' }}>Key</p>
          <div
            css={{
              display: 'grid',
              gridAutoFlow: 'column',
              columnGap: pxToVw({ pixels: 16 }),
              alignItems: 'center',
              gridAutoColumns: 'max-content',
            }}
          >
            {enhancedSections
              .filter((section) => section.title !== 'TURNOVER')
              .flatMap((section) => section.items)
              .map((s) => s.colorTheme || (s.label as StatusSuperset))
              .filter(
                (s) =>
                  s !== CaseStatusName.ACTUAL && s !== CaseStatusName.COMPLETE
              )
              .map((s) => (
                <StatusPill
                  borderRadius={pxToVh({ pixels: 4 })}
                  border={`${caseColors(s)?.border ?? 'solid 1px black'}`}
                  key={s}
                  backgroundColor={
                    caseColors(s)?.backgroundColor ?? theme.palette.gray[30]
                  }
                  css={{
                    ...fontSizes.t1500,
                    ...fontWeights.semibold,
                    padding: `${pxToVh({ pixels: 3 })} ${pxToVw({
                      pixels: 10,
                    })}`,
                    textTransform: 'capitalize',
                  }}
                >
                  {formatStatus(s)}
                </StatusPill>
              ))}
          </div>
        </div>

        <div
          css={{
            display: 'grid',
            gridAutoFlow: 'column',
            columnGap: pxToVw({ pixels: 48 }),
            alignItems: 'center',
            gridAutoColumns: 'max-content',
          }}
        >
          <p css={{ ...fontSizes.t1500, textTransform: 'uppercase' }}>
            Patient alerts
          </p>
          <div
            css={{
              display: 'grid',
              gridAutoFlow: 'column',
              columnGap: pxToVw({ pixels: 16 }),
              alignItems: 'center',
              gridAutoColumns: 'max-content',
            }}
          >
            {CaseFlagOrdering.map((cfType) => {
              return (
                <p
                  key={cfType}
                  css={{
                    ...fontSizes.t1500,
                    display: 'grid',
                    alignItems: 'center',
                    gridAutoFlow: 'column',
                    columnGap: pxToVw({ pixels: 8 }),
                  }}
                >
                  {caseFlagIconMap[cfType]}
                  {CASE_FLAGS_LABELS[cfType]}
                </p>
              )
            })}
          </div>
        </div>
      </div>
    </footer>
  )
}
