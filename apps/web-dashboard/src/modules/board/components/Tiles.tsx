import { ZIndex } from '@apella/component-library'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { TileRoom } from 'src/modules/board/components/TileRoom'
import { BoardContainerId, RoomGap } from 'src/modules/board/constants'
import { useMainRect } from 'src/modules/board/hooks/useMainRect'
import { pxToVw } from 'src/modules/board/styles'
import { BigBoardRoom } from 'src/modules/board/types'

import { pxToVh } from '../pxToVh'

const FadeGradient = () => {
  const mainRect = useMainRect()

  if (!mainRect?.width) {
    return null
  }

  return (
    <div
      css={{
        position: 'absolute',
        width: pxToVw({ pixels: 328 }),
        left: `calc(${mainRect.width}px - ${pxToVw({ pixels: 328 })})`,
        height: `100%`,
        background:
          'linear-gradient(to left, white 17.32%, transparent 97.46%)',
        zIndex: ZIndex.ABOVE,
      }}
    ></div>
  )
}

export const Tiles = ({
  rooms,
  isLoading,
}: {
  rooms: BigBoardRoom[]
  isLoading: boolean
}) => {
  return (
    <div
      css={{
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
      id={BoardContainerId}
    >
      <FadeGradient />
      <div
        css={{
          display: 'grid',
          rowGap: pxToVh({ pixels: RoomGap }),
        }}
      >
        {isLoading && <LoadingOverlay />}
        {rooms.map((room) => (
          <TileRoom key={room.id} room={room} />
        ))}
      </div>
    </div>
  )
}
