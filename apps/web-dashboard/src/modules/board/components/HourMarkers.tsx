import { ReactNode, useMemo, useRef, useState } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import { ZIndex, ApellaDateTimeFormats } from '@apella/component-library'
import { useSize } from '@apella/hooks'
import { useTimezone } from 'src/Contexts'
import {
  CaseSizeConstant,
  CurrentHourPadding,
  RoomNameAndCaseGap,
  RoomNameMarginOffset,
} from 'src/modules/board/constants'
import { calculateOffsetLeft } from 'src/modules/board/helpers'
import { useBoardContext } from 'src/modules/board/hooks/BoardContextProvider'
import { useRoomHeight } from 'src/modules/board/hooks/useRoomHeight'
import { pxToVw, useFontSizes } from 'src/modules/board/styles'
import { TimelineDateTimeBounds } from 'src/pages/Schedule/types'
import { calculateTimeMarkersPerTick } from 'src/utils/timelineHelpers'
import { useCurrentMinute } from 'src/utils/useCurrentTime'

import { pxToVh } from '../pxToVh'

const MarkerContainer = ({
  left,
  children,
}: {
  left: string | number
  children: ReactNode
}) => {
  const theme = useTheme()
  const [hourMarkerRef, setHourMarkerRef] = useState<HTMLDivElement | null>(
    null
  )
  const hourMarkerSize = useSize(hourMarkerRef)
  const fontSizes = useFontSizes()
  return (
    <div
      style={{
        position: 'absolute',
        left: left,
      }}
    >
      <div
        style={{
          ...fontSizes.t1125,
          position: 'relative',
          color: theme.palette.text.secondary,
          left: hourMarkerSize && -hourMarkerSize?.width / 2,
        }}
        ref={setHourMarkerRef}
      >
        {children}
      </div>
    </div>
  )
}

const CurrentMinuteMarker = ({
  timelineDateTimeBounds,
}: {
  timelineDateTimeBounds: TimelineDateTimeBounds
  timelineSize: number
}) => {
  const {
    boardConstants: { headerHeight },
  } = useBoardContext()
  const { timezone } = useTimezone()
  const theme = useTheme()

  const currentMinute = useCurrentMinute().setZone(timezone)

  const currentMinuteVisible =
    currentMinute >= timelineDateTimeBounds.minDateTime &&
    currentMinute <= timelineDateTimeBounds.maxDateTime

  return !currentMinuteVisible ? null : (
    <MarkerContainer
      left={`${calculateOffsetLeft({
        startTime: currentMinute,
        timelineDateTimeBounds,
      })}px`}
    >
      <div
        css={{
          height: `calc(100vh - ${pxToVh({ pixels: headerHeight })})`,
          display: 'grid',
          top: `-${CurrentHourPadding}px`,
          position: 'relative',
          zIndex: ZIndex.ABOVE,
        }}
      >
        <span
          css={{
            background: theme.palette.blue[50],
            padding: `${CurrentHourPadding}px 1rem`,
            borderRadius: 20,
            color: theme.palette.text.alternate,
          }}
        >
          {currentMinute.toLocaleString(ApellaDateTimeFormats.TIME)}
        </span>
        <div
          css={{
            background: theme.palette.blue[40],
            height: `calc(100vh - ${pxToVh({ pixels: headerHeight })})`,
            width: 2,
            margin: 'auto',
          }}
        />
      </div>
    </MarkerContainer>
  )
}

const TimeMarker = ({ left, time }: { time: DateTime; left: string }) => {
  const theme = useTheme()
  const { timezone } = useTimezone()
  const {
    boardConstants: { headerHeight },
  } = useBoardContext()
  return (
    <MarkerContainer left={left}>
      <div
        css={{
          height: `calc(100vh - ${pxToVh({
            pixels: headerHeight,
          })})`,
          display: 'grid',
          rowGap: `calc(${pxToVh({ pixels: 20 })} - ${
            CurrentHourPadding * 2
          }px)`,
          color: theme.palette.gray[50],
        }}
      >
        <span>
          {time.setZone(timezone).toLocaleString(ApellaDateTimeFormats.TIME)}
        </span>
        <div
          css={{
            background: theme.palette.gray[30],
            height: `calc(100vh - ${pxToVh({
              pixels: headerHeight,
            })})`,
            width: 1,
            margin: 'auto',
          }}
        />
      </div>
    </MarkerContainer>
  )
}

export const HourMarkers = ({
  timelineDateTimeBounds,
  hideMarkers = true,
}: {
  timelineDateTimeBounds: TimelineDateTimeBounds
  hideMarkers: boolean
}) => {
  const hourHeader = useRef<HTMLDivElement>(null)

  const timelineSize = useSize(hourHeader.current)

  const height = useRoomHeight()

  const {
    config: { enableVideo },
    boardConstants: {
      roomNameSize,
      videoRatio,
      caseSize: { roomStatusSize, hourMarkerContainerSize },
    },
  } = useBoardContext()

  const markers = useMemo(() => {
    const markerList = calculateTimeMarkersPerTick(
      timelineDateTimeBounds.minDateTime,
      timelineDateTimeBounds.maxDateTime,
      CaseSizeConstant.duration
    )

    return markerList.map(({ time }, ix) => (
      <TimeMarker
        key={`time-marker-${time.toMillis()}`}
        time={time}
        left={`${ix * CaseSizeConstant.width}px`}
      />
    ))
  }, [timelineDateTimeBounds.maxDateTime, timelineDateTimeBounds.minDateTime])

  return (
    <div
      css={{
        position: 'absolute',
        top: 0,
        bottom: 0,
        right: 0,
        width: '100%',
      }}
    >
      <div
        css={{
          display: 'grid',
          margin: `0 0 0 ${pxToVw({ pixels: RoomNameMarginOffset })}`,
          gridAutoFlow: 'column',
          gridTemplateColumns: `${pxToVw({ pixels: roomNameSize })} ${
            enableVideo ? `calc(${height} / ${videoRatio})` : ''
          } ${pxToVw({ pixels: roomStatusSize })} 1fr`,
        }}
      >
        <div
          ref={hourHeader}
          css={{
            position: 'relative',
            height: pxToVh({ pixels: hourMarkerContainerSize }),
            width: `100%`,
            display: 'grid',
            gridAutoFlow: 'row',
            gridAutoColumns: 'minmax(0, 1fr)',
            rowGap: pxToVh({ pixels: 20 }),
            gridColumnStart: -2,
            gridColumnEnd: -1,
            marginLeft: pxToVw({ pixels: RoomNameAndCaseGap }),
          }}
        >
          {!hideMarkers && timelineSize?.width && (
            <>
              <CurrentMinuteMarker
                timelineDateTimeBounds={timelineDateTimeBounds}
                timelineSize={timelineSize?.width}
              />
              {markers}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
