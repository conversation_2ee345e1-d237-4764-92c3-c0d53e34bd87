import { ComponentProps } from 'react'

import { BoardViewType } from 'src/__generated__/globalTypes'
import { Tiles } from 'src/modules/board/components/Tiles'
import { Timeline } from 'src/modules/board/components/Timeline'
import { BoardConfig } from 'src/modules/board/types'

type TimelineProps = ComponentProps<typeof Timeline>
type TileProps = ComponentProps<typeof Tiles>
type BoardViewProps = TileProps | TimelineProps
type Props = BoardViewProps & {
  boardViewType: BoardConfig['boardViewType']
}

export const BigBoardView = ({ boardViewType, ...rest }: Props) => {
  if (boardViewType === BoardViewType.TIMELINE) {
    return <Timeline {...(rest as TimelineProps)} />
  }

  if (boardViewType === BoardViewType.TILE) {
    return <Tiles {...(rest as TileProps)} />
  }

  return null
}
