// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Board Header > renders board header 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: grid;
  margin: 0 1.1458333333333333vw;
  grid-auto-rows: min-content;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  row-gap: 1.2037037037037037vh;
}

.emotion-1 {
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: 1fr auto;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  gap: 2.03125vw;
}

.emotion-2 {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 2.7777777777777777vh;
  line-height: 1.25;
}

.emotion-2::after {
  content: "";
  display: block;
}

.emotion-4 {
  display: grid;
  grid-template-columns: 1fr auto;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-column-gap: 2.03125vw;
  column-gap: 2.03125vw;
}

.emotion-5 {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: auto;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  gap: 2.083333333333333vw;
}

.emotion-6 {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: auto;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  gap: 1.3020833333333335vw;
}

.emotion-7 {
  gap: 0.4166666666666667vw;
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: auto max-content 1fr;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
}

.emotion-8 {
  font-size: 1.25vh;
  line-height: 1.28;
}

.emotion-9 {
  font-size: 2.7777777777777777vh;
  line-height: 1.25;
}

.emotion-10 {
  font-size: 1.25vh;
  line-height: 0.66;
}

.emotion-11 {
  gap: 0.4166666666666667vw;
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: auto 1fr;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
}

.emotion-23 {
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: auto 1fr;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  gap: 0.4166666666666667vw;
}

.emotion-24 {
  font-size: 1.6666666666666667vh;
  line-height: 1.28;
}

.emotion-25 {
  font-size: 2.685185185185185vh;
  line-height: 1.25;
}

.emotion-26 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 4px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 6px;
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: rgba(0,0,0,0.7);
  padding: 0.1388888888888889vh 0.078125vw;
}

.emotion-26:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-26:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-26:active:not(:disabled) {
  background-color: #f2f2f2;
}

.emotion-27 {
  color: inherit;
  fill: currentcolor;
  height: 2.1296296296296298vh;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 2.1296296296296298vh;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

<header
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <p
        class="emotion-2 emotion-3"
      >
        Name
      </p>
      <div
        class="emotion-4"
      >
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <div
              class="emotion-7"
            >
              <p
                class="emotion-8"
              >
                REMAINING CASES
              </p>
              <p
                class="emotion-9"
              >
                — / —
              </p>
              <p
                class="emotion-10"
              >
                0 rooms open
              </p>
            </div>
            <div
              class="emotion-11"
            >
              <p
                class="emotion-8"
              >
                ROOMS CURRENTLY RUNNING
              </p>
              <p
                class="emotion-9"
              >
                0
              </p>
            </div>
            <div
              class="emotion-11"
            >
              <p
                class="emotion-8"
              >
                OPEN PAST 15:00
              </p>
              <p
                class="emotion-9"
              >
                0
              </p>
            </div>
            <div
              class="emotion-11"
            >
              <p
                class="emotion-8"
              >
                OPEN PAST 17:00
              </p>
              <p
                class="emotion-9"
              >
                0
              </p>
            </div>
            <div
              class="emotion-11"
            >
              <p
                class="emotion-8"
              >
                OPEN PAST 19:00
              </p>
              <p
                class="emotion-9"
              >
                0
              </p>
            </div>
          </div>
          <div
            class="emotion-23"
          >
            <p
              class="emotion-24"
            >
              December 19, 2023
            </p>
            <p
              class="emotion-25"
            >
              11:00
            </p>
          </div>
        </div>
        <a
          class="emotion-26"
          data-discover="true"
          href="/boards"
        >
          <svg
            class="emotion-27 emotion-28"
            viewBox="0 0 24 24"
          >
            <path
              clip-rule="evenodd"
              d="M18.3 5.71021C17.91 5.32021 17.28 5.32021 16.89 5.71021L12 10.5902L7.10997 5.70021C6.71997 5.31021 6.08997 5.31021 5.69997 5.70021C5.30997 6.09021 5.30997 6.72021 5.69997 7.11021L10.59 12.0002L5.69997 16.8902C5.30997 17.2802 5.30997 17.9102 5.69997 18.3002C6.08997 18.6902 6.71997 18.6902 7.10997 18.3002L12 13.4102L16.89 18.3002C17.28 18.6902 17.91 18.6902 18.3 18.3002C18.69 17.9102 18.69 17.2802 18.3 16.8902L13.41 12.0002L18.3 7.11021C18.68 6.73021 18.68 6.09021 18.3 5.71021Z"
              fill-rule="evenodd"
            />
          </svg>
        </a>
      </div>
    </div>
  </header>
</DocumentFragment>
`;

exports[`Board Header > renders board header with metrics 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: grid;
  margin: 0 1.1458333333333333vw;
  grid-auto-rows: min-content;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  row-gap: 1.2037037037037037vh;
}

.emotion-1 {
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: 1fr auto;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  gap: 2.03125vw;
}

.emotion-2 {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 2.7777777777777777vh;
  line-height: 1.25;
}

.emotion-2::after {
  content: "";
  display: block;
}

.emotion-4 {
  display: grid;
  grid-template-columns: 1fr auto;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-column-gap: 2.03125vw;
  column-gap: 2.03125vw;
}

.emotion-5 {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: auto;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  gap: 2.083333333333333vw;
}

.emotion-6 {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: auto;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  gap: 1.3020833333333335vw;
}

.emotion-7 {
  gap: 0.4166666666666667vw;
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: auto max-content 1fr;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
}

.emotion-8 {
  font-size: 1.25vh;
  line-height: 1.28;
}

.emotion-9 {
  font-size: 2.7777777777777777vh;
  line-height: 1.25;
}

.emotion-10 {
  font-size: 1.25vh;
  line-height: 0.66;
}

.emotion-11 {
  gap: 0.4166666666666667vw;
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: auto 1fr;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
}

.emotion-23 {
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: auto 1fr;
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  gap: 0.4166666666666667vw;
}

.emotion-24 {
  font-size: 1.6666666666666667vh;
  line-height: 1.28;
}

.emotion-25 {
  font-size: 2.685185185185185vh;
  line-height: 1.25;
}

.emotion-26 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 4px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 6px;
  font-weight: 600;
  font-size: 0.8125rem;
  line-height: 1.25rem;
  letter-spacing: -0.01em;
  background-color: transparent;
  color: rgba(0,0,0,0.7);
  padding: 0.1388888888888889vh 0.078125vw;
}

.emotion-26:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.emotion-26:hover:not(:active):not(:disabled) {
  background-color: #fbfbfb;
}

.emotion-26:active:not(:disabled) {
  background-color: #f2f2f2;
}

.emotion-27 {
  color: inherit;
  fill: currentcolor;
  height: 2.1296296296296298vh;
  -webkit-transition: fill 250ms;
  transition: fill 250ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 2.1296296296296298vh;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

<header
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <p
        class="emotion-2 emotion-3"
      >
        Name
      </p>
      <div
        class="emotion-4"
      >
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <div
              class="emotion-7"
            >
              <p
                class="emotion-8"
              >
                REMAINING CASES
              </p>
              <p
                class="emotion-9"
              >
                2 / 3
              </p>
              <p
                class="emotion-10"
              >
                1 rooms open
              </p>
            </div>
            <div
              class="emotion-11"
            >
              <p
                class="emotion-8"
              >
                ROOMS CURRENTLY RUNNING
              </p>
              <p
                class="emotion-9"
              >
                1
              </p>
            </div>
            <div
              class="emotion-11"
            >
              <p
                class="emotion-8"
              >
                OPEN PAST 15:00
              </p>
              <p
                class="emotion-9"
              >
                2
              </p>
            </div>
            <div
              class="emotion-11"
            >
              <p
                class="emotion-8"
              >
                OPEN PAST 17:00
              </p>
              <p
                class="emotion-9"
              >
                1
              </p>
            </div>
            <div
              class="emotion-11"
            >
              <p
                class="emotion-8"
              >
                OPEN PAST 19:00
              </p>
              <p
                class="emotion-9"
              >
                1
              </p>
            </div>
          </div>
          <div
            class="emotion-23"
          >
            <p
              class="emotion-24"
            >
              December 19, 2023
            </p>
            <p
              class="emotion-25"
            >
              11:00
            </p>
          </div>
        </div>
        <a
          class="emotion-26"
          data-discover="true"
          href="/boards"
        >
          <svg
            class="emotion-27 emotion-28"
            viewBox="0 0 24 24"
          >
            <path
              clip-rule="evenodd"
              d="M18.3 5.71021C17.91 5.32021 17.28 5.32021 16.89 5.71021L12 10.5902L7.10997 5.70021C6.71997 5.31021 6.08997 5.31021 5.69997 5.70021C5.30997 6.09021 5.30997 6.72021 5.69997 7.11021L10.59 12.0002L5.69997 16.8902C5.30997 17.2802 5.30997 17.9102 5.69997 18.3002C6.08997 18.6902 6.71997 18.6902 7.10997 18.3002L12 13.4102L16.89 18.3002C17.28 18.6902 17.91 18.6902 18.3 18.3002C18.69 17.9102 18.69 17.2802 18.3 16.8902L13.41 12.0002L18.3 7.11021C18.68 6.73021 18.68 6.09021 18.3 5.71021Z"
              fill-rule="evenodd"
            />
          </svg>
        </a>
      </div>
    </div>
  </header>
</DocumentFragment>
`;
