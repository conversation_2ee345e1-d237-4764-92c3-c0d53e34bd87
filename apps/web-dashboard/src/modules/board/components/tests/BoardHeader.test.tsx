import { BrowserRouter } from 'react-router'

import { ThemeProvider } from '@emotion/react'

import { render } from '@testing-library/react'
import { DateTime, Settings } from 'luxon'

import { theme } from '@apella/component-library'
import { Header } from 'src/modules/board/components/Header'
import { useApellaCaseDailyMetrics } from 'src/modules/daily-metrics/hooks/useApellaCaseDailyMetrics'

vi.mock('src/modules/daily-metrics/hooks/useApellaCaseDailyMetrics')
const mockedUseApellaCaseDailyMetrics = vi.mocked(useApellaCaseDailyMetrics)

const now = DateTime.fromISO('2023-12-19T19:00:00.000-00:00')

describe('Board Header', () => {
  beforeEach(() => {
    Settings.defaultZone = 'America/Los_Angeles'
    Settings.now = () => now.valueOf()
    mockedUseApellaCaseDailyMetrics.mockReset()
  })

  it('renders board header', () => {
    mockedUseApellaCaseDailyMetrics.mockReturnValue({
      isLoading: false,
      results: {},
    })

    const { asFragment } = render(
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <Header boardName="Name" />
        </ThemeProvider>
      </BrowserRouter>
    )

    expect(asFragment()).toMatchSnapshot()
  })

  it('renders board header with metrics', () => {
    mockedUseApellaCaseDailyMetrics.mockReturnValue({
      isLoading: false,
      results: {
        completedCases: 1,
        totalCases: 3,
        firstCasesCount: 1,
        firstCaseOnTimePercent: 0.99,
        firstCasesWithinFiveMinutesPercent: 1,
        urgentCasesCount: 1,
        addOnCasesCount: 1,
        afterHoursCasesCount: 1,
        showFirstCases: true,
        firstCaseStarts: ['a'],
        openToday: ['room'],
        stillOpen: ['room'],
        openPast15: ['room', 'room2'],
        openPast17: ['room'],
        openPast19: ['room'],
      },
    })

    const { asFragment } = render(
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <Header boardName="Name" />
        </ThemeProvider>
      </BrowserRouter>
    )

    expect(asFragment()).toMatchSnapshot()
  })
})
