import { useContext, useEffect, useState } from 'react'

import { ShowVideoContext } from 'src/Contexts'
import { PrivacyReason } from 'src/hooks/useRoomUpdate'
import { useBoardContext } from 'src/modules/board/hooks/BoardContextProvider'
import { pxToVw, useFontSizes } from 'src/modules/board/styles'
import { BigBoardRoom } from 'src/modules/board/types'

import { CaseType } from '../../../__generated__/globalTypes'
import { useCameraStatus, useMostRecentImage } from '../../../pages/Live/hooks'
import { CameraStatus } from '../../../pages/Live/LiveCameraStatus'
import LiveImage from '../../../pages/Live/LiveImage'
import { useBestCameraForRoom, useRoomCameras } from '../../../utils/hooks'
import { pxToVh } from '../pxToVh'

const useVideoBlockState = ({ room }: { room: BigBoardRoom }) => {
  const [privacyReason, setPrivacyReason] = useState<PrivacyReason>()
  const { cameras } = useRoomCameras(room.id)
  const [cameraId, setCameraId] = useBestCameraForRoom(room.id, cameras)
  const showVideoContext = useContext(ShowVideoContext)
  const { mostRecentImage, privacyEnabled } = useMostRecentImage(
    cameraId,
    !showVideoContext
  )
  const cameraStatus: CameraStatus = useCameraStatus(
    mostRecentImage?.captureTime
  )
  const liveCase = room.cases.find((c) => c.type === CaseType.LIVE)
  const {
    config: { enableVideo, blurVideo },
  } = useBoardContext()

  useEffect(() => {
    if (privacyEnabled) {
      setPrivacyReason(PrivacyReason.PRIVACY_ENABLED)
      return
    }

    if (blurVideo && !!liveCase) {
      setPrivacyReason(PrivacyReason.PATIENT_IN_ROOM)
    }
  }, [blurVideo, liveCase, privacyEnabled])

  return {
    setCameraId,
    cameraStatus,
    enableVideo,
    privacyReason,
    mostRecentImage,
    cameras,
    cameraId,
  }
}

export const VideoBlock = ({
  room,
  borderRadius,
  height,
}: {
  room: BigBoardRoom
  borderRadius?: string
  height?: string
}) => {
  const {
    privacyReason,
    enableVideo,
    setCameraId,
    cameraStatus,
    mostRecentImage,
    cameras,
    cameraId,
  } = useVideoBlockState({ room })

  const fontSizes = useFontSizes()

  if (!enableVideo) {
    return null
  }

  return (
    <LiveImage
      image={mostRecentImage?.url}
      cameras={cameras}
      activeCameraId={cameraId}
      setCameraId={setCameraId}
      cameraStatus={cameraStatus}
      controls={'hidden'}
      keyboardShortcutEnabled={false}
      privacyReason={privacyReason}
      fontSize={fontSizes.t2000.fontSize}
      height={height ?? '100%'}
      minWidth={'unset'}
      borderRadius={borderRadius}
      iconSize={pxToVh({ pixels: 40 })}
      blurredGap={pxToVw({ pixels: 10 })}
      roomId={room.id}
    />
  )
}
