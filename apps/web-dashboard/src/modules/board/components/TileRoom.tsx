import { useCallback, useEffect, useState } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'
import { darken, lighten, readableColor } from 'polished'

import {
  EllidedText,
  fontWeights,
  formatDuration,
  ZIndex,
} from '@apella/component-library'
import { VideoBlock } from 'src/modules/board/components/VideoBlock'
import {
  OverflowCaseWidth,
  RoomNameAndCaseGap,
  RoomNameMarginOffset,
  VideoBorderRadius,
} from 'src/modules/board/constants'
import { useBoardContext } from 'src/modules/board/hooks/BoardContextProvider'
import { useMainRect } from 'src/modules/board/hooks/useMainRect'
import { useRoomHeight } from 'src/modules/board/hooks/useRoomHeight'
import { elevations, pxToVw, useFontSizes } from 'src/modules/board/styles'
import { BigBoardRoom } from 'src/modules/board/types'

import {
  CaseStatusName,
  CaseType,
  RoomStatusName,
} from '../../../__generated__/globalTypes'
import { formatStatus, useStatusStyles } from '../../../utils/status'
import { pxToVh } from '../pxToVh'
import { BoardCase } from './Case'

const OverflowCase = ({ count }: { count: number }) => {
  const theme = useTheme()

  const mainRect = useMainRect()
  const fontSizes = useFontSizes()
  if (!count || !mainRect?.width) {
    return null
  }

  return (
    <div
      css={{
        position: 'absolute',
        width: OverflowCaseWidth,
        left: `calc(${mainRect.width}px - ${OverflowCaseWidth}px - ${pxToVw({
          pixels: RoomNameMarginOffset + 24,
        })})`,
        padding: pxToVw({ pixels: 4 }),
        height: '100%',
        border: 'solid 1px',
        borderColor: theme.palette.gray[40],
        background: theme.palette.background.primary,
        boxShadow: elevations.r200,
        borderRadius: pxToVh({ pixels: 10 }),
        overflow: 'hidden',
        display: 'grid',
        justifyContent: 'center',
        alignContent: 'center',
        zIndex: ZIndex.ABOVE,
      }}
    >
      <p css={{ ...fontSizes.t1500, textAlign: 'center' }}>
        <span css={{ ...fontWeights.semibold }}>+{count}</span>{' '}
        {`Case${count > 1 ? 's' : ''}`}
      </p>
    </div>
  )
}

export const TileRoom = ({ room }: { room: BigBoardRoom }) => {
  const roomHeight = useRoomHeight()
  const theme = useTheme()
  const roomName = room.name.replace('OR', '')
  const fontSizes = useFontSizes()
  const {
    boardConstants: { caseSize: tileCaseSize, roomNameSize },
    config: { enableVideo },
  } = useBoardContext()
  const [casesOffscreen, setCasesOffscreen] = useState<number>()

  const status = room.status.inProgressApellaCase?.status ?? room.status

  const activeCase = room.cases.find((c) => c.type !== CaseType.COMPLETE)

  const caseColors = useStatusStyles()

  const isEmpty = status.name === RoomStatusName.CLOSED

  const caseContainerId = `tile-room-${room.id}`

  const handleResize = useCallback(() => {
    // This timeout is necessary to avoid race conditions with screen painting. We were running into issue
    // where on resize this would run before the cases were repainted, so their bounding rects would be wrong
    // and we would thusly calculate incorrectly how many cases are offscreen
    window.setTimeout(() => {
      const windowWidth = window.innerWidth

      const casesOffscreenList = []
      const caseItems = document.getElementById(caseContainerId)?.children

      if (caseItems) {
        for (const caseItem of caseItems) {
          const caseBoundingRect = caseItem.getBoundingClientRect()
          const visiblePixels = windowWidth - caseBoundingRect.x

          if (
            visiblePixels <= OverflowCaseWidth ||
            // If more than half the case would not be visible with the
            // overflow case badge in place count the case as offscreen. However,
            // only do this logic if there is more than 1 "case / child" as we render an "empty" case
            // when there are no cases in the room and we don't want to count that one
            (visiblePixels / (caseBoundingRect.width - OverflowCaseWidth) <
              0.5 &&
              caseItems.length > 1 &&
              casesOffscreenList.length)
          ) {
            casesOffscreenList.push(caseItem)
          }
        }
      }

      const numberOfCasesOffscreen = casesOffscreenList.length
      setCasesOffscreen(numberOfCasesOffscreen)
    }, 0)
  }, [caseContainerId])

  useEffect(() => {
    if (casesOffscreen === undefined) {
      handleResize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [casesOffscreen, handleResize])

  const backgroundColorForStatus =
    caseColors(status.name)?.backgroundColor ??
    caseColors(CaseStatusName.SCHEDULED).backgroundColor

  const lightBackground = lighten(0.1, backgroundColorForStatus)
  const darkBackground = darken(0.1, backgroundColorForStatus)

  const readableBackgroundColor = readableColor(
    backgroundColorForStatus,
    darkBackground,
    lightBackground,
    false
  )

  return (
    <div
      css={{
        display: 'grid',
        margin: `0 0 0 ${pxToVw({ pixels: RoomNameMarginOffset })}`,
        gridAutoFlow: 'column',
        justifyContent: 'start',
        gridTemplateColumns: `${pxToVw({
          pixels: roomNameSize,
        })} min-content auto`,
        background: isEmpty ? theme.palette.gray[30] : undefined,
        borderRadius: pxToVh({ pixels: 16 }),
        position: 'relative',
        height: roomHeight,
      }}
    >
      <div
        css={{
          height: '100%',
          justifyContent: 'start',
          alignContent: 'center',
          display: 'grid',
          paddingLeft: pxToVw({ pixels: 16 }),
        }}
      >
        <EllidedText
          css={{
            ...fontSizes.t1500,
            color: theme.palette.gray[40],
          }}
          as="p"
        >
          OR
        </EllidedText>
        <EllidedText
          css={{ ...fontSizes.t3625, ...fontWeights.semibold }}
          as="p"
        >
          {roomName}
        </EllidedText>
      </div>
      <div
        css={{
          display: 'grid',
          gridTemplateColumns: isEmpty
            ? 'auto'
            : `auto ${pxToVw({ pixels: tileCaseSize.width })}`,
          borderRadius: VideoBorderRadius,
          overflow: 'hidden',
          boxShadow: activeCase ? elevations.r200 : undefined,
        }}
      >
        <div
          css={{
            position: 'relative',
            width: enableVideo
              ? undefined
              : pxToVw({ pixels: tileCaseSize.roomStatusSize }),
          }}
        >
          <VideoBlock
            room={room}
            height={`calc(${roomHeight} - ${pxToVh({
              pixels: tileCaseSize.roomStatusSize,
            })})`}
          />
          <div
            css={{
              display: 'grid',
              gridAutoFlow: 'column',
              justifyContent: 'start',
              alignContent: 'center',
              backgroundColor: readableBackgroundColor,
              ...(enableVideo
                ? {
                    height: pxToVh({ pixels: tileCaseSize.roomStatusSize }),
                    padding: `0 ${pxToVw({ pixels: 16 })}`,
                    columnGap: 5,
                  }
                : {
                    width: '100%',
                    padding: `${pxToVw({
                      pixels: 16,
                    })} 0`,
                    columnGap: pxToVh({ pixels: 16 }),
                    writingMode: 'vertical-lr',
                    transform: 'rotate(180deg)',
                    position: 'absolute',
                    top: 0,
                    bottom: 0,
                  }),
            }}
          >
            <EllidedText
              css={{
                ...fontSizes.t2000,
                ...fontWeights.semibold,
                display: 'inline',
                textTransform: 'capitalize',
              }}
            >
              {formatStatus(status.name)}
            </EllidedText>
            {!!status.since && !isEmpty && (
              <EllidedText
                css={{
                  display: 'inline',
                  ...fontSizes.t2000,
                }}
              >
                {formatDuration(DateTime.now().diff(status.since))} elapsed
              </EllidedText>
            )}
          </div>
        </div>
        {!isEmpty && (
          <div
            css={{
              borderRadius: `0 ${VideoBorderRadius}px ${VideoBorderRadius}px 0`,
              borderWidth: `1px 1px 1px 0`,
            }}
          >
            {activeCase && (
              <BoardCase apellaCase={activeCase} type="active"></BoardCase>
            )}
          </div>
        )}
      </div>
      <div
        css={{
          display: 'grid',
          gridAutoFlow: 'column',
          columnGap: pxToVw({ pixels: RoomNameAndCaseGap }),
          alignContent: 'center',
          marginLeft: pxToVw({ pixels: RoomNameAndCaseGap }),
        }}
        id={caseContainerId}
      >
        {isEmpty ? (
          <p css={{ ...fontSizes.t1500 }}>
            {formatStatus(RoomStatusName.CLOSED)}
          </p>
        ) : (
          room.cases
            .filter((c) => c.id !== activeCase?.id)
            .map((c) => (
              <BoardCase
                key={`${room.id}-${c.id}`}
                apellaCase={c}
                type="upcoming"
              />
            ))
        )}
      </div>
      {!!casesOffscreen && <OverflowCase count={casesOffscreen} />}
    </div>
  )
}
