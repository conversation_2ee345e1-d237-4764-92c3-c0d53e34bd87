import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import {
  EllidedText,
  fontWeights,
  formatDuration,
} from '@apella/component-library'
import {
  RoomNameAndCaseGap,
  RoomNameMarginOffset,
  VideoBorderRadius,
} from 'src/modules/board/constants'
import { useBoardContext } from 'src/modules/board/hooks/BoardContextProvider'
import { useRoomHeight } from 'src/modules/board/hooks/useRoomHeight'
import { pxToVw, useFontSizes } from 'src/modules/board/styles'
import { BigBoardRoom } from 'src/modules/board/types'
import { TimelineDateTimeBounds } from 'src/pages/Schedule/types'

import { RoomStatusName } from '../../../__generated__/globalTypes'
import { formatStatus, useStatusStyles } from '../../../utils/status'
import { BoardCase } from './Case'
import { VideoBlock } from './VideoBlock'

const RoomNameAndVideo = ({ room }: { room: BigBoardRoom }) => {
  const theme = useTheme()
  const roomName = room.name.replace('OR', '')
  const fontSizes = useFontSizes()
  const status = room.status.inProgressApellaCase?.status ?? room.status

  const {
    config: { enableVideo },
  } = useBoardContext()

  const caseColors = useStatusStyles()

  return (
    <>
      <div>
        <EllidedText
          css={{
            ...fontSizes.t1500,
            color: theme.palette.gray[40],
          }}
          as="p"
        >
          OR
        </EllidedText>
        <EllidedText
          css={{ ...fontSizes.t3625, ...fontWeights.semibold }}
          as="p"
        >
          {roomName}
        </EllidedText>
      </div>
      <VideoBlock
        room={room}
        borderRadius={`${VideoBorderRadius}px 0 0 ${VideoBorderRadius}px`}
      />
      <div
        css={{
          height: '100%',
          backgroundColor: caseColors(status.name)?.backgroundColor,
          padding: `0 ${pxToVw({ pixels: 8 })}`,
          display: 'grid',
          alignContent: 'center',
          justifyContent: 'center',
          borderRadius: !enableVideo
            ? VideoBorderRadius
            : `0 ${VideoBorderRadius}px ${VideoBorderRadius}px 0`,
        }}
      >
        <EllidedText
          css={{
            ...fontSizes.t2000,
            textTransform: 'capitalize',
            ...fontWeights.semibold,
            textAlign: 'center',
          }}
        >
          {formatStatus(status.name)}
        </EllidedText>
        {!!status.since && status.name !== RoomStatusName.CLOSED && (
          <EllidedText
            css={{
              ...fontSizes.t2000,
              textAlign: 'center',
            }}
          >
            {formatDuration(DateTime.now().diff(status.since))}
          </EllidedText>
        )}
        {!!status.since && status.name !== RoomStatusName.CLOSED && (
          <EllidedText
            css={{
              ...fontSizes.t1000,
              textAlign: 'center',
              textTransform: 'uppercase',
            }}
          >
            elapsed
          </EllidedText>
        )}
      </div>
    </>
  )
}

export const TimelineRoom = ({
  room,
  timelineDateTimeBounds,
}: {
  room: BigBoardRoom
  timelineDateTimeBounds: TimelineDateTimeBounds
}) => {
  const visibleCases = room.cases.filter(
    (c) =>
      (c.endTime === undefined ||
        c.endTime.toMillis() > timelineDateTimeBounds.minDateTime.toMillis()) &&
      c.startTime.toMillis() !== c.endTime?.toMillis()
  )

  const height = useRoomHeight()

  const {
    config: { enableVideo },
    boardConstants: {
      roomNameSize,
      videoRatio,
      caseSize: { roomStatusSize },
    },
  } = useBoardContext()

  return (
    <div
      css={{
        display: 'grid',
        height: `${height}`,
        margin: `0 0 0 ${pxToVw({ pixels: RoomNameMarginOffset })}`,
        gridAutoFlow: 'column',
        gridTemplateColumns: `${pxToVw({ pixels: roomNameSize })} ${
          enableVideo ? `calc(${height} / ${videoRatio})` : ''
        } ${pxToVw({ pixels: roomStatusSize })} 1fr`,
      }}
    >
      <RoomNameAndVideo room={room} />
      <div
        css={{
          position: 'relative',
          width: `100%`,
          marginLeft: pxToVw({ pixels: RoomNameAndCaseGap }),
        }}
      >
        {visibleCases.map((apellaCase) => (
          <BoardCase
            key={apellaCase.id}
            apellaCase={apellaCase}
            type="upcoming"
            timelineDateTimeBounds={timelineDateTimeBounds}
          />
        ))}
      </div>
    </div>
  )
}
