import { ComponentProps } from 'react'
import { generatePath } from 'react-router'

import { DateTime } from 'luxon'

import {
  ApellaDateTimeFormats,
  ButtonLink,
  Close,
  EllidedText,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'
import { useBoardZoom } from 'src/modules/board/hooks/useBoardZoom'
import { pxToVw, useFontSizes } from 'src/modules/board/styles'
import { useApellaCaseDailyMetrics } from 'src/modules/daily-metrics/hooks/useApellaCaseDailyMetrics'
import { LocationPath } from 'src/router/types'
import { EM_DASH } from 'src/utils/htmlCodes'

import { pxToVh } from '../pxToVh'

const MetricContainer = ({
  title,
  metric,
  subMetric,
}: {
  title: string
  metric: string | number
  subMetric?: string
}) => {
  const { zoom } = useBoardZoom({
    zoomMultiplier: 1,
  })

  const fontSizes = useFontSizes({ zoom })
  return (
    <div
      css={{
        gap: pxToVw({ pixels: 16 }),
        display: 'grid',
        gridAutoFlow: 'column',
        gridTemplateColumns: subMetric ? 'auto max-content 1fr' : 'auto 1fr',
        alignItems: 'baseline',
      }}
    >
      <p css={{ ...fontSizes.t16875 }}>{title.toLocaleUpperCase()}</p>
      <p
        css={{
          ...fontSizes.t3750,
        }}
      >
        {metric}
      </p>
      {subMetric && (
        <p
          css={{
            ...fontSizes.t16875,
            lineHeight: 0.66,
          }}
        >
          {subMetric}
        </p>
      )}
    </div>
  )
}

export const Header = ({
  boardName,
  roomIds,
  siteId,
}: {
  boardName: string
  siteId?: string
  roomIds?: string[]
}) => {
  const { timezone } = useTimezone()

  const { zoom } = useBoardZoom({
    zoomMultiplier: 1,
  })

  const fontSizes = useFontSizes({ zoom })

  const today = DateTime.now().setZone(timezone)

  const { results } = useApellaCaseDailyMetrics({
    siteIds: siteId ? [siteId] : [],
    roomIds: roomIds ?? [],
    minTime: today.startOf('day').toISO(),
    maxTime: today.endOf('day').toISO(),
  })

  const remainingCases =
    results.totalCases !== undefined && results.completedCases !== undefined
      ? Math.max(results.totalCases - results.completedCases, 0)
      : EM_DASH

  const metrics: ComponentProps<typeof MetricContainer>[] = [
    {
      title: 'Remaining Cases',
      metric: `${remainingCases} / ${results.totalCases || EM_DASH}`,
      subMetric: `${results.openToday?.length ?? 0} rooms open`,
    },
    {
      title: 'Rooms currently running',
      metric: results.stillOpen?.length ?? 0,
    },
    {
      title: 'Open past 15:00',
      metric: results.openPast15?.length ?? 0,
    },
    {
      title: 'Open past 17:00',
      metric: results.openPast17?.length ?? 0,
    },
    {
      title: 'Open past 19:00',
      metric: results.openPast19?.length ?? 0,
    },
  ]

  return (
    <header
      css={{
        display: 'grid',
        margin: `0 ${pxToVw({ pixels: 44 })}`,
        gridAutoRows: 'min-content',
        alignContent: 'center',
        rowGap: pxToVh({ pixels: 26 }),
      }}
    >
      <div
        css={{
          display: 'grid',
          gridAutoFlow: 'column',
          gridTemplateColumns: '1fr auto',
          alignItems: 'center',
          width: '100%',
          gap: pxToVw({ pixels: 78 }),
        }}
      >
        <EllidedText css={{ ...fontSizes.t3750 }} as="p">
          {boardName}
        </EllidedText>
        <div
          css={{
            display: 'grid',
            gridTemplateColumns: '1fr auto',
            alignItems: 'center',
            columnGap: pxToVw({ pixels: 78 }),
          }}
        >
          <div
            css={{
              display: 'grid',
              gridAutoFlow: 'column',
              gridAutoColumns: 'auto',
              alignItems: 'baseline',
              gap: pxToVw({ pixels: 80 }),
            }}
          >
            <div
              css={{
                display: 'grid',
                gridAutoFlow: 'column',
                gridAutoColumns: 'auto',
                alignItems: 'baseline',
                gap: pxToVw({ pixels: 50 }),
              }}
            >
              {metrics.map((metric) => (
                <MetricContainer key={metric.title} {...metric} />
              ))}
            </div>
            <div
              css={{
                display: 'grid',
                gridAutoFlow: 'column',
                gridTemplateColumns: 'auto 1fr',
                alignItems: 'baseline',
                gap: pxToVw({ pixels: 16 }),
              }}
            >
              <p css={{ ...fontSizes.t2250 }}>
                {today
                  .toLocaleString(
                    ApellaDateTimeFormats.DATETIME_WITH_MONTH_LONG
                  )
                  .toString()}
              </p>
              <p css={{ ...fontSizes.t3625 }}>
                {today.toLocaleString(ApellaDateTimeFormats.TIME).toString()}
              </p>
            </div>
          </div>
          <ButtonLink
            color="black"
            appearance="link"
            size={'sm'}
            to={generatePath(LocationPath.Boards)}
            state={{
              exitSiteId: siteId,
            }}
            buttonType={'icon'}
            css={{
              padding: `${pxToVh({ pixels: 3 })} ${pxToVw({ pixels: 3 })}`,
            }}
          >
            <Close size={fontSizes.t2875.fontSize} />
          </ButtonLink>
        </div>
      </div>
    </header>
  )
}
