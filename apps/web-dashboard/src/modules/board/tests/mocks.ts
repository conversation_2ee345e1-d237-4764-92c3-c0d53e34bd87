import { DateTime } from 'luxon'

import { CaseStatusName, CaseType } from '../../../__generated__/globalTypes'
import { BigBoardCase } from '../types'

export const mockBigBoardCase = (
  data?: Partial<Omit<BigBoardCase, 'case'>> & {
    case?: Partial<BigBoardCase['case']>
  }
): BigBoardCase => ({
  id: '1',
  type: CaseType.LIVE,
  startTime: DateTime.fromISO('2024-01-12T16:00:00.000Z').minus({
    minutes: 10,
  }),
  endTime: DateTime.fromISO('2024-01-12T16:00:00.000Z').plus({ minutes: 10 }),
  endTimeOffset: undefined,
  startTimeOffset: undefined,
  ...data,
  status: {
    name: CaseStatusName.SURGERY,
    since: DateTime.now().minus({ minutes: 5 }),
    ...data?.status,
  },
  case: {
    isAddOn: false,
    id: '1',
    scheduledStartTime: DateTime.fromISO('2024-01-12T16:00:00.000Z').minus({
      minutes: 10,
    }),
    scheduledEndTime: DateTime.fromISO('2024-01-12T16:00:00.000Z').plus({
      minutes: 10,
    }),
    staff: [],
    staffPlan: [],
    procedures: [{ name: 'Procedure', anesthesiaType: 'General' }],
    caseFlags: undefined,
    ...data?.case,
  },
})
