import { DateTime } from 'luxon'

import { CaseSizeConstant } from 'src/modules/board/constants'
import { TimelineDateTimeBounds } from 'src/pages/Schedule/types'

export const calculateOffsetLeft = ({
  startTime,
  timelineDateTimeBounds,
}: {
  startTime: DateTime
  timelineDateTimeBounds: TimelineDateTimeBounds
}) => {
  const offset =
    startTime.toMillis() - timelineDateTimeBounds.minDateTime.toMillis()

  if (offset <= 0) {
    return 0
  }

  return (
    (offset / CaseSizeConstant.duration.toMillis()) * CaseSizeConstant.width
  )
}
