import { useBoardContext } from 'src/modules/board/hooks/BoardContextProvider'

import { pxToVh } from './pxToVh'
import { pxToVw } from './styles'

// Intentionally using vh as our fluid measurement for big board as we want to ensure that all content
// is viewable vertically. This isn't standard "web" practice, but we want to do our best to ensure
// that that we don't overflow the y axis, so controlling the vh of the font is a way to help do this.

export const useResponsiveSizing = () => {
  const {
    config: { zoom: ratio },
  } = useBoardContext()

  return {
    vh: ({ pixels }: { pixels: number }) => pxToVh({ pixels: pixels * ratio }),
    vw: ({ pixels }: { pixels: number }) => pxToVw({ pixels: pixels * ratio }),
  }
}
