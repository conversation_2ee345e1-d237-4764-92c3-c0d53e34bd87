import { DateTime } from 'luxon'

import { BoardViewType } from 'src/__generated__/globalTypes'
import { BoardConfig } from 'src/modules/board/types'

export const toBoardConfigFromEdge =
  (timezone: string) =>
  ({
    node,
  }: {
    __typename: 'BoardConfigEdge'
    node: {
      __typename: 'BoardConfig'
      id: string
      name: string
      enableVideo: boolean
      updatedTime: string
      blurVideo: boolean
      pageDuration: number
      pageSize: number
      boardViewType: BoardViewType
      site: { __typename: 'Site'; id: string }
      updatedByUser: { __typename: 'User'; name: string } | null
      rooms: Array<{ __typename: 'Room'; id: string }> | null
      zoomPercent: number
      showClosedRooms: boolean | null
    }
  }): BoardConfig => ({
    id: node.id,
    name: node.name,
    siteId: node.site.id,
    lastUpdated: DateTime.fromISO(node.updatedTime).setZone(timezone),
    lastUpdatedBy: node.updatedByUser?.name ?? 'Unknown',
    roomIds: node.rooms?.length ? node.rooms.map((r) => r.id) : undefined,
    zoom: node.zoomPercent / 100,
    roomsPerPage: node.pageSize,
    pageDurationSeconds: node.pageDuration,
    blurVideo: node.blurVideo,
    boardViewType: node.boardViewType,
    enableVideo: node.enableVideo,
    showClosedRooms: node.showClosedRooms ?? true,
  })
