import { useQuery } from '@apollo/client'

import { BoardConfig } from 'src/modules/board/types'

import { useTimezone } from '../../../Contexts'
import { GET_BOARD_CONFIGS_WITH_SITES } from '../queries'
import {
  BoardConfigsWithSites,
  BoardConfigsWithSitesVariables,
} from '../queries/__generated__'
import { toBoardConfigFromEdge } from './toBoardConfigFromEdge'

export const useSitesAndBoardConfigs = ({ siteId }: { siteId?: string }) => {
  const { timezone } = useTimezone()

  const { data, loading } = useQuery<
    BoardConfigsWithSites,
    BoardConfigsWithSitesVariables
  >(GET_BOARD_CONFIGS_WITH_SITES, {
    variables: {
      siteIds: siteId ? [siteId] : null,
    },
  })

  const sites = data?.sites?.edges.map(({ node }) => ({
    ...node,
  }))

  const boardConfigs = data?.boardConfigs?.edges.map<BoardConfig>(
    toBoardConfigFromEdge(timezone)
  )

  return {
    loading,
    sites,
    boardConfigs,
  }
}
