import { createContext, ReactNode, useContext } from 'react'
import { useSearchParams } from 'react-router'

import { BoardViewType } from 'src/__generated__/globalTypes'
import { RoomGap, screenSizes } from 'src/modules/board/constants'
import { useBoardZoom } from 'src/modules/board/hooks/useBoardZoom'
import { BoardConfig } from 'src/modules/board/types'
import { getCleanedUrlParam } from 'src/utils/getCleanedUrlParam'

import { pxToVh } from '../pxToVh'

const HeaderHeight = 137
const TileCaseHeight = 469
const TimelineCaseHeight = 433
const TileCaseWidth = 709
const FooterHeight = 96
export const RoomNameSize = 102
const HourMarkerContainerSize = 85
const TimelineRoomStatusSize = 218
const VideoRatio = 9 / 16

const parseSizeUnit = (size: string, unit: 'vh' | 'vw' | '%' = 'vh') =>
  parseFloat(size.replaceAll(unit, ''))

interface BoardConstants {
  baseFontSize: number
  caseSize: {
    height: number
    width: number
    roomStatusSize: number
    hourMarkerContainerSize: number
  }
  footerHeight: number
  headerHeight: number
  roomNameSize: number
  videoRatio: number
}

const DEFAULT_BOARD_CONFIG: BoardConfig = {
  id: '',
  name: '',
  siteId: '',
  roomsPerPage: 10,
  pageDurationSeconds: 30,
  enableVideo: false,
  blurVideo: false,
  boardViewType: BoardViewType.TILE,
  zoom: 1,
  showClosedRooms: true,
}

export const BoardContext = createContext<{
  boardConstants: BoardConstants
  config: BoardConfig
}>({
  boardConstants: {
    headerHeight: HeaderHeight,
    footerHeight: FooterHeight,
    roomNameSize: RoomNameSize,
    caseSize: {
      width: TileCaseWidth,
      height: TileCaseHeight,
      roomStatusSize: 74,
      hourMarkerContainerSize: HourMarkerContainerSize,
    },
    baseFontSize: 16,
    videoRatio: VideoRatio,
  },
  config: DEFAULT_BOARD_CONFIG,
})

export const useBoardContext = () => useContext(BoardContext)

const getRoomsPerPage = (boardConstants: BoardConstants) => {
  const roomVh = pxToVh({ pixels: boardConstants.caseSize.height + RoomGap })

  const availableBoardHeight = pxToVh({
    pixels:
      screenSizes.fourK.height -
      boardConstants.headerHeight -
      boardConstants.footerHeight,
  })

  return Math.floor(
    parseSizeUnit(availableBoardHeight, 'vh') / parseSizeUnit(roomVh, 'vh')
  )
}

const BoardConfigEnabledUrlParamOverrides: (keyof BoardConfig)[] = [
  'zoom',
  'name',
  'pageDurationSeconds',
  'enableVideo',
  'blurVideo',
  'boardViewType',
  'zoom',
  'roomIds',
]

export const BoardProvider = ({
  children,
  config,
}: {
  children: ReactNode
  config?: BoardConfig
}) => {
  const [searchParams] = useSearchParams()
  const configFromParams: Partial<{ [key in keyof BoardConfig]: any }> = {}
  searchParams.forEach((val, key) => {
    if (
      BoardConfigEnabledUrlParamOverrides.includes(key as keyof BoardConfig)
    ) {
      configFromParams[key as keyof BoardConfig] = getCleanedUrlParam({
        searchParams,
        key,
        defaultValue: undefined,
      })
    }
  })

  const boardConfig = {
    ...(config ?? DEFAULT_BOARD_CONFIG),
    ...configFromParams,
  }

  const { zoom } = useBoardZoom({
    zoomMultiplier: boardConfig.zoom,
  })

  const ViewTypeSizingLookup = {
    [BoardViewType.TILE]: {
      caseSize: {
        width: TileCaseWidth * Math.max(1, zoom),
        height: TileCaseHeight * zoom,
        roomStatusSize: !boardConfig.enableVideo
          ? 74 * Math.max(1, zoom)
          : 74 * zoom,
        hourMarkerContainerSize: HourMarkerContainerSize * zoom,
      },
    },
    [BoardViewType.TIMELINE]: {
      caseSize: {
        width: TileCaseWidth * Math.max(1, zoom),
        height: TimelineCaseHeight * zoom,
        roomStatusSize: !boardConfig.enableVideo
          ? TimelineRoomStatusSize * Math.max(1, zoom)
          : TimelineRoomStatusSize * zoom,
        hourMarkerContainerSize: HourMarkerContainerSize * zoom,
      },
    },
  }

  const boardConstants: BoardConstants = {
    headerHeight: HeaderHeight,
    footerHeight: FooterHeight,
    roomNameSize: RoomNameSize * Math.max(1, zoom),
    caseSize:
      ViewTypeSizingLookup[boardConfig.boardViewType as BoardViewType].caseSize,
    baseFontSize: 16,
    videoRatio: VideoRatio,
  }

  const roomsPerPage = getRoomsPerPage(boardConstants)

  return (
    <BoardContext.Provider
      value={{
        boardConstants,
        config: {
          ...boardConfig,
          roomsPerPage,
          zoom,
        },
      }}
    >
      {children}
    </BoardContext.Provider>
  )
}
