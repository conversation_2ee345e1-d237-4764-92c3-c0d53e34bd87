import { useCallback, useEffect, useMemo, useState } from 'react'

import { DateTime } from 'luxon'

export const useSwipeEvent = () => {
  const [swipeState, setSwipeState] = useState<{
    startX: number
    endX: number
    diffX: number
    startY: number
    endY: number
    diffY: number
    fingersUsed: number
    startTime: number
    elapsedTime: number
  }>({
    startX: 0,
    endX: 0,
    startY: 0,
    endY: 0,
    fingersUsed: 0,
    diffX: 0,
    diffY: 0,
    startTime: DateTime.now().toMillis(),
    elapsedTime: 0,
  })

  const handleStart = useCallback(
    (event: TouchEvent) => {
      if (!event.changedTouches || !event.changedTouches.length) {
        return
      }

      const startTime = DateTime.now().toMillis()

      setSwipeState({
        endX: 0,
        startX: Math.floor(event.changedTouches[0].clientX),
        diffX: 0,
        startY: Math.floor(event.changedTouches[0].clientY),
        endY: 0,
        diffY: 0,
        fingersUsed: event.touches.length,
        startTime: startTime,
        elapsedTime: 0,
      })
    },
    [setSwipeState]
  )

  const handleTouchEnd = useCallback(
    (event: TouchEvent) => {
      if (!event.changedTouches || !event.changedTouches.length) {
        return
      }

      const endX = Math.ceil(event.changedTouches[0].clientX)
      const endY = Math.ceil(event.changedTouches[0].clientY)
      setSwipeState((prev) => ({
        ...prev,
        endX: endX,
        endY,
        diffX: prev.startX - endX,
        diffY: prev.startY - endY,
        elapsedTime: DateTime.now().toMillis() - prev.startTime,
      }))
    },
    [setSwipeState]
  )

  useEffect(() => {
    window.addEventListener('touchstart', handleStart)
    window.addEventListener('touchend', handleTouchEnd)

    return () => {
      window.removeEventListener('touchstart', handleStart)
      window.removeEventListener('touchstart', handleTouchEnd)
    }
  }, [handleStart, handleTouchEnd])

  const state = useMemo(() => {
    const diffXThreshold = 20,
      diffYThreshold = 20,
      fingersUsedThreshold = 1,
      allowedTimeThreshold = 300,
      driftThreshold = 100

    const { diffX, elapsedTime, diffY, fingersUsed } = swipeState

    if (
      elapsedTime > allowedTimeThreshold ||
      fingersUsed < fingersUsedThreshold
    ) {
      return
    }

    // Swipe X
    if (
      Math.abs(diffX) >= diffXThreshold &&
      Math.abs(diffY) <= driftThreshold
    ) {
      return diffX < 0 ? 'right' : 'left'
    }
    // Swipe Y
    if (
      Math.abs(diffY) >= diffYThreshold &&
      Math.abs(diffX) <= driftThreshold
    ) {
      return diffX < 0 ? 'up' : 'down'
    }

    return
  }, [swipeState])

  return state
}
