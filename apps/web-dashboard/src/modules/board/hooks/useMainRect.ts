import { useEffect, useState } from 'react'

export const useMainRect = () => {
  const [main, setMain] = useState<DOMRect>()

  useEffect(() => {
    const handleResize = () => {
      const mainEl = document.getElementsByTagName('main')?.[0]

      if (mainEl) {
        const boundingRect = mainEl.getBoundingClientRect()

        setMain(boundingRect)
      }
    }

    if (!main) {
      handleResize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [main])

  return main
}
