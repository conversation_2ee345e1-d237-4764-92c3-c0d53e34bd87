import { useQuery } from '@apollo/client'

import { useTimezone } from '../../../Contexts'
import { useCurrentUser } from '../../user/hooks'
import { GET_BOARD_CONFIGS } from '../queries'
import { BoardConfigs, BoardConfigsVariables } from '../queries/__generated__'
import { BoardConfig } from '../types'
import { toBoardConfigFromEdge } from './toBoardConfigFromEdge'

export interface BoardConfigsState {
  data: BoardConfig[]
  isLoading: boolean
}

export const useBoardConfig = ({ boardId }: { boardId: string }) => {
  const { data, isLoading } = useBoardConfigsState()

  return {
    boardConfig: data.find((c) => c.id === boardId),
    isLoading,
  }
}

const useBoardConfigsState = (): BoardConfigsState => {
  const { currentOrganization } = useCurrentUser()

  const { timezone } = useTimezone()
  const orgId = currentOrganization?.node?.id
  const { data, loading } = useQuery<BoardConfigs, BoardConfigsVariables>(
    GET_BOARD_CONFIGS,
    { variables: { orgIds: orgId ? [orgId] : [] } }
  )
  return {
    data: data
      ? data.boardConfigs.edges.map<BoardConfig>(
          toBoardConfigFromEdge(timezone)
        )
      : [],
    isLoading: loading,
  }
}
