import { useCallback, useEffect, useState } from 'react'

import { screenSizes } from 'src/modules/board/constants'

export const MaxZoom = 5
export const MinZoom = 0.25

const toBoardZoom = (boardZoom: number) => {
  return Math.min(<PERSON><PERSON><PERSON>, Math.max(MinZoom, boardZoom))
}

export const useBoardZoom = (options?: {
  zoomMultiplier?: number
}): {
  zoom: number
} => {
  const { zoomMultiplier = 1 } = options ?? {}
  const [initialZoom, setInitialZoom] = useState<number>()
  const [zoom, setZoom] = useState<number>()

  const setRatioCb = useCallback(() => {
    if (!initialZoom) {
      return
    }

    if (window.innerHeight === screen.height) {
      const initialZoom = screenSizes.fourK.height / window.innerHeight
      setInitialZoom(initialZoom)
      setZoom(
        toBoardZoom(screenSizes.fourK.height / window.innerHeight / initialZoom)
      )
      return
    }

    setZoom(
      toBoardZoom(screenSizes.fourK.height / window.innerHeight / initialZoom)
    )
  }, [setZoom, initialZoom])

  useEffect(() => {
    if (!initialZoom) {
      setInitialZoom(toBoardZoom(screenSizes.fourK.height / window.innerHeight))
    }
  }, [initialZoom])

  useEffect(() => {
    if (zoom === undefined && initialZoom) {
      setRatioCb()
    }

    window.addEventListener('resize', setRatioCb)

    return () => {
      window.removeEventListener('resize', setRatioCb)
    }
  }, [zoom, setRatioCb, initialZoom])

  return {
    zoom: toBoardZoom((zoom ?? 1) * zoomMultiplier),
  }
}
