import { useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { CaseType, RoomStatusName } from 'src/__generated__/globalTypes'
import { useTimezone } from 'src/Contexts'
import { GET_BIG_BOARD_DATA } from 'src/modules/board/queries'
import {
  BigBoardData,
  BigBoardDataVariables,
} from 'src/modules/board/queries/__generated__'
import {
  BigBoardCase,
  BigBoardRoom,
  BigBoardSite,
} from 'src/modules/board/types'
import { SLOW_POLL_INTERVAL_MS } from 'src/pages/Live/consts'
import { usePatientReadAccess } from 'src/utils/usePatientReadAccess'
import { usePolling } from 'src/utils/usePolling'

import { getConflictingLastNames } from '../../../utils/getFullName'
import { CaseFlagOrdering, CaseFlagType } from '../../planning/types'
import { useBoardContext } from './BoardContextProvider'

export interface BigBoardState {
  data: BigBoardSite[]
  isLoading: boolean
}

export const useBigBoardState = ({
  minTime,
  maxTime,
  siteIds,
  roomIds,
  skip,
}: {
  minTime: string
  maxTime: string
  siteIds?: string[]
  roomIds?: string[]
  skip?: boolean
}): BigBoardState => {
  const { timezone } = useTimezone()
  const showPatientData = usePatientReadAccess()
  const {
    config: { showClosedRooms },
  } = useBoardContext()

  const roomStatusFilter = showClosedRooms
    ? undefined
    : Object.values(RoomStatusName).filter(
        (status) => status !== RoomStatusName.CLOSED
      )

  const { loading, data, startPolling, stopPolling, refetch } = useQuery<
    BigBoardData,
    BigBoardDataVariables
  >(GET_BIG_BOARD_DATA, {
    variables: {
      maxStartTime: maxTime,
      minEndTime: minTime,
      siteIds: siteIds!,
      roomIds: roomIds && roomIds.length ? roomIds : undefined,
      caseTypes: [CaseType.FORECAST, CaseType.LIVE],
      skipPatientInfo: !showPatientData,
      statusFilter: roomStatusFilter,
    },
    skip: skip || !siteIds || !siteIds.length,
  })
  usePolling({
    refetch,
    startPolling,
    stopPolling,
    skip: skip || !siteIds || !siteIds.length,
    interval: SLOW_POLL_INTERVAL_MS,
  })

  const bigBoardState = useMemo(() => {
    if (loading || !data) {
      return []
    }

    const allStaff = data.sites.edges
      .flatMap(({ node: site }) =>
        site.rooms.edges.flatMap((r) =>
          r.node.apellaCases.edges.flatMap((c) =>
            c.node.case?.caseStaff
              .map((cs) => cs.staff)
              .concat(
                c.node.case?.caseStaffPlan.edges
                  .map((csp) => csp.node.staff)
                  .filter(Boolean) ?? []
              )
          )
        )
      )
      .filter(Boolean)
    const conflictingLastNames = getConflictingLastNames(allStaff)

    const sites = data.sites.edges.map<BigBoardSite>(({ node: site }) => {
      const rooms = site.rooms.edges
      return {
        ...site,
        rooms: rooms.map<BigBoardRoom>(({ node: room }) => {
          return {
            site: {
              ...site,
            },
            ...room,
            status: {
              ...room.status,
              inProgressApellaCase: room.status.inProgressApellaCase
                ? {
                    status: {
                      ...room.status.inProgressApellaCase.status,
                      since: room.status.inProgressApellaCase.status.since
                        ? DateTime.fromISO(
                            room.status.inProgressApellaCase.status.since
                          )
                        : undefined,
                    },
                  }
                : undefined,
              since: room.status.since
                ? DateTime.fromISO(room.status.since)
                : undefined,
            },
            cases: room.apellaCases.edges.map<BigBoardCase>(
              ({ node: apellaCase }) => {
                const endTimeOffset =
                  apellaCase.endTime && apellaCase.case?.scheduledEndTime
                    ? DateTime.fromISO(apellaCase.endTime).diff(
                        DateTime.fromISO(apellaCase.case.scheduledEndTime)
                      )
                    : undefined
                const startTimeOffset = apellaCase.case?.scheduledStartTime
                  ? DateTime.fromISO(apellaCase.startTime).diff(
                      DateTime.fromISO(apellaCase.case.scheduledStartTime)
                    )
                  : undefined

                // Create a lookup so we can order the flags on our case
                const caseFlagsMap = apellaCase.case?.caseFlags.reduce<{
                  [key in CaseFlagType]?: (typeof apellaCase.case.caseFlags)[0]
                }>(
                  (accum, curr) => ({
                    ...accum,
                    [curr.flagType as CaseFlagType]: curr,
                  }),
                  {}
                )
                return {
                  ...apellaCase,
                  status: {
                    ...apellaCase.status,
                    since: apellaCase.status.since
                      ? DateTime.fromISO(apellaCase.status.since)
                      : undefined,
                  },
                  startTime: DateTime.fromISO(apellaCase.startTime).setZone(
                    timezone
                  ),
                  endTime: apellaCase.endTime
                    ? DateTime.fromISO(apellaCase.endTime).setZone(timezone)
                    : undefined,
                  endTimeOffset: endTimeOffset || undefined,
                  startTimeOffset: startTimeOffset || undefined,

                  case: apellaCase.case
                    ? {
                        ...apellaCase.case,
                        isAddOn: apellaCase.case.isAddOn ?? false,
                        scheduledStartTime: DateTime.fromISO(
                          apellaCase.case.scheduledStartTime
                        ).setZone(timezone),
                        scheduledEndTime: DateTime.fromISO(
                          apellaCase.case.scheduledEndTime
                        ).setZone(timezone),
                        procedures: apellaCase.case.primaryCaseProcedures
                          .map(({ anesthesia, procedure }) => ({
                            name: procedure.name,
                            anesthesiaType: anesthesia?.name,
                          }))
                          .filter(Boolean),
                        staff: apellaCase.case.caseStaff.map((cs) => {
                          return {
                            ...cs.staff,
                            role: cs.role ?? '',
                            displayName: conflictingLastNames.has(
                              cs.staff.lastName
                            )
                              ? `${cs.staff.lastName}, ${cs.staff.firstName}`
                              : cs.staff.lastName,
                          }
                        }),
                        staffPlan: apellaCase.case.caseStaffPlan.edges
                          .map((e) => e.node)
                          .map((csp) => {
                            if (csp.staff === null) {
                              return undefined
                            }
                            return {
                              ...csp.staff,
                              role: csp.role ?? '',
                              displayName: conflictingLastNames.has(
                                csp.staff.lastName
                              )
                                ? `${csp.staff.lastName}, ${csp.staff.firstName}`
                                : (csp.staff.lastName ?? ''),
                            }
                          })
                          .filter(Boolean),
                        caseFlags: CaseFlagOrdering.map((type) => {
                          return caseFlagsMap?.[type]
                            ? {
                                ...caseFlagsMap?.[type],
                                flagType: type,
                              }
                            : undefined
                        }).filter(Boolean),
                        notePlan: apellaCase.case?.notePlan || undefined,
                        patient: apellaCase.case?.patient
                          ? {
                              id: apellaCase.case.patient.id,
                              administrativeSex:
                                apellaCase.case.patient.personalInfo
                                  ?.administrativeSex?.text || '',
                              abbreviatedName: apellaCase.case.patient
                                .personalInfo
                                ? `${apellaCase.case.patient.personalInfo.lastNameAbbreviated}., ${apellaCase.case.patient.personalInfo.firstNameAbbreviated}.`
                                : '',
                              age:
                                apellaCase.case.patient.personalInfo?.age ??
                                undefined,
                            }
                          : undefined,
                      }
                    : undefined,
                }
              }
            ),
          }
        }),
      }
    })
    return sites
  }, [data, loading, timezone])

  return { data: bigBoardState, isLoading: loading }
}
