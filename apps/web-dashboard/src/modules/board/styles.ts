import { useTheme } from '@emotion/react'

import { CaseStatusName, RoomStatusName } from 'src/__generated__/globalTypes'
import { screenSizes } from 'src/modules/board/constants'
import { useBoardContext } from 'src/modules/board/hooks/BoardContextProvider'
import { StatusSuperset } from 'src/utils/status'

import { pxToVh } from './pxToVh'

export const pxToVw = ({
  pixels,
  width = screenSizes.fourK.width,
}: {
  pixels: number
  width?: number
}) => `${(pixels / width) * 100}vw`

const fontSizeToVh = ({
  ratio,
  baseFontSize = 16,
}: {
  ratio: number
  baseFontSize?: number
}) => pxToVh({ pixels: baseFontSize * ratio })

export const useFontSizes = ({ zoom }: { zoom?: number } = {}) => {
  const {
    boardConstants: { baseFontSize: initialBaseFontSize },
    config: { zoom: configZoom },
  } = useBoardContext()

  const baseFontSize = initialBaseFontSize * (zoom ?? configZoom)

  return {
    t500: {
      fontSize: fontSizeToVh({ ratio: 0.5, baseFontSize }),
      lineHeight: 1.33,
    },
    t750: {
      fontSize: fontSizeToVh({ ratio: 0.75, baseFontSize }),
      lineHeight: 1.33,
    },
    t875: {
      fontSize: fontSizeToVh({ ratio: 0.875, baseFontSize }),
      lineHeight: 1.33,
    },
    t1000: {
      fontSize: fontSizeToVh({ ratio: 1, baseFontSize }),
      lineHeight: 1.33,
    },
    t1125: {
      fontSize: fontSizeToVh({ ratio: 1.125, baseFontSize }),
      lineHeight: 1.33,
    },
    t1312: {
      fontSize: fontSizeToVh({ ratio: 1.3125, baseFontSize }),
      lineHeight: 1.28,
    },
    t1250: {
      fontSize: fontSizeToVh({ ratio: 1.25, baseFontSize }),
      lineHeight: 1.4,
    },
    t1500: {
      fontSize: fontSizeToVh({ ratio: 1.5, baseFontSize }),
      lineHeight: 1.33,
    },
    t16875: {
      fontSize: fontSizeToVh({ ratio: 1.6875, baseFontSize }),
      lineHeight: 1.28,
    },
    t1750: {
      fontSize: fontSizeToVh({ ratio: 1.75, baseFontSize }),
      lineHeight: 1.33,
    },
    t1875: {
      fontSize: fontSizeToVh({ ratio: 1.875, baseFontSize }),
      lineHeight: 1.33,
    },
    t2000: {
      fontSize: fontSizeToVh({ ratio: 2, baseFontSize }),
      lineHeight: 1.5,
    },
    t2437: {
      fontSize: fontSizeToVh({ ratio: 2.4375, baseFontSize }),
      lineHeight: 1.28,
    },
    t2250: {
      fontSize: fontSizeToVh({ ratio: 2.25, baseFontSize }),
      lineHeight: 1.28,
    },
    t2875: {
      fontSize: fontSizeToVh({ ratio: 2.875, baseFontSize }),
      lineHeight: 1.28,
    },
    t3000: {
      fontSize: fontSizeToVh({ ratio: 3, baseFontSize }),
      lineHeight: 1.25,
    },
    t3625: {
      fontSize: fontSizeToVh({ ratio: 3.625, baseFontSize }),
      lineHeight: 1.25,
    },
    t3750: {
      fontSize: fontSizeToVh({ ratio: 3.75, baseFontSize }),
      lineHeight: 1.25,
    },
  }
}

export const elevations = {
  r200: `0 ${pxToVh({ pixels: 6 })} ${pxToVh({
    pixels: 10,
  })} 0 rgba(0, 0, 0, 0.08), 0 ${pxToVh({ pixels: 1 })} ${pxToVh({
    pixels: 18,
  })} 0 rgba(0, 0, 0, 0.06), 0 ${pxToVh({ pixels: 3 })} ${pxToVh({
    pixels: 5,
  })} 0 rgba(0, 0, 0, 0.10)`,
  r100: `0px ${pxToVh({ pixels: 1 })} ${pxToVh({
    pixels: 1,
  })} 0px rgba(0, 0, 0, 0.08), 0px ${pxToVh({ pixels: 2 })} ${pxToVh({
    pixels: 1,
  })} 0px rgba(0, 0, 0, 0.06), 0px ${pxToVh({ pixels: 1 })} ${pxToVh({
    pixels: 3,
  })} 0px rgba(0, 0, 0, 0.10)`,
}

export const useCaseColors = (): {
  [caseStatusName in StatusSuperset]?:
    | {
        backgroundColor: string
        borderColor: string
      }
    | undefined
} => {
  const theme = useTheme()
  return {
    [CaseStatusName.SURGERY]: {
      backgroundColor: '#ABCE88',
      borderColor: '#8CB860',
    },
    [CaseStatusName.ACTUAL]: { backgroundColor: '', borderColor: '' },
    [CaseStatusName.COMPLETE]: { backgroundColor: '', borderColor: '' },
    [CaseStatusName.IN_FACILITY]: {
      backgroundColor: '#ECD4BF',
      borderColor: '#BA9B7F',
    },
    [CaseStatusName.IN_HOLD]: {
      backgroundColor: '#F2C5A0',
      borderColor: '#E18B47',
    },
    [CaseStatusName.PHASE_II]: {
      backgroundColor: '#F4D3FC',
      borderColor: '#D666F2',
    },
    [CaseStatusName.PREP]: {
      backgroundColor: '#B0E0E2',
      borderColor: '#72C8C8',
    },
    [CaseStatusName.PRE_PROCEDURE]: {
      backgroundColor: '#FFF9DA',
      borderColor: '#F9EAA0',
    },
    [CaseStatusName.PRE_PROCEDURE_COMPLETE]: {
      backgroundColor: '#FFF9DA',
      borderColor: '#F9EAA0',
    },
    [CaseStatusName.RECOVERY]: {
      backgroundColor: '#D9EEFC',
      borderColor: '#B8D4E6',
    },
    [CaseStatusName.SCHEDULED]: {
      backgroundColor: '#FFFFFF',
      borderColor: '#E8E9EE',
    },
    [CaseStatusName.WRAP_UP]: {
      backgroundColor: '#DBFBCE',
      borderColor: '#B7F59D',
    },
    [RoomStatusName.CLOSED]: {
      backgroundColor: theme.palette.gray[40],
      borderColor: theme.palette.gray[40],
    },
    [RoomStatusName.IDLE]: {
      backgroundColor: theme.palette.blue[20],
      borderColor: theme.palette.blue[20],
    },
    [RoomStatusName.TURNOVER]: {
      backgroundColor: '#F7CE6B',
      borderColor: '#F7CE6B',
    },
    [RoomStatusName.IN_CASE]: {
      backgroundColor: '#8CB860',
      borderColor: '#8CB860',
    },
  }
}
