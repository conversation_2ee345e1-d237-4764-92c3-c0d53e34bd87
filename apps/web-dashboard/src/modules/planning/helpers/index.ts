import { DateTime } from 'luxon'

import {
  CaseLabelUpsertInput,
  CaseStaffPlanUpsertInput,
  InputMaybe,
} from 'src/__generated__/globalTypes'
import { CaseStaffPlanFormValues } from 'src/modules/planning/components/CaseStaffPlanForm'
import { TableDataRow } from 'src/modules/planning/hooks/useCasePlanningSearch'

import { CaseStaffPlanRole } from '../../../utils/roles'

interface Archivable {
  archivedTime?: InputMaybe<string>
}

export interface CaseLabelInput extends CaseLabelUpsertInput {
  // We use the fieldId to merge old and new labels
  fieldId: string
}

const mergeGeneric = <T extends Archivable>(
  existingObjs: T[],
  newObjs: T[],
  compareFn: (existingObj: T, newObj: T) => boolean
): T[] => {
  const toArchive = existingObjs
    .filter(
      (existingObj) =>
        !newObjs.some(
          (newObj) =>
            !existingObj.archivedTime && compareFn(existingObj, newObj)
        ) && !existingObj.archivedTime
    )
    .map((toArchiveObj) => ({
      ...toArchiveObj,
      archivedTime: DateTime.now().toISO(),
    }))

  const toCreate = newObjs
    .filter(
      (newObj) =>
        !existingObjs.some(
          (existingObj) =>
            !existingObj.archivedTime && compareFn(existingObj, newObj)
        ) && !newObj.archivedTime
    )
    .map((toCreateObj) => ({
      ...toCreateObj,
      archivedTime: undefined,
    }))

  return [...toArchive, ...toCreate]
}

const generateCaseStaffPlanUpsertInput = ({
  ids,
  role,
  caseIds,
  siteId,
  orgId,
}: {
  ids: string[]
  role: CaseStaffPlanRole
  caseIds: string[]
  siteId: string
  orgId: string
}): CaseStaffPlanUpsertInput[] => {
  return caseIds.flatMap((caseId) =>
    ids.map((staffId) => {
      return {
        staffId,
        caseId,
        role,
        id: crypto.randomUUID(),
        siteId,
        orgId,
      }
    })
  )
}

export const generateCaseStaffPlanUpsertInputData = ({
  caseIds,
  caseStaffPlans,
  values,
  siteId,
  orgId,
}: {
  caseIds: string[]
  caseStaffPlans: TableDataRow['cases'][0]['caseStaffPlan']
  values: Partial<CaseStaffPlanFormValues>
  siteId: string
  orgId: string
}) => {
  const currentCspInput = caseStaffPlans
    .filter((csp) => caseIds.includes(csp.caseId))
    .map<CaseStaffPlanUpsertInput>((csp) => {
      return {
        id: csp.id,
        role: csp.role,
        staffId: csp.staff!.id,
        caseId: csp.caseId,
        archivedTime: csp.archivedTime?.toISO(),
        siteId,
        orgId,
      }
    })

  const newCspInput = Object.values(CaseStaffPlanRole).flatMap((role) =>
    values[role] !== undefined
      ? generateCaseStaffPlanUpsertInput({
          ids: values[role] ?? [],
          role,
          caseIds,
          siteId,
          orgId,
        })
      : []
  )

  return mergeGeneric(
    currentCspInput,
    newCspInput,
    (existingObj, newObj) =>
      newObj.staffId === existingObj.staffId &&
      newObj.caseId === existingObj.caseId &&
      newObj.role === existingObj.role
  )
}

export const generateCaseLabelsUpsertInput = (
  currentLabels: CaseLabelInput[],
  formLabels: CaseLabelInput[]
): CaseLabelUpsertInput[] => {
  return mergeGeneric(
    currentLabels,
    formLabels,
    (existingObj, newObj) =>
      newObj.fieldId === existingObj.fieldId &&
      newObj.caseId === existingObj.caseId &&
      newObj.optionId === existingObj.optionId
  ).map((cli) => ({
    archivedTime: cli.archivedTime,
    caseId: cli.caseId,
    id: cli.id,
    optionId: cli.optionId,
  }))
}
