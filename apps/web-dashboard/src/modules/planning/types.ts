export enum CaseFlagType {
  MALIGNANT_HYPERTHERMIA = 'MALIGNANT_HYPERTHERMIA',
  BLOOD_PRODUCTS = 'BLOOD_PRODUCTS',
  LATEX_ALLERGY = 'LATEX_ALLERGY',
}

export const CaseFlagOrdering: CaseFlagType[] = [
  CaseFlagType.MALIGNANT_HYPERTHERMIA,
  CaseFlagType.BLOOD_PRODUCTS,
  CaseFlagType.LATEX_ALLERGY,
]

export const CASE_FLAGS_LABELS: { [key in CaseFlagType]?: string } = {
  [CaseFlagType.MALIGNANT_HYPERTHERMIA]: 'Risk of Malignant Hyperthermia',
  [CaseFlagType.BLOOD_PRODUCTS]: 'Blood Products Needed',
  [CaseFlagType.LATEX_ALLERGY]: 'Latex Allergy',
}

export interface CaseFlag {
  active: boolean
  id?: string
  type: CaseFlagType
}

export interface CaseNote {
  id: string
  note: string
}
