import { useCallback, useMemo, useState } from 'react'

import { useQuery } from '@apollo/client'
import { isEqual, uniqBy } from 'lodash'
import { DateTime } from 'luxon'

import { useQueryParamState } from '@apella/hooks'
import { CaseStatusName, OrderBy } from 'src/__generated__/globalTypes'
import { toIds } from 'src/components/Filters/FilterWithCount'
import {
  dateTimeToUrlFriendlyDate,
  orderByToUrlFriendly,
  urlFriendlyOrderStrToOrderBy,
} from 'src/components/Filters/urls'
import { useTimezone } from 'src/Contexts'
import {
  GetCasePlanningRoomData,
  GetCasePlanningRoomDataVariables,
} from 'src/modules/planning/queries/__generated__'
import { GET_CASE_PLANNING_ROOM_DATA } from 'src/modules/planning/queries/queries'
import { urlFriendlyDateToDateTime } from 'src/utils/urlFriendlyDate'

import { CaseFlag, CaseFlagType } from '../types'

export const DATE = 'date'
export const SITE_IDS = 'siteIds'
export const ROOM_IDS = 'roomIds'
const ORDER_BY = 'orderBy'

export interface TableDataRow {
  cases: {
    scheduledCaseId: string
    primarySurgeons: { id: string; firstName: string; lastName: string }[]
    startTime: DateTime
    endTime?: DateTime
    primaryCaseProcedures?: string[]
    statusName: CaseStatusName
    scheduledStartTime?: DateTime
    scheduledEndTime?: DateTime
    notePlan?: {
      id: string
      note?: string
    }
    caseStaffPlan: {
      staff?: { id: string; firstName: string; lastName: string }
      role: string
      id: string
      caseId: string
      archivedTime?: DateTime
    }[]
    caseFlags?: CaseFlag[]
  }[]
  room: {
    id: string
    name: string
  }
  site: {
    id: string
    name: string
  }
  totalPlannedStaff: number
}

export const useCasePlanningSearch = ({
  DEFAULT_DATE,
  SORTABLE_FIELDS,
  DEFAULT_STATE,
  DEFAULT_ORDER_BY_STATE,
}: {
  DEFAULT_DATE: DateTime
  SORTABLE_FIELDS: string[]
  DEFAULT_STATE: Partial<GetCasePlanningRoomDataVariables>
  DEFAULT_ORDER_BY_STATE: Pick<
    GetCasePlanningRoomDataVariables,
    typeof ORDER_BY
  >
}) => {
  const { timezone: locale } = useTimezone()

  const [roomIds, setRoomIds] = useQueryParamState<string[] | undefined>(
    ROOM_IDS,
    DEFAULT_STATE.roomIds || undefined
  )

  const [orderBy, setOrderBy] = useQueryParamState<string | undefined>(
    ORDER_BY,
    orderByToUrlFriendly(DEFAULT_STATE.orderBy!)
  )

  const [siteIds, setSiteIds] = useQueryParamState<string[] | undefined>(
    SITE_IDS,
    DEFAULT_STATE.siteIds || undefined
  )

  const [date, setDate] = useQueryParamState<string>(
    DATE,
    dateTimeToUrlFriendlyDate(DEFAULT_DATE)
  )
  const stateActionMapper = useMemo(
    () => ({
      date: (time: string) =>
        setDate(dateTimeToUrlFriendlyDate(DateTime.fromISO(time))),
      siteIds: setSiteIds,
      roomIds: setRoomIds,
      orderBy: (val: OrderBy[]) => setOrderBy(orderByToUrlFriendly(val)),
      first: undefined,
      after: undefined,
    }),
    [setDate, setSiteIds, setRoomIds, setOrderBy]
  )

  const [state, setState] = useState<GetCasePlanningRoomDataVariables>({
    ...DEFAULT_STATE,
    maxStartTime:
      urlFriendlyDateToDateTime(date)
        .setZone(locale, { keepLocalTime: true })
        .endOf('day')
        .toISO() ||
      DEFAULT_DATE.setZone(locale, { keepLocalTime: true })
        .endOf('day')
        .toISO(),
    minEndTime:
      urlFriendlyDateToDateTime(date)
        .setZone(locale, { keepLocalTime: true })
        .startOf('day')
        .toISO() ||
      DEFAULT_DATE.setZone(locale, { keepLocalTime: true })
        .startOf('day')
        .toISO(),
    roomIds: roomIds,
    siteIds: siteIds,
    orderBy:
      urlFriendlyOrderStrToOrderBy(orderBy, SORTABLE_FIELDS) ||
      DEFAULT_ORDER_BY_STATE.orderBy,
  })

  const {
    loading: dataLoading,
    data,
    refetch,
  } = useQuery<GetCasePlanningRoomData, GetCasePlanningRoomDataVariables>(
    GET_CASE_PLANNING_ROOM_DATA,
    {
      variables: state,
    }
  )

  // This goes through all the state changes:
  // - Update QueryParams
  // - Update the state
  const updateTableFilterSortState = useCallback(
    (stateDiff: Partial<GetCasePlanningRoomDataVariables>) => {
      setState((prevState: GetCasePlanningRoomDataVariables) => {
        const newState = {
          ...prevState,
          ...stateDiff,
        }

        for (const key of Object.keys(stateActionMapper).filter(
          (k) => k !== 'date'
        )) {
          const keyOf = key as keyof Omit<typeof stateActionMapper, 'date'>
          const setter = stateActionMapper[keyOf]
          const newVal = newState[keyOf]

          setter && setter(newVal as any)
        }

        return newState
      })
    },
    [stateActionMapper, setState]
  )

  const onSiteIdsChange = useCallback(
    (siteIds?: string | string[]) => {
      updateTableFilterSortState({
        siteIds: toIds(siteIds),
      })
    },
    [updateTableFilterSortState]
  )
  const onRoomIdsChange = useCallback(
    (roomIds?: string | string[]) => {
      updateTableFilterSortState({
        roomIds: toIds(roomIds),
      })
    },
    [updateTableFilterSortState]
  )

  const onSortChange = useCallback(
    (orderBy?: OrderBy[]) => {
      updateTableFilterSortState({
        orderBy,
      })
    },
    [updateTableFilterSortState]
  )

  const onDateChange = useCallback(
    (date: Date) => {
      const maxStartTime = DateTime.fromJSDate(date)
        .setZone(locale)
        .endOf('day')
        .toISO()
      const minEndTime = DateTime.fromJSDate(date)
        .setZone(locale)
        .startOf('day')
        .toISO()

      setState((prevState: GetCasePlanningRoomDataVariables) => {
        return {
          ...prevState,
          maxStartTime,
          minEndTime,
        }
      })

      setDate(dateTimeToUrlFriendlyDate(DateTime.fromJSDate(date)))
    },
    [locale, setDate]
  )

  const resetFilters = useCallback(() => {
    const date = DEFAULT_DATE.setZone(locale, { keepLocalTime: true })

    updateTableFilterSortState({
      ...DEFAULT_STATE,
      maxStartTime: date.endOf('day').toISO(),
      minEndTime: date.startOf('day').toISO(),
    })

    setDate(dateTimeToUrlFriendlyDate(date))
  }, [DEFAULT_DATE, locale, updateTableFilterSortState, DEFAULT_STATE, setDate])

  const filterKeysToCompare: (keyof GetCasePlanningRoomDataVariables)[] =
    useMemo(
      () => [
        'siteIds',
        'roomIds',
        'minEndTime',
        'maxStartTime',
        'after',
        'first',
      ],
      []
    )

  const showResetFilters = useMemo(() => {
    const date = DEFAULT_DATE.setZone(locale, { keepLocalTime: true })

    const defaultState = {
      ...DEFAULT_STATE,
      maxStartTime: date.endOf('day').toISO(),
      minEndTime: date.startOf('day').toISO(),
    }

    return filterKeysToCompare.some((k) => !isEqual(defaultState[k], state[k]))
  }, [DEFAULT_DATE, DEFAULT_STATE, locale, state, filterKeysToCompare])

  const rowKeySelector = useCallback(
    (rowData: TableDataRow) => rowData.room.id,
    []
  )

  const tableData = useMemo(() => {
    return (data?.sites?.edges ?? []).flatMap(
      ({
        node: {
          id: siteId,
          name: siteName,
          rooms: { edges: roomEdge },
        },
      }) =>
        roomEdge.map<TableDataRow>(
          ({ node: { id: roomId, name: roomName, apellaCases } }) => {
            const roomCases = uniqBy(
              apellaCases.edges.map(({ node }) => node).filter(Boolean),
              (c) => c.case?.id
            )

            const totalPlannedStaff = uniqBy(
              roomCases.flatMap(
                (rc) => rc.case?.caseStaffPlan.edges.map((c) => c.node) ?? []
              ),
              (rs) => rs.staff?.id
            )

            return {
              room: { id: roomId, name: roomName },
              site: { id: siteId, name: siteName },
              cases: roomCases.map<TableDataRow['cases'][0]>((c) => ({
                scheduledCaseId: c.case?.id || '',
                statusName: c.status.name,
                startTime: DateTime.fromISO(c.startTime),
                endTime: c.endTime
                  ? DateTime.fromISO(c.case?.scheduledEndTime || c.endTime)
                  : undefined,
                scheduledStartTime: c.case?.scheduledStartTime
                  ? DateTime.fromISO(c.case?.scheduledStartTime)
                  : undefined,
                scheduledEndTime: c.case?.scheduledEndTime
                  ? DateTime.fromISO(c.case?.scheduledEndTime)
                  : undefined,
                primaryCaseProcedures: c.case?.primaryCaseProcedures.map(
                  (pcp) => pcp.procedure.name
                ),
                primarySurgeons: uniqBy(
                  c.case?.caseStaff,
                  (cs) => cs?.staff.externalStaffId
                )
                  .filter(Boolean)
                  .map((cs) => ({
                    id: cs.staff.id,
                    firstName: cs.staff.firstName,
                    lastName: cs.staff.lastName,
                  })),
                notePlan: c.case?.notePlan
                  ? {
                      id: c.case?.notePlan.id,
                      note: c.case?.notePlan.note || '',
                    }
                  : undefined,
                caseStaffPlan:
                  c.case?.caseStaffPlan.edges
                    .filter((csp) => !csp.node.archivedTime)
                    .map(({ node: csp }) => ({
                      id: csp.id,
                      role: csp.role ?? '',
                      caseId: csp.case?.id || '',
                      staff: csp.staff ? { ...csp.staff } : undefined,
                      archivedTime: csp.archivedTime
                        ? DateTime.fromISO(csp.archivedTime)
                        : undefined,
                    })) ?? [],
                caseFlags:
                  c.case?.caseFlags
                    .filter((cf) => cf.flagType as keyof typeof CaseFlagType)
                    .map((cf) => {
                      return {
                        id: cf.id,
                        type: CaseFlagType[
                          cf.flagType as keyof typeof CaseFlagType
                        ],
                        active: !cf.archivedTime,
                      }
                    }) || [],
              })),
              totalPlannedStaff: totalPlannedStaff.length,
            }
          }
        )
    )
  }, [data])

  const selectedDate: Date = useMemo(
    () => new Date(state.minEndTime),
    [state.minEndTime]
  )

  const pageCursors = data?.sites?.pageCursors

  return {
    selectedDate,
    pageCursors,
    tableData,
    rowKeySelector,
    resetFilters,
    dataLoading,
    onSiteIdsChange,
    onRoomIdsChange,
    onSortChange,
    onDateChange,
    state,
    updateTableFilterSortState,
    refetchData: refetch,
    showResetFilters,
  }
}
