import { useTheme } from '@emotion/react'

import { <PERSON>c<PERSON><PERSON>t, BloodDrop, DoNotTouch } from '@apella/component-library'

import { CaseFlagType } from '../types'

export const useCaseFlagIconMap = (
  size: string = 'xs'
): Record<CaseFlagType, React.JSX.Element> => {
  const theme = useTheme()
  return {
    [CaseFlagType.MALIGNANT_HYPERTHERMIA]: (
      <AcUnit
        size={size}
        color={theme.palette.blue[70]}
        data-testid={`caseflag-${CaseFlagType.MALIGNANT_HYPERTHERMIA}`}
      />
    ),
    [CaseFlagType.BLOOD_PRODUCTS]: (
      <BloodDrop
        size={size}
        data-testid={`caseflag-${CaseFlagType.BLOOD_PRODUCTS}`}
      />
    ),
    [CaseFlagType.LATEX_ALLERGY]: (
      <DoNotTouch
        size={size}
        color={theme.palette.blue[40]}
        data-testid={`caseflag-${CaseFlagType.LATEX_ALLERGY}`}
      />
    ),
  }
}
