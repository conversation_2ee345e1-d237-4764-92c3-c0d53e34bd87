import { useCallback, useMemo, useState } from 'react'

import { useTheme } from '@emotion/react'

import {
  ApellaDateTimeFormats,
  Button,
  Caps3,
  ExpandLess,
  ExpandMore,
  H5,
  P3,
  PH5,
  pxSpacing,
  remSpacing,
} from '@apella/component-library'
import {
  CaseStaffPlanForm,
  CaseStaffPlanFormValues,
} from 'src/modules/planning/components/CaseStaffPlanForm'
import { TableDataRow } from 'src/modules/planning/hooks/useCasePlanningSearch'
import { CaseStatusPill } from 'src/pages/Schedule/CaseDetails'

import { CaseStaffPlanRole } from '../../../utils/roles'
import { CaseFlag, CaseFlagType, CaseNote } from '../types'
import { CaseNotePlanForm } from './CaseNotePlanForm'
import { CASE_FLAG_MAP, CasePatientDataForm } from './CasePatientDataForm'

export const Case = ({
  case: c,
  defaultIsOpen,
  caseNoteDataDisabled,
  caseStaffDisabled,
  casePatientDataDisabled,
  onChangeCaseNoteField,
  onChangeCaseStaffField,
  onChangeCasePatientDataField,
}: {
  case: TableDataRow['cases'][number]
  defaultIsOpen?: boolean
  caseNoteDataDisabled?: boolean
  caseStaffDisabled?: boolean
  casePatientDataDisabled?: boolean
  onChangeCaseNoteField: (caseId: string, case_note: CaseNote) => void
  onChangeCaseStaffField: <T extends CaseStaffPlanRole>(
    caseId: string,
    role: T,
    values: CaseStaffPlanFormValues[T]
  ) => void
  onChangeCasePatientDataField: (
    caseId: string,
    case_flags: CASE_FLAG_MAP
  ) => void
}) => {
  const theme = useTheme()
  const [isOpen, setIsOpen] = useState(defaultIsOpen)

  const handleCaseNoteFieldChange = useCallback(
    (case_note: CaseNote) => {
      return onChangeCaseNoteField(c.scheduledCaseId, case_note)
    },
    [c.scheduledCaseId, onChangeCaseNoteField]
  )
  const handleCaseStaffFieldChange = useCallback(
    <T extends keyof CaseStaffPlanFormValues>(
      role: T,
      values: CaseStaffPlanFormValues[T]
    ) => {
      return onChangeCaseStaffField(c.scheduledCaseId, role, values)
    },
    [c.scheduledCaseId, onChangeCaseStaffField]
  )
  const handleCasePatientDataFieldChange = useCallback(
    (caseFlags: CASE_FLAG_MAP) => {
      return onChangeCasePatientDataField(c.scheduledCaseId, caseFlags)
    },
    [c.scheduledCaseId, onChangeCasePatientDataField]
  )

  const initialCaseStaffValues: CaseStaffPlanFormValues = Object.values(
    CaseStaffPlanRole
  ).reduce(
    (accum, curr) => ({
      ...accum,
      [curr]: c.caseStaffPlan
        .filter((csp) => csp.role === curr.toString())
        .map((csp) => csp.staff?.id)
        .filter(Boolean),
    }),
    {} as CaseStaffPlanFormValues
  )

  const initialCaseNote: CaseNote = useMemo(() => {
    return c.notePlan
      ? {
          id: c.notePlan?.id,
          note: c.notePlan?.note || '',
        }
      : {
          id: crypto.randomUUID(),
          note: '',
        }
  }, [c.notePlan])

  const defaultCaseFlag = (caseFlagType: CaseFlagType): CaseFlag => ({
    type: caseFlagType,
    active: false,
    id: crypto.randomUUID(),
  })

  const mergedCaseFlags: CASE_FLAG_MAP = useMemo(() => {
    const case_flags_provided = new Map(
      c.caseFlags?.map((cf) => [cf.type, cf]) || []
    )
    return {
      [CaseFlagType.MALIGNANT_HYPERTHERMIA]:
        case_flags_provided.get(CaseFlagType.MALIGNANT_HYPERTHERMIA) ||
        defaultCaseFlag(CaseFlagType.MALIGNANT_HYPERTHERMIA),
      [CaseFlagType.BLOOD_PRODUCTS]:
        case_flags_provided.get(CaseFlagType.BLOOD_PRODUCTS) ||
        defaultCaseFlag(CaseFlagType.BLOOD_PRODUCTS),
      [CaseFlagType.LATEX_ALLERGY]:
        case_flags_provided.get(CaseFlagType.LATEX_ALLERGY) ||
        defaultCaseFlag(CaseFlagType.LATEX_ALLERGY),
    }
  }, [c.caseFlags])

  const initialCaseFlagValues = {
    ...mergedCaseFlags,
  }

  return (
    <div
      css={{
        display: 'grid',
        justifyItems: 'start',
        alignContent: 'start',
        padding: remSpacing.medium,
        background: theme.palette.background.primary,
        border: `solid 1px ${theme.palette.gray[30]}`,
        borderRadius: pxSpacing.xsmall,
        width: '100%',
        height: '100%',
        gap: remSpacing.medium,
      }}
    >
      <div
        css={{
          display: 'grid',
          width: '100%',
          alignItems: 'baseline',
        }}
      >
        <div
          css={{
            display: 'grid',
            gridTemplateColumns: 'auto min-content',
            justifyContent: 'space-between',
            width: '100%',
            alignItems: 'start',
            columnGap: remSpacing.medium,
          }}
        >
          <div
            css={{
              display: 'grid',
              gridAutoFlow: 'column',
              gridTemplateColumns: 'minmax(0, 1fr) max-content',
              justifyContent: 'space-between',
              width: '100%',
              alignItems: 'baseline',
              columnGap: remSpacing.medium,
            }}
          >
            {!!c.primarySurgeons.length && (
              <PH5 as="p">
                {c.primarySurgeons
                  .map((ps) => `${ps.lastName}, ${ps.firstName}`)
                  .join('; ')}
              </PH5>
            )}

            <CaseStatusPill status={c.statusName} />
          </div>
          <Button
            buttonType="icon"
            size="sm"
            color="black"
            appearance="link"
            label="Expand Case"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <ExpandLess size="sm" /> : <ExpandMore size="sm" />}
          </Button>
        </div>
        {!!c.primaryCaseProcedures?.length && (
          <P3 css={{ textTransform: 'capitalize' }}>
            {c.primaryCaseProcedures?.join('; ').toLocaleLowerCase()}
          </P3>
        )}
        <Caps3 as="p">
          EST: {c.startTime.toLocaleString(ApellaDateTimeFormats.TIME)} -{' '}
          {c.endTime?.toLocaleString(ApellaDateTimeFormats.TIME) ?? ''}
        </Caps3>
        {c.scheduledStartTime && (
          <Caps3 as="p">
            SCH:{' '}
            {c.scheduledStartTime.toLocaleString(ApellaDateTimeFormats.TIME)} -{' '}
            {c.scheduledEndTime?.toLocaleString(ApellaDateTimeFormats.TIME) ??
              ''}
          </Caps3>
        )}
      </div>
      {isOpen && (
        <div css={{ display: 'grid', gap: remSpacing.xsmall }}>
          <H5>Case Staff</H5>
          <CaseStaffPlanForm
            caseIds={[c.scheduledCaseId]}
            direction="row"
            onChangeField={handleCaseStaffFieldChange}
            disabled={caseStaffDisabled}
            initialValues={initialCaseStaffValues}
          />
          <H5>Additional Information</H5>
          <CasePatientDataForm
            initialValues={initialCaseFlagValues}
            disabled={casePatientDataDisabled}
            onChangeField={handleCasePatientDataFieldChange}
          />
          <CaseNotePlanForm
            initialValues={initialCaseNote}
            disabled={caseNoteDataDisabled}
            onChangeField={handleCaseNoteFieldChange}
          />
        </div>
      )}
    </div>
  )
}
