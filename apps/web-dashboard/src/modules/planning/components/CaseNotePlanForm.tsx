import { useMemo, useState } from 'react'

import { debounce } from 'lodash'

import { InputText } from '@apella/component-library'

import { CaseNote } from '../types'

export const CaseNotePlanForm = ({
  initialValues,
  onChangeField,
  disabled,
}: {
  initialValues: CaseNote
  onChangeField: (case_note: CaseNote) => void
  disabled?: boolean
}) => {
  const [note, setNote] = useState(initialValues.note)
  const debouncedChangeHandler = useMemo(
    () =>
      debounce((note) => {
        onChangeField({ id: initialValues.id, note: note })
      }, 1000),
    [onChangeField, initialValues]
  )

  return (
    <div>
      <InputText
        name="note"
        label="Notes"
        placeholder="Some additional notes that may be needed for coordination."
        multiline
        disabled={disabled}
        value={note}
        rows={3}
        onChange={(e) => {
          setNote(e.target.value)
          debouncedChangeHandler(e.target.value)
        }}
      />
    </div>
  )
}
