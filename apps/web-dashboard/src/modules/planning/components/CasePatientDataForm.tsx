import { Form, Formik } from 'formik'

import { FlexContainer, Input, P2, remSpacing } from '@apella/component-library'

import { useCaseFlagIconMap } from '../hooks/useCaseFlagIconMap'
import {
  CASE_FLAGS_LABELS,
  CaseFlag,
  CaseFlagOrdering,
  CaseFlagType,
} from '../types'

export type CASE_FLAG_MAP = { [key in CaseFlagType]?: CaseFlag }

export const CasePatientDataForm = ({
  initialValues,
  onChangeField,
  disabled,
}: {
  initialValues: CASE_FLAG_MAP
  onChangeField: (caseFlags: CASE_FLAG_MAP) => void
  disabled?: boolean
}) => {
  const getUpdatedCaseFlags = (
    caseFlags: CASE_FLAG_MAP,
    type: CaseFlagType
  ): CASE_FLAG_MAP => {
    return {
      ...caseFlags,
      [type]: {
        ...caseFlags[type],
        active: !caseFlags[type]?.active,
      },
    }
  }
  const caseFlagIconMap = useCaseFlagIconMap()

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={() => Promise.resolve()}
      enableReinitialize
    >
      {({ values }) => (
        <Form>
          {CaseFlagOrdering.map((type) => values[type])
            .filter(Boolean)
            .map((cf) => {
              return (
                <div key={cf.type}>
                  <Input.Checkbox
                    id={cf.id}
                    name={`${cf.type} Checkbox`}
                    ariaLabel={`${CASE_FLAGS_LABELS[cf.type]} Checkbox`}
                    value={cf.active}
                    disabled={disabled}
                    onChange={() =>
                      onChangeField(getUpdatedCaseFlags(values, cf.type))
                    }
                    as={FlexContainer}
                    asProps={{ alignItems: 'center', gap: remSpacing.xsmall }}
                  >
                    <FlexContainer
                      alignItems="baseline"
                      gap={remSpacing.xsmall}
                    >
                      {caseFlagIconMap[cf.type]}
                      <P2>{CASE_FLAGS_LABELS[cf.type]}</P2>
                    </FlexContainer>
                  </Input.Checkbox>
                </div>
              )
            })}
        </Form>
      )}
    </Formik>
  )
}
