import { useCallback, useMemo } from 'react'
import { To } from 'react-router'

import { useQuery } from '@apollo/client'
import { Form, Formik, FormikConfig } from 'formik'

import {
  Button,
  ButtonLink,
  FlexContainer,
  H6,
  MultiSelect,
  Option,
  remSpacing,
} from '@apella/component-library'
import { useCurrentUser } from 'src/modules/user/hooks'
import {
  GetStaff,
  GetStaffVariables,
} from 'src/pages/Notifications/__generated__'
import { GET_STAFF } from 'src/pages/Notifications/queries'

import { CaseStaffPlanRole, toStaffRoleDisplayName } from '../../../utils/roles'

export type CaseStaffPlanFormValues = { [key in CaseStaffPlanRole]: string[] }

export interface CaseStaffPlanFormProps {
  cancelDestination?: To
  caseIds: string[]
  direction?: 'row' | 'column'
}

export const CaseStaffPlanForm = (
  props: CaseStaffPlanFormProps & {
    disabled?: boolean
    initialValues?: CaseStaffPlanFormValues
    onSubmit?: (
      args: CaseStaffPlanFormProps
    ) => FormikConfig<CaseStaffPlanFormValues>['onSubmit']
    onReset?: (
      args: CaseStaffPlanFormProps
    ) => FormikConfig<CaseStaffPlanFormValues>['onReset']
    onChangeField?: <T extends keyof CaseStaffPlanFormValues>(
      role: T,
      values: CaseStaffPlanFormValues[T]
    ) => void
  }
) => {
  const { currentOrganization, loading: isUserLoading } = useCurrentUser()

  const { onSubmit, onReset, onChangeField, initialValues, disabled, ...rest } =
    props

  const { data: staffData } = useQuery<GetStaff, GetStaffVariables>(GET_STAFF, {
    variables: { orgId: currentOrganization?.node.id || '' },
    skip: !currentOrganization?.node.id || isUserLoading,
  })

  const staffOptions = useMemo(
    () =>
      staffData?.staff.edges.map(({ node: { id, name } }) => (
        <Option key={id} value={id} label={name} />
      )),
    [staffData]
  )

  const handleOnSubmit = useCallback(
    (args: CaseStaffPlanFormProps) => {
      if (onSubmit) {
        return onSubmit(args)
      }
      return () => Promise.resolve()
    },
    [onSubmit]
  )

  const fieldWidth = props.direction === 'row' ? undefined : '100%'

  const defaultValues = Object.values(CaseStaffPlanRole).reduce(
    (accum, curr) => ({ ...accum, [curr]: [] }),
    {}
  )

  return (
    <Formik
      initialValues={
        initialValues ?? (defaultValues as CaseStaffPlanFormValues)
      }
      onSubmit={handleOnSubmit(rest)}
      onReset={onReset?.(rest)}
      enableReinitialize
    >
      {({ values, setFieldValue }) => {
        const onFieldChange = (role: CaseStaffPlanRole) => {
          return (ids?: string[]) => {
            if (onChangeField) {
              onChangeField(role, ids ?? [])
            }
            setFieldValue(role, ids)
          }
        }

        return (
          <Form css={{ width: '100%' }}>
            <FlexContainer
              css={{
                gap: remSpacing.medium,
                width: '100%',
              }}
              direction={props.direction || 'column'}
              wrap="wrap"
            >
              {Object.values(CaseStaffPlanRole).map((role) => {
                return (
                  <div
                    css={{
                      display: 'grid',
                      justifyItems: 'start',
                      alignContent: 'start',
                      rowGap: remSpacing.xsmall,
                      width: fieldWidth,
                    }}
                    key={role}
                  >
                    <H6>{toStaffRoleDisplayName(role)}</H6>
                    <MultiSelect
                      label={toStaffRoleDisplayName(role)}
                      name={role}
                      search
                      css={{ width: fieldWidth }}
                      value={values[role] ?? []}
                      onChange={onFieldChange(role)}
                      readOnly={disabled}
                    >
                      {staffOptions}
                    </MultiSelect>
                  </div>
                )
              })}
              <div
                css={{
                  display: 'grid',
                  gap: remSpacing.xsmall,
                  gridAutoFlow: 'column',
                  gridAutoColumns: 'max-content',
                  justifyContent: 'end',
                }}
              >
                {props.cancelDestination && (
                  <ButtonLink appearance="link" to={props.cancelDestination}>
                    Cancel
                  </ButtonLink>
                )}
                {!!onSubmit && (
                  <Button type="reset" color="alternate" disabled={disabled}>
                    Clear
                  </Button>
                )}
                {!!onSubmit && (
                  <Button type="submit" disabled={disabled}>
                    Add staff to cases
                  </Button>
                )}
              </div>
            </FlexContainer>
          </Form>
        )
      }}
    </Formik>
  )
}
