import { gql } from '@apollo/client'

import { PAGE_CURSORS_FRAGMENT } from 'src/utils/graphqlFragments'

export const GET_CASE_PLANNING_ROOM_DATA = gql`
  ${PAGE_CURSORS_FRAGMENT}
  query GetCasePlanningRoomData(
    $siteIds: [String!]
    $roomIds: [String!]
    $minEndTime: DateTime!
    $maxStartTime: DateTime!
    $after: String
    $first: Int
    $orderBy: [OrderBy!]
  ) {
    sites(siteIds: $siteIds, after: $after, first: $first, orderBy: $orderBy) {
      pageCursors {
        ...PageCursorsFragment
      }
      edges {
        node {
          id
          name
          rooms(roomIds: $roomIds, orderBy: $orderBy) {
            edges {
              node {
                id
                name
                apellaCases(
                  query: {
                    minEndTime: $minEndTime
                    maxStartTime: $maxStartTime
                  }
                ) {
                  edges {
                    node {
                      id
                      startTime
                      endTime
                      status(useObservations: true) {
                        name
                        since
                      }
                      case {
                        id
                        primaryCaseProcedures {
                          procedure {
                            id
                            name
                          }
                        }
                        scheduledStartTime
                        scheduledEndTime
                        caseStaff(onlyPrimarySurgeons: true) {
                          role
                          staff {
                            id
                            externalStaffId
                            lastName
                            firstName
                          }
                        }
                        notePlan {
                          id
                          note
                        }
                        caseStaffPlan(query: { includeArchived: false }) {
                          edges {
                            node {
                              id
                              archivedTime
                              role
                              case {
                                id
                              }
                              staff {
                                id
                                firstName
                                lastName
                                name
                              }
                            }
                          }
                        }
                        caseFlags(includeArchived: true) {
                          id
                          flagType
                          archivedTime
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`

export const UPSERT_CASE_NOTE_PLAN = gql`
  mutation UpsertCaseNotePlan($input: CaseNotePlanUpsertInput!) {
    caseNotePlanUpsert(input: $input) {
      success
      caseNotePlan {
        id
        note
        case {
          id
        }
      }
    }
  }
`

export const UPSERT_CASE_STAFF_PLAN = gql`
  mutation UpsertCaseStaffPlan($input: [CaseStaffPlanUpsertInput!]!) {
    caseStaffPlanUpsert(input: $input) {
      success
      caseStaffPlans {
        edges {
          node {
            id
            role
            archivedTime
            case {
              id
            }
            staff {
              id
              firstName
              lastName
              name
            }
          }
        }
      }
    }
  }
`

export const SET_CASE_PATIENT_DATA = gql`
  mutation SetCasePatientData($caseFlags: [CaseFlagUpsertInput!]!) {
    caseFlagUpsert(input: $caseFlags) {
      success
    }
  }
`
