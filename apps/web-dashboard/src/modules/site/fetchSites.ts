import { ApolloClient, NormalizedCacheObject } from '@apollo/client'

import { GetSiteOptionsFilter } from './__generated__'
import { GET_SITE_OPTIONS_FILTER } from './queries'

export const fetchSites = async (
  client: ApolloClient<NormalizedCacheObject>
) => {
  const { data: siteOptions } = await client.query<GetSiteOptionsFilter>({
    query: GET_SITE_OPTIONS_FILTER,
  })
  return {
    sites: siteOptions?.sites?.edges || [],
  }
}
