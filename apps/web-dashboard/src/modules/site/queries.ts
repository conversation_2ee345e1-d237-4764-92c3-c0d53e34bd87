import { gql } from '@apollo/client'

export const GET_SITE_OPTIONS_FILTER = gql`
  query GetSiteOptionsFilter {
    sites(orderBy: [{ sort: "id", direction: ASC }]) {
      edges {
        node {
          id
          name
          timezone
          rooms(orderBy: [{ sort: "id", direction: ASC }]) {
            edges {
              node {
                id
                site {
                  id
                  name
                }
                name
                tags {
                  id
                  name
                }
              }
            }
          }
        }
      }
    }
  }
`
