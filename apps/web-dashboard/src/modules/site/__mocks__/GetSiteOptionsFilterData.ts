import { GetSiteOptionsFilter } from 'src/modules/site/__generated__'

export const GetSiteOptionsFilterData: { data: GetSiteOptionsFilter } = {
  data: {
    sites: {
      edges: [
        {
          __typename: 'SiteEdge',
          node: {
            id: 'site-01',
            name: 'Site 01',
            timezone: 'America/Los_Angeles',
            rooms: {
              __typename: 'RoomConnection',
              edges: [
                {
                  node: {
                    id: 'site-01-01',
                    name: 'OR 01',
                    __typename: 'Room',
                    site: {
                      __typename: 'Site',
                      id: 'site-01',
                      name: 'Site 01',
                    },
                    tags: [],
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'site-01-02',
                    name: 'OR 02',
                    __typename: 'Room',
                    site: {
                      __typename: 'Site',
                      id: 'site-01',
                      name: 'Site 01',
                    },
                    tags: [],
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'site-01-03',
                    name: 'OR 03',
                    __typename: 'Room',
                    site: {
                      __typename: 'Site',
                      id: 'site-01',
                      name: 'Site 01',
                    },
                    tags: [],
                  },
                  __typename: 'RoomEdge',
                },
              ],
            },
            __typename: 'Site',
          },
        },
        {
          __typename: 'SiteEdge',
          node: {
            id: 'site-02',
            name: 'Site 02',
            timezone: 'America/Los_Angeles',
            rooms: {
              __typename: 'RoomConnection',
              edges: [
                {
                  node: {
                    id: 'site-02-01',
                    name: 'OR 01',
                    __typename: 'Room',
                    site: {
                      __typename: 'Site',
                      id: 'site-02',
                      name: 'Site 02',
                    },
                    tags: [],
                  },
                  __typename: 'RoomEdge',
                },
                {
                  node: {
                    id: 'site-02-02',
                    name: 'OR 02',
                    __typename: 'Room',
                    site: {
                      __typename: 'Site',
                      id: 'site-02',
                      name: 'Site 02',
                    },
                    tags: [],
                  },
                  __typename: 'RoomEdge',
                },
              ],
            },
            __typename: 'Site',
          },
        },
      ],
      __typename: 'SiteConnection',
    },
  },
}
