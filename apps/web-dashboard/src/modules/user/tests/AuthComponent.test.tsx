import { ComponentProps } from 'react'

import { ThemeProvider } from '@emotion/react'

import { faker } from '@faker-js/faker'
import { render } from '@testing-library/react'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { Settings } from 'luxon'

import { theme } from '@apella/component-library'
import {
  aUserUiPermissions,
  anOrganization,
  anOrganizationConnection,
  anOrganizationEdge,
} from 'src/__generated__/generated-mocks'
import { UserUiPermissions } from 'src/__generated__/globalTypes'
import { AuthComponent } from 'src/modules/user/components/AuthComponent'
import { useCurrentUser } from 'src/modules/user/hooks'
import { Permission } from 'src/router/types'

const testRender = (props: ComponentProps<typeof AuthComponent>) =>
  render(
    <ThemeProvider theme={theme}>
      <AuthComponent {...props}>
        <p>Some component</p>
      </AuthComponent>
    </ThemeProvider>
  )

vi.mock('launchdarkly-react-client-sdk')
const mockedUseFlags = vi.mocked(useFlags)

vi.mock('src/modules/user/hooks')
const mockedUseCurrentUser = vi.mocked(useCurrentUser)

// For the purposes of these tests these can be any boolean flag and permission
const permission: Permission = 'dashboardCreateMeasurementPeriodsEnabled'
const flag: ComponentProps<typeof AuthComponent>['flag'] = 'mockTestingFlag'

export const aPermissions = (overrides?: Partial<UserUiPermissions>) => {
  return {
    ...aUserUiPermissions(overrides),
    patientReadEnabled: faker.datatype.boolean(),
    dashboardTurnoversEnabled: faker.datatype.boolean(),
    availableTimesEmailEnabled: faker.datatype.boolean(),
    caseNotePlanReadEnabled: faker.datatype.boolean(),
  }
}

describe('AuthComponent', () => {
  beforeEach(() => {
    Settings.defaultZone = 'America/Los_Angeles'
  })

  mockedUseCurrentUser.mockReturnValue({
    loading: false,
    permissions: undefined,
    currentOrganization: undefined,
    userOrganizations: undefined,
    error: undefined,
    userId: undefined,
    email: undefined,
    isApellaEmployee: false,
  })

  it('renders when the no auths are specified', () => {
    const { asFragment } = testRender({})

    expect(asFragment()).toMatchSnapshot()
  })

  describe('when checking permission', () => {
    it('renders when user has permission', () => {
      mockedUseCurrentUser.mockReturnValue({
        loading: false,
        permissions: aPermissions({ [permission]: true }),
        currentOrganization: anOrganizationEdge(),
        userOrganizations: anOrganizationConnection().edges,
        error: undefined,
        userId: '',
        email: '',
        isApellaEmployee: false,
      })
      const { asFragment } = testRender({ permission })

      expect(asFragment()).toMatchSnapshot()
    })

    it('does not render when the user does not have permission', () => {
      mockedUseCurrentUser.mockReturnValue({
        loading: false,
        permissions: aPermissions({ [permission]: false }),
        currentOrganization: anOrganizationEdge(),
        userOrganizations: anOrganizationConnection().edges,
        error: undefined,
        userId: '',
        email: '',
        isApellaEmployee: false,
      })
      const { asFragment } = testRender({ permission })

      expect(asFragment()).toMatchSnapshot()
    })
  })

  describe('when checking apella employee restrictions', () => {
    it('renders when org matches whitelisted org', () => {
      mockedUseCurrentUser.mockReturnValue({
        loading: false,
        permissions: aPermissions(),
        currentOrganization: anOrganizationEdge(),
        userOrganizations: anOrganizationConnection().edges,
        error: undefined,
        userId: '',
        email: '',
        isApellaEmployee: true,
      })
      const { asFragment } = testRender({ hideInProductionForApella: true })

      expect(asFragment()).toMatchSnapshot()
    })

    it('does not render when org does not match whitelisted org', () => {
      process.env.APELLA_ENV = 'testing'
      mockedUseCurrentUser.mockReturnValue({
        loading: false,
        permissions: aPermissions(),
        currentOrganization: anOrganizationEdge({
          node: anOrganization({ id: 'foo' }),
        }),
        userOrganizations: anOrganizationConnection().edges,
        error: undefined,
        userId: '',
        email: '',
        isApellaEmployee: true,
      })
      const { asFragment } = testRender({ hideInProductionForApella: true })

      expect(asFragment()).toMatchSnapshot()

      process.env.APELLA_ENV = ''
    })

    it('renders when restricted and org does not match whitelisted org and employee is not apella employee', () => {
      process.env.APELLA_ENV = 'testing'
      mockedUseCurrentUser.mockReturnValue({
        loading: false,
        permissions: aPermissions(),
        currentOrganization: anOrganizationEdge({
          node: anOrganization({ id: 'foo' }),
        }),
        userOrganizations: anOrganizationConnection().edges,
        error: undefined,
        userId: '',
        email: '',
        isApellaEmployee: false,
      })
      const { asFragment } = testRender({ hideInProductionForApella: true })

      expect(asFragment()).toMatchSnapshot()

      process.env.APELLA_ENV = ''
    })
  })

  describe('when checking feature flag', () => {
    it('does not render when flag is specified and flag is disabled', () => {
      mockedUseFlags.mockReturnValue({ flag: false })

      const { asFragment } = testRender({ flag })

      expect(asFragment()).toMatchSnapshot()
    })

    it('does render when flag is specified and flag is enabled', () => {
      mockedUseFlags.mockReturnValue({ flag: true })

      const { asFragment } = testRender({ flag })

      expect(asFragment()).toMatchSnapshot()
    })
  })
})
