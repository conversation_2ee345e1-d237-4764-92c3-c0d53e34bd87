// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`AuthComponent > renders when the no auths are specified 1`] = `
<DocumentFragment>
  <p>
    Some component
  </p>
</DocumentFragment>
`;

exports[`AuthComponent > when checking apella employee restrictions > does not render when org does not match whitelisted org 1`] = `<DocumentFragment />`;

exports[`AuthComponent > when checking apella employee restrictions > renders when org matches whitelisted org 1`] = `
<DocumentFragment>
  <p>
    Some component
  </p>
</DocumentFragment>
`;

exports[`AuthComponent > when checking apella employee restrictions > renders when restricted and org does not match whitelisted org and employee is not apella employee 1`] = `
<DocumentFragment>
  <p>
    Some component
  </p>
</DocumentFragment>
`;

exports[`AuthComponent > when checking feature flag > does not render when flag is specified and flag is disabled 1`] = `<DocumentFragment />`;

exports[`AuthComponent > when checking feature flag > does render when flag is specified and flag is enabled 1`] = `<DocumentFragment />`;

exports[`AuthComponent > when checking permission > does not render when the user does not have permission 1`] = `<DocumentFragment />`;

exports[`AuthComponent > when checking permission > renders when user has permission 1`] = `
<DocumentFragment>
  <p>
    Some component
  </p>
</DocumentFragment>
`;
