import { GetCurrentUser } from './__generated__'

export type GqlUiPermissions = NonNullable<
  GetCurrentUser['me']
>['uiPermissions']

export type GqlPermissions = NonNullable<GetCurrentUser['me']>['permissions']

export type Permissions = GqlUiPermissions & {
  contactInformationReadEnabled: boolean | null
  dashboardTurnoversEnabled: boolean | null
  notificationReadEnabled: boolean | null
  patientReadEnabled: boolean | null
  roomWriteConfigurationEnabled: boolean | null
  availableTimesEmailEnabled: boolean | null
  caseNotePlanReadEnabled: boolean | null
}

export type ApellaUser = {
  currentOrganization: NonNullable<
    GetCurrentUser['me']
  >['organizations']['edges'][0]
  userOrganizations: NonNullable<GetCurrentUser['me']>['organizations']['edges']
  permissions?: Permissions
  userId: string
  email: string
  isApellaEmployee: boolean
}
