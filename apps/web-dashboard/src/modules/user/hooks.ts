import { useQuery } from '@apollo/client'

import { useApellaAuth0 } from '@apella/hooks'
import { GetCurrentUser } from 'src/modules/user/__generated__'
import { GET_CURRENT_USER } from 'src/modules/user/queries'
import { isApellaEmployee } from 'src/utils/userTypes'

import { GqlPermissions, GqlUiPermissions, Permissions } from './types'

export const useCurrentUser = (
  { skip }: { skip?: boolean } = { skip: false }
) => {
  const { user, isLoading, error: authError } = useApellaAuth0()

  const {
    loading,
    data,
    error: queryError,
  } = useQuery<GetCurrentUser>(GET_CURRENT_USER, { skip })

  return {
    loading: isLoading || loading,
    error: authError || queryError,
    currentOrganization: data?.me?.organizations.edges.find(
      ({ node }) => node.auth0OrgId === user?.org_id
    ),
    userOrganizations: data?.me?.organizations.edges,
    permissions: getPermissions(data?.me?.uiPermissions, data?.me?.permissions),
    userId: data?.me?.id,
    email: data?.me?.email,
    isApellaEmployee: isApellaEmployee(data?.me?.email),
  }
}

export const PERMISSIONS = {
  READ_ANY_CASE_NOTE_PLAN: 'case_note_plan:read:any',
  READ_ANY_CONTACT_INFORMATION: 'contact_information:read:any',
  READ_ANY_NOTIFICATION: 'notification:read:any',
  READ_ANY_PATIENT: 'patient:read:all',
  READ_DASHBOARD_TURNOVERS: 'dashboard:turnovers_dashboard:read:any',
  WRITE_CONFIGURATION_ROOM: 'room:write:configuration',
  EMAIL_ANY_AVAILABLE_TIMES: 'available_times:email:any',
}

export const getPermissions = (
  uiPermissions: GqlUiPermissions | undefined,
  permissions: GqlPermissions | undefined
): Permissions | undefined => {
  if (!!uiPermissions && !!permissions) {
    return {
      ...uiPermissions,
      contactInformationReadEnabled: permissions?.includes(
        PERMISSIONS.READ_ANY_CONTACT_INFORMATION
      ),
      dashboardTurnoversEnabled: permissions?.includes(
        PERMISSIONS.READ_DASHBOARD_TURNOVERS
      ),
      notificationReadEnabled: permissions?.includes(
        PERMISSIONS.READ_ANY_NOTIFICATION
      ),
      patientReadEnabled: permissions?.includes(PERMISSIONS.READ_ANY_PATIENT),
      roomWriteConfigurationEnabled: permissions?.includes(
        PERMISSIONS.WRITE_CONFIGURATION_ROOM
      ),
      availableTimesEmailEnabled: permissions?.includes(
        PERMISSIONS.EMAIL_ANY_AVAILABLE_TIMES
      ),
      caseNotePlanReadEnabled: permissions?.includes(
        PERMISSIONS.READ_ANY_CASE_NOTE_PLAN
      ),
    }
  }
  return undefined
}
