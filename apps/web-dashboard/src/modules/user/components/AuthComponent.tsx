import { useFlags } from 'launchdarkly-react-client-sdk'

import { ChildrenProps } from '@apella/component-library'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import { Permission } from 'src/router/types'

const WHITE_LISTED_TEST_ORGS = ['apella_internal_0', 'greys_anatomy', 'scrubs']

// Filter keys of a type to the keys where key value is boolean
type BooleanKeys<T> = {
  [k in keyof T]: T[k] extends boolean ? k : never
}[keyof T]

export const AuthComponent = ({
  permission,
  hideInProductionForApella,
  children,
  flag,
}: {
  permission?: Permission
  hideInProductionForApella?: boolean
  flag?: BooleanKeys<WebDashboardFeatureFlagSet>
} & ChildrenProps) => {
  const isCurrentUserRequired = permission || hideInProductionForApella

  const { permissions, loading, isApellaEmployee, currentOrganization } =
    useCurrentUser({ skip: !isCurrentUserRequired })

  const flags = useFlags<WebDashboardFeatureFlagSet>()

  const currentOrgId = currentOrganization?.node.id || ''

  // Allow test, dev, staging, and test orgs to always be accessible
  const showForApellaEmployees =
    !process.env.APELLA_ENV ||
    process.env.APELLA_ENV === 'development' ||
    process.env.APELLA_ENV === 'staging' ||
    WHITE_LISTED_TEST_ORGS.includes(currentOrgId)

  const showForEmployee =
    !isApellaEmployee || !hideInProductionForApella || showForApellaEmployees

  const permissionEnabled =
    !permission || (!loading && permissions && permissions[permission])

  const isFlagEnabled = !flag || flags[flag]

  if (isFlagEnabled && permissionEnabled && showForEmployee) {
    return <>{children}</>
  }

  return null
}
