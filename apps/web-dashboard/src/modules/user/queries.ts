import { gql } from '@apollo/client'

export const GET_CURRENT_USER = gql`
  query GetCurrentUser {
    me {
      id
      email
      organizations {
        edges {
          node {
            id
            auth0OrgId
            name
            sites(orderBy: [{ sort: "id", direction: ASC }]) {
              edges {
                node {
                  id
                  timezone
                  name
                  rooms(orderBy: [{ sort: "id", direction: ASC }]) {
                    edges {
                      node {
                        id
                        name
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      permissions
      uiPermissions {
        dashboardInsightsEnabled
        dashboardHighlightsEnabled
        dashboardScheduleEnabled
        dashboardScheduleEditEnabled
        dashboardLiveEnabled
        dashboardLiveFromScheduleEnabled
        dashboardTerminalCleansEnabled
        dashboardCreateMeasurementPeriodsEnabled
        dashboardStaffManagementEnabled
        bigBoardEnabled
        bigBoardWriteEnabled
        caseStaffPlanWriteEnabled
        caseStaffPlanReadEnabled
        caseDurationEnabled
        roomWriteEnabled
        readAnyAssetEnabled
      }
    }
  }
`
