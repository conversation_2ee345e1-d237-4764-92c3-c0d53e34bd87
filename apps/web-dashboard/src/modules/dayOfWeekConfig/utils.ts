// TODO: consolidate these definitions w/those in web-internal
import { DateTime } from 'luxon'

import { DayOfWeek, WeekConfig, WeekRecord } from './types'

export function createWeekRecord<T>(fn: (val: DayOfWeek) => T): WeekRecord<T> {
  return {
    sunday: fn('sunday'),
    monday: fn('monday'),
    tuesday: fn('tuesday'),
    wednesday: fn('wednesday'),
    thursday: fn('thursday'),
    friday: fn('friday'),
    saturday: fn('saturday'),
  }
}

export const getDayOfWeek = (date: DateTime) => {
  return date.weekdayLong.toLowerCase() as DayOfWeek
}

/** Converts raw GraphQL output to remove nulls + format time */
export const convertToWeekConfig = (
  rawData: WeekRecord<{ startTime: string; endTime: string } | null> | undefined
): WeekConfig => {
  if (!rawData) return createWeekRecord(() => undefined)

  return createWeekRecord((day) => {
    const configForDay = rawData[day]
    if (!configForDay) return undefined

    return {
      startTime: formatTime(configForDay.startTime),
      endTime: formatTime(configForDay.endTime),
    }
  })
}

/**
 * Helper for formatting time string to match JS `<input type="time">` value format
 *
 * @param time string representing time. Assumes that this is formatted as "09:00:00" or "09:00"
 * @returns string representing time formatted as "09:00"
 * */
export const formatTime = (time: string) => {
  // Split the time string by ":"
  const timeParts = time.split(':')

  // Return only the first two parts (hours and minutes)
  return `${timeParts[0]}:${timeParts[1]}`
}
