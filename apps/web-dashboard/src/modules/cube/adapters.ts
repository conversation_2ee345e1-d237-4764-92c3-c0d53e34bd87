import { TimeDimensionGranularity } from '@cubejs-client/core'

import { TimeDimensions } from 'src/utils/bucketHelpers'

export const toTimeDimensionGranularity = (
  timeDimension: TimeDimensions
): TimeDimensionGranularity => {
  switch (timeDimension) {
    case TimeDimensions.DAY:
      return 'day'
    case TimeDimensions.MINUTE:
      return 'minute'
    case TimeDimensions.MONTH:
      return 'month'
    case TimeDimensions.WEEK:
      return 'week'
  }
}
