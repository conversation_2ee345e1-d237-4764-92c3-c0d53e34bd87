export const CUBE_QUERY_LIMIT = 50000

export enum ROOMS {
  ID = 'RoomsBQ.id',
  NAME = 'RoomsBQ.name',
}

export enum CASES {
  CASE_ID = 'InsightsCases.caseId',
  CASE_PHASE_ID = 'InsightsCases.casePhaseId',
  CUSTOMER_CASE_ID = 'InsightsCases.customerCaseId',
  ROOM_ID = 'InsightsCases.roomId',
  SITE_ID = 'InsightsCases.siteId',
  IS_ADD_ON = 'InsightsCases.isAddOn',
  IS_FIRST_CASE = 'InsightsCases.isFirstCase',
  CASE_CLASSIFICATION_ID = 'InsightsCases.caseClassificationId',
  PATIENT_CLASSIFICATION_ID = 'InsightsCases.patientClassificationId',
  SCHEDULED_DURATION_MINUTES = 'InsightsCases.scheduledDurationMinutes',
  ACTUAL_DURATION_MINUTES = 'InsightsCases.actualDurationMinutes',
  ACTUAL_DAY_OF_WEEK = 'InsightsCases.actualDayOfWeek',
  ACTUAL_START_TIMESTAMP = 'InsightsCases.actualStartTimestamp',
  ACTUAL_START_TIME_LOCAL = 'InsightsCases.actualStartTimeLocal',
  ACTUAL_END_TIME_LOCAL = 'InsightsCases.actualEndTimeLocal',
  SCHEDULED_START_TIMESTAMP = 'InsightsCases.scheduledStartTimestamp',
  PRE_OP_DURATION_MINUTES = 'InsightsCases.preOpDurationMinutes',
  ANESTHESIA_PREP_DURATION_MINUTES = 'InsightsCases.anesthesiaPrepDurationMinutes',
  SURGERY_PREP_DURATION_MINUTES = 'InsightsCases.surgeryPrepDurationMinutes',
  INTRA_OP_DURATION_MINUTES = 'InsightsCases.intraOpDurationMinutes',
  POST_OP_DURATION_MINUTES = 'InsightsCases.postOpDurationMinutes',
  LATE_MINUTES = 'InsightsCases.lateMinutes',
  COUNT = 'InsightsCases.count',
  COUNT_LATE_STARTS = 'InsightsCases.countLateStarts',
  COUNT_LATE_WITHIN_5_MINUTES = 'InsightsCases.countLateWithinFiveMinutes',
  LATE_STARTS_PERCENT = 'InsightsCases.lateStartsPercent',
  ON_TIME_STARTS_PERCENT = 'InsightsCases.onTimeStartsPercent',
  WITHIN_FIVE_MINUTE_GRACE_PERIOD_PERCENT = 'InsightsCases.withinFiveMinuteGracePeriodPercent',
  SUM_ACTUAL_DURATION_MINUTES = 'InsightsCases.sumActualDurationMinutes',
  AVG_SCHEDULED_DURATION_MINUTES = 'InsightsCases.avgScheduledDurationMinutes',
  AVG_ACTUAL_MINUS_SCHEDULED_DURATION_MINUTES = 'InsightsCases.avgActualMinusScheduledDurationMinutes',
  AVG_ACTUAL_DURATION_MINUTES = 'InsightsCases.avgActualDurationMinutes',
  AVG_PRE_OP_DURATION_MINUTES = 'InsightsCases.avgPreOpDurationMinutes',
  AVG_INTRA_OP_DURATION_MINUTES = 'InsightsCases.avgIntraOpDurationMinutes',
  AVG_POST_OP_DURATION_MINUTES = 'InsightsCases.avgPostOpDurationMinutes',
  AVG_LATE_MINUTES = 'InsightsCases.avgLateMinutes',
}

export enum CASE_STAFF {
  ROLE = 'CaseStaffBQ.role',
  STAFF_ID = 'CaseStaffBQ.staffId',
}

export enum CASE_STAFF_SURGEONS {
  ROLE = 'CaseStaffSurgeonsBQ.role',
  STAFF_ID = 'CaseStaffSurgeonsBQ.staffId',
}

export enum CASE_STAFF_ANESTHESIA {
  ROLE = 'CaseStaffAnesthesiaBQ.role',
  STAFF_ID = 'CaseStaffAnesthesiaBQ.staffId',
}

export enum CASE_STAFF_CIRCULATOR {
  ROLE = 'CaseStaffCirculator.role',
  STAFF_ID = 'CaseStaffCirculator.staffId',
}

export enum CASE_STAFF_SCRUB_TECH {
  ROLE = 'CaseStaffScrubTech.role',
  STAFF_ID = 'CaseStaffScrubTech.staffId',
}

export enum CASE_PROCEDURES {
  PROCEDURE_ID = 'CaseProceduresBQ.procedureId',
}

export enum STAFF {
  ID = 'StaffBQ.id',
  FIRST_NAME = 'StaffBQ.firstName',
  LAST_NAME = 'StaffBQ.lastName',
}

export enum PROCEDURES {
  ID = 'ProceduresBQ.id',
  NAME = 'ProceduresBQ.name',
}

export enum SITES {
  NAME = 'SitesBQ.name',
}

export enum SERVICE_LINES {
  ID = 'ServiceLinesBQ.id',
  NAME = 'ServiceLinesBQ.name',
  EXTERNAL_SERVICE_LINE_ID = 'ServiceLinesBQ.externalServiceLineId',
  ORG_ID = 'ServiceLinesBQ.orgId',
}

export enum CASE_CLASSIFICATION_TYPES {
  ID = 'CaseClassificationTypesBQ.id',
  NAME = 'CaseClassificationTypesBQ.name',
  DESCRIPTION = 'CaseClassificationTypesBQ.description',
}

export enum TURNOVERS {
  PHASE_ID = 'Turnovers.phaseId',
  SITE_ID = 'Turnovers.siteId',
  ROOM_ID = 'Turnovers.roomId',
  START_TIMESTAMP = 'Turnovers.startTimestamp',
  START_TIME_LOCAL = 'Turnovers.startTimeLocal',
  END_TIME_LOCAL = 'Turnovers.endTimeLocal',
  TOTAL_DURATION_MINUTES = 'Turnovers.totalDurationMinutes',
  CLEAN_DURATION_MINUTES = 'Turnovers.cleanDurationMinutes',
  OPEN_DURATION_MINUTES = 'Turnovers.openDurationMinutes',
  PRECEDING_CASE_ID = 'Turnovers.precedingCaseId',
  FOLLOWING_CASE_ID = 'Turnovers.followingCaseId',
  DAY_OF_WEEK = 'Turnovers.dayOfWeek',
  COUNT = 'Turnovers.count',
  AVG_DURATION = 'Turnovers.averageDuration',
  CLEANING_COUNT = 'Turnovers.cleaningCount',
  OPENING_COUNT = 'Turnovers.openingCount',
  OPENING_AVG_MINUTES = 'Turnovers.openingAverageMinutes',
  CLEANING_AVG_MINUTES = 'Turnovers.cleaningAverageMinutes',
  DELAY_LABELS = 'Turnovers.delayLabels',
  SAME_SURGEON_SAME_ROOM = 'Turnovers.isSameSurgeonSameRoom',
}

export enum TURNOVER_NOTES {
  NOTE = 'TurnoverNotes.note',
}

export enum TURNOVER_LABELS {
  TYPE = 'TurnoverLabels.type',
}

export enum TURNOVER_GOAL {
  GOAL = 'TurnoverGoals.goal',
  MAX_MINUTES = 'TurnoverGoals.maxMinutes',
}

export enum CASE_DERIVED_PROPERTIES {
  PRECEDING_CASE_ID = 'CaseDerivedPropertiesBQ.precedingCaseId',
  IS_IN_FLIP_ROOM = 'CaseDerivedPropertiesBQ.isInFlipRoom',
  IS_FIRST_CASE = 'CaseDerivedPropertiesBQ.isFirstCase',
}

export enum FORECASTING_REPORT_DATA_USED_IN_TRAINING {
  ROOM_ID = 'ForecastingReportDataUsedInTrainingBQ.roomId',
  CASE_ID = 'ForecastingReportDataUsedInTrainingBQ.caseId',
  APELLA_CASE_ID = 'ForecastingReportDataUsedInTrainingBQ.apellaCaseId',
  START_TIME_LOCAL = 'ForecastingReportDataUsedInTrainingBQ.startTimeLocal',
  DURATION = 'ForecastingReportDataUsedInTrainingBQ.duration',
  SITE = 'ForecastingReportDataUsedInTrainingBQ.site',
  ROOM = 'ForecastingReportDataUsedInTrainingBQ.room',
  FIRST_PRIMARY_SURGEON = 'ForecastingReportDataUsedInTrainingBQ.firstPrimarySurgeon',
  PROCEDURE_LIST = 'ForecastingReportDataUsedInTrainingBQ.procedureList',
  SURGEON_LIST = 'ForecastingReportDataUsedInTrainingBQ.surgeonList',
}

export enum REPORT_CASE {
  DELAY_TYPE = 'ReportCase.delay_type',
  DELAY_REASON = 'ReportCase.delay_reason',
  DELAY_DURATION_SECONDS = 'ReportCase.delay_duration_seconds',
  DELAY_COMMENTS = 'ReportCase.delay_comments',
}
