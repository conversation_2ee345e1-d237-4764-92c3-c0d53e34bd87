import { DateTime } from 'luxon'

export interface ApellaCase {
  endTime?: DateTime
}

export interface ApellaRoom {
  apellaCases: ApellaCase[]
  hasFirstCase?: boolean
  id: string
}

export interface DailyMetrics {
  addOnCasesCount: number
  afterHoursCasesCount: number
  completedCases: number | undefined
  firstCaseOnTimePercent?: number
  firstCasesCount: number
  firstCaseStarts: string[]
  firstCasesWithinFiveMinutesPercent?: number
  openPast15: string[]
  openPast17: string[]
  openPast19: string[]
  openToday: string[]
  showFirstCases: boolean
  stillOpen: string[]
  totalCases: number
  urgentCasesCount: number
}
