import { gql } from '@apollo/client'

export const GET_DAILY_METRICS_CASE_DATA = gql`
  query GetDailyMetricCaseData(
    $siteIds: [ID!]
    $roomIds: [ID!]
    $staffIds: [ID!]
    $minEndTime: DateTime!
    $maxStartTime: DateTime!
  ) {
    apellaCases(
      query: {
        minEndTime: $minEndTime
        maxStartTime: $maxStartTime
        siteIds: $siteIds
        roomIds: $roomIds
        staffIds: $staffIds
      }
    ) {
      edges {
        node {
          id
          case {
            id
            isFirstCase
            scheduledStartTime
            isAddOn
            caseClassificationType {
              id
            }
          }
          room {
            id
          }
          type
          startTime
          endTime
        }
      }
      totalRecords
    }
  }
`
