import { LoaderFunctionArgs, useLoaderData } from 'react-router'

import { DateTime } from 'luxon'

import { FlexContainer, remSpacing } from '@apella/component-library'
import {
  Border,
  InsightsMetrics,
  MetricsSection,
  MetricType,
} from 'src/components/InsightsTile/InsightsTile'
import { defaultTimezone } from 'src/Contexts'
import { fetchBlockUtilizationData } from 'src/pages/Schedule/fetchBlockUtilizationData'
import { getScheduleSearchParams } from 'src/pages/Schedule/getScheduleSearchParams'
import { ScheduleFilterProviderContext } from 'src/pages/Schedule/ScheduleFilterContext'
import {
  BlockTooltipBodyBlockRow,
  compareBlocksByMinStartTime,
} from 'src/pages/Schedule/TimelinePill/BlockTimePill'
import { getRouteContext } from 'src/router/types'
import { urlFriendlyDateToDateTime } from 'src/utils/urlFriendlyDate'

export const loader = async (
  { request }: LoaderFunctionArgs,
  context: unknown
) => {
  const { apolloClient, flags, user } = getRouteContext(context)
  const defaultBlockUtilization = {
    overallBlockUtilization: undefined,
    blocks: [],
  }
  if (!flags?.blockTimeOnSchedulePageEnabled) {
    return defaultBlockUtilization
  }

  const requestURL = new URL(request.url)
  const searchParams = new URLSearchParams(requestURL.search)
  const { selectedDate, selectedRoomIds, selectedSiteIds } =
    getScheduleSearchParams(searchParams)

  const timezone =
    user.currentOrganization?.node.sites.edges[0]?.node.timezone ||
    defaultTimezone

  const computedTime = selectedDate
    ? urlFriendlyDateToDateTime(selectedDate)
    : DateTime.now()
  const minTime = computedTime
    .startOf('day')
    .setZone(timezone, { keepLocalTime: true })
  const maxTime = computedTime
    .endOf('day')
    .setZone(timezone, { keepLocalTime: true })

  const stringSiteIdsStore = window.localStorage.getItem(
    `${ScheduleFilterProviderContext.SCHEDULE}_SITE_IDS`
  )
  const siteIdsStore =
    stringSiteIdsStore && stringSiteIdsStore !== 'undefined'
      ? JSON.parse(stringSiteIdsStore)
      : undefined

  const siteIds: string[] =
    selectedSiteIds !== undefined ? selectedSiteIds : siteIdsStore

  const roomIds =
    !user.currentOrganization || !siteIds
      ? undefined
      : (selectedRoomIds ??
        siteIds
          .flatMap((siteId) =>
            user.currentOrganization?.node.sites.edges
              .find(({ node }) => node.id === siteId)
              ?.node.rooms.edges.map((e) => e.node.id)
          )
          .filter(Boolean))

  if (!roomIds || (roomIds && roomIds.length === 0)) {
    return defaultBlockUtilization
  }

  return fetchBlockUtilizationData(apolloClient, {
    minTime,
    maxTime,
    siteIds: siteIds ?? [],
    timezone,
    roomIds: roomIds,
  })
}

export const BlockData = () => {
  const { overallBlockUtilization, blocks } =
    useLoaderData<Awaited<ReturnType<typeof loader>>>()

  return (
    <FlexContainer
      direction="column"
      gap={remSpacing.medium}
      css={{
        paddingTop: remSpacing.medium,
      }}
    >
      <div
        css={{
          padding: `${remSpacing.xsmall} ${remSpacing.medium}`,
        }}
      >
        <InsightsMetrics
          metricsSections={[
            <MetricsSection
              key="key"
              metrics={[
                {
                  label: 'Overall block utilization',
                  value: overallBlockUtilization,
                  type: MetricType.PERCENT,
                  description: `The overall block utilization across all rooms at this site on the selected date.`,
                  style: 'h2',
                },
              ]}
            />,
          ]}
        />
      </div>
      <Border />

      <FlexContainer
        direction="column"
        css={{
          paddingLeft: remSpacing.small,
          paddingRight: remSpacing.small,
          gap: remSpacing.medium,
        }}
      >
        {blocks.length > 0 &&
          blocks
            .sort((a, b) => compareBlocksByMinStartTime(a, b))
            .map((block) => (
              <BlockTooltipBodyBlockRow key={block.id} block={block} />
            ))}
      </FlexContainer>
    </FlexContainer>
  )
}
