import { generatePath } from 'react-router'

import {
  ButtonLink,
  ChevronRight,
  remSpacing,
  theme,
  Caps1,
} from '@apella/component-library'
import {
  InsightsMetrics,
  MetricType,
  MetricsSection,
} from 'src/components/InsightsTile/InsightsTile'
import { AuthComponent } from 'src/modules/user/components/AuthComponent'
import { CaseLengthMeasures } from 'src/pages/Insights/CaseLength/caseLengthMeasures'
import { FirstCaseMeasures } from 'src/pages/Insights/FirstCaseStart/firstCaseMeasures'
import { LocationPath } from 'src/router/types'

import { useApellaCaseDailyMetrics } from '../hooks/useApellaCaseDailyMetrics'
import { useQueryFilterState } from '../hooks/useQueryFilterState'

export const CaseData = () => {
  const { siteIds, minTime, maxTime, staffIds, roomIds } = useQueryFilterState()

  const { isLoading, results } = useApellaCaseDailyMetrics({
    siteIds: siteIds ?? [],
    minTime,
    maxTime,
    staffIds,
    roomIds,
  })

  return (
    <div css={{ paddingTop: remSpacing.medium }}>
      <InsightsMetrics
        metricsSections={[
          <div
            key="all-cases"
            css={{ display: 'flex', flexDirection: 'column' }}
          >
            <Caps1
              color={theme.palette.text.secondary}
              css={{
                paddingLeft: remSpacing.medium,
              }}
            >
              all cases
            </Caps1>
            <div css={{ padding: remSpacing.medium }}>
              <MetricsSection
                isLoading={isLoading}
                metrics={[
                  {
                    value: results?.totalCases,
                    label: CaseLengthMeasures.COUNT,
                    style: 'h2',
                  },
                ]}
              />
              {results?.showFirstCases && (
                <MetricsSection
                  isLoading={isLoading}
                  metrics={[
                    {
                      value: results?.urgentCasesCount,
                      label: 'Urgent/Emergent',
                    },
                    {
                      value: results?.addOnCasesCount,
                      label: 'Add-On',
                    },
                    {
                      label: 'After Hours',
                      value: results?.afterHoursCasesCount,
                    },
                  ]}
                />
              )}
            </div>
          </div>,
          <div
            key="first-cases"
            css={{ display: 'flex', flexDirection: 'column' }}
          >
            <Caps1
              color={theme.palette.text.secondary}
              css={{
                paddingBottom: 0,
                paddingTop: remSpacing.medium,
                paddingLeft: remSpacing.medium,
              }}
            >
              first case starts
            </Caps1>

            <div css={{ padding: remSpacing.medium }}>
              <MetricsSection
                isLoading={isLoading}
                metrics={[
                  {
                    value: results?.firstCasesCount,
                    label: FirstCaseMeasures.TOTAL_FIRST_CASES,
                    style: 'h2',
                  },
                ]}
              />
              {results?.showFirstCases && (
                <MetricsSection
                  isLoading={isLoading}
                  metrics={[
                    {
                      value: results.firstCaseOnTimePercent,
                      label: FirstCaseMeasures.ONTIME_STARTS_PERCENT,
                      type: MetricType.PERCENT,
                      description: `The percentage of first cases which were on time for the selected date. A case is considered on time if the patient is wheeled in less than 1 min after the scheduled start time.`,
                    },
                    {
                      value: results.firstCasesWithinFiveMinutesPercent,
                      label: FirstCaseMeasures.WITHIN_FIVE_MINUTES,
                      type: MetricType.PERCENT,
                      description: `The percentage of first cases that started within 5 min of the scheduled start time for the selected day.`,
                    },
                  ]}
                />
              )}
            </div>
          </div>,
        ]}
      />

      <AuthComponent permission="dashboardInsightsEnabled">
        <div css={{ display: 'flex' }}>
          <ButtonLink
            appearance="link"
            to={generatePath(LocationPath.CaseVolume)}
          >
            View case volume
            <ChevronRight size="sm" />
          </ButtonLink>
        </div>
      </AuthComponent>
    </div>
  )
}
