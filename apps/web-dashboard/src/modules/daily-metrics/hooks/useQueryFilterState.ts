import useDateState from 'src/utils/useDateState'

import { useSitesState } from '../../../utils/useSitesState'

export const useQueryFilterState = () => {
  const query = new URLSearchParams(location.search)
  const [dates] = useDateState()
  const [siteIds] = useSitesState()

  const staffIds = query.get('surgeons')?.split(',') ?? []
  const roomIds = query.get('rooms')?.split(',') ?? []

  const { minTime, maxTime } = dates

  return {
    minTime,
    maxTime,
    staffIds,
    roomIds,
    siteIds,
  }
}
