import { DateTime } from 'luxon'

import { CaseType } from 'src/__generated__/globalTypes'
import { useTimezone } from 'src/Contexts'
import { DailyMetrics } from 'src/modules/daily-metrics/types'
import { useCurrentMinute } from 'src/utils/useCurrentTime'

export type InsightApellaCase = {
  endTime?: DateTime
  case?: {
    isFirstCase?: boolean
    scheduledStartTime: DateTime
  } | null
  room: {
    id: string
  }
  type: CaseType
}

export const useScheduleMetrics = ({
  apellaCases,
  minTime,
}: {
  minTime: string
  apellaCases?: InsightApellaCase[]
}): Pick<
  DailyMetrics,
  | 'firstCaseStarts'
  | 'stillOpen'
  | 'openPast15'
  | 'openPast17'
  | 'openPast19'
  | 'openToday'
  | 'totalCases'
  | 'completedCases'
> => {
  const { timezone } = useTimezone()
  const currentMinute = useCurrentMinute()

  const threePM = DateTime.fromISO(minTime)
    .setZone(timezone)
    .set({ hour: 15 })
    .startOf('hour')

  const fivePM = DateTime.fromISO(minTime)
    .setZone(timezone)
    .set({ hour: 17 })
    .startOf('hour')

  const sevenPM = DateTime.fromISO(minTime)
    .setZone(timezone)
    .set({ hour: 19 })
    .startOf('hour')

  const openToday = Array.from(new Set(apellaCases?.map((ac) => ac.room.id)))

  const firstCaseStarts = Array.from(
    new Set(
      apellaCases
        ?.filter(
          (apellaCase) =>
            apellaCase.case?.isFirstCase && apellaCase.case?.scheduledStartTime
        )
        .map((apellaCase) => apellaCase.room.id)
    )
  )
  const completedCases = apellaCases?.filter(
    (apellaCase) => apellaCase.type == CaseType.COMPLETE
  ).length

  const stillOpen = Array.from(
    new Set(
      apellaCases
        ?.filter(
          (apellaCase) =>
            !apellaCase.endTime ||
            apellaCase.endTime.setZone(timezone) > currentMinute
        )
        .map((apellaCase) => apellaCase.room.id)
    )
  )

  const openPast15 = Array.from(
    new Set(
      apellaCases
        ?.filter(
          (apellaCase) =>
            apellaCase.endTime && apellaCase.endTime.setZone(timezone) > threePM
        )
        .map((apellaCase) => apellaCase.room.id)
    )
  )
  const openPast17 = Array.from(
    new Set(
      apellaCases
        ?.filter(
          (apellaCase) =>
            apellaCase.endTime && apellaCase.endTime.setZone(timezone) > fivePM
        )
        .map((apellaCase) => apellaCase.room.id)
    )
  )
  const openPast19 = Array.from(
    new Set(
      apellaCases
        ?.filter(
          (apellaCase) =>
            apellaCase.endTime && apellaCase.endTime.setZone(timezone) > sevenPM
        )
        .map((apellaCase) => apellaCase.room.id)
    )
  )

  return {
    firstCaseStarts,
    stillOpen,
    openPast15,
    openPast17,
    openPast19,
    openToday,
    completedCases,
    totalCases: apellaCases?.length ?? 0,
  }
}
