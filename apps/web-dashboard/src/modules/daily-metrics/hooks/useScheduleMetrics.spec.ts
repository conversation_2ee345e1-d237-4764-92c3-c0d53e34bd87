import { renderHook } from '@testing-library/react'
import { DateTime } from 'luxon'

import { useScheduleMetrics } from 'src/modules/daily-metrics/hooks/useScheduleMetrics'
import { mockApellaCase, mockCase } from 'src/test/testMocks'

import { useTimezone } from '../../../Contexts'

describe('useScheduleMetrics', () => {
  const { result: timezoneResult } = renderHook(() => useTimezone())
  const minTime = DateTime.fromObject(
    {
      year: 2022,
      month: 10,
      day: 1,
      hour: 7,
      minute: 5,
    },
    { zone: timezoneResult.current.timezone }
  )

  const cases = [
    mockApellaCase(
      '1',
      minTime.set({ hour: 16 }),
      minTime.set({ hour: 16, minute: 10 }),

      mockCase(
        '1',
        minTime.set({ hour: 16 }),
        minTime.set({ hour: 16, minute: 10 })
      )
    ),
    mockApellaCase('1', minTime, minTime.plus({ minute: 3 }), {
      ...mockCase('1', minTime, minTime.plus({ minute: 3 })),
      isFirstCase: true,
    }),
    mockApellaCase(
      '1',
      minTime.set({ hour: 18 }),
      minTime.set({ hour: 18, minute: 10 }),
      mockCase(
        '1',
        minTime.set({ hour: 18 }),
        minTime.set({ hour: 188888888, minute: 10 })
      )
    ),
    mockApellaCase(
      '1',
      minTime.set({ hour: 20 }),
      minTime.set({ hour: 20, minute: 10 }),
      mockCase(
        '1',
        minTime.set({ hour: 20 }),
        minTime.set({ hour: 20, minute: 10 })
      )
    ),
  ]

  it('calculates metrics correctly', () => {
    const { result } = renderHook(() =>
      useScheduleMetrics({
        minTime: minTime.toISO(),
        apellaCases: cases,
      })
    )

    expect(result.current.firstCaseStarts ?? []).toHaveLength(1)
    expect(result.current.totalCases ?? []).toEqual(4)
    expect(result.current.openToday ?? []).toHaveLength(1)
    expect(result.current.stillOpen ?? []).toHaveLength(0)
    expect(result.current.openPast15 ?? []).toHaveLength(1)
    expect(result.current.openPast17 ?? []).toHaveLength(1)
    expect(result.current.openPast19 ?? []).toHaveLength(1)
  })
})
