import { useQuery } from '@apollo/client'
import { renderHook } from '@testing-library/react'
import { DateTime } from 'luxon'

import { CaseType } from 'src/__generated__/globalTypes'
import { useApellaCaseDailyMetrics } from 'src/modules/daily-metrics/hooks/useApellaCaseDailyMetrics'
import { GetDailyMetricCaseData } from 'src/modules/daily-metrics/queries/__generated__'
import { MockAppProvider } from 'src/test/MockAppProvider'
import { mockUseQueryResult } from 'src/test/useQueryMock'
import { useCurrentMinute } from 'src/utils/useCurrentTime'

vi.mock('@apollo/client')
const mockedUseQuery =
  vi.mocked<typeof useQuery<GetDailyMetricCaseData, any>>(useQuery)

vi.mock('src/utils/useCurrentTime')
const mockedUseCurrentMinute = vi.mocked(useCurrentMinute)

const mockApellaCases: GetDailyMetricCaseData = {
  apellaCases: {
    edges: [
      {
        node: {
          id: '1',
          case: {
            id: '1',
            isFirstCase: true,
            scheduledStartTime: '2023-09-01T08:13:56.234750-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_0',
            __typename: 'Room',
          },
          type: CaseType.LIVE,
          startTime: '2023-09-01T08:13:58.518006-07:00',
          endTime: '2023-09-01T08:54:06.454941-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '2',
          case: {
            id: '2',
            isFirstCase: true,
            scheduledStartTime: '2023-09-01T08:11:23.890498-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_1',
            __typename: 'Room',
          },
          type: CaseType.LIVE,
          startTime: '2023-09-01T08:22:09.662444-07:00',
          endTime: '2023-09-01T09:01:00.670134-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '3',
          case: {
            id: '3',
            isFirstCase: false,
            scheduledStartTime: '2023-09-01T09:19:34.297272-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_0',
            __typename: 'Room',
          },
          type: CaseType.FORECAST,
          startTime: '2023-09-01T09:28:54.713417-07:00',
          endTime: '2023-09-01T10:35:19.691292-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '4',
          case: {
            id: '4',
            isFirstCase: false,
            scheduledStartTime: '2023-09-01T09:20:08.294013-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_1',
            __typename: 'Room',
          },
          type: CaseType.FORECAST,
          startTime: '2023-09-01T09:29:58.312081-07:00',
          endTime: '2023-09-01T10:22:16.126412-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '5',
          case: {
            id: '5',
            isFirstCase: false,
            scheduledStartTime: '2023-09-01T10:52:25.340347-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_1',
            __typename: 'Room',
          },
          type: CaseType.FORECAST,
          startTime: '2023-09-01T11:00:37.274059-07:00',
          endTime: '2023-09-01T12:04:52.809642-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '6',
          case: {
            id: '6',
            isFirstCase: false,
            scheduledStartTime: '2023-09-01T11:03:15.285757-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_0',
            __typename: 'Room',
          },
          type: CaseType.FORECAST,
          startTime: '2023-09-01T11:19:46.318009-07:00',
          endTime: '2023-09-01T12:24:43.145249-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '7',
          case: {
            id: '7',
            isFirstCase: false,
            scheduledStartTime: '2023-09-01T12:34:27.959044-07:00',
            isAddOn: true,
            caseClassificationType: {
              id: 'CASE_CLASSIFICATION_EMERGENT',
              __typename: 'CaseClassificationType',
            },
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_1',
            __typename: 'Room',
          },
          type: CaseType.FORECAST,
          startTime: '2023-09-01T12:42:53.657463-07:00',
          endTime: '2023-09-01T13:50:02.008446-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '9',
          case: {
            id: '9',
            isFirstCase: false,
            scheduledStartTime: '2023-09-01T12:45:50.840909-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_0',
            __typename: 'Room',
          },
          type: CaseType.FORECAST,
          startTime: '2023-09-01T13:01:04.967422-07:00',
          endTime: '2023-09-01T14:03:34.695053-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '9',
          case: {
            id: '9',
            isFirstCase: false,
            scheduledStartTime: '2023-09-01T14:21:19.255604-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_0',
            __typename: 'Room',
          },
          type: CaseType.FORECAST,
          startTime: '2023-09-01T14:20:46.684547-07:00',
          endTime: '2023-09-01T15:27:38.955207-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '10',
          case: {
            id: '10',
            isFirstCase: null,
            scheduledStartTime: '2023-09-01T14:21:30.117820-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_1',
            __typename: 'Room',
          },
          type: CaseType.FORECAST,
          startTime: '2023-09-01T14:23:06.822816-07:00',
          endTime: '2023-09-01T17:13:02.750779-07:00', // 22 = 15
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
      {
        node: {
          id: '11',
          case: {
            id: '11',
            isFirstCase: false,
            scheduledStartTime: '2023-09-01T15:48:24.248430-07:00',
            isAddOn: null,
            caseClassificationType: null,
            __typename: 'ScheduledCase',
          },
          room: {
            id: 'garage_1',
            __typename: 'Room',
          },
          type: CaseType.FORECAST,
          startTime: '2023-09-01T15:21:56.797090-07:00',
          endTime: '2023-09-01T19:09:31.729036-07:00',
          __typename: 'ApellaCase',
        },
        __typename: 'ApellaCaseEdge',
      },
    ],
    totalRecords: 11,
    __typename: 'ApellaCaseConnection',
  },
}

describe('useApellaCaseDailyMetrics', () => {
  it('should calculate all metrics correctly at beginning of the day', () => {
    mockedUseQuery.mockReturnValue(
      mockUseQueryResult({ data: mockApellaCases })
    )
    mockedUseCurrentMinute.mockReturnValue(
      DateTime.fromISO('2023-09-01T08:30:00-07:00')
    )

    const { result } = renderHook(
      () =>
        useApellaCaseDailyMetrics({
          minTime: '2023-09-01T00:00:00.000-07:00',
          maxTime: '2023-09-01T16:59:59.999-07:00',
          siteIds: ['lab_1'],
        }),
      { wrapper: MockAppProvider }
    )

    expect(result.current.isLoading).toBeFalsy()
    expect(result.current.results).toEqual({
      totalCases: 11,
      firstCasesCount: 2,
      completedCases: 0,
      firstCaseOnTimePercent: 0.5,
      firstCasesWithinFiveMinutesPercent: 0.5,
      urgentCasesCount: 1,
      addOnCasesCount: 1,
      afterHoursCasesCount: 2,
      showFirstCases: true,
      firstCaseStarts: ['garage_0', 'garage_1'],
      openToday: ['garage_0', 'garage_1'],
      stillOpen: ['garage_0', 'garage_1'],
      openPast15: ['garage_0', 'garage_1'],
      openPast17: ['garage_1'],
      openPast19: ['garage_1'],
    })
  })

  it('should calculate still open correctly after 3pm', () => {
    mockedUseQuery.mockReturnValue(
      mockUseQueryResult({ data: mockApellaCases })
    )
    mockedUseCurrentMinute.mockReturnValue(
      DateTime.fromISO('2023-09-01T15:01:00-07:00')
    )

    const { result } = renderHook(
      () =>
        useApellaCaseDailyMetrics({
          minTime: '2023-09-01T00:00:00.000-07:00',
          maxTime: '2023-09-01T16:59:59.999-07:00',
          siteIds: ['lab_1'],
        }),
      { wrapper: MockAppProvider }
    )

    expect(result.current.isLoading).toBeFalsy()
    expect(result.current.results.stillOpen).toEqual(['garage_0', 'garage_1'])
  })

  it('should calculate still open correctly after 5pm', () => {
    mockedUseQuery.mockReturnValue(
      mockUseQueryResult({ data: mockApellaCases })
    )
    mockedUseCurrentMinute.mockReturnValue(
      DateTime.fromISO('2023-09-01T17:01:00-07:00')
    )

    const { result } = renderHook(
      () =>
        useApellaCaseDailyMetrics({
          minTime: '2023-09-01T00:00:00.000-07:00',
          maxTime: '2023-09-01T16:59:59.999-07:00',
          siteIds: ['lab_1'],
        }),
      { wrapper: MockAppProvider }
    )

    expect(result.current.isLoading).toBeFalsy()

    expect(result.current.results.stillOpen).toEqual(['garage_1'])
  })

  it('should calculate still open correctly after 7pm', () => {
    mockedUseQuery.mockReturnValue(
      mockUseQueryResult({ data: mockApellaCases })
    )
    mockedUseCurrentMinute.mockReturnValue(
      DateTime.fromISO('2023-09-01T19:01:00-07:00')
    )

    const { result } = renderHook(
      () =>
        useApellaCaseDailyMetrics({
          minTime: '2023-09-01T00:00:00.000-07:00',
          maxTime: '2023-09-01T16:59:59.999-07:00',
          siteIds: ['lab_1'],
        }),
      { wrapper: MockAppProvider }
    )

    expect(result.current.isLoading).toBeFalsy()

    expect(result.current.results.stillOpen).toEqual(['garage_1'])
  })

  it('should return undefined results if query is loading', () => {
    mockedUseQuery.mockReturnValue(
      mockUseQueryResult({ data: mockApellaCases, loading: true })
    )
    mockedUseCurrentMinute.mockReturnValue(
      DateTime.fromISO('2023-09-01T09:00:00-07:00')
    )

    const { result } = renderHook(
      () =>
        useApellaCaseDailyMetrics({
          minTime: '2023-09-01T00:00:00.000-07:00',
          maxTime: '2023-09-01T16:59:59.999-07:00',
          siteIds: ['lab_1'],
        }),
      { wrapper: MockAppProvider }
    )

    expect(result.current.isLoading).toBeTruthy()
    expect(result.current.results).toEqual({})
  })
})

export {}
