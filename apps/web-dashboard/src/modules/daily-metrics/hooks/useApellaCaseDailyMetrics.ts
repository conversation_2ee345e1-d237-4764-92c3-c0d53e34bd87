import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { CaseType } from 'src/__generated__/globalTypes'
import { useTimezone } from 'src/Contexts'
import {
  GetDailyMetricCaseData,
  GetDailyMetricCaseDataVariables,
} from 'src/modules/daily-metrics/queries/__generated__'
import { GET_DAILY_METRICS_CASE_DATA } from 'src/modules/daily-metrics/queries/caseData'
import { DailyMetrics } from 'src/modules/daily-metrics/types'
import { usePolling } from 'src/utils/usePolling'

import { InsightApellaCase, useScheduleMetrics } from './useScheduleMetrics'

const URGENT_CASE_CLASSIFICATIONS = [
  'CASE_CLASSIFICATION_EMERGENT',
  'CASE_CLASSIFICATION_URGENT',
  'CASE_CLASSIFICATION_LEVEL1',
  'CASE_CLASSIFICATION_LEVEL2',
  'CASE_CLASSIFICATION_LEVEL3',
]

export const toInsightApellaCase = (
  apellaCase: GetDailyMetricCaseData['apellaCases']['edges'][number]['node']
): InsightApellaCase => ({
  endTime: apellaCase.endTime
    ? DateTime.fromISO(apellaCase.endTime)
    : undefined,
  room: {
    id: apellaCase.room.id,
  },
  case: apellaCase.case
    ? {
        isFirstCase: apellaCase.case.isFirstCase || false,
        scheduledStartTime: DateTime.fromISO(
          apellaCase.case.scheduledStartTime
        ),
      }
    : undefined,
  type: apellaCase.type,
})

export const useApellaCaseDailyMetrics = ({
  minTime,
  maxTime,
  staffIds,
  roomIds,
  siteIds,
  pollingInterval = 0,
}: {
  minTime: string
  maxTime: string
  staffIds?: string[]
  roomIds?: string[]
  siteIds: string[]
  pollingInterval?: number
}) => {
  const { timezone } = useTimezone()

  const {
    loading: isLoading,
    data,
    refetch,
    startPolling,
    stopPolling,
  } = useQuery<GetDailyMetricCaseData, GetDailyMetricCaseDataVariables>(
    GET_DAILY_METRICS_CASE_DATA,
    {
      variables: {
        minEndTime: minTime,
        maxStartTime: maxTime,
        siteIds: siteIds && siteIds.length ? siteIds : undefined,
        roomIds: roomIds && roomIds.length ? roomIds : undefined,
        staffIds: staffIds && staffIds.length ? staffIds : undefined,
      },
      skip: !siteIds.length,
    }
  )

  usePolling({
    refetch,
    startPolling,
    stopPolling,
    skip: !pollingInterval,
    interval: pollingInterval,
  })

  const apellaCases = data?.apellaCases.edges.flatMap(({ node }) =>
    toInsightApellaCase(node)
  )

  const hasPendingFirstCase = apellaCases?.some(
    (apellaCase) =>
      apellaCase?.case?.isFirstCase && apellaCase.type === CaseType.FORECAST
  )

  const scheduleMetrics = useScheduleMetrics({
    minTime,
    apellaCases,
  })

  const fivePM = DateTime.fromISO(minTime)
    .setZone(timezone)
    .set({ hour: 17 })
    .startOf('hour')

  const firstCases = data?.apellaCases.edges.filter(
    ({ node }) =>
      node.case?.isFirstCase &&
      node.case?.scheduledStartTime &&
      node.type !== CaseType.FORECAST
  )

  const firstCaseCount = firstCases?.length || 0

  const onTimeCases = firstCases?.filter(
    ({ node }) =>
      node.case?.scheduledStartTime &&
      node.startTime &&
      DateTime.fromISO(node.startTime)
        .setZone(timezone)
        .diff(DateTime.fromISO(node.case?.scheduledStartTime).setZone(timezone))
        .as('minutes') < 1
  )

  const withinFiveMinutesPercentCases = firstCases?.filter(
    ({ node }) =>
      node.case?.scheduledStartTime &&
      node.startTime &&
      DateTime.fromISO(node.startTime)
        .diff(DateTime.fromISO(node.case?.scheduledStartTime))
        .as('minutes') < 6
  )

  const firstCaseOnTime = firstCaseCount
    ? (onTimeCases?.length ?? 0) / firstCaseCount
    : undefined

  const withinFiveMinutesPercent = firstCaseCount
    ? (withinFiveMinutesPercentCases?.length ?? 0) / firstCaseCount
    : undefined

  const afterHoursCases = data?.apellaCases.edges.filter(
    ({ node }) =>
      node.endTime &&
      (DateTime.fromISO(node.endTime).setZone(timezone).day >
        DateTime.fromISO(node.startTime).setZone(timezone).day ||
        DateTime.fromISO(node.endTime).setZone(timezone) > fivePM ||
        DateTime.fromISO(node.startTime).setZone(timezone) > fivePM)
  )

  const results: Partial<DailyMetrics> = isLoading
    ? {}
    : {
        firstCasesCount: firstCaseCount,
        firstCaseOnTimePercent: firstCaseOnTime,
        firstCasesWithinFiveMinutesPercent: withinFiveMinutesPercent,
        urgentCasesCount:
          data?.apellaCases.edges.filter(({ node }) =>
            URGENT_CASE_CLASSIFICATIONS.includes(
              node.case?.caseClassificationType?.id ?? ''
            )
          ).length || 0,
        addOnCasesCount: data?.apellaCases.edges.filter(
          ({ node }) => node.case?.isAddOn
        ).length,
        afterHoursCasesCount: afterHoursCases?.length || 0,
        showFirstCases: !hasPendingFirstCase,
        ...scheduleMetrics,
      }

  return {
    isLoading,
    results,
  }
}
