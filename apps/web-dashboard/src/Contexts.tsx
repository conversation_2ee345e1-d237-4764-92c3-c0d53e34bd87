import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react'
import { useSearchParams } from 'react-router'

import { DateTime } from 'luxon'

import { ChildrenProps } from '@apella/component-library'

import { EVENTS, useAnalyticsEventLogger } from './utils/analyticsEvents'
import { getCleanedUrlParam } from './utils/getCleanedUrlParam'

interface SiteWithTz {
  id: string
  timezone: string
}

interface TimezoneState {
  hasMultiple: boolean
  timezone: string
}

interface ITimezoneContext extends TimezoneState {
  updateTimezoneState: (sites?: SiteWithTz[]) => void
}
export const defaultTimezone = 'America/Los_Angeles'
const defaultTimezoneContext = {
  timezone: defaultTimezone,
  hasMultiple: false,
  updateTimezoneState: () => {},
}
const TimezoneContext = createContext<ITimezoneContext>(defaultTimezoneContext)
export const useTimezone = () => useContext(TimezoneContext)

export const TimezoneProvider = ({
  children,
  orgDefaultTimezone,
}: { orgDefaultTimezone?: string } & ChildrenProps) => {
  const [timezoneState, setTimezoneState] = useState<TimezoneState | undefined>(
    undefined
  )

  const timezoneStateValue = timezoneState ?? {
    timezone: orgDefaultTimezone ?? defaultTimezone,
    hasMultiple: false,
  }

  const updateTimezoneState = (sitesWithTz?: SiteWithTz[]) => {
    const newTimezones = new Set(sitesWithTz?.map((s) => s.timezone))

    // Arbitrarily choose the alphabetically first timezone
    const timezoneToUse = [...newTimezones].sort()[0]
    const newHasMultiple = newTimezones.size > 1
    if (
      timezoneToUse &&
      (timezoneToUse !== timezoneState?.timezone ||
        newHasMultiple !== timezoneState?.hasMultiple)
    ) {
      setTimezoneState({
        timezone: timezoneToUse,
        hasMultiple: newHasMultiple,
      })
    }
  }

  return (
    <TimezoneContext.Provider
      value={{ ...timezoneStateValue, updateTimezoneState }}
    >
      {children}
    </TimezoneContext.Provider>
  )
}

export const IsTouchDeviceContext = createContext(false)
export const useTouchDeviceContext = () => useContext(IsTouchDeviceContext)

interface IsTvModeEnabledContextShape {
  isTvModeEnabled: boolean
  setIsTvModeEnabled?: (newValue: boolean) => void
}
const IsTvModeEnabledContext = createContext<IsTvModeEnabledContextShape>({
  isTvModeEnabled: false,
  setIsTvModeEnabled: undefined,
})

const DISPLAY_MODE_PARAM = 'displayMode'

export enum DISPLAY_MODE {
  TV = 'tv',
}

export const DisplayModeProvider = ({
  children,
  pageDisplayMode,
}: ChildrenProps & { pageDisplayMode?: DISPLAY_MODE }) => {
  const [isTvModeEnabled, setIsTvModeEnabled] = useState(
    pageDisplayMode === DISPLAY_MODE.TV
  )
  const [tvModeEnteredTime, setTvModeEnteredTime] = useState<
    DateTime | undefined
  >()

  useEffect(
    () => setIsTvModeEnabled(pageDisplayMode === DISPLAY_MODE.TV),
    [pageDisplayMode]
  )

  const eventsLogger = useAnalyticsEventLogger()

  useEffect(() => {
    const now = DateTime.now()
    if (isTvModeEnabled) {
      eventsLogger(EVENTS.ENTER_TV_MODE, { type: 'TvMode' })
      setTvModeEnteredTime(now)
    } else if (tvModeEnteredTime) {
      eventsLogger(EVENTS.EXIT_TV_MODE, {
        secondsTvMode: now.diff(tvModeEnteredTime).as('seconds'),
        type: 'TvMode',
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTvModeEnabled, eventsLogger])

  return (
    <IsTvModeEnabledContext.Provider
      value={{
        isTvModeEnabled,
        setIsTvModeEnabled:
          pageDisplayMode !== DISPLAY_MODE.TV ? setIsTvModeEnabled : undefined,
      }}
    >
      {children}
    </IsTvModeEnabledContext.Provider>
  )
}

export const useDisplayMode = () => {
  const { isTvModeEnabled, setIsTvModeEnabled } = useContext(
    IsTvModeEnabledContext
  )

  const [searchParams, setSearchParams] = useSearchParams()

  const displayModeParam = getCleanedUrlParam<DISPLAY_MODE | undefined>({
    searchParams,
    key: DISPLAY_MODE_PARAM,
    defaultValue: undefined,
  })

  useEffect(() => {
    if (setIsTvModeEnabled) {
      setIsTvModeEnabled(displayModeParam === DISPLAY_MODE.TV)
    }
  }, [displayModeParam, setIsTvModeEnabled])

  const onClick = useCallback(() => {
    if (isTvModeEnabled) {
      searchParams.delete(DISPLAY_MODE_PARAM)
    } else {
      searchParams.set(DISPLAY_MODE_PARAM, JSON.stringify(DISPLAY_MODE.TV))
    }
    setSearchParams(searchParams)
  }, [isTvModeEnabled, searchParams, setSearchParams])

  return {
    isTvModeEnabled: isTvModeEnabled,
    toggleTvMode: setIsTvModeEnabled ? onClick : undefined,
  }
}

export const ShowVideoContext = createContext<boolean | 0>(true)
