import { DateTime, DurationLike } from 'luxon'

import { TurnoverType } from 'src/__generated__/globalTypes'
import {
  TurnoverLengthWithGoal,
  TurnoverLengthWithoutGoal,
} from 'src/utils/status'
import {
  calculateTurnoverLengthStatus,
  getAnalyticsAddtlTurnoverData,
} from 'src/utils/turnovers'

describe('turnovers', () => {
  describe('getAnalyticsAddtlTurnoverData', () => {
    beforeEach(() => {
      vi.useFakeTimers({ now: new Date(2021, 1, 1, 0, 30) })
    })
    afterEach(() => {
      vi.useRealTimers()
    })
    it('should return the correct data for a live turnover', () => {
      const turnover = {
        overallLengthStatus: TurnoverLengthWithGoal.UNDER_GOAL,
        currentLengthStatus: TurnoverLengthWithGoal.OVER_GOAL,
        type: TurnoverType.LIVE,
        startTime: DateTime.fromJSDate(new Date(2021, 1, 1, 0, 0)),
        endTime: DateTime.fromJSDate(new Date(2021, 1, 1, 1, 0)),
      }

      const result = getAnalyticsAddtlTurnoverData(turnover)

      expect(result).toEqual({
        turnoverLengthSeconds: 3600,
        turnoverOverallLengthStatus: TurnoverLengthWithGoal.UNDER_GOAL,
        turnoverType: TurnoverType.LIVE,
        turnoverPercentDone: 50,
        turnoverCurrentLengthStatus: TurnoverLengthWithGoal.OVER_GOAL,
        turnoverTimeElapsedSeconds: 1800,
      })
    })
    it('should return the correct data for a non-live turnover', () => {
      const turnover = {
        overallLengthStatus: TurnoverLengthWithGoal.UNDER_GOAL,
        currentLengthStatus: TurnoverLengthWithGoal.OVER_GOAL,
        type: TurnoverType.COMPLETE,
        startTime: DateTime.fromJSDate(new Date(2021, 1, 1, 0, 0)),
        endTime: DateTime.fromJSDate(new Date(2021, 1, 1, 1, 0)),
      }

      const result = getAnalyticsAddtlTurnoverData(turnover)

      expect(result).toEqual({
        turnoverLengthSeconds: 3600,
        turnoverOverallLengthStatus: TurnoverLengthWithGoal.UNDER_GOAL,
        turnoverType: TurnoverType.COMPLETE,
      })
    })
  })
})

describe('calculateTurnoverLengthStatus', () => {
  const anchorDate = DateTime.fromJSDate(new Date(2021, 1, 1, 5, 0))

  const withoutGoal = {
    maxMinutes: 90,
    goalMinutes: null,
  }

  const withGoal = {
    maxMinutes: 90,
    goalMinutes: 30,
  }

  const runTest = (
    duration: DurationLike,
    expectedResult: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal,
    hasGoal: boolean = false
  ) => {
    const result = calculateTurnoverLengthStatus(
      anchorDate,
      anchorDate.plus(duration),
      hasGoal ? withGoal : withoutGoal
    )
    expect(result).toBe(expectedResult)
  }

  describe('without a turnover goal', () => {
    it('shows under max if the length is less than max', () => {
      runTest({ minutes: 45 }, TurnoverLengthWithoutGoal.UNDER_MAX)
    })

    it('shows under max if less than a minute over max', () => {
      runTest({ minutes: 90, seconds: 59 }, TurnoverLengthWithoutGoal.UNDER_MAX)
    })

    it('shows over max if a minute over max', () => {
      runTest({ minutes: 91 }, TurnoverLengthWithoutGoal.OVER_MAX)
    })
  })

  describe('with a turnover goal', () => {
    it('shows under goal if the length is less than goal', () => {
      runTest({ minutes: 20 }, TurnoverLengthWithGoal.UNDER_GOAL, true)
    })

    it('shows under goal if the length is less than a minute over goal', () => {
      runTest(
        { minutes: 30, seconds: 59 },
        TurnoverLengthWithGoal.UNDER_GOAL,
        true
      )
    })

    it('shows over goal if the length is more than a minute over the goal', () => {
      runTest({ minutes: 31 }, TurnoverLengthWithGoal.OVER_GOAL, true)
    })

    it('shows over goal if less than a minute over max', () => {
      runTest(
        { minutes: 90, seconds: 59 },
        TurnoverLengthWithGoal.OVER_GOAL,
        true
      )
    })

    it('shows over max if a minute over max', () => {
      runTest({ minutes: 91 }, TurnoverLengthWithGoal.OVER_MAX, true)
    })
  })
})
