import { useFlags } from 'launchdarkly-react-client-sdk'

import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import { logger } from 'src/utils/exceptionLogging'

export const usePatientReadAccess = () => {
  /**
   * This hook is used to determine if the current user has access to read patient data.
   * Once the Feature flag is fully rolled out, this hook abstraction won't be
   * needed.
   */
  const { permissions, userId } = useCurrentUser()
  const { showPatientData: showPatientDataFeatureFlag = false } =
    useFlags<WebDashboardFeatureFlagSet>()
  if (showPatientDataFeatureFlag && !permissions?.patientReadEnabled) {
    logger.warn(
      'Patient data feature flag enabled but no permissions found.',
      {
        featureFlagValue: showPatientDataFeatureFlag,
        permissionValue: permissions?.patientReadEnabled,
      },
      userId
    )
  }
  return showPatientDataFeatureFlag && !!permissions?.patientReadEnabled
}
