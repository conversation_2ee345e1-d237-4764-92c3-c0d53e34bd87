import { useCallback } from 'react'
import { useLocation, useNavigate, useSearchParams } from 'react-router'

import { DateTime } from 'luxon'

import { CaseType } from '../__generated__/globalTypes'
import { useTimezone } from '../Contexts'
import { ApellaCase } from '../pages/types'
import { LocationPath } from '../router/types'
import { EVENTS, useAnalyticsEventLogger } from './analyticsEvents'
import { useCurrentMinute } from './useCurrentTime'

const VIDEO_START_BUFFER_HRS = 1
const VIDEO_END_BUFFER_HRS = 3
// If a case or turnover starts or ends close to an hour boundary, we should
// extend the video blade time window to include another hour. This buffer is
// how close to the hour boundary we need to be to add the extra hour.
const CASE_OR_TURNOVER_VIDEO_BUFFER_MINS = 30

type CaseBladeInfo = Pick<ApellaCase, 'endTime' | 'id' | 'startTime' | 'type'>
export type TurnoverBladeInfo = Omit<CaseBladeInfo, 'type'>

type OpenVideoBladeParams = {
  appendParams?: boolean
}
export const useOpenVideoBlade = (
  { appendParams = false }: OpenVideoBladeParams = { appendParams: false }
) => {
  const now = useCurrentMinute()
  const navigate = useNavigate()
  const location = useLocation()
  const [currentParams] = useSearchParams()
  const eventsLogger = useAnalyticsEventLogger()
  const { timezone } = useTimezone()

  return useCallback(
    (
      roomId: string,
      options: {
        apellaCase?: CaseBladeInfo
        turnover?: TurnoverBladeInfo
        time?: DateTime
        startBufferHrs?: number
        endBufferHrs?: number
        analyticsEvent?: EVENTS
        analyticsAddtlData?: Record<string, any>
      }
    ) => {
      const {
        apellaCase,
        turnover,
        time,
        startBufferHrs,
        endBufferHrs,
        analyticsEvent,
        analyticsAddtlData,
      } = options

      const params = appendParams ? currentParams : new URLSearchParams()
      params.append('roomId', roomId)
      if (apellaCase && apellaCase.endTime) {
        const { id: apellaCaseId, startTime, endTime } = apellaCase

        params.append(
          'startTime',
          startTime
            .setZone(timezone)
            .minus({
              minutes: startBufferHrs ? 0 : CASE_OR_TURNOVER_VIDEO_BUFFER_MINS,
            })
            .startOf('hour')
            .minus({ hours: startBufferHrs ?? 0 })
            .toISO({ suppressMilliseconds: true })
        )
        params.append(
          'endTime',
          endTime
            .setZone(timezone)
            .plus({
              hour: 1,
              minutes: endBufferHrs ? 0 : CASE_OR_TURNOVER_VIDEO_BUFFER_MINS,
            })
            .startOf('hour')
            .plus({ hours: endBufferHrs ?? 0 })
            .toISO({ suppressMilliseconds: true })
        )
        if (apellaCase.type !== CaseType.LIVE) {
          params.append(
            'time',
            startTime.setZone(timezone).toISO({ suppressMilliseconds: true })
          )
        }
        params.append('apellaCaseId', apellaCaseId)
      } else if (turnover && turnover.endTime) {
        const { id: turnoverId, startTime, endTime } = turnover

        params.append(
          'startTime',
          startTime
            .setZone(timezone)
            .minus({
              minutes: startBufferHrs ? 0 : CASE_OR_TURNOVER_VIDEO_BUFFER_MINS,
            })
            .startOf('hour')
            .minus({ hours: startBufferHrs ?? 0 })
            .toISO({ suppressMilliseconds: true })
        )
        params.append(
          'endTime',
          endTime
            .setZone(timezone)
            .plus({
              hour: 1,
              minutes: endBufferHrs ? 0 : CASE_OR_TURNOVER_VIDEO_BUFFER_MINS,
            })
            .startOf('hour')
            .plus({ hours: endBufferHrs ?? 0 })
            .toISO({ suppressMilliseconds: true })
        )
        if (endTime < now) {
          params.append(
            'time',
            startTime.setZone(timezone).toISO({ suppressMilliseconds: true })
          )
        }
        params.append('turnoverId', turnoverId)
      } else if (!!time) {
        params.append(
          'startTime',
          time
            .setZone(timezone)
            .startOf('hour')
            .minus({ hours: startBufferHrs ?? VIDEO_START_BUFFER_HRS })
            .toISO({ suppressMilliseconds: true })
        )
        params.append(
          'endTime',
          time
            .setZone(timezone)
            .startOf('hour')
            .plus({ hours: endBufferHrs ?? VIDEO_END_BUFFER_HRS })
            .toISO({ suppressMilliseconds: true })
        )
        params.append('time', time.toISO({ suppressMilliseconds: true }))
      } else {
        params.append(
          'startTime',
          now
            .setZone(timezone)
            .startOf('hour')
            .minus({ hours: startBufferHrs ?? VIDEO_START_BUFFER_HRS })
            .toISO({ suppressMilliseconds: true })
        )
        params.append(
          'endTime',
          now
            .setZone(timezone)
            .startOf('hour')
            .plus({ hours: endBufferHrs ?? VIDEO_END_BUFFER_HRS })
            .toISO({ suppressMilliseconds: true })
        )
      }

      if (analyticsEvent) {
        eventsLogger(analyticsEvent, {
          startTime: params.get('startTime'),
          endTime: params.get('endTime'),
          apellaCaseId: apellaCase?.id,
          turnoverId: turnover?.id,
          roomId,
          ...analyticsAddtlData,
        })
      }

      const state = { background: location }

      navigate(
        {
          pathname: LocationPath.VideoView,
          search: params.toString(),
        },
        { state }
      )
    },
    [
      appendParams,
      currentParams,
      location,
      navigate,
      timezone,
      now,
      eventsLogger,
    ]
  )
}
