// TODO(INS-694): Categorize and model roles in DB

import { Staff } from '../pages/types'

const byRole = (a: Staff, b: Staff) => a.role.localeCompare(b.role)

export const PRIMARY_SURGEON_ROLES = [
  'Primary Surgeon',
  'Primary',
  'Physician',
  'Surgeon',
  'Surgeon 1',
]
export const CRNA_ROLES = ['CRNA', 'CRNA Student']
export const ANESTHESIOLOGIST_ROLES = [
  'Anesthesia Resident',
  'Primary Anesthesiologist',
  'Anesthesiologist',
]
export const ANESTHESIA_ROLES = ANESTHESIOLOGIST_ROLES.concat(CRNA_ROLES)

// values duplicated in:
// https://github.com/Apella-Technology/cubejs/blob/main/src/model/CaseStaffCirculator.js
export const CIRCULATOR_ROLES = ['Circulator']

// values duplicated in:
// https://github.com/Apella-Technology/cubejs/blob/main/src/model/CaseStaffScrubTech.js
export const SCRUB_TECH_ROLES = ['Scrub Person', 'Surg Tech', 'Scrub Tech']

export enum CaseStaffPlanRole {
  Anesthesiologist = 'anesthesiologist',
  CRNA = 'crna',
  Circulator = 'circulator',
  ScrubTech = 'scrub_tech',
}

const MAX_NUM_ROLES = 4

export const StaffRoleTranslations: Partial<{ [key: string]: string }> & {
  [key in CaseStaffPlanRole]: string
} = {
  [CaseStaffPlanRole.CRNA]: 'CRNA',
  [CaseStaffPlanRole.Circulator]: 'Circulator',
  [CaseStaffPlanRole.ScrubTech]: 'Scrub Tech',
  [CaseStaffPlanRole.Anesthesiologist]: 'Anesthesiologist',
} as const

export interface CaseWithStaff {
  staff: Staff[]
  staffPlan?: Staff[]
}

type StaffSource = 'ehr' | 'plan'

export const toRoleShortForm = (role: CaseStaffPlanRole | string) => {
  return role
    .split(' ')
    .map((rolePiece) =>
      rolePiece.length > 4 ? rolePiece.substring(0, 3) : rolePiece
    )
    .join(' ')
}

export const toStaffRoleDisplayName = (role: CaseStaffPlanRole | string) =>
  StaffRoleTranslations[role] || role

export const toStaffFullName = ({
  staff,
  displayLastNameFirst,
  shorten = false,
}: {
  staff: {
    firstName: string
    lastName: string
  }
  displayLastNameFirst?: boolean
  shorten?: boolean
}) => {
  const firstName = shorten ? `${staff.firstName.charAt(0)}.` : staff.firstName
  return displayLastNameFirst
    ? `${staff.lastName}, ${firstName}`
    : `${firstName} ${staff.lastName}`
}

export const isPrimarySurgeon = (staff: Pick<Staff, 'role'>) => {
  const primarySurgeonRoles = new Set(
    PRIMARY_SURGEON_ROLES.map((r) => r.toLowerCase())
  )
  return primarySurgeonRoles.has(staff.role.toLowerCase())
}

const filterStaff = (
  caseObj: CaseWithStaff,
  prefer: StaffSource = 'ehr',
  pred: (s: Staff) => boolean
) => getAllStaff(caseObj, prefer).filter(pred).sort(byRole)

export const getPrimarySurgeons = (caseObj: CaseWithStaff): Staff[] =>
  filterStaff(caseObj, 'ehr', isPrimarySurgeon)

export const getPrimarySurgeonText = (
  caseObj: CaseWithStaff,
  useDisplayName = false
) =>
  getPrimarySurgeons(caseObj)
    .map((s) =>
      useDisplayName ? s.displayName : `${s.lastName}, ${s.firstName}`
    )
    .join('; ')

export const getCRNAStaff = (
  caseObj: CaseWithStaff,
  prefer: StaffSource = 'ehr'
): Staff[] => {
  const crnaRoles = new Set(CRNA_ROLES.map((r) => r.toLowerCase()))
  return filterStaff(caseObj, prefer, (s) =>
    crnaRoles.has(s.role.toLowerCase())
  )
}

export const getCirculatorStaff = (
  caseObj: CaseWithStaff,
  prefer: StaffSource = 'ehr'
): Staff[] =>
  filterStaff(
    caseObj,
    prefer,
    (s) => CaseStaffPlanRole.Circulator.toLowerCase() === s.role.toLowerCase()
  )

export const getScrubTechStaff = (
  caseObj: CaseWithStaff,
  prefer: StaffSource = 'ehr'
): Staff[] =>
  filterStaff(
    caseObj,
    prefer,
    (s) => CaseStaffPlanRole.ScrubTech.toLowerCase() === s.role.toLowerCase()
  )

export const getAnesthesiologistStaff = (
  caseObj: CaseWithStaff,
  prefer: StaffSource = 'ehr'
): Staff[] => {
  const anesthesiologistRoles = new Set(
    ANESTHESIOLOGIST_ROLES.map((r) => r.toLowerCase())
  )
  return filterStaff(caseObj, prefer, (s) =>
    anesthesiologistRoles.has(s.role.toLowerCase())
  )
}

export const getAnesthesiaStaff = (
  caseObj: CaseWithStaff,
  prefer: StaffSource = 'ehr'
): Staff[] => {
  const anesthesiaRoles = new Set(ANESTHESIA_ROLES.map((r) => r.toLowerCase()))
  return filterStaff(caseObj, prefer, (s) =>
    anesthesiaRoles.has(s.role.toLowerCase())
  )
}

export const getORStaff = (
  caseObj: CaseWithStaff,
  prefer: StaffSource = 'ehr'
): Staff[] => {
  const nonORStaffRoles = new Set(
    ANESTHESIA_ROLES.concat(PRIMARY_SURGEON_ROLES).map((r) => r.toLowerCase())
  )
  return filterStaff(
    caseObj,
    prefer,
    (s) => !nonORStaffRoles.has(s.role.toLowerCase())
  )
}

export const getAllStaff = (
  caseObj: CaseWithStaff,
  prefer: StaffSource
): Staff[] => {
  const staffPlan = caseObj?.staffPlan ?? []

  const preferredStaff = prefer === 'ehr' ? caseObj.staff : staffPlan
  const secondaryStaff = prefer === 'ehr' ? staffPlan : caseObj.staff

  const preferredStaffIds = new Set(preferredStaff.map((s) => s.id))
  const dedupedSecondaryStaff = secondaryStaff.filter(
    (sp) => !preferredStaffIds.has(sp.id)
  )

  return preferredStaff.concat(dedupedSecondaryStaff)
}

export const getStaffMembersByRole = (
  caseWithStaff?: CaseWithStaff
): { role: string; staff: Staff[] }[] => {
  if (!caseWithStaff) {
    return [
      { role: CaseStaffPlanRole.Anesthesiologist, staff: [] },
      { role: CaseStaffPlanRole.CRNA, staff: [] },
      { role: CaseStaffPlanRole.Circulator, staff: [] },
      { role: CaseStaffPlanRole.ScrubTech, staff: [] },
    ]
  }

  const result: { role: string; staff: Staff[] }[] = []
  const unusedRoles = []

  // First look for Anesthesiologist, CRNA, Circulator, and Scrub Tech staff members, in that order
  const anesthesiologistStaff = getAnesthesiologistStaff(caseWithStaff, 'plan')
  if (anesthesiologistStaff.length) {
    result.push({
      role: CaseStaffPlanRole.Anesthesiologist,
      staff: anesthesiologistStaff,
    })
  } else {
    unusedRoles.push(CaseStaffPlanRole.Anesthesiologist)
  }

  const crnaStaff = getCRNAStaff(caseWithStaff, 'plan')
  if (crnaStaff.length) {
    result.push({
      role: CaseStaffPlanRole.CRNA,
      staff: crnaStaff,
    })
  } else {
    unusedRoles.push(CaseStaffPlanRole.CRNA)
  }

  const circulatorStaff = getCirculatorStaff(caseWithStaff, 'plan')
  if (circulatorStaff.length) {
    result.push({
      role: CaseStaffPlanRole.Circulator,
      staff: circulatorStaff,
    })
  } else {
    unusedRoles.push(CaseStaffPlanRole.Circulator)
  }

  const scrubTechStaff = getScrubTechStaff(caseWithStaff, 'plan')
  if (scrubTechStaff.length) {
    result.push({
      role: CaseStaffPlanRole.ScrubTech,
      staff: scrubTechStaff,
    })
  } else {
    unusedRoles.push(CaseStaffPlanRole.ScrubTech)
  }

  // If there are still remaining staff with uncategorized roles, add those
  const usedStaffIds = anesthesiologistStaff
    .concat(crnaStaff, circulatorStaff, scrubTechStaff)
    .map((s) => s.id)
  const primarySurgeonIds = getPrimarySurgeons(caseWithStaff).map((s) => s.id)
  const remainingStaff = getAllStaff(caseWithStaff, 'plan').filter(
    (s) => !usedStaffIds.includes(s.id) && !primarySurgeonIds.includes(s.id)
  )

  for (const staff of remainingStaff) {
    if (result.length >= MAX_NUM_ROLES) {
      break
    }

    result.push({
      role: staff.role,
      staff: [staff],
    })
  }

  // If there are no more staff, add the roles with no staff mapped to them
  for (const role of unusedRoles) {
    if (result.length >= MAX_NUM_ROLES) {
      break
    }

    result.push({
      role,
      staff: [],
    })
  }

  return result
}
