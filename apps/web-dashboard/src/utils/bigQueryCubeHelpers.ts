import { Duration, DurationLikeObject } from 'luxon'

function getSign(match: string): number {
  return match === '-' ? -1 : 1
}

function getInt(match: string): number {
  return parseInt(match, 10) || 0
}

function durationfromString(duration: string): Duration {
  // https://cloud.google.com/bigquery/docs/reference/standard-sql/data-types#canonical_format_3
  const regex = /(-)?(\d+)-(\d+) (-)?(\d+) (-)?(\d+):(\d+):(\d+\.?\d*)/
  const match = duration.match(regex)
  if (!match) {
    throw new Error(`Could not parse string interval ${duration}`)
  }
  const yearMonthSign = getSign(match[1])
  const years = getInt(match[2])
  const months = getInt(match[3])
  const daySign = getSign(match[4])
  const days = getInt(match[5])
  const timeSign = getSign(match[6])
  const hours = parseInt(match[7], 10) || 0
  const minutes = parseInt(match[8], 10) || 0
  const seconds = parseFloat(match[9]) || 0
  const durationObj = {
    years: yearMonthSign * years,
    months: yearMonthSign * months,
    days: daySign * days,
    hours: timeSign * hours,
    minutes: timeSign * minutes,
    seconds: timeSign * seconds,
  }
  return Duration.fromObject(durationObj)
}

export function durationFromObjectOrString(
  duration: DurationLikeObject | string | null | undefined
): Duration {
  if (duration === null || typeof duration === 'undefined') {
    return Duration.fromObject({})
  } else if (typeof duration === 'string') {
    return durationfromString(duration)
  } else {
    return Duration.fromObject(duration)
  }
}

export function durationFromMinutes(
  duration: DurationLikeObject | number
): Duration {
  if (typeof duration === 'number') {
    return Duration.fromObject({ minutes: duration })
  }
  return Duration.fromObject(duration)
}
