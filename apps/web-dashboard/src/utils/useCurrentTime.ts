import { useEffect, useState } from 'react'

import { DateTime } from 'luxon'

import { useTimezone } from '../Contexts'

export const useCurrentTime = (intervalMs = 1000): DateTime => {
  const [currentTime, setCurrentTime] = useState(DateTime.now())

  useEffect(() => {
    const intervalId = setInterval(
      () => setCurrentTime(DateTime.now()),
      intervalMs
    )

    return () => clearInterval(intervalId)
  }, [intervalMs])

  return currentTime
}

// This is a variation of the above that updates the currentTime state
// at the millisecond the minute updates. It sets a timeout when first rendered
// with the remaining milliseconds in the current minute, and then sets a 60s
// interval after that. This way the minute always reflects the clock without
// delay.
export const useCurrentMinute = (skip = false): DateTime => {
  const [currentTime, setCurrentTime] = useState(DateTime.now())

  useEffect(() => {
    if (skip) {
      return
    }

    const now = DateTime.now()
    const endOfMinute = now.endOf('minute')
    const msUntilNextMinute = endOfMinute.diff(now).as('milliseconds')
    let intervalId: number | undefined

    const timeoutId = setTimeout(() => {
      setCurrentTime(DateTime.now())
      intervalId = window.setInterval(
        () => setCurrentTime(DateTime.now()),
        60 * 1000
      )
    }, msUntilNextMinute)

    return () => {
      clearTimeout(timeoutId)
      clearInterval(intervalId)
    }
  }, [skip])

  return currentTime
}

export const useIsToday = (timeToCheck: DateTime): boolean => {
  const currentMinute = useCurrentMinute()
  const { timezone } = useTimezone()

  return (
    timeToCheck >= currentMinute.setZone(timezone).startOf('day') &&
    timeToCheck <
      currentMinute.setZone(timezone).plus({ day: 1 }).startOf('day')
  )
}
