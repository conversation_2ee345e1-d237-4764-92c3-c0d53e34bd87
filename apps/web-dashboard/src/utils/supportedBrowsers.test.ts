import { isBrowserSupported } from './supportedBrowsers'

// These user agents are taken from:
// https://www.whatismybrowser.com/guides/the-latest-user-agent/?utm_source=whatismybrowsercom&utm_medium=internal&utm_campaign=breadcrumbs
describe('isBrowserSupported', () => {
  const run = (ua: string, expected: boolean): void => {
    const result = isBrowserSupported(ua)
    expect(result).toBe(expected)
  }

  test('passes for a Chrome user agent', () => {
    run(
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36',
      true
    )
  })

  test('passes for a Firefox user agent', () => {
    run(
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 11.6; rv:93.0) Gecko/20100101 Firefox/93.0',
      true
    )
  })

  test('passes for an Edge user agent', () => {
    run(
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 11_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36 Edg/94.0.992.31',
      true
    )
  })

  test('passes for a Safari user agent', () => {
    run(
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 11_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15',
      true
    )
  })

  test('fails for an IE11 user agent', () => {
    run('Mozilla/5.0 (Windows NT 10.0; Trident/7.0; rv:11.0) like Gecko', false)
  })

  test('fails for an IE10 user agent', () => {
    run(
      'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Trident/6.0)',
      false
    )
  })

  test('fails for an Opera user agent', () => {
    run(
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 11_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36 OPR/79.0.4143.72',
      false
    )
  })
})
