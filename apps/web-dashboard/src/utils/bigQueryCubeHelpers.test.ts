import { Duration } from 'luxon'

import {
  durationFromObjectOrString,
  durationFromMinutes,
} from 'src/utils/bigQueryCubeHelpers'

describe('durationFromObjectOrString', () => {
  it('null function input', () => {
    const durationInput = null

    const result = durationFromObjectOrString(durationInput)

    expect(result).toEqual(Duration.fromObject({}))
  })
  it('undefined function input', () => {
    const durationInput = undefined

    const result = durationFromObjectOrString(durationInput)

    expect(result).toEqual(Duration.fromObject({}))
  })
  //
  // Behavior for Postgres - objects come in and hit Duration.fromObject directly
  //

  it('object function input', () => {
    const durationInput = {
      hours: 3,
      seconds: 4,
      milliseconds: 5,
    }

    const result = durationFromObjectOrString(durationInput)

    expect(result).toEqual(Duration.fromObject(durationInput))
  })
  //
  // These tests make sure we're handling positive string intervals properly.
  //

  it('string function input, positive time interval', () => {
    // looks like this in BigQuery
    const durationInput = '0-0 0 7:27:59.222757'

    const result = durationFromObjectOrString(durationInput)

    expect(result.toMillis()).toEqual(
      // looks like this in Postgres
      Duration.fromObject({
        hours: 7,
        minutes: 27,
        seconds: 59,
        milliseconds: 222.757,
      }).toMillis()
    )
  })
  it('string function input, positive time interval', () => {
    // looks like this in BigQuery
    const durationInput = '0-0 0 7:27:59.222757'

    const result = durationFromObjectOrString(durationInput)

    expect(result.toMillis()).toEqual(
      // looks like this in Postgres
      Duration.fromObject({
        hours: 7,
        minutes: 27,
        seconds: 59,
        milliseconds: 222.757,
      }).toMillis()
    )
  })
  //
  // These tests make sure we're handling negative string intervals properly.
  // The test cases are picked by looking up a case ID in dev, and pulling both
  // the BigQuery and Postgres representations as returned by Cube.js
  //

  it('string function input, negative interval, time sign mismatch between Postgres and BigQuery', () => {
    // looks like this in BigQuery
    const durationInput = '0-0 -161 -22:11:9.001'

    const result = durationFromObjectOrString(durationInput)

    expect(result.toMillis().toFixed(3)).toEqual(
      // looks like this in Postgres
      Duration.fromObject({
        days: -162,
        hours: 1,
        minutes: 48,
        seconds: 50,
        milliseconds: 999,
      })
        .toMillis()
        .toFixed(3)
    )
  })
  it('string function input, negative time interval, day + time sign mismatch between Postgres and BigQuery', () => {
    // looks like this in BigQuery
    const durationInput = '0-0 0 -5:58:25.924465'

    const result = durationFromObjectOrString(durationInput)

    expect(result.toMillis().toFixed(3)).toEqual(
      // looks like this in Postgres
      Duration.fromObject({
        days: -1,
        hours: 18,
        minutes: 1,
        seconds: 34,
        milliseconds: 75.535,
      })
        .toMillis()
        .toFixed(3)
    )
  })
  it('string function input, negative hour interval', () => {
    // looks like this in BigQuery
    const durationInput = '0-0 0 -9:50:40.175'

    const result = durationFromObjectOrString(durationInput)

    expect(result.toMillis().toFixed(3)).toEqual(
      Duration.fromObject({
        // looks like this in Postgres
        hours: -9,
        minutes: -50,
        seconds: -40,
        milliseconds: -175,
      })
        .toMillis()
        .toFixed(3)
    )
  })
  it('string function input, negative minutes interval', () => {
    // looks like this in BigQuery
    const durationInput = '0-0 0 -0:37:2.610'

    const result = durationFromObjectOrString(durationInput)

    expect(result.toMillis().toFixed(3)).toEqual(
      // looks like this in Postgres
      Duration.fromObject({
        minutes: -37,
        seconds: -2,
        milliseconds: -610,
      })
        .toMillis()
        .toFixed(3)
    )
  })

  it('string function input, negative seconds interval, Postgres parity', () => {
    // looks like this in BigQuery
    const durationInput = '0-0 0 -0:0:39.454275'

    const result = durationFromObjectOrString(durationInput)

    expect(result.toMillis().toFixed(3)).toEqual(
      // looks like this in Postgres
      Duration.fromObject({
        days: -1,
        hours: 23,
        minutes: 59,
        seconds: 20,
        milliseconds: 545.725,
      })
        .toMillis()
        .toFixed(3)
    )
  })
})

describe('durationFromMinutes', () => {
  it('converts number to minutes of duration', () => {
    const duration = Duration.fromMillis(35 * 60 * 1000)
    const result = durationFromMinutes(35)

    expect(result.toMillis()).toEqual(duration.toMillis())
  })

  it('keeps duration object the same', () => {
    const duration = { minutes: 42 }
    const result = durationFromMinutes(duration)

    expect(result.minutes).toEqual(duration.minutes)
  })
})
