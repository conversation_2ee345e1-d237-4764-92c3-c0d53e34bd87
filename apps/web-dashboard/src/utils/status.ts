import { useCallback, useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime } from 'luxon'
import { rem } from 'polished'

import { shape } from '@apella/component-library'
import {
  CaseStatusName,
  CaseType,
  RoomStatusName,
  TurnoverStatusName,
} from 'src/__generated__/globalTypes'

import { WebDashboardFeatureFlagSet } from '../modules/feature/types'
import { useTextColor } from './useTextColor'

export enum TurnoverLengthWithGoal {
  UNDER_GOAL = 'TURNOVER_W_GOAL_UNDER_GOAL',
  OVER_GOAL = 'TURNOVER_W_GOAL_OVER_GOAL',
  OVER_MAX = 'TURNOVER_W_GOAL_OVER_MAX',
}

export enum TurnoverLengthWithoutGoal {
  UNDER_MAX = 'TURNOVER_WO_GOAL_UNDER_MAX',
  OVER_MAX = 'TURNOVER_WO_GOAL_OVER_MAX',
}

// Every status that a live room may be in
export type LiveStatus = CaseStatusName | RoomStatusName | TurnoverStatusName
// Every status that is featured on the timeline
export type TimelinePillStatus =
  | CaseStatusName
  | CaseType.FORECAST
  | RoomStatusName
  | TurnoverLengthWithGoal
  | TurnoverLengthWithoutGoal
// All statuses that get displayed somewhere in the app
export type StatusSuperset = LiveStatus | TimelinePillStatus

export interface CaseStatusNameStyleConfig {
  backgroundColor: string
  border?: string
  borderRadius: string
  textColor?: string
}

export interface CaseStatusNameConfig {
  priority: { [key in StatusSuperset]?: number }
  statusesForKey: TimelinePillStatus[]
}

const StatusTranslations: { [key in StatusSuperset]?: string } = {
  [CaseStatusName.ACTUAL]: 'Actual',
  [CaseStatusName.COMPLETE]: 'Complete',
  [CaseStatusName.IN_FACILITY]: 'In Fac',
  [CaseStatusName.IN_HOLD]: 'In Hold',
  [CaseStatusName.PHASE_II]: 'Phase II',
  [CaseStatusName.PREP]: 'Prep',
  [CaseStatusName.PRE_PROCEDURE]: 'Pre Proc',
  [CaseStatusName.PRE_PROCEDURE_COMPLETE]: 'Pre Comp',
  [CaseStatusName.RECOVERY]: 'Recovery',
  [CaseStatusName.SCHEDULED]: 'Scheduled',
  [CaseStatusName.SURGERY]: 'Surgery',
  [CaseStatusName.WRAP_UP]: 'Wrap-up',
  [CaseType.FORECAST]: 'Forecast',
  [RoomStatusName.CLOSED]: 'Closed',
  [RoomStatusName.IDLE]: 'Idle',
  [RoomStatusName.IN_CASE]: 'Live',
  [RoomStatusName.TURNOVER]: 'Turnover',
  [TurnoverLengthWithGoal.OVER_GOAL]: 'Attention',
  [TurnoverStatusName.CLEANING]: 'Turnover Started',
  [TurnoverStatusName.CLEANED]: 'Cleaned',
  [TurnoverStatusName.OPENING]: 'Opening',
} as const

export const formatStatus = (status: StatusSuperset) =>
  StatusTranslations[status] ?? status.replaceAll('_', ' ').toLocaleLowerCase()

export const useApellaCaseStateColor = (): {
  [key in StatusSuperset]?: string
} => {
  const theme = useTheme()
  const { statusColors } = useFlags<WebDashboardFeatureFlagSet>()

  const parsedColors = statusColors
    ? (statusColors as { [key in StatusSuperset]?: string })
    : {}

  return {
    [CaseStatusName.IN_FACILITY]: '#C0997B',
    [CaseStatusName.PRE_PROCEDURE]: '#FCE995',
    [CaseStatusName.PRE_PROCEDURE_COMPLETE]:
      parsedColors[CaseStatusName.PRE_PROCEDURE] &&
      !parsedColors[CaseStatusName.PRE_PROCEDURE_COMPLETE]
        ? parsedColors[CaseStatusName.PRE_PROCEDURE]
        : theme.palette.violet[20],
    [CaseStatusName.IN_HOLD]: '#EF8633',
    [CaseStatusName.RECOVERY]: '#B1D5E8',
    [CaseStatusName.PHASE_II]: '#F19EFA',
    [CaseStatusName.PREP]: theme.palette.teal[30],
    [CaseStatusName.SURGERY]: '#8CB860',
    [CaseStatusName.WRAP_UP]: '#A1F98E',
    [CaseStatusName.SCHEDULED]: theme.palette.gray[20],
    [CaseType.FORECAST]: theme.palette.background.primary,
    [CaseStatusName.ACTUAL]: theme.palette.gray[30],
    [CaseStatusName.COMPLETE]: theme.palette.gray[30],
    [RoomStatusName.CLOSED]: theme.palette.gray[20],
    [RoomStatusName.IDLE]: theme.palette.gray[20],
    [RoomStatusName.IN_CASE]: theme.palette.gray[20],
    [RoomStatusName.TURNOVER]: theme.palette.yellow[40],
    [TurnoverLengthWithGoal.OVER_GOAL]: theme.palette.red[30],
    [TurnoverStatusName.CLEANING]: theme.palette.magenta[70],
    [TurnoverStatusName.CLEANED]: theme.palette.magenta[50],
    [TurnoverStatusName.OPENING]: theme.palette.violet[50],
    ...parsedColors,
  } as const
}
export const useStatusStyles = ({
  isKey,
}: {
  isKey?: boolean
} = {}): ((status: StatusSuperset) => CaseStatusNameStyleConfig) => {
  const theme = useTheme()

  const backgroundColors = useApellaCaseStateColor()
  const getTextColor = useTextColor()

  const statusStyles: { [key in StatusSuperset]?: CaseStatusNameStyleConfig } =
    useMemo(() => {
      const actualStyles = {
        borderRadius: shape.borderRadius.xsmall,
        border: `${rem('1px')} solid ${theme.palette.gray[30]}`,
      }

      return {
        [CaseStatusName.IN_FACILITY]: {
          backgroundColor: backgroundColors[CaseStatusName.IN_FACILITY] ?? '',
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          border: isKey
            ? 'none'
            : `${rem('1px')} solid ${theme.palette.gray[30]}`,
          textColor: getTextColor(backgroundColors[CaseStatusName.IN_FACILITY]),
        },
        [CaseStatusName.PRE_PROCEDURE]: {
          backgroundColor: backgroundColors[CaseStatusName.PRE_PROCEDURE] ?? '',
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          border: isKey
            ? 'none'
            : `${rem('1px')} solid ${theme.palette.gray[30]}`,
          textColor: getTextColor(
            backgroundColors[CaseStatusName.PRE_PROCEDURE]
          ),
        },
        [CaseStatusName.PRE_PROCEDURE_COMPLETE]: {
          backgroundColor:
            backgroundColors[CaseStatusName.PRE_PROCEDURE_COMPLETE] ?? '',
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          border: isKey
            ? 'none'
            : `${rem('1px')} solid ${theme.palette.gray[30]}`,
          textColor: getTextColor(
            backgroundColors[CaseStatusName.PRE_PROCEDURE_COMPLETE]
          ),
        },
        [CaseStatusName.IN_HOLD]: {
          backgroundColor: backgroundColors[CaseStatusName.IN_HOLD] ?? '',
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          border: isKey
            ? 'none'
            : `${rem('1px')} solid ${theme.palette.gray[30]}`,
          textColor: getTextColor(backgroundColors[CaseStatusName.IN_HOLD]),
        },
        [CaseStatusName.PREP]: {
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          backgroundColor: backgroundColors[CaseStatusName.PREP] ?? '',
          border: isKey
            ? 'none'
            : `${rem('1px')} solid ${theme.palette.gray[30]}`,
        },
        [CaseStatusName.SURGERY]: {
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          backgroundColor: backgroundColors[CaseStatusName.SURGERY] ?? '',
          border: isKey
            ? 'none'
            : `${rem('1px')} solid ${theme.palette.gray[30]}`,
        },
        [CaseStatusName.WRAP_UP]: {
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          backgroundColor: backgroundColors[CaseStatusName.WRAP_UP] ?? '',
          border: isKey
            ? 'none'
            : `${rem('1px')} solid ${theme.palette.gray[30]}`,
        },
        [CaseStatusName.RECOVERY]: {
          backgroundColor: backgroundColors[CaseStatusName.RECOVERY] ?? '',
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          border: isKey
            ? 'none'
            : `${rem('1px')} solid ${theme.palette.gray[30]}`,
          textColor: getTextColor(backgroundColors[CaseStatusName.RECOVERY]),
        },
        [CaseStatusName.PHASE_II]: {
          backgroundColor: backgroundColors[CaseStatusName.PHASE_II] ?? '',
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          border: isKey
            ? 'none'
            : `${rem('1px')} solid ${theme.palette.gray[30]}`,
          textColor: getTextColor(backgroundColors[CaseStatusName.PHASE_II]),
        },
        [CaseStatusName.ACTUAL]: {
          ...actualStyles,
          backgroundColor: backgroundColors[CaseStatusName.ACTUAL] ?? '',
        },
        [CaseStatusName.SCHEDULED]: {
          borderRadius: isKey ? shape.borderRadius.xxsmall : rem('10px'),
          backgroundColor: backgroundColors[CaseStatusName.SCHEDULED] ?? '',
          border: `${rem('1px')} solid ${theme.palette.gray[30]}`,
        },
        [CaseStatusName.COMPLETE]: {
          ...actualStyles,
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          backgroundColor: backgroundColors[CaseStatusName.COMPLETE] ?? '',
        },
        [CaseType.FORECAST]: {
          backgroundColor: backgroundColors[CaseType.FORECAST] ?? '',
          borderRadius: isKey
            ? shape.borderRadius.xxsmall
            : shape.borderRadius.xsmall,
          border: `${rem('1px')} dashed ${theme.palette.gray[50]}`,
        },
        [RoomStatusName.CLOSED]: {
          ...actualStyles,
          backgroundColor: backgroundColors[RoomStatusName.CLOSED] ?? '',
        },
        [RoomStatusName.IDLE]: {
          ...actualStyles,
          backgroundColor: backgroundColors[RoomStatusName.IDLE] ?? '',
        },
        [RoomStatusName.IN_CASE]: {
          ...actualStyles,
          backgroundColor: backgroundColors[RoomStatusName.IN_CASE] ?? '',
        },
        [RoomStatusName.TURNOVER]: {
          backgroundColor: backgroundColors[RoomStatusName.TURNOVER] ?? '',
          borderRadius: shape.borderRadius.xsmall,
          border: `${rem('1px')} solid ${theme.palette.yellow[40]}`,
        },
        [TurnoverLengthWithGoal.UNDER_GOAL]: {
          backgroundColor: backgroundColors[RoomStatusName.TURNOVER] ?? '',
          borderRadius: shape.borderRadius.xsmall,
          border: `${rem('1px')} solid ${backgroundColors[RoomStatusName.TURNOVER]}`,
        },
        [TurnoverLengthWithGoal.OVER_GOAL]: {
          backgroundColor: theme.palette.red[30],
          borderRadius: shape.borderRadius.xsmall,
          border: `${rem('1px')} solid ${theme.palette.red[30]}`,
          textColor: 'white',
        },
        [TurnoverLengthWithGoal.OVER_MAX]: {
          backgroundColor: backgroundColors[RoomStatusName.IDLE] ?? '',
          borderRadius: shape.borderRadius.xsmall,
          border: `${rem('1px')} solid ${theme.palette.gray[40]}`,
        },
        [TurnoverLengthWithoutGoal.UNDER_MAX]: {
          backgroundColor: backgroundColors[RoomStatusName.TURNOVER] ?? '',
          borderRadius: shape.borderRadius.xsmall,
          border: `${rem('1px')} solid ${backgroundColors[RoomStatusName.TURNOVER]}`,
        },
        [TurnoverLengthWithoutGoal.OVER_MAX]: {
          backgroundColor: backgroundColors[RoomStatusName.IDLE] ?? '',
          borderRadius: shape.borderRadius.xsmall,
          border: `${rem('1px')} solid ${theme.palette.gray[40]}`,
        },
        [TurnoverStatusName.CLEANING]: {
          backgroundColor: backgroundColors[TurnoverStatusName.CLEANING] ?? '',
          borderRadius: shape.borderRadius.xsmall,
          border: `${rem('1px')} solid ${backgroundColors[TurnoverStatusName.CLEANING]}`,
          textColor: 'white',
        },
        [TurnoverStatusName.CLEANED]: {
          backgroundColor: backgroundColors[TurnoverStatusName.CLEANED] ?? '',
          borderRadius: shape.borderRadius.xsmall,
          border: `${rem('1px')} solid ${backgroundColors[TurnoverStatusName.CLEANED]}`,
          textColor: 'white',
        },
        [TurnoverStatusName.OPENING]: {
          backgroundColor: backgroundColors[TurnoverStatusName.OPENING] ?? '',
          borderRadius: shape.borderRadius.xsmall,
          border: `${rem('1px')} solid ${backgroundColors[TurnoverStatusName.OPENING]}`,
          textColor: 'white',
        },
      }
    }, [backgroundColors, theme, isKey, getTextColor])

  return useCallback(
    (status: StatusSuperset) => {
      const defaultStyles = statusStyles[
        CaseStatusName.SCHEDULED
      ] as CaseStatusNameStyleConfig
      return statusStyles[status] ?? defaultStyles
    },
    [statusStyles]
  )
}

export const getLiveStatus = (
  roomStatus: { name: RoomStatusName; since: string },
  caseStatus?: { name: CaseStatusName; since: string | null }
) => {
  const { name: roomStatusName, since: roomSince } = roomStatus
  const caseStatusName = caseStatus?.name
  const caseSince = caseStatus?.since

  const hasCaseStatus = !!caseStatusName
  const liveStatusName = hasCaseStatus ? caseStatusName : roomStatusName
  const liveSinceStr = hasCaseStatus && caseSince ? caseSince : roomSince
  const since = DateTime.fromISO(liveSinceStr)

  return {
    name: liveStatusName,
    since,
  }
}
