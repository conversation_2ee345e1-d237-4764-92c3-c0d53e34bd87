import { useQueryParamAndLocalStorageState } from '@apella/hooks'

export const SITES_KEY = 'sites'

export const useSitesState = () => {
  return useQueryParamAndLocalStorageState<string[] | undefined>(
    SITES_KEY,
    undefined
  )
}

export const sitesStateToSearch = (sitesState?: string[]) => {
  if (sitesState === undefined) {
    return undefined
  }

  const search = new URLSearchParams()
  search.set(SITES_KEY, JSON.stringify(sitesState))
  return search.toString()
}
