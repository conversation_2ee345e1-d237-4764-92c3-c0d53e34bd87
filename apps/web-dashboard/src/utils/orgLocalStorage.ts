import { ACTIVE_ORG_ID_KEY } from 'src/router/constants'

const DELIMITER = '-'

/**
 * Store and retrieve items in localStorage with organization-specific keys.
 * It uses a delimiter to separate the organization ID from the actual key.
 *
 * @module orgLocalStorage
 */
export default {
  setItem(key: string, value: string) {
    const org = getActiveOrgId()
    if (!org) {
      localStorage.setItem(key, value)
    }
    localStorage.setItem(`${org}${DELIMITER}${key}`, value)
  },

  getItem(key: string) {
    const org = getActiveOrgId()
    if (!org) {
      return localStorage.getItem(key)
    }
    return localStorage.getItem(`${org}${DELIMITER}${key}`)
  },

  removeItem(key: string) {
    const org = getActiveOrgId()
    if (!org) {
      localStorage.removeItem(key)
    }
    localStorage.removeItem(`${org}${DELIMITER}${key}`)
  },
}

function getActiveOrgId(): string {
  const org = JSON.parse(
    window.localStorage.getItem(ACTIVE_ORG_ID_KEY) || 'null'
  )
  if (typeof org === 'string') {
    return org
  }
  return ''
}
