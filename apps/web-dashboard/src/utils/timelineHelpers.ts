import { DateTime, Duration } from 'luxon'

const MIN_PILL_WIDTH_PX = 20

// generates a DateTime[] with an entry for each hour between the start of the earliest phase/case to the end of the latest
export const calculateWorkingHours = (
  minTime: DateTime,
  maxTime: DateTime
): DateTime[] => {
  if (minTime >= maxTime) {
    return []
  }
  const hours = [minTime.startOf('hour')]
  while (hours[hours.length - 1] <= maxTime) {
    hours.push(hours[hours.length - 1].plus({ hours: 1 }))
  }
  return hours
}

// filters the workingHours and adds to the end of that range if necessary to cover the same time range while containing <= maxHours total
export const calculateHoursFromWorkingHoursAndMaxHours = (
  workingHours: DateTime[],
  maxHours: number | undefined
) => {
  if (!maxHours || !workingHours.length || workingHours.length <= maxHours) {
    return workingHours
  }

  const hours = [...workingHours]

  // figure out how many hours should be grouped into a header tick
  const hoursPerTick = Math.ceil(hours.length / maxHours)

  // while the last hour will be filtered out, add hours to the end of the range until it won't be
  while (!shouldDisplayIndex(hours.length - 1, hoursPerTick)) {
    hours.push(hours[hours.length - 1].plus({ hours: 1 }))
  }

  // return every hoursPerTick-th hour
  return hours.filter((_h, i) => shouldDisplayIndex(i, hoursPerTick))
}

const shouldDisplayIndex = (index: number, hoursPerTick: number) =>
  index % hoursPerTick === 0

// Creates markers that should be displayed as well as adds whether a label should be displayed as well
export const calculateTimeMarkers = (
  minTime: DateTime,
  maxTime: DateTime,
  maxHourLabels: number
) => {
  const totalDuration = maxTime.diff(minTime)

  const hoursPerLabel = Math.ceil(
    totalDuration.as('hours') / Math.max(maxHourLabels, 1)
  )
  const minutesPerTick =
    hoursPerLabel === 1
      ? 5
      : hoursPerLabel === 2
        ? 15
        : hoursPerLabel === 3
          ? 30
          : 60
  const durationPerTick = Duration.fromObject({ minutes: minutesPerTick })

  const markers = [{ time: minTime, shouldDisplayLabel: true }]

  while (markers[markers.length - 1].time < maxTime) {
    const time = markers[markers.length - 1].time.plus(durationPerTick)
    const shouldDisplayLabel =
      time.diff(minTime).as('hours') % hoursPerLabel === 0
    markers.push({ time, shouldDisplayLabel })
  }
  return markers
}

export const calculateTimeMarkersPerTick = (
  minTime: DateTime,
  maxTime: DateTime,
  durationPerTick: Duration
) => {
  const markers = [{ time: minTime }]

  while (markers[markers.length - 1].time < maxTime) {
    const time = markers[markers.length - 1].time.plus(durationPerTick)
    markers.push({ time })
  }
  return markers
}

export interface PillPositionAndSize {
  doesOverflowMaxTime: boolean
  doesOverflowMinTime: boolean
  forecastedWidthPx: number
  gradientWidthPx: number
  left: string
  widthPct: string
  widthPx: number
}

export const calculateTimeMarkerPercentHelper = (
  time: DateTime,
  minHour: DateTime,
  maxHour: DateTime
) => {
  const timelineWidthMillis = maxHour.diff(minHour).toMillis()
  return (
    (Math.max(time.diff(minHour).toMillis(), 0) / timelineWidthMillis) * 100
  )
}

export const calculateTimeMarkerPositionHelper = (
  time: DateTime,
  minHour: DateTime,
  maxHour: DateTime
) => {
  return `${calculateTimeMarkerPercentHelper(time, minHour, maxHour)}%`
}

export const calculateTimeMarkerPositionHelperLeft = ({
  timelineSize,
  time,
  minHour,
  maxHour,
}: {
  timelineSize?: number
  time: DateTime
  minHour: DateTime
  maxHour: DateTime
}) => {
  if (!timelineSize) return `0`

  return calculateTimeMarkerPositionHelper(time, minHour, maxHour)
}

interface PillPositionAndSizeCalc {
  endTime?: DateTime
  liveTime?: DateTime
  maxHour: DateTime
  minHour: DateTime
  minWidth?: number
  startTime: DateTime
  timelineWidth: number
}

export const calculatePillPositionAndSizeHelper = ({
  startTime,
  timelineWidth,
  minHour,
  maxHour,
  minWidth = MIN_PILL_WIDTH_PX,
  endTime,
  liveTime,
}: PillPositionAndSizeCalc): PillPositionAndSize => {
  const timelineWidthMillis = maxHour.diff(minHour).toMillis()

  const minHourMSDiff = startTime.diff(minHour).toMillis()
  const maxHourMSDiff = (liveTime || endTime || startTime)
    .diff(minHour)
    .toMillis()

  const startFrac = Math.max(minHourMSDiff, 0) / timelineWidthMillis
  const endFrac =
    Math.min(maxHourMSDiff, timelineWidthMillis) / timelineWidthMillis
  const forecastedFrac = liveTime
    ? Math.min(endTime?.diff(minHour).toMillis() || 0, timelineWidthMillis) /
      timelineWidthMillis
    : 0

  const trueWidthFrac = endFrac - startFrac
  const minFinalWidth = minWidth / timelineWidth
  const finalWidthFrac = Math.max(trueWidthFrac, minFinalWidth)
  const forecastedWidthFrac = Math.max(0, forecastedFrac - endFrac)
  const trueLeftFrac = startFrac - (finalWidthFrac - trueWidthFrac) / 2
  const finalLeftFrac = Math.max(0, Math.min(trueLeftFrac, 1 - finalWidthFrac))
  const gradientWidthFrac =
    liveTime && endTime && liveTime > endTime
      ? liveTime.diff(endTime).toMillis() / timelineWidthMillis
      : 0

  return {
    left: `${finalLeftFrac * 100}%`,
    widthPct: `${finalWidthFrac * 100}%`,
    widthPx: Math.round(finalWidthFrac * timelineWidth),
    forecastedWidthPx: Math.round(forecastedWidthFrac * timelineWidth),
    doesOverflowMinTime: minHourMSDiff < 0,
    doesOverflowMaxTime: maxHourMSDiff > timelineWidthMillis,
    gradientWidthPx: Math.round(gradientWidthFrac * timelineWidth),
  }
}
