const wait = (ms: number) => new Promise((res) => window.setTimeout(res, ms))

export const HOUR_IN_MS = 3600000

export const backoff = async <T>(
  fn: () => Promise<T>,
  options?: {
    attempt?: number
    maxWait?: number
    maxRetries?: number
  }
): Promise<T> => {
  const maxWait = options?.maxWait
  const attempt = Math.max(options?.attempt ?? 1, 1)
  const maxRetries = Math.min(options?.maxRetries ?? Infinity, Infinity)

  try {
    return await fn()
  } catch (e) {
    if (attempt > maxRetries) {
      throw e
    }

    const delayInMs = 1000 * 2 ** attempt

    const delay = Math.min(1000 * 2 ** attempt, maxWait ?? delayInMs)

    await wait(delay)

    return backoff(fn, {
      ...options,
      maxWait,
      attempt: attempt + 1,
    })
  }
}
