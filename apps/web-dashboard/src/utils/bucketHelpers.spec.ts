import { DateTime } from 'luxon'

import {
  determineDimension,
  getBucketEndDate,
  NonTimeDimensions,
  TimeDimensions,
} from './bucketHelpers'

describe('bucketHelpers', () => {
  describe('determineBucketSize', () => {
    test('returns MONTH for a diff of 4 months', () => {
      const minTime = DateTime.fromObject({ year: 2022, month: 1, day: 1 })
      const maxTime = DateTime.fromObject({ year: 2022, month: 5, day: 1 })

      const result = determineDimension(minTime, maxTime)
      expect(result).toEqual(TimeDimensions.MONTH)
    })
    test('returns MONTH for a diff of 3 months', () => {
      const minTime = DateTime.fromObject({ year: 2022, month: 1, day: 1 })
      const maxTime = DateTime.fromObject({ year: 2022, month: 4, day: 1 })

      const result = determineDimension(minTime, maxTime)
      expect(result).toEqual(TimeDimensions.MONTH)
    })
    test('returns WEEK for a diff of 2 months', () => {
      const minTime = DateTime.fromObject({ year: 2022, month: 1, day: 1 })
      const maxTime = DateTime.fromObject({ year: 2022, month: 3, day: 1 })

      const result = determineDimension(minTime, maxTime)
      expect(result).toEqual(TimeDimensions.WEEK)
    })
    test('returns DAY for a diff of 1 month', () => {
      const minTime = DateTime.fromObject({ year: 2022, month: 1, day: 1 })
      const maxTime = DateTime.fromObject({ year: 2022, month: 2, day: 1 })

      const result = determineDimension(minTime, maxTime)
      expect(result).toEqual(TimeDimensions.DAY)
    })
    test('returns DAY for a diff of 2 weeks', () => {
      const minTime = DateTime.fromObject({ year: 2022, month: 1, day: 1 })
      const maxTime = DateTime.fromObject({ year: 2022, month: 1, day: 15 })

      const result = determineDimension(minTime, maxTime)
      expect(result).toEqual(TimeDimensions.DAY)
    })
    test('returns original dimension if non-time dimension', () => {
      const minTime = DateTime.now()
      const maxTime = DateTime.max()

      Object.values(NonTimeDimensions).forEach((nonTimeDimension) => {
        const result = determineDimension(minTime, maxTime, nonTimeDimension)
        expect(result).toEqual(nonTimeDimension)
      })
    })
  })

  describe('getBucketEndDate', () => {
    test('returns the end of the same day for a DAY bucket', () => {
      const startDate = DateTime.fromObject({
        year: 2022,
        month: 1,
        day: 1,
      }).toJSDate()
      const expected = DateTime.fromObject({
        year: 2022,
        month: 1,
        day: 1,
      })
        .endOf('day')
        .toJSDate()

      const result = getBucketEndDate(startDate, TimeDimensions.DAY)
      expect(result).toEqual(expected)
    })

    test('returns following Sunday for a WEEK bucket', () => {
      const startDate = DateTime.fromObject({
        year: 2022,
        month: 1,
        day: 3,
      }).toJSDate()
      const expected = DateTime.fromObject({
        year: 2022,
        month: 1,
        day: 9,
      })
        .endOf('day')
        .toJSDate()

      const result = getBucketEndDate(startDate, TimeDimensions.WEEK)
      expect(result).toEqual(expected)
    })

    test('returns last day of month for a MONTH bucket', () => {
      const startDate = DateTime.fromObject({
        year: 2022,
        month: 1,
        day: 1,
      }).toJSDate()
      const expected = DateTime.fromObject({
        year: 2022,
        month: 1,
        day: 31,
      })
        .endOf('day')
        .toJSDate()

      const result = getBucketEndDate(startDate, TimeDimensions.MONTH)
      expect(result).toEqual(expected)
    })
  })
})
