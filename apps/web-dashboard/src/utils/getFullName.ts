import { groupBy, mapValues, pickBy } from 'lodash'

export const getFullName = (last: string, first: string) => `${last}, ${first}`

export const getConflictingLastNames = (
  allStaff: { id: string; firstName: string; lastName: string }[]
): Set<string> => {
  const numStaffMembersByLastName = mapValues(
    groupBy(allStaff, (s) => s.lastName),
    (v) => new Set(v.map((s) => s.id)).size
  )
  return new Set(
    Object.keys(pickBy(numStaffMembersByLastName, (num) => num > 1))
  )
}
