import { LDClient } from 'launchdarkly-react-client-sdk'
import { vi } from 'vitest'

import { createLDFlagsProxy } from './launchDarklyFlagProxy'

describe('createLDProxy', () => {
  it('gets flag from ldClient', () => {
    const ldClient = {
      variation: vi.fn().mockReturnValue(true),
    }
    const flags = createLDFlagsProxy(ldClient as unknown as LDClient)

    const value = flags.flagName

    expect(ldClient.variation).toHaveBeenCalledWith('flag-name')
    expect(value).toBe(true)
  })
})
