export const SUPPORTED_BROWSERS = [
  {
    name: 'Chrome',
    url: 'https://www.google.com/chrome/',
    test: (ua: string): boolean =>
      ua.indexOf('Chrome') !== -1 && ua.indexOf('OP') === -1,
  },
  {
    name: 'Firefox',
    url: 'https://www.mozilla.org/en-US/firefox/new/',
    test: (ua: string): boolean => ua.indexOf('Firefox') !== -1,
  },
  {
    name: 'Edge',
    url: 'https://www.microsoft.com/en-us/edge',
    test: (ua: string): boolean => ua.indexOf('Edg') !== -1,
  },
  {
    name: 'Safari',
    test: (ua: string): boolean =>
      ua.indexOf('Safari') !== -1 && ua.indexOf('OP') === -1,
  },
]

export const isBrowserSupported = (userAgent: string): boolean => {
  return SUPPORTED_BROWSERS.some((b) => b.test(userAgent))
}
