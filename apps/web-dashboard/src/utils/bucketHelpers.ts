import { DateTime } from 'luxon'

enum TimeDimensions {
  MINUTE = 'MINUTE',
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
}

enum NonTimeDimensions {
  DAYOFWEEK = 'DAYOFWEEK',
  OR = 'OR',
  SURGEON = 'SURGEON',
  ANESTHESIA = 'ANESTHESIA',
  SERVICE_LINE = 'SERVICE_LINE',
  PROCEDURE = 'PROCEDURE',
  CIRCULATOR = 'CIRCULATOR',
  SCRUB_TECH = 'SCRUB_TECH',
}

type Dimension = keyof typeof TimeDimensions | keyof typeof NonTimeDimensions

const determineDimension = (
  minTime: DateTime,
  maxTime: DateTime,
  currentDimension?: Dimension
): Dimension => {
  if (currentDimension && !(currentDimension in TimeDimensions)) {
    return currentDimension
  }
  const diffInMonths = maxTime.diff(minTime, 'months').months
  const diffInDays = maxTime.diff(minTime, 'days').days
  if (diffInMonths >= 3) {
    return TimeDimensions.MONTH
  } else if (diffInDays >= 32) {
    // 32 days so that a month will be in days
    return TimeDimensions.WEEK
  } else {
    return TimeDimensions.DAY
  }
}

const getBucketEndDate = (
  startDate: Date,
  bucketSize: TimeDimensions
): Date => {
  if (bucketSize === TimeDimensions.MONTH) {
    return DateTime.fromJSDate(startDate).endOf('month').endOf('day').toJSDate()
  } else if (bucketSize === TimeDimensions.WEEK) {
    return DateTime.fromJSDate(startDate)
      .plus({ days: 6 })
      .endOf('day')
      .toJSDate()
  } else {
    return DateTime.fromJSDate(startDate).endOf('day').toJSDate()
  }
}

export {
  determineDimension,
  getBucketEndDate,
  NonTimeDimensions,
  TimeDimensions,
}
export type { Dimension }
