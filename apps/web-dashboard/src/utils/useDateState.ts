import { useMemo } from 'react'

import { DateTime } from 'luxon'

import { useQueryParamState } from '@apella/hooks'

import { useTimezone } from '../Contexts'
import {
  dateTimeToUrlFriendlyDate,
  urlFriendlyDateToDateTime,
} from './urlFriendlyDate'

export const DATE_KEY = 'date'

export type DateQueryParam = [
  { minTime: string; maxTime: string },
  (newDate: string) => void,
]

const useDateState = (): DateQueryParam => {
  const { timezone } = useTimezone()
  const [selectedDateQueryString, setSelectedDateQueryString] =
    useQueryParamState<string | undefined>(DATE_KEY, undefined)

  const [startDate] = useQueryParamState<string | undefined>(
    'startDate',
    undefined
  )

  const { minTime, maxTime } = useMemo(() => {
    const computedTime =
      selectedDateQueryString || startDate
        ? urlFriendlyDateToDateTime((selectedDateQueryString ?? startDate)!)
        : DateTime.now()
    return {
      minTime: computedTime
        .startOf('day')
        .setZone(timezone, { keepLocalTime: true })
        .toISO(),
      maxTime: computedTime
        .endOf('day')
        .setZone(timezone, { keepLocalTime: true })
        .toISO(),
    }
  }, [selectedDateQueryString, startDate, timezone])

  return [
    {
      minTime,
      maxTime,
    },
    setSelectedDateQueryString,
  ]
}

const dateStateToSearch = (isoDate: string, timezone: string): string => {
  const urlFriendlyDate = dateTimeToUrlFriendlyDate(
    DateTime.fromISO(isoDate).setZone(timezone)
  )
  return `${DATE_KEY}="${urlFriendlyDate}"`
}

export default useDateState
export { dateStateToSearch }
