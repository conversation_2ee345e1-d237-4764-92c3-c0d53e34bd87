import { useTheme } from '@emotion/react'

import colorContrast from 'color-contrast'

export const useTextColor = () => {
  const theme = useTheme()
  return (backgroundColor?: string) => {
    if (!backgroundColor) {
      return theme.palette.text.primary
    }

    const primaryContrast = colorContrast(
      theme.palette.text.primary,
      backgroundColor
    )
    const alternateContrast = colorContrast(
      theme.palette.text.alternate,
      backgroundColor
    )

    return primaryContrast > alternateContrast
      ? theme.palette.text.primary
      : theme.palette.text.alternate
  }
}
