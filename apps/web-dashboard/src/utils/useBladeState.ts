import { useCallback, useState } from 'react'
import { useLocation, useNavigate } from 'react-router'

/*
Since this hook relies on the history stack to goBack one level with navigate(-1) any link inside of a blade should use the replace option on the Link component
This will ensure that the X button on the blade always brings you back to the initial page you came from

replace documentation: https://api.reactrouter.com/v7/interfaces/react_router.LinkProps.html#replace
default hardRefreshFallbackPath is '..' which will go back one path segment in the url
relative routing documentation for '..': https://api.reactrouter.com/v7/types/react_router.RelativeRoutingType.html
*/
export const useBladeState = (
  {
    hardRefreshFallbackPath,
  }: {
    hardRefreshFallbackPath: string
  } = { hardRefreshFallbackPath: '..' }
) => {
  const location = useLocation()
  const navigate = useNavigate()
  // when location.key is 'default' the page is on the initial location.
  const onInitialPageLocation = location.key === 'default'
  const background = location.state && location.state.background

  const [isBladeOpen, setIsBladeOpen] = useState(true)

  const goBack = useCallback(() => {
    background && !onInitialPageLocation
      ? navigate(-1)
      : navigate(hardRefreshFallbackPath, { relative: 'path' })
  }, [background, hardRefreshFallbackPath, onInitialPageLocation, navigate])

  const onBladeClose = useCallback(() => {
    setIsBladeOpen(false)
    goBack()
  }, [goBack])

  return {
    isBladeOpen,
    setIsBladeOpen,
    onBladeClose,
  }
}
