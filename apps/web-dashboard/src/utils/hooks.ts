import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react'

import { useApolloClient, useQuery } from '@apollo/client'
import { useFlags } from 'launchdarkly-react-client-sdk'

import { VideoPlayerCamera } from '@apella/component-library'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'

import {
  GetRoomDetails,
  GetRoomDetailsVariables,
  GetSiteDetails,
  GetSiteDetailsVariables,
  RoomCamerasFragment,
  RoomDefaultCameraFragment,
  RoomNameFragment,
  RoomSiteFragment,
  SiteFragment,
  GetEventTypes,
  GetRoomName,
  GetRoomNameVariables,
  GetRoomSiteId,
  GetRoomSiteIdVariables,
} from '../pages/__generated__'
import { Camera } from '../pages/Live/types'
import {
  GET_ROOM_DETAILS,
  GET_SITE_DETAILS,
  ROOM_CAMERAS_FRAGMENT,
  ROOM_DEFAULT_CAMERA_FRAGMENT,
  ROOM_NAME_FRAGMENT,
  ROOM_SITE_FRAGMENT,
  SITE_FRAGMENT,
  GET_EVENT_TYPES,
  GET_ROOM_NAME,
  GET_ROOM_SITE_ID,
} from '../pages/queries'

// We use the  default cache key behavior, which takes the form
// `<typename>:<id>`.
const getSiteCacheKey = (siteId?: string) => `Site:${siteId}`
const getRoomCacheKey = (roomId?: string) => `Room:${roomId}`

export const useRoomName = (roomId?: string) => {
  const client = useApolloClient()
  const cacheValue = client.readFragment<RoomNameFragment>({
    id: getRoomCacheKey(roomId),
    fragment: ROOM_NAME_FRAGMENT,
  })

  const { data } = useQuery<GetRoomName, GetRoomNameVariables>(GET_ROOM_NAME, {
    variables: { roomId: roomId! },
    skip: !roomId || !!cacheValue,
  })
  return cacheValue?.name || data?.room?.name
}

export const useRoomSiteId = (roomId?: string) => {
  const client = useApolloClient()
  const cacheValue = client.readFragment<RoomSiteFragment>({
    id: getRoomCacheKey(roomId),
    fragment: ROOM_SITE_FRAGMENT,
    fragmentName: 'RoomSiteFragment',
  })

  const { data, loading } = useQuery<GetRoomSiteId, GetRoomSiteIdVariables>(
    GET_ROOM_SITE_ID,
    {
      variables: { roomId: roomId! },
      skip: !roomId || !!cacheValue,
    }
  )

  return { siteId: cacheValue?.site.id || data?.room?.site?.id, loading }
}

export const useRoomSiteName = (roomId?: string) => {
  const { siteId } = useRoomSiteId(roomId)
  const siteName = useSiteName(siteId)

  return siteName
}

export const useBestCameraForRoom = (
  roomId?: string,
  cameras?: Camera[]
): [
  cameraId: string | undefined,
  setCameraId: Dispatch<SetStateAction<string | undefined>>,
] => {
  const client = useApolloClient()
  const cacheValue = client.readFragment<RoomDefaultCameraFragment>({
    id: getRoomCacheKey(roomId),
    fragment: ROOM_DEFAULT_CAMERA_FRAGMENT,
  })

  const { data, loading } = useQuery<GetRoomDetails, GetRoomDetailsVariables>(
    GET_ROOM_DETAILS,
    { variables: { roomId: roomId! }, skip: !roomId || !!cacheValue }
  )

  const defaultCameraId =
    cacheValue?.defaultCamera?.id || data?.room?.defaultCamera?.id

  const [cameraId, setCameraId] = useState<string | undefined>()

  useEffect(() => {
    const changeCameraId =
      cameras && !cameras?.some((cam) => cam.id === cameraId)

    if (!loading && (changeCameraId || (cameraId === undefined && cameras))) {
      const bestCameraId = defaultCameraId ?? cameras[0]?.id
      setCameraId(bestCameraId)
    }
  }, [defaultCameraId, cameras, cameraId, loading])

  useEffect(() => {
    if (cameraId !== undefined && roomId === undefined) {
      setCameraId(undefined)
    }
  }, [cameraId, roomId])

  return [cameraId, setCameraId]
}

export const useRoomCameras = (roomId?: string) => {
  const client = useApolloClient()
  const { blurVideoPlayer } = useFlags<WebDashboardFeatureFlagSet>()

  const cacheValue = client.readFragment<RoomCamerasFragment>({
    id: getRoomCacheKey(roomId),
    fragment: ROOM_CAMERAS_FRAGMENT,
    fragmentName: 'RoomCamerasFragment',
    variables: { blurVideoPlayer },
  })

  const { data, loading } = useQuery<GetRoomDetails, GetRoomDetailsVariables>(
    GET_ROOM_DETAILS,
    {
      variables: {
        roomId: roomId!,
        blurVideoPlayer,
      } as GetRoomDetailsVariables,
      skip: !roomId || !!cacheValue,
    }
  )

  const cameras = cacheValue?.cameras?.edges || data?.room?.cameras?.edges

  return useMemo(() => {
    return {
      loading,
      cameras:
        (cameras
          ?.map((e) => e.node.id)
          .sort()
          .map((id, i) => ({
            id,
            // The names that come from the API are long ('Camera 1'...), so we
            // create our own
            name: `${i + 1}`,
            patientBoundingBox: cameras[i].node.patientBoundingBox,
          })) as VideoPlayerCamera[]) ?? [],
    }
  }, [cameras, loading])
}

export const useSiteName = (siteId?: string) => {
  const siteDetails = useSiteDetails(siteId)
  return siteDetails?.name
}

export const useSiteTurnoverGoals = (siteId?: string) => {
  const siteDetails = useSiteDetails(siteId)
  return siteDetails?.turnoverGoals
}

export const useSiteDetails = (siteId?: string) => {
  const client = useApolloClient()
  const cacheValue = client.readFragment<SiteFragment>({
    id: getSiteCacheKey(siteId),
    fragment: SITE_FRAGMENT,
  })

  const { data } = useQuery<GetSiteDetails, GetSiteDetailsVariables>(
    GET_SITE_DETAILS,
    {
      variables: { siteId: siteId! },
      skip: !siteId || !!cacheValue,
    }
  )

  return cacheValue || data?.site
}

export function useEventTypes() {
  const { data, ...useQueryResult } = useQuery<GetEventTypes>(GET_EVENT_TYPES)

  const eventTypes = useMemo(() => {
    return (
      data?.eventTypes?.edges?.map((e) => ({
        ...e.node,
      })) ?? []
    )
  }, [data?.eventTypes])

  return { ...useQueryResult, eventTypes }
}
