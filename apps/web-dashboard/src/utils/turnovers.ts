import { DateTime } from 'luxon'

import { TurnoverType } from '../__generated__/globalTypes'
import { TurnoverLengthWithGoal, TurnoverLengthWithoutGoal } from './status'

export interface TurnoverGoals {
  goalMinutes?: number | null
  maxMinutes?: number | null
}

export const calculateTurnoverLengthStatus = (
  start: DateTime,
  end: DateTime,
  turnoverGoals: TurnoverGoals
): TurnoverLengthWithGoal | TurnoverLengthWithoutGoal => {
  // To be a little lenient here, we consider a turnover to be under goal
  // as long as it's in the same minute as the goal. Eg. 30m 59s is still under
  // goal for a 30m goal.
  const { maxMinutes: max, goalMinutes: goal } = turnoverGoals
  const duration = Math.floor(end.diff(start).as('minutes'))
  const DEFAULT_MAX_TURNOVER_MINUTES = 120 //using the same default value as seen in cloud api server
  const maxMinutes = max ?? DEFAULT_MAX_TURNOVER_MINUTES

  if (goal) {
    if (duration <= goal) {
      return TurnoverLengthWithGoal.UNDER_GOAL
    } else if (duration <= maxMinutes) {
      return TurnoverLengthWithGoal.OVER_GOAL
    } else {
      return TurnoverLengthWithGoal.OVER_MAX
    }
  } else {
    if (duration <= maxMinutes) {
      return TurnoverLengthWithoutGoal.UNDER_MAX
    } else {
      return TurnoverLengthWithoutGoal.OVER_MAX
    }
  }
}

export const getAnalyticsAddtlTurnoverData = (turnover?: {
  overallLengthStatus: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
  currentLengthStatus: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
  type: TurnoverType
  startTime: DateTime
  endTime: DateTime
}) => {
  const totalDuration = turnover
    ? turnover.endTime.diff(turnover.startTime)
    : undefined

  const liveTurnoverData = (() => {
    if (turnover?.type == TurnoverType.LIVE) {
      const currentDuration = turnover
        ? DateTime.now().diff(turnover.startTime)
        : undefined

      return {
        turnoverPercentDone:
          totalDuration && currentDuration && totalDuration.toMillis() > 0
            ? (currentDuration.toMillis() / totalDuration.toMillis()) * 100
            : undefined,
        turnoverCurrentLengthStatus:
          turnover?.type == TurnoverType.LIVE
            ? turnover?.currentLengthStatus
            : undefined,
        turnoverTimeElapsedSeconds: currentDuration?.as('seconds'),
      }
    }

    return {}
  })()

  return {
    turnoverLengthSeconds: totalDuration?.as('seconds'),
    turnoverOverallLengthStatus: turnover?.overallLengthStatus,
    turnoverType: turnover?.type,
    ...liveTurnoverData,
  }
}

export const getDisplayNameByTurnoverLength = (
  overallLengthStatus?: TurnoverLengthWithGoal | TurnoverLengthWithoutGoal
): string => {
  return overallLengthStatus === TurnoverLengthWithGoal.OVER_MAX ||
    overallLengthStatus === TurnoverLengthWithoutGoal.OVER_MAX
    ? 'Idle'
    : 'Turnover'
}
