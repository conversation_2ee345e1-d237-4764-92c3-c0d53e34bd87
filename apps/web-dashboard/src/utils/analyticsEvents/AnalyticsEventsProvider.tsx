import { createContext, useCallback, useContext, useEffect } from 'react'

import {
  init,
  logEvent as amplitudeLogEvent,
} from '@amplitude/analytics-browser'

import { useApellaAuth0 } from '@apella/hooks'
import { identifyAuth0UserInAmplitude } from '@apella/logger'
import { settings } from 'src/settings'
import { CLAIMS } from 'src/utils/auth0User'

import { EVENTS } from './analyticsEvents'

const AnalyticsEventsContext = createContext<boolean>(false)

export const AnalyticsEventsProvider = ({
  children,
}: React.PropsWithChildren) => {
  const { user } = useApellaAuth0()

  // Initializing the Amplitude analytics client
  useEffect(() => {
    if (settings.analytics?.amplitude) {
      init(settings.analytics.amplitude, {
        appVersion: settings.version,
        defaultTracking: {
          pageViews: false,
        },
      })
    }
  }, [])

  // Setting amplitude analytics properties
  useEffect(() => {
    if (user) {
      identifyAuth0UserInAmplitude(user)
    }
  }, [user])

  // Setting up beacon
  // I could see this going somewhere else, as we aren't really sending events through to the beacon
  useEffect(() => {
    if (user !== undefined && window.Beacon) {
      window.Beacon('identify', {
        userId: user.sub,
        email: user.email,
        name: user.name,
        company: user[CLAIMS.ORG_ID],
        organization: user[CLAIMS.ORG_ID],
        roles: user[CLAIMS.ROLES]?.join(', '),
      })
    }
  }, [user])

  return (
    <AnalyticsEventsContext.Provider value={true}>
      {children}
    </AnalyticsEventsContext.Provider>
  )
}
export const logEvent = (e: EVENTS, data?: Record<string, any>) => {
  amplitudeLogEvent(e, data)
}

type EventParams = [EVENTS, Record<string, any>?]

export const eventLogger = (...[e, data]: EventParams) => {
  logEvent(e, data)
}

export const useAnalyticsEventLogger = () => {
  const enabled = useContext(AnalyticsEventsContext)
  const logger = useCallback(
    (...args: EventParams) => {
      if (enabled) {
        eventLogger(...args)
      }
    },
    [enabled]
  )
  return logger
}
