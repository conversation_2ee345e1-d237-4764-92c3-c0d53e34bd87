import { useEffect } from 'react'
import { usePageVisibility } from 'react-page-visibility'

import { OperationVariables, QueryResult } from '@apollo/client'

export const usePolling = <
  T,
  V extends OperationVariables = OperationVariables,
>({
  refetch,
  startPolling,
  stopPolling,
  interval,
  skip,
}: Pick<QueryResult<T, V>, 'refetch' | 'startPolling' | 'stopPolling'> & {
  interval: number
  skip?: boolean
}) => {
  const pageVisibility = usePageVisibility()
  useEffect(() => {
    if (!pageVisibility || skip) {
      stopPolling()
      return
    }

    refetch()
    startPolling(interval)
    return () => {
      stopPolling()
    }
  }, [pageVisibility, refetch, startPolling, stopPolling, interval, skip])
}
