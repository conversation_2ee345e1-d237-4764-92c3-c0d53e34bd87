import { backoff } from './backoff'

const rejectError = () => Promise.reject(new Error('error'))

describe('backoff', () => {
  it('calls fn once on success', async () => {
    const mockFn = vi.fn()
    mockFn.mockReturnValueOnce(Promise.resolve(true))

    await backoff(mockFn)
    expect(mockFn).toBeCalledTimes(1)
  })

  it('calls failing fn twice with max 1 retry', async () => {
    const mockFn = vi.fn(() => rejectError())

    try {
      await backoff(mockFn, { maxRetries: 1, maxWait: 1 })
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {}
    expect(mockFn).toBeCalledTimes(2)
  })

  it('calls failing fn three times with max 2 retries', async () => {
    const mockFn = vi.fn(() => rejectError())

    try {
      await backoff(mockFn, { maxRetries: 2, maxWait: 1 })
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {}
    expect(mockFn).toBeCalledTimes(3)
  })

  it('calls fn twice when 2nd attempt succeed and max retries not set', async () => {
    const mockFn = vi.fn()
    mockFn
      .mockReturnValueOnce(Promise.reject(new Error('error')))
      .mockReturnValueOnce(Promise.resolve(true))

    await backoff(mockFn, { maxWait: 1 })
    expect(mockFn).toBeCalledTimes(2)
  })

  it('handles negative attempts', async () => {
    const mockFn = vi.fn(() => rejectError())

    try {
      await backoff(mockFn, { attempt: -1, maxRetries: 1 })
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {}
    expect(mockFn).toBeCalledTimes(2)
  })

  it('handles attempt 0', async () => {
    const mockFn = vi.fn(() => rejectError())

    try {
      await backoff(mockFn, { attempt: 0, maxRetries: 1 })
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {}
    expect(mockFn).toBeCalledTimes(2)
  })

  it('handles negative retries', async () => {
    const mockFn = vi.fn(() => rejectError())

    try {
      await backoff(mockFn, { attempt: -1, maxRetries: -1 })
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {}
    expect(mockFn).toBeCalledTimes(1)
  })

  it('handles no retries', async () => {
    const mockFn = vi.fn(() => rejectError())

    try {
      await backoff(mockFn, { attempt: -1, maxRetries: 0 })
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {}
    expect(mockFn).toBeCalledTimes(1)
  })
})
