import { DateTime } from 'luxon'

import { isSameDate, isToday } from './dateTimeHelpers'

describe('dateTimeHelpers', () => {
  describe('isSameDate', () => {
    it('is true when the date matches', () => {
      expect(
        isSameDate(DateTime.local(2022, 12, 2), DateTime.local(2022, 12, 2, 8))
      ).toBe(true)
    })

    it('is false when the date doesnt match', () => {
      expect(
        isSameDate(DateTime.local(2022, 12, 3), DateTime.local(2022, 12, 2))
      ).toBe(false)

      expect(
        isSameDate(DateTime.local(2022, 11, 2), DateTime.local(2022, 12, 2))
      ).toBe(false)

      expect(
        isSameDate(DateTime.local(2021, 12, 2), DateTime.local(2022, 12, 2))
      ).toBe(false)
    })
  })

  describe('isToday', () => {
    it('is true when the date is today', () => {
      expect(isToday(DateTime.now().startOf('day'))).toBe(true)
    })
  })
})
