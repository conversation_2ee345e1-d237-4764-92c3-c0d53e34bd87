import { useMemo } from 'react'

import { useQuery } from '@apollo/client'

import { GetSiteOptionsFilter } from 'src/modules/site/__generated__'
import { GET_SITE_OPTIONS_FILTER } from 'src/modules/site/queries'

const useSiteOptions = () => {
  const { data: filtersData, loading } = useQuery<GetSiteOptionsFilter>(
    GET_SITE_OPTIONS_FILTER
  )
  return useMemo(() => {
    return {
      sites: filtersData?.sites.edges ?? [],
      isLoading: loading,
    }
  }, [filtersData?.sites.edges, loading])
}

const useRoomOptions = () => {
  const { sites } = useSiteOptions()

  return useMemo(
    () =>
      sites.reduce<
        GetSiteOptionsFilter['sites']['edges'][number]['node']['rooms']['edges'][number][]
      >((acc, site) => acc.concat(site.node.rooms.edges), []),
    [sites]
  )
}

export { useRoomOptions, useSiteOptions }
