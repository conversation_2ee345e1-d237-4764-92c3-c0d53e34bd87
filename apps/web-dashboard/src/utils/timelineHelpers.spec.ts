import { DateTime } from 'luxon'

import {
  calculateHoursFromWorkingHoursAndMaxHours,
  calculatePillPositionAndSizeHelper,
  calculateWorkingHours,
} from './timelineHelpers'

describe('timelineHelpers', () => {
  describe('calculateWorkingHours', () => {
    it('returns an array of hours if passed a valid min and max time', () => {
      const result = calculateWorkingHours(
        DateTime.fromISO('2020-01-01T01:00:00.000Z'),
        DateTime.fromISO('2020-01-01T02:30:00.000Z')
      )
      const expected = [
        DateTime.fromISO('2020-01-01T01:00:00.000Z'),
        DateTime.fromISO('2020-01-01T02:00:00.000Z'),
        DateTime.fromISO('2020-01-01T03:00:00.000Z'),
      ]
      expect(result).toEqual(expected)
    })

    it('returns an empty array if passed a negative time delta', () => {
      const result = calculateWorkingHours(
        DateTime.fromISO('2020-01-01T02:00:00.000Z'),
        DateTime.fromISO('2020-01-01T01:00:00.000Z')
      )
      expect(result).toEqual([])
    })
  })

  describe('calculateHoursFromWorkingHoursAndMaxHours', () => {
    it('returns an array like workingHours if workingHours.length <= maxHours', () => {
      const workingHours: DateTime[] = [
        DateTime.fromISO('2020-01-01T01:00:00.000Z'),
        DateTime.fromISO('2020-01-01T02:00:00.000Z'),
        DateTime.fromISO('2020-01-01T03:00:00.000Z'),
      ]
      const maxHours = 3
      const result = calculateHoursFromWorkingHoursAndMaxHours(
        workingHours,
        maxHours
      )
      expect(result).toEqual(workingHours)
    })

    it('returns an shortened array if workingHours.length > maxHours', () => {
      const workingHours: DateTime[] = [
        DateTime.fromISO('2020-01-01T01:00:00.000Z'),
        DateTime.fromISO('2020-01-01T02:00:00.000Z'),
        DateTime.fromISO('2020-01-01T03:00:00.000Z'),
        DateTime.fromISO('2020-01-01T04:00:00.000Z'),
        DateTime.fromISO('2020-01-01T05:00:00.000Z'),
      ]
      const maxHours = 3
      const result = calculateHoursFromWorkingHoursAndMaxHours(
        workingHours,
        maxHours
      )
      const expected = [
        DateTime.fromISO('2020-01-01T01:00:00.000Z'),
        DateTime.fromISO('2020-01-01T03:00:00.000Z'),
        DateTime.fromISO('2020-01-01T05:00:00.000Z'),
      ]
      expect(result).toEqual(expected)
    })

    it('adds hours to the end of workingHours if necessary', () => {
      const workingHours: DateTime[] = [
        DateTime.fromISO('2020-01-01T01:00:00.000Z'),
        DateTime.fromISO('2020-01-01T02:00:00.000Z'),
        DateTime.fromISO('2020-01-01T03:00:00.000Z'),
        DateTime.fromISO('2020-01-01T04:00:00.000Z'),
      ]
      const maxHours = 3
      const result = calculateHoursFromWorkingHoursAndMaxHours(
        workingHours,
        maxHours
      )
      const expected = [
        DateTime.fromISO('2020-01-01T01:00:00.000Z'),
        DateTime.fromISO('2020-01-01T03:00:00.000Z'),
        DateTime.fromISO('2020-01-01T05:00:00.000Z'),
      ]
      expect(result).toEqual(expected)
    })
  })

  describe('calculatePillPositionAndSizeHelper', () => {
    it('correctly calculates position and size for a large pill in the middle of the timeline', () => {
      const startTime = DateTime.fromISO('2020-01-01T01:00:00.000Z')
      const endTime = DateTime.fromISO('2020-01-01T02:00:00.000Z')
      const timelineWidth = 1000
      const minHour = DateTime.fromISO('2020-01-01T00:00:00.000Z')
      const maxHour = DateTime.fromISO('2020-01-01T05:00:00.000Z')
      const result = calculatePillPositionAndSizeHelper({
        startTime: startTime,
        timelineWidth: timelineWidth,
        minHour: minHour,
        maxHour: maxHour,
        endTime: endTime,
      })
      const expected = {
        left: '20%',
        widthPx: 200,
        widthPct: '20%',
        forecastedWidthPx: 0,
        doesOverflowMaxTime: false,
        doesOverflowMinTime: false,
        gradientWidthPx: 0,
      }
      expect(result).toEqual(expected)
    })

    it('correctly calculates position and size for a large pill in the middle of the timeline with a live time past prediction', () => {
      const startTime = DateTime.fromISO('2020-01-01T01:00:00.000Z')
      const endTime = DateTime.fromISO('2020-01-01T02:00:00.000Z')
      const liveTime = DateTime.fromISO('2020-01-01T02:15:00.000Z')
      const timelineWidth = 1000
      const minHour = DateTime.fromISO('2020-01-01T00:00:00.000Z')
      const maxHour = DateTime.fromISO('2020-01-01T05:00:00.000Z')
      const result = calculatePillPositionAndSizeHelper({
        startTime: startTime,
        endTime: endTime,
        timelineWidth: timelineWidth,
        minHour: minHour,
        maxHour: maxHour,
        minWidth: 20,
        liveTime,
      })

      const expected = {
        left: '20%',
        widthPx: 250,
        widthPct: '25%',
        forecastedWidthPx: 0,
        doesOverflowMaxTime: false,
        doesOverflowMinTime: false,
        gradientWidthPx: 50,
      }
      expect(result).toEqual(expected)
    })

    it('correctly calculates position and size for a large pill in the middle of the timeline with live time', () => {
      const startTime = DateTime.fromISO('2020-01-01T01:00:00.000Z')
      const endTime = DateTime.fromISO('2020-01-01T02:00:00.000Z')
      const liveTime = DateTime.fromISO('2020-01-01T01:45:00.000Z')
      const timelineWidth = 1000
      const minHour = DateTime.fromISO('2020-01-01T00:00:00.000Z')
      const maxHour = DateTime.fromISO('2020-01-01T05:00:00.000Z')
      const result = calculatePillPositionAndSizeHelper({
        startTime,
        endTime,
        timelineWidth,
        minHour,
        maxHour,
        minWidth: 20,
        liveTime,
      })

      const expected = {
        left: '20%',
        widthPx: 150,
        widthPct: '14.999999999999996%',
        forecastedWidthPx: 50,
        doesOverflowMaxTime: false,
        doesOverflowMinTime: false,
        gradientWidthPx: 0,
      }
      expect(result).toEqual(expected)
    })

    it('correctly calculates position and size for a tiny pill in the middle of the timeline', () => {
      const startTime = DateTime.fromISO('2020-01-01T01:00:00.000Z')
      const endTime = DateTime.fromISO('2020-01-01T01:01:00.000Z')
      const timelineWidth = 1000
      const minHour = DateTime.fromISO('2020-01-01T00:00:00.000Z')
      const maxHour = DateTime.fromISO('2020-01-01T05:00:00.000Z')
      const result = calculatePillPositionAndSizeHelper({
        startTime,
        endTime,
        timelineWidth,
        minHour,
        maxHour,
      })
      const expected = {
        left: '19.166666666666668%',
        widthPx: 20,
        widthPct: '2%',
        forecastedWidthPx: 0,
        doesOverflowMaxTime: false,
        doesOverflowMinTime: false,
        gradientWidthPx: 0,
      }
      expect(result).toEqual(expected)
    })

    it('correctly calculates position and size for a tiny pill at the end of the timeline', () => {
      const startTime = DateTime.fromISO('2020-01-01T04:58:00.000Z')
      const endTime = DateTime.fromISO('2020-01-01T04:59:00.000Z')
      const timelineWidth = 1000
      const minHour = DateTime.fromISO('2020-01-01T00:00:00.000Z')
      const maxHour = DateTime.fromISO('2020-01-01T05:00:00.000Z')
      const result = calculatePillPositionAndSizeHelper({
        startTime,
        endTime,
        timelineWidth,
        minHour,
        maxHour,
      })
      const expected = {
        left: '98%',
        widthPx: 20,
        widthPct: '2%',
        forecastedWidthPx: 0,
        doesOverflowMaxTime: false,
        doesOverflowMinTime: false,
        gradientWidthPx: 0,
      }
      expect(result).toEqual(expected)
    })

    it('correctly calculates position and size for a tiny pill at the beginning of the timeline', () => {
      const startTime = DateTime.fromISO('2020-01-01T00:00:00.000Z')
      const endTime = DateTime.fromISO('2020-01-01T00:01:00.000Z')
      const timelineWidth = 1000
      const minHour = DateTime.fromISO('2020-01-01T00:00:00.000Z')
      const maxHour = DateTime.fromISO('2020-01-01T05:00:00.000Z')
      const result = calculatePillPositionAndSizeHelper({
        startTime,
        endTime,
        timelineWidth,
        minHour,
        maxHour,
      })
      const expected = {
        left: '0%',
        widthPx: 20,
        widthPct: '2%',
        forecastedWidthPx: 0,
        doesOverflowMaxTime: false,
        doesOverflowMinTime: false,
        gradientWidthPx: 0,
      }
      expect(result).toEqual(expected)
    })
  })

  it('correctly calculates position and size for a pill overlapping the beginning of the timeline', () => {
    const startTime = DateTime.fromISO('2020-01-01T00:00:00.000Z')
    const endTime = DateTime.fromISO('2020-01-01T01:30:00.000Z')
    const timelineWidth = 1000
    const minHour = DateTime.fromISO('2020-01-01T01:00:00.000Z')
    const maxHour = DateTime.fromISO('2020-01-01T05:00:00.000Z')
    const result = calculatePillPositionAndSizeHelper({
      startTime,
      endTime,
      timelineWidth,
      minHour,
      maxHour,
    })
    const expected = {
      left: '0%',
      widthPx: 125,
      widthPct: '12.5%',
      forecastedWidthPx: 0,
      doesOverflowMaxTime: false,
      doesOverflowMinTime: true,
      gradientWidthPx: 0,
    }
    expect(result).toEqual(expected)
  })

  it('correctly calculates position and size for a pill overlapping the end of the timeline', () => {
    const startTime = DateTime.fromISO('2020-01-01T04:00:00.000Z')
    const endTime = DateTime.fromISO('2020-01-01T05:30:00.000Z')
    const timelineWidth = 1000
    const minHour = DateTime.fromISO('2020-01-01T01:00:00.000Z')
    const maxHour = DateTime.fromISO('2020-01-01T05:00:00.000Z')
    const result = calculatePillPositionAndSizeHelper({
      startTime,
      endTime,
      timelineWidth,
      minHour,
      maxHour,
    })
    const expected = {
      left: '75%',
      widthPx: 250,
      widthPct: '25%',
      forecastedWidthPx: 0,
      doesOverflowMaxTime: true,
      doesOverflowMinTime: false,
      gradientWidthPx: 0,
    }
    expect(result).toEqual(expected)
  })
})
