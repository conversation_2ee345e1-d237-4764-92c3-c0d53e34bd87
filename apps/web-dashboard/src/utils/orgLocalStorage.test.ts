import { ACTIVE_ORG_ID_KEY } from 'src/router/constants'

import orgLocalStorage from './orgLocalStorage'

describe('orgLocalStorage', () => {
  const DELIMITER = '-'
  const TEST_KEY = 'testKey'
  const TEST_VALUE = 'testValue'
  const ORG_ID = 'org123'

  beforeEach(() => {
    localStorage.clear()
  })

  describe('setItem', () => {
    it('should set an item without an active organization', () => {
      localStorage.setItem(ACTIVE_ORG_ID_KEY, JSON.stringify(null))

      orgLocalStorage.setItem(TEST_KEY, TEST_VALUE)

      expect(localStorage.getItem(TEST_KEY)).toBe(TEST_VALUE)
    })

    it('should set an item with an active organization', () => {
      localStorage.setItem(ACTIVE_ORG_ID_KEY, JSON.stringify(ORG_ID))

      orgLocalStorage.setItem(TEST_KEY, TEST_VALUE)

      expect(localStorage.getItem(`${ORG_ID}${DELIMITER}${TEST_KEY}`)).toBe(
        TEST_VALUE
      )
    })
  })

  describe('getItem', () => {
    it('should get an item without an active organization', () => {
      localStorage.setItem(ACTIVE_ORG_ID_KEY, JSON.stringify(null))
      localStorage.setItem(TEST_KEY, TEST_VALUE)

      const result = orgLocalStorage.getItem(TEST_KEY)

      expect(result).toBe(TEST_VALUE)
    })

    it('should get an item with an active organization', () => {
      localStorage.setItem(ACTIVE_ORG_ID_KEY, JSON.stringify(ORG_ID))
      localStorage.setItem(`${ORG_ID}${DELIMITER}${TEST_KEY}`, TEST_VALUE)

      const result = orgLocalStorage.getItem(TEST_KEY)

      expect(result).toBe(TEST_VALUE)
    })
  })

  describe('removeItem', () => {
    it('should remove an item without an active organization', () => {
      localStorage.setItem(ACTIVE_ORG_ID_KEY, JSON.stringify(null))
      localStorage.setItem(TEST_KEY, TEST_VALUE)

      orgLocalStorage.removeItem(TEST_KEY)

      expect(localStorage.getItem(TEST_KEY)).toBeNull()
    })

    it('should remove an item with an active organization', () => {
      localStorage.setItem(ACTIVE_ORG_ID_KEY, JSON.stringify(ORG_ID))
      localStorage.setItem(`${ORG_ID}${DELIMITER}${TEST_KEY}`, TEST_VALUE)

      orgLocalStorage.removeItem(TEST_KEY)

      expect(
        localStorage.getItem(`${ORG_ID}${DELIMITER}${TEST_KEY}`)
      ).toBeNull()
    })
  })
})
