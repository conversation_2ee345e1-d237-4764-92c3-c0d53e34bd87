import {
  ANESTHESIOLOGIST_ROLES,
  CaseStaffPlanRole,
  getStaffMembersByRole,
} from './roles'

const mockStaff = (id: string, role: string) => ({
  id,
  role,
  displayName: 'fake',
  firstName: 'fake',
  lastName: 'fake',
})

describe('getStaffMembersByRole', () => {
  it('displays empty planned roles if no staff', () => {
    const mockCase = {
      staff: [],
      staffPlan: [],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result).toEqual([
      { role: CaseStaffPlanRole.Anesthesiologist, staff: [] },
      { role: CaseStaffPlanRole.CRNA, staff: [] },
      { role: CaseStaffPlanRole.Circulator, staff: [] },
      { role: CaseStaffPlanRole.ScrubTech, staff: [] },
    ])
  })

  it('uses planned staff roles from staffPlan and EHR staff', () => {
    const staff = mockStaff('2', ANESTHESIOLOGIST_ROLES[1])
    const staffPlan = mockStaff('1', ANESTHESIOLOGIST_ROLES[0])
    const mockCase = {
      staff: [staff],
      staffPlan: [staffPlan],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result[0]).toEqual({
      role: CaseStaffPlanRole.Anesthesiologist,
      staff: [staffPlan, staff],
    })
  })

  it('prefers planned roles to EHR roles', () => {
    const staffPlan = mockStaff('1', 'plan')
    const mockCase = {
      staff: [mockStaff('1', 'ehr')],
      staffPlan: [staffPlan],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result[0]).toEqual({ role: 'plan', staff: [staffPlan] })
  })

  it('fills in with EHR staff roles', () => {
    const staff = mockStaff('1', 'ehr')
    const mockCase = {
      staff: [staff],
      staffPlan: [],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result[0]).toEqual({ role: 'ehr', staff: [staff] })
  })

  it('fills in with empty planned staff roles', () => {
    const staffPlan = mockStaff('1', 'plan')
    const mockCase = {
      staff: [],
      staffPlan: [staffPlan],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result).toEqual([
      { role: 'plan', staff: [staffPlan] },
      { role: CaseStaffPlanRole.Anesthesiologist, staff: [] },
      { role: CaseStaffPlanRole.CRNA, staff: [] },
      { role: CaseStaffPlanRole.Circulator, staff: [] },
    ])
  })
})
