import { isEqual, isUndefined } from 'lodash'

import { OrderBy } from '@apella/component-library'
import { useUrlCleanParamAndLocalStorageState } from '@apella/hooks'

import { Direction } from '../__generated__/globalTypes'

const UNDEFINED_ORDER_BY_STATE: OrderBy[] | undefined = [
  {
    sort: 'undefined',
    direction: Direction.DESC,
  },
]

const useSortOrderConversion = (
  key: string,
  defaultOrderByState: OrderBy[] | undefined
) => {
  const [orderByState, setOrderByState] = useUrlCleanParamAndLocalStorageState<
    OrderBy[] | undefined
  >(key, defaultOrderByState)
  let stateToReturn
  if (isUndefined(orderByState)) {
    stateToReturn = defaultOrderByState
  } else if (isEqual(orderByState, UNDEFINED_ORDER_BY_STATE)) {
    stateToReturn = undefined
  } else {
    stateToReturn = orderByState
  }
  const localSetOrderByState = (newValue: OrderBy[] | undefined) => {
    if (isEqual(newValue, undefined)) {
      setOrderByState(UNDEFINED_ORDER_BY_STATE)
    } else if (isEqual(newValue, defaultOrderByState)) {
      setOrderByState(undefined)
    } else {
      setOrderByState(newValue)
    }
  }
  return [stateToReturn, localSetOrderByState] as const
}

export default useSortOrderConversion
