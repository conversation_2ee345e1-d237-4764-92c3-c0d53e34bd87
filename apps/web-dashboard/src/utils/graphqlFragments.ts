import { gql } from '@apollo/client'

export const PAGE_CURSOR_FRAGMENT = gql`
  fragment PageCursorFragment on PageCursor {
    cursor
    isCurrent
    page
  }
`

export const PAGE_CURSORS_FRAGMENT = gql`
  ${PAGE_CURSOR_FRAGMENT}
  fragment PageCursorsFragment on PageCursors {
    around {
      ...PageCursorFragment
    }
    first {
      ...PageCursorFragment
    }
    last {
      ...PageCursorFragment
    }
    next {
      ...PageCursorFragment
    }
    previous {
      ...PageCursorFragment
    }
  }
`
