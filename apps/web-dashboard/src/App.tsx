import { StrictMode } from 'react'
import {
  create<PERSON><PERSON>er<PERSON>outer,
  LoaderFunction<PERSON>rgs,
  Outlet,
  useLoaderData,
} from 'react-router'
import { RouterProvider } from 'react-router/dom'
import { ToastContainer } from 'react-toastify'

import { ThemeProvider } from '@emotion/react'

import { ApolloProvider } from '@apollo/client'
import { CubeProvider } from '@cubejs-client/react'
import { wrapCreateBrowserRouterV7 } from '@sentry/react'
import { LDProvider } from 'launchdarkly-react-client-sdk'

import { GlobalStyles, theme } from '@apella/component-library'
import AuthBoundary from 'src/router/AuthBoundary'
import { AppRefreshProvider } from 'src/router/components/AppRefresh'
import { MenuProvider } from 'src/router/components/MenuContext'
import { PageTemplate } from 'src/router/components/PageTemplate'
import { datadogRumMiddleware } from 'src/router/middleware/datadogRum'
import { settings } from 'src/settings'

import { Auth0ProviderWithClient } from './api/Auth0ProviderWithClient'
import {
  ApellaDataDependencies,
  createDataDependencies,
} from './api/clients/apellaDataDependencies'
import { LoadingBackdrop } from './components/LoadingBackdrop'
import { UnexpectedErrorBoundary } from './components/UnexpectedErrorBoundary'
import { isBoardsHost } from './pages/Boards'
import { appRoutes } from './router/appRoutes'
import { PageWrapper } from './router/components/PageWrapper'
import { createDataStrategy } from './router/dataStrategy'
import {
  auth0Middleware,
  launchDarklyMiddleware,
  apolloClientMiddleware,
  permissionCheckRedirect,
  cubejsMiddleware,
  ensureAuthenticated,
} from './router/middleware'
import OrgAuth from './router/OrgAuth'
import { ApellaRouteContext } from './router/types'
import { AnalyticsEventsProvider } from './utils/analyticsEvents'

const loader = (_args: LoaderFunctionArgs, context: unknown) => {
  return context as ApellaRouteContext
}

const Root = ({
  auth0,
  boardsAuth0,
  ldClient,
  apolloClient,
}: ApellaDataDependencies): React.JSX.Element => {
  const auth0Client = isBoardsHost(window.location.host) ? boardsAuth0 : auth0
  const { cube } = useLoaderData<ReturnType<typeof loader>>()

  return (
    <>
      <ToastContainer />
      <AppRefreshProvider>
        <Auth0ProviderWithClient client={auth0Client}>
          <LDProvider
            clientSideID={settings.launchDarkly.clientSideID}
            ldClient={ldClient}
          >
            <AnalyticsEventsProvider>
              <ApolloProvider client={apolloClient}>
                <AuthBoundary>
                  <OrgAuth>
                    <CubeProvider cubeApi={cube}>
                      <MenuProvider>
                        <PageTemplate>
                          <Outlet />
                        </PageTemplate>
                      </MenuProvider>
                    </CubeProvider>
                  </OrgAuth>
                </AuthBoundary>
              </ApolloProvider>
            </AnalyticsEventsProvider>
          </LDProvider>
        </Auth0ProviderWithClient>
      </AppRefreshProvider>
    </>
  )
}

const sentryCreateBrowserRouter = wrapCreateBrowserRouterV7(createBrowserRouter)

const dependencies = createDataDependencies()

const router = sentryCreateBrowserRouter(
  [
    {
      path: '/',
      loader,
      element: <Root {...dependencies} />,
      handle: {
        // Top level middleware
        middleware: [
          auth0Middleware,
          ensureAuthenticated,
          launchDarklyMiddleware,
          apolloClientMiddleware,
          cubejsMiddleware,
          datadogRumMiddleware,
        ],
      },
      children: [
        {
          Component: PageWrapper,
          handle: {
            middleware: [permissionCheckRedirect],
          },
          // Middleware only run if the dataStrategy runs
          // The dataStrategy will only run if the route has a loader
          loader: () => null,
          // This pathless route middleware should run on every navigation
          // This avoids adding the permission check redirect middleware to every route
          shouldRevalidate: () => true,
          children: appRoutes,
        },
      ],
      HydrateFallback: LoadingBackdrop,
      ErrorBoundary: UnexpectedErrorBoundary,
    },
  ],
  {
    dataStrategy: createDataStrategy(dependencies),
  }
)

const App = (): React.JSX.Element => {
  return (
    <StrictMode>
      <ThemeProvider theme={theme}>
        <GlobalStyles />
        <RouterProvider router={router} />
      </ThemeProvider>
    </StrictMode>
  )
}

export default App
