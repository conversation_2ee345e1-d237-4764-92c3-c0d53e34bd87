import { ApellaUser } from 'src/modules/user/types'

export const DEFAULT_STORYBOOK_USER: ApellaUser = {
  userId: '',
  email: '',
  isApellaEmployee: false,
  userOrganizations: [],
  currentOrganization: {
    node: {
      id: 'home_hospital',
      auth0OrgId: 'org_123456',
      name: 'Home Hospital',
      sites: {
        edges: [
          {
            node: {
              id: 'lab_1',
              name: 'Apella Lab',
              timezone: 'America/Chicago',
              rooms: {
                edges: [],
                __typename: 'RoomConnection',
              },
              __typename: 'Site',
            },
            __typename: 'SiteEdge',
          },
        ],
        __typename: 'SiteConnection',
      },
      __typename: 'Organization',
    },
    __typename: 'OrganizationEdge',
  },
}
