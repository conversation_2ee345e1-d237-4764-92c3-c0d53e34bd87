/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  ApolloClient,
  NetworkStatus,
  ObservableQuery,
  OperationVariables,
  QueryResult,
  SubscribeToMoreOptions,
} from '@apollo/client'

export const mockUseQueryResult = <T>({
  data,
  loading = false,
}: {
  data: T
  loading?: boolean
}): QueryResult<T, any> => ({
  loading,
  data,
  client: vi.mocked(ApolloClient<any>) as unknown as ApolloClient<any>,
  observable: vi.mocked(
    ObservableQuery<any, any>
  ) as unknown as ObservableQuery<T, any>,
  networkStatus: NetworkStatus.ready,
  called: true,
  startPolling: () => {
    return
  },
  stopPolling: () => {
    return
  },
  subscribeToMore: function <
    TSubscriptionData = T,
    TSubscriptionVariables extends OperationVariables = OperationVariables,
  >(
    options: SubscribeToMoreOptions<
      T,
      TSubscriptionVariables,
      TSubscriptionData
    >
  ) {
    return () => {
      return
    }
  },
  updateQuery(): void {
    throw new Error('Function not implemented.')
  },
  refetch: () => {
    throw new Error('Function not implemented.')
  },
  reobserve: () => {
    throw new Error('Function not implemented.')
  },
  variables: undefined,
  fetchMore: () => {
    throw new Error('Function not implemented.')
  },
})
