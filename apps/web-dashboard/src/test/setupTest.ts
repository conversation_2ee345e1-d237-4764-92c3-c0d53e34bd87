import { createSerializer } from '@emotion/jest'
import { vi } from 'vitest'
import createFetchMock from 'vitest-fetch-mock'

import '@testing-library/jest-dom/vitest'

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

window.ResizeObserver = ResizeObserver

class IntersectionObserver {
  readonly root = null
  readonly rootMargin = '0'
  readonly thresholds = []
  observe() {}
  unobserve() {}
  disconnect() {}
  takeRecords() {
    return []
  }
}

window.IntersectionObserver = IntersectionObserver

createFetchMock(vi).enableMocks()

expect.addSnapshotSerializer(createSerializer())
