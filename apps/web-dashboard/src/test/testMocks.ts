import { DateTime } from 'luxon'

import {
  CaseStatusName,
  CaseType,
  PatientClass,
} from '../__generated__/globalTypes'
import { ApellaCase, Case, LiveEvent } from '../pages/types'

export const mockCase = (
  id: string,
  scheduledStartTime: DateTime,
  scheduledEndTime: DateTime
): Case => ({
  id,
  scheduledStartTime,
  scheduledEndTime,
  room: {
    id: 'room1',
    name: 'Room 1',
  },
  staff: [
    {
      id: '1',
      firstName: 'Bil',
      lastName: 'Bo',
      displayName: 'Bo',
      role: 'Primary',
    },
  ],
  procedures: [],
  isAddOn: false,
  isFirstCase: false,
  isInFlipRoom: false,
  patientClass: PatientClass.INPATIENT,
  caseFlags: [],
})

export const mockApellaCase = (
  id: string,
  startTime: DateTime,
  endTime: DateTime,
  caseObj?: Case,
  type = CaseType.FORECAST
): ApellaCase => ({
  id,
  case: caseObj,
  startTime,
  endTime,
  type,
  site: {
    id: 'site1',
    name: 'Site 1',
  },
  status: {
    name: CaseStatusName.SCHEDULED,
  },
  room: {
    id: 'room1',
    name: 'Room 1',
  },
})

export const mockEvent = (
  name: string,
  id: string,
  startTime: DateTime
): LiveEvent => {
  return {
    name,
    id,
    startTime: startTime.toISO(),
    attrs: null,
  }
}
