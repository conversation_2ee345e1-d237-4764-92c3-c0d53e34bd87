import { LDProvider } from 'launchdarkly-react-client-sdk'

import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'

interface MockLDProviderProps {
  children: React.ReactNode
  flags: Partial<WebDashboardFeatureFlagSet>
}

const noop = () => undefined

const MockLDProvider = ({ flags, children }: MockLDProviderProps) => (
  <LDProvider
    clientSideID=""
    flags={flags}
    options={{
      bootstrap: flags,
      logger: { debug: noop, error: noop, warn: noop, info: noop },
    }}
  >
    {children}
  </LDProvider>
)

export default MockLDProvider
