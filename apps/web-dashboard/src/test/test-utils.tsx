import { FC, ReactElement, ReactNode } from 'react'

import { ThemeProvider } from '@emotion/react'

import { render, RenderOptions } from '@testing-library/react'

import { GlobalStyles, theme } from '@apella/component-library'

/**
 * https://testing-library.com/docs/react-testing-library/setup#custom-render
 */

type AllProvidersProps = {
  children: ReactNode
}

const AllProviders: FC<AllProvidersProps> = ({
  children,
}: AllProvidersProps) => (
  <ThemeProvider theme={theme}>
    <GlobalStyles />
    {children}
  </ThemeProvider>
)

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllProviders, ...options })

export * from '@testing-library/react'

export { customRender as render }
