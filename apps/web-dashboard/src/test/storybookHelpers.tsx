import { MINIMAL_VIEWPORTS } from '@storybook/addon-viewport'

import { breakpoints } from '@apella/component-library'

// https://storybook.js.org/docs/essentials/viewport#use-a-detailed-set-of-devices
export const viewports = {
  mobile1: MINIMAL_VIEWPORTS.mobile1,
  ...MINIMAL_VIEWPORTS,
  xLarge: {
    name: 'Desktop - XL',
    styles: { width: breakpoints.xl, height: '1000px' },
    type: 'desktop',
  },
  xxLarge: {
    name: 'Desktop - XXL',
    styles: { width: breakpoints.xxl, height: '1080px' },
    type: 'desktop',
  },
}
interface Mode {
  viewport: keyof typeof viewports
}
interface Modes {
  mobile: Mode
  xLarge: Mode
  xxLarge: Mode
}
export const modes: Modes = {
  mobile: {
    viewport: 'mobile1',
  },
  xLarge: {
    viewport: 'xLarge',
  },
  xxLarge: {
    viewport: 'xxLarge',
  },
}
