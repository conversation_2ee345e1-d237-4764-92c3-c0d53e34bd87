import { ReactNode } from 'react'

import cubejs from '@cubejs-client/core'
import { CubeProvider } from '@cubejs-client/react'

import { TimezoneProvider } from 'src/Contexts'

export const MockAppProvider = ({ children }: { children: ReactNode }) => {
  const mockCubeApi = cubejs({
    apiUrl: `dummy/cubejs-api/v1`,
    headers: { Authorization: `accessToken` },
  })
  return (
    <TimezoneProvider>
      <CubeProvider cubeApi={mockCubeApi}>{children}</CubeProvider>
    </TimezoneProvider>
  )
}
