import { gql, useQuery } from '@apollo/client'

import {
  GetRoomPrivacy,
  GetRoomPrivacyVariables,
} from 'src/hooks/__generated__'
import { LIVE_POLL_INTERVAL_MS } from 'src/pages/Live/consts'
import { usePolling } from 'src/utils/usePolling'

const GET_ROOM_PRIVACY = gql`
  query GetRoomPrivacy($roomId: String!) {
    room(id: $roomId) {
      id
      privacyEnabled
    }
  }
`

export const useRoomPrivacy = ({
  skip = false,
  pollingInterval = LIVE_POLL_INTERVAL_MS,
  roomId,
}: {
  skip?: boolean
  pollingInterval?: number
  roomId: string
}) => {
  const { loading, data, refetch, startPolling, stopPolling } = useQuery<
    GetRoomPrivacy,
    GetRoomPrivacyVariables
  >(GET_ROOM_PRIVACY, {
    variables: {
      roomId: roomId,
    },
    skip,
  })

  usePolling({
    refetch,
    startPolling,
    stopPolling,
    interval: pollingInterval,
    skip,
  })

  return { loading, data }
}
