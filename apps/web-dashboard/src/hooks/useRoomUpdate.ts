import { useCallback } from 'react'

import { gql, useMutation } from '@apollo/client'

import {
  UpdateRoomPrivacy,
  UpdateRoomPrivacyVariables,
} from 'src/hooks/__generated__'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

export enum PrivacyReason {
  PRIVACY_ENABLED = 'PRIVACY_ENABLED',
  PATIENT_IN_ROOM = 'PATIENT_IN_ROOM',
}

const UPDATE_ROOM_PRIVACY = gql`
  mutation UpdateRoomPrivacy($input: RoomUpdateConfigurationInput!) {
    roomUpdateConfiguration(input: $input) {
      success
      updatedRoom {
        id
        privacyEnabled
      }
    }
  }
`

export const useRoomUpdate = ({
  onMutationCompleted,
  roomId,
  isPrivacyEnabled,
}: {
  onMutationCompleted?: (data: UpdateRoomPrivacy) => void
  roomId: string
  isPrivacyEnabled: boolean
}) => {
  const eventsLogger = useAnalyticsEventLogger()

  const onCompleted = useCallback(
    (data: UpdateRoomPrivacy) => {
      if (data.roomUpdateConfiguration?.success) {
        eventsLogger(EVENTS.VIDEO_PRIVACY_TOGGLED, {
          roomId: data.roomUpdateConfiguration?.updatedRoom?.id,
          privacyEnabled:
            data.roomUpdateConfiguration?.updatedRoom?.privacyEnabled,
        })
      }

      if (onMutationCompleted) {
        return onMutationCompleted(data)
      }
    },
    [eventsLogger, onMutationCompleted]
  )
  const [updateRoomPrivacy, { loading }] = useMutation<
    UpdateRoomPrivacy,
    UpdateRoomPrivacyVariables
  >(UPDATE_ROOM_PRIVACY, {
    onCompleted,
  })

  const toggleRoomPrivacy = useCallback(() => {
    updateRoomPrivacy({
      variables: {
        input: {
          privacyEnabled: !isPrivacyEnabled,
          id: roomId,
        },
      },
    })
  }, [roomId, updateRoomPrivacy, isPrivacyEnabled])

  return { toggleRoomPrivacy, loading }
}
