import { useMemo } from 'react'

import { sortBy } from 'lodash'

import {
  MultiFilterWithCount,
  MultiFilterWithCountProps,
} from 'src/components/Filters/FilterWithCount'

export const DEFAULT_STAFF = undefined
export interface StaffShape {
  node: {
    id: string
    lastName: string
    firstName: string
    name: string
    count: number
  }
}

export function StaffFilter<T extends StaffShape>({
  items,
  label,
  ...rest
}: MultiFilterWithCountProps<T>): React.JSX.Element {
  const surgeonOptions = useMemo(() => {
    return sortBy(items, [(s) => -(s.node?.count ?? 0), (s) => s.node.name])
  }, [items])

  return (
    <MultiFilterWithCount
      items={surgeonOptions}
      label={label ?? 'Primary Surgeons'}
      {...rest}
    />
  )
}
