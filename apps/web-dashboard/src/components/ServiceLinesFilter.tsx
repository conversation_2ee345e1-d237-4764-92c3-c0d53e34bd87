import { ComponentProps, useMemo } from 'react'

import { gql, useQuery } from '@apollo/client'
import { Query } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'

import { MultiSelect, Option } from '@apella/component-library'
import { SERVICE_LINES } from 'src/modules/cube/types/dataCubes'
import { CountField } from 'src/pages/Insights/types'

import { GetServiceLines } from './__generated__'

export const DEFAULT_SERVICE_LINES = undefined

const GET_SERVICE_LINES = gql`
  query GetServiceLines {
    serviceLines(query: {}) {
      id
      name
      orgId
    }
  }
`

export const useServiceLines = () => {
  const { loading, data } = useQuery<GetServiceLines>(GET_SERVICE_LINES)
  const serviceLines = useMemo(() => {
    return (data?.serviceLines ?? []).filter(
      (serviceLine): serviceLine is typeof serviceLine & { name: string } =>
        !!serviceLine.name
    )
  }, [data])

  return { loading, serviceLines }
}

export const ServiceLinesFilter = ({
  label = 'Service Lines',
  name = 'service-lines-filter',
  bulkSelect = true,
  ...rest
}: Omit<ComponentProps<typeof MultiSelect>, 'children' | 'name'> & {
  name?: string
}) => {
  const { serviceLines } = useServiceLines()

  return (
    <MultiSelect
      name={name}
      bulkSelect={bulkSelect}
      disabled={!serviceLines}
      label={label}
      {...rest}
    >
      {serviceLines.map((serviceLine) =>
        serviceLine.name ? (
          <Option
            key={serviceLine.id}
            value={serviceLine.id}
            label={serviceLine.name}
          />
        ) : null
      )}
    </MultiSelect>
  )
}

export const ServiceLinesFilterWithCount = ({
  label = 'Service Lines',
  name = 'service-lines-filter',
  cubeParams,
  measure,
  bulkSelect = true,
  ...rest
}: Omit<ComponentProps<typeof MultiSelect>, 'children' | 'name'> & {
  cubeParams: Query
  measure: CountField
  name?: string
}) => {
  const { loading: serviceLinesLoading, serviceLines } = useServiceLines()

  const { isLoading: cubedataLoading, resultSet } = useCubeQuery({
    ...cubeParams,
    // Omit the service lines filter from the query
    filters: cubeParams.filters?.filter(
      (filter) => !('member' in filter) || filter.member !== SERVICE_LINES.ID
    ),
    dimensions: [SERVICE_LINES.NAME, SERVICE_LINES.ID],
    measures: [measure],
    order: { [measure]: 'desc' },
  })

  const serviceLinesWithCount = useMemo(() => {
    const countMap = new Map<string, number>()

    resultSet?.rawData().forEach((serviceLine) => {
      countMap.set(
        `${serviceLine[SERVICE_LINES.ID]}`,
        Number.parseInt(`${serviceLine[measure]}`, 10)
      )
    })

    const value = serviceLines.map((serviceLine) => {
      return { ...serviceLine, count: countMap.get(serviceLine.id) ?? 0 }
    })

    value.sort((a, b) => b.count - a.count)

    return value
  }, [resultSet, serviceLines, measure])

  return (
    <MultiSelect
      name={name}
      bulkSelect={bulkSelect}
      disabled={serviceLinesLoading || cubedataLoading}
      label={label}
      {...rest}
    >
      {serviceLinesWithCount.map((serviceLine) => (
        <Option
          key={serviceLine.id}
          value={serviceLine.id}
          label={serviceLine.name}
          count={serviceLine.count}
        />
      ))}
    </MultiSelect>
  )
}
