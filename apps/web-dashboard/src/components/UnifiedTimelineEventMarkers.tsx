import { memo, MouseEvent as ReactMouseEvent } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import {
  ComponentTheme,
  MarkerShape,
  remSpacing,
  TimelineMarkerDot,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'
import { EventSourceType } from 'src/pages/types'

export interface TimelineEventMarker {
  color: string
  id: string
  label: string
  name: string
  recipients?: string[]
  shape?: MarkerShape
  sourceType?: string
  startLocationPercent: number
  startTime: DateTime
}

export enum TimelineType {
  APELLA = 'APELLA',
  OBX = 'OBX',
}

export type ReactMouseClickHandler = (e: ReactMouseEvent) => void
export type EventMarkerClickHandler = (marker: TimelineEventMarker) => void

type TimelineEventMarkerProps = {
  markers: TimelineEventMarker[]
  boundary?: HTMLDivElement
  children?: React.ReactNode
  type?: TimelineType
}
export const UnifiedTimelineEventMarkers = memo(function TimelineEventMarker({
  markers,
  boundary,
  children,
  type = TimelineType.APELLA,
}: TimelineEventMarkerProps): React.JSX.Element {
  const theme: ComponentTheme = useTheme()

  return (
    <div
      css={{
        marginTop: remSpacing.xsmall,
        marginBottom: remSpacing.medium,
        position: 'relative',
        width: '100%',
        height: `${remSpacing.medium}`,
        verticalAlign: 'middle',
      }}
    >
      <div
        css={{
          height: '2px',
          width: '100%',
          position: 'absolute',
          top: 'calc(50% - 1px)',
        }}
        style={{
          borderTop:
            type === TimelineType.APELLA
              ? `2px solid ${theme.palette.gray[30]}`
              : `2px dashed ${theme.palette.gray[30]}`,
        }}
      ></div>
      {markers.map((marker) => (
        <UnifiedTimelineEventMarker
          key={marker.id}
          marker={marker}
          boundary={boundary}
        />
      ))}
      {children}
    </div>
  )
})

const UnifiedTimelineEventMarker = memo(function TimelineEventMarker({
  marker,
  boundary,
}: {
  marker: TimelineEventMarker
  boundary?: HTMLDivElement
}) {
  const { timezone } = useTimezone()

  if (marker.startLocationPercent < 0 || marker.startLocationPercent > 100) {
    return null
  }
  return (
    <div
      css={{
        position: 'absolute',
      }}
      style={{
        left: `calc(${marker.startLocationPercent}% - 0.5rem)`,
      }}
      key={marker.id}
    >
      <TimelineMarkerDot
        time={marker.startTime}
        label={marker.label}
        recipients={marker.recipients}
        color={marker.color}
        shape={marker.shape}
        filled={marker.sourceType === EventSourceType.Human}
        boundary={boundary}
        timezone={timezone}
        viewOnly
      />
    </div>
  )
})
