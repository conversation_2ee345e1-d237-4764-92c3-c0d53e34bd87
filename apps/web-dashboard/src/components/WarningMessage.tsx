import { ComponentProps } from 'react'

import { useTheme } from '@emotion/react'

import { remSpacing } from '@apella/component-library'

export const WarningMessage = ({
  children,
  className,
}: ComponentProps<'div'>) => {
  const theme = useTheme()
  return (
    <div
      css={{
        display: 'flex',
        padding: `${remSpacing.xsmall} ${remSpacing.gutter}`,
        flexDirection: 'column',
        gap: remSpacing.xxsmall,
        alignSelf: 'stretch',
        borderRadius: remSpacing.xsmall,
        border: `solid 1px ${theme.palette.yellow[30]}`,
        background: theme.palette.yellow.background,
        color: theme.palette.text.secondary,
      }}
      className={className}
    >
      {children}
    </div>
  )
}
