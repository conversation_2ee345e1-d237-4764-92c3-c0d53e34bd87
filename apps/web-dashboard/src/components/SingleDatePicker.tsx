import { useMemo } from 'react'

import {
  <PERSON>ton,
  ChevronLeft,
  ChevronRight,
  DatePicker,
  FlexContainer,
  FlexContainerProps,
  remSpacing,
  Tooltip,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'

interface SingleDatePickerProps
  extends Pick<React.ComponentProps<typeof DatePicker>, 'calendarProps'> {
  date: string
  disabled?: boolean
  onChangeDate: (date: Date) => void
  showAdvanceByDay?: boolean
}

export const SingleDatePicker = ({
  date,
  onChangeDate,
  calendarProps,
  flexContainerProps,
  showAdvanceByDay = true,
  disabled,
}: SingleDatePickerProps & {
  flexContainerProps?: FlexContainerProps
}) => {
  const { timezone } = useTimezone()

  const { goToPreviousDate, goToNextDate } = useMemo(() => {
    const nextDay = new Date(date)
    nextDay.setDate(nextDay.getDate() + 1)

    const previousDay = new Date(date)
    previousDay.setDate(previousDay.getDate() - 1)

    const isPreviousDateDisabled = calendarProps?.tileDisabled
      ? calendarProps.tileDisabled({
          date: previousDay,
          activeStartDate: previousDay,
          view: 'month',
        })
      : undefined

    const isNextDateDisabled = calendarProps?.tileDisabled
      ? calendarProps.tileDisabled({
          date: nextDay,
          activeStartDate: nextDay,
          view: 'month',
        })
      : undefined

    const goToNextDate = isNextDateDisabled
      ? undefined
      : () => onChangeDate(nextDay)

    const goToPreviousDate = isPreviousDateDisabled
      ? undefined
      : () => onChangeDate(previousDay)

    return {
      goToPreviousDate,
      goToNextDate,
    }
  }, [date, calendarProps, onChangeDate])

  return (
    <FlexContainer gap={remSpacing.xsmall} {...flexContainerProps}>
      {showAdvanceByDay && (
        <Tooltip body={<>Previous Day</>} placement="bottom">
          <Button
            onClick={goToPreviousDate}
            appearance={'button'}
            color="alternate"
            buttonType="icon"
            disabled={!goToPreviousDate}
          >
            <ChevronLeft size="sm" />
          </Button>
        </Tooltip>
      )}
      <DatePicker
        value={new Date(date)}
        showPresets
        timezone={timezone}
        setValue={(date) =>
          !!date && !Array.isArray(date) && onChangeDate(date)
        }
        calendarProps={calendarProps}
        disabled={disabled}
      />
      {showAdvanceByDay && (
        <Tooltip body={<>Next Day</>} placement="bottom">
          <Button
            onClick={goToNextDate}
            appearance={'button'}
            color="alternate"
            buttonType="icon"
            disabled={!goToNextDate}
          >
            <ChevronRight size="sm" />
          </Button>
        </Tooltip>
      )}
    </FlexContainer>
  )
}
