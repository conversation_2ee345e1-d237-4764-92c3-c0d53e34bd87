import { H5, remSpacing } from '@apella/component-library'

import { HR } from '../HR'

export const Section = ({
  title,
  children,
}: {
  title?: string
  children: React.ReactNode
}) => {
  return (
    <>
      <section
        css={{
          display: 'flex',
          flexDirection: 'column',
          gap: remSpacing.medium,
          marginTop: remSpacing.medium,
          marginBottom: remSpacing.large,
        }}
      >
        {title && (
          <div css={{ margin: `${remSpacing.medium} 0` }}>
            <H5 as="h2">{title}</H5>
          </div>
        )}
        {children}
      </section>
      <HR />
    </>
  )
}
