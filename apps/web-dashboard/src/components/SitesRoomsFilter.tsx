import { ComponentProps, useMemo } from 'react'

import { compact } from 'lodash'

import { Col, MultiSelect, SingleSelect } from '@apella/component-library'
import {
  MultiFilterWithCount,
  SingleFilterWithCount,
} from 'src/components/Filters/FilterWithCount'

import { FilterLabel } from './FilterLabel'

interface SiteShape {
  node: {
    id: string
    name: string
  }
}

interface RoomSiteShape {
  id: string
  name: string
}

interface RoomShape {
  node: {
    id: string
    name: string
    site: RoomSiteShape
  }
}

type MultiSelectPassedProps = Omit<
  ComponentProps<typeof MultiFilterWithCount>,
  'items' | 'selectedIds' | 'onChange' | 'label' | 'bulkSelect'
>

type SingleSelectPassedProps = Omit<
  ComponentProps<typeof SingleFilterWithCount>,
  'items' | 'selectedIds' | 'onChange' | 'label' | 'disableSelectAllOption'
>

type SitesRoomFilterProps =
  // Multiple sites, with rooms
  | {
      sites: SiteShape[]
      multipleSites: true
      selectedSiteIds: ComponentProps<typeof MultiSelect>['value']
      onChangeSites?: ComponentProps<typeof MultiSelect>['onChange']
      rooms: RoomShape[]
      selectedRoomIds: ComponentProps<typeof MultiSelect>['value']
      onChangeRooms?: ComponentProps<typeof MultiSelect>['onChange']
      onChangeSitesAndRooms?: (
        siteIds?: ComponentProps<typeof MultiSelect>['value'],
        roomIds?: ComponentProps<typeof MultiSelect>['value']
      ) => void
      bulkSelectSites?: ComponentProps<typeof MultiSelect>['bulkSelect']
      bulkSelectRooms?: ComponentProps<typeof MultiSelect>['bulkSelect']
      disableSelectAllOption?: false
      siteSelectProps?: MultiSelectPassedProps
      roomSelectProps?: MultiSelectPassedProps
      disabled?: boolean
      sitesLabel?: string
      showLabels?: boolean
    }
  // One site, with rooms
  | {
      sites: SiteShape[]
      multipleSites?: false
      selectedSiteIds: ComponentProps<typeof SingleSelect>['value']
      onChangeSites?: ComponentProps<typeof SingleSelect>['onChange']
      rooms: RoomShape[]
      selectedRoomIds: ComponentProps<typeof MultiSelect>['value']
      onChangeRooms: ComponentProps<typeof MultiSelect>['onChange']
      onChangeSitesAndRooms?: (
        siteIds?: ComponentProps<typeof MultiSelect>['value'],
        roomIds?: ComponentProps<typeof MultiSelect>['value']
      ) => void
      bulkSelectSites?: false
      bulkSelectRooms?: ComponentProps<typeof MultiSelect>['bulkSelect']
      disableSelectAllOption?: boolean
      siteSelectProps?: SingleSelectPassedProps
      roomSelectProps?: MultiSelectPassedProps
      disabled?: boolean
      sitesLabel?: string
      showLabels?: boolean
    }
  // One site, no rooms
  | {
      sites: SiteShape[]
      multipleSites?: false
      selectedSiteIds: ComponentProps<typeof SingleSelect>['value']
      onChangeSites?: ComponentProps<typeof SingleSelect>['onChange']
      rooms?: RoomShape[]
      selectedRoomIds?: ComponentProps<typeof MultiSelect>['value']
      onChangeRooms?: ComponentProps<typeof MultiSelect>['onChange']
      onChangeSitesAndRooms?: (
        siteIds?: ComponentProps<typeof MultiSelect>['value'],
        roomIds?: ComponentProps<typeof MultiSelect>['value']
      ) => void
      bulkSelectSites?: false
      bulkSelectRooms?: ComponentProps<typeof MultiSelect>['bulkSelect']
      disableSelectAllOption?: boolean
      siteSelectProps?: SingleSelectPassedProps
      roomSelectProps?: MultiSelectPassedProps
      disabled?: boolean
      sitesLabel?: string
      showLabels?: boolean
    }

export const SitesRoomsFilter = ({
  sites,
  selectedSiteIds,
  onChangeSites,
  rooms,
  selectedRoomIds,
  onChangeRooms,
  onChangeSitesAndRooms,
  multipleSites,
  bulkSelectSites,
  bulkSelectRooms,
  disableSelectAllOption,
  siteSelectProps,
  roomSelectProps,
  disabled,
  sitesLabel = 'All sites',
  showLabels = false,
}: SitesRoomFilterProps): React.JSX.Element => {
  const filteredRooms = useMemo(() => {
    const roomsForSite = selectedSiteIds
      ? rooms?.filter((room) => selectedSiteIds.includes(room.node.site.id))
      : rooms
    return roomsForSite?.map((room) => ({
      node: {
        ...room.node,
        group: room.node.site.name,
      },
    }))
  }, [rooms, selectedSiteIds])

  const siteFilter = multipleSites ? (
    <MultiFilterWithCount
      exclusive={true}
      items={sites}
      selectedIds={selectedSiteIds}
      onChange={(siteIds: ComponentProps<typeof MultiSelect>['value']) => {
        if (onChangeSites) {
          onChangeSites(siteIds)
        }
        // Every time the user changes a site we need to check that we exclude rooms no longer
        // associated to selected sites
        const selectedRooms = rooms
          .filter((room) => {
            // first we need to verify that this is a selected room
            const isASelectedRoom =
              selectedRoomIds &&
              selectedRoomIds?.findIndex((roomId) => room.node.id == roomId) >=
                0

            if (isASelectedRoom) {
              // If the current room is a selected room we can then check if that
              // Room is at a selected site
              return (
                siteIds &&
                siteIds.findIndex((siteId) => siteId == room.node.site.id) >= 0
              )
            }
          })
          .map((room) => room.node.id)
        const roomsToSelect = selectedRooms.length ? selectedRooms : undefined
        if (onChangeRooms && rooms) {
          onChangeRooms(roomsToSelect)
        }
        if (onChangeSitesAndRooms && rooms) {
          onChangeSitesAndRooms(siteIds, roomsToSelect)
        }
      }}
      label={sitesLabel}
      bulkSelect={bulkSelectSites}
      disabled={disabled}
      {...siteSelectProps}
    />
  ) : (
    <SingleFilterWithCount
      items={sites}
      value={selectedSiteIds}
      onChange={(siteIds: ComponentProps<typeof SingleSelect>['value']) => {
        if (onChangeSites) {
          onChangeSites(siteIds)
        }
        // Every time the user changes a site we need to check that we exclude rooms no longer
        // associated to selected sites
        const selectedRooms = rooms
          ?.filter((room) => {
            // first we need to verify that this is a selected room
            const isASelectedRoom =
              selectedRoomIds &&
              selectedRoomIds?.findIndex((roomId) => room.node.id == roomId) >=
                0

            if (isASelectedRoom) {
              // If the current room is a selected room we can then check if that
              // Room is at a selected site
              return siteIds && siteIds === room.node.site.id
            }
          })
          .map((room) => room.node.id)
        const roomsToSelect = selectedRooms?.length ? selectedRooms : undefined
        if (onChangeRooms && rooms) {
          onChangeRooms(roomsToSelect)
        }
        if (onChangeSitesAndRooms && rooms) {
          onChangeSitesAndRooms(compact([siteIds]), roomsToSelect)
        }
      }}
      label={'All sites'}
      disableSelectAllOption={disableSelectAllOption}
      disabled={disabled}
      {...siteSelectProps}
    />
  )

  const roomFilter = (
    <MultiFilterWithCount
      items={filteredRooms || []}
      selectedIds={selectedRoomIds}
      onChange={(value?: string[]) => {
        if (onChangeRooms) {
          onChangeRooms(value)
        }
        if (onChangeSitesAndRooms) {
          onChangeSitesAndRooms(
            Array.isArray(selectedSiteIds)
              ? selectedSiteIds
              : compact([selectedSiteIds]),
            value
          )
        }
      }}
      label={'All rooms'}
      bulkSelect={bulkSelectRooms}
      disabled={disabled}
      {...roomSelectProps}
    />
  )

  return (
    <>
      {sites.length > 1 ? (
        <Col>
          {showLabels ? (
            <FilterLabel label="Site">{siteFilter}</FilterLabel>
          ) : (
            siteFilter
          )}
        </Col>
      ) : null}
      {filteredRooms && !!filteredRooms.length && (
        <Col>
          {showLabels ? (
            <FilterLabel label="Room">{roomFilter}</FilterLabel>
          ) : (
            roomFilter
          )}
        </Col>
      )}
    </>
  )
}
