import { useTheme } from '@emotion/react'

import { Button, remSpacing, ZIndex } from '@apella/component-library'
import { SvgIconProps } from '@apella/component-library/dist/icons/SvgIcon'
import { TimelineTooltip } from 'src/components/Timeline/TimelineTooltip'

export const FloatingActionIconButton = ({
  onClick,
  tooltipContent,
  Icon,
  color,
}: Pick<React.ComponentProps<typeof Button>, 'onClick'> & {
  tooltipContent?: string
  Icon: (props: SvgIconProps) => React.JSX.Element
  color?: string
}) => {
  const theme = useTheme()
  const iconColor = color ?? theme.palette.gray[50]
  const button = (
    <Button buttonType="round icon" onClick={onClick} color="alternate">
      <Icon color={iconColor} />
    </Button>
  )
  return (
    <div
      css={{
        boxShadow: `0px 24px 38px 0px rgba(0, 0, 0, 0.04), 0px 9px 46px 0px rgba(0, 0, 0, 0.06), 0px 11px 15px 0px rgba(0, 0, 0, 0.10)`,
        borderRadius: '50%',
        height: '100%',
      }}
    >
      {tooltipContent ? (
        <TimelineTooltip
          content={tooltipContent ?? ''}
          placement="top-end"
          offset={8}
          noWrap
        >
          {button}
        </TimelineTooltip>
      ) : (
        button
      )}
    </div>
  )
}

export const FloatingActionContainer = ({
  children,
}: React.PropsWithChildren) => (
  <div
    css={{
      position: 'fixed',
      zIndex: ZIndex.MORE_ABOVE,
      bottom: remSpacing.gutter,
      right: remSpacing.gutter,
      display: 'flex',
    }}
  >
    {children}
  </div>
)
