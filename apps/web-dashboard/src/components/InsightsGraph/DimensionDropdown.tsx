import { lowerCase } from 'lodash'

import { Option, SingleSelect } from '@apella/component-library'
import {
  Dimension,
  NonTimeDimensions,
  TimeDimensions,
} from 'src/utils/bucketHelpers'

interface DimensionDropdownProps {
  dimension: Dimension
  onChangeDimension: (newDimension: Dimension) => void
  showCirculatorAndScrubTechOptions?: boolean
}

export const DimensionDropdown = ({
  dimension,
  onChangeDimension,
  showCirculatorAndScrubTechOptions,
}: DimensionDropdownProps) => {
  return (
    <SingleSelect
      label={'Dimension'}
      name={'dimension'}
      value={dimension}
      onChange={(newValue) => onChangeDimension(newValue as Dimension)}
    >
      <Option
        key={TimeDimensions.DAY}
        value={TimeDimensions.DAY}
        label={`By ${lowerCase(TimeDimensions.DAY)}`}
      />
      <Option
        key={TimeDimensions.WEEK}
        value={TimeDimensions.WEEK}
        label={`By ${lowerCase(TimeDimensions.WEEK)}`}
      />
      <Option
        key={TimeDimensions.MONTH}
        value={TimeDimensions.MONTH}
        label={`By ${lowerCase(TimeDimensions.MONTH)}`}
      />
      <Option
        key={NonTimeDimensions.DAYOFWEEK}
        value={NonTimeDimensions.DAYOFWEEK}
        label={`By day of week`}
      />
      <Option
        key={NonTimeDimensions.OR}
        value={NonTimeDimensions.OR}
        label={`By ${NonTimeDimensions.OR}`}
      />
      <Option
        key={NonTimeDimensions.SURGEON}
        value={NonTimeDimensions.SURGEON}
        label={`By ${lowerCase(NonTimeDimensions.SURGEON)}`}
      />
      <Option
        key={NonTimeDimensions.ANESTHESIA}
        value={NonTimeDimensions.ANESTHESIA}
        label={`By ${lowerCase(NonTimeDimensions.ANESTHESIA)}`}
      />
      {showCirculatorAndScrubTechOptions && (
        <Option
          key={NonTimeDimensions.CIRCULATOR}
          value={NonTimeDimensions.CIRCULATOR}
          label={`By ${lowerCase(NonTimeDimensions.CIRCULATOR)}`}
        />
      )}
      {showCirculatorAndScrubTechOptions && (
        <Option
          key={NonTimeDimensions.SCRUB_TECH}
          value={NonTimeDimensions.SCRUB_TECH}
          label={`By ${lowerCase(NonTimeDimensions.SCRUB_TECH)}`}
        />
      )}
      <Option
        key={NonTimeDimensions.SERVICE_LINE}
        value={NonTimeDimensions.SERVICE_LINE}
        label={`By ${lowerCase(NonTimeDimensions.SERVICE_LINE)}`}
      />
      <Option
        key={NonTimeDimensions.PROCEDURE}
        value={NonTimeDimensions.PROCEDURE}
        label={`By ${lowerCase(NonTimeDimensions.PROCEDURE)}`}
      />
    </SingleSelect>
  )
}
