import { useMemo } from 'react'

import { DateTime } from 'luxon'

import {
  ChartData,
  FlexContainer,
  H4,
  Option,
  remSpacing,
  SingleSelect,
  StackedBarChart,
  StackedBarChartByDate,
  StackedBarChartProps,
  Tile,
} from '@apella/component-library'
import GraphSortDropdown from 'src/components/InsightsGraph/GraphSortDropdown'
import { ProcedureShape } from 'src/components/ProceduresFilter'
import { StaffShape } from 'src/components/StaffFilter'
import { GetSiteOptionsFilter } from 'src/modules/site/__generated__'
import {
  Dimension,
  NonTimeDimensions,
  TimeDimensions,
} from 'src/utils/bucketHelpers'

export enum CaseGraphMode {
  Breakdown = 'Duration with phases',
  ScheduleVsActual = 'Duration compared to scheduled',
  Volume = 'Case volume',
}

export interface InsightGraphData {
  anesthesiaStaff: StaffShape[]
  procedures: ProcedureShape[]
  sites: GetSiteOptionsFilter['sites']['edges'][number][]
  staff: StaffShape[]
}

export interface InsightsGraphProps
  extends Pick<
    StackedBarChartProps,
    | 'isLoading'
    | 'valueAxisScale'
    | 'valueAxisType'
    | 'meta'
    | 'data'
    | 'valueAxisDomain'
    | 'referenceLines'
  > {
  allowSort?: boolean
  children: React.ReactNode
  data: ChartData[]
  dimension: Dimension
  graphSort: string
  graphStackGrouping?: string
  graphStackGroupingOptions?: string[]
  maxTime: string
  measures?: string[]
  minTime: string
  onChangeGraphSort: (sortBy: string) => void
  onChangeGraphStackGrouping?: (option?: string) => void
  onClickBarGroup: (dimensionValue: string) => void // Make the onClickBarGroup required
  showLegend?: boolean
  timezone: string
  title: string
}

export const InsightsGraph = ({
  showLegend = true,
  dimension,
  data,
  meta,
  isLoading,
  minTime,
  maxTime,
  title,
  timezone,
  valueAxisScale,
  valueAxisType,
  valueAxisDomain,
  graphSort,
  measures,
  onChangeGraphSort,
  onClickBarGroup,
  graphStackGroupingOptions,
  graphStackGrouping,
  onChangeGraphStackGrouping,
  referenceLines,
  allowSort = true,
  children,
}: InsightsGraphProps): React.JSX.Element => {
  const startDate = useMemo(
    () => DateTime.fromISO(minTime).setZone(timezone),
    [minTime, timezone]
  )
  const endDate = useMemo(
    () => DateTime.fromISO(maxTime).setZone(timezone),
    [maxTime, timezone]
  )

  const layout = dimension in NonTimeDimensions ? 'vertical' : 'horizontal'

  // The vertical graphs should be a bit taller to fit the dense information from staff and procedure. These numbers are a bit "magic" and should be re-evaluated.
  const graphHeight = layout == 'vertical' ? 400 : 277
  // Account for the prev/next buttons and the legend
  const columnHeight =
    layout == 'vertical'
      ? graphHeight + (showLegend ? 48 : 32) * 2
      : graphHeight + (showLegend ? 24 : 0)

  return (
    <div css={{ position: 'relative' }}>
      <Tile gutter>
        <FlexContainer
          alignItems={'center'}
          justifyContent={'space-between'}
          css={{ marginBottom: remSpacing.medium }}
        >
          <H4>{title}</H4>
          <FlexContainer css={{ gap: remSpacing.medium }}>
            {graphStackGroupingOptions &&
              graphStackGrouping &&
              onChangeGraphStackGrouping &&
              graphStackGroupingOptions.includes(graphStackGrouping) && (
                <SingleSelect
                  name="case-graph-mode"
                  defaultValue={CaseGraphMode.Breakdown}
                  value={graphStackGrouping}
                  onChange={onChangeGraphStackGrouping}
                >
                  {graphStackGroupingOptions.map((option) => (
                    <Option key={option} label={option} value={option} />
                  ))}
                </SingleSelect>
              )}

            {layout === 'vertical' && measures && allowSort && (
              <GraphSortDropdown
                measures={measures}
                value={graphSort}
                onChange={onChangeGraphSort}
              />
            )}
            {children}
          </FlexContainer>
        </FlexContainer>
        <FlexContainer style={{ height: columnHeight }}>
          <div css={{ width: '100%', height: graphHeight }}>
            {dimension in TimeDimensions ? (
              <StackedBarChartByDate
                showLegend={showLegend}
                bucketSize={dimension as TimeDimensions}
                data={data}
                isLoading={isLoading}
                startDate={startDate}
                endDate={endDate}
                timezone={timezone}
                onClickBarGroup={onClickBarGroup}
                meta={meta}
                valueAxisScale={valueAxisScale}
                valueAxisType={valueAxisType}
                valueAxisDomain={valueAxisDomain}
                referenceLines={referenceLines}
              />
            ) : (
              <StackedBarChart
                showLegend={showLegend}
                data={data}
                meta={meta}
                isLoading={isLoading}
                onClickBarGroup={onClickBarGroup}
                characterLimit={12}
                layout={layout}
                valueAxisScale={valueAxisScale}
                valueAxisType={valueAxisType}
                valueAxisDomain={valueAxisDomain}
                referenceLines={referenceLines}
              />
            )}
          </div>
        </FlexContainer>
      </Tile>
    </div>
  )
}
