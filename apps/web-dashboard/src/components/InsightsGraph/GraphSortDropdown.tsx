import { lowerCase } from 'lodash'

import { Option, SingleSelect } from '@apella/component-library'

interface GraphSortDropdownProps {
  measures: string[]
  onChange: (newValue: string) => void
  value: string
}

const GraphSortDropdown = ({
  value,
  onChange,
  measures,
}: GraphSortDropdownProps): React.JSX.Element => {
  const options = measures.map((measure) => (
    <Option
      key={measure}
      value={measure}
      label={`Sort desc. by ${lowerCase(measure)}`}
    />
  ))
  return (
    <SingleSelect
      name={'graph-sort'}
      label={'Sort by'}
      value={value}
      onChange={(newValue?: string) => onChange(newValue!)}
    >
      {options}
    </SingleSelect>
  )
}

export default GraphSortDropdown
