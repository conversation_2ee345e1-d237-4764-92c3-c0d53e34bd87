import { ReactNode } from 'react'

import { rem } from 'polished'

import {
  Button,
  H6,
  remSpacing,
  SelectToggleIcon,
} from '@apella/component-library'

export const ToggleSelect = ({
  onClick,
  value,
  label,
  children,
  disabled = false,
}: {
  onClick: () => void
  value: boolean
  label?: string
  children?: ReactNode
  disabled?: boolean
}) => {
  return (
    <Button
      onClick={onClick}
      appearance="link"
      buttonType="icon"
      color="black"
      disabled={disabled}
      css={{
        height: rem('40px'),
      }}
    >
      <div
        css={{
          display: 'flex',
          height: rem('36px'),
          alignItems: 'center',
          gap: remSpacing.small,
        }}
      >
        <SelectToggleIcon
          type={value ? 'selected' : undefined}
          size={'sm'}
          data-testid={`checkbox-${value ? 'checked' : 'unchecked'}`}
        />
        {children || <H6>{label}</H6>}
      </div>
    </Button>
  )
}
