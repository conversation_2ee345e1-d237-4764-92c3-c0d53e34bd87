import { <PERSON><PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import { <PERSON>a, StoryObj } from '@storybook/react'
import { userEvent, within } from '@storybook/test'
import { graphql, HttpResponse } from 'msw'

import {
  anEventType,
  anEventTypeEdge,
  anEventTypeConnection,
} from 'src/__generated__/generated-mocks'
import {
  ToggleableCaseKey,
  SCHEDULE_KEY_LOCAL_STORAGE_KEY,
} from 'src/components/Keys'
import { GetEventTypesDocument } from 'src/pages/__generated__'
import { LIVE_EVENTS } from 'src/pages/Live/livePhases'
import { modes } from 'src/test/storybookHelpers'

const mockedClient = new ApolloClient({
  uri: 'https://your-mock-endpoint',
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
    query: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all',
    },
  },
})

const getEventTypes = () => {
  return {
    eventTypes: anEventTypeConnection({
      edges: [
        anEventTypeEdge({
          node: anEventType({
            __typename: 'EventType',
            id: LIVE_EVENTS.PATIENT_WHEELS_IN,
            name: 'Pt wheeled in',
            color: '#f36f66',
            description: null,
            hidden: false,
            type: '',
          }),
        }),
        anEventTypeEdge({
          node: anEventType({
            id: LIVE_EVENTS.PATIENT_DRAPED,
            name: 'Pt draped (procedural)',
            color: '#72c8c8',
            description: null,
            hidden: false,
            type: '',
          }),
        }),
        anEventTypeEdge({
          node: anEventType({
            id: LIVE_EVENTS.PATIENT_UNDRAPED,
            name: 'Pt undraped (procedural)',
            color: '#108383',
            description: null,
            hidden: false,
            type: '',
          }),
        }),
        anEventTypeEdge({
          node: anEventType({
            id: LIVE_EVENTS.PATIENT_WHEELS_OUT,
            name: 'Pt wheeled out',
            color: '#bc0c00',
            description: null,
            hidden: false,
            type: '',
          }),
        }),
      ],
    }),
  }
}

const MswHandlers = [
  graphql.query(GetEventTypesDocument, () => {
    return HttpResponse.json({
      data: getEventTypes(),
    })
  }),
]

const meta: Meta<typeof ToggleableCaseKey> = {
  title: 'Components/StatusKey',
  component: ToggleableCaseKey,
  decorators: [
    (Story) => (
      <ApolloProvider client={mockedClient}>
        <Story />
      </ApolloProvider>
    ),
  ],
  parameters: {
    chromatic: {
      modes: {
        mobile: modes.mobile,
        xLarge: modes.xLarge,
      },
    },
    msw: {
      handlers: MswHandlers,
    },
  },

  beforeEach: async () => {
    window.localStorage.setItem(SCHEDULE_KEY_LOCAL_STORAGE_KEY, 'false')
  },
}

export default meta
type Story = StoryObj<typeof ToggleableCaseKey>

export const Open: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    const floatingActionButton = canvas.getByRole('button')
    await userEvent.click(floatingActionButton)
  },
}

export const Closed: Story = {}
