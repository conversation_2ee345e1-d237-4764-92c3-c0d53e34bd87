import { capitalize, isEqual, isUndefined } from 'lodash'

import { Option, SingleSelect } from '@apella/component-library'
import {
  DayOfWeek,
  daysOfWeekInOrder,
  ISOWeekDate,
} from 'src/pages/Insights/types'

export const dayOfWeekToISOWeekDate: Record<DayOfWeek, ISOWeekDate> = {
  monday: 1,
  tuesday: 2,
  wednesday: 3,
  thursday: 4,
  friday: 5,
  saturday: 6,
  sunday: 7,
} as const

export const isoWeekDateToDayOfWeek: Record<ISOWeekDate, DayOfWeek> = {
  1: 'monday',
  2: 'tuesday',
  3: 'wednesday',
  4: 'thursday',
  5: 'friday',
  6: 'saturday',
  7: 'sunday',
} as const

export const ISO_DAYS_OF_WEEK: ISOWeekDate[] = [1, 2, 3, 4, 5, 6, 7]
export const ISO_WEEKDAYS: ISOWeekDate[] = [1, 2, 3, 4, 5]
export const ISO_WEEKENDS: ISOWeekDate[] = [6, 7]

export const weekdays: DayOfWeek[] = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
]
export const weekends: DayOfWeek[] = ['saturday', 'sunday']

const isoDaysOfWeekSet = new Set(ISO_DAYS_OF_WEEK)
const isoWeekdaysSet = new Set(ISO_WEEKDAYS)
const isoWeekendsSet = new Set(ISO_WEEKENDS)

const daysOfWeekSet = new Set(daysOfWeekInOrder)
const weekdaysSet = new Set(weekdays)
const weekendsSet = new Set(weekends)

export const DEFAULT_DAYS_OF_WEEK = weekdays
export const VALID_DAYS_OF_WEEK = [...weekdays, ...weekends]
const entireWeekOption = 'entireWeek'
const weekdaysOption = 'weekdays'
const weekendsOption = 'weekends'
type DayOfWeekOption =
  | DayOfWeek
  | 'weekends'
  | 'entireWeek'
  | 'weekdays'
  | undefined

const presetGroupName = 'Presets'
const daysOfWeekGroupName = 'Days of the week'

export const isDayOfWeek = (value: string): value is DayOfWeek => {
  return VALID_DAYS_OF_WEEK.includes(value as DayOfWeek)
}

const DayOfWeekSelect = ({
  name = 'day-of-week-filter',
  label = 'Day of Week',
  value,
  onChange,
  ...props
}: {
  name?: string
  label?: string
  value: string
  onChange: (option: DayOfWeekOption) => void
}) => {
  return (
    <SingleSelect
      name={name}
      label={label}
      value={value}
      onChange={(option) => onChange(option as DayOfWeekOption)}
      {...props}
    >
      <Option
        value={entireWeekOption}
        label={'Entire week'}
        group={presetGroupName}
      />
      <Option
        value={weekdaysOption}
        label={'Weekdays'}
        group={presetGroupName}
      />
      <Option
        value={weekendsOption}
        label={'Weekends'}
        group={presetGroupName}
      />
      {daysOfWeekInOrder.map((dayOfWeek) => (
        <Option
          key={dayOfWeek}
          value={dayOfWeek}
          label={`${capitalize(dayOfWeek)}s`}
          group={daysOfWeekGroupName}
        />
      ))}
    </SingleSelect>
  )
}

export const DayOfWeekFilter = ({
  selected,
  onChange: onChangeProp,
}: {
  selected: DayOfWeek[] | undefined
  onChange: (selected: DayOfWeek[]) => void
}): React.JSX.Element => {
  const selectedSet = new Set(selected)
  let optionDisplay: DayOfWeekOption
  if (isEqual(selectedSet, daysOfWeekSet)) {
    optionDisplay = entireWeekOption
  } else if (isEqual(selectedSet, weekendsSet)) {
    optionDisplay = weekendsOption
  } else if (isEqual(selectedSet, weekdaysSet) || isUndefined(selected)) {
    optionDisplay = weekdaysOption
  } else {
    optionDisplay = selected[0]
  }

  return (
    <DayOfWeekSelect
      value={optionDisplay}
      onChange={(option: DayOfWeekOption) => {
        switch (option) {
          case entireWeekOption:
            onChangeProp(daysOfWeekInOrder)
            break
          case weekendsOption:
            onChangeProp(weekends)
            break
          case weekdaysOption:
          case undefined:
            onChangeProp(weekdays)
            break
          default:
            onChangeProp([option])
        }
      }}
    />
  )
}

export const ISODayOfWeekFilter = ({
  selected,
  onChange: onChangeProp,
}: {
  selected: ISOWeekDate[] | undefined
  onChange: (selected: ISOWeekDate[]) => void
}): React.JSX.Element => {
  const selectedSet = new Set(selected)
  let optionDisplay: DayOfWeekOption
  if (isEqual(selectedSet, isoDaysOfWeekSet)) {
    optionDisplay = entireWeekOption
  } else if (isEqual(selectedSet, isoWeekendsSet)) {
    optionDisplay = weekendsOption
  } else if (isEqual(selectedSet, isoWeekdaysSet) || isUndefined(selected)) {
    optionDisplay = weekdaysOption
  } else {
    optionDisplay = isoWeekDateToDayOfWeek[selected[0]]
  }

  return (
    <DayOfWeekSelect
      value={optionDisplay}
      onChange={(option: DayOfWeekOption) => {
        switch (option) {
          case entireWeekOption:
            onChangeProp(ISO_DAYS_OF_WEEK)
            break
          case weekendsOption:
            onChangeProp(ISO_WEEKENDS)
            break
          case weekdaysOption:
          case undefined:
            onChangeProp(ISO_WEEKDAYS)
            break
          default:
            onChangeProp([dayOfWeekToISOWeekDate[option]])
        }
      }}
    />
  )
}
