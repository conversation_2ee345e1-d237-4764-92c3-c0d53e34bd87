import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime, Duration } from 'luxon'

import {
  ArrowDropDown,
  ArrowDropUp,
  formatDatum,
  H5,
  P3,
  Span3,
  theme,
  Tooltip,
  ApellaDateTimeFormats,
} from '@apella/component-library'
import { EM_DASH } from 'src/utils/htmlCodes'

import { BoldedSpan } from './BoldedSpan'

const ARROW_PROPS = {
  size: 'xs',
  css: { alignSelf: 'center' },
}

export enum METRIC_DIRECTION {
  POSITIVE = 'POSITIVE',
  NEGATIVE = 'NEGATIVE',
}

interface BasePercentChangeProps {
  label: string
  type: undefined | 'percent' | 'auto'
}

interface PercentChangeIndicatorProps extends BasePercentChangeProps {
  currentValue?: number | Duration
  desiredChangeDirection?: METRIC_DIRECTION
  maxDateTime?: DateTime
  minDateTime?: DateTime
  previousMinDateTime?: DateTime
  previousValue?: number | Duration
}

export const PercentChangeIndicator = ({
  label,
  type,
  currentValue,
  previousValue,
  minDateTime,
  maxDateTime,
  previousMinDateTime,
  desiredChangeDirection,
}: PercentChangeIndicatorProps): React.JSX.Element => {
  const pctChange = useMemo(() => {
    if (previousValue == undefined || currentValue == undefined) {
      return undefined
    }

    const currentValueNumber =
      currentValue instanceof Duration
        ? currentValue.as('seconds')
        : currentValue
    const previousValueNumber =
      previousValue instanceof Duration
        ? previousValue.as('seconds')
        : previousValue

    if (previousValueNumber == 0) {
      return undefined
    }

    return Math.round(
      ((currentValueNumber - previousValueNumber) / previousValueNumber) * 100
    )
  }, [currentValue, previousValue])

  if (
    pctChange == undefined ||
    currentValue == undefined ||
    previousValue == undefined ||
    minDateTime == undefined ||
    maxDateTime == undefined ||
    previousMinDateTime == undefined
  ) {
    return <></>
  }

  return (
    <PercentChangeComponent
      label={label}
      type={type}
      currentValue={currentValue}
      previousValue={previousValue}
      percentChange={pctChange}
      minDateTime={minDateTime}
      maxDateTime={maxDateTime}
      previousMinDateTime={previousMinDateTime}
      positiveChangeColor={
        desiredChangeDirection
          ? desiredChangeDirection == METRIC_DIRECTION.POSITIVE
            ? theme.palette.green[50]
            : theme.palette.orange[50]
          : undefined
      }
      negativeChangeColor={
        desiredChangeDirection
          ? desiredChangeDirection == METRIC_DIRECTION.NEGATIVE
            ? theme.palette.green[50]
            : theme.palette.orange[50]
          : undefined
      }
    />
  )
}

interface PercentChangeComponentProps extends BasePercentChangeProps {
  currentValue: number | Duration
  maxDateTime: DateTime
  minDateTime: DateTime
  negativeChangeColor?: string
  percentChange: number
  positiveChangeColor?: string
  previousMinDateTime: DateTime
  previousValue: number | Duration
}

const PercentChangeComponent = ({
  label,
  type,
  currentValue,
  previousValue,
  minDateTime,
  maxDateTime,
  previousMinDateTime,
  percentChange,
  positiveChangeColor,
  negativeChangeColor,
}: PercentChangeComponentProps): React.JSX.Element => {
  const theme = useTheme()
  const color = useMemo(() => {
    if (positiveChangeColor && percentChange > 0) return positiveChangeColor
    if (negativeChangeColor && percentChange < 0) return negativeChangeColor
    return theme.palette.text.secondary
  }, [
    percentChange,
    positiveChangeColor,
    negativeChangeColor,
    theme.palette.text.secondary,
  ])
  const indicator = useMemo(() => {
    if (percentChange > 0) return <ArrowDropUp {...ARROW_PROPS} />
    if (percentChange < 0) return <ArrowDropDown {...ARROW_PROPS} />
    return (
      <Span3 style={{ paddingLeft: '12px', paddingRight: '2px' }}>
        {EM_DASH}
      </Span3>
    )
  }, [percentChange])

  const absPctChange = Math.abs(percentChange)

  return (
    <Tooltip
      body={
        <PercentChangeDescription
          label={label}
          type={type}
          percentChange={percentChange}
          currentValue={currentValue}
          previousValue={previousValue}
          minDateTime={minDateTime}
          maxDateTime={maxDateTime}
          previousMinDateTime={previousMinDateTime}
          positiveChangeColor={positiveChangeColor}
          negativeChangeColor={negativeChangeColor}
        />
      }
      placement={'bottom-end'}
    >
      <H5
        color={color}
        css={{
          cursor: 'pointer',
          display: 'grid',
          alignItems: 'baseline',
          gridAutoFlow: 'column',
        }}
      >
        {indicator} {absPctChange.toLocaleString()}%
      </H5>
    </Tooltip>
  )
}

type PercentChangeDescriptionProps = PercentChangeComponentProps

const PercentChangeDescription = ({
  label,
  type,
  percentChange,
  currentValue,
  minDateTime,
  maxDateTime,
  previousMinDateTime,
  previousValue,
  positiveChangeColor,
  negativeChangeColor,
}: PercentChangeDescriptionProps): React.JSX.Element => {
  const percentChangeWording =
    percentChange < 0 ? (
      <span
        css={{
          color: negativeChangeColor
            ? negativeChangeColor
            : theme.palette.text.primary,
        }}
      >
        decreased {Math.abs(percentChange)}%
      </span>
    ) : percentChange == 0 ? (
      'remained the same'
    ) : (
      <span
        css={{
          color: positiveChangeColor
            ? positiveChangeColor
            : theme.palette.text.primary,
        }}
      >
        increased {percentChange}%
      </span>
    )

  const currentValueString = formatDatum(currentValue, type)
  const previousValueString = formatDatum(previousValue, type)

  return (
    <P3>
      {label}
      <BoldedSpan>&nbsp;{percentChangeWording}&nbsp;</BoldedSpan>
      from
      <BoldedSpan>&nbsp;{previousValueString}&nbsp;</BoldedSpan>
      last period
      <BoldedSpan>
        &nbsp;({previousMinDateTime.toLocaleString(ApellaDateTimeFormats.DATE)}{' '}
        -{minDateTime.toLocaleString(ApellaDateTimeFormats.DATE)})&nbsp;
      </BoldedSpan>
      to
      <BoldedSpan>&nbsp;{currentValueString}&nbsp;</BoldedSpan>
      this period
      <BoldedSpan>
        &nbsp;({minDateTime.toLocaleString(ApellaDateTimeFormats.DATE)} -
        {maxDateTime.toLocaleString(ApellaDateTimeFormats.DATE)})&nbsp;
      </BoldedSpan>
      for the selected filters.
    </P3>
  )
}
