import { Fragment } from 'react'

import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { DateTime, Duration } from 'luxon'

import {
  Caps2,
  formatDatum,
  H2,
  H3,
  Info,
  P3,
  Progress,
  remSpacing,
  shape,
  Tile,
  Tooltip,
} from '@apella/component-library'
import { DashComponent } from 'src/components/DashComponent'
import {
  METRIC_DIRECTION,
  PercentChangeIndicator,
} from 'src/components/InsightsTile/PercentChangeIndicator'

import { BoldedSpan } from './BoldedSpan'

export const enum MetricType {
  AUTO = 'auto',
  PERCENT = 'percent',
}

export interface InsightsSummaryMetric {
  description?: string
  desiredChangeDirection?: METRIC_DIRECTION
  label: string
  maxDateTime?: DateTime
  minDateTime?: DateTime
  previousMinDateTime?: DateTime
  previousValue?: number | Duration
  style?: 'h3' | 'h2'
  type?: MetricType
  value?: number | Duration
}

export interface InsightsSummaryMetricInfo {
  postLabel: string
  preLabel: string
  value?: number
}

export const Border = styled.div(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.gray[20]}`,
}))

export const MetricsSection = ({
  metrics,
  isLoading,
}: {
  metrics: InsightsSummaryMetric[]
  isLoading?: boolean
}) => (
  <div>
    {metrics.length ? (
      <MultiMetric metrics={metrics} isLoading={isLoading} />
    ) : (
      <></>
    )}
  </div>
)

export const SingleMetricWithPadding = ({
  metric,
  isLoading,
}: {
  metric: InsightsSummaryMetric
  isLoading?: boolean
}) => {
  return (
    <div
      css={{
        display: 'flex',
        flexDirection: 'column',
        paddingTop: metric.style === 'h2' ? 0 : remSpacing.medium,
        paddingRight: remSpacing.medium,
        paddingBottom: remSpacing.medium,
      }}
    >
      <SingleMetric metric={metric} isLoading={isLoading} />
    </div>
  )
}

export const SingleMetric = ({
  metric,
  isLoading,
}: {
  isLoading?: boolean
  metric: InsightsSummaryMetric
}): React.JSX.Element => {
  const theme = useTheme()
  const TitleComponent = metric.style === 'h2' ? H2 : H3

  return (
    <div css={{ paddingRight: remSpacing.medium }}>
      <div css={{ alignItems: 'baseline', display: 'flex' }}>
        {isLoading ? (
          <Progress size="lg" />
        ) : (
          <>
            <TitleComponent
              as="span"
              css={{
                color: theme.palette.text.secondary,
              }}
            >
              {metric.value == undefined ? (
                <DashComponent />
              ) : (
                formatDatum(metric.value, metric.type)
              )}
            </TitleComponent>

            <PercentChangeIndicator
              label={metric.label}
              type={metric.type}
              currentValue={metric.value}
              previousValue={metric.previousValue}
              maxDateTime={metric.maxDateTime}
              minDateTime={metric.minDateTime}
              previousMinDateTime={metric.previousMinDateTime}
              desiredChangeDirection={metric.desiredChangeDirection}
            />
          </>
        )}
      </div>
      <div
        css={{
          display: 'flex',
          minHeight: remSpacing.gutter,
          alignItems: 'center',
          gap: remSpacing.xsmall,
        }}
      >
        <Caps2 color={theme.palette.text.secondary}>{metric.label}</Caps2>
        {metric.description && (
          <Tooltip
            body={
              <P3
                css={{
                  whiteSpace: 'pre-wrap',
                }}
              >
                {metric.description}
              </P3>
            }
            placement={'bottom-end'}
          >
            <Info
              css={{
                color: theme.palette.gray[50],
                cursor: 'pointer',
              }}
              size="xs"
            />
          </Tooltip>
        )}
      </div>
    </div>
  )
}

const MultiMetric = ({
  metrics,
  isLoading,
}: {
  metrics: InsightsSummaryMetric[]
  isLoading?: boolean
}): React.JSX.Element => {
  return (
    <div
      css={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
      }}
    >
      {metrics.map((metric, index) => (
        <SingleMetricWithPadding
          key={index}
          metric={metric}
          isLoading={isLoading}
        />
      ))}
    </div>
  )
}

export const MetricInfo = ({
  value,
  preLabel,
  postLabel,
}: InsightsSummaryMetricInfo) => {
  const theme = useTheme()
  return (
    <div
      css={{
        display: 'flex',
        color: theme.palette.violet[50],
        backgroundColor: theme.palette.violet.background,
        padding: remSpacing.small,
        gap: remSpacing.small,
        borderRadius: shape.borderRadius.small,
        marginBottom: remSpacing.medium,
      }}
    >
      <Info
        css={{
          color: theme.palette.violet[50],
          width: remSpacing.medium,
          height: remSpacing.medium,
        }}
      />
      <P3
        css={{
          whiteSpace: 'pre-wrap',
        }}
      >
        {preLabel}
        <BoldedSpan>&nbsp;{value}&nbsp;</BoldedSpan>
        {postLabel}
      </P3>
    </div>
  )
}

export const InsightsMetrics = ({
  metricsSections,
}: {
  metricsSections: React.ReactNode[]
}) => {
  return (
    <>
      {metricsSections.map((metricSection, ix) => {
        return (
          <Fragment key={`metric-section-${ix}`}>
            {ix > 0 && <Border />}
            {metricSection}
          </Fragment>
        )
      })}
    </>
  )
}

export const InsightsTile = ({
  metricsSections,
}: {
  metricsSections: React.ReactNode[]
}): React.JSX.Element => {
  return (
    <Tile
      css={{
        paddingLeft: remSpacing.medium,
      }}
    >
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
          flexDirection: 'column',
          height: '100%',
        }}
      >
        <InsightsMetrics metricsSections={metricsSections} />
      </div>
    </Tile>
  )
}
