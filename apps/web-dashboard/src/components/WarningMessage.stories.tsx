import { useState } from 'react'

import { useTheme } from '@emotion/react'

import { Meta } from '@storybook/react'

import { Button, Close, P3 } from '@apella/component-library'

import { WarningMessage } from './WarningMessage'

export default {
  title: 'Components/WarningMessage',
  component: WarningMessage,
} as Meta

export const Showcase = () => {
  return (
    <WarningMessage>
      <P3 css={{ fontWeight: 'bold' }}>
        Prediction is highly variable for this procedure.
      </P3>
      <P3>Please review case details before scheduling.</P3>
    </WarningMessage>
  )
}

export const WarningMessageWithCloseButton = () => {
  const [closed, setClosed] = useState(false)
  const theme = useTheme()
  return !closed ? (
    <WarningMessage>
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
          alignSelf: 'stretch',
        }}
      >
        <P3 css={{ fontWeight: 'bold' }}>
          Currently viewing unblocked available times
        </P3>
        <Button
          color="black"
          appearance="link"
          onClick={() => setClosed(true)}
          buttonType={'icon'}
          css={{ padding: 0 }}
        >
          <Close css={{ color: theme.palette.gray[60] }} size={'sm'} />
        </Button>
      </div>
      <P3>No block availability was found for Dr. House</P3>
    </WarningMessage>
  ) : null
}
