import { ReactNode, useCallback, useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router'

import { StringSchema, ValidationError, string as YupString } from 'yup'

import {
  Button,
  Delete,
  DotsHorizontal,
  Dropdown,
  DropdownWrapper,
  EllidedText,
  FlexItem,
  H5,
  InputText,
  NavigationTab,
  NavigationTabTitle,
  NavigationTabWrapper,
  Plus,
  Save,
  Tooltip,
  mediaQueries,
  pxSpacing,
  remSpacing,
  theme,
} from '@apella/component-library'
import { useKeyboardShortcuts } from '@apella/hooks'
import {
  UserViewContext,
  useUserFilterViews,
} from 'src/components/UserViews/useUserFilterViews'
import { pathsAreEqual, splitViewOptions } from 'src/components/UserViews/utils'

import { SecondaryView } from './types'
import { useUserParamContext } from './UserParamContext'

export interface SecondaryViewsProps {
  disabled?: boolean
  onClick?: (option: SecondaryView) => void
  selectedViewId?: string
  userViewContexts?: UserViewContext[]
  views: SecondaryView[]
}

const viewNameSchema = YupString()
  .trim('Name cannot be only whitespace')
  .required('Name is required')
  .min(3, 'Name must be at least 3 characters')
  .max(128, 'Name must be at most 128 characters')

const validateViewName = (
  name: string,
  schema: StringSchema
): [boolean, string | null] => {
  try {
    schema.validateSync(name)
  } catch (e: unknown) {
    if (e instanceof ValidationError) return [false, e.errors[0]]
  }

  return [true, null]
}

/**
 * Renders a horizontal list of view tabs, with the following UX logic, in left-to-right order:
 * 1. the first 3 non-custom views (ie: all users should always see the same views)
 * 2. the remaining (ie: 3 and up) non-custom views; hidden in dropdown on mobile
 * 3. a placeholder tab for naming a new custom view for the user. This is hidden until the user initiates this
 * (see #6); not applicable on mobile, in which this UX is hidden
 * 4. a combination of the currently selected custom view (if applicable) and latest N # of custom views,
 * where N is limited based on the view name (ie: longer view names prevent more views being displayed), rendered
 * in descending chronological order; hidden on mobile
 * 5. '+' icon to allow a user to allow the user to initiate the create new view workflow; hidden on mobile
 * 6. an icon to open a dropdown which shows all custom views which have been hidden (due to the logic in #4); SHOWN
 * on mobile
 * @param views : navigation tabs which should always be an option to the user (ie: these don't contain any
 *  user-specific navigation tabs / views)
 */
export const SecondaryViews = ({
  selectedViewId: selectedViewIdProp,
  views,
  onClick,
  disabled,
  userViewContexts,
}: SecondaryViewsProps): React.JSX.Element => {
  const { userFilterViews, selectedView, loading } = useUserFilterViews({
    contexts: userViewContexts,
  })

  const [showCreateUserViewInput, setShowCreateUserViewInput] =
    useState<boolean>(false)

  const selectedViewId = !loading
    ? selectedView || selectedViewIdProp
    : undefined

  const defaultViews = views.slice(0, 3) // non-custom views, the first 3 are 'Primary'
  const secondaryDefaultViews = views.slice(3) // non-custom views, the 3+ are 'Secondary'

  const allSecondaryViews = secondaryDefaultViews.concat(userFilterViews) // custom views are always secondary

  // given all tabs, determine which ones should be displayed
  const { shownUserOptions } = splitViewOptions({
    userOptions: allSecondaryViews,
    maxCharShown: 70,
    selectedId: selectedViewId,
  })

  return (
    <NavigationTabWrapper>
      {defaultViews.map((view, index) => {
        // only apply rounded borders to the last default view if there's NOT
        // going to be a view-dropdown (ie: there are no secondaryViews)
        const shouldApplyRoundBorderRadiusRightOnMobile =
          !allSecondaryViews.length && index === defaultViews.length - 1
        return (
          <NavigationTab
            isSelected={view.id === selectedViewId}
            disabled={disabled}
            shouldApplyRoundBorderRadiusRightOnMobile={
              shouldApplyRoundBorderRadiusRightOnMobile
            }
            key={view.id}
          >
            <NavigationTabTitle
              isSelected={view.id === selectedViewId}
              onClick={() => onClick && onClick(view)}
              to={view.to}
              disabled={disabled}
              shouldApplyIconStylings={!!view.icon}
            >
              <>
                {view.display}
                {!!view.icon && <IconWrapper>{view.icon}</IconWrapper>}
              </>
            </NavigationTabTitle>
          </NavigationTab>
        )
      })}
      {secondaryDefaultViews
        .filter((uv) => shownUserOptions.has(uv.id))
        .map((view) => (
          <NavigationTab
            isSelected={view.id === selectedViewId}
            disabled={disabled}
            hideOnMobile
            maxWidth={200}
            key={view.id}
          >
            <NavigationTabTitle
              isSelected={view.id === selectedViewId}
              onClick={() => onClick && onClick(view)}
              to={view.to as string}
              disabled={disabled}
              shouldApplyIconStylings={!!view.icon}
            >
              <>
                {view.display}
                {!!view.icon && <IconWrapper>{view.icon}</IconWrapper>}
              </>
            </NavigationTabTitle>
          </NavigationTab>
        ))}
      {userViewContexts && !!userViewContexts.length && (
        <>
          {/** render the tab for naming a new tab */}
          {showCreateUserViewInput && (
            <ViewInput onCancel={() => setShowCreateUserViewInput(false)} />
          )}
          {/** render all custom tabs */}
          {userFilterViews
            .filter((uv) => shownUserOptions.has(uv.id))
            .map((view: SecondaryView) => (
              <NavigationTab
                isSelected={view.id === selectedViewId}
                disabled={disabled}
                hideOnMobile
                maxWidth={200}
                key={view.id}
              >
                <NavigationTabTitle
                  isSelected={view.id === selectedViewId}
                  onClick={() => onClick && onClick(view)}
                  to={view.to as string}
                  disabled={disabled}
                  shouldApplyIconStylings={!!view.icon}
                >
                  <>{view.display}</>
                </NavigationTabTitle>
                {view.id === selectedViewId && (
                  <UserViewAction view={view} type="UPDATE" />
                )}
                {view.id === selectedViewId && (
                  <UserViewAction view={view} type="DELETE" />
                )}
              </NavigationTab>
            ))}
          {/** render the tab for allowing a user to create a custom tab */}
          <CreateNewViewTab
            onCreateNewViewClicked={() => setShowCreateUserViewInput(true)}
          />
        </>
      )}
      {/** if there are lots of tabs, show a dropdown for the tabs not rendered */}
      <ViewsDropdown
        views={allSecondaryViews}
        onClick={onClick}
        disabled={disabled}
        selectedViewId={selectedViewId}
      />
    </NavigationTabWrapper>
  )
}

const CreateNewViewTab = ({
  onCreateNewViewClicked,
}: {
  onCreateNewViewClicked: () => void
}) => {
  return (
    <FlexItem
      css={{
        display: 'none',
        [mediaQueries.lg]: {
          display: 'flex',
          alignItems: 'center',
        },
      }}
    >
      <Tooltip
        body="Create a new view"
        placement="bottom"
        isTouchEnabled={false}
      >
        <Button
          aria-label="Create view"
          onClick={onCreateNewViewClicked}
          type="button"
          buttonType="icon"
          appearance="link"
          size="sm"
        >
          <Plus />
        </Button>
      </Tooltip>
    </FlexItem>
  )
}

const IconWrapper = ({ children }: { children: ReactNode }) => {
  return (
    <span
      style={{
        marginLeft: remSpacing.xsmall,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {children}
    </span>
  )
}

const ViewsDropdown = ({
  views,
  disabled,
  selectedViewId,
  onClick,
}: {
  views: SecondaryView[]
  disabled?: boolean
  selectedViewId?: string
  onClick?: (option: SecondaryView) => void
}) => {
  const { shownUserOptions, hiddenUserOptions } = splitViewOptions({
    userOptions: views,
    maxCharShown: 70,
    selectedId: selectedViewId,
  })

  return (
    <FlexItem
      css={{
        display: !views.length ? 'none' : 'block',
        borderStyle: 'solid',
        borderWidth: '1px 0.5px 1px 0.5px',
        borderColor: theme.palette.gray[30],
        backgroundColor: 'transparent',
        borderRadius: '0px 8px 8px 0px',
        [mediaQueries.lg]: {
          width: 'auto',
          cursor: disabled ? 'default' : 'pointer',
          padding: `0 ${remSpacing.xsmall}`,
          border: 'none',
          backgroundColor: 'transparent',
          borderRadius: '0px',
          display: !hiddenUserOptions.size ? 'none' : undefined,
        },
      }}
    >
      <DropdownContainer>
        {views.map(({ id, display, ...view }) => (
          <Link
            key={id}
            style={{
              textDecoration: 'none',
              pointerEvents: disabled ? 'none' : undefined,
            }}
            css={{
              '&[aria-selected=true] > *': {
                color: theme.palette.text.alternate,
                backgroundColor: theme.palette.blue[50],
              },
              '&[aria-selected=false]:hover > *': {
                backgroundColor: theme.palette.blue[10],
              },
              '&[aria-disabled=true] > *': {
                color: theme.palette.gray[10],
              },
              [mediaQueries.lg]: {
                display: shownUserOptions.has(id) ? 'none' : undefined,
                '&[aria-selected=true] > *': {
                  color: theme.palette.text.primary,
                },
              },
            }}
            onClick={() => onClick?.({ id, display, ...view })}
            aria-disabled={disabled}
            aria-selected={selectedViewId === id}
            {...view}
          >
            <EllidedText
              css={{
                textAlign: 'left',
                lineHeight: `${pxSpacing.medium * 2.5}px`,
                padding: `0 ${remSpacing.xsmall}`,
                color: theme.palette.text.tertiary,
              }}
              as={H5}
            >
              {display}
            </EllidedText>
          </Link>
        ))}
      </DropdownContainer>
    </FlexItem>
  )
}

const DropdownContainer = ({ children }: { children: ReactNode }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  return (
    <DropdownWrapper
      display="block"
      open={isOpen}
      onClose={() => setIsOpen(false)}
      css={{
        alignItems: 'center',
        [mediaQueries.lg]: {
          cursor: 'pointer',
          borderStyle: 'none',
          borderWidth: '0px',
          backgroundColor: 'white',
        },
      }}
    >
      <Button
        aria-label="More views"
        onClick={() => setIsOpen(!isOpen)}
        type="button"
        buttonType="icon"
        appearance="link"
        size="md"
        color={isOpen ? 'black' : 'gray'}
      >
        <DotsHorizontal size="sm" />
      </Button>
      <Dropdown open={isOpen} left>
        {children}
      </Dropdown>
    </DropdownWrapper>
  )
}

const UserViewAction = ({
  type,
  view,
}: {
  type: 'UPDATE' | 'DELETE'
  view: SecondaryView
}) => {
  const location = useLocation()
  const currentPath = `${location.pathname}${location.search}`
  const { paramAllowList } = useUserParamContext()

  const { updateView, deleteView } = useUserFilterViews({
    skipLoadingUserViews: true,
  })

  if (
    type === 'UPDATE' &&
    !pathsAreEqual({
      path1: currentPath,
      path2: view.to,
      paramAllowList,
    })
  ) {
    return (
      <Tooltip body="Update view" placement="bottom" isTouchEnabled={false}>
        <Button
          aria-label="Update view"
          onClick={updateView({ id: view.id, name: view.display })}
          size="sm"
          type="button"
          buttonType="icon"
          appearance="link"
        >
          <Save size="xs" />
        </Button>
      </Tooltip>
    )
  }

  if (type === 'DELETE') {
    return (
      <Tooltip body="Delete view" placement="bottom" isTouchEnabled={false}>
        <Button
          aria-label="Delete view"
          onClick={deleteView(view.id)}
          size="sm"
          type="button"
          buttonType="icon"
          appearance="link"
          color="gray"
        >
          <Delete size="xs" />
        </Button>
      </Tooltip>
    )
  }

  return null
}

const ViewInput = ({ onCancel }: { onCancel: () => void }) => {
  const [value, setValue] = useState('')

  const navigate = useNavigate()

  const { createView } = useUserFilterViews({
    skipLoadingUserViews: true,
  })

  useKeyboardShortcuts((e: KeyboardEvent) => {
    switch (e.key) {
      case 'Enter':
        onSave(value)()
        break
      case 'Escape':
        onCancel()
        break
    }
  }, true)

  const onComplete = useCallback(
    (path: string) => {
      onCancel()
      navigate(path)
    },
    [navigate, onCancel]
  )

  const onSave = useCallback(
    (name: string) => async () => await createView(name, onComplete),
    [createView, onComplete]
  )

  const [isViewNameValid, errorMessage] = validateViewName(
    value,
    viewNameSchema
  )

  return (
    <div
      css={{
        display: 'none',
        [mediaQueries.lg]: {
          display: 'flex',
          alignItems: 'center',
          // Input has default margin that causes nav to shift from default heights, this removes it
          margin: `-${remSpacing.xsmall} 0`,
        },
      }}
    >
      <InputText
        width={200}
        innerRef={(ref: HTMLInputElement | null) => ref?.focus()}
        placeholder="Name this view"
        name="custom-view-input"
        value={value}
        onChange={(e) => setValue(e.target.value)}
      />

      <Tooltip
        body={errorMessage || 'Save view'}
        placement="bottom"
        isTouchEnabled={false}
      >
        <Button
          aria-label="Save view"
          size="sm"
          type="button"
          appearance="link"
          onClick={onSave(value)}
          disabled={!isViewNameValid}
          color={!isViewNameValid ? 'error' : 'primary'}
        >
          <Save />
        </Button>
      </Tooltip>
    </div>
  )
}
