import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router'

import { ThemeProvider } from '@emotion/react'

import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DateTime, Duration } from 'luxon'

import {
  ApellaDateTimeFormats,
  PaginatedTableColumn,
  theme,
} from '@apella/component-library'
import { METRICS, VideoBladeTableData } from 'src/pages/Insights/types'

import { RemotePaginatedTableWithVideoBlade } from './RemotePaginatedTableWithVideoBlade'

const mockedUsedNavigate = vi.fn()

vi.mock('react-router', async (importOriginal) => ({
  ...(await importOriginal<typeof import('react-router')>()),
  useNavigate: () => mockedUsedNavigate,
}))

describe('Remote Paginated Table with Video Blade', () => {
  const columns: PaginatedTableColumn<VideoBladeTableData>[] = [
    { name: 'Room', selector: 'roomName' },
    {
      name: 'Start time',
      selector: 'actualStartTime',
      formatter: (startTime: DateTime) =>
        startTime.toLocaleString(ApellaDateTimeFormats.DATETIME_WITH_WEEKDAY),
    },
  ]

  const data: VideoBladeTableData[] = [
    {
      roomId: '1',
      roomName: 'Room 1',
      actualStartTime: DateTime.fromISO('2023-11-17T08:30:00.000-08:00', {
        setZone: true,
      }),
      actualDuration: Duration.fromObject({ hours: 5 }),
      caseId: '1',
    },
    {
      roomId: '2',
      roomName: 'Room 2',
      actualStartTime: DateTime.fromISO('2023-11-17T08:00:00.000-08:00', {
        setZone: true,
      }),
      actualDuration: Duration.fromObject({ hours: 4, minutes: 30 }),
      caseId: '2',
    },
    {
      roomId: '3',
      roomName: 'Room 3',
      actualStartTime: DateTime.fromISO('2023-11-17T07:45:00.000-08:00', {
        setZone: true,
      }),
      actualDuration: Duration.fromObject({ hours: 1 }),
      caseId: '3',
    },
    {
      roomId: '4',
      roomName: 'Room 4',
      actualStartTime: DateTime.fromISO('2023-11-17T09:50:00.000-08:00', {
        setZone: true,
      }),
      actualDuration: Duration.fromObject({ minutes: 45 }),
      caseId: '4',
    },
    {
      roomId: '5',
      roomName: 'Room 5',
      actualStartTime: DateTime.fromISO('2023-11-17T10:35:00.000-08:00', {
        setZone: true,
      }),
      actualDuration: Duration.fromObject({ hours: 2, minutes: 1 }),
      caseId: '5',
    },
    {
      roomId: '6',
      roomName: 'Room 6',
      actualStartTime: DateTime.fromISO('2023-11-17T12:15:00.000-08:00', {
        setZone: true,
      }),
      actualDuration: undefined,
      caseId: '6',
    },
  ]

  beforeEach(() => {
    mockedUsedNavigate.mockClear()

    render(
      <ThemeProvider theme={theme}>
        <RemotePaginatedTableWithVideoBlade
          columns={columns}
          data={data}
          isLoading={false}
          isMultiSort={false}
          paginationType="minimal"
          sortOrder={undefined}
          hasNextPage={false}
          hasPreviousPage={false}
          onNextPageClicked={() => undefined}
          onPreviousPageClicked={() => undefined}
          onChangeSort={() => undefined}
          rowKeySelector={() => ''}
          onRowSelectionChanged={() => undefined}
          metric={METRICS.CASE}
        />
      </ThemeProvider>,
      { wrapper: BrowserRouter }
    )
  })

  it('sets an 8-hour interval for a 5-hour case starting at 30 min past the hour', async () => {
    expect(await screen.findByText('Room 1')).toBeInTheDocument()

    await userEvent.click(screen.getByText('Room 1'))

    const paramString = new URLSearchParams({
      roomId: '1',
      startTime: '2023-11-17T07:00:00-08:00',
      endTime: '2023-11-17T15:00:00-08:00',
      time: '2023-11-17T08:30:00-08:00',
      apellaCaseId: 'case:1',
    }).toString()

    expect(mockedUsedNavigate).toHaveBeenCalledWith(
      expect.objectContaining({
        search: expect.stringContaining(paramString),
      }),
      expect.any(Object)
    )
  })

  it('sets a 7-hour interval for a 4.5-hour case starting at 0 min past the hour', async () => {
    expect(await screen.findByText('Room 2')).toBeInTheDocument()

    await userEvent.click(screen.getByText('Room 2'))

    const paramString = new URLSearchParams({
      roomId: '2',
      startTime: '2023-11-17T07:00:00-08:00',
      endTime: '2023-11-17T14:00:00-08:00',
      time: '2023-11-17T08:00:00-08:00',
      apellaCaseId: 'case:2',
    }).toString()

    expect(mockedUsedNavigate).toHaveBeenCalledWith(
      expect.objectContaining({
        search: expect.stringContaining(paramString),
      }),
      expect.any(Object)
    )
  })

  it('sets a 4-hour interval for a 1-hour case', async () => {
    expect(await screen.findByText('Room 3')).toBeInTheDocument()

    await userEvent.click(screen.getByText('Room 3'))

    const paramString = new URLSearchParams({
      roomId: '3',
      startTime: '2023-11-17T06:00:00-08:00',
      endTime: '2023-11-17T10:00:00-08:00',
      time: '2023-11-17T07:45:00-08:00',
      apellaCaseId: 'case:3',
    }).toString()

    expect(mockedUsedNavigate).toHaveBeenCalledWith(
      expect.objectContaining({
        search: expect.stringContaining(paramString),
      }),
      expect.any(Object)
    )
  })

  it('sets a 4-hour interval for a case shorter than 1 hour', async () => {
    expect(await screen.findByText('Room 4')).toBeInTheDocument()

    await userEvent.click(screen.getByText('Room 4'))

    const paramString = new URLSearchParams({
      roomId: '4',
      startTime: '2023-11-17T08:00:00-08:00',
      endTime: '2023-11-17T12:00:00-08:00',
      time: '2023-11-17T09:50:00-08:00',
      apellaCaseId: 'case:4',
    }).toString()

    expect(mockedUsedNavigate).toHaveBeenCalledWith(
      expect.objectContaining({
        search: expect.stringContaining(paramString),
      }),
      expect.any(Object)
    )
  })

  it('sets a 5-hour interval for a 2 hour 1 minute case', async () => {
    expect(await screen.findByText('Room 5')).toBeInTheDocument()

    await userEvent.click(screen.getByText('Room 5'))

    const paramString = new URLSearchParams({
      roomId: '5',
      startTime: '2023-11-17T09:00:00-08:00',
      endTime: '2023-11-17T14:00:00-08:00',
      time: '2023-11-17T10:35:00-08:00',
      apellaCaseId: 'case:5',
    }).toString()

    expect(mockedUsedNavigate).toHaveBeenCalledWith(
      expect.objectContaining({
        search: expect.stringContaining(paramString),
      }),
      expect.any(Object)
    )
  })

  it('sets a 4-hour interval for a case with unset duration', async () => {
    expect(await screen.findByText('Room 6')).toBeInTheDocument()

    await userEvent.click(screen.getByText('Room 6'))

    const paramString = new URLSearchParams({
      roomId: '6',
      startTime: '2023-11-17T11:00:00-08:00',
      endTime: '2023-11-17T15:00:00-08:00',
      time: '2023-11-17T12:15:00-08:00',
      apellaCaseId: 'case:6',
    }).toString()

    expect(mockedUsedNavigate).toHaveBeenCalledWith(
      expect.objectContaining({
        search: expect.stringContaining(paramString),
      }),
      expect.any(Object)
    )
  })
})
