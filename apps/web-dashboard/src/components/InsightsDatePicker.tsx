import { useMemo } from 'react'

import { DateTime } from 'luxon'

import { DatePicker } from '@apella/component-library'
import { useTimezone } from 'src/Contexts'
import { useCurrentUser } from 'src/modules/user/hooks'

interface InsightsDatePickerProps {
  maxTime: string
  minTime: string
  onChangeDateRanges: (dates: [Date, Date]) => void
}

export const InsightsDatePicker = ({
  minTime,
  maxTime,
  onChangeDateRanges,
}: InsightsDatePickerProps): React.JSX.Element => {
  const { timezone } = useTimezone()
  const datePickerParams: [Date, Date] = useMemo(
    () => [new Date(minTime), new Date(maxTime)],
    [minTime, maxTime]
  )
  const today = DateTime.local({ zone: timezone })
  const { currentOrganization } = useCurrentUser()

  // Filter Keys

  // Hardcode date picker range for Health First to start at 3/17/2023 in order to hide historical data.
  // Health First does not want to see data containing user-entered timestamps, and so we want to hide
  // all data from before the Apella cameras went live on 3/17/2023.
  // TODO: Remove this once we have a better way to handle historical data
  const orgId = currentOrganization?.node?.id
  const healthFirstOrgId = 'health_first'
  // Also do the same in Apella Internal for testing purposes.
  const apellaInternalOrgId = 'apella_internal_0'
  const calendarProps =
    orgId == healthFirstOrgId || orgId == apellaInternalOrgId
      ? {
          minDate: new Date('2023-03-17T00:00:00.000-04:00'),
          maxDate: today.toJSDate(),
        }
      : {
          maxDate: today.toJSDate(),
        }

  const laterDate = (dates: Date[]) => new Date(Math.max(...dates.map(Number)))
  const setValue = (
    dates: Date | [Date | null, Date | null] | null | undefined
  ) =>
    Array.isArray(dates) && dates[0] && dates[1]
      ? orgId == healthFirstOrgId || orgId == apellaInternalOrgId
        ? onChangeDateRanges([
            laterDate([new Date('2023-03-17T00:00:00.000-04:00'), dates[0]]),
            dates[1],
          ])
        : onChangeDateRanges([dates[0], dates[1]])
      : undefined

  return (
    <DatePicker
      value={datePickerParams}
      selectRange={true}
      showPresets={true}
      timezone={timezone}
      setValue={setValue}
      calendarProps={calendarProps}
      excludeToday={true}
    />
  )
}
