import { useCallback } from 'react'

import {
  RemotePaginatedTable,
  RemotePaginatedTableProps,
} from '@apella/component-library'
import { METRICS, VideoBladeTableData } from 'src/pages/Insights/types'
import {
  calculateTurnoverLengthStatus,
  getAnalyticsAddtlTurnoverData,
} from 'src/utils/turnovers'

import { CaseType, TurnoverType } from '../__generated__/globalTypes'
import { EVENTS } from '../utils/analyticsEvents'
import { useOpenVideoBlade } from '../utils/useOpenVideoBlade'

type RemotePaginatedTableWithVideoBladeProps<T> =
  RemotePaginatedTableProps<T> & { metric: METRICS }

export const RemotePaginatedTableWithVideoBlade = <
  T extends VideoBladeTableData,
>({
  columns,
  data,
  isLoading,
  isMultiSort,
  paginationType,
  sortOrder,
  hasNextPage,
  hasPreviousPage,
  onNextPageClicked,
  onPreviousPageClicked,
  onChangeSort,
  metric,
}: RemotePaginatedTableWithVideoBladeProps<T>) => {
  const handleOpenVideoBlade = useOpenVideoBlade({ appendParams: true })
  const onRowClicked = useCallback(
    (data: VideoBladeTableData) => {
      const roomId = data.roomId

      const caseEndTimeWithBuffer = data.actualStartTime
        .plus(data.actualDuration ?? 0)
        .startOf('hour')
        .plus({ hours: 2 })
      // For short cases, continue to show the 4-hour window.
      const minimumEndTime = data.actualStartTime
        .startOf('hour')
        .plus({ hours: 3 })

      const endBufferHrs = minimumEndTime > caseEndTimeWithBuffer ? 2 : 1

      //We need to convert the caseId to an apellaCaseId or a phaseId
      //before passing it into handleOpenVideoBlade
      const caseId = data.caseId
        ? `case:${data?.caseId}`
        : data?.actualPhaseId
          ? `phase:${data?.actualPhaseId}`
          : undefined
      const lengthStatus = calculateTurnoverLengthStatus(
        data.actualStartTime,
        data.actualStartTime.plus(data.actualDuration ?? 0),
        { goalMinutes: data.goal, maxMinutes: data.maxMinutes }
      )
      const turnoverId =
        data.precedingCaseId && data.followingCaseId
          ? `turnover-case:${data.precedingCaseId}-case:${data.followingCaseId}`
          : undefined
      const turnoverAnalytics = turnoverId
        ? {
            turnover: {
              id: turnoverId,
              startTime: data.actualStartTime,
              endTime: data.actualStartTime.plus(data.actualDuration ?? 0),
            },
            addtlTurnoverData: getAnalyticsAddtlTurnoverData({
              overallLengthStatus: lengthStatus,
              currentLengthStatus: lengthStatus,
              type: TurnoverType.COMPLETE,
              startTime: data.actualStartTime,
              endTime: data.actualStartTime.plus(data.actualDuration ?? 0),
            }),
          }
        : undefined

      handleOpenVideoBlade(roomId, {
        apellaCase: caseId
          ? {
              id: caseId,
              startTime: data.actualStartTime,
              endTime: data.actualStartTime.plus(data.actualDuration ?? 0),
              type: CaseType.COMPLETE,
            }
          : undefined,
        time: data.actualStartTime,
        startBufferHrs: 1,
        endBufferHrs,
        analyticsEvent: EVENTS.OPEN_INSIGHTS_VIDEO_BLADE,

        turnover: turnoverAnalytics?.turnover,
        analyticsAddtlData: {
          metric,
          turnover: turnoverAnalytics?.addtlTurnoverData,
        },
      })
    },
    [handleOpenVideoBlade, metric]
  )

  return (
    <>
      <RemotePaginatedTable
        columns={columns}
        data={data}
        isLoading={isLoading}
        isMultiSort={isMultiSort}
        paginationType={paginationType}
        sortOrder={sortOrder}
        hasNextPage={hasNextPage}
        hasPreviousPage={hasPreviousPage}
        onNextPageClicked={onNextPageClicked}
        onPreviousPageClicked={onPreviousPageClicked}
        onChangeSort={onChangeSort}
        onRowClicked={onRowClicked}
      />
    </>
  )
}
