import { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'
import { rem } from 'polished'

import { ApellaDateTimeFormats, remSpacing } from '@apella/component-library'

import { useTimezone } from '../../Contexts'
import { calculateTimeMarkerPositionHelper } from '../../utils/timelineHelpers'
import { CURRENT_TIME_PILL_HEIGHT, CURRENT_TIME_PILL_WIDTH } from './cssConsts'

export interface ProgressLineProps {
  color?: string
  maxDateTime: DateTime
  minDateTime: DateTime
  time?: DateTime
}

const ProgressPill = ({
  time,
  color,
  minDateTime,
  maxDateTime,
}: ProgressLineProps) => {
  const { hasMultiple: hasMultipleTz } = useTimezone()
  const theme = useTheme()
  const left = useMemo(() => {
    if (!time) return 0

    return calculateTimeMarkerPositionHelper(time, minDateTime, maxDateTime)
  }, [time, minDateTime, maxDateTime])

  return (
    <div
      style={{
        // Every time `left` updates, emotion will create a new stylesheet with
        // a new class for that new value, so set it in inline style instead.
        left,
      }}
      css={{
        height: '100%',
        position: 'absolute',
        width: 1,
        pointerEvents: 'none',
        top: remSpacing.xsmall,
        background: color,
      }}
    >
      <div
        css={{
          position: 'absolute',
          top: 0,
          left: -CURRENT_TIME_PILL_WIDTH / 2 - 0.5,
          pointerEvents: 'none',
          height: CURRENT_TIME_PILL_HEIGHT,
          fontSize: rem('10px'),
          color: theme.palette.background.primary,
          background: color,
          width: CURRENT_TIME_PILL_WIDTH,
          borderRadius: CURRENT_TIME_PILL_HEIGHT,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {time?.toLocaleString(
          hasMultipleTz
            ? ApellaDateTimeFormats.TIME_WITH_TIMEZONE
            : ApellaDateTimeFormats.TIME
        )}
      </div>
    </div>
  )
}

const ProgressLine = ({
  time,
  minDateTime,
  maxDateTime,
  color,
}: ProgressLineProps) => {
  const left = useMemo(() => {
    if (!time) return 0

    return calculateTimeMarkerPositionHelper(time, minDateTime, maxDateTime)
  }, [time, minDateTime, maxDateTime])
  return (
    <div
      style={{
        // Every time `left` updates, emotion will create a new stylesheet with
        // a new class for that new value, so set it in inline style instead.
        left,
      }}
      css={{
        height: '100%',
        position: 'absolute',
        width: 1,
        pointerEvents: 'none',
        top: 0,
        background: color,
      }}
    />
  )
}

export { ProgressLine, ProgressPill }
