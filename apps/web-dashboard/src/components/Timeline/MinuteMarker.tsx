import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import {
  BIG_TICK_HEIGHT,
  HOUR_TICK_HEIGHT,
  LITTLE_TICK_HEIGHT,
  MINUTE_TICK_TOP_MARGIN,
} from './cssConsts'

export interface TimeMarkerProps {
  dt: DateTime
  left: string | number
}

const MinuteMarker = ({ dt, left }: TimeMarkerProps) => {
  const theme = useTheme()
  const height =
    dt.minute === 0
      ? HOUR_TICK_HEIGHT
      : dt.minute % 15 === 0
        ? BIG_TICK_HEIGHT
        : LITTLE_TICK_HEIGHT

  return (
    <div style={{ position: 'absolute', left: left }}>
      <div
        css={{
          position: 'relative',
          left: -0.5,
        }}
      >
        <div
          css={{
            background:
              dt.minute === 0 ? theme.palette.gray[50] : theme.palette.gray[40],
            height: height,
            width: 1,
            marginTop: MINUTE_TICK_TOP_MARGIN + (HOUR_TICK_HEIGHT - height),
          }}
        />
      </div>
    </div>
  )
}

export default MinuteMarker
