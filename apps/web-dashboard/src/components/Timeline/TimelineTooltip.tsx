import {
  remSpacing,
  shape,
  Span3,
  theme,
  Tooltip,
  TooltipProps,
} from '@apella/component-library'

export const TimelineTooltip = ({
  children,
  offset = 24,
  content,
  noWrap,
  ...rest
}: Omit<TooltipProps, 'body'> & {
  offset?: number
  content: string
  noWrap?: boolean
}) => {
  const tileProps = {
    style: {
      background: theme.palette.gray[60],
      color: theme.palette.gray[10],
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: shape.borderRadius.xsmall,
      display: 'flex',
      padding: remSpacing.xsmall,
    },
  }
  const tooltipMiddleware = [Tooltip.middleware.offset(offset)]

  return (
    <Tooltip
      middleware={tooltipMiddleware}
      isTouchEnabled={false}
      body={
        <Span3 css={{ whiteSpace: noWrap ? 'nowrap' : undefined }}>
          {content}
        </Span3>
      }
      {...tileProps}
      {...rest}
    >
      {children}
    </Tooltip>
  )
}
