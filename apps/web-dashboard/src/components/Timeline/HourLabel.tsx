import { useState } from 'react'

import { useTheme } from '@emotion/react'

import { DateTime } from 'luxon'

import { Caps3, ApellaDateTimeFormats } from '@apella/component-library'
import { useSize } from '@apella/hooks'

export interface HourMarkerProps {
  dt: DateTime
  left: string | number
}

const HourLabel = ({ dt, left }: HourMarkerProps) => {
  const theme = useTheme()
  const [hourMarkerRef, setHourMarkerRef] = useState<HTMLDivElement | null>(
    null
  )
  const hourMarkerSize = useSize(hourMarkerRef)

  return (
    <div
      style={{
        position: 'absolute',
        left: left,
      }}
    >
      <div
        style={{
          position: 'relative',
          color: theme.palette.text.secondary,
          left: hourMarkerSize && -hourMarkerSize?.width / 2,
        }}
        ref={setHourMarkerRef}
      >
        <Caps3 style={{ lineHeight: 'inherit', whiteSpace: 'nowrap' }}>
          {dt.toLocaleString(ApellaDateTimeFormats.TIME)}
        </Caps3>
      </div>
    </div>
  )
}

export default HourLabel
