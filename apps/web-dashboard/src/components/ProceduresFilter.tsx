import { ComponentProps, useMemo } from 'react'

import { sortBy } from 'lodash'

import { MultiFilterWithCount } from 'src/components/Filters/FilterWithCount'

export interface ProcedureShape {
  node: {
    id: string
    name: string
    count: number
  }
}

export const DEFAULT_PROCEDURES = undefined

export const ProceduresFilter = ({
  items,
  label,
  ...rest
}: ComponentProps<typeof MultiFilterWithCount>): React.JSX.Element => {
  const procedureOptions = useMemo(() => {
    return sortBy(items, [
      (procedure) => -(procedure.node.count ?? 0),
      (procedure) => procedure.node.name,
    ])
  }, [items])

  return (
    <MultiFilterWithCount
      items={procedureOptions}
      label={label ?? 'Procedures'}
      css={{ dropdownWidth: '35rem' }}
      {...rest}
    />
  )
}
