import { ReactNode } from 'react'

import { useTheme } from '@emotion/react'

import { H6, remSpacing } from '@apella/component-library'

export const FilterLabel = ({
  label,
  children,
}: {
  label: string
  children: ReactNode
}) => {
  const theme = useTheme()

  return (
    <div
      css={{
        display: 'flex',
        flexDirection: 'column',
        gap: remSpacing.xsmall,
      }}
    >
      <H6 css={{ color: theme.palette.text.secondary }}>{label}</H6>
      {children}
    </div>
  )
}
