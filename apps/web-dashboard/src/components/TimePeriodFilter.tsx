import { useState, useEffect, useMemo, useCallback } from 'react'

import debounce from 'lodash/debounce'
import { DateTime } from 'luxon'

import { ApellaDateTimeFormats } from '@apella/component-library'
import { DropdownRangeSlider } from 'src/components/DropdownSlider'

export interface TimePeriod<T extends number | DateTime | string> {
  max: T
  min: T
}

interface TimePeriodFilterProps {
  isInverted?: boolean
  onChangeInverted?: (isInverted: boolean) => void
  onChangeTimePeriod: (timePeriod: TimePeriod<string>) => void
  showInvertButton?: boolean
  stepSeconds?: number
  timePeriod: TimePeriod<string>
}

/**
 * Time Period Filter is used to filter by time of day.
 *
 * @param timePeriod an interval of ISO Time range.
 * @param onChangeTimePeriod a callback when the time period is changed.
 * @param stepSeconds number of seconds between each selectable tick
 */
export const TimePeriodFilter = ({
  timePeriod,
  onChangeTimePeriod,
  stepSeconds = 30 * 60, // 30 minutes
  showInvertButton = false,
  isInverted,
  onChangeInverted,
}: TimePeriodFilterProps): React.JSX.Element => {
  // The RangeSlider component only works with number values. Since this component is dealing with
  // times, it must somehow convert from times to number and vice versa.
  // To do so, the `timePeriod` is converted to epoch seconds for the current date.
  // The `min`/`max` ranges are also converted to epoch seconds.
  //
  // There's an alternative way to do this conversion which proved more complicated:
  // Have the `min`/`max` range of the Slider be from 0 to 86400 (seconds) in a day, and have the `timePeriod`
  // converted to the number of absolute seconds. The logic
  // proved to not use the Luxon library as natively since it doesn't have a Time object.
  const minTimeSeconds = DateTime.fromObject({ hour: 0 }).toSeconds()
  const maxTimeSeconds = DateTime.fromObject({
    hour: 23,
    minute: 59,
    second: 59,
  }).toSeconds()

  const timePeriodDateTime = {
    min: DateTime.fromISO(timePeriod.min),
    max: DateTime.fromISO(timePeriod.max),
  }
  const timePeriodSeconds = {
    min: timePeriodDateTime.min.toSeconds(),
    max: timePeriodDateTime.max.toSeconds(),
  }

  // This component need to maintain an internal state to allow separating
  // the slider interaction from sending network requests.
  const [internalValues, setInternalValues] = useState([
    timePeriodSeconds.min,
    timePeriodSeconds.max,
  ])

  // This approach goes against the best practices in the React documentation,
  // but unfortunately it is the only way to make the "Reset filters" button work.
  // Othwerise, it would have no effect on the internal state of the component,
  // and the internal state is necessary to reduce the number of network requests
  // sent while the use is interacting with the slider.
  useEffect(() => {
    setInternalValues([timePeriodSeconds.min, timePeriodSeconds.max])
  }, [timePeriodSeconds.min, timePeriodSeconds.max])

  // All the values below must depend on internal state to update properly.
  const minTime = internalValues[0]
  const maxTime = internalValues[1]

  const marks = {
    [minTime]: DateTime.fromSeconds(minTime).toLocaleString(
      ApellaDateTimeFormats.TIME
    ),
    [maxTime]: DateTime.fromSeconds(maxTime).toLocaleString(
      ApellaDateTimeFormats.TIME
    ),
  }

  const startTime = isInverted ? maxTime : minTime
  const endTime = isInverted ? minTime : maxTime
  const dropdownLabel = `${marks[startTime]} - ${marks[endTime]}`

  // Debounce the handler to avoid sending multiple network requests
  // while the user is still dragging the slider.
  const debouncedChangeTimePeriodHandler = useMemo(
    () =>
      debounce((timePeriod) => {
        onChangeTimePeriod(timePeriod)
      }, 300),
    [onChangeTimePeriod]
  )

  // Separating setting internal state from calling the change
  // handler is what allows us to maintain a good user interaction
  // while avoiding extra network requests.
  const onChangeSlider = useCallback(
    (sliderValues: number[]) => {
      setInternalValues(sliderValues)
      debouncedChangeTimePeriodHandler({
        min: DateTime.fromSeconds(sliderValues[0]).toISOTime({
          includeOffset: false,
        }),
        max: DateTime.fromSeconds(sliderValues[1]).toISOTime({
          includeOffset: false,
        }),
      })
    },
    [debouncedChangeTimePeriodHandler]
  )

  return (
    <DropdownRangeSlider
      label={'Time Period'}
      dropdownLabel={dropdownLabel}
      max={maxTimeSeconds}
      min={minTimeSeconds}
      step={stepSeconds}
      value={internalValues}
      marks={marks}
      onChange={onChangeSlider}
      showInvertButton={showInvertButton}
      isInverted={isInverted}
      onChangeInverted={onChangeInverted}
    />
  )
}
