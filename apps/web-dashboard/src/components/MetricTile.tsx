import { useTheme } from '@emotion/react'

import { FlexItem, shape } from '@apella/component-library'

export const MetricTile = ({
  children,
  className,
  borderColor,
}: {
  children?: React.ReactNode
  className?: string
  borderColor?: string
}) => {
  const theme = useTheme()

  return (
    <FlexItem
      css={{
        flex: 1,
        border: `1px solid`,
        borderColor: borderColor ?? theme.palette.gray[30],
        borderRadius: shape.borderRadius.xsmall,
        textAlign: 'center',
        color: theme.palette.text.secondary,
        display: 'grid',
      }}
      className={className}
    >
      {children}
    </FlexItem>
  )
}
