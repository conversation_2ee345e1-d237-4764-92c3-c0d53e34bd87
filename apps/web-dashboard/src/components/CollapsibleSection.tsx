import { useState } from 'react'

import {
  <PERSON><PERSON>,
  ChildrenProps,
  ExpandLess,
  ExpandMore,
  FlexContainer,
  H4,
  remSpacing,
} from '@apella/component-library'
import { useQueryFilterState } from 'src/modules/daily-metrics/hooks/useQueryFilterState'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

export type PostOpSectionTitle = 'Not started' | 'In progress'
type SectionTitle = 'blocks' | 'cases' | PostOpSectionTitle

const EventLookup: {
  [title in SectionTitle]?: { open: EVENTS; close: EVENTS }
} = {
  cases: {
    open: EVENTS.OPEN_CASE_DATA_PANEL,
    close: EVENTS.CLOSE_CASE_DATA_PANEL,
  },
  'Not started': {
    open: EVENTS.POSTOP_OPEN_NOT_STARTED_PANEL,
    close: EVENTS.POSTOP_CLOSE_NOT_STARTED_PANEL,
  },
  'In progress': {
    open: EVENTS.POSTOP_OPEN_IN_PROGRESS_PANEL,
    close: EVENTS.POSTOP_CLOSE_IN_PROGRESS_PANEL,
  },
}

export const CollapsibleSectionControlled = ({
  children,
  title,
  isOpen,
  onToggle: onToggleProp,
  ...rest
}: {
  title: SectionTitle
  isOpen: boolean
  onToggle: (newVal: boolean) => void
  css?: Record<string, any>
} & ChildrenProps) => {
  const filterState = useQueryFilterState()
  const eventsLogger = useAnalyticsEventLogger()

  const onToggle = () => {
    const tracking = EventLookup[title]

    if (tracking) {
      const Event = isOpen ? tracking.close : tracking.open
      eventsLogger(Event, { ...filterState, title })
    }

    onToggleProp(!isOpen)
  }

  return (
    <FlexContainer direction="column" gap={remSpacing.medium} {...rest}>
      <FlexContainer justifyContent="space-between">
        <FlexContainer gap={remSpacing.medium}>
          <H4 as="p" css={{ textTransform: 'capitalize' }}>
            {title}
          </H4>
        </FlexContainer>
        <Button
          buttonType="icon"
          size="sm"
          color="black"
          appearance="link"
          onClick={onToggle}
        >
          {isOpen ? <ExpandLess size="sm" /> : <ExpandMore size="sm" />}
        </Button>
      </FlexContainer>
      {isOpen && children}
    </FlexContainer>
  )
}

export const CollapsibleSection = ({
  children,
  defaultIsOpen = true,
  ...rest
}: {
  title: SectionTitle
  defaultIsOpen?: boolean
  css?: Record<string, any>
} & ChildrenProps) => {
  const [isOpen, setIsOpen] = useState(defaultIsOpen)

  return (
    <CollapsibleSectionControlled
      isOpen={isOpen}
      onToggle={setIsOpen}
      {...rest}
    >
      {children}
    </CollapsibleSectionControlled>
  )
}
