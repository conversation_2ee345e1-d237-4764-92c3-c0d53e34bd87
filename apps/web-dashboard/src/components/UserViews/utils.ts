import { To } from 'react-router'

import { SecondaryView } from '../types'

/**
 * Remove undefined values from the param state
 */
export const removeUndefinedProps = (
  paramState: Record<string, string | undefined>
): Record<string, string | string[]> => {
  const newParamState: Record<string, string | string[]> = {}
  for (const key in paramState) {
    const value = paramState[key]
    if (value !== undefined) {
      newParamState[key] = value
    }
  }
  return newParamState
}
/**
 * Given two paths (e.g. /insights/turnover?foo=bar and /insights/turnover?foo=baz), returns true
 * if the paths are the same (e.g. /insights/turnover) and the query params are the same (e.g. { foo: 'bar' } and { foo: 'baz' })
 * Need to perform ordering of query params to compare them.
 */
export const pathsAreEqual = ({
  path1,
  path2,
  paramAllowList,
}: {
  path1: To
  path2: To
  paramAllowList?: Set<string>
}): boolean => {
  const [pathname1, search1] =
    typeof path1 === 'string'
      ? path1.split('?')
      : [path1.pathname, path1.search]
  const [pathname2, search2] =
    typeof path2 === 'string'
      ? path2.split('?')
      : [path2.pathname, path2.search]

  if (pathname1 !== pathname2) {
    return false
  }

  const searchParams1 = new URLSearchParams(search1)
  const searchParams2 = new URLSearchParams(search2)

  if (searchParams1.toString() === searchParams2.toString()) {
    return true
  }

  const searchParams1Keys = paramAllowList
    ? Array.from(searchParams1.keys()).filter((key) => paramAllowList?.has(key))
    : Array.from(searchParams1.keys())
  const searchParams2Keys = paramAllowList
    ? Array.from(searchParams2.keys()).filter((key) => paramAllowList?.has(key))
    : Array.from(searchParams2.keys())

  if (searchParams1Keys.length !== searchParams2Keys.length) {
    return false
  }

  const sortedSearchParams1 = [...searchParams1Keys].sort((a, b) =>
    a.localeCompare(b)
  )
  const sortedSearchParams2 = [...searchParams2Keys].sort((a, b) =>
    a.localeCompare(b)
  )

  for (let i = 0; i < sortedSearchParams1.length; i++) {
    if (sortedSearchParams1[i] !== sortedSearchParams2[i]) {
      return false
    }
    const key = sortedSearchParams1[i]
    if (searchParams1.get(key) !== searchParams2.get(key)) {
      return false
    }
  }

  return true
}

/**
 * Given a list of user options, the selected id, and the max number of characters to show,
 * returns the shown and hidden user options. The selected option is always shown.
 */
export const splitViewOptions = ({
  userOptions,
  maxCharShown,
  selectedId,
}: {
  userOptions: SecondaryView[]
  maxCharShown: number
  selectedId?: string
}): {
  shownUserOptions: Set<string>
  hiddenUserOptions: Set<string>
} => {
  let shownUserOptions: SecondaryView[] = []
  let hiddenUserOptions: SecondaryView[] = []

  // split based on number of characters shown
  const totalCharacters = userOptions.reduce(
    (acc, option) => acc + option.display.length,
    0
  )

  if (totalCharacters > maxCharShown) {
    const reorderedUserOptions = [
      ...(userOptions.filter((option) => option.id === selectedId) ?? []),
      ...userOptions.filter((option) => option.id !== selectedId),
    ]

    let charactersShown = 0
    let idx = 0

    while (
      charactersShown < maxCharShown &&
      idx < reorderedUserOptions.length
    ) {
      // Math.min 25 because we use ellipsis on long text, it's just a best guess
      charactersShown += Math.min(reorderedUserOptions[idx].display.length, 25)
      idx += 1
    }

    shownUserOptions = userOptions.slice(0, idx)
    hiddenUserOptions = userOptions.slice(idx)

    // if selected item is in hidden, place it last in shown options
    const activeUserViewItemIdx = userOptions.findIndex(
      (option) => option.id === selectedId
    )
    if (activeUserViewItemIdx >= idx) {
      const lastPreviouslyShownOption =
        shownUserOptions[shownUserOptions.length - 1]
      shownUserOptions = [
        ...shownUserOptions.slice(0, idx - 1),
        userOptions[activeUserViewItemIdx],
      ]
      hiddenUserOptions = [
        lastPreviouslyShownOption,
        ...hiddenUserOptions.filter((option) => option.id !== selectedId),
      ]
    }
  } else {
    shownUserOptions = userOptions
  }

  return {
    shownUserOptions: new Set(shownUserOptions.map((v) => v.id)),
    hiddenUserOptions: new Set(hiddenUserOptions.map((v) => v.id)),
  }
}
