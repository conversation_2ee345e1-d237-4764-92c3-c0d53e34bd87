import { gql } from '@apollo/client'

export const GET_USER_FILTER_VIEWS = gql`
  query GetUserFilterViews {
    userFilterViews(query: {}) {
      id
      name
      url
      userId
      orgId
      createdTime
      updatedTime
    }
  }
`

export const CREATE_USER_FILTER_VIEW = gql`
  mutation CreateUserFilterView($input: UserFilterViewCreateInput!) {
    userFilterViewCreate(input: $input) {
      userFilterView {
        id
        name
        url
      }
    }
  }
`

export const UPDATE_USER_FILTER_VIEW = gql`
  mutation UpdateUserFilterView($input: UserFilterViewUpdateInput!) {
    userFilterViewUpdate(input: $input) {
      userFilterView {
        id
        name
        url
      }
    }
  }
`

export const DELETE_USER_FILTER_VIEW = gql`
  mutation DeleteUserFilterView($id: ID!) {
    userFilterViewDelete(id: $id) {
      success
    }
  }
`
