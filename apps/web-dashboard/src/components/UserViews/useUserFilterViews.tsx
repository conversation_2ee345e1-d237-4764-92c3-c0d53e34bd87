import { useCallback, useMemo } from 'react'
import { useLocation } from 'react-router'
import { toast } from 'react-toastify'

import { ApolloError, useMutation, useQuery } from '@apollo/client'

import { UserFilterViewUpdateInput } from 'src/__generated__/globalTypes'
import { getCleanedUrlParam } from 'src/utils/getCleanedUrlParam'

import {
  CreateUserFilterView,
  CreateUserFilterViewVariables,
  DeleteUserFilterView,
  DeleteUserFilterViewVariables,
  GetUserFilterViews,
  GetUserFilterViewsVariables,
  UpdateUserFilterView,
  UpdateUserFilterViewVariables,
} from './__generated__'
import {
  CREATE_USER_FILTER_VIEW,
  DELETE_USER_FILTER_VIEW,
  GET_USER_FILTER_VIEWS,
  UPDATE_USER_FILTER_VIEW,
} from './queries'

export const USER_VIEW_ID = 'viewId'

export enum UserViewContext {
  Insights = '/insights',
  Live = '/live',
  Schedule = '/schedule',
  StaffManagement = '/staff-management',
}

export const useUserFilterViews = ({
  contexts,
  skipLoadingUserViews,
}: {
  contexts?: UserViewContext[]
  skipLoadingUserViews?: boolean
}) => {
  const { search, pathname } = useLocation()
  const currentPath = `${pathname}${search}`
  const searchParams = useMemo(() => new URLSearchParams(search), [search])

  const onError = (error: ApolloError) => {
    toast.error(error.message)
  }

  const {
    loading,
    data: userFilterViewsData,
    refetch,
  } = useQuery<GetUserFilterViews, GetUserFilterViewsVariables>(
    GET_USER_FILTER_VIEWS,
    {
      skip: !contexts || !contexts.length || skipLoadingUserViews,
    }
  )

  const userFilterViews = useMemo(
    () =>
      (userFilterViewsData?.userFilterViews ?? [])
        .map((view) => {
          return {
            id: view.id,
            display: view.name,
            to: view.url,
          }
        })
        .filter(
          (view) =>
            contexts && contexts.some((context) => view.to.includes(context))
        ),
    [contexts, userFilterViewsData?.userFilterViews]
  )

  const [createUserFilterView] = useMutation<
    CreateUserFilterView,
    CreateUserFilterViewVariables
  >(CREATE_USER_FILTER_VIEW, {
    onCompleted: (data) => {
      const newView = data.userFilterViewCreate?.userFilterView
      if (newView) {
        refetch()
      }
    },
    onError,
  })

  const [updateUserFilterView] = useMutation<
    UpdateUserFilterView,
    UpdateUserFilterViewVariables
  >(UPDATE_USER_FILTER_VIEW, {
    onCompleted: (data) => {
      const updatedView = data.userFilterViewUpdate?.userFilterView
      if (updatedView) {
        refetch()
      }
    },
    onError,
  })

  const [deleteUserFilterView] = useMutation<
    DeleteUserFilterView,
    DeleteUserFilterViewVariables
  >(DELETE_USER_FILTER_VIEW, {
    onCompleted: (data) => {
      if (data.userFilterViewDelete?.success) {
        refetch()
      }
    },
    onError,
  })

  const createView = useCallback(
    async (name: string, onComplete: (path: string) => void) => {
      const id = crypto.randomUUID()
      const newSearchParams = new URLSearchParams(searchParams)
      newSearchParams.set(USER_VIEW_ID, JSON.stringify(id))
      const path = `${location.pathname}?${newSearchParams.toString()}`

      await createUserFilterView({
        variables: {
          input: {
            id,
            name,
            url: path,
          },
        },
      })

      onComplete(path)
    },
    [createUserFilterView, searchParams]
  )

  const updateView = useCallback(
    ({ id, name }: Pick<UserFilterViewUpdateInput, 'id' | 'name'>) =>
      () =>
        updateUserFilterView({
          variables: {
            input: {
              id,
              name,
              url: currentPath,
            },
          },
        }),
    [updateUserFilterView, currentPath]
  )

  const deleteView = useCallback(
    (id: string) => () => {
      deleteUserFilterView({
        variables: {
          id,
        },
      })
    },
    [deleteUserFilterView]
  )

  const selectedView = getCleanedUrlParam<string>({
    searchParams,
    key: USER_VIEW_ID,
    defaultValue: '',
  })

  return useMemo(
    () => ({
      selectedView,
      userFilterViews,
      loading,
      createView,
      updateView,
      deleteView,
    }),
    [selectedView, userFilterViews, loading, createView, updateView, deleteView]
  )
}
