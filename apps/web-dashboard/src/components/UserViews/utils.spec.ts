import { pathsAreEqual, splitViewOptions } from './utils'

describe('pathsAreEqual', () => {
  it('returns true when two strings are equal', () => {
    const result = pathsAreEqual({
      path1: `/insights/firstcasestarts?north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-12%22&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22`,
      path2: `/insights/firstcasestarts?north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-12%22&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22`,
    })
    expect(result).toEqual(true)
  })

  it('returns true when two paths have the same param sets and values (no matter their orderings)', () => {
    const result = pathsAreEqual({
      path1: `/insights/firstcasestarts?north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-12%22&north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22`,
      path2: `/insights/firstcasestarts?north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-12%22&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22`,
    })
    expect(result).toEqual(true)
  })

  it('returns true when two paths have the same param sets and values from an allowList (no matter their orderings)', () => {
    const result = pathsAreEqual({
      path1: `/insights/firstcasestarts?north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-12%22&north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22`,
      path2: `/insights/firstcasestarts?north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-12%22&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22&foo=bar`,
      paramAllowList: new Set([
        'north_bay_Insights_FirstCaseStart_dimension',
        'north_bay_Insights_FirstCaseStart_graph_sort',
        'north_bay_Insights_FirstCaseStart_weekdays',
        'north_bay_Insights_FirstCaseStart_procedures',
        'viewId',
        'startDate',
        'endDate',
      ]),
    })
    expect(result).toEqual(true)
  })

  it('returns false when two paths have different pathnames', () => {
    const result = pathsAreEqual({
      path1: `/insights/turnover?north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-12%22north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22`,
      path2: `/insights/firstcasestarts?north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-12%22&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22`,
    })
    expect(result).toEqual(false)
  })

  it('returns false when two paths have different search strings', () => {
    // diff is in endDate
    const result = pathsAreEqual({
      path1: `/insights/firstcasestarts?north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-22%22&north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22`,
      path2: `/insights/firstcasestarts?north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_dimension=%22ANESTHESIA%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&startDate=%222024-04-13%22&endDate=%222024-05-12%22&north_bay_Insights_FirstCaseStart_procedures=%5B%222b7c4ce4-57ff-40ac-87b5-d670248fb35f%22%5D&viewId=%227163fcac-d18a-4493-8e3d-aead603a90ca%22`,
    })
    expect(result).toEqual(false)
  })
})

describe('splitViewOptions', () => {
  const userViewOptions = [
    {
      display:
        'A very long long long long long long long long long long long long long view', // 75 characters
      id: 'd2a93cc9-c196-441a-98b6-a54674c3e119',
      to: '/insights/caselengths?startDate=%222024-05-13%22&endDate=%222024-05-20%22&north_bay_Insights_CaseLength_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_CaseLength_dimension=%22DAY%22&north_bay_Insights_CaseLength_graph_sort=%22Total+cases%22&north_bay_Insights_CaseLength_timePeriod=%7B%22min%22%3A%2200%3A00%3A00.000%22%2C%22max%22%3A%2223%3A59%3A00.000%22%7D',
    },
    {
      display: 'Weekend Cases', // 13 characters
      id: '05f30fca-9a18-4118-889d-dc5955844493',
      to: '/insights/caselengths?startDate=%222024-04-17%22&endDate=%222024-05-16%22&north_bay_Insights_CaseLength_weekdays=%5B%22saturday%22%2C%22sunday%22%5D&north_bay_Insights_CaseLength_dimension=%22DAY%22&north_bay_Insights_CaseLength_graph_sort=%22Total+cases%22',
    },

    {
      display: 'Another View', // 12 characters
      id: 'ced5c2e2-4188-4d1b-b401-924b1a8e0f0e',
      to: '/insights/caselengths?north_bay_Insights_CaseLength_rooms=%5B%22NB-OR1%22%5D&north_bay_Insights_CaseLength_dimension=%22MONTH%22&north_bay_Insights_CaseLength_graph_stack_grouping=%22Duration+compared+to+scheduled%22&startDate=%222023-11-14%22&endDate=%222024-05-14%22&viewId=%22ced5c2e2-4188-4d1b-b401-924b1a8e0f0e%22',
    },
    {
      display: 'Something FCOTS', // 15 characters
      id: 'c098bac9-31fc-4c91-b3ef-bcc7d1b285c1',
      to: '/insights/firstcasestarts?startDate=%222023-11-13%22&endDate=%222024-05-13%22&north_bay_Insights_FirstCaseStart_weekdays=%5B%22monday%22%2C%22tuesday%22%2C%22wednesday%22%2C%22thursday%22%2C%22friday%22%5D&north_bay_Insights_FirstCaseStart_dimension=%22MONTH%22&north_bay_Insights_FirstCaseStart_graph_sort=%22Late+starts%22&north_bay_Insights_FirstCaseStart_rooms=%5B%22NB-OR1%22%2C%22NB-OR2%22%5D&viewId=%22c098bac9-31fc-4c91-b3ef-bcc7d1b285c1%22',
    },
  ]
  const selectedId = 'ced5c2e2-4188-4d1b-b401-924b1a8e0f0e'

  it('should not hide any user view given all user views and max characters shown of 200 (ultra-wide screen), ', () => {
    const { shownUserOptions, hiddenUserOptions } = splitViewOptions({
      userOptions: userViewOptions,
      maxCharShown: 200,
      selectedId,
    })

    expect(shownUserOptions).toEqual(
      new Set(userViewOptions.map((uv) => uv.id))
    )
    expect(hiddenUserOptions).toEqual(new Set())
  })

  it('should split into 3:1 sub-lists given all user views and max characters shown of 50', () => {
    // Display length: 25 (cap) + 13 + 12 + 15 = 65
    // 65 > 50, so we should split into 3:1
    const { shownUserOptions, hiddenUserOptions } = splitViewOptions({
      userOptions: userViewOptions,
      maxCharShown: 50,
      selectedId,
    })

    expect(shownUserOptions).toEqual(
      new Set([
        userViewOptions[0].id,
        userViewOptions[1].id,
        userViewOptions[2].id,
      ])
    )
    expect(hiddenUserOptions).toEqual(new Set([userViewOptions[3].id]))
  })

  it('should split evenly into 2 sub-lists, last item in the shown list should be the selected view given all user views and max characters shown of 35, ', () => {
    const { shownUserOptions, hiddenUserOptions } = splitViewOptions({
      userOptions: userViewOptions,
      maxCharShown: 35,
      selectedId,
    })

    expect(shownUserOptions).toEqual(
      new Set([userViewOptions[0].id, userViewOptions[2].id])
    )
    expect(hiddenUserOptions).toEqual(
      new Set([userViewOptions[1].id, userViewOptions[3].id])
    )
  })
})
