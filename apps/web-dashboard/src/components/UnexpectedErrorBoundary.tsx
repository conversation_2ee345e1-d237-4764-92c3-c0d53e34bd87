import { useEffect } from 'react'
import { useRouteError } from 'react-router'

import { useTheme } from '@emotion/react'

import { ErrorPage, P2, ButtonLink } from '@apella/component-library'
import { LocationPath } from 'src/router/types'
import { logger } from 'src/utils/exceptionLogging'

export const UnexpectedErrorBoundary = () => {
  const theme = useTheme()
  const error = useRouteError()

  useEffect(() => {
    if (error) {
      // eslint-disable-next-line no-console
      console.error('Unexpected Error', error)
      logger.error('Unexpected Error', { errorMessage: error })
    }
  }, [error])

  return (
    <ErrorPage
      header="Unexpected Error Occurred"
      body={
        <P2 color={theme.palette.text.secondary}>Please try again later</P2>
      }
      actions={
        <ButtonLink
          to={{
            pathname: LocationPath.Root,
          }}
        >
          Go Home
        </ButtonLink>
      }
    />
  )
}
