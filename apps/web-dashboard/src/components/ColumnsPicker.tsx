import { MultiSelect, Option } from '@apella/component-library'

export const ColumnsPicker = ({
  label = 'Columns',
  name = 'column-picker',
  bulkSelect = true,
  columnNames,
  selectedColumnNames,
  onChange,
}: {
  label: string
  name: string
  bulkSelect: boolean
  columnNames: string[]
  selectedColumnNames: string[]
  onChange?: (value?: string[]) => void
}): React.JSX.Element => {
  return (
    <MultiSelect
      name={name}
      bulkSelect={bulkSelect}
      disabled={!columnNames}
      displayLabel={label}
      label={label}
      value={selectedColumnNames}
      onChange={onChange}
      search={true}
      left
      hideTooltip
    >
      {columnNames?.map((colName) =>
        colName ? (
          <Option key={colName} value={colName} label={colName} />
        ) : null
      )}
    </MultiSelect>
  )
}
