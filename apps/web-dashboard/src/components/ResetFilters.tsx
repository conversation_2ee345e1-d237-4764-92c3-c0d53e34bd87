import { But<PERSON>, <PERSON> } from '@apella/component-library'

interface ResetFiltersProps
  extends Omit<React.ComponentProps<typeof Button>, 'onClick' | 'appearance'> {
  resetActions: () => void
}

export const ResetFilters = ({
  resetActions,
  ...rest
}: ResetFiltersProps): React.JSX.Element => {
  return (
    <Button onClick={resetActions} appearance="link" {...rest}>
      <Close size="sm" />
      Reset filters
    </Button>
  )
}
