import { ComponentProps } from 'react'

import { sumBy } from 'lodash'

import { MultiSelect, Option, SingleSelect } from '@apella/component-library'
import { DataNodeWithCount } from 'src/components/Filters/helper'

export const toIds = (ids?: string | string[]) => {
  if (Array.isArray(ids)) {
    return ids.length ? ids : undefined
  }

  return ids ? [ids] : undefined
}

export interface SingleFilterWithCountProps<T>
  extends Omit<ComponentProps<typeof SingleSelect>, 'name' | 'children'> {
  disableSelectAllOption?: boolean
  items: DataNodeWithCount<T>[]
  selectAllLabel?: string
  value?: string
}

export const SingleFilterWithCount = <T,>({
  items,
  value,
  label,
  selectAllLabel,
  disableSelectAllOption,
  ...props
}: SingleFilterWithCountProps<T>): React.JSX.Element => {
  return (
    <SingleSelect
      name={`${label}-filter`}
      label={label}
      value={value}
      search={true}
      {...props}
    >
      {!disableSelectAllOption && (
        <Option
          key={`${label}-all-option`}
          label={selectAllLabel ?? 'All'}
          count={
            items.some((i) => i.node.count !== undefined)
              ? sumBy(items, (item) => item.node?.count ?? 0)
              : undefined
          }
        />
      )}
      {items.map((item) => (
        <Option
          key={item.node.id}
          value={item.node.id}
          label={item.node.name}
          count={item.node.count}
          group={item.node.group}
        />
      ))}
    </SingleSelect>
  )
}

export interface MultiFilterWithCountProps<T>
  extends Omit<ComponentProps<typeof MultiSelect>, 'name' | 'children'> {
  items: DataNodeWithCount<T>[]
  selectedIds?: string[]
}

export const MultiFilterWithCount = <T,>({
  items,
  selectedIds,
  label,
  ...props
}: MultiFilterWithCountProps<T>): React.JSX.Element => {
  return (
    <MultiSelect
      name={`${label}-filter`}
      label={label}
      value={selectedIds}
      search={true}
      {...props}
    >
      {items.map((item) => (
        <Option
          key={item.node.id}
          value={item.node.id}
          label={item.node.name}
          count={item.node.count}
          group={item.node.group}
        />
      ))}
    </MultiSelect>
  )
}
