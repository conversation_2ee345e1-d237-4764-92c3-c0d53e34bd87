import { Option, SingleSelect } from '@apella/component-library'

type MinimumDurationFilterProps = {
  name?: string
  label?: string
  value: string
  onChange: (value: number) => void
}

export const MinimumDurationFilter = ({
  name = 'minimum-duration-filter',
  label = 'Minimum Duration',
  value,
  onChange,
  ...props
}: MinimumDurationFilterProps) => {
  const options = [
    {
      value: '30',
      label: '30m',
    },
    {
      value: '60',
      label: '60m',
    },
    {
      value: '90',
      label: '90m',
    },
    {
      value: '120',
      label: '120m',
    },
    {
      value: '180',
      label: '180m',
    },
    {
      value: '240',
      label: '240m +',
    },
  ]
  return (
    <SingleSelect
      name={name}
      label={label}
      value={value}
      onChange={(option) => onChange(Number(option))}
      {...props}
    >
      {options.map(({ value, label }) => (
        <Option
          key={label}
          value={value}
          label={label}
          group={'Minimum Duration'}
        />
      ))}
    </SingleSelect>
  )
}
