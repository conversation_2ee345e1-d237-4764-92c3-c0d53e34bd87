import { Meta, StoryObj } from '@storybook/react'
import { userEvent, within } from '@storybook/test'

import { MinimumDurationFilter } from './MinimumDurationFilter'

const meta: Meta<typeof MinimumDurationFilter> = {
  title: 'Components/MinimumDuration',
  component: MinimumDurationFilter,
  decorators: [
    (Story) => {
      return <Story />
    },
  ],
}

type Story = StoryObj<typeof MinimumDurationFilter>

export const Open: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)

    const floatingActionButton = canvas.getByRole('button')
    await userEvent.click(floatingActionButton)
  },
}

export const Closed: Story = {}

export default meta
