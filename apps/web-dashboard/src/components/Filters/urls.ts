import { DateTime } from 'luxon'

import { Direction, OrderBy } from 'src/__generated__/globalTypes'

// A human readable format for dates
export const yyyyMMdd = 'yyyy-MM-dd'
const SEPARATOR = '~'
const PAIR_SEPARATOR = '-'

export const dateTimeToUrlFriendlyDate = (datetime: DateTime): string =>
  datetime.toFormat(yyyyMMdd)

export const urlFriendlyDateToJSDate = (
  urlFriendlyDate: string,
  locale: string
): string =>
  DateTime.fromFormat(urlFriendlyDate, yyyyMMdd, { zone: locale }).toISO()

/**
 * orderBy: [
    {
      sort: 'startTime',
      direction: Direction.ASC,
    },
    {
      sort: 'status',
      direction: Direction.DESC,
    },
  ]
  becomes 'startTime-ASC~status-DESC', vice versa
 */
export const orderByToUrlFriendly = (
  orderBys?: OrderBy[]
): string | undefined =>
  orderBys &&
  orderBys
    .map((o) => `${o.sort}${PAIR_SEPARATOR}${o.direction}`)
    .join(SEPARATOR)

export const urlFriendlyOrderStrToOrderBy = (
  urlFriendlyOrderByStr?: string,
  sortableFields?: string[]
): OrderBy[] | undefined => {
  if (urlFriendlyOrderByStr && typeof urlFriendlyOrderByStr === 'string') {
    return urlFriendlyOrderByStr
      .split(SEPARATOR)
      .map((pair: string): OrderBy | undefined => {
        const [sort, direction] = pair.split(PAIR_SEPARATOR)

        if (sortableFields?.includes(sort)) {
          return {
            sort,
            direction: direction === 'ASC' ? Direction.ASC : Direction.DESC,
          }
        }
      })
      .filter((o): o is OrderBy => !!o)
  }
}
