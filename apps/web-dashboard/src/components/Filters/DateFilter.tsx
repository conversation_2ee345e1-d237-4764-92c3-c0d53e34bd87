import { DatePicker } from '@apella/component-library'
import { useTimezone } from 'src/Contexts'

interface DateFilterProps
  extends Pick<
    React.ComponentProps<typeof DatePicker>,
    | 'buttonAlignment'
    | 'buttonFullWidth'
    | 'showArrow'
    | 'showIcon'
    | 'showPresets'
  > {
  onChangeDate: (dates: [Date, Date]) => void
  selected?: [Date, Date]
}

export const DateFilter = ({
  selected,
  onChangeDate,
  showPresets = true,
  buttonAlignment,
  buttonFullWidth,
  showArrow,
  showIcon,
}: DateFilterProps): React.JSX.Element => {
  const { timezone } = useTimezone()
  return (
    <DatePicker
      value={selected}
      selectRange={true}
      showPresets={showPresets}
      setValue={(dates) =>
        Array.isArray(dates) &&
        dates[0] &&
        dates[1] &&
        onChangeDate([dates[0], dates[1]])
      }
      timezone={timezone}
      buttonAlignment={buttonAlignment}
      buttonFullWidth={buttonFullWidth}
      showArrow={showArrow}
      showIcon={showIcon}
    />
  )
}
