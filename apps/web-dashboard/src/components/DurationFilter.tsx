import { useState, useEffect, useMemo, useCallback } from 'react'

import debounce from 'lodash/debounce'

import { DropdownRangeSlider } from 'src/components/DropdownSlider'

import { TimePeriod } from './TimePeriodFilter'

export const SLIDER_MAX_VALUE = 600
export const SLIDER_MIN_VALUE = 0

export const SLIDER_STEP_VALUE = 15

type DurationFilterProps = {
  value: TimePeriod<number>
  onChange: (value: TimePeriod<number>) => void
  stepSeconds?: number
}

export const DurationFilter = ({
  value,
  onChange,
  stepSeconds = SLIDER_STEP_VALUE,
}: DurationFilterProps): React.JSX.Element => {
  // This component need to maintain an internal state to allow separating
  // the slider interaction from sending network requests.
  const [internalValues, setInternalValues] = useState([value.min, value.max])

  // Debounce the handler so that only one network request is sent at the
  // end of the interaction.
  const debouncedChangeHandler = useMemo(
    () =>
      debounce((timePeriod) => {
        onChange(timePeriod)
      }, 300),
    [onChange]
  )

  // When the user interacts with the slider, set the internal state to
  // render updated UI, then call the debounced change handler.
  const onChangeSlider = useCallback(
    (sliderValues: number[]) => {
      setInternalValues(sliderValues)
      debouncedChangeHandler({
        min: sliderValues[0],
        max: sliderValues[1],
      })
    },
    [debouncedChangeHandler]
  )

  // This is necessary for "Reset filters" to work.
  useEffect(() => {
    setInternalValues([value.min, value.max])
  }, [value.min, value.max])

  let style = {}

  const suffix = 'm'
  // All the values below must depend on internal state to update properly.
  const min = internalValues[0]
  const max = internalValues[1]
  const marks = {
    [min]: { style, label: `${min}${suffix}` },
    [max]: { style, label: `${max}${suffix}` },
  }

  const dropdownLabel = `${marks[min].label} - ${marks[max].label}`

  // Make sure the labels don't overlap for small ranges.
  const valueDifference = max - min
  if (valueDifference <= 60) {
    const distance = (-4 / 3) * (valueDifference - 30)
    style = { transform: `translateX(${distance}%)` }
    marks[max] = { style, label: `${max}${suffix}` }
  }

  return (
    <DropdownRangeSlider
      dropdownLabel={dropdownLabel}
      label={'Duration'}
      marks={marks}
      min={SLIDER_MIN_VALUE}
      max={SLIDER_MAX_VALUE}
      value={internalValues}
      onChange={onChangeSlider}
      step={stepSeconds}
    />
  )
}
