import { useCallback } from 'react'

import { Option, SingleSelect } from '@apella/component-library'

export enum FlipRoomValues {
  AllTurnovers = 'all_turnovers',
  SameSurgeonSameRoom = 'same_surgeon_same_room',
  ShowFlipRooms = 'show_flip_rooms',
  HideFlipRooms = 'hide_flip_rooms',
}

export interface FlipRoomProps {
  onChange: (selected?: FlipRoomValues) => void
  selected?: FlipRoomValues
}

export const FlipRoomFilter = ({
  selected,
  onChange,
}: FlipRoomProps): React.JSX.Element => {
  const onChangeWrapper = useCallback(
    (selected?: string) => isFlipRoomValue(selected) && onChange(selected),
    [onChange]
  )

  return (
    <SingleSelect
      name={'FlipRooms'}
      value={selected}
      label={'Display'}
      onChange={onChangeWrapper}
    >
      <Option
        key={FlipRoomValues.AllTurnovers}
        label={'All turnovers'}
        value={undefined}
      />
      <Option
        key={FlipRoomValues.SameSurgeonSameRoom}
        label={'Show same surgeon same room'}
        value={FlipRoomValues.SameSurgeonSameRoom}
      />
      <Option
        key={FlipRoomValues.HideFlipRooms}
        label={'Hide flip rooms'}
        value={FlipRoomValues.HideFlipRooms}
      />
      <Option
        key={FlipRoomValues.ShowFlipRooms}
        label={'Show only flip rooms'}
        value={FlipRoomValues.ShowFlipRooms}
      />
    </SingleSelect>
  )
}

const isFlipRoomValue = (value: any): value is FlipRoomValues | undefined =>
  value === undefined || Object.values(FlipRoomValues).includes(value)
