import { useState } from 'react'

import userEvent from '@testing-library/user-event'
import { render, screen } from 'test-utils'

import { DayOfWeek } from 'src/pages/Insights/types'

import { DayOfWeekFilter } from './DayOfWeekFilter'

// Needed so that the component rerenders when a new option is selected.
const ParentComp = () => {
  const [value, setValue] = useState<DayOfWeek[] | undefined>(undefined)
  return <DayOfWeekFilter selected={value} onChange={setValue} />
}

describe('Day of Week Filter', () => {
  beforeEach(async () => {
    render(<ParentComp />)
  })

  it.each([
    ['Weekdays'],
    ['Weekends'],
    ['Entire week'],
    ['Sundays'],
    ['Mondays'],
    ['Tuesdays'],
    ['Wednesdays'],
    ['Thursdays'],
    ['Fridays'],
    ['Saturdays'],
  ])('selects the correct option', async (option) => {
    await userEvent.click(screen.getByRole('button'))
    await userEvent.click(screen.getByTitle(option))
    await userEvent.click(screen.getByRole('button'))
    expect(screen.getByRole('button')).toHaveTextContent(option)
    expect(screen.getByTitle(option)).toHaveAttribute('aria-selected', 'true')
  })
})
