import { Fragment, ReactElement, ReactNode } from 'react'
import { Link } from 'react-router'

import { useTheme } from '@emotion/react'

import {
  Caps3,
  ChevronRight,
  H1,
  H4,
  mediaQueries,
  pxSpacing,
  remSpacing,
  typography,
} from '@apella/component-library'
import {
  SecondaryViews,
  SecondaryViewsProps,
} from 'src/components/SecondaryViews'

import { SecondaryView } from './types'

export interface PageHeaderProps extends Partial<SecondaryViewsProps> {
  actionGap?: string
  actions?: ReactNode
  breadcrumbs?: SecondaryView[]
  subTitle?: string
  title?: string | ReactElement
}

export const PageHeader = ({
  selectedViewId,
  actions,
  actionGap,
  disabled,
  views,
  onClick,
  title,
  subTitle,
  breadcrumbs,
  userViewContexts,
}: PageHeaderProps) => {
  const theme = useTheme()

  const hasViews =
    (!!views && !!views.length) || (userViewContexts && userViewContexts.length)

  if (!title && !hasViews && !actions) {
    return null
  }

  return (
    <div
      css={{
        display: 'grid',
        gap: remSpacing.medium,
        alignItems: 'center',
        gridTemplateAreas: `"title actions" ${hasViews ? '"views views"' : ''}`,
        width: '100%',
        [mediaQueries.lg]: {
          paddingTop: remSpacing.xsmall,
          gridTemplateAreas: `"title views actions"`,
          gridTemplateColumns: 'max-content max-content auto',
        },
      }}
    >
      {title && (
        <div css={{ gridArea: 'title', overflowX: 'auto' }}>
          <div
            css={{
              display: 'grid',
              gridAutoFlow: 'column',
              gridAutoColumns: 'max-content',
              gap: remSpacing.xsmall,
              alignItems: 'center',
              whiteSpace: 'nowrap',
            }}
          >
            {!!breadcrumbs &&
              breadcrumbs.map((crumb) => {
                return (
                  <Fragment key={crumb.id}>
                    <Link
                      css={{
                        ...typography.h4,
                        color: theme.palette.text.primary,
                        lineHeight: `${pxSpacing.xlarge}px`,
                        textDecoration: 'none',
                        '&:hover, &:focus': {
                          textDecoration: 'underline',
                        },
                      }}
                      to={crumb.to}
                    >
                      {crumb.display}
                    </Link>
                    <ChevronRight size="xs" />
                  </Fragment>
                )
              })}
            <H4
              as={H1}
              css={{
                display: 'grid',
                gridAutoFlow: 'column',
                gridAutoColumns: 'minmax(0, max-content)',
                gap: remSpacing.xsmall,
                alignItems: 'center',
                overflowX: 'hidden',
                lineHeight: `${pxSpacing.xlarge}px`,
              }}
            >
              {title}
            </H4>
          </div>

          {subTitle && <Caps3 as="h2">{subTitle}</Caps3>}
        </div>
      )}
      {hasViews && (
        <SecondaryViews
          selectedViewId={selectedViewId}
          views={views ?? []}
          onClick={onClick}
          disabled={disabled}
          userViewContexts={userViewContexts}
        />
      )}
      {!!actions && (
        <div
          css={{
            display: 'grid',
            gridArea: 'actions',
            justifySelf: 'end',
            gridAutoFlow: 'column',
            gap: actionGap ?? remSpacing.xxsmall,
            alignItems: 'center',
            gridAutoColumns: 'minmax(0, max-content)',
          }}
        >
          {actions}
        </div>
      )}
    </div>
  )
}
