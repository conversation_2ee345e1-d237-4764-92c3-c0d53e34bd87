import { ReactNode } from 'react'

import { useTheme } from '@emotion/react'

import { remSpacing, H3, P2 } from '@apella/component-library'
import { SvgIconProps } from '@apella/component-library/src/icons/SvgIcon'

export const EmptyState = ({
  message,
  subtext,
  Icon,
  children,
}: {
  message: string
  subtext: string
  Icon?: React.ComponentType<SvgIconProps>
  children?: ReactNode
}) => {
  const theme = useTheme()

  return (
    <div
      css={{
        display: 'flex',
        padding: remSpacing.xlarge,
        margin: remSpacing.gutter,
        alignItems: 'center',
        flexDirection: 'column',
        gap: remSpacing.xsmall,
      }}
    >
      {Icon && (
        <Icon
          css={{ alignSelf: 'center', color: theme.palette.gray[20] }}
          size="xl"
        />
      )}
      <H3 as="h2">{message}</H3>
      <P2 css={{ color: theme.palette.text.secondary }}>{subtext}</P2>
      {children}
    </div>
  )
}
