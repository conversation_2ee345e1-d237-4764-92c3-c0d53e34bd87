import { createContext, useContext } from 'react'

export const UserParamContext = createContext<{
  paramAllowList: Set<string>
  updateParamAllowList: (newList: string[]) => void
}>({
  paramAllowList: new Set(),
  updateParamAllowList: () => {},
})

export const useUserParamContext = () => {
  const context = useContext(UserParamContext)
  if (context === undefined) {
    throw new Error('Context must be used within a UserParamContext Provider')
  }
  return context
}
