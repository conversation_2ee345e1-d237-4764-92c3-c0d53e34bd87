import { ComponentProps, useMemo } from 'react'

import { gql, useQuery } from '@apollo/client'

import { MultiSelect, Option } from '@apella/component-library'

import { GetInsightsCaseClassification } from './__generated__'

export const DEFAULT_CASE_CLASSIFICATION_TYPES_ID = undefined

export const NONE_CASE_CLASSIFICATION_ID = 'CASE_CLASSIFICATION_NONE'

export const GET_INSIGHTS_CASE_CLASSIFICATION = gql`
  query GetInsightsCaseClassification {
    caseClassificationTypes {
      name
      id
    }
  }
`
export const NONE_CASE_CLASSIFICATION = {
  id: NONE_CASE_CLASSIFICATION_ID,
  name: 'None',
}

export const useCaseClassificationTypes = () => {
  const { loading, data: filtersData } =
    useQuery<GetInsightsCaseClassification>(GET_INSIGHTS_CASE_CLASSIFICATION)

  const caseClassificationTypes = useMemo(
    () => [
      NONE_CASE_CLASSIFICATION,
      ...(filtersData?.caseClassificationTypes ?? []),
    ],
    [filtersData?.caseClassificationTypes]
  )

  return { loading, caseClassificationTypes }
}

export const CaseClassificationTypesFilter = ({
  name = 'case-classification-types-filter',
  label = 'Case Classification Types',
  ...props
}: Omit<
  ComponentProps<typeof MultiSelect>,
  'children' | 'name' | 'disabled' | 'bulkSelect'
> & {
  name?: string
}): React.JSX.Element => {
  const { loading, caseClassificationTypes } = useCaseClassificationTypes()

  return (
    <MultiSelect
      disabled={loading}
      name={name}
      label={label}
      bulkSelect
      {...props}
    >
      {caseClassificationTypes.map((cct) => (
        <Option key={cct.id} value={cct.id} label={cct.name} />
      ))}
    </MultiSelect>
  )
}
