import { Meta, StoryObj } from '@storybook/react'

import { EmailListInput } from './EmailListInput'

const meta: Meta<typeof EmailListInput> = {
  title: 'Components/EmailListInput',
  component: EmailListInput,
  args: {
    placeholder: 'Enter recipient emails',
    allowDisplayName: true,
    emails: ['<PERSON> <<EMAIL>>', '<PERSON> <<EMAIL>>'],
  },
  decorators: [
    (Story) => {
      return <Story />
    },
  ],
}

type Story = StoryObj<typeof EmailListInput>

export const Showcase: Story = {}

export default meta
