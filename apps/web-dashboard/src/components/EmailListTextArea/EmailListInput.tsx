import * as React from 'react'

import { rem } from 'polished'

import {
  Button,
  Close,
  lightShadows,
  remSpacing,
  shape,
  theme,
} from '@apella/component-library'

export interface IReactMultiEmailProps {
  allowDisplayName?: boolean
  allowDuplicate?: boolean
  autoComplete?: string
  autoFocus?: boolean
  className?: string
  delimiter?: string
  disableOnBlurValidation?: boolean
  emails?: string[]
  enable?: ({ emailCnt }: { emailCnt: number }) => boolean
  id?: string
  initialInputValue?: string
  inputClassName?: string
  inputName?: string
  inputValue?: string
  noClass?: boolean
  onBlur?: () => void
  onChange?: (emails: string[]) => void
  onChangeInput?: (value: string) => void
  onDisabled?: () => void
  onFocus?: () => void
  onKeyDown?: (evt: React.KeyboardEvent<HTMLInputElement>) => void
  onKeyUp?: (evt: React.KeyboardEvent<HTMLInputElement>) => void
  placeholder?: string | React.ReactNode
  spinner?: () => React.ReactNode
  stripDisplayName?: boolean
  style?: React.CSSProperties
}

export function EmailListInput(props: IReactMultiEmailProps) {
  const {
    id,
    style,
    placeholder,
    autoFocus,
    allowDisplayName = false,
    stripDisplayName = false,
    allowDuplicate = false,
    delimiter = `[${allowDisplayName ? '' : ' '}\,\;]`,
    initialInputValue = '',
    inputName,
    inputClassName,
    autoComplete,
    enable,
    onDisabled,
    onChange,
    onChangeInput,
    onFocus,
    onBlur,
    onKeyDown,
    onKeyUp,
    disableOnBlurValidation = false,
    inputValue,
  } = props
  const emailInputRef = React.useRef<HTMLInputElement>(null)

  const [focused, setFocused] = React.useState(false)
  const [emails, setEmails] = React.useState<string[]>([])
  const [inpValue, setInpValue] = React.useState('')

  const findEmailAddress = React.useCallback(
    async (value: string, isEnter?: boolean) => {
      const validEmails: string[] = []
      let _inputValue = ''
      const re = new RegExp(delimiter, 'g')

      const addEmails = (email: string) => {
        if (!allowDuplicate) {
          for (let i = 0, l = emails.length; i < l; i++) {
            if (emails[i].toLowerCase() === email.toLowerCase()) {
              return false
            }
          }
        }
        validEmails.push(email)
        return true
      }

      if (value !== '') {
        if (re.test(value)) {
          const setArr = new Set(value.split(re).filter((n) => n))

          const arr = [...setArr]
          while (arr.length) {
            const validateResult = isEmail('' + arr[0].trim())
            if (validateResult) {
              addEmails('' + arr.shift()?.trim())
            } else {
              if (allowDisplayName) {
                const validateResultWithDisplayName = isEmail(
                  '' + arr[0].trim(),
                  { allowDisplayName }
                )
                if (validateResultWithDisplayName) {
                  // Strip display name from email formatted as such "First Last <<EMAIL>>"
                  const email = stripDisplayName
                    ? arr.shift()?.split('<')[1].split('>')[0]
                    : arr.shift()
                  addEmails('' + email)
                } else {
                  if (arr.length === 1) {
                    _inputValue = '' + arr.shift()
                  } else {
                    arr.shift()
                  }
                }
              } else {
                _inputValue = '' + arr.shift()
              }
            }
          }
        } else {
          if (enable && !enable({ emailCnt: emails.length })) {
            onDisabled?.()
            return
          }

          if (isEnter) {
            const validateResult = isEmail(value)
            if (typeof validateResult === 'boolean') {
              if (validateResult) {
                addEmails(value)
              } else if (allowDisplayName) {
                const validateResultWithDisplayName = isEmail(value, {
                  allowDisplayName,
                })
                if (validateResultWithDisplayName) {
                  // Strip display name from email formatted as such "First Last <<EMAIL>>"
                  const email = stripDisplayName
                    ? value.split('<')[1].split('>')[0]
                    : value
                  addEmails(email)
                } else {
                  _inputValue = value
                }
              } else {
                _inputValue = value
              }
            } else {
              _inputValue = value
            }
          } else {
            _inputValue = value
          }
        }
      }

      setEmails([...emails, ...validEmails])
      setInpValue(_inputValue)

      if (validEmails.length) {
        onChange?.([...emails, ...validEmails])
      }

      if (inputValue !== _inputValue) {
        onChangeInput?.(_inputValue)
      }
    },
    [
      allowDisplayName,
      allowDuplicate,
      delimiter,
      emails,
      enable,
      inputValue,
      onChange,
      onChangeInput,
      onDisabled,
      stripDisplayName,
    ]
  )

  const onChangeInputValue = React.useCallback(
    async (value: string) => {
      await findEmailAddress(value)
    },
    [findEmailAddress]
  )

  const removeEmail = React.useCallback(
    (index: number, isDisabled?: boolean) => {
      if (isDisabled) {
        return
      }

      const _emails = [...emails.slice(0, index), ...emails.slice(index + 1)]
      setEmails(_emails)
      onChange?.(_emails)
    },
    [emails, onChange]
  )

  const handleOnKeydown = React.useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      onKeyDown?.(e)

      switch (e.key) {
        case 'Enter':
          e.preventDefault()
          break
        case 'Backspace':
          if (!e.currentTarget.value) {
            removeEmail(emails.length - 1, false)
          }
          break
        default:
      }
    },
    [emails.length, onKeyDown, removeEmail]
  )

  const handleOnKeyup = React.useCallback(
    async (e: React.KeyboardEvent<HTMLInputElement>) => {
      onKeyUp?.(e)

      switch (e.key) {
        case 'Enter':
          await findEmailAddress(e.currentTarget.value, true)
          break
        default:
      }
    },
    [findEmailAddress, onKeyUp]
  )

  const handleOnChange = React.useCallback(
    async (e: React.SyntheticEvent<HTMLInputElement>) =>
      await onChangeInputValue(e.currentTarget.value),
    [onChangeInputValue]
  )

  const handleOnBlur = React.useCallback(
    async (e: React.SyntheticEvent<HTMLInputElement>) => {
      setFocused(false)
      if (!disableOnBlurValidation) {
        await findEmailAddress(e.currentTarget.value, true)
      }
      onBlur?.()
    },
    [disableOnBlurValidation, findEmailAddress, onBlur]
  )

  const handleOnFocus = React.useCallback(() => {
    setFocused(true)
    onFocus?.()
  }, [onFocus])

  React.useEffect(() => {
    if (autoFocus) {
      emailInputRef.current?.focus()
    }
  }, [autoFocus])

  React.useEffect(() => {
    setInpValue(initialInputValue)
  }, [initialInputValue])

  React.useEffect(() => {
    setInpValue(inputValue ?? '')
  }, [inputValue])

  React.useEffect(() => {
    const validEmails = props.emails?.filter((email) => {
      return isEmail(email, { allowDisplayName })
    })
    setEmails(validEmails ?? [])
  }, [allowDisplayName, props.emails])

  const focusStateCSS = focused
    ? {
        display: 'flex',
        borderColor: theme.palette.blue[50],
      }
    : {
        display: 'flex',
      }
  const isEmpty = inpValue === '' && emails.length === 0
  const emptyFullCSS = isEmpty
    ? {
        display: 'inline',
        color: '#ccc',
      }
    : {
        display: 'none',
      }

  return (
    <div
      style={style}
      onClick={() => emailInputRef.current?.focus()}
      css={{
        margin: '0',
        maxWidth: '100%',
        flex: '1 0 auto',
        outline: '0',
        WebkitTapHighlightColor: 'rgba(255, 255, 255, 0)',
        textAlign: 'left',
        padding: remSpacing.xsmall,
        background: '#fff',
        border: `1px solid ${theme.palette.gray[30]}`,
        borderRadius: shape.borderRadius.xxsmall,
        transition: `boxShadow 0.1s ease,
          borderColor 0.1s ease`,
        position: 'relative',
        flexWrap: 'wrap',
        alignItems: 'center',
        alignContent: 'flex-start',
        ...focusStateCSS,
      }}
    >
      {placeholder ? (
        <span
          css={{
            position: 'absolute',
            marginLeft: remSpacing.xsmall,
            ...emptyFullCSS,
          }}
        >
          {placeholder}
        </span>
      ) : null}
      <div
        style={{
          display: 'flex',
          flexWrap: 'inherit',
          gap: remSpacing.xsmall,
        }}
      >
        {emails.map((email: string, index: number) => (
          <div
            css={{
              lineHeight: 1.4,
              backgroundColor: theme.palette.gray[10],
              backgroundImage: 'none',
              padding: `2px ${remSpacing.xsmall}`,
              fontSize: rem(14),
              color: theme.palette.gray[70],
              textTransform: 'none',
              border: '0 solid transparent',
              borderRadius: shape.borderRadius.small,
              transition: 'background 0.1s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
              maxWidth: '100%',
              boxShadow: lightShadows[1],
            }}
            key={email}
          >
            <span>{email}</span>
            <button
              aria-label={`Remove ${email}`}
              css={{
                display: 'flex',
                fontSize: rem(18),
                padding: 0,
                color: theme.palette.gray[60],
                marginLeft: remSpacing.xxsmall,
                background: 'none',
                border: 'none',
                cursor: 'pointer',
              }}
              onClick={() => removeEmail(index)}
            >
              <Close size="xxs" />
            </button>
          </div>
        ))}
      </div>
      <input
        name={inputName}
        id={id}
        ref={emailInputRef}
        type="text"
        value={inpValue}
        onFocus={handleOnFocus}
        onBlur={handleOnBlur}
        onChange={handleOnChange}
        onKeyDown={handleOnKeydown}
        onKeyUp={handleOnKeyup}
        className={inputClassName}
        autoComplete={autoComplete}
        css={{
          flex: 1,
          outline: 'none',
          border: '0 none',
          marginLeft: isEmpty ? 0 : remSpacing.xxsmall,
          lineHeight: 1.6,
        }}
      />

      {emails.length > 0 && (
        <Button
          buttonType="icon"
          appearance="link"
          css={{
            background: 'none',
            border: 'none',
            padding: 0,
            color: theme.palette.gray[60],
          }}
          aria-label="Clear all emails"
          onClick={() => setEmails([])}
        >
          <Close size="xs" />
        </Button>
      )}
    </div>
  )
}

interface IFqdnOptions {
  allowTrailingDot?: boolean
  allowUnderscores?: boolean
  requireTld?: boolean
}

interface IEmailOptions {
  allowDisplayName?: boolean
  allowUtf8LocalPart?: boolean
  requireDisplayName?: boolean
  requireTld?: boolean
}

const defaultFqdnOptions = {
  requireTld: true,
  allowUnderscores: false,
  allowTrailingDot: false,
}

const defaultEmailOptions = {
  allowDisplayName: false,
  requireDisplayName: false,
  allowUtf8LocalPart: true,
  requireTld: true,
}

const displayName =
  /^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\.\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\,\.\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF\s]*<(.+)>$/i
const emailUserPart = /^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i
const quotedEmailUser =
  /^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i
const emailUserUtf8Part =
  /^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i
const quotedEmailUserUtf8 =
  /^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i

function isByteLength(str: string, options: { min?: number; max?: number }) {
  let min: number = 0
  let max: number | undefined
  const len = encodeURI(str).split(/%..|./).length - 1

  if (options) {
    min = Number(options.min) || 0
    max = Number(options.max)
  }

  return len >= min && (typeof max === 'undefined' || len <= max)
}

function isFQDN(str: string, options?: IFqdnOptions) {
  options = { ...defaultFqdnOptions, ...options }

  /* Remove the optional trailing dot before checking validity */
  if (options.allowTrailingDot && str[str.length - 1] === '.') {
    str = str.substring(0, str.length - 1)
  }
  const parts = str.split('.')

  if (options.requireTld) {
    const tld: string = '' + parts.pop()
    if (
      !parts.length ||
      !/^([a-z\u00a1-\uffff]{2,}|xn[a-z0-9-]{2,})$/i.test(tld)
    ) {
      return false
    }
    // disallow spaces
    if (/[\s\u2002-\u200B\u202F\u205F\u3000\uFEFF\uDB40\uDC20]/.test(tld)) {
      return false
    }
  }

  for (let part, i = 0; i < parts.length; i++) {
    part = parts[i]
    if (options.allowUnderscores) {
      part = part.replace(/_/g, '')
    }
    if (!/^[a-z\u00a1-\uffff0-9-]+$/i.test(part)) {
      return false
    }
    // disallow full-width chars
    if (/[\uff01-\uff5e]/.test(part)) {
      return false
    }
    if (part[0] === '-' || part[part.length - 1] === '-') {
      return false
    }
  }
  return true
}

function isEmail(str: string, options?: IEmailOptions) {
  options = { ...defaultEmailOptions, ...options }

  if (options.requireDisplayName || options.allowDisplayName) {
    const displayEmail = str.match(displayName)
    if (displayEmail) {
      str = displayEmail[1]
    } else if (options.requireDisplayName) {
      return false
    }
  }

  const parts: string[] = str.split('@')
  const domain: string = '' + parts.pop()
  const lowerDomain = domain.toLowerCase()
  let user: string = parts.join('@')

  if (lowerDomain === 'gmail.com' || lowerDomain === 'googlemail.com') {
    user = user.replace(/\./g, '').toLowerCase()
  }

  if (!isByteLength(user, { max: 64 }) || !isByteLength(domain, { max: 254 })) {
    return false
  }

  if (!isFQDN(domain, { requireTld: options.requireTld })) {
    return false
  }

  if (user[0] === '"') {
    user = user.slice(1, user.length - 1)
    return options.allowUtf8LocalPart
      ? quotedEmailUserUtf8.test(user)
      : quotedEmailUser.test(user)
  }

  const pattern = options.allowUtf8LocalPart ? emailUserUtf8Part : emailUserPart
  const userParts = user.split('.')

  for (let i = 0; i < userParts.length; i++) {
    if (!pattern.test(userParts[i])) {
      return false
    }
  }

  return true
}
