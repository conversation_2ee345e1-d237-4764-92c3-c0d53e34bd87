import { useCallback, useState } from 'react'

import styled from '@emotion/styled'
import { rem } from 'polished'

import {
  Blade,
  Button,
  Caps2Bold,
  Caps3Bold,
  ChildrenProps,
  Close,
  FlexContainer,
  FlexItem,
  H5,
  Key,
  mediaQueries,
  P3,
  P4,
  remSpacing,
  shape,
  theme,
  ZIndex,
} from '@apella/component-library'
import { useLocalStorageState } from '@apella/hooks'
import {
  CaseStatusName,
  CaseType,
  RoomStatusName,
  EventType,
} from 'src/__generated__/globalTypes'
import {
  FloatingActionContainer,
  FloatingActionIconButton,
} from 'src/components/FloatingAction'
import { TimelineTooltip } from 'src/components/Timeline/TimelineTooltip'
import { LIVE_EVENTS } from 'src/pages/Live/livePhases'
import { useEventTypes } from 'src/utils/hooks'

import {
  CaseStatusNameStyleConfig,
  formatStatus,
  StatusSuperset,
  TurnoverLengthWithGoal,
  useStatusStyles,
} from '../utils/status'

interface EventItem {
  colorTheme?: StatusSuperset
  description: { start: Description; end?: Description }
  label: StatusSuperset | string
  source: string
}

interface Description {
  dotColor?: string
  eventTypeId?: string
  text: string
}

interface Section {
  items: EventItem[]
  title: string
}

enum KeySource {
  EHR = 'EHR',
  APELLA = 'Apella',
}

const KeyCategories: Section[] = [
  {
    title: 'PLAN',
    items: [
      {
        label: CaseStatusName.SCHEDULED,
        description: { start: { text: 'Scheduled case time' } },
        source: KeySource.EHR,
      },
      {
        label: CaseType.FORECAST,
        description: { start: { text: 'Forecast case time' } },
        source: KeySource.APELLA,
      },
      {
        label: CaseStatusName.COMPLETE,
        description: { start: { text: 'Actual case time' } },
        source: KeySource.APELLA,
      },
    ],
  },
  {
    title: 'PRE SURGERY',
    items: [
      {
        label: CaseStatusName.IN_FACILITY,
        description: { start: { text: 'Patient in facility' } },
        source: KeySource.EHR,
      },
      {
        label: CaseStatusName.PRE_PROCEDURE,
        description: { start: { text: 'Patient in pre-procedure' } },
        source: KeySource.EHR,
      },
      {
        label: CaseStatusName.PRE_PROCEDURE_COMPLETE,
        description: { start: { text: 'Patient pre-procedure complete' } },
        source: KeySource.EHR,
      },
      {
        label: CaseStatusName.IN_HOLD,
        description: { start: { text: 'Case in hold' } },
        source: KeySource.EHR,
      },
    ],
  },
  {
    title: 'SURGERY',
    items: [
      {
        label: CaseStatusName.PREP,
        description: {
          start: {
            text: 'Pt wheeled in',
            eventTypeId: LIVE_EVENTS.PATIENT_WHEELS_IN,
          },
          end: { text: 'Pt draped', eventTypeId: LIVE_EVENTS.PATIENT_DRAPED },
        },
        source: KeySource.APELLA,
      },
      {
        label: CaseStatusName.SURGERY,
        description: {
          start: { text: 'Pt draped', eventTypeId: LIVE_EVENTS.PATIENT_DRAPED },
          end: {
            text: 'Pt undraped',
            eventTypeId: LIVE_EVENTS.PATIENT_UNDRAPED,
          },
        },
        source: KeySource.APELLA,
      },
      {
        label: CaseStatusName.WRAP_UP,
        description: {
          start: {
            text: 'Pt undraped',
            eventTypeId: LIVE_EVENTS.PATIENT_UNDRAPED,
          },
          end: {
            text: 'Pt wheeled out',
            eventTypeId: LIVE_EVENTS.PATIENT_WHEELS_OUT,
          },
        },
        source: KeySource.APELLA,
      },
    ],
  },
  {
    title: 'POST SURGERY',
    items: [
      {
        label: CaseStatusName.RECOVERY,
        description: { start: { text: 'Patient in recovery' } },
        source: KeySource.EHR,
      },
      {
        label: CaseStatusName.PHASE_II,
        description: { start: { text: 'Patient in phase II recovery' } },
        source: KeySource.EHR,
      },
    ],
  },
  {
    title: 'TURNOVER',
    items: [
      {
        colorTheme: RoomStatusName.TURNOVER,
        label: '25m',
        description: {
          start: {
            text: 'Pt wheeled out',
            eventTypeId: LIVE_EVENTS.PATIENT_WHEELS_OUT,
          },
          end: {
            text: 'Pt wheeled in',
            eventTypeId: LIVE_EVENTS.PATIENT_WHEELS_IN,
          },
        },
        source: KeySource.APELLA,
      },
      {
        colorTheme: TurnoverLengthWithGoal.OVER_GOAL,
        label: '50m',
        description: { start: { text: 'Exceeding turnover goal time' } },
        source: KeySource.APELLA,
      },
      {
        colorTheme: CaseStatusName.COMPLETE,
        label: '120m',
        description: { start: { text: 'Non-turnover, idle' } },
        source: KeySource.APELLA,
      },
    ],
  },
]

export const StatusPill = styled.div<CaseStatusNameStyleConfig>`
    border-radius: ${(props) => props.borderRadius};
    background-color: ${(props) => props.backgroundColor};
    border: ${(props) => props.border ?? 'none'}};
`

export const SCHEDULE_KEY_LOCAL_STORAGE_KEY = 'showScheduleKey'

export const ToggleableCaseKey = () => {
  return (
    <FloatingActionContainer>
      <MobileStatusKey />
      <StatusKey />
    </FloatingActionContainer>
  )
}

const Dot = ({ color }: { color: string }) => (
  <span
    style={{
      display: 'inline-block',
      width: remSpacing.small,
      height: remSpacing.small,
      marginRight: remSpacing.xxsmall,
      backgroundColor: color,
      borderRadius: '50%',
      verticalAlign: 'middle',
    }}
  />
)

//Update event name and color based on eventTypeId from eventTypes
export const buildEnhancedKeyCategories = (eventTypes: EventType[]) => {
  const eventTypeMap = eventTypes.reduce(
    (map, eventType) => {
      map[eventType.id] = {
        name: eventType.name ?? '',
        color: eventType.color ?? '',
      }
      return map
    },
    {} as Record<string, { name: string; color: string }>
  )

  const enhancedSections = KeyCategories.map((section) => ({
    ...section,
    items: section.items.map((item) => ({
      ...item,
      description: {
        start:
          item.description.start.eventTypeId &&
          eventTypeMap[item.description.start.eventTypeId]
            ? {
                ...item.description.start,
                text: eventTypeMap[item.description.start.eventTypeId].name,
                dotColor:
                  eventTypeMap[item.description.start.eventTypeId].color,
              }
            : item.description.start,
        end:
          item.description.end?.eventTypeId &&
          eventTypeMap[item.description.end.eventTypeId]
            ? {
                ...item.description.end,
                text: eventTypeMap[item.description.end.eventTypeId].name,
                dotColor: eventTypeMap[item.description.end.eventTypeId].color,
              }
            : item.description.end,
      },
    })),
  }))

  return enhancedSections
}

const CategoryComponent = ({ section }: { section: Section }) => {
  const statusStyles = useStatusStyles({ isKey: true })

  return (
    <div
      css={{
        [mediaQueries.lg]: {
          width: rem('180px'),
        },
        [mediaQueries.xl]: {
          width: rem('220px'),
        },
      }}
      key={section.title}
    >
      <div>
        <Caps2Bold>{section.title}</Caps2Bold>
      </div>
      {section.items.map((item) => (
        <div key={item.label} style={{ marginTop: remSpacing.small }}>
          <FlexContainer alignItems="center" gap={remSpacing.xsmall}>
            <StatusPill
              {...statusStyles(
                item.colorTheme || (item.label as StatusSuperset)
              )}
              style={{
                display: 'flex',
                padding: `0 ${remSpacing.xxsmall}`,
              }}
            >
              <Caps3Bold
                style={{
                  color: statusStyles(
                    item.colorTheme || (item.label as StatusSuperset)
                  ).textColor,
                }}
              >
                {formatStatus(item.label as StatusSuperset)}
              </Caps3Bold>
            </StatusPill>
            <P4 style={{ color: theme.palette.gray[50] }}>{item.source}</P4>
          </FlexContainer>
          <div
            style={{
              display: 'flex',
              flexWrap: 'wrap',
              alignItems: 'center',
              gap: remSpacing.xxsmall,
              marginTop: remSpacing.xxsmall,
            }}
          >
            {item.description.start.dotColor && (
              <Dot color={item.description.start.dotColor} />
            )}
            <P3>{item.description.start.text}</P3>
            {item.description.end?.text && (
              <>
                <span>→</span>
                {item.description.end.dotColor && (
                  <Dot color={item.description.end.dotColor} />
                )}
                <P3>{item.description.end?.text}</P3>
              </>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
const FlexContainerWithGap = styled.div`
  display: flex;
  flex-direction: row;
  gap: 0;
  @media only screen and (min-width: 1152px) {
    gap: ${remSpacing.gutter};
  }
`

const ResponsiveKeyContainer = ({
  onClose,
  children,
}: { onClose: () => void } & ChildrenProps) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        background: theme.palette.background.primary,
        boxShadow:
          '0px 3px 5px rgba(0, 0, 0, 0.1), 0px 1px 18px rgba(0, 0, 0, 0.06), 0px 6px 10px rgba(0, 0, 0, 0.08)',
        padding: `${rem('20px')} ${remSpacing.gutter}`,
        border: `1px solid ${theme.palette.gray[30]}`,
        borderRadius: shape.borderRadius.xsmall,
        gap: remSpacing.medium,
      }}
    >
      <FlexContainer
        justifyContent="space-between"
        alignItems="center"
        style={{ height: remSpacing.gutter }}
      >
        <FlexContainer alignItems="center" gap={remSpacing.xsmall}>
          <Key color={theme.palette.gray[50]} size="sm" />
          <H5 style={{ color: theme.palette.gray[50] }}>KEY</H5>
        </FlexContainer>
        <TimelineTooltip content="Close Key" placement="top">
          <Button
            appearance="link"
            buttonType="icon"
            color="black"
            aria-label="schedule-key-close"
            onClick={onClose}
          >
            <Close size="md" color={theme.palette.gray[40]} cursor="pointer" />
          </Button>
        </TimelineTooltip>
      </FlexContainer>
      <FlexContainerWithGap>{children}</FlexContainerWithGap>
    </div>
  )
}

const MobileStatusKey = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false)

  const onClose = useCallback(() => setIsOpen(false), [setIsOpen])
  const onToggle = useCallback(() => setIsOpen((prev) => !prev), [setIsOpen])
  const { eventTypes } = useEventTypes()
  const enhancedSections = buildEnhancedKeyCategories(eventTypes)
  return (
    <div
      css={{
        [mediaQueries.lg]: { display: 'none' },
      }}
    >
      {!isOpen && (
        <FloatingActionIconButton
          tooltipContent="Show Key"
          onClick={onToggle}
          Icon={Key}
        />
      )}
      <Blade
        isOpen={isOpen}
        onClose={onClose}
        side={'bottom'}
        isMobileOnly
        size={'xs'}
      >
        <FlexContainer
          style={{
            flexDirection: 'column',
            overflowY: 'scroll',
            padding: `${rem('20px')} ${remSpacing.gutter}`,
          }}
        >
          <FlexItem grow>
            <Blade.Header
              style={{
                zIndex: ZIndex.ABOVE,
                borderBottom: '0px solid',
                padding: 0,
              }}
            >
              <Blade.Title>
                <FlexContainer alignItems="center" gap={remSpacing.xsmall}>
                  <Key color={theme.palette.gray[50]} size="sm" />
                  <H5 style={{ color: theme.palette.gray[50] }}>KEY</H5>
                </FlexContainer>
              </Blade.Title>
              <Blade.CloseButton onClose={onClose} />
            </Blade.Header>
            <FlexContainer
              gap={remSpacing.gutter}
              style={{
                flexDirection: 'column',
                zIndex: ZIndex.ABOVE,
                marginTop: remSpacing.xsmall,
              }}
            >
              {enhancedSections.map((section) => (
                <CategoryComponent key={section.title} section={section} />
              ))}
            </FlexContainer>
          </FlexItem>
        </FlexContainer>
      </Blade>
    </div>
  )
}

const StatusKey = () => {
  const [isOpen, setIsOpen] = useLocalStorageState<boolean>(
    SCHEDULE_KEY_LOCAL_STORAGE_KEY,
    false
  )
  const { eventTypes } = useEventTypes()
  const onClose = useCallback(() => setIsOpen(false), [setIsOpen])
  const onToggle = useCallback(() => setIsOpen((prev) => !prev), [setIsOpen])
  const enhancedSections = buildEnhancedKeyCategories(eventTypes)
  return (
    <div
      css={{
        display: 'none',
        [mediaQueries.lg]: {
          display: 'flex',
        },
      }}
    >
      {isOpen ? (
        <ResponsiveKeyContainer onClose={onClose}>
          {enhancedSections.map((section) => (
            <CategoryComponent key={section.title} section={section} />
          ))}
        </ResponsiveKeyContainer>
      ) : (
        <FloatingActionIconButton
          tooltipContent="Show Key"
          onClick={onToggle}
          Icon={Key}
        />
      )}
    </div>
  )
}
