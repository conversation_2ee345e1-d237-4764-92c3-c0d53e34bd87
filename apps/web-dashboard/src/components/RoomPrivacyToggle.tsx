import { ComponentProps } from 'react'

import { Interpolation, Theme } from '@emotion/react'

import {
  Button,
  Progress,
  Span3,
  Tooltip,
  Visibility,
  VisibilityOff,
  pxSpacing,
} from '@apella/component-library'
import { UpdateRoomPrivacy } from 'src/hooks/__generated__'
import { useRoomUpdate } from 'src/hooks/useRoomUpdate'
import { AuthComponent } from 'src/modules/user/components/AuthComponent'

type RoomPrivacyToggleSize = 'sm' | 'lg'

type RoomPrivacyToggleStyles = {
  [key in RoomPrivacyToggleSize]: {
    buttonProps: React.ComponentProps<typeof Button> & {
      css?: Interpolation<Theme>
    }
    svgProps:
      | ComponentProps<typeof Visibility>
      | ComponentProps<typeof Progress>
  }
}

const RoomPrivacyToggleStyling: RoomPrivacyToggleStyles = {
  lg: {
    buttonProps: {
      color: 'alternate',
      buttonType: 'icon',
      size: 'lg',
      css: { height: 56, width: 56, borderRadius: pxSpacing.small },
    },
    svgProps: {
      size: 'md',
    },
  },
  sm: {
    buttonProps: {
      appearance: 'link',
      buttonType: 'icon',
      color: 'alternate',
      size: 'sm',
      css: { padding: 0 },
    },
    svgProps: {
      size: 'sm',
    },
  },
} as const

export const RoomPrivacyToggle = ({
  size = 'lg',
  isPrivacyEnabled,
  roomId,
  onMutationCompleted,
}: {
  size?: RoomPrivacyToggleSize
  isPrivacyEnabled: boolean
  onMutationCompleted?: (data: UpdateRoomPrivacy) => void
  roomId: string
}) => {
  const { toggleRoomPrivacy, loading } = useRoomUpdate({
    roomId,
    onMutationCompleted,
    isPrivacyEnabled,
  })

  const stylingProps = RoomPrivacyToggleStyling[size]

  if (!toggleRoomPrivacy) {
    return null
  }

  const VisibilityIcon = isPrivacyEnabled ? VisibilityOff : Visibility

  return (
    <AuthComponent
      permission="roomWriteConfigurationEnabled"
      hideInProductionForApella
      flag="toggleRoomPrivacy"
    >
      <Tooltip
        body={<Span3>Turn privacy {isPrivacyEnabled ? 'off' : 'on'}</Span3>}
        placement="bottom"
      >
        <Button
          onClick={toggleRoomPrivacy}
          {...stylingProps.buttonProps}
          disabled={loading}
        >
          {loading ? (
            <Progress
              size={
                (stylingProps.svgProps as ComponentProps<typeof Progress>).size
              }
            />
          ) : (
            <VisibilityIcon {...stylingProps.svgProps} />
          )}
        </Button>
      </Tooltip>
    </AuthComponent>
  )
}
