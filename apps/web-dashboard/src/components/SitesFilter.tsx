import { ComponentProps } from 'react'

import { Col, MultiSelect, SingleSelect } from '@apella/component-library'
import {
  MultiFilterWithCount,
  SingleFilterWithCount,
} from 'src/components/Filters/FilterWithCount'

export interface SiteShape {
  node: {
    id: string
    name: string
  }
}

type SitesFilterProps =
  | {
      // Single Site selection
      sites: SiteShape[]
      selectedSiteIds: ComponentProps<typeof SingleSelect>['value']
      onChangeSites: ComponentProps<typeof SingleSelect>['onChange']
      multipleSites?: false
      label?: string
      bulkSelectSites?: false
      disableSelectAllOption?: boolean
      disableSearch?: boolean
    }
  | {
      // Multiple Site selection
      sites: SiteShape[]
      selectedSiteIds: ComponentProps<typeof MultiSelect>['value']
      onChangeSites: ComponentProps<typeof MultiSelect>['onChange']
      multipleSites: true
      label?: string
      bulkSelectSites?: ComponentProps<typeof MultiSelect>['bulkSelect']
      disableSelectAllOption?: boolean
      disableSearch?: boolean
    }

export const SitesFilter = ({
  sites,
  selectedSiteIds,
  onChangeSites,
  multipleSites,
  label,
  bulkSelectSites,
  disableSelectAllOption,
  disableSearch = false,
}: SitesFilterProps): React.JSX.Element => {
  const siteFilter = multipleSites ? (
    <MultiFilterWithCount
      items={sites}
      label={label ? label : 'Select site(s)'}
      selectedIds={selectedSiteIds}
      onChange={(siteIds: ComponentProps<typeof MultiSelect>['value']) => {
        if (onChangeSites) {
          onChangeSites(siteIds)
        }
      }}
      bulkSelect={bulkSelectSites}
      search={!disableSearch}
    />
  ) : (
    <SingleFilterWithCount
      items={sites}
      value={selectedSiteIds}
      onChange={(siteIds: ComponentProps<typeof SingleSelect>['value']) => {
        if (onChangeSites) {
          onChangeSites(siteIds)
        }
      }}
      label={label ? label : 'All sites'}
      disableSelectAllOption={disableSelectAllOption}
      search={!disableSearch}
    />
  )
  return <>{sites.length >= 1 ? <Col>{siteFilter}</Col> : null}</>
}
