import { memo, ReactElement } from 'react'

import { RangeSlider, RangeSliderProps } from '@apella/component-library'
import { DropdownFilter } from 'src/components/DropdownSlider/DropdownFilter'

export interface DropdownRangeSliderProps extends RangeSliderProps {
  buttonIcon?: ReactElement
  dropdownLabel: string
  label: string
}

export const DropdownRangeSlider = memo(function DropdownRangeSlider({
  onChange,
  value,
  label,
  dropdownLabel,
  buttonIcon,
  min,
  max,
  marks,
  step,
  showInvertButton,
  isInverted,
  onChangeInverted,
}: DropdownRangeSliderProps): React.JSX.Element {
  return (
    <DropdownFilter
      dropdownLabel={dropdownLabel}
      icon={buttonIcon}
      label={label}
    >
      <RangeSlider
        min={min}
        max={max}
        step={step}
        value={value}
        marks={marks}
        onChange={onChange}
        showInvertButton={showInvertButton}
        isInverted={isInverted}
        onChangeInverted={onChangeInverted}
      />
    </DropdownFilter>
  )
})
