import { KeyboardEvent, memo, ReactElement, useCallback, useState } from 'react'

import { useTheme } from '@emotion/react'

import { CODE_ESCAPE, CODE_RETURN } from 'keycode-js'

import {
  ArrowDropDown,
  Button,
  Caps2,
  Dropdown,
  DropdownWrapper,
  FlexContainer,
  FlexItem,
  remSpacing,
  Span2,
} from '@apella/component-library'

export interface DropdownFilterProps {
  children: ReactElement
  dropdownLabel: string
  icon?: ReactElement
  label: string
}

export const DropdownFilter = memo(function BaseDropdownSlider({
  label,
  icon,
  children,
  dropdownLabel,
}: DropdownFilterProps): React.JSX.Element {
  const theme = useTheme()
  const [isOpen, setIsOpen] = useState(false)

  const onClose = useCallback(() => {
    setIsOpen(false)
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur()
    }
  }, [setIsOpen])

  const onClick = useCallback(() => {
    setIsOpen((isOpen) => !isOpen)
  }, [])

  const keyDownHandler = useCallback(
    (event: KeyboardEvent) => {
      if (!isOpen) return

      if (event.code === CODE_ESCAPE || event.code === CODE_RETURN) {
        onClose()
      }

      event.stopPropagation()
    },
    [isOpen, onClose]
  )

  return (
    <DropdownWrapper
      display="inline"
      onKeyDown={keyDownHandler}
      onClose={onClose}
      open={isOpen}
    >
      <Button type="button" color="alternate" onClick={onClick}>
        {icon}
        <Span2>{dropdownLabel}</Span2>
        <ArrowDropDown color={theme.palette.gray[50]} size="sm" />
      </Button>
      <Dropdown open={isOpen} below above={false} right left={false}>
        <FlexContainer
          direction={'column'}
          css={{
            margin: `${remSpacing.xsmall} ${remSpacing.gutter} ${remSpacing.gutter}`,
          }}
        >
          <FlexItem css={{ marginBottom: remSpacing.xsmall }}>
            <Caps2>{label}</Caps2>
          </FlexItem>
          <FlexItem>{children}</FlexItem>
        </FlexContainer>
      </Dropdown>
    </DropdownWrapper>
  )
})
