import { H3 } from '@apella/component-library'

import { DropdownSlider } from './DropdownSlider'

import './DropdownSlider.css'
import 'rc-slider/assets/index.css'

export default {
  title: 'Components/DropdownSlider',
  component: DropdownSlider,
}

export const Showcase = (): React.JSX.Element => {
  const onChangeHandler = (newValue: number) => console.log(newValue)
  const value = 100
  const marks = { [value]: value.toString() }

  return (
    <>
      <div style={{ margin: '24px' }}>
        <H3>DropdownSlider</H3>
        <DropdownSlider
          label={'Label'}
          dropdownLabel={value.toString()}
          marks={marks}
          value={value}
          max={200}
          step={10}
          onChange={onChangeHandler}
        />
      </div>
    </>
  )
}
