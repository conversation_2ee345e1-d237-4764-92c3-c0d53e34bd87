import { ReactElement, memo } from 'react'

import { Slider, SliderProps } from '@apella/component-library'
import { DropdownFilter } from 'src/components/DropdownSlider/DropdownFilter'

export interface DropdownSliderProps extends SliderProps {
  buttonIcon?: ReactElement
  dropdownLabel: string
  label: string
}

export const DropdownSlider = memo(function DropdownSlider({
  onChange,
  value,
  label,
  dropdownLabel,
  marks,
  buttonIcon,
  max,
  step,
}: DropdownSliderProps): React.JSX.Element {
  return (
    <DropdownFilter
      dropdownLabel={dropdownLabel}
      icon={buttonIcon}
      label={label}
    >
      <Slider
        max={max}
        step={step}
        value={value}
        marks={marks}
        onChange={onChange}
      />
    </DropdownFilter>
  )
})
