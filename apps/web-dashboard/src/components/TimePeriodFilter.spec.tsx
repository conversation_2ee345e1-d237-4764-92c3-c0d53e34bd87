import userEvent from '@testing-library/user-event'
import { DateTime } from 'luxon'
import { render, screen } from 'test-utils'

import { TimePeriodFilter } from './TimePeriodFilter'

describe('Time Period Filter', () => {
  beforeEach(async () => {
    const timePeriod = {
      min: DateTime.fromObject({ hour: 7, minute: 30 }).toISOTime(),
      max: DateTime.fromObject({ hour: 17, minute: 0 }).toISOTime(),
    }
    render(
      <TimePeriodFilter
        timePeriod={timePeriod}
        onChangeTimePeriod={() => null}
      />
    )

    // open slider
    await userEvent.click(screen.getByRole('button'))
  })

  it('displays correct time period', () => {
    expect(screen.getByRole('button')).toHaveTextContent('07:30 - 17:00')

    expect(screen.getByText('07:30')).toBeVisible()
    expect(screen.getByText('17:00')).toBeVisible()
  })

  it('has correct bounds', () => {
    // There should be two sliders: min and max. Both will have the valuemin and valuemax set.
    expect(screen.getAllByRole('slider').length).toEqual(2)
    expect(screen.getAllByRole('slider')[0]).toHaveAttribute(
      'aria-valuemin',
      DateTime.fromObject({ hour: 0 }).toSeconds().toString()
    )
    expect(screen.getAllByRole('slider')[0]).toHaveAttribute(
      'aria-valuemax',
      DateTime.fromObject({
        hour: 23,
        minute: 59,
        second: 59,
      })
        .toSeconds()
        .toString()
    )
  })
})
