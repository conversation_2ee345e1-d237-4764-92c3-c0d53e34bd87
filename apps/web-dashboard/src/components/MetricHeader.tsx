import { useTheme } from '@emotion/react'

import {
  Button,
  Caps2,
  Caps3,
  Caps3Bold,
  FlexContainer,
  H4,
  remSpacing,
} from '@apella/component-library'
import { EM_DASH } from 'src/utils/htmlCodes'

import { MetricTile } from './MetricTile'

interface Metric {
  label: string
  subLink?: { label: string; onClick?: () => void; disabled?: boolean }
  subMetric?: Metric
  value?: number | string | React.JSX.Element
}

export const MetricHeader = ({ metrics }: { metrics: Metric[] }) => {
  const theme = useTheme()

  return (
    <FlexContainer
      gap={remSpacing.medium}
      css={{
        overflowX: 'auto',
      }}
      wrap="nowrap"
    >
      {metrics.map((m) => (
        <MetricTile
          key={m.label}
          css={{
            padding: `${remSpacing.gutter} ${remSpacing.small} ${!!m.subMetric ? remSpacing.small : remSpacing.gutter}`,
            justifyContent: 'center',
          }}
        >
          <H4>{m.value ?? EM_DASH}</H4>
          <Caps2>{m.label}</Caps2>
          {m.subMetric && (
            <div css={{ color: theme.palette.text.secondary }}>
              <Caps3Bold>{m.subMetric.value ?? EM_DASH} </Caps3Bold>
              <Caps3>{m.subMetric.label}</Caps3>
            </div>
          )}

          {m.subLink && !!m.subLink.label && (
            <Button
              appearance={'link'}
              size={'sm'}
              onClick={m.subLink.onClick}
              disabled={m.subLink.disabled}
            >
              <Caps3>{m.subLink.label}</Caps3>
            </Button>
          )}
        </MetricTile>
      ))}
    </FlexContainer>
  )
}
