import { memo, useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { gql, useQuery } from '@apollo/client'
import { DateTime, Duration } from 'luxon'
import {
  Area,
  Bar,
  ComposedChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from 'recharts'

import { Progress, remSpacing, shape } from '@apella/component-library'
import { onClickContactSupport } from 'src/utils/toggleBeacon'
import { usePolling } from 'src/utils/usePolling'

import { SLOW_POLL_INTERVAL_MS } from '../pages/Live/consts'
import { useCurrentMinute } from '../utils/useCurrentTime'
import {
  GetOccupancyBuckets,
  GetOccupancyBucketsVariables,
} from './__generated__'

interface OccupancySliderProps {
  endTime?: DateTime
  roomId: string
  startTime?: DateTime
}

const GET_OCCUPANCY_BUCKETS = gql`
  query GetOccupancyBuckets(
    $roomId: String!
    $minTime: DateTime!
    $maxTime: DateTime!
  ) {
    objectMetrics {
      countOccupancyAndOutagePerBucket(
        query: { minTime: $minTime, maxTime: $maxTime, roomId: $roomId }
      ) {
        date
        bucketSize
        metrics {
          metric
          value
        }
      }
    }
  }
`

export const GRAPH_HEIGHT = 50

const OccupancySlider = memo(function OccupancySlider({
  startTime,
  endTime,
  roomId,
}: OccupancySliderProps): React.JSX.Element {
  const theme = useTheme()
  const currentMinute = useCurrentMinute()

  const { data, loading, error, startPolling, stopPolling, refetch } = useQuery<
    GetOccupancyBuckets,
    GetOccupancyBucketsVariables
  >(GET_OCCUPANCY_BUCKETS, {
    variables: {
      minTime: startTime!.toISO(),
      maxTime: endTime!.toISO(),
      roomId,
    },
    skip: !startTime || !endTime || !roomId,
    fetchPolicy: 'no-cache',
  })

  usePolling({
    refetch,
    startPolling,
    stopPolling,
    interval: SLOW_POLL_INTERVAL_MS,
    skip: !startTime || !endTime,
  })

  const chartData = useMemo(() => {
    return data?.objectMetrics?.countOccupancyAndOutagePerBucket?.map(
      (bucket) => {
        const date =
          DateTime.fromISO(bucket.date).toSeconds() +
          Duration.fromISO(bucket.bucketSize).as('seconds') / 2
        const isInFuture =
          DateTime.fromISO(bucket.date).plus(
            Duration.fromISO(bucket.bucketSize)
          ) > currentMinute

        const occupancyValue = bucket?.metrics
          .find((metric) => metric?.metric === 'occupancy')
          ?.value.toFixed(2)
        const mediaAvailabilityValue =
          bucket?.metrics.find(
            (metric) => metric?.metric === 'media_availability'
          )?.value ?? 0

        const occupancy = isInFuture ? 0 : occupancyValue
        const missingObservations = isInFuture
          ? 0
          : (1 - mediaAvailabilityValue).toFixed(2)

        return {
          date,
          occupancy,
          missing_observations: missingObservations,
        }
      }
    )
  }, [data, currentMinute])

  return (
    // There's a bug in recharts where the ResponsiveContainer does not shrink to a flexbox size
    // correctly without this position: absolute hack.
    // https://github.com/recharts/recharts/issues/172
    <div
      css={{
        position: 'relative',
        height: GRAPH_HEIGHT,
      }}
    >
      {loading ? (
        <Progress size="sm" />
      ) : (
        <ResponsiveContainer width={'100%'} height={GRAPH_HEIGHT}>
          {error ? (
            <div style={{ textAlign: 'center', paddingTop: remSpacing.large }}>
              <span style={{ cursor: 'auto' }}>
                Error loading the occupancy graph, try reloading or
              </span>
              <a
                onClick={onClickContactSupport}
                style={{ color: theme.palette.blue[50] }}
              >
                {' '}
                contact support
              </a>
            </div>
          ) : (
            <ComposedChart data={chartData} margin={{ left: 0, right: 0 }}>
              <XAxis
                dataKey="date"
                type="number"
                interval={0}
                domain={[startTime!.toSeconds(), endTime!.toSeconds()]}
                tick={false}
                offset={0}
                hide={true}
                allowDataOverflow={true}
              />
              <YAxis
                yAxisId="occupancy"
                type="number"
                domain={[0, 6]}
                offset={0}
                hide={true}
                scale="sqrt"
                allowDataOverflow={true}
              />
              <Bar
                barSize={5}
                yAxisId="occupancy"
                dataKey="occupancy"
                fill={theme.palette.blue[50]}
                isAnimationActive={true}
                radius={shape.borderRadiusPx.xxsmall}
              />
              <YAxis
                yAxisId="missing_observations"
                type="number"
                domain={[0.2, 1]}
                offset={0}
                allowDataOverflow={true}
                hide={true}
              />
              <Area
                name="missing observations"
                dataKey="missing_observations"
                yAxisId="missing_observations"
                type="step"
                fill={theme.palette.red[40]}
                stroke={theme.palette.red[60]}
                isAnimationActive={true}
              />
            </ComposedChart>
          )}
        </ResponsiveContainer>
      )}
    </div>
  )
})

export default OccupancySlider
