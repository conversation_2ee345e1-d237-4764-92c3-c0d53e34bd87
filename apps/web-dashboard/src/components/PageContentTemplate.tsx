import { ComponentProps, ReactNode, useState } from 'react'

import { rem } from 'polished'

import {
  ChildrenProps,
  FlexContainer,
  mediaQueries,
  remSpacing,
} from '@apella/component-library'
import { PageHeader } from 'src/components/PageHeader'
import { USER_VIEW_ID } from 'src/components/UserViews/useUserFilterViews'
import { useDisplayMode } from 'src/Contexts'

import { UserParamContext } from './UserParamContext'

export interface FilterBarPageTemplateProps {
  children: ReactNode
}

export const FilterBarContainer = ({ children }: ChildrenProps) => {
  return (
    <FlexContainer
      css={{
        position: 'relative',
        width: '100%',
        [mediaQueries.lg]: {
          justifyContent: 'start',
        },
      }}
      wrap="wrap"
      gap={remSpacing.gutter}
    >
      {children}
    </FlexContainer>
  )
}

export const maxContainerSizeLookup = {
  form: rem('480px'),
  default: rem('1920px'),
  largeForm: rem('880px'),
  full: '100%',
} as const

export const PageContentTemplate = ({
  children,
  filters,
  maxContainerSize = 'default',
  ...pageHeader
}: ChildrenProps &
  ComponentProps<typeof PageHeader> & {
    filters?: ChildrenProps['children']
    maxContainerSize?: keyof typeof maxContainerSizeLookup
  }): React.JSX.Element => {
  const { isTvModeEnabled: isFullscreen } = useDisplayMode()

  const [paramAllowList, updateParamAllowList] = useState<Set<string>>(
    new Set()
  )

  return (
    <UserParamContext.Provider
      value={{
        paramAllowList,
        updateParamAllowList: (newList: string[]) =>
          updateParamAllowList(new Set([USER_VIEW_ID, ...newList])),
      }}
    >
      <div
        css={{
          display: 'grid',
          width: '100%',
          padding: `0 ${remSpacing.medium} ${remSpacing.medium} ${remSpacing.medium}`,
          [mediaQueries.lg]: {
            padding: `0 ${remSpacing.gutter} ${remSpacing.gutter} ${remSpacing.gutter}`,
          },
        }}
        style={isFullscreen ? { padding: `0 ${remSpacing.gutter}` } : {}}
      >
        <div
          css={{
            display: 'grid',
            width: '100%',
            gridTemplateColumns: 'minmax(0, 1fr)',
            justifySelf: 'center',
            gap: remSpacing.medium,
            [mediaQueries.lg]: {
              gap: remSpacing.gutter,
            },
          }}
          style={{
            maxWidth: maxContainerSizeLookup[maxContainerSize],
          }}
        >
          {!!pageHeader && <PageHeader {...pageHeader} />}
          {!!filters && <FilterBarContainer>{filters}</FilterBarContainer>}
          {children}
        </div>
      </div>
    </UserParamContext.Provider>
  )
}
