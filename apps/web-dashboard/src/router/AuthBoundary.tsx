import { Fragment } from 'react'

import { withAuthenticationRequired } from '@auth0/auth0-react'

import { useApellaAuth0 } from '@apella/hooks'
import { AuthErrorPage } from 'src/pages/AuthErrorPage'

/**
 * Display auth errors.
 * Enforce authentication for all children beyond this component.
 */
const AuthBoundary = ({
  children,
}: React.PropsWithChildren): React.JSX.Element => {
  const { error } = useApellaAuth0()

  if (error) {
    return <AuthErrorPage message={error.message} />
  }

  return <AuthRequired>{children}</AuthRequired>
}

const AuthRequired = withAuthenticationRequired(Fragment)

export default AuthBoundary
