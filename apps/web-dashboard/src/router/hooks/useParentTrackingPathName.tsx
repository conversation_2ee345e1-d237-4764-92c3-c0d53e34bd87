import { useMatches } from 'react-router'

import { reverse } from 'lodash'

import { isApellaRouteHandle, TrackingPathTitle } from '../types'

// Example usage: A video blade can report what page it is on when viewing a video.
export const useParentTrackingPathName = (): TrackingPathTitle => {
  const matches = useMatches()

  // Remove most specific match as it is likely the video blade route.
  const nearestParentMatches = matches.slice(0, matches.length - 1)
  for (const match of reverse(nearestParentMatches)) {
    if (isApellaRouteHandle(match?.handle) && match?.handle.trackingPathName) {
      return match?.handle.trackingPathName
    }
  }
  return TrackingPathTitle.Unknown
}
