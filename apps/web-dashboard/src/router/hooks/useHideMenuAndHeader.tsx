import { useMatches } from 'react-router'

import { isUndefined, reverse } from 'lodash'

import { DISPLAY_MODE } from 'src/Contexts'

import { isApellaRouteHandle } from '../types'

export const usePageDefaultDisplayMode = (): DISPLAY_MODE | undefined => {
  const matches = useMatches()

  for (const match of reverse(matches)) {
    if (
      isApellaRouteHandle(match?.handle) &&
      !isUndefined(match?.handle.displayMode)
    ) {
      return match?.handle.displayMode
    }
  }
  return undefined
}
