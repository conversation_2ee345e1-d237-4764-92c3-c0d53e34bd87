import { useMatches } from 'react-router'

import { isUndefined, reverse } from 'lodash'

import { isApellaRouteHandle } from '../types'

/**
 * When a custom loading experience is needed for a page, the default global loading bar can be hidden.
 *
 * @returns boolean
 */
export const useHideGlobalLoadingBar = (): boolean => {
  const matches = useMatches()

  for (const match of reverse(matches)) {
    if (
      isApellaRouteHandle(match?.handle) &&
      !isUndefined(match?.handle.hideGlobalLoadingBar)
    ) {
      return match?.handle.hideGlobalLoadingBar
    }
  }
  return false
}
