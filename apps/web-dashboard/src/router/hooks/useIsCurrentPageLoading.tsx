import { useLocation, useNavigation } from 'react-router'

/**
 * Use this hook when you need to show a custom loading state for reloads of the current page.
 * NOTE: This should only be used if the global LoadingBar indicator is not a sufficient UX.
 *
 * @returns boolean
 */
export const useIsCurrentPageLoading = () => {
  const nextNavigation = useNavigation()
  const currentLocation = useLocation()
  return (
    currentLocation.pathname === nextNavigation.location?.pathname &&
    nextNavigation.state == 'loading'
  )
}
