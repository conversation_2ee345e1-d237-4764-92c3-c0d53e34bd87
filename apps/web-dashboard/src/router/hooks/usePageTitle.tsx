import { useMatches } from 'react-router'

import { reverse } from 'lodash'

import { APELLA_DASHBOARD_APP_TITLE } from '../constants'
import { isApellaRouteHandle } from '../types'

export const usePageTitle = (): string => {
  const matches = useMatches()

  for (const match of reverse(matches)) {
    if (isApellaRouteHandle(match?.handle) && match?.handle.title) {
      return match?.handle.title
    }
  }
  return APELLA_DASHBOARD_APP_TITLE
}
