import { useMatches } from 'react-router'

import { isUndefined, reverse } from 'lodash'

import { isApellaRouteHandle } from '../types'

export const useHideAppRefresh = (): boolean => {
  const matches = useMatches()

  for (const match of reverse(matches)) {
    if (
      isApellaRouteHandle(match?.handle) &&
      !isUndefined(match?.handle.hideAppRefresh)
    ) {
      return match?.handle.hideAppRefresh
    }
  }
  return false
}
