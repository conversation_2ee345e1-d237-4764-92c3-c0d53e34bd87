import { useMatches } from 'react-router'

import { SecondaryView } from 'src/components/types'

import { isApellaRouteHandle } from '../types'

export function useBreadCrumbs(): SecondaryView[] {
  const matches = useMatches()

  return (
    matches
      // Currently PageHeader is including the current page title in the breadcrumb automatically, so is should be excluded here.
      .slice(0, matches.length - 1)
      .reduce((crumbs: SecondaryView[], match) => {
        if (isApellaRouteHandle(match?.handle) && match.handle.title) {
          crumbs.push({
            id: match.pathname,
            display: match.handle.title,
            to: match.pathname,
          })
        }
        return crumbs
      }, [])
  )
}
