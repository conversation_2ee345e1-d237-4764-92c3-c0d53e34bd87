import { useEffect, useMemo } from 'react'
import { generatePath, useLocation } from 'react-router'

import { useFlags } from 'launchdarkly-react-client-sdk'

import {
  Camera,
  Duration,
  Highlights,
  InsertChart,
  MenuItemProps,
  ScheduleGantt,
  theme,
  TrackChanges,
  TV,
  Union,
} from '@apella/component-library'
import { useApellaAuth0 } from '@apella/hooks'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import { useScheduleFilterProviderUrls } from 'src/pages/Schedule/ScheduleFilterProvider'
import { LocationPath, MenuItemTitle } from 'src/router/types'

import { HighlightCounter } from './components/HighlightCounter'
import { useMenuContext } from './components/MenuContext'

export const useMenuItems = () => {
  const { permissions: uiPermissions, loading } = useCurrentUser()

  const location = useLocation()

  useEffect(() => {
    if (window.Beacon) {
      window.Beacon('event', {
        type: 'page-viewed',
        url: document.location.href,
        title: document.title,
      })
      window.Beacon('suggest')
    }
  }, [location])

  const { isAuthenticated, isLoading } = useApellaAuth0()
  const { isChangingOrg } = useMenuContext()
  const { currentOrganization: activeOrganization, userId } = useCurrentUser()
  const { allSearchParamsWithoutDate } = useScheduleFilterProviderUrls()
  const isLoadingAuthenticationAndPermissions = isLoading || loading
  const { enableBlockUtilizationDashboard } =
    useFlags<WebDashboardFeatureFlagSet>()

  const menuItems: MenuItemProps[] = useMemo(() => {
    if (!isAuthenticated) {
      return []
    }
    const menuItems: MenuItemProps[] = []
    if (uiPermissions?.dashboardLiveEnabled) {
      menuItems.push({
        title: MenuItemTitle.Live,
        to: `${generatePath(LocationPath.Live)}${allSearchParamsWithoutDate.size ? `?${allSearchParamsWithoutDate.toString()}` : ''}`,
        Icon: Camera,
        backgroundColor: theme.palette.blue[80],
        iconColor: 'white',
      })
    }
    if (uiPermissions?.dashboardScheduleEnabled) {
      menuItems.push({
        title: MenuItemTitle.Schedule,
        to: `${generatePath(LocationPath.Schedule)}${allSearchParamsWithoutDate.size ? `?${allSearchParamsWithoutDate.toString()}` : ''}`,
        Icon: ScheduleGantt,
        backgroundColor: theme.palette.green[40],
        iconColor: 'white',
      })
    }
    if (
      uiPermissions?.dashboardTurnoversEnabled &&
      !uiPermissions?.dashboardScheduleEnabled
    ) {
      menuItems.push({
        title: MenuItemTitle.Schedule,
        to: `${generatePath(LocationPath.TurnoversDashboard)}${allSearchParamsWithoutDate.size ? `?${allSearchParamsWithoutDate.toString()}` : ''}`,
        Icon: ScheduleGantt,
        backgroundColor: theme.palette.green[40],
        iconColor: 'white',
      })
    }
    if (uiPermissions?.caseDurationEnabled) {
      menuItems.push({
        title: MenuItemTitle.ScheduleAssistant,
        to: generatePath(LocationPath.ScheduleAssistant),
        Icon: Duration,
        backgroundColor: theme.palette.green[20],
        iconColor: 'black',
      })
    }
    if (enableBlockUtilizationDashboard) {
      menuItems.push({
        title: MenuItemTitle.BlockUtilization,
        to: generatePath(LocationPath.BlockUtilization),
        Icon: Union,
        backgroundColor: theme.palette.blue[20],
        iconColor: 'black',
      })
    }
    if (uiPermissions?.dashboardInsightsEnabled) {
      menuItems.push({
        title: MenuItemTitle.Insights,
        to: generatePath(LocationPath.CaseLengths),
        Icon: InsertChart,
        backgroundColor: theme.palette.blue[40],
        iconColor: 'white',
      })
    }
    if (uiPermissions?.bigBoardEnabled) {
      menuItems.push({
        title: MenuItemTitle.Boards,
        to: generatePath(LocationPath.Boards),
        Icon: TV,
        backgroundColor: theme.palette.gray[90],
        iconColor: 'white',
      })
    }
    if (uiPermissions?.dashboardTerminalCleansEnabled) {
      menuItems.push({
        title: MenuItemTitle.TerminalCleans,
        to: generatePath(LocationPath.TerminalCleans),
        Icon: TrackChanges,
        backgroundColor: theme.palette.gray[60],
        iconColor: 'white',
      })
    }
    if (uiPermissions?.dashboardHighlightsEnabled) {
      menuItems.push({
        title: MenuItemTitle.Highlights,
        to: generatePath(LocationPath.Highlights),
        Icon: Highlights,
        backgroundColor: theme.palette.gray[30],
        iconColor: 'black',
        componentRight: !isChangingOrg && activeOrganization && userId && (
          <HighlightCounter
            organizationId={activeOrganization.node.id}
            userId={userId}
          />
        ),
      })
    }

    return menuItems.map<MenuItemProps>((menuItem) => ({
      ...menuItem,
      disabled: isChangingOrg,
    }))
  }, [
    isAuthenticated,
    uiPermissions?.dashboardLiveEnabled,
    uiPermissions?.dashboardScheduleEnabled,
    uiPermissions?.dashboardTurnoversEnabled,
    uiPermissions?.caseDurationEnabled,
    uiPermissions?.dashboardInsightsEnabled,
    uiPermissions?.bigBoardEnabled,
    uiPermissions?.dashboardTerminalCleansEnabled,
    uiPermissions?.dashboardHighlightsEnabled,
    allSearchParamsWithoutDate,
    isChangingOrg,
    activeOrganization,
    userId,
    enableBlockUtilizationDashboard,
  ])
  return {
    menuItems,
    isLoadingMenuItems: isLoadingAuthenticationAndPermissions,
  }
}
