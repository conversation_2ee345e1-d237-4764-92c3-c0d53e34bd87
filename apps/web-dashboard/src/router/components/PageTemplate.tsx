import { ReactNode, useEffect, useMemo } from 'react'
import { generatePath, useLocation, useNavigation } from 'react-router'

import { useFlags } from 'launchdarkly-react-client-sdk'

import {
  Badge,
  ChildrenProps,
  ExitToApp,
  HeaderNavPageTemplate,
  LifeRing,
  LoadingBar,
  MenuItemProps,
  Notification,
  OrganizationProps,
  Progress,
  SettingProps,
  Union,
  Videocam,
  ZIndex,
} from '@apella/component-library'
import {
  useApellaAuth0,
  useLocalStorageState,
  useSpinDelay,
} from '@apella/hooks'
import {
  DisplayModeProvider,
  IsTouchDeviceContext,
  ShowVideoContext,
  useDisplayMode,
  TimezoneProvider,
} from 'src/Contexts'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import { UnsupportedBrowserPage } from 'src/pages'
import { LocationPath } from 'src/router/types'
import { settings } from 'src/settings'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'
import { isBrowserSupported } from 'src/utils/supportedBrowsers'
import { onClickContactSupport } from 'src/utils/toggleBeacon'
import { isApellaEmployee } from 'src/utils/userTypes'

import { useHideGlobalLoadingBar } from '../hooks/useHideGlobalLoadingBar'
import { usePageDefaultDisplayMode } from '../hooks/useHideMenuAndHeader'
import { useMenuItems } from '../useMenuItems'
import { useMenuContext } from './MenuContext'

const isTabletOrPhone = () => {
  /**
   * Use a regex to see if the userAgent is a tablet or phone, from here: https://stackoverflow.com/a/11381730
   * Preferring this method as it is easy to test on a browser by spoofing userAgent
   */
  return (
    /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(
      navigator.userAgent
    ) ||
    /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(
      navigator.userAgent.substring(0, 4)
    )
  )
}

interface PageTemplateProps {
  children?: ReactNode
}

export const PageTemplate = ({
  children,
}: PageTemplateProps): React.ReactElement => {
  if (!isBrowserSupported(window.navigator.userAgent)) {
    return <UnsupportedBrowserPage />
  }

  return (
    <PageTemplateProviders>
      <PageTemplateContent>{children}</PageTemplateContent>
    </PageTemplateProviders>
  )
}

const PageTemplateProviders = ({ children }: ChildrenProps) => {
  const [showVideo] = useLocalStorageState('showVideo', true)

  const defaultDisplayMode = usePageDefaultDisplayMode()

  const isTouchDevice = useMemo(() => isTabletOrPhone(), [])

  const { currentOrganization: activeOrganization } = useCurrentUser()

  return (
    <TimezoneProvider
      orgDefaultTimezone={
        activeOrganization?.node?.sites.edges[0]?.node.timezone
      }
    >
      <IsTouchDeviceContext.Provider value={isTouchDevice}>
        <DisplayModeProvider pageDisplayMode={defaultDisplayMode}>
          <ShowVideoContext.Provider value={showVideo}>
            {children}
          </ShowVideoContext.Provider>
        </DisplayModeProvider>
      </IsTouchDeviceContext.Provider>
    </TimezoneProvider>
  )
}

const PageTemplateContent = ({
  children,
}: PageTemplateProps): React.JSX.Element => {
  const { isTvModeEnabled } = useDisplayMode()
  const { textNotificationEnabled } = useFlags<WebDashboardFeatureFlagSet>()
  const {
    currentOrganization: activeOrganization,
    userOrganizations,
    loading: pageIsLoading,
  } = useCurrentUser()
  const logEvent = useAnalyticsEventLogger()
  const transition = useNavigation()
  const active = transition.state !== 'idle'
  const loading = useSpinDelay(active, { delay: 200, minDuration: 400 })

  const location = useLocation()
  const showGlobalLoadingBar = !useHideGlobalLoadingBar()

  useEffect(() => {
    if (window.Beacon) {
      window.Beacon('event', {
        type: 'page-viewed',
        url: document.location.href,
        title: document.title,
      })
      window.Beacon('suggest')
    }
  }, [location])

  useEffect(() => {
    logEvent(EVENTS.PAGE_VIEWED, {
      domain: window.location.hostname,
      path: location.pathname,
      search: location.search,
    })
  }, [location, logEvent])

  const { user } = useApellaAuth0()
  const { isChangingOrg, onChangeOrg } = useMenuContext()
  const [showVideo, setShowVideo] = useLocalStorageState('showVideo', true)

  const { menuItems } = useMenuItems()

  // Feedback should always be the last menu item.
  const footerMenuItem: MenuItemProps = {
    title: 'Feedback',
    Icon: LifeRing,
    onClick: onClickContactSupport,
  }

  const organizationItems = useMemo(
    () =>
      userOrganizations?.map((organization) => ({
        name: organization.node.name,
        id: organization.node.auth0OrgId,
      })),
    [userOrganizations]
  )

  const organizationProps: OrganizationProps = {
    organizations: organizationItems ?? [],
    activeOrganizationId: activeOrganization?.node?.auth0OrgId ?? '',
    onChange: onChangeOrg,
  }

  const userSettings: SettingProps[] = [
    {
      title: 'Sign out',
      to: generatePath(LocationPath.SignOut),
      Icon: ExitToApp,
      settingLevel: 'user',
    },
  ]

  if (isApellaEmployee(user?.email)) {
    userSettings.unshift({
      title: showVideo ? 'Disable Video' : 'Enable Video',
      onClick: () => setShowVideo(!showVideo),
      Icon: Videocam,
      componentRight: <Badge color={'orange'}>Internal only</Badge>,
      settingLevel: 'user',
    })
  }
  if (textNotificationEnabled) {
    userSettings.unshift({
      title: 'Text Notifications',
      to: generatePath(LocationPath.Notifications),
      Icon: Notification,
      componentRight: <Badge color={'orange'}>Internal only</Badge>,
      settingLevel: 'org',
    })
  }

  const { blockManagementEnabled, enableBlockUtilizationManagementPage } =
    useFlags<WebDashboardFeatureFlagSet>()

  if (blockManagementEnabled) {
    userSettings.unshift({
      title: 'Block Management',
      to: generatePath(LocationPath.Blocks),
      Icon: Union,
      settingLevel: 'org',
    })
  }

  if (enableBlockUtilizationManagementPage) {
    userSettings.unshift({
      title: 'Block Utilization Management',
      to: generatePath(LocationPath.BlockUtilizationManagement),
      Icon: Union,
      settingLevel: 'org',
    })
  }

  if (!isBrowserSupported(window.navigator.userAgent)) {
    return <UnsupportedBrowserPage />
  }

  return (
    <>
      {showGlobalLoadingBar && (
        <LoadingBar zIndex={ZIndex.ABOVE} loading={loading} />
      )}
      <HeaderNavPageTemplate
        organizationProps={organizationProps}
        menuItems={menuItems}
        footerMenuItem={footerMenuItem}
        settings={userSettings}
        name={user?.name}
        defaultOpen={false}
        environment={settings.environment}
        isTvModeEnabled={isTvModeEnabled}
        image={user?.picture}
      >
        {isChangingOrg || pageIsLoading ? (
          <div
            css={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Progress />
          </div>
        ) : (
          children
        )}
      </HeaderNavPageTemplate>
    </>
  )
}
