import { memo, useEffect, useMemo } from 'react'

import { useLazyQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { Counter } from '@apella/component-library'
import { FeedbackStatus } from 'src/__generated__/globalTypes'
import {
  GetHighlightCount,
  GetHighlightCountVariables,
} from 'src/modules/highlights/queries/__generated__'
import { GET_HIGHLIGHTS_COUNT } from 'src/modules/highlights/queries/search'

export const HighlightCounter = memo(function HighlightCounter({
  organizationId,
  userId,
}: {
  organizationId: string
  userId: string
}): React.JSX.Element {
  const variables = useMemo(
    () => ({
      startTime: DateTime.now().minus({ years: 100 }).toISO(),
      endTime: DateTime.now().plus({ years: 100 }).toISO(),
      organizationIds: [organizationId],
      assignedUserIds: [userId],
      feedbackStatus: FeedbackStatus.INCOMPLETE,
    }),
    [organizationId, userId]
  )

  const [query, { data: highlightData }] = useLazyQuery<
    GetHighlightCount,
    GetHighlightCountVariables
  >(GET_HIGHLIGHTS_COUNT, {
    variables,
  })

  useEffect(() => {
    if (organizationId && userId) {
      query()
    }
  }, [organizationId, query, userId])

  const highlightsCount = highlightData?.highlightSearch?.totalRecords
  return highlightsCount && highlightsCount > 0 ? (
    <Counter size="sm" color="gray" count={highlightsCount} />
  ) : (
    <></>
  )
})
