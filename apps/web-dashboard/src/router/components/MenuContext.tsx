import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react'

import { useApolloClient } from '@apollo/client'

import { useApellaAuth0, useLocalStorageState } from '@apella/hooks'
import { useCurrentUser } from 'src/modules/user/hooks'

import { ACTIVE_ORG_ID_KEY } from '../constants'

const MenuContext = createContext<{
  isChangingOrg: boolean
  onChangeOrg: (selectedAuth0OrgId: string) => Promise<void>
}>({
  isChangingOrg: false,
  onChangeOrg: async () => {},
})

export function MenuProvider({ children }: { children: ReactNode }) {
  const [isChangingOrg, setIsChangingOrg] = useState<boolean>(false)
  const { currentOrganization: activeOrganization } = useCurrentUser()
  const { loginWithRedirect } = useApellaAuth0()
  const [, setActiveOrgId] = useLocalStorageState(
    ACTIVE_ORG_ID_KEY,
    activeOrganization?.node.auth0OrgId
  )
  const client = useApolloClient()
  const onChangeOrg = useCallback(
    async (selectedAuth0OrgId: string) => {
      // Redirect the user to Auth0 to login in the context of the selected Organization
      setIsChangingOrg(true)
      setActiveOrgId(selectedAuth0OrgId)
      await loginWithRedirect({
        authorizationParams: {
          organization: selectedAuth0OrgId,
          redirect_uri: window.location.origin,
        },
        appState: {
          returnTo: window.location.pathname,
        },
      })
      await client.resetStore()
    },
    [setActiveOrgId, loginWithRedirect, client]
  )
  const value = useMemo(() => {
    return {
      isChangingOrg,
      onChangeOrg,
    }
  }, [isChangingOrg, onChangeOrg])

  return <MenuContext.Provider value={value}>{children}</MenuContext.Provider>
}

export function useMenuContext() {
  return useContext(MenuContext)
}
