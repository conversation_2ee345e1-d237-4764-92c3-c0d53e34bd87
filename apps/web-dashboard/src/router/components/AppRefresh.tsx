import {
  createContext,
  ReactN<PERSON>,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react'
import { toast } from 'react-toastify'

import {
  Button,
  FlexContainer,
  P3,
  theme,
  ThemeProvider,
} from '@apella/component-library'
import { useLocalStorageState } from '@apella/hooks'
import { backoff, HOUR_IN_MS } from 'src/utils/backoff'

const VERSION_ENDPOINT = '/manifest.webmanifest'
const LAST_MODIFIED_HEADER = 'last-modified'
const APP_VERSION_KEY = 'apella_web_dashboard_version'

const getAppVersion = async () => {
  const headRes = await fetch(VERSION_ENDPOINT, { method: 'HEAD' })

  return headRes.headers.get(LAST_MODIFIED_HEADER)
}

export const AppRefreshContext = createContext<{
  refresh: boolean
}>({
  refresh: false,
})

const refreshWindow = () => window.location.reload()

export const useAppRefreshContext = () => useContext(AppRefreshContext)

export const AppRefreshToast = () => {
  const { refresh } = useAppRefreshContext()

  useEffect(() => {
    if (!refresh) {
      return
    }

    toast(
      <ThemeProvider theme={theme}>
        <FlexContainer alignItems="baseline">
          <P3>New version of Apella available!</P3>{' '}
          <Button
            onClick={refreshWindow}
            appearance="link"
            color="alternate"
            size="sm"
          >
            Refresh
          </Button>
        </FlexContainer>
      </ThemeProvider>,
      {
        autoClose: false,
        hideProgressBar: false,
        closeOnClick: false,
        pauseOnHover: true,
        draggable: false,
        toastId: 'refresh-app-toast',
        closeButton: false,
      }
    )
  }, [refresh])

  return null
}

export const AppRefreshProvider = ({ children }: { children: ReactNode }) => {
  const [appRefresh, setAppRefresh] = useState<{
    refresh: boolean
    version: string
    isInitialLoad: boolean
  }>({
    refresh: false,
    version: '',
    isInitialLoad: true,
  })

  const [appVersion, setAppVersion] = useLocalStorageState<string | null>(
    APP_VERSION_KEY,
    null
  )

  const initialLoad = useCallback(async () => {
    try {
      const version = await backoff(getAppVersion, { maxRetries: 3 })
      setAppVersion(version)

      setAppRefresh({
        refresh: false,
        version: version || '',
        isInitialLoad: false,
      })
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error(e)
      setAppRefresh({
        refresh: false,
        version: '',
        isInitialLoad: false,
      })
    }
  }, [setAppVersion, setAppRefresh])

  const setVersionFn = useCallback(async () => {
    const newAppVersion = await getAppVersion()

    if (!newAppVersion || newAppVersion === appVersion) {
      return Promise.reject(new Error('No new version available'))
    }

    setAppRefresh({
      refresh: true,
      version: newAppVersion,
      isInitialLoad: false,
    })
    return Promise.resolve()
  }, [appVersion])

  useEffect(() => {
    if (appRefresh.isInitialLoad) {
      initialLoad()
    }
  }, [appRefresh.isInitialLoad, initialLoad])

  useEffect(() => {
    if (!appRefresh.isInitialLoad && !appRefresh.refresh) {
      backoff(setVersionFn, { attempt: 1, maxWait: HOUR_IN_MS })
    }
  }, [setVersionFn, appRefresh.isInitialLoad, appRefresh.refresh])

  return (
    <AppRefreshContext.Provider
      value={{
        refresh: appRefresh?.refresh ?? false,
      }}
    >
      {children}
    </AppRefreshContext.Provider>
  )
}
