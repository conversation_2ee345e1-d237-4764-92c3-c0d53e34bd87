import { useEffect } from 'react'
import { Outlet } from 'react-router'

import { AppRefreshToast } from 'src/router/components/AppRefresh'

import { useHideAppRefresh } from '../hooks/useHideAppRefresh'
import { usePageTitle } from '../hooks/usePageTitle'
import { useParentTrackingPathName } from '../hooks/useParentTrackingPathName'
import { PageContext } from './PageContext'

export function PageWrapper() {
  const pageTitle = usePageTitle()
  const trackingPathName = useParentTrackingPathName()
  const hideAppRefresh = useHideAppRefresh()

  useEffect(() => {
    document.title = pageTitle
  }, [pageTitle])

  return (
    <PageContext.Provider value={{ trackingPathName }}>
      {!hideAppRefresh && <AppRefreshToast />}
      <Outlet />
    </PageContext.Provider>
  )
}
