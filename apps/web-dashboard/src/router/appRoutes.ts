/* eslint-disable perfectionist/sort-imports */
import { replace, RouteObject } from 'react-router'

import { UnexpectedErrorBoundary } from 'src/components/UnexpectedErrorBoundary'
import {
  CaseLengthPage,
  FirstCaseStartPage,
  HighlightsPage,
  HighlightViewPage,
  HomePage,
  LogoutPage,
  PageNotFound as PageNotFoundComponent,
  Schedule,
  loader as scheduleLoader,
  TurnoverPage,
} from 'src/pages'
import { BlockEditPage, BlockNewPage } from 'src/pages/BlockManagement'
import { BlockManagementPage } from 'src/pages/BlockManagement/BlockManagementPage'
import { BlockManagementPageUploadPage } from 'src/pages/BlockManagement/BlockManagementPageUploadPage'
import { BlockManagementPageUploadReleasePage } from 'src/pages/BlockManagement/BlockManagementPageUploadReleasePage'
import { BoardView } from 'src/pages/Boards/Board'
import { CasePlanningRoom } from 'src/pages/CasePlanning'
import { CasePlanning } from 'src/pages/CasePlanning/CasePlanning'
import { CaseVolumePage } from 'src/pages/Insights/CaseVolume/CaseVolumePage'
import Live from 'src/pages/Live/Live'
import { AddSubscriber, EditSubscriber } from 'src/pages/Notifications'
import { Notifications } from 'src/pages/Notifications/StaffEventNotifications'
import { EditSchedule } from 'src/pages/Schedule/EditSchedule/EditSchedule'
import {
  loader as availableTimesLoader,
  AvailableTimesRoute,
} from 'src/pages/ScheduleAssistant/AvailableTimes/AvailableTimes'
import {
  RecentCases,
  loader as recentCasesLoader,
} from 'src/pages/ScheduleAssistant/CaseDuration/RecentCases'
import { Component as ScheduleAssistantRoute } from 'src/pages/ScheduleAssistant/ScheduleAssistantRoute'
import {
  TimeSelection,
  loader as timeSelectionLoader,
} from 'src/pages/ScheduleAssistant/TimeSelection/TimeSelection'
import { TerminalCleansRowView } from 'src/pages/TerminalCleans/LegacyTerminalCleans/TerminalCleansRowView'
import TerminalCleans from 'src/pages/TerminalCleans/TerminalCleans'

import { DISPLAY_MODE } from 'src/Contexts'
import {
  BlockData,
  loader as blockDataLoader,
} from 'src/modules/daily-metrics/components/BlockData'
import { CaseData } from 'src/modules/daily-metrics/components/CaseData'
import { ApellaHome } from 'src/pages/ApellaHome'
import {
  BlockUtilizationForSiteLayout,
  loader as blockUtilizationForSiteLoader,
} from 'src/pages/BlockUtilization/BlockUtilizationForSite'
import {
  BlockUtilizationCaseEditView,
  action as blockUtilizationCaseEditViewAction,
  loader as blockUtilizationCaseEditViewLoader,
} from 'src/pages/BlockUtilizationManagement/BlockUtilizationCaseEditView'
import {
  action as blockUtilizationManagementAction,
  loader as blockUtilizationManagementLoader,
  BlockUtilizationManagementPage,
} from 'src/pages/BlockUtilizationManagement/BlockUtilizationManagementPage'
import OpenStaffPlanningBlade from 'src/pages/Schedule/OpenStaffPlanningBlade'
import {
  EstimateLayout,
  ErrorBoundary as EstimateLayoutErrorBoundary,
  loader as estimateLoader,
} from 'src/pages/ScheduleAssistant/EstimateLayout'
import { SurgeonProcedureFiltersLayout } from 'src/pages/ScheduleAssistant/SurgeonProcedureFiltersLayout'
import orgLocalStorage from 'src/utils/orgLocalStorage'
import { BoardEdit } from '../pages/Boards'
import { Boards } from '../pages/Boards/Boards'
import { PostOp } from '../pages/PreOpPostOp/PostOp'
import { PreOp } from '../pages/PreOpPostOp/PreOp'
import { DailyInsights } from '../pages/Schedule/DailyInsights'
import StaffManagement from '../pages/StaffManagement/StaffManagement'
import { Turnovers } from '../pages/Turnovers/Turnovers'
import { VideoViewPage } from '../pages/VideoBlade/VideoViewPage'
import { ApellaRouteHandle, LocationPath, TrackingPathTitle } from './types'
import {
  loader as blockUtilizationForBlockLoader,
  BlockUtilizationForBlockLayout,
} from 'src/pages/BlockUtilization/BlockUtilizationForBlock'
import {
  action as blockUtilizationVerifyReportAction,
  loader as blockUtilizationVerifyReportLoader,
  VerifyReportLayout,
} from 'src/pages/BlockUtilization/VerifyReport'

// ATTENTION: Read the ./router/README.md for information and FAQs on routing

const VideoView: RouteObject = {
  path: LocationPath.VideoView,
  handle: { title: 'Video', trackingPathName: TrackingPathTitle.Video },
  Component: VideoViewPage,
}

const DailyInsightsRoute: RouteObject = {
  path: LocationPath.DailyInsights,
  handle: {
    title: 'Daily Insights',
    trackingPathName: TrackingPathTitle.DailyInsights,
  },
  Component: DailyInsights,
  children: [
    {
      path: LocationPath.CasesDataView,
      handle: { trackingPathName: TrackingPathTitle.CasesData },
      Component: CaseData,
    },
    {
      path: LocationPath.BlocksDataView,
      handle: { trackingPathName: TrackingPathTitle.BlocksData },
      Component: BlockData,
      loader: blockDataLoader,
    },
  ],
}

const StaffPlanningRoute: RouteObject = {
  path: LocationPath.StaffPlanning,
  handle: {
    title: 'Staff Planning',
    trackingPathName: TrackingPathTitle.StaffPlanning,
  },
  Component: OpenStaffPlanningBlade,
}

export const appRoutes: RouteObject[] = [
  {
    path: LocationPath.Root,
    handle: { title: 'Root' },
    Component: HomePage,
  },
  {
    path: LocationPath.Home,
    handle: { title: 'Home' },
    Component: ApellaHome,
  },
  {
    path: LocationPath.SignOut,
    handle: { title: 'Logout' },
    Component: LogoutPage,
  },
  {
    path: LocationPath.Notifications,
    handle: { title: 'Staff Event Notifications' },
    Component: Notifications,
  },
  {
    path: LocationPath.AddSubscriber,
    handle: { title: 'Add Subscriber' },
    Component: AddSubscriber,
  },
  {
    path: LocationPath.EditSubscriber,
    handle: { title: 'Edit Subscriber' },
    Component: EditSubscriber,
  },
  {
    path: LocationPath.CasePlanning,
    Component: CasePlanning,
    handle: {
      authorization: 'caseStaffPlanWriteEnabled',
      title: 'Case Planning',
    } satisfies ApellaRouteHandle,
  },
  {
    path: LocationPath.CasePlanningLegacy,
    Component: CasePlanning,
    handle: {
      title: 'Case Planning',
      redirect: LocationPath.CasePlanning,
      authorization: 'caseStaffPlanWriteEnabled',
    } satisfies ApellaRouteHandle,
  },
  {
    path: LocationPath.CasePlanningRoom,
    Component: CasePlanningRoom,
    handle: {
      title: 'Case Planning',
      authorization: 'caseStaffPlanWriteEnabled',
    } satisfies ApellaRouteHandle,
  },
  {
    path: LocationPath.CasePlanningRoomLegacy,
    Component: CasePlanningRoom,
    handle: {
      title: 'Case Planning',
      redirect: LocationPath.CasePlanningRoom,
      authorization: 'caseStaffPlanWriteEnabled',
    } satisfies ApellaRouteHandle,
  },
  {
    path: LocationPath.Highlights,
    handle: {
      title: 'Highlights',
      authorization: 'dashboardHighlightsEnabled',
      trackingPathName: TrackingPathTitle.Highlights,
    } satisfies ApellaRouteHandle,
    Component: HighlightsPage,
    children: [
      {
        path: LocationPath.HighlightView,
        handle: {
          title: 'Highlight',
          authorization: 'dashboardHighlightsEnabled',
          trackingPathName: TrackingPathTitle.Highlights,
        } satisfies ApellaRouteHandle,
        Component: HighlightViewPage,
      },
    ],
  },
  {
    path: LocationPath.Live,
    handle: {
      title: 'Live',
      authorization: 'dashboardLiveEnabled',
      trackingPathName: TrackingPathTitle.Live,
    } satisfies ApellaRouteHandle,
    Component: Live,
    children: [VideoView],
  },
  {
    path: LocationPath.Schedule,
    handle: {
      title: 'Schedule',
      authorization: 'dashboardScheduleEnabled',
      trackingPathName: TrackingPathTitle.Schedule,
    } satisfies ApellaRouteHandle,
    Component: Schedule,
    loader: scheduleLoader,
    children: [
      VideoView,
      DailyInsightsRoute,
      StaffPlanningRoute,
      {
        path: LocationPath.EditSchedule,
        Component: EditSchedule,
        handle: {
          title: 'Edit Schedule',
          authorization: 'dashboardScheduleEditEnabled',
        } satisfies ApellaRouteHandle,
      },
    ],
  },
  {
    path: LocationPath.PreOp,
    handle: {
      title: 'PreOp',
      authorization: 'dashboardScheduleEnabled',
      trackingPathName: TrackingPathTitle.Schedule,
    } satisfies ApellaRouteHandle,
    Component: PreOp,
  },
  {
    path: LocationPath.PostOp,
    handle: {
      title: 'PostOp',
      authorization: 'dashboardScheduleEnabled',
      trackingPathName: TrackingPathTitle.Schedule,
    } satisfies ApellaRouteHandle,
    Component: PostOp,
  },
  {
    path: LocationPath.TurnoversDashboard,
    handle: {
      title: 'Turnovers',
      trackingPathName: TrackingPathTitle.Schedule,
    } satisfies ApellaRouteHandle,
    Component: Turnovers,
    children: [VideoView],
  },
  {
    path: LocationPath.StaffManagement,
    handle: {
      title: 'Staff Management',
      authorization: 'dashboardScheduleEnabled',
      trackingPathName: TrackingPathTitle.StaffManagement,
    } satisfies ApellaRouteHandle,
    Component: StaffManagement,
  },
  {
    path: LocationPath.CaseLengths,
    handle: {
      title: 'Cases',
      authorization: 'dashboardInsightsEnabled',
      trackingPathName: TrackingPathTitle.Insights,
    } satisfies ApellaRouteHandle,
    Component: CaseLengthPage,
    children: [VideoView],
  },
  {
    path: LocationPath.CaseVolume,
    handle: {
      title: 'Cases',
      authorization: 'dashboardInsightsEnabled',
      trackingPathName: TrackingPathTitle.Insights,
    } satisfies ApellaRouteHandle,
    Component: CaseVolumePage,
    children: [VideoView],
  },
  {
    path: LocationPath.FirstCaseStarts,
    handle: {
      title: 'First Case Starts',
      authorization: 'dashboardInsightsEnabled',
      trackingPathName: TrackingPathTitle.Insights,
    } satisfies ApellaRouteHandle,
    Component: FirstCaseStartPage,
    children: [VideoView],
  },
  {
    path: LocationPath.Turnovers,
    handle: {
      title: 'Turnovers',
      authorization: 'dashboardInsightsEnabled',
      trackingPathName: TrackingPathTitle.Insights,
    } satisfies ApellaRouteHandle,
    Component: TurnoverPage,
    children: [VideoView],
  },
  {
    path: LocationPath.TerminalCleans,
    handle: {
      title: 'Terminal Cleans',
      trackingPathName: TrackingPathTitle.TerminalCleans,
    },
    Component: TerminalCleans,
    children: [
      {
        path: LocationPath.TerminalCleansView,
        handle: {
          title: 'Terminal Cleans',
          trackingPathName: TrackingPathTitle.TerminalCleans,
        },
        Component: TerminalCleansRowView,
      },
    ],
  },
  {
    path: LocationPath.Blocks,
    handle: { title: 'Block Management' },
    children: [
      {
        index: true,
        handle: { trackingPathName: TrackingPathTitle.BlockManagement },
        Component: BlockManagementPage,
      },
      {
        path: LocationPath.BlockEditor,
        handle: {
          title: 'Block Editor',
          trackingPathName: TrackingPathTitle.BlockManagement,
        },
        Component: BlockEditPage,
      },
      {
        path: LocationPath.BlockNew,
        handle: {
          title: 'New Block',
          trackingPathName: TrackingPathTitle.BlockManagement,
        },
        Component: BlockNewPage,
      },
      {
        path: LocationPath.BlockUpload,
        handle: {
          title: 'Upload Block Times',
          trackingPathName: TrackingPathTitle.BlockManagement,
        },
        Component: BlockManagementPageUploadPage,
      },
      {
        path: LocationPath.BlockUploadReleases,
        handle: {
          title: 'Upload Block Releases',
          trackingPathName: TrackingPathTitle.BlockManagement,
        },
        Component: BlockManagementPageUploadReleasePage,
      },
    ],
  },
  {
    path: LocationPath.BlockUtilizationManagement,
    handle: {
      title: 'Block Utilization Management',
      trackingPathName: TrackingPathTitle.BlockUtilizationManagement,
    } satisfies ApellaRouteHandle,
    children: [
      {
        index: true,
        Component: BlockUtilizationManagementPage,
        loader: blockUtilizationManagementLoader,
        action: blockUtilizationManagementAction,
      },
      {
        path: LocationPath.BlockUtilizationCaseEditView,
        Component: BlockUtilizationCaseEditView,
        loader: blockUtilizationCaseEditViewLoader,
        action: blockUtilizationCaseEditViewAction,
      },
    ],
  },
  {
    path: LocationPath.BlockUtilization,
    handle: {
      title: 'Block Utilization',
      trackingPathName: TrackingPathTitle.BlockUtilizationData,
    } satisfies ApellaRouteHandle,
    children: [
      {
        index: true,
        Component: BlockUtilizationForSiteLayout,
        loader: blockUtilizationForSiteLoader,
      },
      {
        id: 'blockUtilizationForBlock',
        path: LocationPath.BlockUtilizationForBlock,
        loader: blockUtilizationForBlockLoader,
        children: [
          {
            index: true,
            Component: BlockUtilizationForBlockLayout,
          },
          {
            id: 'verifyReport',
            path: LocationPath.BlockUtilizationVerifyReport,
            Component: VerifyReportLayout,
            loader: blockUtilizationVerifyReportLoader,
            action: blockUtilizationVerifyReportAction,
          },
        ],
      },
    ],
  },
  {
    path: LocationPath.Boards,
    handle: {
      title: 'Boards',
      authorization: 'bigBoardEnabled',
      trackingPathName: TrackingPathTitle.Boards,
    } satisfies ApellaRouteHandle,
    Component: Boards,
  },
  {
    path: LocationPath.BoardView,
    handle: {
      title: 'Board',
      authorization: 'bigBoardEnabled',
      trackingPathName: TrackingPathTitle.Boards,
      displayMode: DISPLAY_MODE.TV,
    } satisfies ApellaRouteHandle,
    Component: BoardView,
  },
  {
    path: LocationPath.BoardEdit,
    handle: {
      title: 'Edit Board',
      authorization: 'bigBoardWriteEnabled',
      trackingPathName: TrackingPathTitle.Boards,
      hideAppRefresh: true,
    } satisfies ApellaRouteHandle,
    Component: BoardEdit,
  },
  {
    path: LocationPath.ScheduleAssistant,
    handle: {
      title: 'Schedule Assistant',
      authorization: 'caseDurationEnabled',
      trackingPathName: TrackingPathTitle.ScheduleAssistant,
    } satisfies ApellaRouteHandle,
    Component: ScheduleAssistantRoute,
    ErrorBoundary: UnexpectedErrorBoundary,
    children: [
      {
        index: true,
        loader() {
          const scheduleAssistantLastTab = orgLocalStorage.getItem(
            'scheduleAssistantLastTab'
          )
          return replace(scheduleAssistantLastTab ?? LocationPath.CaseDuration)
        },
      },
      {
        id: 'surgeonProcedureFilters',
        Component: SurgeonProcedureFiltersLayout,
        children: [
          {
            id: 'caseDurationEstimate',
            Component: EstimateLayout,
            ErrorBoundary: EstimateLayoutErrorBoundary,
            loader: estimateLoader,
            children: [
              {
                path: LocationPath.CaseDuration,
                handle: {
                  title: 'Case Duration Lookup',
                  authorization: 'caseDurationEnabled',
                  trackingPathName: TrackingPathTitle.CaseDuration,
                  hideGlobalLoadingBar: true,
                } satisfies ApellaRouteHandle,
                Component: RecentCases,
                loader: recentCasesLoader,
                children: [VideoView],
              },
              {
                path: LocationPath.AvailableTimes,
                handle: {
                  title: 'Available Times',
                  authorization: 'caseDurationEnabled',
                  trackingPathName: TrackingPathTitle.AvailableTimes,
                } satisfies ApellaRouteHandle,
                Component: AvailableTimesRoute,
                loader: availableTimesLoader,
              },
            ],
          },
        ],
      },
      {
        path: LocationPath.TimeSelection,

        handle: {
          title: 'Time boost',
          authorization: 'availableTimesEmailEnabled',
          trackingPathName: TrackingPathTitle.TimeSelection,
        } satisfies ApellaRouteHandle,
        Component: TimeSelection,
        loader: timeSelectionLoader,
      },
    ],
  },
  {
    path: LocationPath.Other,
    handle: {
      title: 'Error 404: Not Found',
    },
    Component: PageNotFoundComponent,
  },
] as const
