import { useEffect } from 'react'

import { useLocalStorageState } from '@apella/hooks'
import { useCurrentUser } from 'src/modules/user/hooks'

import { ACTIVE_ORG_ID_KEY } from './constants'

/**
 * Store the user's current organization in local storage. Used in App.tsx
 * to log the user into the last active organization automatically on the next
 * full app reload.
 */
const OrgAuth = ({ children }: React.PropsWithChildren): React.JSX.Element => {
  const { currentOrganization } = useCurrentUser()
  const [, setActiveOrgId] = useLocalStorageState(ACTIVE_ORG_ID_KEY, '')
  useEffect(() => {
    const activeOrgId = currentOrganization?.node.auth0OrgId
    if (activeOrgId) {
      setActiveOrgId(activeOrgId)
    }
  }, [currentOrganization, setActiveOrgId])
  return <>{children}</>
}

export default OrgAuth
