# Router Docs

We use [React Router](https://reactrouter.com/) for routing and data loading in our react app. React Router uses nesting routing to allow for composing layouts and their data fetching. This allows for all matching routes on a given page to load data in parallel and render their respective child routes after data is loaded. This dramatically simplifies data fetching and loading states and co-locates data fetching with the UI that needs it.

## How to add a route/page

- Create a new directory in `src/pages` with the name of the route. E.g: `src/pages/ScheduleAssistant`
  - If your new route is a child of an existing route, create a new directory in the parent route directory. E.g: `src/pages/ScheduleAssistant/CaseDuration`
- Create a new file in the directory with the name of the route. E.g: `src/pages/ScheduleAssistant/ScheduleAssistantRoute.tsx`
- Inside your new file, export a React component as the default export. E.g: `export default function ScheduleAssistantRoute() { return <div>My new route</div> }`
- If using a loader or action, export functions by that name. E.g: `export async function loader() { return { text: 'Hello World' } }`
- Import your new component/loader/action in `appRoutes.ts`.
- Nest a new object in the `appRoutes` array with the following properties (if your new route is a child, nest it under the parent route):
  - `path`: The path of the route. E.g: `/schedule-assistant`. For IDs, dynamic segments see [React Router "Routing" doc](https://reactrouter.com/start/data/routing)
  - `Component`: The component you created E.g: `ScheduleAssistantRoute`
  - `handle`: See [types.ts](./types.ts) for supported properties
  - `loader`: Load and return data from GraphQL or redirect to other routes
  - `action`: Handle Form submissions and call GraphQL mutations, redirect to other routes
  - `children`: Only use this property if your route nests any children route. E.g: Show a video page on your page
  - `shouldRevalidate`: This property is rarely needed. By default, all routes are revalidated after actions. This function allows a route to opt-out of revalidation for actions that don't affect its data.
- Add an `<Outlet />` to the parent route you're rendering inside of. This is where nesting of routes render. See the [React Router Outlet docs](https://reactrouter.com/start/data/routing#nested-routes) for more.
- Add a link to your page `<Link to="schedule-assistant/case-duration">Case Duration</Link>`

## How to load data in a `loader`:

- The apollo client and other clients/data (org, user, feature flags) are available in the `context` of the loader function. See `ApellaRouteContext` in [types.ts](./types.ts).

### Data loading options

#### await + useLoaderData

Load data before rendering the route. Use this for simple data fetching that is relatively quick.

```tsx
export const loader = async (
  { request }: LoaderFunctionArgs,
  context: unknown
) => {
  const { apolloClient } = getRouteContext(context)
  const { data } = await apolloClient.query({ query: MY_QUERY })
  return { data }
}
```

Access the data in your component with [`useLoaderData`](https://reactrouter.com/api/hooks/useLoaderData#summary) like so:

```tsx
const { data } = useLoaderData() // Access data for nearest parent route with a loader
```

#### Return a promise without `await`

Load data after rendering the route without blocking routing transitions. Useful for loading data that is slow or not critical to the page.

```tsx
export const loader = async (
  { request }: LoaderFunctionArgs,
  context: unknown
) => {
  const { apolloClient } = getRouteContext(context)
  const mySlowDataPromise = apolloClient.query({ query: MY_SLOW_QUERY })
  return { mySlowDataPromise }
}
```

Because the `mySlowDataPromise` promise was not awaited, we have to use Suspense to show a fallback until it is resolved. Access the data in your component with `Suspense` and `Await` like so:

```tsx
const { mySlowDataPromise } = useLoaderData() // Access promise for nearest parent route with a loader
return (
  <Suspense fallback={<div>Loading...</div>}>
    {/* The Await component uses a function as its childen and exposes the data that was awaited as a parameter */}
    <Await resolve={mySlowDataPromise}>{({ data }) => <div>{data}</div>}</Await>
  </Suspense>
)
```

If you dislike the Function as Children pattern, use the `useAsyncValue()` hook:

```tsx
const MyRoute = () => {
    const { mySlowDataPromise } = useLoaderData() // Access promise for nearest parent route with a loader
    return (
    <Suspense fallback={<div>Loading...</div>}>
        <Await resolve={mySlowDataPromise}>
            <MyComponent>
        </Await>
    </Suspense>
    )
}

const MyComponent = () => {
    const { data } = useAsyncValue()
    return <div>{data}</div>
}
```

## React Router conventions

### Page with Form submission/redirect

```mermaid
graph TD
    URLChange[URL Change] --> LoaderCalled[Loader Called]
    LoaderCalled --> RouteRendered[Route Rendered]
    RouteRendered --> FormSubmitted[Form Submitted]
    FormSubmitted --> ActionCalled[Action Called]
    ActionCalled --> Redirect[Redirect]
    Redirect --> URLChange
```

### Page with URL Search Param powered filters

```mermaid
graph TD
    URLChange[URL Change] --> LoaderCalled[Loader Called]
    LoaderCalled --> RouteRendered[Route Rendered]
    RouteRendered --> SearchParams[Search Params Change. e.g Filter Value]
    SearchParams --> URLChange
```

### Page with async data loading

```mermaid
graph TD
    URLChange[URL Change] --> LoaderCalled[Loader Called - no await]
    LoaderCalled --> RouteRendered[Route Rendered]
    RouteRendered --> SuspenseFallback[Show Suspense Fallback]
    SuspenseFallback --> RestOfPageLoads[Continue Rendering Page]
    RestOfPageLoads --> AwaitPromise[Promise Resolves]
    AwaitPromise --> RenderComponent[Render Component]
```

## FAQs

> [!TIP]
> Why isn't my route showing up?
> If you have a nested child, you need to use <Outlet/> on the parent component in order for the child to render. [Example Outlet usage](https://github.com/Apella-Technology/lib-web/blob/d45f996d88df6f27ab23b2135e89b64a2737e572/apps/web-dashboard/src/pages/ScheduleAssistant/CaseDuration/RecentCases.tsx#L280). [Example Nested Route](https://github.com/Apella-Technology/lib-web/blob/d45f996d88df6f27ab23b2135e89b64a2737e572/apps/web-dashboard/src/router/appRoutes.ts#L462)

> [!TIP]
> When should I use loaders?
> Loaders are the best practice for loading data in react router apps. `useQuery` should only be used to load data if a query should not run until the component is rendered. This is a rare case.

> [!TIP]
> When should I `await` in a loader vs return a property with a promise?
> If your loader is slow and you want to show a loading skeleton, you should return a property with a promise from your loader. If you don't need to show a loading skeleton, you can `await` in your loader and fall back to the global loading bar.

> [!TIP]
> What does `satisfies ApellaRouteHandle` do? Because Apella has its own concept of a `handle` with custom properties that we want to type check and React Router defines `handle` as `any`, we must use a TypeScript `satisfies` to force the type checking to occur. Without it, we can't make sure that `authorization` is a `Permission` type, for example.
