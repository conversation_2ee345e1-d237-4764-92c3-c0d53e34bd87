import {
  DataStrategyFunction,
  DataStrategyFunctionArgs,
  DataStrategyResult,
} from 'react-router'

import { ApellaDataDependencies } from 'src/api/clients/apellaDataDependencies'

import { isApellaRouteHandle } from './types'

export const createDataStrategy = (
  dependencies: ApellaDataDependencies
): DataStrategyFunction =>
  async function dataStrategy({
    matches,
    request,
    params,
  }: DataStrategyFunctionArgs): Promise<Record<string, DataStrategyResult>> {
    // context is passed to all middlewares to be mutated and will be provided to actions/loaders
    // Scope context to the current "request" being handled and avoid dependencies being mutated
    const context = { ...dependencies }
    for (const match of matches) {
      if (
        isApellaRouteHandle(match.route.handle) &&
        match.route.handle?.middleware?.length
      ) {
        for (const middleware of match.route.handle.middleware) {
          try {
            await middleware({ request, params, matches, context }, context)
          } catch (error) {
            // Middleware can interrupt the loading process by throwing a response (e.g. redirect)
            if (error instanceof Response) {
              return {
                [match.route.id]: {
                  type: 'error',
                  result: error,
                },
              }
            }
            throw error
          }
        }
      }
    }

    const matchesToLoad = matches.filter((m) => m.shouldLoad)
    const results = await Promise.all(
      matchesToLoad.map((match) =>
        match.resolve((handler) => {
          return handler(context)
        })
      )
    )

    return results.reduce(
      (acc, result, i) =>
        Object.assign(acc, {
          [matchesToLoad[i].route.id]: result,
        }),
      {}
    )
  }
