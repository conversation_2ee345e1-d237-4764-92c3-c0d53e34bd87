import {
  RumEventDomainContext,
  RumFetchResourceEventDomainContext,
  datadogRum,
} from '@datadog/browser-rum'

import { MiddlewareFunctionArgs } from 'src/router/middleware/index'
import { ApellaRouteContext } from 'src/router/types'
import { settings } from 'src/settings'
import { CLAIMS } from 'src/utils/auth0User'
import { logger } from 'src/utils/exceptionLogging'

let DATADOG_RUM_INITIALIZED = false

/**
 * Middleware to initialize Datadog Real-User-Monitoring.
 */
export const datadogRumMiddleware = async (
  _args: MiddlewareFunctionArgs,
  context: ApellaRouteContext
) => {
  // Refrain from sending traffic to Datadog if running locally.
  if (!DATADOG_RUM_INITIALIZED && !isLocalhost()) {
    datadogRum.init({
      applicationId: settings.datadog.applicationId,
      clientToken: settings.datadog.clientToken,
      service: 'web-dashboard',
      env: settings.environment,
      version: settings.version,
      // Sample 100% of sessions as the cost impact is minimal.
      sessionSampleRate: 100,
      // Exclude these URLs as activity to get more accurate timestamps for page loads.
      excludedActivityUrls: [
        /^https:\/\/events\.launchdarkly\.com\//,
        /^https:\/\/api2\.amplitude\.com\//,
      ],
      beforeSend: function (event, context: RumEventDomainContext) {
        // Replace the resource URL for GraphQL to be the GraphQL operation name
        if (
          event.type === 'resource' &&
          event.resource?.url.includes('/v1/graphql') &&
          event.resource &&
          isRumResourceEventContext(context)
        ) {
          try {
            const requestBody = context?.requestInit?.body
            if (requestBody && typeof requestBody === 'string') {
              const parsedBody = JSON.parse(requestBody)
              const gqlOperationName = parsedBody?.operationName
              if (gqlOperationName) {
                event.resource.url = gqlOperationName
              }
            }
          } catch (error) {
            logger.warn(`Failed to parse GraphQL request body: ${error}`)
          }
        }
        return true
      },
      trackResources: true,
      // Track user interactions.
      // This enables interesting features under Product Analytics > Heatmap.
      trackUserInteractions: true,
      // Mask all actions to avoid exposing patient data.
      // Despite having a BAA with Datadog, we must refrain from disclosing patient information to external parties.
      // Patient data could be exposed if a user clicks on a data element with the patient name.
      enablePrivacyForActionName: true,
      // Enable session replay.
      sessionReplaySampleRate: 100,
      // The 'mask' mode conceals all HTML text, user inputs, images, links, and data-* attributes.
      // Session replay does not support `video`, `audio`, or `canvas` under any circumstances.
      // Despite having a BAA with Datadog, we must refrain from disclosing patient information,
      // videos, images, or audio to external parties.
      defaultPrivacyLevel: 'mask',
    })

    // Minimally identify the user
    datadogRum.setUser({
      id: context.auth0User?.sub,
      orgId: context.auth0User?.[CLAIMS.ORG_ID],
      isApellaEmployee: context.user.isApellaEmployee,
    })

    DATADOG_RUM_INITIALIZED = true
  }
}

const isRumResourceEventContext = (
  context: RumEventDomainContext
): context is RumFetchResourceEventDomainContext => {
  return 'requestInit' in context
}

const isLocalhost = () => {
  return location.hostname == 'localhost' || location.hostname == '127.0.0.1'
}
