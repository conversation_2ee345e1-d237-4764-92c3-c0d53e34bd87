import { DataStrategyMatch, LoaderFunctionArgs } from 'react-router'

import { ApellaRouteContext } from '../types'

/**
 * Route middleware function arguments
 */
export type MiddlewareFunctionArgs = LoaderFunctionArgs<
  Partial<ApellaRouteContext>
> & {
  matches: DataStrategyMatch[]
}

/**
 * Route middleware function signature
 */
export type MiddlewareFunction = (
  args: MiddlewareFunctionArgs,
  context: unknown
) => Promise<void>

export { apolloClientMiddleware } from './apollo'
export { auth0Middleware } from './auth0'
export { cubejsMiddleware } from './cube'
export { ensureAuthenticated } from './ensureAuthenticated'
export { launchDarklyMiddleware } from './launchDarkly'
export { permissionCheckRedirect } from './permissionCheckRedirect'
