import { replace } from 'react-router'

import { GenericError } from '@auth0/auth0-spa-js'

import { hasAuthParams } from 'src/api/Auth0ProviderWithClient'
import { isBoardsHost } from 'src/pages/Boards'
import { logger } from 'src/utils/exceptionLogging'

import { MiddlewareFunctionArgs } from '.'
import { ACTIVE_ORG_ID_KEY } from '../constants'
import { ApellaRouteContext } from '../types'

type AppState = {
  returnTo: string
}

// Authentication flow should only happen once on initial page load
let didInitialize = false

export const auth0Middleware = async (
  { request }: MiddlewareFunctionArgs,
  context: ApellaRouteContext
) => {
  const url = new URL(request.url)
  const auth0Client = isBoardsHost(url.host)
    ? context.boardsAuth0
    : context.auth0
  context.auth0Client = auth0Client
  if (didInitialize) {
    context.auth0User = await auth0Client.getUser()
    return
  }
  didInitialize = true
  if (hasAuthParams(url.search)) {
    let returnTo = window.location.pathname
    try {
      const { appState } = await auth0Client.handleRedirectCallback<AppState>(
        url.href
      )
      returnTo = appState?.returnTo || returnTo
    } catch {
      localStorage.removeItem(ACTIVE_ORG_ID_KEY)
      await auth0Client.logout({
        openUrl: () => {
          replace('/')
        },
      })
    }
    context.auth0User = await auth0Client.getUser()
    throw replace(returnTo)
  }
  try {
    await auth0Client.getTokenSilently()
  } catch (e) {
    if (e instanceof GenericError && e.error !== 'login_required') {
      await auth0Client.loginWithRedirect({
        appState: {
          returnTo: `${url.pathname}${url.search || ''}`,
        },
      })
    } else {
      logger.error('Authorization error', { errorMessage: e })
      await auth0Client.logout({
        openUrl: () => {
          replace('/')
        },
      })
    }
  }
  context.auth0User = await auth0Client.getUser()
  return
}
