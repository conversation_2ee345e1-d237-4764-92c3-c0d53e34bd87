import cubejs from '@cubejs-client/core'

import { getAccessTokenSilentlyOrLogout } from 'src/api/clients/auth0'
import { settings } from 'src/settings'

import { MiddlewareFunctionArgs } from '.'
import { ApellaRouteContext } from '../types'

export const cubejsMiddleware = async (
  _args: MiddlewareFunctionArgs,
  context: ApellaRouteContext
) => {
  const { auth0Client } = context
  if (!auth0Client) {
    return
  }

  context.cube = cubejs(
    async () => await getAccessTokenSilentlyOrLogout(auth0Client),
    {
      apiUrl: `${settings.cubejs.url}/cubejs-api/v1`,
    }
  )
}
