name-template: 'v$RESOLVED_VERSION'
tag-template: 'v$RESOLVED_VERSION'
categories:
  - title: '💔 Breaking Changes'
    labels:
      - 'major'
      - 'breaking'
  - title: '🚀 Features'
    labels:
      - 'feature'
      - 'enhancement'
  - title: '🐛 Bug Fixes'
    labels:
      - 'fix'
      - 'bugfix'
      - 'bug'
  - title: '🧰 Maintenance'
    label: 'chore'
change-template: '- $TITLE @$AUTHOR (#$NUMBER)'
change-title-escapes: '\<*_&' # You can add # and @ to disable mentions, and add ` to disable code blocks.
version-resolver:
  major:
    labels:
      - 'major'
  minor:
    labels:
      - 'minor'
      - 'enhancement'
  patch:
    labels:
      - 'patch'
  default: patch
autolabeler:
  - label: 'chore'
    branch:
      - '/chore\.+/'
    title:
      - '/chore/i'
    body:
      - '/\[\s*x\s*\] Chore/ig'
  - label: 'bug'
    branch:
      - '/bug\/.+/'
    title:
      - '/fix/i'
    body:
      - '/\[\s*x\s*\] Bug fix/ig'
  - label: 'enhancement'
    branch:
      - '/feature\/.+/'
    title:
      - '/feature/i'
    body:
      - '/\[\s*x\s*\] New feature/ig'
  - label: 'major'
    body:
      - '/\[\s*x\s*\] Breaking change/ig'
template: |
  ## Changes

  $CHANGES
