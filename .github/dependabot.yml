version: 2
updates:
  # Keep npm dependencies up-to-date
  - package-ecosystem: 'npm'
    directories:
      - '/'
      - 'apps/*/'
      - 'libs/*/'
    schedule:
      interval: 'weekly'
      day: 'tuesday'
      time: '10:00'
      timezone: 'America/Los_Angeles'
    groups:
      lint:
        patterns:
          - 'eslint'
          - '@eslint/*'
          - 'eslint-plugin-*'
          - 'prettier'
      typescript:
        patterns:
          - 'typescript'
          - '@typescript-eslint/*'
      production:
        dependency-type: 'production'
        update-types:
          - 'patch'
          - 'minor'
      development:
        dependency-type: 'development'
        update-types:
          - 'patch'
          - 'minor'
      major-versions:
        patterns:
          - '*'
        update-types:
          - 'major'
    ignore:
      - dependency-name: '@types/luxon'
        versions: ['>=3']
      - dependency-name: 'madge'
        versions: ['>=7']
      - dependency-name: '@graphql-codegen/typed-document-node'
      - dependency-name: 'graphql-codegen-typescript-mock-data'
  # Keep GitHub Actions up-to-date
  - package-ecosystem: 'github-actions'
    directory: '/'
    schedule:
      interval: 'daily' #'monthly'
    groups:
      github-actions:
        patterns:
          - '*'
