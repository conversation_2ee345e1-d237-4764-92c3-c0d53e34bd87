name: Set up Node
description: Installs node at the specified version in .nvmrc, authenticates with gcp, caches node modules, and installs dependencies

inputs:
  GOOGLE_APPLICATION_CREDENTIALS:
    required: false
    description: The google application credentials to use to login. Default should be secrets.GOOGLE_APPLICATION_CREDENTIALS
  SKIP_CACHE:
    required: false
    description: If true, the node_modules directory will not be cached.

runs:
  using: 'composite'

  steps:
    - name: Authenticate to Google Cloud
      id: google-auth
      if: ${{ !contains(runner.name, 'gh-runner') && inputs.GOOGLE_APPLICATION_CREDENTIALS }}
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ inputs.GOOGLE_APPLICATION_CREDENTIALS }}
        token_format: 'access_token'

    - name: Set up Node
      id: set-up-node
      uses: actions/setup-node@v4
      with:
        node-version-file: .nvmrc
        cache: 'yarn'

    # In addition to the upstream setup-node yarn cache, we separately cache node_modules because `yarn
    # install` ends up copying every file from the yarn cache into the node_modules directory otherwise. In
    # practice, this copying seems to take about twice as long as the untarring of the cache tarball into
    # the yarn cache in the first place. Directly caching the node_modules directory eliminates that
    # extra copy, reducing the total time that this action takes by about 60%.
    #
    # Note: while the actions/cache documentation specifically recommends not caching the
    # node_modules directory (https://github.com/actions/cache/blob/611465405cc89ab98caca2c42504d9289fb5f22e/examples.md#node---npm),
    # we don't use `npm ci` and adding the node-version to the cache key suffices to solve the
    # problem of node version compatibility.
    - name: Cache node_modules directory
      if: ${{ !inputs.SKIP_CACHE }}
      id: cache-node-modules
      uses: actions/cache@v4
      with:
        path: node_modules
        key: node-modules-${{steps.set-up-node.outputs.node-version}}-${{ hashFiles('**/yarn.lock') }}

    - name: Install Dependencies
      run: yarn install --frozen-lockfile
      shell: bash
