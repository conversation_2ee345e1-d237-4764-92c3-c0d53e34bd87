#
# NOTE: The order of the paths in this file is important for Sentry alerting. Sentry will assign
# ownership based on the _last_ matching rule in the CODEOWNERS list that matches _any_ part of the
# stack trace. This means that CODEOWNERS needs to be sorted from more generic to more specific.
#
# For more information, see the Sentry documentation:
# https://docs.sentry.io/product/issues/ownership-rules/#evaluation-flow
# https://github.com/getsentry/sentry/issues/47216
#

# Platform Services
# Note: these are separate for Sentry ownership matching reasons (see comment at top of file)
api_server/logging/                          @Apella-Technology/platform-services
api_server/tracing/                          @Apella-Technology/platform-services
api_server/app_service_provider/             @Apella-Technology/platform-services
api_server/graphql/                          @Apella-Technology/platform-services
auth/                                        @Apella-Technology/platform-services
utils/                                       @Apella-Technology/platform-services


# ORion
api_server/services/annotation_tasks/        @Apella-Technology/orion
api_server/services/block/                   @Apella-Technology/orion
api_server/services/case_duration/           @Apella-Technology/orion
api_server/services/highlights/              @Apella-Technology/orion
api_server/services/measurement_periods/     @Apella-Technology/orion
api_server/services/user_filter_views/       @Apella-Technology/orion

# EHR
api_server/services/anesthesia/              @Apella-Technology/ehr
api_server/services/case/                    @Apella-Technology/ehr
api_server/services/case_derived_properties/ @Apella-Technology/ehr
api_server/services/ehr_interfaces/          @Apella-Technology/ehr
api_server/services/observations/            @Apella-Technology/ehr
api_server/services/patients/                @Apella-Technology/ehr
api_server/services/procedures/              @Apella-Technology/ehr
api_server/services/service_lines/           @Apella-Technology/ehr
api_server/services/staff/                   @Apella-Technology/ehr
api_server/services/staff_role/              @Apella-Technology/ehr
mock_ehr_scribe_service/                     @Apella-Technology/ehr

# Platform Services
api_server/metrics/                          @Apella-Technology/platform-services
api_server/services/camera/                  @Apella-Technology/platform-services
api_server/services/cluster/                 @Apella-Technology/platform-services
api_server/services/healthz/                 @Apella-Technology/platform-services
api_server/services/media/                   @Apella-Technology/platform-services
api_server/services/meta/                    @Apella-Technology/platform-services
api_server/services/metrics/                 @Apella-Technology/platform-services
api_server/services/organization/            @Apella-Technology/platform-services
api_server/services/room/                    @Apella-Technology/platform-services
api_server/services/site/                    @Apella-Technology/platform-services
api_server/services/users/                   @Apella-Technology/platform-services
api_server/services/utils/                   @Apella-Technology/platform-services
mock_auth0/                                  @Apella-Technology/platform-services
mock_media_asset_service/                    @Apella-Technology/platform-services

# Real Time
api_server/services/apella_case/             @Apella-Technology/realtime
api_server/services/boards/                  @Apella-Technology/realtime
api_server/services/contact_information/     @Apella-Technology/realtime
api_server/services/launch_darkly/           @Apella-Technology/realtime
api_server/services/plan/                    @Apella-Technology/realtime
api_server/services/staffing_needs/          @Apella-Technology/realtime
api_server/services/turnover/                @Apella-Technology/realtime

# DS / ML
api_server/services/case_forecasts/          @Apella-Technology/ds-ml
api_server/services/events/                  @Apella-Technology/ds-ml
api_server/services/objects/                 @Apella-Technology/ds-ml
api_server/services/phases/                  @Apella-Technology/ds-ml
