name: Chromatic
on:
  push:
    branches: [main]
  pull_request:
  merge_group:

jobs:
  Chromatic-Deployment:
    runs-on: 'ubuntu-latest'
    strategy:
      matrix:
        project:
          - name: React Router Storybook
            path: libs/react-router-storybook
            token_name: CHROMATIC_PROJECT_TOKEN_REACT_ROUTER_STORYBOOK
            grep: '@apella/react-router-storybook'
          - name: Component Library
            path: libs/component-library
            token_name: CHROMATIC_PROJECT_TOKEN_COMPONENT_LIBRARY
            grep: '@apella/component-library'
          - name: Web Dashboard
            path: apps/web-dashboard
            token_name: CHROMATIC_PROJECT_TOKEN_WEB_DASHBOARD
            grep: 'web-dashboard'
            graphql: true

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Skip Check
        # We need the `|| true` here as count of 0 causes a 0 exit code without it,
        # and adding the `|| true` doesn't impact the output as it will still output `0`
        run: |
          RUN_CHROMATIC=$(yarn nx show projects --affected --json | grep '${{ matrix.project.grep }}' -c || true)
          echo $RUN_CHROMATIC
          echo "RUN_CHROMATIC=$RUN_CHROMATIC" >> "$GITHUB_ENV"

      - name: Generate GraphQL Types
        if: ${{ matrix.project.graphql }}
        run: |
          cd ${{ matrix.project.path }}
          APOLLO_KEY=${{ secrets.APOLLO_KEY }} yarn graphql:gen

      - name: Run Chromatic
        if: ${{ env.RUN_CHROMATIC != '0' }}
        uses: chromaui/action@latest
        with:
          projectToken: ${{ secrets[matrix.project.token_name] }}
          workingDir: ${{ matrix.project.path }}
