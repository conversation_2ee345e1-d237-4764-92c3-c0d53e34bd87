name: 'Build Container'

on:
  pull_request:

env:
  CONTAINER_REGISTRY: us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry
  DOCKER_CACHE_LOCATION: /tmp/.buildx-cache
  DOCKER_CACHE_NEW_LOCATION: /tmp/.buildx-cache-new

jobs:
  Build-Web-Internal-Container:
    runs-on: 'ubuntu-latest'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Publish Container
        uses: Apella-Technology/build-docker-container@v1
        with:
          # We use a secret which only gives access to Artifact Registry, and thus more restrictive than `secrets.GOOGLE_APPLICATION_CREDENTIALS`
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          CONTAINER_REPOSITORY: web-internal
          DOCKER_BUILD_ARGS: |
            APELLA_ENV=development
            APOLLO_KEY=${{ secrets.APOLLO_KEY }}
            APP_VERSION=${{ github.sha }}
          DOCKERFILE: ./apps/web-internal/Dockerfile

  Build-Web-Dashboard-Container:
    runs-on: 'ubuntu-latest'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Publish Container
        uses: Apella-Technology/build-docker-container@v1
        with:
          # We use a secret which only gives access to Artifact Registry, and thus more restrictive than `secrets.GOOGLE_APPLICATION_CREDENTIALS`
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          CONTAINER_REPOSITORY: web-dashboard
          DOCKER_BUILD_ARGS: |
            APELLA_ENV=development
            APOLLO_KEY=${{ secrets.APOLLO_KEY }}
            APP_VERSION=${{ github.sha }}
          DOCKERFILE: ./apps/web-dashboard/Dockerfile
