name: Publish Client Packages

on:
  workflow_call:
    inputs:
      version:
        description: Release version to deploy
        required: true
        type: string
      ARTIFACT_REGISTRY_PROJECT:
        description: Artifact registry GCP Project
        required: true
        type: string
    secrets:
      ARTIFACT_REGISTRY_SA_CREDENTIALS:
        description: Artifact registry credentials
        required: true

jobs:
  Package-Modules:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_project: ${{ inputs.ARTIFACT_REGISTRY_PROJECT }}
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          enable_package_uploads: true
          poetry_version: 2.1.1
          python_version: "3.10"

      - name: Inject version
        run: printf "${{ inputs.version }}" > version

      - name: Build client packages
        run: poetry run make build-client-packages

      # In order for upload-packages to work, we need to create a credentials file that we can pass
      # via environment variable to twine that allows the Google auth keyring plugin to authenticate
      # against our private repository.
      - id: google-auth
        name: "Authenticate to Google Cloud"
        uses: "google-github-actions/auth@v1"
        with:
          credentials_json: "${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}"
          export_environment_variables: false

      - name: Upload client packages
        env:
          PYTHON_ARTIFACT_REGISTRY: "${{ vars.PYTHON_ARTIFACT_REGISTRY }}"
          GOOGLE_APPLICATION_CREDENTIALS: "${{ steps.google-auth.outputs.credentials_file_path }}"
        run: poetry run make upload-client-packages
        shell: bash
