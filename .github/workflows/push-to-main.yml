name: On push to main

# Ensure that each Docker image build happens in sequence so that the "main" tag that's applied
# always refers to the HEAD of main.
concurrency: push-to-main

on:
  push:
    branches: [main]

jobs:
  Run-Staging-SQL-Migrations:
    uses: ./.github/workflows/run-migrations.yml
    with:
      ref: ${{ github.sha }}
      GCP_PROJECT: staging-web-api-3efef9
      APELLA_ENV: staging
      SQL_INSTANCE_NAME: staging-web-api-3efef9:us-central1:staging-postgres-02
      GITHUB_RUNNER_ENV: nonprod

  # Build a new image tagged with the "main" branch. This has the side-effect of causing an
  # asynchronous deployment to happen for staging via ArgoCD image updater.
  Build:
    uses: ./.github/workflows/submit-build.yml
    with:
      version: ${{ github.sha }}
      CONTAINER_REGISTRY: us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry
      GITHUB_RUNNER_ENV: prod

    # We don't want the image to build until migrations are done to avoid a situation where the new
    # image could be deployed in staging before the SQL migrations have completed, leaving it
    # in a broken state until the migrations complete.
    needs:
      - Run-Staging-SQL-Migrations

  Deploy-Dev:
    uses: ./.github/workflows/deploy-dev.yml
    with:
      publish_client_packages: false
      build_image: false # No sense in rebuilding the image
    secrets:
      ARTIFACT_REGISTRY_SA_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
      ARGOCD_DEV_USERNAME: ${{ secrets.ARGOCD_DEV_USERNAME }}
      ARGOCD_DEV_PASSWORD: ${{ secrets.ARGOCD_DEV_PASSWORD }}
