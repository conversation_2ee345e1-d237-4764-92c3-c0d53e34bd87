# Used to test deployment to Google Cloud Storage during PR development.
# It is not triggered by any events, but can be manually triggered from the Actions tab
name: Test Deploy

on:
  workflow_dispatch: { }

jobs:
  Affected-Projects:
    runs-on: ubuntu-latest
    outputs:
      affected_projects: ${{ steps.affected.outputs.affected_projects }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Setup Node
        uses: ./.github/actions/setup-node
        with:
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}

      - name: List affected projects
        id: affected
        run: |
          affected_projects=$(yarn --silent nx show projects --affected --json)
          echo $affected_projects
          echo "affected_projects=$affected_projects" >> "$GITHUB_OUTPUT"

  Deploy-Web-Internal-Dev:
    if: contains(fromJSON(needs.Affected-Projects.outputs.affected_projects), 'web-internal')
    needs: Affected-Projects
    uses: ./.github/workflows/deploy-app-to-gcp-storage.yml
    with:
      application_name: web-internal
      environment: development
      bucket_name: gs://internal.dev.apella.io
    secrets: inherit
