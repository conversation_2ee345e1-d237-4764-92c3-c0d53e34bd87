name: Validate

on:
  push:
    branches: [main]
  pull_request:
  merge_group:

env:
  APOLLO_KEY: ${{ secrets.APOLLO_KEY }}

# List of required checks here:
# https://github.com/Apella-Technology/infrastructure/blob/main/github/shared-product.tf
jobs:
  Build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Build
        run: yarn nx affected -t build

  Typescript:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Typecheck
        run: yarn nx affected -t tsc

  Lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Lint
        run: yarn nx affected -t lint

  Unit-Tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Test
        run: yarn nx affected -t test

  Circular-Dependency-Check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Circular Dependency Check
        run: yarn nx affected -t circular-dependency:check
