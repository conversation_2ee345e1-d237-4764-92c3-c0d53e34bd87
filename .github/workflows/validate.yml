name: Validate

on:
  push:
    branches: [main]
  pull_request:
  merge_group:

jobs:
  Python-Linter:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 2.1.1
          python_version: "3.10"

      - name: Lint
        run: make lint

  Python-Unit-Tests:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 2.1.1
          python_version: "3.10"

      - name: Run unit tests
        run: poetry run make test-cov

      - name: Pytest coverage comment
        if: ${{ github.actor != 'dependabot[bot]' }} # Don't comment on PRs from dependabot
        uses: Misha<PERSON>av/pytest-coverage-comment@main
        with:
          junitxml-path: ./pytest.xml
          pytest-coverage-path: ./pytest-coverage.txt
          report-only-changed-files: true

  Component-Tests:
    runs-on: ubuntu-22.04-16-cores

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 2.1.1
          python_version: "3.10"

      - name: Run tests
        run: poetry run make component-test

  Build-Client-Packages:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 2.1.1
          python_version: "3.10"

      - name: Inject version
        run: printf "0.1.0" > version

      - name: Build client packages
        run: poetry run make build-client-packages

  No-Pending-SQL-Migrations:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 2.1.1
          python_version: "3.10"

      - name: Run postgres
        run: poetry run make start-postgres-local

      - name: Validate no pending autogenerated SQL upgrades
        env:
          SQL_USER: postgres
          SQL_DB: postgres
          SQL_PASSWORD: password
          SQL_HOST: localhost
        run: |
          poetry run alembic heads
          poetry run alembic upgrade head
          poetry run alembic check

  No-Pending-Schema-Changes:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 2.1.1
          python_version: "3.10"

      - name: re-generate schema
        run: poetry run make generate-client-types

      - name: Validate no pending schema changes
        run: |
          if [[ $(git status -uno --porcelain) ]]; then
            echo "Schema changes detected. Please run 'make generate-client-types' and commit the changes."
            exit 1
          fi

  Notify-Breaking-Schema-Changes:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Apollo Rover
        run: |
          curl -sSL https://rover.apollo.dev/nix/v0.26.0-rc.1 | sh
          echo "$HOME/.rover/bin" >> $GITHUB_PATH

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 2.1.1
          python_version: "3.10"

      - name: Generate GraphQL
        run: poetry run make generate-graphql

      - name: Run schema check against current
        env:
          APOLLO_KEY: ${{ secrets.APOLLO_KEY }}
          APOLLO_VCS_COMMIT: ${{ github.event.pull_request.head.sha }}
        run: |
          rover graph check api-server-ncdndm@current --schema ./schema.graphql > schema_check_output.txt 2>&1 || true

      - name: Extract Apollo Studio link
        id: extract_link
        run: |
          # Extract the first URL from the output file
          APOLLO_LINK=$(grep -o 'https://studio\.apollographql\.com/[^ ]*' schema_check_output.txt | head -n 1)
          echo "url=$APOLLO_LINK" >> $GITHUB_OUTPUT

      - name: Extract full output and detect errors
        id: extract_errors
        run: |
          if grep -E 'FAIL|error' schema_check_output.txt; then
            ERROR_CONTENT=$(cat schema_check_output.txt)
          else
            ERROR_CONTENT=""
          fi
          echo "content<<EOF" >> $GITHUB_OUTPUT
          echo "$ERROR_CONTENT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Comment on the PR with schema errors
        if: steps.extract_errors.outputs.content != ''
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          recreate: true
          header: "Schema Check Results"
          message: |
            ## 🚨 Warning: API breaking changes detected 🚨
            ```
            ${{ steps.extract_errors.outputs.content }}
            ```
            [View more details](${{ steps.extract_link.outputs.url }}) in Apollo Studio

      - name: Remove previous comment if no errors
        if: steps.extract_errors.outputs.content == ''
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          header: "Schema Check Results"
          delete: true
