name: Deploy Application to GCP Storage

on:
  workflow_call:
    inputs:
      application_name:
        description: Name of the application to deploy
        required: true
        type: string
      environment:
        description: Apella Environment for deployment
        required: true
        type: string
      bucket_name:
        description: Google Cloud Storage bucket
        required: true
        type: string
    secrets:
      GOOGLE_APPLICATION_CREDENTIALS:
        description: Google Cloud Service Account credentials
        required: true
      APOLLO_KEY:
        description: Apollo API key
        required: true

jobs:
  Deploy:
    runs-on: ubuntu-latest
    environment: ${{inputs.application_name}}-${{ inputs.environment }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: ./.github/actions/setup-node
        with:
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}

      - name: Bundle
        run: APP_VERSION=${GITHUB_SHA::8} APELLA_ENV=${{ inputs.environment }} yarn nx run ${{ inputs.application_name }}:build
        env:
          APOLLO_KEY: ${{ secrets.APOLLO_KEY }}

      - id: google-auth
        name: Authenticate to Google Cloud
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
          # Don't export environment variables to refrain from having unintended consequences of overwriting environment variables
          export_environment_variables: false

      - name: Deploy
        run: gcloud storage cp --cache-control no-cache -z html,js,txt -r dist/* ${{ inputs.bucket_name}}
        working-directory: ./apps/${{ inputs.application_name }}
        env:
          GOOGLE_APPLICATION_CREDENTIALS: '${{ steps.google-auth.outputs.credentials_file_path }}'

      - name: Upload Source Maps
        if: ${{ inputs.environment == 'production' }}
        run: SENTRY_RELEASE=${GITHUB_SHA::8} yarn sourcemaps:sentry
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
        working-directory: ./apps/${{ inputs.application_name }}
