name: Deploy Application

on:
  workflow_call:
    inputs:
      application_name:
        description: Name of the application to deploy to match path of ./apps/{application_name}. ie `web-internal`
        required: true
        type: string
      bucket_root_name:
        description: Google Cloud Storage bucket root name. ie 'internal'
        required: true
        type: string
    secrets:
      GOOGLE_APPLICATION_CREDENTIALS:
        description: Google Cloud Service Account credentials
        required: true
      APOLLO_KEY:
        description: Apollo API key
        required: true
      SENTRY_AUTH_TOKEN:
        description: Sentry API key
        required: true

jobs:
  Deploy-Dev:
    uses: ./.github/workflows/deploy-app-to-gcp-storage.yml
    with:
      application_name: ${{ inputs.application_name }}
      environment: development
      bucket_name: gs://${{ inputs.bucket_root_name }}.dev.apella.io
    secrets: inherit

  Deploy-Staging:
    uses: ./.github/workflows/deploy-app-to-gcp-storage.yml
    with:
      application_name: ${{ inputs.application_name }}
      environment: staging
      bucket_name: gs://${{ inputs.bucket_root_name }}.staging.apella.io
    secrets: inherit

  Deploy-Production:
    needs:
      - Deploy-Dev
      - Deploy-Staging
    uses: ./.github/workflows/deploy-app-to-gcp-storage.yml
    with:
      application_name: ${{ inputs.application_name }}
      environment: production
      bucket_name: gs://${{ inputs.bucket_root_name }}.apella.io
    secrets: inherit
