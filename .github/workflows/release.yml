name: Release

on:
  release:
    types: [published]

# Ensure we only release one build at a time
concurrency: release

jobs:
  Publish-Client-Packages:
    uses: ./.github/workflows/publish-client-packages.yml
    with:
      version: ${{ github.event.release.tag_name }}
      ARTIFACT_REGISTRY_PROJECT: ${{ vars.ARTIFACT_REGISTRY_PROJECT }}
    secrets:
      ARTIFACT_REGISTRY_SA_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}

  Run-Production-SQL-Migrations:
    uses: ./.github/workflows/run-migrations.yml
    with:
      ref: ${{ github.sha }}
      GCP_PROJECT: prod-web-api-7f60bf
      APELLA_ENV: prod
      SQL_INSTANCE_NAME: prod-web-api-7f60bf:us-central1:prod-postgres-01
      GITHUB_RUNNER_ENV: prod

  Build-Production:
    uses: ./.github/workflows/submit-build.yml
    with:
      version: ${{ github.event.release.tag_name }}
      CONTAINER_REGISTRY: us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry
      GITHUB_RUNNER_ENV: prod
    needs:
      - Run-Production-SQL-Migrations
