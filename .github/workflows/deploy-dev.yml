name: Deploy Dev

on:
  workflow_dispatch:
    inputs:
      publish_client_packages:
        description: "Publish Python client packages"
        required: true
        type: boolean
      build_image:
        description: "Build the container image"
        required: false
        type: boolean
        default: true
  workflow_call:
    inputs:
      publish_client_packages:
        description: "Publish Python client packages"
        required: true
        type: boolean
      build_image:
        description: "Build the container image"
        required: false
        type: boolean
        default: true
    secrets:
      ARTIFACT_REGISTRY_SA_CREDENTIALS:
        required: true
      ARGOCD_DEV_USERNAME:
        required: true
      ARGOCD_DEV_PASSWORD:
        required: true

jobs:
  Publish-Client-Packages:
    if: ${{ inputs.publish_client_packages }}
    uses: ./.github/workflows/publish-client-packages.yml
    with:
      # Pip doesn't allow git hashes in the version, so we cannot use ${{ github.sha }}
      version: 0.0dev-${{ github.run_id }}
      ARTIFACT_REGISTRY_PROJECT: ${{ vars.ARTIFACT_REGISTRY_PROJECT }}
    secrets:
      ARTIFACT_REGISTRY_SA_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}

  Run-SQL-Migrations:
    uses: ./.github/workflows/run-migrations.yml
    with:
      ref: ${{ github.sha }}
      GCP_PROJECT: dev-web-api-72f12b
      APELLA_ENV: dev
      SQL_INSTANCE_NAME: dev-web-api-72f12b:us-central1:dev-postgres-01
      GITHUB_RUNNER_ENV: nonprod-test

  Build:
    if: ${{ inputs.build_image }}
    uses: ./.github/workflows/submit-build.yml
    with:
      version: ${{ github.sha }}
      CONTAINER_REGISTRY: us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry
      GITHUB_RUNNER_ENV: prod

  Deploy:
    if: ${{ always() && needs.build.result != 'failure' }} # Run even if the Build step was skipped
    runs-on:
      - self-hosted
      - nonprod-test
    steps:
      - name: Setup ArgoCD CLI
        uses: Apella-Technology/setup-argocd-cli@v1
        with:
          ARGOCD_VERSION: v2.8.6

      - name: ArgoCD login
        run: |
          sudo argocd login argocd.dev.internal.apella.io --password ${{ secrets.ARGOCD_DEV_PASSWORD }} --username ${{ secrets.ARGOCD_DEV_USERNAME }} --loglevel debug --grpc-web

      - name: Deploy latest version
        run: |
          argocd app set api-server -p image.tag=${{ github.sha }}
          argocd app set notification-processor -p image.tag=${{ github.sha }}
          argocd app set notion-daemon -p image.tag=${{ github.sha }}
    needs:
      - Build
      - Run-SQL-Migrations
