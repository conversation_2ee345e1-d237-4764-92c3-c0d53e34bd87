name: Deploy

on:
  push:
    branches: [main]

jobs:
  Affected-Projects:
    runs-on: ubuntu-latest
    outputs:
      affected_projects: ${{ steps.affected.outputs.affected_projects }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - name: Setup Node
        uses: ./.github/actions/setup-node
        with:
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}

      - name: List affected projects
        id: affected
        run: |
          affected_projects=$(yarn --silent nx show projects --affected --json)
          echo $affected_projects
          echo "affected_projects=$affected_projects" >> "$GITHUB_OUTPUT"

  Deploy-Web-Internal:
    needs: Affected-Projects
    if: contains(fromJSON(needs.Affected-Projects.outputs.affected_projects), 'web-internal')
    uses: ./.github/workflows/deploy-app.yml
    with:
      application_name: web-internal
      bucket_root_name: internal
    secrets: inherit

  Deploy-Web-Dashboard:
    needs: Affected-Projects
    if: contains(fromJ<PERSON><PERSON>(needs.Affected-Projects.outputs.affected_projects), 'web-dashboard')
    uses: ./.github/workflows/deploy-app.yml
    with:
      application_name: web-dashboard
      bucket_root_name: dashboard
    secrets: inherit
