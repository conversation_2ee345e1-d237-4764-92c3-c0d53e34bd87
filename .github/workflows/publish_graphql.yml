name: Publish Graphql

on:
  release:
    types: [published]
  push:
    branches: [main]

jobs:
  Publish-Graphql:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Apollo Rover
        run: |
          curl -sSL https://rover.apollo.dev/nix/v0.1.5 | sh
          echo "$HOME/.rover/bin" >> $GITHUB_PATH

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 2.1.1
          python_version: '3.10'

      - name: Generate GraphQL
        run: poetry run make generate-graphql

      - name: Upload GraphQL Schema
        run: |
          if [ $GITHUB_EVENT_NAME == "push" ] && [ $GITHUB_REF == "refs/heads/main" ]
          then
            rover graph publish api-server-ncdndm --schema ./schema.graphql
          else
            rover graph publish api-server-ncdndm@prod --schema ./schema.graphql
          fi

        env:
          APOLLO_KEY: ${{ secrets.APOLLO_KEY }}
