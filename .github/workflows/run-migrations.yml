name: Run Alembic migrations

on:
  workflow_call:
    inputs:
      ref:
        description: Git commit sha, branch, or tag
        required: true
        type: string
      GCP_PROJECT:
        required: true
        description: GCP Project
        type: string
      APELLA_ENV:
        required: true
        description: Environment
        type: string
      SQL_INSTANCE_NAME:
        required: true
        description: path to the SQL instance
        type: string
      GITHUB_RUNNER_ENV:
        required: true
        description: one of ('prod', 'nonprod')
        type: string

# Ensure that no more than one migration is concurrently running to any given SQL instance
concurrency: ${{ inputs.SQL_INSTANCE_NAME }}

jobs:
  Upgrade-SQL:
    runs-on:
      - self-hosted
      - ${{ inputs.GITHUB_RUNNER_ENV }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Debug print
        run: |
          echo "The active SA"
          gcloud auth list --filter=status:ACTIVE --format="value(account)"
          echo $RUNNER_TOOL_CACHE

      - name: setup python environment
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 2.1.1
          python_version: '3.10'

      - name: Remove gcloud proxy docker container because this github runner doesn't reset
        run: docker rm -f /gce-cloudsql-proxy

      - name: Set up gcloud proxy
        run: docker run -d --name gce-cloudsql-proxy -p 127.0.0.1:5432:5432 gcr.io/cloudsql-docker/gce-proxy:1.21.0 /cloud_sql_proxy -instances=${{ inputs.SQL_INSTANCE_NAME }}=tcp:0.0.0.0:5432

      - name: Checkout codebase at release tag, branch, or sha
        uses: actions/checkout@v3
        with:
          ref: ${{ inputs.ref }}

      - name: Upgrade SQL
        env:
          SQL_USER: api-server
          GCP_PROJECT: ${{ inputs.GCP_PROJECT }}
          SQL_PASSWORD_SECRET: projects/${{ inputs.GCP_PROJECT }}/secrets/${{ inputs.APELLA_ENV }}-api-user-password
          SQL_DATABASE: postgres
          SQL_HOST: localhost
        run: poetry run alembic upgrade head
