name: Release Management

on:
  push:
    # branches to consider in the event; optional, defaults to all
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize, edited]
jobs:
  update_draft_release:
    runs-on: ubuntu-22.04
    steps:
      # Drafts your next Release notes as Pull Requests are merged into "main"
      - uses: release-drafter/release-drafter@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # allows autolabeler to run without unmerged PRs from being added to draft
          disable-releaser: ${{ github.ref_name != github.event.repository.default_branch }}
