name: Test Deploy Dev

on:
  workflow_dispatch:
    inputs:
      publish_client_packages:
        description: "Publish Python client packages"
        required: true
        type: boolean
        default: false
      build_image:
        description: "Build the container image"
        required: false
        type: boolean
        default: false

jobs:
  Run-SQL-Migrations:
    uses: ./.github/workflows/run-migrations.yml
    with:
      ref: ${{ github.sha }}
      GCP_PROJECT: dev-web-api-72f12b
      APELLA_ENV: dev
      SQL_INSTANCE_NAME: dev-web-api-72f12b:us-central1:dev-postgres-01
      GITHUB_RUNNER_ENV: nonprod-test

  Deploy:
    if: ${{ always() }}
    runs-on:
      - self-hosted
      - nonprod-test
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Show runner info
        run: |
          echo "Running on host: $HOSTNAME"
          echo "Runner labels: $RUNNER_LABELS"
          echo "Runner name: $RUNNER_NAME"
          echo "Runner OS: $RUNNER_OS"
          echo "Runner temp: $RUNNER_TEMP"
          echo "Runner tool cache: $RUNNER_TOOL_CACHE"
          echo "Runner workspace: $RUNNER_WORKSPACE"
          
      - name: Echo success
        run: echo "Successfully ran on the test runner!"
    needs:
      - Run-SQL-Migrations
