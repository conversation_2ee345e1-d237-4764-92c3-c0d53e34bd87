{"name": "@apella/root", "version": "0.0.1", "license": "UNLICENSED", "private": true, "workspaces": ["apps/*", "libs/*"], "scripts": {"build": "nx run-many -t build", "circular-dependency:check": "nx run-many -t circular-dependency:check", "clean": "nx run-many -t clean && nx reset && rm -rf node_modules dist", "lint": "nx run-many -t lint", "format": "nx run-many -t format", "test": "nx run-many -t test", "tsc": "nx run-many -t tsc"}, "devDependencies": {"@apella/eslint-config-custom": "*", "@nx/js": "^21.1.2", "eslint": "^9.27.0", "nx": "^21.1.2", "prettier": "^3.5.3", "typescript": "~5.8.3"}}