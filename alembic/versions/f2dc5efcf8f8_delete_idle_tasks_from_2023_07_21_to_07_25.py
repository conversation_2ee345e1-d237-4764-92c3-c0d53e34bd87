"""delete idle tasks from 2023-07-21 to 07-25

Revision ID: f2dc5efcf8f8
Revises: 766fb14afd86
Create Date: 2023-07-25 14:35:01.941448

"""

from datetime import datetime

import sqlalchemy as sa
from sqlalchemy.sql import column, table

from alembic import op

# revision identifiers, used by Alembic.
revision = "f2dc5efcf8f8"
down_revision = "766fb14afd86"


annotationTask = table(
    "annotation_tasks",
    column("id", sa.String(32)),
    column("start_time", sa.DateTime(timezone=True)),
    column("end_time", sa.DateTime(timezone=True)),
    column("status", sa.String()),
)


def upgrade() -> None:
    op.execute(
        annotationTask.delete()
        .where(annotationTask.c.status == "NOT_STARTED")
        .where(annotationTask.c.start_time >= datetime.fromisoformat("2023-07-21"))
        .where(annotationTask.c.start_time <= datetime.fromisoformat("2023-07-25"))
    )


def downgrade() -> None:
    # ### do nothing ###
    pass
