"""EHR-545: add org_id primary key to case clf table

Revision ID: e4a40adc80e0
Revises: c5dab8feeb9c
Create Date: 2024-08-20 18:09:29.536477

"""

from datetime import datetime
import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "e4a40adc80e0"
down_revision = "2976fbd06a86"
branch_labels = None
depends_on = None


def upgrade():
    # Drop the existing foreign key constraint so that the primary key can be changed
    op.drop_constraint("cases_case_classification_types_id_fkey", "cases", type_="foreignkey")
    # Drop the existing primary key constraint
    op.execute(
        "ALTER TABLE case_classification_types DROP CONSTRAINT case_classification_types_pkey"
    )
    # Add the org_id column
    op.add_column(
        "case_classification_types",
        sa.Column("org_id", sa.String(), nullable=False, server_default="temporary"),
    )
    # Create a new composite primary key
    op.create_primary_key(
        "case_classification_types_pkey", "case_classification_types", ["org_id", "id"]
    )

    # the existing table data is not compatible with the new primary key constraint
    op.execute("DELETE FROM case_classification_types")

    UNKNOWN = {"Unknown": "CASE_CLASSIFICATION_UNKNOWN"}
    BASE_CLASSIFICATIONS: dict[str, str] = {
        **{
            "Emergent": "CASE_CLASSIFICATION_EMERGENT",
            "Urgent": "CASE_CLASSIFICATION_URGENT",
            "Elective": "CASE_CLASSIFICATION_ELECTIVE",
        },
        **UNKNOWN,
    }

    LEVEL_CLASSIFICATIONS: dict[str, str] = {
        **{
            "Level 1": "CASE_CLASSIFICATION_LEVEL1",
            "Level 2": "CASE_CLASSIFICATION_LEVEL2",
            "Level 3": "CASE_CLASSIFICATION_LEVEL3",
            "Level 4": "CASE_CLASSIFICATION_LEVEL4",
            "Level 5": "CASE_CLASSIFICATION_LEVEL5",
        },
        **UNKNOWN,
    }

    ORG_CASE_CLASSIFICATION_TYPE = {
        "health_first": BASE_CLASSIFICATIONS,
        "houston_methodist": BASE_CLASSIFICATIONS,
        "north_bay": {
            **BASE_CLASSIFICATIONS,
            "Newborn (Birth in healthcare facility)": "CASE_CLASSIFICATION_NEWBORN",
        },
        "nyu": {**BASE_CLASSIFICATIONS, "Add-On": "CASE_CLASSIFICATION_ADD_ON"},
        "tampa_general": LEVEL_CLASSIFICATIONS,
        "apella_internal_0": BASE_CLASSIFICATIONS,
        # dev orgs
        "scrubs": BASE_CLASSIFICATIONS,
        "greys_anatomy": BASE_CLASSIFICATIONS,
    }
    table_input = []
    for org_id, clf_name in ORG_CASE_CLASSIFICATION_TYPE.items():
        for name, clf_id in clf_name.items():
            table_input.append(
                {
                    "org_id": org_id,
                    "id": clf_id,
                    "name": name,
                }
            )

    curr_time_isofmt = datetime.now().isoformat()
    ARCHIVED_TAMPA_GENERAL_CLASSIFICATIONS = {
        "Emergent": "CASE_CLASSIFICATION_EMERGENT",
        "Urgent": "CASE_CLASSIFICATION_URGENT",
        "Elective": "CASE_CLASSIFICATION_ELECTIVE",
        "Expedited": "CASE_CLASSIFICATION_EXPEDITED",
    }
    for name, clf_id in ARCHIVED_TAMPA_GENERAL_CLASSIFICATIONS.items():
        table_input.append(
            {
                "org_id": "tampa_general",
                "id": clf_id,
                "name": name,
                "archived_time": curr_time_isofmt,
            }
        )

    # using a manual insert statement b/c meta.reflect can't be converted to sql by alembic
    values = ", ".join(
        f"('{row['org_id']}', '{row['id']}', '{row['name']}', '{row['archived_time']}')"
        if row.get("archived_time")
        else f"('{row['org_id']}', '{row['id']}', '{row['name']}', NULL)"
        for row in table_input
    )
    insert_statement = (
        f"INSERT INTO case_classification_types (org_id, id, name, archived_time) VALUES {values};"
    )
    op.execute(insert_statement)

    op.create_foreign_key(
        None,
        "cases",
        "case_classification_types",
        ["case_classification_types_id", "org_id"],
        ["id", "org_id"],
    )


def downgrade():
    # Drop the foreign key constraint added in the upgrade for org_id, but leave the original foreign key constraint on id
    op.drop_constraint(
        "cases_case_classification_types_id_org_id_fkey", "cases", type_="foreignkey"
    )
    op.execute("DELETE FROM case_classification_types")
    # Drop the new composite primary key constraint
    op.drop_constraint(
        "case_classification_types_pkey", "case_classification_types", type_="primary"
    )
    # Remove the org_id column
    op.drop_column("case_classification_types", "org_id")
    # Restore the original primary key constraint
    op.create_primary_key(None, "case_classification_types", ["id"])

    CASE_CLASSIFICATION_TYPES_TABLE_2024_08_19 = [
        {
            "created_time": datetime.fromisoformat("2022-09-26T11:46:11.152224-07:00"),
            "updated_time": datetime.fromisoformat("2022-09-26T11:46:11.152224-07:00"),
            "id": "CASE_CLASSIFICATION_ELECTIVE",
            "name": "Elective",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2022-09-26T11:46:11.152224-07:00"),
            "updated_time": datetime.fromisoformat("2022-09-26T11:46:11.152224-07:00"),
            "id": "CASE_CLASSIFICATION_URGENT",
            "name": "Urgent",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2022-09-26T11:46:11.152224-07:00"),
            "updated_time": datetime.fromisoformat("2022-09-26T11:46:11.152224-07:00"),
            "id": "CASE_CLASSIFICATION_EMERGENT",
            "name": "Emergent",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2023-05-03T14:47:52.353184-07:00"),
            "updated_time": datetime.fromisoformat("2023-05-03T14:47:52.353184-07:00"),
            "id": "CASE_CLASSIFICATION_UNKNOWN",
            "name": "Unknown",
            "description": None,
            "archived_time": None,
        },
        # this was removed from the new case_classification_types table b/c it's not present in the dev, staging, or prod `cases` table
        {
            "created_time": datetime.fromisoformat("2023-06-02T07:23:28.716589-07:00"),
            "updated_time": datetime.fromisoformat("2023-06-02T07:23:28.716589-07:00"),
            "id": "CASE_CLASSIFICATION_CALL_BACK",
            "name": "Call back",
            "description": None,
            "archived_time": datetime.fromisoformat("2023-06-02T07:23:28.826590-07:00"),
        },
        {
            "created_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "updated_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "id": "CASE_CLASSIFICATION_ACCIDENT",
            "name": "Accident",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "updated_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "id": "CASE_CLASSIFICATION_EMERGENCY",
            "name": "Emergency",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "updated_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "id": "CASE_CLASSIFICATION_LABOR_AND_DELIVERY",
            "name": "Labor and Delivery",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "updated_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "id": "CASE_CLASSIFICATION_NEWBORN",
            "name": "Newborn (Birth in healthcare facility)",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "updated_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "id": "CASE_CLASSIFICATION_ROUTINE",
            "name": "Routine",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "updated_time": datetime.fromisoformat("2024-03-03T12:07:34.276527-08:00"),
            "id": "CASE_CLASSIFICATION_EXPEDITED",
            "name": "Expedited",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2023-06-02T07:23:28.716589-07:00"),
            "updated_time": datetime.fromisoformat("2023-06-02T07:23:28.716589-07:00"),
            "id": "CASE_CLASSIFICATION_ADD_ON",
            "name": "Add on",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "updated_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "id": "CASE_CLASSIFICATION_LEVEL1",
            "name": "Level 1",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "updated_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "id": "CASE_CLASSIFICATION_LEVEL2",
            "name": "Level 2",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "updated_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "id": "CASE_CLASSIFICATION_LEVEL3",
            "name": "Level 3",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "updated_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "id": "CASE_CLASSIFICATION_LEVEL4",
            "name": "Level 4",
            "description": None,
            "archived_time": None,
        },
        {
            "created_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "updated_time": datetime.fromisoformat("2024-07-24T15:55:17.723189-07:00"),
            "id": "CASE_CLASSIFICATION_LEVEL5",
            "name": "Level 5",
            "description": None,
            "archived_time": None,
        },
    ]

    # using a manual insert statement b/c meta.reflect can't be converted to sql by alembic
    values = ", ".join(
        f"('{row['created_time']}', '{row['updated_time']}', '{row['id']}', '{row['name']}', NULL, '{row['archived_time']}')"
        if row.get("archived_time")
        else f"('{row['created_time']}', '{row['updated_time']}', '{row['id']}', '{row['name']}', NULL, NULL)"
        for row in CASE_CLASSIFICATION_TYPES_TABLE_2024_08_19
    )
    insert_statement = f"INSERT INTO case_classification_types (created_time, updated_time, id, name, description, archived_time) VALUES {values};"
    op.create_foreign_key(
        None,
        "cases",
        "case_classification_types",
        ["case_classification_types_id"],
        ["id"],
    )
    op.execute(insert_statement)
