"""New patient and case classifications from Health First

Revision ID: d896e44cc8be
Revises: cec0fb8dc67b
Create Date: 2023-05-02 11:30:48.528005

"""

from sqlalchemy import MetaData, Table

from alembic import op

# revision identifiers, used by Alembic.
revision = "d896e44cc8be"
down_revision = "cec0fb8dc67b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # get metadata from current connection
    # meta = MetaData(bind=op.get_bind())
    meta = MetaData()
    # pass in tuple with tables we want to reflect, otherwise whole database will get reflected
    meta.reflect(only=("case_classification_types",), bind=op.get_bind())

    # define table representation
    case_classification_types = Table("case_classification_types", meta)
    op.bulk_insert(
        case_classification_types,
        [
            {"id": "CASE_CLASSIFICATION_ADD_ON", "name": "Add on"},
            {"id": "CASE_CLASSIFICATION_CALL_BACK", "name": "Call back"},
            {"id": "CASE_CLASSIFICATION_UNKNOWN", "name": "Unknown"},
        ],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "DELETE FROM case_classification_types WHERE id IN ('CASE_CLASSIFICATION_ADD_ON', 'CASE_CLASSIFICATION_CALL_BACK', 'CASE_CLASSIFICATION_UNKNOWN')"
    )
    # ### end Alembic commands ###
