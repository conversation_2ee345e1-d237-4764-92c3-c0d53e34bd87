"""Add the data for tracking notifications errors

Revision ID: e87565f76105
Revises: 25aaa0fd8421
Create Date: 2024-03-26 15:56:06.556359

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "e87565f76105"
down_revision = "e3b70043c6e0"
branch_labels = None
depends_on = None


def upgrade():
    # Create a new table to store the old notifications
    op.create_table(
        "staff_event_notification_old_01",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("message_id", sa.String(), nullable=False),
        sa.Column(
            "staff_event_contact_information_id", postgresql.UUID(as_uuid=True), nullable=False
        ),
        sa.Column("event_id", sa.String(), nullable=False),
        sa.Column("case_id", sa.String(), nullable=True),
        sa.Column("event_time", sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["case_id"], ["cases.case_id"], name="notification_case_id_fkey", ondelete="SET NULL"
        ),
        sa.ForeignKeyConstraint(["event_id"], ["events.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["staff_event_contact_information_id"],
            ["staff_event_notification_contact_information.id"],
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint(
            "message_id",
            "staff_event_contact_information_id",
            name="pk_staffEventNotification_msgId_senciID_old",
        ),
    )
    op.create_index(
        op.f("ix_staff_event_notification_old_01_case_id"),
        "staff_event_notification_old_01",
        ["case_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_old_01_created_time"),
        "staff_event_notification_old_01",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_old_01_event_id"),
        "staff_event_notification_old_01",
        ["event_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_old_01_event_time"),
        "staff_event_notification_old_01",
        ["event_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_old_01_id"),
        "staff_event_notification_old_01",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_old_01_message_id"),
        "staff_event_notification_old_01",
        ["message_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_old_01_staff_event_contact_information_id"),
        "staff_event_notification_old_01",
        ["staff_event_contact_information_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_old_01_updated_time"),
        "staff_event_notification_old_01",
        ["updated_time"],
        unique=False,
    )

    # # Move the data from the old table to the new table
    op.execute(
        """
        INSERT INTO staff_event_notification_old_01 (
            created_time,
            updated_time,
            id,
            message_id,
            staff_event_contact_information_id,
            event_id,
            case_id,
            event_time
            )
        SELECT
            created_time,
            updated_time,
            id,
            message_id,
            staff_event_contact_information_id,
            event_id,
            case_id,
            event_time
        FROM staff_event_notification
        """
    )
    # Truncate the old table
    op.execute("TRUNCATE TABLE staff_event_notification")

    op.drop_constraint(
        "pk_staffEventNotification_msgId_staffEventContactInformationId",
        "staff_event_notification",
        type_="primary",
    )
    op.create_primary_key("pk_staffEventNotification_id_sen", "staff_event_notification", ["id"])
    op.create_unique_constraint(
        "uq_staffEventNotification_senciID_event_id_case_id",
        "staff_event_notification",
        ["staff_event_contact_information_id", "event_id", "case_id"],
    )
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum("SENT", "FAILED", "NOT_ATTEMPTED", "MISSED", name="sentnotificationstatus").create(
        op.get_bind()
    )
    op.create_table(
        "staff_event_notification_history",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("id", postgresql.UUID(as_uuid=True), autoincrement=False, nullable=False),
        sa.Column("message_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column(
            "staff_event_contact_information_id",
            postgresql.UUID(as_uuid=True),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("event_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("case_id", sa.String(), autoincrement=False, nullable=True),
        sa.Column("event_time", sa.DateTime(timezone=True), autoincrement=False, nullable=False),
        sa.Column(
            "sent_status",
            postgresql.ENUM(
                "SENT",
                "FAILED",
                "NOT_ATTEMPTED",
                "MISSED",
                name="sentnotificationstatus",
                create_type=False,
            ),
            server_default="SENT",
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "attempts", sa.Integer(), server_default="1", autoincrement=False, nullable=False
        ),
        sa.Column(
            "duplicated_id", postgresql.UUID(as_uuid=True), autoincrement=False, nullable=True
        ),
        sa.Column("is_excess", sa.Boolean(), autoincrement=False, nullable=False),
        sa.Column(
            "version",
            sa.Integer(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", "version"),
    )
    op.create_index(
        op.f("ix_staff_event_notification_history_case_id"),
        "staff_event_notification_history",
        ["case_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_history_created_time"),
        "staff_event_notification_history",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_history_event_id"),
        "staff_event_notification_history",
        ["event_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_history_event_time"),
        "staff_event_notification_history",
        ["event_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_history_id"),
        "staff_event_notification_history",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_history_message_id"),
        "staff_event_notification_history",
        ["message_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_history_staff_event_contact_information_id"),
        "staff_event_notification_history",
        ["staff_event_contact_information_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_history_updated_time"),
        "staff_event_notification_history",
        ["updated_time"],
        unique=False,
    )
    op.add_column(
        "staff_event_notification",
        sa.Column(
            "sent_status",
            postgresql.ENUM(
                "SENT",
                "FAILED",
                "NOT_ATTEMPTED",
                "MISSED",
                name="sentnotificationstatus",
                create_type=False,
            ),
            server_default="SENT",
            nullable=False,
        ),
    )
    op.add_column(
        "staff_event_notification",
        sa.Column("attempts", sa.Integer(), server_default="1", autoincrement=True, nullable=False),
    )
    op.add_column(
        "staff_event_notification",
        sa.Column("duplicated_id", postgresql.UUID(as_uuid=True), nullable=True),
    )
    op.add_column("staff_event_notification", sa.Column("is_excess", sa.Boolean(), nullable=False))
    op.add_column(
        "staff_event_notification",
        sa.Column("version", sa.Integer(), server_default=sa.text("1"), nullable=False),
    )
    op.alter_column(
        "staff_event_notification", "id", existing_type=postgresql.UUID(), nullable=False
    )
    op.create_foreign_key(
        "notification_duplicated_id_fkey",
        "staff_event_notification",
        "staff_event_notification",
        ["duplicated_id"],
        ["id"],
    )
    # ### end Alembic commands ###

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("staff_event_notification", sa.Column("sent_time", sa.DateTime(), nullable=True))
    op.add_column(
        "staff_event_notification_history",
        sa.Column("sent_time", sa.DateTime(), autoincrement=False, nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("staff_event_notification_history", "sent_time")
    op.drop_column("staff_event_notification", "sent_time")
    # ### end Alembic commands ###

    op.drop_constraint(
        "notification_duplicated_id_fkey", "staff_event_notification", type_="foreignkey"
    )
    op.drop_constraint(
        "pk_staffEventNotification_id_sen", "staff_event_notification", type_="primary"
    )
    op.drop_constraint(
        "uq_staffEventNotification_senciID_event_id_case_id",
        "staff_event_notification",
        type_="unique",
    )
    op.create_primary_key(
        "pk_staffEventNotification_msgId_staffEventContactInformationId",
        "staff_event_notification",
        ["message_id", "staff_event_contact_information_id"],
    )
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("staff_event_notification", "version")
    op.drop_column("staff_event_notification", "is_excess")
    op.drop_column("staff_event_notification", "duplicated_id")
    op.drop_column("staff_event_notification", "attempts")
    op.drop_column("staff_event_notification", "sent_status")
    op.drop_index(
        op.f("ix_staff_event_notification_history_updated_time"),
        table_name="staff_event_notification_history",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_history_staff_event_contact_information_id"),
        table_name="staff_event_notification_history",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_history_message_id"),
        table_name="staff_event_notification_history",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_history_id"),
        table_name="staff_event_notification_history",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_history_event_time"),
        table_name="staff_event_notification_history",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_history_event_id"),
        table_name="staff_event_notification_history",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_history_created_time"),
        table_name="staff_event_notification_history",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_history_case_id"),
        table_name="staff_event_notification_history",
    )
    op.drop_table("staff_event_notification_history")
    sa.Enum("SENT", "FAILED", "NOT_ATTEMPTED", "MISSED", name="sentnotificationstatus").drop(
        op.get_bind()
    )
    # ### end Alembic commands ###
    # Move the data from the old table to the new table
    op.execute(
        """
        INSERT INTO staff_event_notification (
            created_time,
            updated_time,
            id,
            message_id,
            staff_event_contact_information_id,
            event_id,
            case_id,
            event_time
            )
        SELECT
            created_time,
            updated_time,
            id,
            message_id,
            staff_event_contact_information_id,
            event_id,
            case_id,
            event_time
        FROM staff_event_notification_old_01
        """
    )
    op.drop_index(
        op.f("ix_staff_event_notification_old_01_updated_time"),
        table_name="staff_event_notification_old_01",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_old_01_staff_event_contact_information_id"),
        table_name="staff_event_notification_old_01",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_old_01_message_id"),
        table_name="staff_event_notification_old_01",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_old_01_id"), table_name="staff_event_notification_old_01"
    )
    op.drop_index(
        op.f("ix_staff_event_notification_old_01_event_time"),
        table_name="staff_event_notification_old_01",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_old_01_event_id"),
        table_name="staff_event_notification_old_01",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_old_01_created_time"),
        table_name="staff_event_notification_old_01",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_old_01_case_id"),
        table_name="staff_event_notification_old_01",
    )
    op.drop_table("staff_event_notification_old_01")
