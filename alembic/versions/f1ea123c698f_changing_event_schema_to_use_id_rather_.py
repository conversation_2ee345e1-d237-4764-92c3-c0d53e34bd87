"""Changing event schema to use id rather than event_id

Revision ID: f1ea123c698f
Revises: e09d106b0370
Create Date: 2022-06-22 11:31:19.966205

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f1ea123c698f"
down_revision = "451a54bcd7d9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("events", "event_id", new_column_name="id")
    op.drop_constraint("phases_start_event_id_fkey", "phases", type_="foreignkey")
    op.drop_constraint("phases_end_event_id_fkey", "phases", type_="foreignkey")
    op.create_foreign_key(
        "phases_start_event_id_fkey", "phases", "events", ["start_event_id"], ["id"]
    )
    op.create_foreign_key("phases_end_event_id_fkey", "phases", "events", ["end_event_id"], ["id"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("events", "id", new_column_name="event_id")
    op.drop_constraint("phases_start_event_id_fkey", "phases", type_="foreignkey")
    op.drop_constraint("phases_end_event_id_fkey", "phases", type_="foreignkey")
    op.create_foreign_key(
        "phases_end_event_id_fkey", "phases", "events", ["end_event_id"], ["event_id"]
    )
    op.create_foreign_key(
        "phases_start_event_id_fkey", "phases", "events", ["start_event_id"], ["event_id"]
    )
    # ### end Alembic commands ###
