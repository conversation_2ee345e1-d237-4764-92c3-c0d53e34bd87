"""Make events.event_type_id nullable False, Add event_types.hidden column

Revision ID: f9df24ca12d9
Revises: 72890e46b3ee
Create Date: 2022-09-22 14:21:04.852116

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "f9df24ca12d9"
down_revision = "72890e46b3ee"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "event_types",
        sa.Column("hidden", sa.<PERSON>(), server_default="false", nullable=False),
    )
    op.alter_column("events", "event_name", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column("events_history", "event_name", existing_type=sa.VARCHAR(), nullable=True)

    op.execute(
        "INSERT INTO event_types (id, type, name, color, hidden) VALUES('null_event','uncategorized','','#000000',true) ON CONFLICT DO NOTHING;"
    )

    op.execute("UPDATE events SET event_type_id = 'null_event' WHERE event_type_id IS NULL")
    op.execute("UPDATE events_history SET event_type_id = 'null_event' WHERE event_type_id IS NULL")

    op.alter_column("events", "event_type_id", existing_type=sa.VARCHAR(), nullable=False)
    op.alter_column(
        "events_history",
        "event_type_id",
        nullable=False,
    )

    # This stays (because of previous mismatch between model and migration)
    op.alter_column(
        "phases",
        "status",
        nullable=False,
    )

    # Mark obsolete event types hidden on Annotation Tool
    op.execute(
        "UPDATE event_types SET hidden = true WHERE id = ANY('{back_table_unprepared,sterile_pack_on_back_table,lights_off,lights_on,case_cart_in,case_cart_out,no_case_cart,mop_in,mop_out,extubation,intubation,patient_briefing_end,patient_briefing_start,patient_imaging_end,patient_imaging_start,patient_on_hospital_bed,patient_on_or_table,patient_skin_prep_end,patient_skin_prep_start,post_operative,pre_operative,surgery,turn_over_clean,turn_over_idle,turn_over_open,case_cart_visible,cleaning_crew_in,cleaning_crew_out,closing_start,count_end,count_start,endoscopy_end,endoscopy_start,first_incision_start,mop,no_mop,no_patient,or_table_ready,timeout_end,timeout_start}')"
    )

    op.execute(
        "UPDATE events SET event_type_id = 'patient_xfer_to_or_table' WHERE id = 'patient_on_or_table' and start_time > '2022-01-31T00:00:00-07:00'"
    )
    op.execute(
        "UPDATE events_history SET event_type_id = 'patient_xfer_to_or_table' WHERE id = 'patient_on_or_table' and start_time > '2022-01-31T00:00:00-07:00'"
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("events", "event_type_id", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column(
        "events_history",
        "event_type_id",
        nullable=True,
    )
    op.alter_column("events", "event_name", existing_type=sa.VARCHAR(), nullable=False)
    op.alter_column("events_history", "event_name", existing_type=sa.VARCHAR(), nullable=False)

    op.drop_column("event_types", "hidden")
    # ### end Alembic commands ###
