"""Restore block name case insensitive constraint

Revision ID: e13e87f463bd
Revises: 802917b5e2d7
Create Date: 2024-09-25 15:17:42.297517

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "e13e87f463bd"
down_revision = "802917b5e2d7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_unique_block_name_org_id_not_archived",
        table_name="blocks",
        postgresql_where="(archived_time IS NULL)",
    )
    op.create_index(
        "ix_unique_block_name_org_id_not_archived",
        "blocks",
        [sa.text("lower(name)"), "org_id"],
        unique=True,
        postgresql_where="(archived_time IS NULL)",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_unique_block_name_org_id_not_archived",
        table_name="blocks",
        postgresql_where="(archived_time IS NULL)",
    )
    op.create_index(
        "ix_unique_block_name_org_id_not_archived",
        "blocks",
        ["name", "org_id"],
        unique=True,
        postgresql_where="(archived_time IS NULL)",
    )
    # ### end Alembic commands ###
