"""Make active block name unique in each org

Revision ID: e8f9ff56673d
Revises: a9a231f78d7e
Create Date: 2024-01-03 13:54:40.661772

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "e8f9ff56673d"
down_revision = "a9a231f78d7e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        "ix_unique_block_name_org_id_not_archived",
        "blocks",
        ["name", "org_id"],
        unique=True,
        postgresql_where="(archived_time IS NULL)",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_unique_block_name_org_id_not_archived",
        table_name="blocks",
        postgresql_where="(archived_time IS NULL)",
    )


# ### end Alembic commands ###
