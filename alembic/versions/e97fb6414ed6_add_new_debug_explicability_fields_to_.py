"""Add new debug/explicability fields to case_forecasts

Revision ID: e97fb6414ed6
Revises: 9be6965495d2
Create Date: 2025-02-07 15:18:33.254400

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "e97fb6414ed6"
down_revision = "9be6965495d2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("case_forecast", sa.Column("static_duration_minutes", sa.Float(), nullable=True))
    op.add_column("case_forecast", sa.Column("pythia_duration_minutes", sa.Float(), nullable=True))
    op.add_column(
        "case_forecast",
        sa.Column("static_duration_end_time", sa.DateTime(timezone=True), nullable=True),
    )
    op.add_column(
        "case_forecast",
        sa.Column("transformer_end_time", sa.DateTime(timezone=True), nullable=True),
    )
    op.add_column(
        "case_forecast", sa.Column("pythia_end_time", sa.DateTime(timezone=True), nullable=True)
    )
    op.add_column(
        "case_forecast", sa.Column("turnover_duration_minutes", sa.Float(), nullable=True)
    )
    op.add_column(
        "case_forecast", sa.Column("static_start_offset_minutes", sa.Float(), nullable=True)
    )
    op.add_column("case_forecast", sa.Column("is_auto_follow", sa.Boolean(), nullable=True))
    op.add_column("case_forecast", sa.Column("is_overtime", sa.Boolean(), nullable=True))
    op.add_column("case_forecast", sa.Column("pythia_prediction_tag", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_case_forecast_is_auto_follow"), "case_forecast", ["is_auto_follow"], unique=False
    )
    op.create_index(
        op.f("ix_case_forecast_is_overtime"), "case_forecast", ["is_overtime"], unique=False
    )
    op.create_index(
        op.f("ix_case_forecast_pythia_duration_minutes"),
        "case_forecast",
        ["pythia_duration_minutes"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_pythia_end_time"), "case_forecast", ["pythia_end_time"], unique=False
    )
    op.create_index(
        op.f("ix_case_forecast_pythia_prediction_tag"),
        "case_forecast",
        ["pythia_prediction_tag"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_static_duration_end_time"),
        "case_forecast",
        ["static_duration_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_static_duration_minutes"),
        "case_forecast",
        ["static_duration_minutes"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_static_start_offset_minutes"),
        "case_forecast",
        ["static_start_offset_minutes"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_transformer_end_time"),
        "case_forecast",
        ["transformer_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_turnover_duration_minutes"),
        "case_forecast",
        ["turnover_duration_minutes"],
        unique=False,
    )
    op.add_column(
        "case_forecast_history",
        sa.Column("static_duration_minutes", sa.Float(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column("pythia_duration_minutes", sa.Float(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column(
            "static_duration_end_time",
            sa.DateTime(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column(
            "transformer_end_time", sa.DateTime(timezone=True), autoincrement=False, nullable=True
        ),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column(
            "pythia_end_time", sa.DateTime(timezone=True), autoincrement=False, nullable=True
        ),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column("turnover_duration_minutes", sa.Float(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column("static_start_offset_minutes", sa.Float(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column("is_auto_follow", sa.Boolean(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column("is_overtime", sa.Boolean(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column("pythia_prediction_tag", sa.String(), autoincrement=False, nullable=True),
    )
    op.create_index(
        op.f("ix_case_forecast_history_is_auto_follow"),
        "case_forecast_history",
        ["is_auto_follow"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_is_overtime"),
        "case_forecast_history",
        ["is_overtime"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_pythia_duration_minutes"),
        "case_forecast_history",
        ["pythia_duration_minutes"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_pythia_end_time"),
        "case_forecast_history",
        ["pythia_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_pythia_prediction_tag"),
        "case_forecast_history",
        ["pythia_prediction_tag"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_static_duration_end_time"),
        "case_forecast_history",
        ["static_duration_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_static_duration_minutes"),
        "case_forecast_history",
        ["static_duration_minutes"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_static_start_offset_minutes"),
        "case_forecast_history",
        ["static_start_offset_minutes"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_transformer_end_time"),
        "case_forecast_history",
        ["transformer_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_turnover_duration_minutes"),
        "case_forecast_history",
        ["turnover_duration_minutes"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_case_forecast_history_turnover_duration_minutes"),
        table_name="case_forecast_history",
    )
    op.drop_index(
        op.f("ix_case_forecast_history_transformer_end_time"), table_name="case_forecast_history"
    )
    op.drop_index(
        op.f("ix_case_forecast_history_static_start_offset_minutes"),
        table_name="case_forecast_history",
    )
    op.drop_index(
        op.f("ix_case_forecast_history_static_duration_minutes"), table_name="case_forecast_history"
    )
    op.drop_index(
        op.f("ix_case_forecast_history_static_duration_end_time"),
        table_name="case_forecast_history",
    )
    op.drop_index(
        op.f("ix_case_forecast_history_pythia_prediction_tag"), table_name="case_forecast_history"
    )
    op.drop_index(
        op.f("ix_case_forecast_history_pythia_end_time"), table_name="case_forecast_history"
    )
    op.drop_index(
        op.f("ix_case_forecast_history_pythia_duration_minutes"), table_name="case_forecast_history"
    )
    op.drop_index(op.f("ix_case_forecast_history_is_overtime"), table_name="case_forecast_history")
    op.drop_index(
        op.f("ix_case_forecast_history_is_auto_follow"), table_name="case_forecast_history"
    )
    op.drop_column("case_forecast_history", "pythia_prediction_tag")
    op.drop_column("case_forecast_history", "is_overtime")
    op.drop_column("case_forecast_history", "is_auto_follow")
    op.drop_column("case_forecast_history", "static_start_offset_minutes")
    op.drop_column("case_forecast_history", "turnover_duration_minutes")
    op.drop_column("case_forecast_history", "pythia_end_time")
    op.drop_column("case_forecast_history", "transformer_end_time")
    op.drop_column("case_forecast_history", "static_duration_end_time")
    op.drop_column("case_forecast_history", "pythia_duration_minutes")
    op.drop_column("case_forecast_history", "static_duration_minutes")
    op.drop_index(op.f("ix_case_forecast_turnover_duration_minutes"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_transformer_end_time"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_static_start_offset_minutes"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_static_duration_minutes"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_static_duration_end_time"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_pythia_prediction_tag"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_pythia_end_time"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_pythia_duration_minutes"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_is_overtime"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_is_auto_follow"), table_name="case_forecast")
    op.drop_column("case_forecast", "pythia_prediction_tag")
    op.drop_column("case_forecast", "is_overtime")
    op.drop_column("case_forecast", "is_auto_follow")
    op.drop_column("case_forecast", "static_start_offset_minutes")
    op.drop_column("case_forecast", "turnover_duration_minutes")
    op.drop_column("case_forecast", "pythia_end_time")
    op.drop_column("case_forecast", "transformer_end_time")
    op.drop_column("case_forecast", "static_duration_end_time")
    op.drop_column("case_forecast", "pythia_duration_minutes")
    op.drop_column("case_forecast", "static_duration_minutes")
    # ### end Alembic commands ###
