"""Add phase relationship indexes


Revision ID: d896e4033903
Revises: cf9303c43ce3
Create Date: 2022-11-10 15:10:29.737579

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d896e4033903"
down_revision = "cf9303c43ce3"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        op.f("ix_phase_relationships_child_phase_id"),
        "phase_relationships",
        ["child_phase_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_phase_relationships_parent_phase_id"),
        "phase_relationships",
        ["parent_phase_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_phase_relationships_parent_phase_id"), table_name="phase_relationships")
    op.drop_index(op.f("ix_phase_relationships_child_phase_id"), table_name="phase_relationships")
    # ### end Alembic commands ###
