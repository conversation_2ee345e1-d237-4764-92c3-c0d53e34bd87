"""Use annotation_tasks_history with Versioned mixin

Revision ID: f7a43be42fd5
Revises: 1ffefe3baf0f
Create Date: 2023-08-30 13:21:05.921717

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "f7a43be42fd5"
down_revision = "1ffefe3baf0f"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO 60000;")  # 1 minute for this session

    op.add_column(
        "annotation_tasks",
        sa.Column(
            "version",
            sa.Integer(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        "annotation_tasks_history",
        sa.Column("created_time", sa.DateTime(timezone=True), nullable=True),
    )
    op.add_column(
        "annotation_tasks_history",
        sa.Column(
            "version",
            sa.Integer(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.alter_column(
        "annotation_tasks_history",
        "updated_time",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=True,
        existing_server_default=sa.text("now()"),
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_created_time"),
        "annotation_tasks_history",
        ["created_time"],
        unique=False,
    )
    op.drop_constraint(
        "annotation_tasks_history_annotation_task_id_fkey",
        "annotation_tasks_history",
        type_="foreignkey",
    )
    op.drop_constraint(
        "annotation_tasks_history_org_id_fkey",
        "annotation_tasks_history",
        type_="foreignkey",
    )
    op.drop_constraint(
        "annotation_tasks_history_site_id_fkey",
        "annotation_tasks_history",
        type_="foreignkey",
    )
    op.drop_constraint(
        "annotation_tasks_history_room_id_fkey",
        "annotation_tasks_history",
        type_="foreignkey",
    )

    # 1. Set past version numbers
    op.execute(
        """ UPDATE
                annotation_tasks_history
            SET
                "version" = version_table."version"
            FROM
                (SELECT
                    id,
                    annotation_task_id,
                    updated_time,
                    ROW_NUMBER () OVER (
                        PARTITION BY annotation_task_id
                        ORDER BY updated_time ASC
                    ) "version"
                FROM
                    annotation_tasks_history) version_table
            WHERE
                annotation_tasks_history.id = version_table.id;"""
    )

    # 2. Update records in annotation_tasks to max_version + 1
    op.execute(
        """ WITH max_versions AS
            (SELECT
                annotation_task_id,
                MAX("version") mv
            FROM
                annotation_tasks_history
            GROUP BY
                annotation_task_id)

            UPDATE
                annotation_tasks
            SET
                "version" = max_versions.mv + 1
            FROM
                max_versions
            WHERE
                id = max_versions.annotation_task_id;"""
    )

    # 3. Copy records from annotation_tasks to annotation_tasks_history as latest version
    op.execute(
        """ INSERT INTO annotation_tasks_history
                (id,
                annotation_task_id,
                org_id,
                site_id,
                room_id,
                start_time,
                end_time,
                status,
                annotator_user_id,
                reviewer_user_id,
                updated_time,
                type_id,
                cancelled_reason,
                "version")
            (SELECT
                gen_random_uuid() id,
                id annotation_task_id,
                org_id,
                site_id,
                room_id,
                start_time,
                end_time,
                status,
                annotator_user_id,
                reviewer_user_id,
                updated_time,
                type_id,
                cancelled_reason,
                "version"
            FROM annotation_tasks ORDER BY annotation_task_id);"""
    )

    # 4. Update history records with created_time
    op.execute(
        """ UPDATE annotation_tasks_history ath
            SET
                created_time = ats.created_time
            FROM
                (SELECT
                    id,
                    created_time
                FROM
                    annotation_tasks) ats
            WHERE
                ath.annotation_task_id = ats.id;"""
    )

    op.alter_column(
        "annotation_tasks_history",
        "created_time",
        server_default=sa.text("now()"),
        nullable=False,
    )

    op.execute(
        "ALTER TABLE annotation_tasks_history DROP CONSTRAINT IF EXISTS annotation_tasks_history_pkey;"
    )

    op.create_primary_key(
        "annotation_tasks_history_pkey", "annotation_tasks_history", ["id", "version"]
    )

    # 4. Replace ids
    op.execute("""UPDATE annotation_tasks_history SET id = annotation_task_id""")

    op.drop_column("annotation_tasks_history", "annotation_task_id")
    # ### end Alembic commands ###


def downgrade():
    op.execute("SET statement_timeout TO 60000;")  # 1 minute for this session

    op.add_column(
        "annotation_tasks_history",
        sa.Column("annotation_task_id", postgresql.UUID(), nullable=True),
    )

    # 1. Copy id over to annotation_task_id
    op.execute(""" UPDATE annotation_tasks_history SET annotation_task_id = id""")

    # 2. Delete max_version of each annotation_task_id
    op.execute(
        """ DELETE FROM
                annotation_tasks_history
            WHERE (id, "version") IN
                (SELECT
                    id,
                    MAX("version") mv
                FROM
                    annotation_tasks_history
                GROUP BY id)
        """
    )

    op.execute(
        "ALTER TABLE annotation_tasks_history DROP CONSTRAINT IF EXISTS annotation_tasks_history_pkey;"
    )

    # 3. Randomize ids
    op.execute(""" UPDATE annotation_tasks_history SET id = gen_random_uuid()""")

    op.create_primary_key("annotation_tasks_history_pkey", "annotation_tasks_history", ["id"])

    op.create_foreign_key(
        "annotation_tasks_history_room_id_fkey",
        "annotation_tasks_history",
        "rooms",
        ["room_id"],
        ["id"],
    )
    op.create_foreign_key(
        "annotation_tasks_history_site_id_fkey",
        "annotation_tasks_history",
        "sites",
        ["site_id"],
        ["id"],
    )
    op.create_foreign_key(
        "annotation_tasks_history_org_id_fkey",
        "annotation_tasks_history",
        "organizations",
        ["org_id"],
        ["id"],
    )
    op.create_foreign_key(
        "annotation_tasks_history_annotation_task_id_fkey",
        "annotation_tasks_history",
        "annotation_tasks",
        ["annotation_task_id"],
        ["id"],
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_created_time"),
        table_name="annotation_tasks_history",
    )
    op.alter_column(
        "annotation_tasks_history",
        "updated_time",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.drop_column("annotation_tasks_history", "version")
    op.drop_column("annotation_tasks_history", "created_time")
    op.drop_column("annotation_tasks", "version")
    # ### end Alembic commands ###
