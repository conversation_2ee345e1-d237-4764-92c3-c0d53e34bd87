"""Add updated by column to tasks

Revision ID: e072a3281e64
Revises: 44bae06aa992
Create Date: 2024-01-19 13:59:23.960678

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "e072a3281e64"
down_revision = "44bae06aa992"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("annotation_tasks", sa.Column("updated_by_user_id", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_annotation_tasks_updated_by_user_id"),
        "annotation_tasks",
        ["updated_by_user_id"],
        unique=False,
    )
    op.add_column(
        "annotation_tasks_history",
        sa.Column("updated_by_user_id", sa.String(), autoincrement=False, nullable=True),
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_updated_by_user_id"),
        "annotation_tasks_history",
        ["updated_by_user_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_annotation_tasks_history_updated_by_user_id"),
        table_name="annotation_tasks_history",
    )
    op.drop_column("annotation_tasks_history", "updated_by_user_id")
    op.drop_index(op.f("ix_annotation_tasks_updated_by_user_id"), table_name="annotation_tasks")
    op.drop_column("annotation_tasks", "updated_by_user_id")
    # ### end Alembic commands ###
