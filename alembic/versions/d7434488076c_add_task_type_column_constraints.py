"""Add task type column constraints

Revision ID: d7434488076c
Revises: 051622cb2c33
Create Date: 2023-04-17 17:05:51.331467

"""

from sqlalchemy.sql import and_, column, func

from alembic import op

# revision identifiers, used by Alembic.
revision = "d7434488076c"
down_revision = "051622cb2c33"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.alter_column("annotation_task_types", "description", nullable=False)

    op.create_check_constraint(
        "ck_annotation_task_types_name_min_len_and_trimmed",
        "annotation_task_types",
        and_(
            func.length(func.trim(column("name"))) >= 3, func.trim(column("name")) == column("name")
        ),
    )

    op.create_check_constraint(
        "ck_annotation_task_types_description_trimmed",
        "annotation_task_types",
        func.trim(column("description")) == column("description"),
    )


def downgrade() -> None:
    op.drop_constraint(
        "ck_annotation_task_types_name_min_len_and_trimmed",
        "annotation_task_types",
        type_="check",
    )

    op.drop_constraint(
        "ck_annotation_task_types_description_trimmed",
        "annotation_task_types",
        type_="check",
    )

    op.alter_column("annotation_task_types", "description", nullable=False)
