"""Add org and site id to casestaffplans

Revision ID: f993ffa11abe
Revises: 7cfd8185e81b
Create Date: 2023-12-14 13:15:25.632678

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "f993ffa11abe"
down_revision = "7cfd8185e81b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("case_flag", sa.Column("org_id", sa.String(), nullable=False))
    op.add_column("case_flag", sa.Column("site_id", sa.String(), nullable=False))
    op.create_index(op.f("ix_case_flag_org_id"), "case_flag", ["org_id"], unique=False)
    op.create_index(op.f("ix_case_flag_site_id"), "case_flag", ["site_id"], unique=False)
    op.create_foreign_key(None, "case_flag", "organizations", ["org_id"], ["id"])
    op.create_foreign_key(None, "case_flag", "sites", ["site_id"], ["id"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "case_flag", type_="foreignkey")
    op.drop_constraint(None, "case_flag", type_="foreignkey")
    op.drop_index(op.f("ix_case_flag_site_id"), table_name="case_flag")
    op.drop_index(op.f("ix_case_flag_org_id"), table_name="case_flag")
    op.drop_column("case_flag", "site_id")
    op.drop_column("case_flag", "org_id")
    # ### end Alembic commands ###
