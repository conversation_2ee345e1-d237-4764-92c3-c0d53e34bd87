"""Write cancelled reason to annotation task history

Revision ID: eb8241cc25a9
Revises: 3c0d6e6385e6
Create Date: 2023-06-26 13:37:03.427536

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "eb8241cc25a9"
down_revision = "3c0d6e6385e6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "annotation_tasks_history",
        sa.Column(
            "cancelled_reason",
            sa.String(),
            nullable=True,
        ),
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_cancelled_reason"),
        "annotation_tasks_history",
        ["cancelled_reason"],
        unique=False,
    )


def downgrade() -> None:
    op.drop_index(
        op.f("ix_annotation_tasks_history_cancelled_reason"), table_name="annotation_tasks_history"
    )
    op.drop_column("annotation_tasks_history", "cancelled_reason")
