"""

Revision ID: e8d159246fb7
Revises: e97fb6414ed6
Create Date: 2025-02-10 22:38:52.865977

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "e8d159246fb7"
down_revision = "e97fb6414ed6"
branch_labels = None
depends_on = None


def upgrade():
    # Insert the exclusion label into turnover_labels
    op.execute(
        """
        INSERT INTO turnover_labels (name, type) VALUES
        ('Exclude this turnover', 'exclusion')
        """
    )


def downgrade():
    # Remove the inserted row during downgrade
    op.execute(
        """
        DELETE FROM turnover_labels WHERE name = 'Exclude this turnover' AND type = 'exclusion'
        """
    )
