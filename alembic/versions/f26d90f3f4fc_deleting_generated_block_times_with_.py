"""deleting generated block times with special chars

Revision ID: f26d90f3f4fc
Revises: 85f141d754d4
Create Date: 2025-03-10 11:08:17.883160

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f26d90f3f4fc"
down_revision = "85f141d754d4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
            DELETE FROM public.block_times
            WHERE released_from IS NOT NULL;
        """
    )

    # deleting the block with invalid name - '...' for HF
    op.execute(
        """
        DELETE FROM public.block_time_releases
        WHERE block_time_id IN (SELECT bt.id FROM public.block_times bt INNER JOIN public.blocks b ON b.id = bt.block_id WHERE b.name = '...');
        """
    )

    op.execute(
        """
        DELETE FROM public.block_times
        WHERE block_id IN (SELECT id FROM public.blocks WHERE name = '...');
        """
    )

    op.execute(
        """
        DELETE FROM public.blocks
        WHERE name = '...';
        """
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
