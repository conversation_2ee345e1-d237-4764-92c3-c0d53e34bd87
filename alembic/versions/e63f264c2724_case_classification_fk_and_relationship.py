"""case classification fk and relationship

Revision ID: e63f264c2724
Revises: d610d1e44179
Create Date: 2022-09-25 20:49:55.530162

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e63f264c2724"
down_revision = "d610d1e44179"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("cases", sa.Column("case_classification_types_id", sa.String(), nullable=True))
    op.create_foreign_key(
        None, "cases", "case_classification_types", ["case_classification_types_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "cases", type_="foreignkey")
    op.drop_column("cases", "case_classification_types_id")
    # ### end Alembic commands ###
