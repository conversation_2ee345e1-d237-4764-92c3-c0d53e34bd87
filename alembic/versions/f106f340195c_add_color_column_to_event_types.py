"""Add Color column to EventTypes

Revision ID: f106f340195c
Revises: 1abdb6a02209
Create Date: 2022-08-22 13:22:22.401114

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "f106f340195c"
down_revision = "1abdb6a02209"
branch_labels = None
depends_on = None


def upgrade():
    bind = op.get_bind()
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "event_types",
        sa.Column("color", sa.String(), nullable=True, default="#999999"),
    )
    op.execute("UPDATE event_types SET color = '#999999'")
    op.alter_column("event_types", "color", nullable=False, default="#999999")

    t_event_types = sa.Table(
        "event_types",
        sa.MetaData(),
        sa.Column("id", sa.String(32)),
        sa.Column("name", sa.String(64)),
        sa.Column("type", sa.String(64)),
        sa.Column("color", sa.String(16)),
    )

    # Insert a few more event types
    op.bulk_insert(
        t_event_types,
        [
            {
                "id": "turn_over_clean",
                "type": "phase",
                "name": "Turn Over Clean",
                "color": "#999999",
            },
            {
                "id": "turn_over_open",
                "type": "phase",
                "name": "Turn Over Open",
                "color": "#999999",
            },
            {
                "id": "turn_over_idle",
                "type": "phase",
                "name": "Turn Over Idle",
                "color": "#999999",
            },
            {
                "id": "pre_operative",
                "type": "phase",
                "name": "Pre-Operative",
                "color": "#999999",
            },
            {
                "id": "post_operative",
                "type": "phase",
                "name": "Post-Operative",
                "color": "#999999",
            },
            {"id": "surgery", "type": "phase", "name": "Surgery", "color": "#999999"},
        ],
    )

    # Correcting typos and add colors
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_briefing_start")
        .values(name="Patient briefing start", color="#999999")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_briefing_end")
        .values(color="#999999")
    )

    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_skin_prep_start")
        .values(name="Patient skin prep start", color="#6fcca5")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_skin_prep_end")
        .values(color="#6fcca5")
    )

    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "sensitive_content_start")
        .values(name="Sensitive content start", color="#999999")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "sensitive_content_end")
        .values(color="#999999")
    )

    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "sterile_pack_on_back_table")
        .values(name="Sterile pack on back table", color="#999999")
    )

    # Add colors
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "back_table_open")
        .values(color="#4c94ff")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "case_cart_in").values(color="#0faa6c")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "case_cart_out").values(color="#999999")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "cleaning_crew_in")
        .values(color="#999999")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "cleaning_crew_out")
        .values(color="#999999")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "closing_start").values(color="#e97500")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "count_start").values(color="#999999")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "count_end").values(color="#999999")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "endo_pack_open").values(color="#4c94ff")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "endoscopy_start")
        .values(color="#14a4a4")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "endoscopy_end").values(color="#14a4a4")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "first_incision_start")
        .values(color="#999999")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "intubation").values(color="#f5c346")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "extubation").values(color="#f5c346")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "mop_in").values(color="#8c8ce3")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "mop_out").values(color="#8c8ce3")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "or_table_ready").values(color="#e868a8")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "patient_draped").values(color="#ed6b6b")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_imaging_start")
        .values(color="#93beff")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_imaging_end")
        .values(color="#93beff")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_wheels_in")
        .values(color="#f4a6a6")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_wheels_out")
        .values(color="#f4a6a6")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_xfer_to_or_table")
        .values(color="#babaee")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_xfer_to_bed")
        .values(color="#72c8c8")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "patient_undraped")
        .values(color="#ed6b6b")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "terminal_clean_start")
        .values(color="#999999")
    )
    bind.execute(
        t_event_types.update()
        .where(t_event_types.c.id == "terminal_clean_end")
        .values(color="#999999")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "timeout_start").values(color="#999999")
    )
    bind.execute(
        t_event_types.update().where(t_event_types.c.id == "timeout_end").values(color="#999999")
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("event_types", "color")
    # ### end Alembic commands ###
