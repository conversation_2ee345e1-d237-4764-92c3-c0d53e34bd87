"""add cascade to phases foreign key

Revision ID: fbba9a7c4744
Revises: f1ea123c698f
Create Date: 2022-06-29 15:29:27.177384

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "fbba9a7c4744"
down_revision = "f1ea123c698f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("phases_end_event_id_fkey", "phases", type_="foreignkey")
    op.drop_constraint("phases_start_event_id_fkey", "phases", type_="foreignkey")
    op.create_foreign_key(None, "phases", "events", ["end_event_id"], ["id"], ondelete="CASCADE")
    op.create_foreign_key(None, "phases", "events", ["start_event_id"], ["id"], ondelete="CASCADE")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "phases", type_="foreignkey")
    op.drop_constraint(None, "phases", type_="foreignkey")
    op.create_foreign_key(
        "phases_start_event_id_fkey", "phases", "events", ["start_event_id"], ["id"]
    )
    op.create_foreign_key("phases_end_event_id_fkey", "phases", "events", ["end_event_id"], ["id"])
    # ### end Alembic commands ###
