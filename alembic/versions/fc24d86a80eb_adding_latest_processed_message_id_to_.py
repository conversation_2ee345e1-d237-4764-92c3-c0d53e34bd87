"""Adding latest_processed_message_id to case_raw

Revision ID: fc24d86a80eb
Revises: 4fcc8216c7af
Create Date: 2025-04-08 17:12:46.976102

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "fc24d86a80eb"
down_revision = "4fcc8216c7af"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("cases", sa.Column("latest_processed_message_id", sa.String(), nullable=True))
    op.add_column(
        "cases_history",
        sa.Column("latest_processed_message_id", sa.String(), autoincrement=False, nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("cases_history", "latest_processed_message_id")
    op.drop_column("cases", "latest_processed_message_id")
    # ### end Alembic commands ###
