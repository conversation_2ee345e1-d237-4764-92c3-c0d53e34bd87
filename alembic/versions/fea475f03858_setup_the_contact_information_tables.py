"""Setup the contact information tables

Revision ID: fea475f03858
Revises: 1b5e62984a64
Create Date: 2023-01-27 19:48:04.618024

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "fea475f03858"
down_revision = "1b5e62984a64"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "contact_information",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("type", sa.Enum("PHONE_NUMBER", name="contactinformationtype"), nullable=False),
        sa.Column("contact_information_value", sa.String(), nullable=False),
        sa.CheckConstraint(
            "(type in ('PHONE_NUMBER') and contact_information_value ~ '^\\+?[0-9]{0,3}[0-9]{10}$')",
            name="ck_type",
        ),
        sa.PrimaryKeyConstraint(
            "contact_information_value", "type", name="pk_contactInformationValue_type"
        ),
        sa.UniqueConstraint("id", name="uq_id"),
    )
    op.create_index(
        op.f("ix_contact_information_contact_information_value"),
        "contact_information",
        ["contact_information_value"],
        unique=False,
    )
    op.create_index(
        op.f("ix_contact_information_created_time"),
        "contact_information",
        ["created_time"],
        unique=False,
    )
    op.create_index(op.f("ix_contact_information_id"), "contact_information", ["id"], unique=False)
    op.create_index(
        op.f("ix_contact_information_type"), "contact_information", ["type"], unique=False
    )
    op.create_index(
        op.f("ix_contact_information_updated_time"),
        "contact_information",
        ["updated_time"],
        unique=False,
    )
    op.create_table(
        "staff_event_notification_contact_information",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("archived_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("staff_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("contact_information_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("event_type_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["contact_information_id"], ["contact_information.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["event_type_id"], ["event_types.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["staff_id"], ["staff.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint(
            "contact_information_id",
            "staff_id",
            "event_type_id",
            name="pk_contactInfoId_staffId_eventTypeId",
        ),
    )
    op.create_index(
        op.f("ix_staff_event_notification_contact_information_archived_time"),
        "staff_event_notification_contact_information",
        ["archived_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_contact_information_contact_information_id"),
        "staff_event_notification_contact_information",
        ["contact_information_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_contact_information_created_time"),
        "staff_event_notification_contact_information",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_contact_information_event_type_id"),
        "staff_event_notification_contact_information",
        ["event_type_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_contact_information_staff_id"),
        "staff_event_notification_contact_information",
        ["staff_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_contact_information_updated_time"),
        "staff_event_notification_contact_information",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_staff_event_notification_contact_information_updated_time"),
        table_name="staff_event_notification_contact_information",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_contact_information_staff_id"),
        table_name="staff_event_notification_contact_information",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_contact_information_event_type_id"),
        table_name="staff_event_notification_contact_information",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_contact_information_created_time"),
        table_name="staff_event_notification_contact_information",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_contact_information_contact_information_id"),
        table_name="staff_event_notification_contact_information",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_contact_information_archived_time"),
        table_name="staff_event_notification_contact_information",
    )
    op.drop_table("staff_event_notification_contact_information")
    op.drop_index(op.f("ix_contact_information_updated_time"), table_name="contact_information")
    op.drop_index(op.f("ix_contact_information_type"), table_name="contact_information")
    op.drop_index(op.f("ix_contact_information_id"), table_name="contact_information")
    op.drop_index(op.f("ix_contact_information_created_time"), table_name="contact_information")
    op.drop_index(
        op.f("ix_contact_information_contact_information_value"), table_name="contact_information"
    )
    op.drop_table("contact_information")
    # ### end Alembic commands ###
